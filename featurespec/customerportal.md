# Customer Portal Implementation

## Overview
This document tracks the implementation of the customer portal feature, which allows customers to view and pay invoices, as well as ask questions about them.

## Features and Status

### Authentication and Access
- [x] Customer model with portal access fields
- [x] Farm model with customer_portal_enabled flag
- [x] Customer authentication middleware
- [x] Customer login API endpoint
- [x] Customer registration API endpoint
- [x] Password reset API endpoints
- [x] Email verification API endpoint
- [x] Customer login page
- [x] Customer registration page
- [x] Password reset page
- [x] Email verification page

### Invoice Management
- [x] Basic invoice viewing API endpoints
- [x] Invoice history and status tracking
- [x] Invoice questions API endpoints
- [x] Invoice detail view
- [x] Invoice list view with filtering and sorting
- [x] Invoice question UI

### Payment Processing
- [x] Stripe integration for farms
- [x] Payment intent creation API endpoint
- [x] Invoice payment API endpoint
- [x] Support for ACH transfers
- [x] Payment confirmation and receipts
- [x] Transaction history
- [x] Payment UI with Stripe Elements
- [x] Payment method selection (card/ACH)
- [x] Payment confirmation display

### Stripe Fee Handling
- [x] Add farm setting for who pays Stripe fees (farm or customer)
- [x] Display fee information to customers when applicable
- [x] Calculate and apply fees correctly during payment
- [x] Fee display in payment UI

### Communication
- [x] Allow customers to ask questions about invoices (API)
- [x] View invoice questions (API)
- [x] Notification system for new questions and responses
- [x] Message history and tracking UI

### User Interface
- [x] Responsive design for all devices
- [x] Intuitive navigation
- [x] Clear payment flow
- [x] Accessible design

## Database Changes Required
1. Add `customer_pays_stripe_fees` boolean field to Farm model
2. Create InvoiceQuestion model for customer questions about invoices
3. Add payment_transaction_id to Invoice model to link payments

## API Endpoints Needed
1. Customer authentication endpoints (login, logout, password reset)
2. Invoice listing and detail endpoints
3. Payment processing endpoints
4. Invoice question endpoints

## Implementation Plan
1. Create database migrations for schema changes
2. Implement backend API endpoints
3. Create frontend components and pages
4. Integrate Stripe payment processing
5. Test the complete flow
6. Deploy to production
