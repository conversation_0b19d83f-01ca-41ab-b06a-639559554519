# Invoice System Enhancements

## Overview

This document describes the comprehensive enhancements made to the invoice management system, implementing advanced features for dispute handling, audit logging, role-based permissions, invoice status restrictions, and a multi-channel notification system.

## 🚀 New Features Implemented

### 1. **Dispute Management System**

A complete dispute workflow for handling invoice disagreements between farms:

#### **Features:**
- **Dispute Creation**: Farms can raise disputes for invoices they receive
- **Dispute Types**: Amount incorrect, services not provided, quality issues, billing errors, etc.
- **Priority Levels**: Low, medium, high, urgent
- **Status Workflow**: Open → Under Review → Awaiting Response → Resolved/Escalated → Closed
- **Communication**: Built-in messaging system for dispute resolution
- **Escalation**: Disputes can be escalated to higher authorities
- **Resolution Tracking**: Complete audit trail of dispute resolution

#### **Database Tables:**
- `invoice_disputes`: Main dispute records
- `invoice_dispute_messages`: Communication within disputes

#### **API Endpoints:**
- `POST /api/invoices/:invoiceId/disputes` - Create dispute
- `GET /api/invoices/:invoiceId/disputes` - Get disputes for invoice
- `GET /api/disputes/:disputeId` - Get specific dispute
- `POST /api/disputes/:disputeId/messages` - Add message to dispute
- `PATCH /api/disputes/:disputeId/resolve` - Resolve dispute
- `PATCH /api/disputes/:disputeId/escalate` - Escalate dispute

### 2. **Comprehensive Audit Logging**

Complete tracking of all invoice-related actions for compliance and security:

#### **Features:**
- **Action Tracking**: All CRUD operations, views, sends, payments, disputes
- **User Context**: IP address, user agent, session tracking
- **Change Tracking**: Before/after values for updates
- **Access Logging**: Failed permission checks and denied access attempts
- **Metadata Storage**: Additional context for each action

#### **Database Table:**
- `invoice_audit_logs`: Complete audit trail

#### **Logged Actions:**
- Created, viewed, updated, deleted, sent, paid, cancelled, disputed
- Document uploads/deletions, reminder sends, status changes
- Access denied events, permission checks

### 3. **Multi-Channel Notification System**

Real-time notifications across multiple channels:

#### **Features:**
- **Channels**: In-app, email, SMS, push notifications
- **Event Types**: Invoice received, viewed, paid, overdue, disputed
- **Priority Levels**: Low, medium, high, urgent
- **Delivery Tracking**: Track which notifications were sent and when
- **User Preferences**: Respect user notification preferences

#### **Database Table:**
- `invoice_notifications`: Notification records and delivery status

#### **Notification Events:**
- Invoice received from another farm
- Invoice viewed by recipient
- Invoice payment received
- Invoice overdue
- Dispute created/resolved
- Dispute messages

### 4. **Role-Based Permissions**

Granular access control based on user roles within farms:

#### **Permission Matrix:**

| Role | View | Edit | Delete | Pay | Dispute | Resolve Dispute |
|------|------|------|--------|-----|---------|----------------|
| Farm Owner | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ |
| Farm Admin | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ |
| Farm Manager | ✅ | ✅ | ❌ | ✅ | ✅ | ❌ |
| Accountant | ✅ | ✅ | ❌ | ✅ | ❌ | ❌ |
| Farm Employee | ✅ | ❌ | ❌ | ❌ | ❌ | ❌ |

#### **Features:**
- **Role-Based Access**: Different permissions for different roles
- **Custom Permissions**: Override default role permissions
- **Action-Specific**: Granular control over specific actions
- **Farm Context**: Permissions are farm-specific

### 5. **Invoice Status Restrictions**

Prevent inappropriate actions based on invoice status:

#### **Status-Based Rules:**

| Status | Allowed Actions | Restricted Actions |
|--------|----------------|-------------------|
| Draft | View, Edit, Delete, Cancel, Send | Pay |
| Sent | View, Edit, Delete, Cancel, Pay, Dispute | - |
| Paid | View, Dispute | Edit, Delete, Cancel, Pay |
| Cancelled | View | Edit, Delete, Cancel, Pay, Send |

### 6. **Enhanced Access Control**

Improved security for farm-to-farm invoicing:

#### **Access Rules:**
- **Sent Invoices**: Full control (view, edit, delete, send, etc.)
- **Received Invoices**: Read-only (view, pay, dispute only)
- **Unrelated Invoices**: No access
- **Global Admins**: Full access to all invoices

## 🛠 Technical Implementation

### **Services Created:**

1. **InvoiceAuditService** (`server/services/invoiceAuditService.js`)
   - Centralized audit logging
   - Request metadata extraction
   - Audit log querying

2. **InvoiceNotificationService** (`server/services/invoiceNotificationService.js`)
   - Multi-channel notification delivery
   - User preference handling
   - Notification templates

3. **InvoicePermissionService** (`server/services/invoicePermissionService.js`)
   - Role-based permission checking
   - Status-based restrictions
   - Middleware for route protection

### **Controllers Enhanced:**

1. **InvoiceController** (`server/controllers/invoiceController.js`)
   - Integrated audit logging
   - Enhanced permission checks
   - Notification triggers

2. **InvoiceDisputeController** (`server/controllers/invoiceDisputeController.js`)
   - Complete dispute management
   - Message handling
   - Resolution workflow

### **Database Migrations:**

- **Migration**: `20241217000001-create-invoice-enhancements.js`
- **Tables**: invoice_disputes, invoice_dispute_messages, invoice_audit_logs, invoice_notifications
- **Indexes**: Optimized for performance

## 📊 Usage Examples

### **Creating a Dispute:**
```javascript
POST /api/invoices/invoice-123/disputes
{
  "disputeType": "amount_incorrect",
  "subject": "Incorrect billing amount",
  "description": "The invoice shows $1000 but we agreed on $800",
  "disputedAmount": 200.00,
  "priority": "medium"
}
```

### **Checking Permissions:**
```javascript
const permission = await InvoicePermissionService.checkInvoicePermission({
  userId: 'user-123',
  farmId: 'farm-456',
  invoiceId: 'invoice-789',
  action: 'edit',
  req
});
```

### **Logging Actions:**
```javascript
await InvoiceAuditService.logUpdate(
  invoiceId,
  userId,
  farmId,
  req,
  { from: { status: 'draft' }, to: { status: 'sent' } }
);
```

### **Sending Notifications:**
```javascript
await InvoiceNotificationService.notifyInvoiceReceived(
  invoice,
  senderFarm,
  recipientFarm
);
```

## 🧪 Testing

### **Test Files:**
- `server/tests/invoiceAccessControlTest.js` - Original access control tests
- `server/tests/invoiceEnhancementsTest.js` - Comprehensive enhancement tests

### **Test Coverage:**
- ✅ Audit logging functionality
- ✅ Notification system validation
- ✅ Role-based permissions matrix
- ✅ Invoice status restrictions
- ✅ Dispute workflow states

## 🔧 Configuration

### **Environment Variables:**
```env
# SMS Configuration (optional)
SMS_FROM_NUMBER=+**********
SMS_API_KEY=your_sms_api_key
SMS_API_SECRET=your_sms_api_secret
SMS_PROVIDER=twilio

# Email Configuration (existing)
EMAIL_HOST=smtp.example.com
EMAIL_PORT=587
EMAIL_USER=your_email
EMAIL_PASSWORD=your_password
```

## 🚦 Migration Guide

### **To Deploy These Enhancements:**

1. **Run Database Migration:**
   ```bash
   npm run migrate
   ```

2. **Update Frontend:**
   - Use `isReceivedInvoice` and `canEdit` metadata
   - Implement dispute UI components
   - Add notification display
   - Handle permission-based UI states

3. **Configure Notifications:**
   - Set up SMS provider (optional)
   - Configure notification preferences
   - Test email templates

## 🔮 Future Enhancements

### **Potential Improvements:**
1. **Advanced Dispute Analytics**: Reporting and trends
2. **Automated Dispute Resolution**: AI-powered suggestions
3. **Integration with Payment Systems**: Automatic payment tracking
4. **Mobile Push Notifications**: Real-time mobile alerts
5. **Webhook Support**: External system integration
6. **Advanced Audit Reporting**: Compliance reports and dashboards

## 📋 Summary

The enhanced invoice system now provides:

- **🛡️ Security**: Comprehensive audit logging and access control
- **⚖️ Dispute Resolution**: Complete workflow for handling disagreements
- **🔔 Real-time Notifications**: Multi-channel communication system
- **👥 Role Management**: Granular permission control
- **📊 Compliance**: Full audit trail for regulatory requirements
- **🚀 Scalability**: Designed for enterprise-level usage

This implementation transforms the basic invoice system into a comprehensive, enterprise-ready solution that handles complex farm-to-farm business relationships with proper security, compliance, and user experience considerations.
