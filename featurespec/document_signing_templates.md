# Document Signing Template System

This document provides an overview of the document signing template system implemented in the NxtAcre Farm Management Platform.

## Overview

The template system allows users to create and manage document templates for the document signing feature. Templates can be used to quickly create new documents for signing without having to upload a new file each time. This is particularly useful for frequently used documents like lease agreements, contracts, and other standardized forms.

## Features

- Create and manage document templates
- View a list of available templates
- Create new documents from templates
- Save existing documents as templates
- Create default templates with predefined fields

## Technical Implementation

### Database Schema

The template system leverages the existing `signable_documents` table with the following key fields:

- `is_template` (boolean): Indicates whether the document is a template
- `template_id` (UUID): References another document that was used as a template

### API Endpoints

The following API endpoints have been added to support the template system:

#### Template Management

- `GET /document-signing/farm/:farmId/templates`: Get all templates for a farm
- `GET /document-signing/templates/:id`: Get a specific template by ID
- `POST /document-signing/farm/:farmId/templates`: Create a new template
- `POST /document-signing/signable-documents/:id/save-as-template`: Save an existing document as a template
- `POST /document-signing/templates/:templateId/create-document`: Create a document from a template
- `POST /document-signing/farm/:farmId/create-default-templates`: Create default templates for a farm

### Frontend Components

The following frontend components have been added:

- `TemplateList.tsx`: Displays a list of available templates and provides options to create new templates or use existing ones
- `CreateFromTemplate.tsx`: Allows users to create a new document from a selected template

### Default Templates

The system includes the following default templates:

1. **Farm Lease Agreement**: Standard lease agreement for farm land
2. **Equipment Rental Agreement**: Agreement for renting farm equipment
3. **Crop Purchase Contract**: Contract for purchasing crops

Each template includes predefined signature and date fields positioned appropriately on the document.

## Usage Instructions

### Accessing Templates

1. Navigate to the Document Signing page
2. Click on the "Templates" button in the top right corner

### Creating a New Template

1. From the Templates page, click "Create Template"
2. Upload a document file
3. Fill in the template details (title, description, document type)
4. Add fields to the template (signatures, dates, text fields, etc.)
5. Click "Create Template"

### Creating a Document from a Template

1. From the Templates page, find the template you want to use
2. Click "Use Template"
3. Fill in the document details (title, description)
4. Add signers who will need to sign the document
5. Click "Create Document"

### Saving a Document as a Template

1. From the Document Signing page, select an existing document
2. Click "Save as Template"
3. Fill in the template details
4. Click "Save Template"

### Creating Default Templates

1. From the Templates page, click "Create Default Templates"
2. The system will create the default templates with predefined fields

## File Structure

The template system consists of the following files:

- Backend:
  - `/server/controllers/documentTemplateController.js`: Controller for template-related API endpoints
  - `/server/templates/documents/`: Directory containing default template files

- Frontend:
  - `/src/pages/Documents/TemplateList.tsx`: Component for displaying and managing templates
  - `/src/pages/Documents/CreateFromTemplate.tsx`: Component for creating documents from templates

## Notes for Developers

- When creating new templates, ensure that the document file is properly formatted and that fields are positioned correctly.
- The template system uses the same document signing infrastructure, so any updates to the document signing system will also affect templates.
- Default templates are stored as placeholder text files that should be replaced with actual PDF files in production.