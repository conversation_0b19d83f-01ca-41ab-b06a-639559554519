import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, FlatList, TouchableOpacity, TextInput, Modal, ScrollView, ActivityIndicator } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { MainStackParamList } from '../navigation/MainNavigator';

type EmployeesScreenNavigationProp = NativeStackNavigationProp<MainStackParamList, 'MainTabs'>;

interface Employee {
  id: string;
  name: string;
  role: string;
  department: string;
  email: string;
  phone: string;
  status: 'active' | 'inactive' | 'on_leave';
  hireDate: string;
  skills: string[];
  avatar?: string;
}

const EmployeesScreen = () => {
  const navigation = useNavigation<EmployeesScreenNavigationProp>();
  const [searchQuery, setSearchQuery] = useState('');
  const [filterDepartment, setFilterDepartment] = useState<string | null>(null);
  const [isAddModalVisible, setAddModalVisible] = useState(false);
  const [loading, setLoading] = useState(true);
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [newEmployee, setNewEmployee] = useState<Partial<Employee>>({
    name: '',
    role: '',
    department: '',
    email: '',
    phone: '',
    status: 'active',
    hireDate: new Date().toISOString().split('T')[0],
    skills: [],
  });

  // Departments for filtering
  const departments = ['Field Operations', 'Equipment', 'Maintenance', 'Office', 'Management'];

  useEffect(() => {
    // In a real implementation, this would fetch employees from an API
    const fetchEmployees = async () => {
      try {
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Mock employee data
        const mockEmployees: Employee[] = [
          {
            id: 'emp123',
            name: 'John Doe',
            role: 'Field Worker',
            department: 'Field Operations',
            email: '<EMAIL>',
            phone: '(*************',
            status: 'active',
            hireDate: '2022-03-15',
            skills: ['Harvesting', 'Planting', 'Equipment Operation'],
          },
          {
            id: 'emp456',
            name: 'Jane Smith',
            role: 'Equipment Operator',
            department: 'Equipment',
            email: '<EMAIL>',
            phone: '(*************',
            status: 'active',
            hireDate: '2021-06-10',
            skills: ['Tractor Operation', 'Maintenance', 'Harvesting'],
          },
          {
            id: 'emp789',
            name: 'Bob Johnson',
            role: 'Maintenance Technician',
            department: 'Maintenance',
            email: '<EMAIL>',
            phone: '(*************',
            status: 'active',
            hireDate: '2022-01-05',
            skills: ['Repairs', 'Welding', 'Electrical'],
          },
          {
            id: 'emp101',
            name: 'Alice Williams',
            role: 'Office Manager',
            department: 'Office',
            email: '<EMAIL>',
            phone: '(*************',
            status: 'active',
            hireDate: '2020-11-20',
            skills: ['Administration', 'Scheduling', 'Bookkeeping'],
          },
          {
            id: 'emp202',
            name: 'Charlie Brown',
            role: 'Farm Manager',
            department: 'Management',
            email: '<EMAIL>',
            phone: '(*************',
            status: 'active',
            hireDate: '2019-08-15',
            skills: ['Leadership', 'Planning', 'Budgeting'],
          },
          {
            id: 'emp303',
            name: 'Diana Miller',
            role: 'Field Supervisor',
            department: 'Field Operations',
            email: '<EMAIL>',
            phone: '(*************',
            status: 'on_leave',
            hireDate: '2021-02-28',
            skills: ['Supervision', 'Training', 'Quality Control'],
          },
        ];
        
        setEmployees(mockEmployees);
      } catch (error) {
        console.error('Error fetching employees:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchEmployees();
  }, []);

  // Filter employees based on search query and department filter
  const filteredEmployees = employees.filter(employee => {
    const matchesSearch = employee.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         employee.role.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         employee.email.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesFilter = filterDepartment ? employee.department === filterDepartment : true;
    return matchesSearch && matchesFilter;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return '#4CAF50';
      case 'inactive':
        return '#F44336';
      case 'on_leave':
        return '#FF9800';
      default:
        return '#757575';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'active':
        return 'Active';
      case 'inactive':
        return 'Inactive';
      case 'on_leave':
        return 'On Leave';
      default:
        return status;
    }
  };

  const handleAddEmployee = () => {
    if (!newEmployee.name || !newEmployee.role || !newEmployee.department || !newEmployee.email) {
      return;
    }

    // In a real implementation, this would send the data to an API
    const newEmployeeData: Employee = {
      id: `emp${Date.now()}`, // Generate a temporary ID
      name: newEmployee.name || '',
      role: newEmployee.role || '',
      department: newEmployee.department || '',
      email: newEmployee.email || '',
      phone: newEmployee.phone || '',
      status: newEmployee.status as 'active' | 'inactive' | 'on_leave' || 'active',
      hireDate: newEmployee.hireDate || new Date().toISOString().split('T')[0],
      skills: newEmployee.skills || [],
    };

    // Add the new employee to the list
    setEmployees([...employees, newEmployeeData]);
    
    // Reset form and close modal
    setNewEmployee({
      name: '',
      role: '',
      department: '',
      email: '',
      phone: '',
      status: 'active',
      hireDate: new Date().toISOString().split('T')[0],
      skills: [],
    });
    setAddModalVisible(false);
  };

  const renderEmployeeItem = ({ item }: { item: Employee }) => (
    <TouchableOpacity 
      style={styles.employeeItem}
      onPress={() => navigation.navigate('EmployeeDetail', { employeeId: item.id })}
    >
      <View style={styles.employeeHeader}>
        <View style={styles.avatarContainer}>
          {item.avatar ? (
            <Text>Avatar would be here</Text>
          ) : (
            <Text style={styles.avatarText}>{item.name.split(' ').map(n => n[0]).join('')}</Text>
          )}
        </View>
        <View style={styles.employeeInfo}>
          <Text style={styles.employeeName}>{item.name}</Text>
          <Text style={styles.employeeRole}>{item.role}</Text>
        </View>
        <View style={[styles.statusBadge, { backgroundColor: getStatusColor(item.status) }]}>
          <Text style={styles.statusText}>{getStatusLabel(item.status)}</Text>
        </View>
      </View>
      
      <View style={styles.employeeDetails}>
        <View style={styles.detailItem}>
          <Ionicons name="business" size={16} color="#757575" />
          <Text style={styles.detailText}>{item.department}</Text>
        </View>
        <View style={styles.detailItem}>
          <Ionicons name="mail" size={16} color="#757575" />
          <Text style={styles.detailText}>{item.email}</Text>
        </View>
        <View style={styles.detailItem}>
          <Ionicons name="call" size={16} color="#757575" />
          <Text style={styles.detailText}>{item.phone}</Text>
        </View>
      </View>
      
      <View style={styles.skillsContainer}>
        {item.skills.map((skill, index) => (
          <View key={index} style={styles.skillBadge}>
            <Text style={styles.skillText}>{skill}</Text>
          </View>
        ))}
      </View>
    </TouchableOpacity>
  );

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#3b82f6" />
        <Text style={styles.loadingText}>Loading employees...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.searchContainer}>
        <Ionicons name="search" size={20} color="#757575" style={styles.searchIcon} />
        <TextInput
          style={styles.searchInput}
          placeholder="Search employees..."
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
      </View>

      <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.filterScrollView}>
        <View style={styles.filterContainer}>
          <TouchableOpacity
            style={[styles.filterButton, filterDepartment === null && styles.filterButtonActive]}
            onPress={() => setFilterDepartment(null)}
          >
            <Text style={[styles.filterButtonText, filterDepartment === null && styles.filterButtonTextActive]}>All</Text>
          </TouchableOpacity>
          
          {departments.map((department) => (
            <TouchableOpacity
              key={department}
              style={[styles.filterButton, filterDepartment === department && styles.filterButtonActive]}
              onPress={() => setFilterDepartment(department)}
            >
              <Text style={[styles.filterButtonText, filterDepartment === department && styles.filterButtonTextActive]}>
                {department}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </ScrollView>

      <FlatList
        data={filteredEmployees}
        renderItem={renderEmployeeItem}
        keyExtractor={item => item.id}
        contentContainerStyle={styles.employeeList}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Ionicons name="people" size={50} color="#BDBDBD" />
            <Text style={styles.emptyText}>No employees found</Text>
          </View>
        }
      />

      <TouchableOpacity 
        style={styles.fab}
        onPress={() => setAddModalVisible(true)}
      >
        <Ionicons name="add" size={24} color="#fff" />
      </TouchableOpacity>

      {/* Add Employee Modal */}
      <Modal
        animationType="slide"
        transparent={true}
        visible={isAddModalVisible}
        onRequestClose={() => setAddModalVisible(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Add New Employee</Text>
              <TouchableOpacity onPress={() => setAddModalVisible(false)}>
                <Ionicons name="close" size={24} color="#000" />
              </TouchableOpacity>
            </View>
            
            <ScrollView style={styles.modalBody}>
              <Text style={styles.inputLabel}>Name</Text>
              <TextInput
                style={styles.input}
                placeholder="Full name"
                value={newEmployee.name}
                onChangeText={(text) => setNewEmployee({...newEmployee, name: text})}
              />
              
              <Text style={styles.inputLabel}>Role</Text>
              <TextInput
                style={styles.input}
                placeholder="Job title"
                value={newEmployee.role}
                onChangeText={(text) => setNewEmployee({...newEmployee, role: text})}
              />
              
              <Text style={styles.inputLabel}>Department</Text>
              <View style={styles.departmentContainer}>
                {departments.map((department) => (
                  <TouchableOpacity
                    key={department}
                    style={[
                      styles.departmentButton,
                      newEmployee.department === department && styles.departmentButtonActive
                    ]}
                    onPress={() => setNewEmployee({...newEmployee, department})}
                  >
                    <Text style={[
                      styles.departmentButtonText,
                      newEmployee.department === department && styles.departmentButtonTextActive
                    ]}>
                      {department}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
              
              <Text style={styles.inputLabel}>Email</Text>
              <TextInput
                style={styles.input}
                placeholder="Email address"
                keyboardType="email-address"
                value={newEmployee.email}
                onChangeText={(text) => setNewEmployee({...newEmployee, email: text})}
              />
              
              <Text style={styles.inputLabel}>Phone</Text>
              <TextInput
                style={styles.input}
                placeholder="Phone number"
                keyboardType="phone-pad"
                value={newEmployee.phone}
                onChangeText={(text) => setNewEmployee({...newEmployee, phone: text})}
              />
              
              <Text style={styles.inputLabel}>Hire Date</Text>
              <TextInput
                style={styles.input}
                placeholder="YYYY-MM-DD"
                value={newEmployee.hireDate}
                onChangeText={(text) => setNewEmployee({...newEmployee, hireDate: text})}
              />
              
              <Text style={styles.inputLabel}>Status</Text>
              <View style={styles.statusContainer}>
                <TouchableOpacity
                  style={[
                    styles.statusButton,
                    newEmployee.status === 'active' && styles.statusButtonActive,
                    {borderColor: '#4CAF50'}
                  ]}
                  onPress={() => setNewEmployee({...newEmployee, status: 'active'})}
                >
                  <Text style={[
                    styles.statusButtonText,
                    newEmployee.status === 'active' && {color: '#4CAF50'}
                  ]}>Active</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[
                    styles.statusButton,
                    newEmployee.status === 'inactive' && styles.statusButtonActive,
                    {borderColor: '#F44336'}
                  ]}
                  onPress={() => setNewEmployee({...newEmployee, status: 'inactive'})}
                >
                  <Text style={[
                    styles.statusButtonText,
                    newEmployee.status === 'inactive' && {color: '#F44336'}
                  ]}>Inactive</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[
                    styles.statusButton,
                    newEmployee.status === 'on_leave' && styles.statusButtonActive,
                    {borderColor: '#FF9800'}
                  ]}
                  onPress={() => setNewEmployee({...newEmployee, status: 'on_leave'})}
                >
                  <Text style={[
                    styles.statusButtonText,
                    newEmployee.status === 'on_leave' && {color: '#FF9800'}
                  ]}>On Leave</Text>
                </TouchableOpacity>
              </View>
              
              <Text style={styles.inputLabel}>Skills (comma separated)</Text>
              <TextInput
                style={[styles.input, styles.textArea]}
                placeholder="Enter skills separated by commas"
                multiline
                numberOfLines={3}
                value={newEmployee.skills?.join(', ')}
                onChangeText={(text) => setNewEmployee({
                  ...newEmployee, 
                  skills: text.split(',').map(skill => skill.trim()).filter(skill => skill)
                })}
              />
            </ScrollView>
            
            <View style={styles.modalFooter}>
              <TouchableOpacity
                style={styles.cancelButton}
                onPress={() => setAddModalVisible(false)}
              >
                <Text style={styles.cancelButtonText}>Cancel</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[
                  styles.addButton,
                  (!newEmployee.name || !newEmployee.role || !newEmployee.department || !newEmployee.email) && 
                  styles.addButtonDisabled
                ]}
                onPress={handleAddEmployee}
                disabled={!newEmployee.name || !newEmployee.role || !newEmployee.department || !newEmployee.email}
              >
                <Text style={styles.addButtonText}>Add Employee</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#757575',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderRadius: 8,
    margin: 15,
    paddingHorizontal: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  searchIcon: {
    marginRight: 10,
  },
  searchInput: {
    flex: 1,
    height: 50,
    fontSize: 16,
  },
  filterScrollView: {
    maxHeight: 50,
  },
  filterContainer: {
    flexDirection: 'row',
    paddingHorizontal: 15,
    marginBottom: 10,
  },
  filterButton: {
    paddingVertical: 8,
    paddingHorizontal: 12,
    marginRight: 8,
    borderRadius: 20,
    backgroundColor: '#E0E0E0',
  },
  filterButtonActive: {
    backgroundColor: '#3b82f6',
  },
  filterButtonText: {
    color: '#757575',
    fontSize: 14,
  },
  filterButtonTextActive: {
    color: '#fff',
  },
  employeeList: {
    padding: 15,
    paddingBottom: 80, // Add padding for FAB
  },
  employeeItem: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 15,
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  employeeHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 15,
  },
  avatarContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#3b82f6',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
  },
  avatarText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
  employeeInfo: {
    flex: 1,
  },
  employeeName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#212121',
  },
  employeeRole: {
    fontSize: 14,
    color: '#757575',
  },
  statusBadge: {
    paddingVertical: 4,
    paddingHorizontal: 8,
    borderRadius: 4,
  },
  statusText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '500',
  },
  employeeDetails: {
    marginBottom: 15,
  },
  detailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 5,
  },
  detailText: {
    fontSize: 14,
    color: '#757575',
    marginLeft: 10,
  },
  skillsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  skillBadge: {
    backgroundColor: '#E0E0E0',
    borderRadius: 15,
    paddingVertical: 4,
    paddingHorizontal: 8,
    marginRight: 8,
    marginBottom: 8,
  },
  skillText: {
    fontSize: 12,
    color: '#757575',
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 50,
  },
  emptyText: {
    fontSize: 16,
    color: '#757575',
    marginTop: 10,
  },
  fab: {
    position: 'absolute',
    bottom: 20,
    right: 20,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: '#3b82f6',
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    width: '90%',
    maxHeight: '80%',
    backgroundColor: '#fff',
    borderRadius: 8,
    overflow: 'hidden',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  modalBody: {
    padding: 15,
    maxHeight: 400,
  },
  modalFooter: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    padding: 15,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 5,
    marginTop: 10,
  },
  input: {
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 4,
    padding: 10,
    fontSize: 16,
  },
  textArea: {
    height: 80,
    textAlignVertical: 'top',
  },
  departmentContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 5,
  },
  departmentButton: {
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 4,
    padding: 10,
    margin: 5,
  },
  departmentButtonActive: {
    backgroundColor: '#3b82f6',
    borderColor: '#3b82f6',
  },
  departmentButtonText: {
    fontSize: 14,
  },
  departmentButtonTextActive: {
    color: '#fff',
  },
  statusContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 5,
  },
  statusButton: {
    flex: 1,
    borderWidth: 1,
    borderRadius: 4,
    padding: 10,
    marginHorizontal: 5,
    alignItems: 'center',
  },
  statusButtonActive: {
    backgroundColor: 'rgba(0, 0, 0, 0.05)',
  },
  statusButtonText: {
    fontWeight: '500',
  },
  cancelButton: {
    paddingVertical: 10,
    paddingHorizontal: 15,
    marginRight: 10,
  },
  cancelButtonText: {
    color: '#757575',
    fontSize: 16,
  },
  addButton: {
    backgroundColor: '#3b82f6',
    paddingVertical: 10,
    paddingHorizontal: 15,
    borderRadius: 4,
  },
  addButtonDisabled: {
    backgroundColor: '#BDBDBD',
  },
  addButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '500',
  },
});

export default EmployeesScreen;