import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  Dimensions
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

// Mock data for analytics
const mockAnalyticsData = {
  financialMetrics: {
    revenue: {
      current: 1250000,
      previous: 1150000,
      change: 8.7
    },
    expenses: {
      current: 850000,
      previous: 820000,
      change: 3.7
    },
    profit: {
      current: 400000,
      previous: 330000,
      change: 21.2
    },
    costPerAcre: {
      current: 425,
      previous: 410,
      change: 3.7
    }
  },
  productionMetrics: {
    totalYield: {
      current: 12500,
      previous: 11800,
      change: 5.9,
      unit: 'bushels'
    },
    yieldPerAcre: {
      current: 185,
      previous: 175,
      change: 5.7,
      unit: 'bushels/acre'
    },
    harvestedAcres: {
      current: 2000,
      previous: 2000,
      change: 0,
      unit: 'acres'
    },
    cropQuality: {
      current: 92,
      previous: 89,
      change: 3.4,
      unit: '%'
    }
  },
  resourceMetrics: {
    fuelUsage: {
      current: 15000,
      previous: 16200,
      change: -7.4,
      unit: 'gallons'
    },
    laborHours: {
      current: 12500,
      previous: 13000,
      change: -3.8,
      unit: 'hours'
    },
    waterUsage: {
      current: 25000000,
      previous: 27500000,
      change: -9.1,
      unit: 'gallons'
    },
    fertilizer: {
      current: 125,
      previous: 130,
      change: -3.8,
      unit: 'tons'
    }
  },
  equipmentMetrics: {
    utilization: {
      current: 78,
      previous: 72,
      change: 8.3,
      unit: '%'
    },
    downtime: {
      current: 120,
      previous: 150,
      change: -20,
      unit: 'hours'
    },
    maintenanceCost: {
      current: 45000,
      previous: 52000,
      change: -13.5,
      unit: 'USD'
    },
    fuelEfficiency: {
      current: 0.85,
      previous: 0.78,
      change: 9.0,
      unit: 'acres/gallon'
    }
  },
  monthlyRevenue: [
    { month: 'Jan', value: 85000 },
    { month: 'Feb', value: 75000 },
    { month: 'Mar', value: 90000 },
    { month: 'Apr', value: 110000 },
    { month: 'May', value: 120000 },
    { month: 'Jun', value: 135000 },
    { month: 'Jul', value: 150000 },
    { month: 'Aug', value: 160000 },
    { month: 'Sep', value: 140000 },
    { month: 'Oct', value: 130000 },
    { month: 'Nov', value: 95000 },
    { month: 'Dec', value: 80000 }
  ],
  cropDistribution: [
    { crop: 'Corn', acres: 850, color: '#4CAF50' },
    { crop: 'Soybeans', acres: 650, color: '#FFC107' },
    { crop: 'Wheat', acres: 350, color: '#FF9800' },
    { crop: 'Alfalfa', acres: 150, color: '#2196F3' }
  ],
  fieldPerformance: [
    { field: 'North 40', yield: 195, target: 180, color: '#4CAF50' },
    { field: 'South Meadow', yield: 175, target: 180, color: '#FF9800' },
    { field: 'West Field', yield: 190, target: 180, color: '#4CAF50' },
    { field: 'East Ridge', yield: 165, target: 180, color: '#F44336' },
    { field: 'Creek Bottom', yield: 205, target: 180, color: '#4CAF50' }
  ]
};

// Simple bar chart component
const BarChart = ({ data, height = 200, barColor = '#3b82f6' }) => {
  const maxValue = Math.max(...data.map(item => item.value));
  
  return (
    <View style={[styles.chartContainer, { height }]}>
      {data.map((item, index) => (
        <View key={index} style={styles.barContainer}>
          <View 
            style={[
              styles.bar, 
              { 
                height: `${(item.value / maxValue) * 100}%`,
                backgroundColor: item.color || barColor
              }
            ]} 
          />
          <Text style={styles.barLabel}>{item.month || item.label}</Text>
        </View>
      ))}
    </View>
  );
};

// Simple pie chart component
const PieChart = ({ data, size = 200 }) => {
  const total = data.reduce((sum, item) => sum + item.acres, 0);
  let startAngle = 0;
  
  return (
    <View style={[styles.pieContainer, { width: size, height: size }]}>
      <View style={styles.pieChartContainer}>
        {/* This is a simplified representation - in a real app, use a proper charting library */}
        <View style={styles.pieChart}>
          <Text style={styles.pieTotal}>{total}</Text>
          <Text style={styles.pieTotalLabel}>acres</Text>
        </View>
      </View>
      
      <View style={styles.legendContainer}>
        {data.map((item, index) => (
          <View key={index} style={styles.legendItem}>
            <View style={[styles.legendColor, { backgroundColor: item.color }]} />
            <Text style={styles.legendText}>{item.crop}: {item.acres} acres</Text>
          </View>
        ))}
      </View>
    </View>
  );
};

// Horizontal bar chart for field performance
const HorizontalBarChart = ({ data, height = 200, targetColor = '#ccc' }) => {
  const maxValue = Math.max(...data.map(item => Math.max(item.yield, item.target)));
  const barWidth = Dimensions.get('window').width - 64; // Adjust based on padding
  
  return (
    <View style={[styles.horizontalChartContainer, { height }]}>
      {data.map((item, index) => (
        <View key={index} style={styles.horizontalBarRow}>
          <Text style={styles.horizontalBarLabel}>{item.field}</Text>
          <View style={styles.horizontalBarContainer}>
            {/* Target line */}
            <View 
              style={[
                styles.targetLine, 
                { 
                  left: `${(item.target / maxValue) * 100}%`,
                  backgroundColor: targetColor
                }
              ]} 
            />
            
            {/* Actual yield bar */}
            <View 
              style={[
                styles.horizontalBar, 
                { 
                  width: `${(item.yield / maxValue) * 100}%`,
                  backgroundColor: item.color
                }
              ]} 
            />
            
            <Text style={styles.horizontalBarValue}>{item.yield}</Text>
          </View>
        </View>
      ))}
      
      <View style={styles.legendContainer}>
        <View style={styles.legendItem}>
          <View style={[styles.legendColor, { backgroundColor: '#4CAF50' }]} />
          <Text style={styles.legendText}>Above Target</Text>
        </View>
        <View style={styles.legendItem}>
          <View style={[styles.legendColor, { backgroundColor: '#FF9800' }]} />
          <Text style={styles.legendText}>Near Target</Text>
        </View>
        <View style={styles.legendItem}>
          <View style={[styles.legendColor, { backgroundColor: '#F44336' }]} />
          <Text style={styles.legendText}>Below Target</Text>
        </View>
        <View style={styles.legendItem}>
          <View style={[styles.legendColor, { backgroundColor: '#ccc' }]} />
          <Text style={styles.legendText}>Target</Text>
        </View>
      </View>
    </View>
  );
};

// Metric card component
const MetricCard = ({ title, value, previousValue, change, unit = '', prefix = '' }) => {
  const isPositive = change >= 0;
  const changeColor = isPositive ? '#4CAF50' : '#F44336';
  const changeIcon = isPositive ? 'arrow-up' : 'arrow-down';
  
  return (
    <View style={styles.metricCard}>
      <Text style={styles.metricTitle}>{title}</Text>
      <Text style={styles.metricValue}>{prefix}{typeof value === 'number' ? value.toLocaleString() : value}{unit}</Text>
      
      <View style={styles.metricChangeContainer}>
        <Ionicons name={changeIcon} size={16} color={changeColor} />
        <Text style={[styles.metricChange, { color: changeColor }]}>
          {Math.abs(change).toFixed(1)}%
        </Text>
        <Text style={styles.metricPrevious}>vs. previous</Text>
      </View>
    </View>
  );
};

const AnalyticsScreen: React.FC = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [analyticsData, setAnalyticsData] = useState(mockAnalyticsData);
  const [timeRange, setTimeRange] = useState('year'); // 'month', 'quarter', 'year'
  const [activeTab, setActiveTab] = useState('financial'); // 'financial', 'production', 'resources', 'equipment'
  
  // Fetch analytics data
  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      // Simulate API call
      setTimeout(() => {
        setAnalyticsData(mockAnalyticsData);
        setIsLoading(false);
      }, 1000);
    };
    
    fetchData();
  }, [timeRange]);
  
  // Render time range selector
  const renderTimeRangeSelector = () => {
    return (
      <View style={styles.timeRangeContainer}>
        <TouchableOpacity
          style={[styles.timeRangeButton, timeRange === 'month' && styles.activeTimeRangeButton]}
          onPress={() => setTimeRange('month')}
        >
          <Text style={[styles.timeRangeText, timeRange === 'month' && styles.activeTimeRangeText]}>
            Month
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.timeRangeButton, timeRange === 'quarter' && styles.activeTimeRangeButton]}
          onPress={() => setTimeRange('quarter')}
        >
          <Text style={[styles.timeRangeText, timeRange === 'quarter' && styles.activeTimeRangeText]}>
            Quarter
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.timeRangeButton, timeRange === 'year' && styles.activeTimeRangeButton]}
          onPress={() => setTimeRange('year')}
        >
          <Text style={[styles.timeRangeText, timeRange === 'year' && styles.activeTimeRangeText]}>
            Year
          </Text>
        </TouchableOpacity>
      </View>
    );
  };
  
  // Render tab selector
  const renderTabSelector = () => {
    return (
      <View style={styles.tabsContainer}>
        <TouchableOpacity
          style={[styles.tabButton, activeTab === 'financial' && styles.activeTabButton]}
          onPress={() => setActiveTab('financial')}
        >
          <Ionicons 
            name="cash-outline" 
            size={20} 
            color={activeTab === 'financial' ? '#3b82f6' : '#666'} 
          />
          <Text style={[styles.tabText, activeTab === 'financial' && styles.activeTabText]}>
            Financial
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.tabButton, activeTab === 'production' && styles.activeTabButton]}
          onPress={() => setActiveTab('production')}
        >
          <Ionicons 
            name="leaf-outline" 
            size={20} 
            color={activeTab === 'production' ? '#3b82f6' : '#666'} 
          />
          <Text style={[styles.tabText, activeTab === 'production' && styles.activeTabText]}>
            Production
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.tabButton, activeTab === 'resources' && styles.activeTabButton]}
          onPress={() => setActiveTab('resources')}
        >
          <Ionicons 
            name="water-outline" 
            size={20} 
            color={activeTab === 'resources' ? '#3b82f6' : '#666'} 
          />
          <Text style={[styles.tabText, activeTab === 'resources' && styles.activeTabText]}>
            Resources
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.tabButton, activeTab === 'equipment' && styles.activeTabButton]}
          onPress={() => setActiveTab('equipment')}
        >
          <Ionicons 
            name="construct-outline" 
            size={20} 
            color={activeTab === 'equipment' ? '#3b82f6' : '#666'} 
          />
          <Text style={[styles.tabText, activeTab === 'equipment' && styles.activeTabText]}>
            Equipment
          </Text>
        </TouchableOpacity>
      </View>
    );
  };
  
  // Render financial metrics
  const renderFinancialMetrics = () => {
    const { financialMetrics, monthlyRevenue } = analyticsData;
    
    return (
      <View style={styles.tabContent}>
        <View style={styles.metricsRow}>
          <MetricCard 
            title="Revenue" 
            value={financialMetrics.revenue.current} 
            previousValue={financialMetrics.revenue.previous}
            change={financialMetrics.revenue.change}
            prefix="$"
          />
          <MetricCard 
            title="Expenses" 
            value={financialMetrics.expenses.current} 
            previousValue={financialMetrics.expenses.previous}
            change={financialMetrics.expenses.change * -1} // Invert for expenses
            prefix="$"
          />
        </View>
        
        <View style={styles.metricsRow}>
          <MetricCard 
            title="Profit" 
            value={financialMetrics.profit.current} 
            previousValue={financialMetrics.profit.previous}
            change={financialMetrics.profit.change}
            prefix="$"
          />
          <MetricCard 
            title="Cost Per Acre" 
            value={financialMetrics.costPerAcre.current} 
            previousValue={financialMetrics.costPerAcre.previous}
            change={financialMetrics.costPerAcre.change * -1} // Invert for cost
            prefix="$"
          />
        </View>
        
        <View style={styles.chartSection}>
          <Text style={styles.chartTitle}>Monthly Revenue</Text>
          <BarChart data={monthlyRevenue} height={200} />
        </View>
      </View>
    );
  };
  
  // Render production metrics
  const renderProductionMetrics = () => {
    const { productionMetrics, cropDistribution, fieldPerformance } = analyticsData;
    
    return (
      <View style={styles.tabContent}>
        <View style={styles.metricsRow}>
          <MetricCard 
            title="Total Yield" 
            value={productionMetrics.totalYield.current} 
            previousValue={productionMetrics.totalYield.previous}
            change={productionMetrics.totalYield.change}
            unit={` ${productionMetrics.totalYield.unit}`}
          />
          <MetricCard 
            title="Yield Per Acre" 
            value={productionMetrics.yieldPerAcre.current} 
            previousValue={productionMetrics.yieldPerAcre.previous}
            change={productionMetrics.yieldPerAcre.change}
            unit={` ${productionMetrics.yieldPerAcre.unit}`}
          />
        </View>
        
        <View style={styles.metricsRow}>
          <MetricCard 
            title="Harvested Acres" 
            value={productionMetrics.harvestedAcres.current} 
            previousValue={productionMetrics.harvestedAcres.previous}
            change={productionMetrics.harvestedAcres.change}
            unit={` ${productionMetrics.harvestedAcres.unit}`}
          />
          <MetricCard 
            title="Crop Quality" 
            value={productionMetrics.cropQuality.current} 
            previousValue={productionMetrics.cropQuality.previous}
            change={productionMetrics.cropQuality.change}
            unit={productionMetrics.cropQuality.unit}
          />
        </View>
        
        <View style={styles.chartSection}>
          <Text style={styles.chartTitle}>Crop Distribution</Text>
          <PieChart data={cropDistribution} size={200} />
        </View>
        
        <View style={styles.chartSection}>
          <Text style={styles.chartTitle}>Field Performance (Yield per Acre)</Text>
          <HorizontalBarChart data={fieldPerformance} height={250} />
        </View>
      </View>
    );
  };
  
  // Render resource metrics
  const renderResourceMetrics = () => {
    const { resourceMetrics } = analyticsData;
    
    return (
      <View style={styles.tabContent}>
        <View style={styles.metricsRow}>
          <MetricCard 
            title="Fuel Usage" 
            value={resourceMetrics.fuelUsage.current} 
            previousValue={resourceMetrics.fuelUsage.previous}
            change={resourceMetrics.fuelUsage.change}
            unit={` ${resourceMetrics.fuelUsage.unit}`}
          />
          <MetricCard 
            title="Labor Hours" 
            value={resourceMetrics.laborHours.current} 
            previousValue={resourceMetrics.laborHours.previous}
            change={resourceMetrics.laborHours.change}
            unit={` ${resourceMetrics.laborHours.unit}`}
          />
        </View>
        
        <View style={styles.metricsRow}>
          <MetricCard 
            title="Water Usage" 
            value={(resourceMetrics.waterUsage.current / 1000000).toFixed(1)} 
            previousValue={resourceMetrics.waterUsage.previous}
            change={resourceMetrics.waterUsage.change}
            unit=" million gallons"
          />
          <MetricCard 
            title="Fertilizer" 
            value={resourceMetrics.fertilizer.current} 
            previousValue={resourceMetrics.fertilizer.previous}
            change={resourceMetrics.fertilizer.change}
            unit={` ${resourceMetrics.fertilizer.unit}`}
          />
        </View>
        
        <View style={styles.chartSection}>
          <Text style={styles.chartTitle}>Resource Efficiency</Text>
          <Text style={styles.chartDescription}>
            Resource usage is down across all categories compared to the previous period,
            indicating improved efficiency in farm operations.
          </Text>
        </View>
      </View>
    );
  };
  
  // Render equipment metrics
  const renderEquipmentMetrics = () => {
    const { equipmentMetrics } = analyticsData;
    
    return (
      <View style={styles.tabContent}>
        <View style={styles.metricsRow}>
          <MetricCard 
            title="Utilization" 
            value={equipmentMetrics.utilization.current} 
            previousValue={equipmentMetrics.utilization.previous}
            change={equipmentMetrics.utilization.change}
            unit={equipmentMetrics.utilization.unit}
          />
          <MetricCard 
            title="Downtime" 
            value={equipmentMetrics.downtime.current} 
            previousValue={equipmentMetrics.downtime.previous}
            change={equipmentMetrics.downtime.change}
            unit={` ${equipmentMetrics.downtime.unit}`}
          />
        </View>
        
        <View style={styles.metricsRow}>
          <MetricCard 
            title="Maintenance Cost" 
            value={equipmentMetrics.maintenanceCost.current} 
            previousValue={equipmentMetrics.maintenanceCost.previous}
            change={equipmentMetrics.maintenanceCost.change}
            prefix="$"
          />
          <MetricCard 
            title="Fuel Efficiency" 
            value={equipmentMetrics.fuelEfficiency.current} 
            previousValue={equipmentMetrics.fuelEfficiency.previous}
            change={equipmentMetrics.fuelEfficiency.change}
            unit={` ${equipmentMetrics.fuelEfficiency.unit}`}
          />
        </View>
        
        <View style={styles.chartSection}>
          <Text style={styles.chartTitle}>Equipment Performance</Text>
          <Text style={styles.chartDescription}>
            Equipment utilization has increased while downtime and maintenance costs have decreased,
            indicating improved equipment management and maintenance practices.
          </Text>
        </View>
      </View>
    );
  };
  
  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#3b82f6" />
      </View>
    );
  }
  
  return (
    <View style={styles.container}>
      <ScrollView>
        <View style={styles.header}>
          <Text style={styles.headerTitle}>Farm Analytics</Text>
          {renderTimeRangeSelector()}
        </View>
        
        {renderTabSelector()}
        
        {activeTab === 'financial' && renderFinancialMetrics()}
        {activeTab === 'production' && renderProductionMetrics()}
        {activeTab === 'resources' && renderResourceMetrics()}
        {activeTab === 'equipment' && renderEquipmentMetrics()}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    padding: 16,
    backgroundColor: '#fff',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  timeRangeContainer: {
    flexDirection: 'row',
    backgroundColor: '#f0f0f0',
    borderRadius: 8,
    padding: 4,
  },
  timeRangeButton: {
    flex: 1,
    paddingVertical: 8,
    alignItems: 'center',
    borderRadius: 6,
  },
  activeTimeRangeButton: {
    backgroundColor: '#fff',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 1,
  },
  timeRangeText: {
    color: '#666',
    fontWeight: '500',
  },
  activeTimeRangeText: {
    color: '#3b82f6',
    fontWeight: 'bold',
  },
  tabsContainer: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  tabButton: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
  },
  activeTabButton: {
    borderBottomWidth: 2,
    borderBottomColor: '#3b82f6',
  },
  tabText: {
    color: '#666',
    fontWeight: '500',
    marginLeft: 4,
  },
  activeTabText: {
    color: '#3b82f6',
    fontWeight: 'bold',
  },
  tabContent: {
    padding: 16,
  },
  metricsRow: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  metricCard: {
    flex: 1,
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 16,
    marginHorizontal: 4,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 1,
  },
  metricTitle: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
  },
  metricValue: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  metricChangeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  metricChange: {
    fontSize: 14,
    fontWeight: 'bold',
    marginLeft: 4,
  },
  metricPrevious: {
    fontSize: 12,
    color: '#666',
    marginLeft: 4,
  },
  chartSection: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 16,
    marginTop: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 1,
  },
  chartTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 16,
    color: '#333',
  },
  chartDescription: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
  chartContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    justifyContent: 'space-between',
    marginTop: 16,
  },
  barContainer: {
    flex: 1,
    alignItems: 'center',
  },
  bar: {
    width: 20,
    borderTopLeftRadius: 4,
    borderTopRightRadius: 4,
  },
  barLabel: {
    fontSize: 10,
    color: '#666',
    marginTop: 4,
  },
  pieContainer: {
    alignItems: 'center',
  },
  pieChartContainer: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  pieChart: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: '#fff',
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 1,
  },
  pieTotal: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  pieTotalLabel: {
    fontSize: 12,
    color: '#666',
  },
  legendContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 16,
    marginBottom: 8,
  },
  legendColor: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 4,
  },
  legendText: {
    fontSize: 12,
    color: '#666',
  },
  horizontalChartContainer: {
    marginTop: 16,
  },
  horizontalBarRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  horizontalBarLabel: {
    width: 100,
    fontSize: 12,
    color: '#666',
  },
  horizontalBarContainer: {
    flex: 1,
    height: 24,
    backgroundColor: '#f0f0f0',
    borderRadius: 4,
    position: 'relative',
  },
  horizontalBar: {
    height: '100%',
    borderRadius: 4,
  },
  horizontalBarValue: {
    position: 'absolute',
    right: 8,
    top: 4,
    fontSize: 12,
    fontWeight: 'bold',
    color: '#fff',
  },
  targetLine: {
    position: 'absolute',
    width: 2,
    height: '100%',
    zIndex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default AnalyticsScreen;