import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  TextInput,
  ActivityIndicator,
  Alert,
  ScrollView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../../../../shared/store/AuthProvider';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { MainTabParamList } from '../navigation/MainNavigator';
import { CompositeScreenProps } from '@react-navigation/native';
import { BottomTabScreenProps } from '@react-navigation/bottom-tabs';
import { MainStackParamList } from '../navigation/MainNavigator';

// Mock inventory service
// This would be replaced with actual service once implemented
const inventoryService = {
  getInventoryItems: async (farmId: string) => {
    // Mock data
    return [
      {
        id: 'inv1',
        name: 'Corn Seeds',
        category: 'Seeds',
        quantity: 500,
        unit: 'kg',
        location: 'Warehouse A',
        lastUpdated: '2023-06-15',
        minimumLevel: 100,
        status: 'normal',
      },
      {
        id: 'inv2',
        name: 'NPK Fertilizer',
        category: 'Fertilizer',
        quantity: 50,
        unit: 'kg',
        location: 'Warehouse B',
        lastUpdated: '2023-06-10',
        minimumLevel: 200,
        status: 'low',
      },
      {
        id: 'inv3',
        name: 'Organic Pesticide',
        category: 'Pesticide',
        quantity: 5,
        unit: 'L',
        location: 'Storage Room 1',
        lastUpdated: '2023-06-20',
        minimumLevel: 10,
        status: 'low',
      },
      {
        id: 'inv4',
        name: 'Chicken Feed',
        category: 'Feed',
        quantity: 750,
        unit: 'kg',
        location: 'Feed Storage',
        lastUpdated: '2023-06-18',
        minimumLevel: 150,
        status: 'normal',
      },
      {
        id: 'inv5',
        name: 'Irrigation Pipes',
        category: 'Supplies',
        quantity: 30,
        unit: 'pieces',
        location: 'Equipment Shed',
        lastUpdated: '2023-05-25',
        minimumLevel: 10,
        status: 'normal',
      },
      {
        id: 'inv6',
        name: 'Tomato Seeds',
        category: 'Seeds',
        quantity: 10,
        unit: 'packets',
        location: 'Warehouse A',
        lastUpdated: '2023-06-12',
        minimumLevel: 20,
        status: 'low',
      },
      {
        id: 'inv7',
        name: 'Diesel Fuel',
        category: 'Fuel',
        quantity: 200,
        unit: 'L',
        location: 'Fuel Storage',
        lastUpdated: '2023-06-05',
        minimumLevel: 100,
        status: 'normal',
      },
      {
        id: 'inv8',
        name: 'Hay Bales',
        category: 'Feed',
        quantity: 50,
        unit: 'bales',
        location: 'Barn',
        lastUpdated: '2023-06-08',
        minimumLevel: 20,
        status: 'normal',
      },
    ];
  }
};

type InventoryScreenProps = CompositeScreenProps<
  BottomTabScreenProps<MainTabParamList, 'Inventory'>,
  NativeStackScreenProps<MainStackParamList>
>;

type InventoryItem = {
  id: string;
  name: string;
  category: string;
  quantity: number;
  unit: string;
  location: string;
  lastUpdated: string;
  minimumLevel: number;
  status: 'low' | 'normal';
};

const InventoryScreen: React.FC<InventoryScreenProps> = ({ navigation }) => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [inventoryItems, setInventoryItems] = useState<InventoryItem[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterCategory, setFilterCategory] = useState<string | null>(null);
  const [filterStatus, setFilterStatus] = useState<'all' | 'low'>('all');

  // Categories for filtering
  const categories = ['Seeds', 'Fertilizer', 'Pesticide', 'Feed', 'Supplies', 'Fuel'];

  const loadData = async () => {
    try {
      setLoading(true);

      if (!user?.farmId) {
        console.error('No farm ID available');
        setLoading(false);
        return;
      }

      // Load inventory items
      const items = await inventoryService.getInventoryItems(user.farmId);
      setInventoryItems(items);

      setLoading(false);
    } catch (error) {
      console.error('Error loading inventory data:', error);
      setLoading(false);
    }
  };

  useEffect(() => {
    loadData();
  }, [user?.farmId]);

  const onRefresh = async () => {
    setRefreshing(true);
    await loadData();
    setRefreshing(false);
  };

  // Filter inventory items based on search query, category filter, and status filter
  const filteredItems = inventoryItems.filter(item => {
    const matchesSearch = item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         item.category.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         item.location.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = filterCategory ? item.category === filterCategory : true;
    const matchesStatus = filterStatus === 'all' ? true : item.status === 'low';
    return matchesSearch && matchesCategory && matchesStatus;
  });

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { year: 'numeric', month: 'short', day: 'numeric' });
  };

  const handleOrderMore = (item: InventoryItem) => {
    Alert.alert(
      "Order More",
      `Would you like to order more ${item.name}?`,
      [
        {
          text: "Cancel",
          style: "cancel"
        },
        { 
          text: "Yes", 
          onPress: () => console.log(`Ordering more ${item.name}`) 
        }
      ]
    );
  };

  const renderInventoryItem = ({ item }: { item: InventoryItem }) => (
    <View style={styles.inventoryItem}>
      <View style={styles.itemHeader}>
        <Text style={styles.itemName}>{item.name}</Text>
        {item.status === 'low' && (
          <View style={styles.lowStockBadge}>
            <Text style={styles.lowStockText}>Low Stock</Text>
          </View>
        )}
      </View>

      <View style={styles.itemDetails}>
        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>Category:</Text>
          <Text style={styles.detailText}>{item.category}</Text>
        </View>

        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>Quantity:</Text>
          <Text style={styles.detailText}>{item.quantity} {item.unit}</Text>
        </View>

        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>Location:</Text>
          <Text style={styles.detailText}>{item.location}</Text>
        </View>

        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>Last Updated:</Text>
          <Text style={styles.detailText}>{formatDate(item.lastUpdated)}</Text>
        </View>
      </View>

      <View style={styles.itemActions}>
        <TouchableOpacity 
          style={styles.actionButton}
          onPress={() => navigation.navigate('InventoryDetail', { inventoryId: item.id })}
        >
          <Ionicons name="information-circle-outline" size={20} color="#3b82f6" />
          <Text style={styles.actionButtonText}>Details</Text>
        </TouchableOpacity>

        <TouchableOpacity 
          style={styles.actionButton}
          onPress={() => handleOrderMore(item)}
        >
          <Ionicons name="cart-outline" size={20} color="#3b82f6" />
          <Text style={styles.actionButtonText}>Order More</Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#3b82f6" />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.searchContainer}>
        <Ionicons name="search" size={20} color="#757575" style={styles.searchIcon} />
        <TextInput
          style={styles.searchInput}
          placeholder="Search inventory..."
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
      </View>

      <View style={styles.filterContainer}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          <TouchableOpacity
            style={[styles.filterButton, filterCategory === null && styles.filterButtonActive]}
            onPress={() => setFilterCategory(null)}
          >
            <Text style={[styles.filterButtonText, filterCategory === null && styles.filterButtonTextActive]}>All</Text>
          </TouchableOpacity>

          {categories.map((category) => (
            <TouchableOpacity
              key={category}
              style={[styles.filterButton, filterCategory === category && styles.filterButtonActive]}
              onPress={() => setFilterCategory(category)}
            >
              <Text style={[styles.filterButtonText, filterCategory === category && styles.filterButtonTextActive]}>
                {category}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>

      <View style={styles.statusFilterContainer}>
        <TouchableOpacity
          style={[styles.statusButton, filterStatus === 'all' && styles.statusButtonActive]}
          onPress={() => setFilterStatus('all')}
        >
          <Text style={[styles.statusButtonText, filterStatus === 'all' && styles.statusButtonTextActive]}>All Items</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.statusButton, filterStatus === 'low' && styles.statusButtonActive]}
          onPress={() => setFilterStatus('low')}
        >
          <Text style={[styles.statusButtonText, filterStatus === 'low' && styles.statusButtonTextActive]}>Low Stock Only</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.summaryContainer}>
        <View style={styles.summaryItem}>
          <Text style={styles.summaryNumber}>{inventoryItems.length}</Text>
          <Text style={styles.summaryLabel}>Total Items</Text>
        </View>
        <View style={styles.summaryItem}>
          <Text style={[styles.summaryNumber, { color: '#ef4444' }]}>
            {inventoryItems.filter(item => item.status === 'low').length}
          </Text>
          <Text style={styles.summaryLabel}>Low Stock</Text>
        </View>
        <View style={styles.summaryItem}>
          <Text style={styles.summaryNumber}>
            {categories.length}
          </Text>
          <Text style={styles.summaryLabel}>Categories</Text>
        </View>
      </View>

      <FlatList
        data={filteredItems}
        renderItem={renderInventoryItem}
        keyExtractor={item => item.id}
        contentContainerStyle={styles.inventoryList}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Ionicons name="cube" size={50} color="#BDBDBD" />
            <Text style={styles.emptyText}>No inventory items found</Text>
          </View>
        }
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9fafb',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    borderRadius: 8,
    margin: 15,
    paddingHorizontal: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  searchIcon: {
    marginRight: 10,
  },
  searchInput: {
    flex: 1,
    height: 50,
    fontSize: 16,
  },
  filterContainer: {
    paddingHorizontal: 15,
    marginBottom: 10,
  },
  filterButton: {
    paddingVertical: 8,
    paddingHorizontal: 12,
    marginRight: 8,
    borderRadius: 20,
    backgroundColor: '#E0E0E0',
  },
  filterButtonActive: {
    backgroundColor: '#3b82f6',
  },
  filterButtonText: {
    color: '#757575',
    fontSize: 14,
  },
  filterButtonTextActive: {
    color: 'white',
  },
  statusFilterContainer: {
    flexDirection: 'row',
    paddingHorizontal: 15,
    marginBottom: 15,
  },
  statusButton: {
    flex: 1,
    paddingVertical: 8,
    alignItems: 'center',
    backgroundColor: '#E0E0E0',
    marginRight: 5,
    borderRadius: 4,
  },
  statusButtonActive: {
    backgroundColor: '#3b82f6',
  },
  statusButtonText: {
    color: '#757575',
    fontSize: 14,
  },
  statusButtonTextActive: {
    color: 'white',
  },
  summaryContainer: {
    flexDirection: 'row',
    backgroundColor: 'white',
    borderRadius: 8,
    margin: 15,
    marginTop: 0,
    padding: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  summaryItem: {
    flex: 1,
    alignItems: 'center',
  },
  summaryNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#3b82f6',
  },
  summaryLabel: {
    fontSize: 14,
    color: '#6b7280',
    marginTop: 5,
  },
  inventoryList: {
    padding: 15,
    paddingTop: 0,
  },
  inventoryItem: {
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 15,
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  itemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  itemName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#212121',
    flex: 1,
  },
  lowStockBadge: {
    backgroundColor: '#ef4444',
    paddingVertical: 4,
    paddingHorizontal: 8,
    borderRadius: 4,
  },
  lowStockText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '500',
  },
  itemDetails: {
    borderTopWidth: 1,
    borderTopColor: '#E0E0E0',
    paddingTop: 10,
  },
  detailRow: {
    flexDirection: 'row',
    marginBottom: 5,
  },
  detailLabel: {
    fontSize: 14,
    color: '#6b7280',
    width: 100,
  },
  detailText: {
    fontSize: 14,
    color: '#212121',
    flex: 1,
  },
  itemActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginTop: 10,
    borderTopWidth: 1,
    borderTopColor: '#E0E0E0',
    paddingTop: 10,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: 15,
    padding: 5,
  },
  actionButtonText: {
    color: '#3b82f6',
    fontSize: 14,
    marginLeft: 5,
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 50,
  },
  emptyText: {
    fontSize: 16,
    color: '#6b7280',
    marginTop: 10,
  },
});

export default InventoryScreen;
