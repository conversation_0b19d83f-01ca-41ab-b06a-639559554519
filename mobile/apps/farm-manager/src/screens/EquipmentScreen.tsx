import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  TextInput,
  ActivityIndicator,
  RefreshControl,
  Alert,
  ScrollView
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { MainStackParamList } from '../navigation/MainNavigator';

// Mock data for equipment
const mockEquipment = [
  {
    id: '1',
    name: 'Tractor - <PERSON> 8R',
    type: 'Tractor',
    status: 'Operational',
    lastMaintenance: '2023-05-15',
    nextMaintenance: '2023-08-15',
    hoursUsed: 1250,
    location: 'Main Barn',
    assignedTo: '<PERSON>',
    purchaseDate: '2020-03-10',
    purchasePrice: 250000,
    currentValue: 210000,
    image: 'https://example.com/tractor.jpg'
  },
  {
    id: '2',
    name: 'Combine - Case IH 8250',
    type: 'Harvester',
    status: 'Maintenance Required',
    lastMaintenance: '2023-02-20',
    nextMaintenance: '2023-06-20',
    hoursUsed: 890,
    location: 'Equipment Shed',
    assignedTo: 'Unassigned',
    purchaseDate: '2021-01-15',
    purchasePrice: 375000,
    currentValue: 350000,
    image: 'https://example.com/combine.jpg'
  },
  {
    id: '3',
    name: 'Sprayer - John Deere R4044',
    type: 'Sprayer',
    status: 'Operational',
    lastMaintenance: '2023-04-10',
    nextMaintenance: '2023-07-10',
    hoursUsed: 560,
    location: 'Field 3',
    assignedTo: 'Sarah Johnson',
    purchaseDate: '2022-02-28',
    purchasePrice: 180000,
    currentValue: 170000,
    image: 'https://example.com/sprayer.jpg'
  },
  {
    id: '4',
    name: 'Planter - Kinze 3660',
    type: 'Planter',
    status: 'Under Repair',
    lastMaintenance: '2023-03-05',
    nextMaintenance: '2023-06-05',
    hoursUsed: 320,
    location: 'Repair Shop',
    assignedTo: 'Unassigned',
    purchaseDate: '2021-11-10',
    purchasePrice: 120000,
    currentValue: 105000,
    image: 'https://example.com/planter.jpg'
  },
  {
    id: '5',
    name: 'Utility Vehicle - Kubota RTV-X1100C',
    type: 'Utility Vehicle',
    status: 'Operational',
    lastMaintenance: '2023-05-01',
    nextMaintenance: '2023-08-01',
    hoursUsed: 450,
    location: 'Main Farm',
    assignedTo: 'Multiple',
    purchaseDate: '2022-06-15',
    purchasePrice: 25000,
    currentValue: 22000,
    image: 'https://example.com/utv.jpg'
  }
];

type EquipmentScreenNavigationProp = NativeStackNavigationProp<MainStackParamList>;

const EquipmentScreen: React.FC = () => {
  const navigation = useNavigation<EquipmentScreenNavigationProp>();
  const [equipment, setEquipment] = useState(mockEquipment);
  const [filteredEquipment, setFilteredEquipment] = useState(mockEquipment);
  const [searchQuery, setSearchQuery] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [filterStatus, setFilterStatus] = useState<string | null>(null);

  // Filter equipment based on search query and status filter
  useEffect(() => {
    let result = equipment;

    if (searchQuery) {
      result = result.filter(item => 
        item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.type.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.status.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.location.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.assignedTo.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    if (filterStatus) {
      result = result.filter(item => item.status === filterStatus);
    }

    setFilteredEquipment(result);
  }, [searchQuery, equipment, filterStatus]);

  // Simulate fetching equipment data
  const fetchEquipment = async () => {
    setIsLoading(true);
    // Simulate API call
    setTimeout(() => {
      setEquipment(mockEquipment);
      setFilteredEquipment(mockEquipment);
      setIsLoading(false);
    }, 1000);
  };

  // Handle refresh
  const onRefresh = async () => {
    setRefreshing(true);
    await fetchEquipment();
    setRefreshing(false);
  };

  // Navigate to equipment detail screen
  const handleEquipmentPress = (equipmentId: string) => {
    navigation.navigate('EquipmentDetail', { equipmentId });
  };

  // Add new equipment
  const handleAddEquipment = () => {
    Alert.alert('Add Equipment', 'This feature will be implemented soon.');
    // In a real app, navigate to an add equipment form
    // navigation.navigate('AddEquipment');
  };

  // Render status badge with appropriate color
  const renderStatusBadge = (status: string) => {
    let color = '#4CAF50'; // Default green for operational

    if (status === 'Maintenance Required') {
      color = '#FF9800'; // Orange for maintenance
    } else if (status === 'Under Repair') {
      color = '#F44336'; // Red for repair
    } else if (status === 'Out of Service') {
      color = '#9E9E9E'; // Grey for out of service
    }

    return (
      <View style={[styles.statusBadge, { backgroundColor: color }]}>
        <Text style={styles.statusText}>{status}</Text>
      </View>
    );
  };

  // Render filter buttons
  const renderFilterButtons = () => {
    const statuses = ['Operational', 'Maintenance Required', 'Under Repair', 'Out of Service'];

    return (
      <View style={styles.filterContainer}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          <TouchableOpacity
            style={[
              styles.filterButton,
              filterStatus === null && styles.filterButtonActive
            ]}
            onPress={() => setFilterStatus(null)}
          >
            <Text style={filterStatus === null ? styles.filterTextActive : styles.filterText}>
              All
            </Text>
          </TouchableOpacity>

          {statuses.map(status => (
            <TouchableOpacity
              key={status}
              style={[
                styles.filterButton,
                filterStatus === status && styles.filterButtonActive
              ]}
              onPress={() => setFilterStatus(status)}
            >
              <Text style={filterStatus === status ? styles.filterTextActive : styles.filterText}>
                {status}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>
    );
  };

  // Render equipment item
  const renderEquipmentItem = ({ item }: { item: typeof mockEquipment[0] }) => (
    <TouchableOpacity
      style={styles.equipmentItem}
      onPress={() => handleEquipmentPress(item.id)}
    >
      <View style={styles.equipmentHeader}>
        <Text style={styles.equipmentName}>{item.name}</Text>
        {renderStatusBadge(item.status)}
      </View>

      <View style={styles.equipmentDetails}>
        <View style={styles.detailRow}>
          <Ionicons name="construct-outline" size={16} color="#666" />
          <Text style={styles.detailText}>Type: {item.type}</Text>
        </View>

        <View style={styles.detailRow}>
          <Ionicons name="location-outline" size={16} color="#666" />
          <Text style={styles.detailText}>Location: {item.location}</Text>
        </View>

        <View style={styles.detailRow}>
          <Ionicons name="person-outline" size={16} color="#666" />
          <Text style={styles.detailText}>Assigned to: {item.assignedTo}</Text>
        </View>

        <View style={styles.detailRow}>
          <Ionicons name="time-outline" size={16} color="#666" />
          <Text style={styles.detailText}>Hours: {item.hoursUsed}</Text>
        </View>

        <View style={styles.detailRow}>
          <Ionicons name="calendar-outline" size={16} color="#666" />
          <Text style={styles.detailText}>Next Maintenance: {item.nextMaintenance}</Text>
        </View>
      </View>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      <View style={styles.searchContainer}>
        <Ionicons name="search" size={20} color="#666" style={styles.searchIcon} />
        <TextInput
          style={styles.searchInput}
          placeholder="Search equipment..."
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
        {searchQuery ? (
          <TouchableOpacity onPress={() => setSearchQuery('')}>
            <Ionicons name="close-circle" size={20} color="#666" />
          </TouchableOpacity>
        ) : null}
      </View>

      {renderFilterButtons()}

      {isLoading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#3b82f6" />
        </View>
      ) : (
        <>
          <FlatList
            data={filteredEquipment}
            keyExtractor={item => item.id}
            renderItem={renderEquipmentItem}
            contentContainerStyle={styles.listContainer}
            refreshControl={
              <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
            }
            ListEmptyComponent={
              <View style={styles.emptyContainer}>
                <Ionicons name="alert-circle-outline" size={48} color="#666" />
                <Text style={styles.emptyText}>No equipment found</Text>
              </View>
            }
          />

          <TouchableOpacity style={styles.addButton} onPress={handleAddEquipment}>
            <Ionicons name="add" size={24} color="#fff" />
          </TouchableOpacity>
        </>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderRadius: 8,
    margin: 16,
    paddingHorizontal: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 1,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    height: 48,
    fontSize: 16,
  },
  filterContainer: {
    marginHorizontal: 16,
    marginBottom: 8,
  },
  filterButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: '#fff',
    marginRight: 8,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  filterButtonActive: {
    backgroundColor: '#3b82f6',
    borderColor: '#3b82f6',
  },
  filterText: {
    color: '#666',
  },
  filterTextActive: {
    color: '#fff',
  },
  listContainer: {
    padding: 16,
  },
  equipmentItem: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 1,
  },
  equipmentHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  equipmentName: {
    fontSize: 18,
    fontWeight: 'bold',
    flex: 1,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  statusText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  equipmentDetails: {
    marginTop: 8,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  detailText: {
    marginLeft: 8,
    color: '#666',
    fontSize: 14,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  emptyText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
  addButton: {
    position: 'absolute',
    bottom: 24,
    right: 24,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: '#3b82f6',
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
});

export default EquipmentScreen;
