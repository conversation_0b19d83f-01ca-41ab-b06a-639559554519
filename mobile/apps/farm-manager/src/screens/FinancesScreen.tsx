import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  ActivityIndicator,
  FlatList,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../../../../shared/store/AuthProvider';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { MainTabParamList } from '../navigation/MainNavigator';
import { CompositeScreenProps } from '@react-navigation/native';
import { BottomTabScreenProps } from '@react-navigation/bottom-tabs';
import { MainStackParamList } from '../navigation/MainNavigator';

// Mock finance service
// This would be replaced with actual service once implemented
const financeService = {
  getFinancialSummary: async (farmId: string) => {
    // Mock data
    return {
      revenue: 125000,
      expenses: 75000,
      profit: 50000,
      pendingInvoices: 3,
      paidInvoices: 12,
      overdueInvoices: 1,
      recentTransactions: [
        { id: '1', type: 'income', amount: 5000, description: 'Crop sale - Corn', date: '2023-06-15', status: 'completed' },
        { id: '2', type: 'expense', amount: 1200, description: 'Equipment repair', date: '2023-06-12', status: 'completed' },
        { id: '3', type: 'income', amount: 3500, description: 'Crop sale - Wheat', date: '2023-06-10', status: 'completed' },
        { id: '4', type: 'expense', amount: 800, description: 'Fertilizer purchase', date: '2023-06-08', status: 'completed' },
        { id: '5', type: 'expense', amount: 1500, description: 'Fuel', date: '2023-06-05', status: 'completed' },
      ],
      invoices: [
        { id: '1', amount: 2500, customer: 'ABC Distributors', dueDate: '2023-07-15', status: 'pending' },
        { id: '2', amount: 1800, customer: 'XYZ Markets', dueDate: '2023-07-10', status: 'pending' },
        { id: '3', amount: 3200, customer: 'Local Co-op', dueDate: '2023-06-30', status: 'overdue' },
        { id: '4', amount: 4500, customer: 'Farm Supply Inc', dueDate: '2023-06-15', status: 'paid' },
        { id: '5', amount: 2200, customer: 'Regional Buyers', dueDate: '2023-06-10', status: 'paid' },
      ],
      budgets: [
        { id: '1', category: 'Equipment', budgeted: 25000, spent: 18000, remaining: 7000 },
        { id: '2', category: 'Seeds', budgeted: 15000, spent: 12000, remaining: 3000 },
        { id: '3', category: 'Fertilizer', budgeted: 20000, spent: 15000, remaining: 5000 },
        { id: '4', category: 'Labor', budgeted: 30000, spent: 22000, remaining: 8000 },
        { id: '5', category: 'Fuel', budgeted: 10000, spent: 8000, remaining: 2000 },
      ]
    };
  }
};

type FinancesScreenProps = CompositeScreenProps<
  BottomTabScreenProps<MainTabParamList, 'Finances'>,
  NativeStackScreenProps<MainStackParamList>
>;

const FinancesScreen: React.FC<FinancesScreenProps> = ({ navigation }) => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [financialData, setFinancialData] = useState<any>(null);
  const [activeTab, setActiveTab] = useState('overview');

  const loadData = async () => {
    try {
      setLoading(true);

      if (!user?.farmId) {
        console.error('No farm ID available');
        setLoading(false);
        return;
      }

      // Load financial data
      const data = await financeService.getFinancialSummary(user.farmId);
      setFinancialData(data);

      setLoading(false);
    } catch (error) {
      console.error('Error loading financial data:', error);
      setLoading(false);
    }
  };

  useEffect(() => {
    loadData();
  }, [user?.farmId]);

  const onRefresh = async () => {
    setRefreshing(true);
    await loadData();
    setRefreshing(false);
  };

  const formatCurrency = (amount: number) => {
    return `$${amount.toLocaleString()}`;
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { year: 'numeric', month: 'short', day: 'numeric' });
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#3b82f6" />
      </View>
    );
  }

  const renderOverviewTab = () => (
    <>
      <View style={styles.summaryContainer}>
        <View style={styles.summaryCard}>
          <Text style={styles.summaryLabel}>Revenue</Text>
          <Text style={[styles.summaryValue, { color: '#22c55e' }]}>
            {formatCurrency(financialData.revenue)}
          </Text>
        </View>
        <View style={styles.summaryCard}>
          <Text style={styles.summaryLabel}>Expenses</Text>
          <Text style={[styles.summaryValue, { color: '#ef4444' }]}>
            {formatCurrency(financialData.expenses)}
          </Text>
        </View>
        <View style={styles.summaryCard}>
          <Text style={styles.summaryLabel}>Profit</Text>
          <Text style={[styles.summaryValue, { color: '#3b82f6' }]}>
            {formatCurrency(financialData.profit)}
          </Text>
        </View>
      </View>

      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Invoice Status</Text>
        </View>
        <View style={styles.invoiceStatusContainer}>
          <View style={styles.invoiceStatusItem}>
            <Text style={styles.invoiceStatusNumber}>{financialData.pendingInvoices}</Text>
            <Text style={styles.invoiceStatusLabel}>Pending</Text>
          </View>
          <View style={styles.invoiceStatusItem}>
            <Text style={styles.invoiceStatusNumber}>{financialData.paidInvoices}</Text>
            <Text style={styles.invoiceStatusLabel}>Paid</Text>
          </View>
          <View style={styles.invoiceStatusItem}>
            <Text style={[styles.invoiceStatusNumber, { color: '#ef4444' }]}>{financialData.overdueInvoices}</Text>
            <Text style={styles.invoiceStatusLabel}>Overdue</Text>
          </View>
        </View>
      </View>

      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Recent Transactions</Text>
          <TouchableOpacity onPress={() => setActiveTab('transactions')}>
            <Text style={styles.seeAllText}>See All</Text>
          </TouchableOpacity>
        </View>
        {financialData.recentTransactions.slice(0, 3).map((transaction: any) => (
          <View key={transaction.id} style={styles.transactionItem}>
            <View style={styles.transactionIconContainer}>
              <Ionicons
                name={transaction.type === 'income' ? 'arrow-down-circle' : 'arrow-up-circle'}
                size={24}
                color={transaction.type === 'income' ? '#22c55e' : '#ef4444'}
              />
            </View>
            <View style={styles.transactionDetails}>
              <Text style={styles.transactionDescription}>{transaction.description}</Text>
              <Text style={styles.transactionDate}>{formatDate(transaction.date)}</Text>
            </View>
            <Text
              style={[
                styles.transactionAmount,
                { color: transaction.type === 'income' ? '#22c55e' : '#ef4444' }
              ]}
            >
              {transaction.type === 'income' ? '+' : '-'}{formatCurrency(transaction.amount)}
            </Text>
          </View>
        ))}
      </View>

      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Budget Overview</Text>
          <TouchableOpacity onPress={() => setActiveTab('budgets')}>
            <Text style={styles.seeAllText}>See All</Text>
          </TouchableOpacity>
        </View>
        {financialData.budgets.slice(0, 3).map((budget: any) => (
          <View key={budget.id} style={styles.budgetItem}>
            <View style={styles.budgetHeader}>
              <Text style={styles.budgetCategory}>{budget.category}</Text>
              <Text style={styles.budgetRemaining}>{formatCurrency(budget.remaining)} remaining</Text>
            </View>
            <View style={styles.budgetProgressContainer}>
              <View
                style={[
                  styles.budgetProgress,
                  { width: `${(budget.spent / budget.budgeted) * 100}%` }
                ]}
              />
            </View>
            <View style={styles.budgetDetails}>
              <Text style={styles.budgetSpent}>Spent: {formatCurrency(budget.spent)}</Text>
              <Text style={styles.budgetTotal}>of {formatCurrency(budget.budgeted)}</Text>
            </View>
          </View>
        ))}
      </View>
    </>
  );

  const renderTransactionsTab = () => (
    <>
      <View style={styles.filterContainer}>
        <TouchableOpacity style={[styles.filterButton, styles.filterButtonActive]}>
          <Text style={styles.filterButtonTextActive}>All</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.filterButton}>
          <Text style={styles.filterButtonText}>Income</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.filterButton}>
          <Text style={styles.filterButtonText}>Expenses</Text>
        </TouchableOpacity>
      </View>

      <FlatList
        data={financialData.recentTransactions}
        keyExtractor={(item) => item.id}
        renderItem={({ item }) => (
          <TouchableOpacity 
            style={styles.transactionItem}
            onPress={() => navigation.navigate('FinancialDetail', { financialId: item.id })}
          >
            <View style={styles.transactionIconContainer}>
              <Ionicons
                name={item.type === 'income' ? 'arrow-down-circle' : 'arrow-up-circle'}
                size={24}
                color={item.type === 'income' ? '#22c55e' : '#ef4444'}
              />
            </View>
            <View style={styles.transactionDetails}>
              <Text style={styles.transactionDescription}>{item.description}</Text>
              <Text style={styles.transactionDate}>{formatDate(item.date)}</Text>
            </View>
            <Text
              style={[
                styles.transactionAmount,
                { color: item.type === 'income' ? '#22c55e' : '#ef4444' }
              ]}
            >
              {item.type === 'income' ? '+' : '-'}{formatCurrency(item.amount)}
            </Text>
          </TouchableOpacity>
        )}
        style={styles.list}
      />

      <TouchableOpacity style={styles.addButton}>
        <Ionicons name="add" size={24} color="white" />
      </TouchableOpacity>
    </>
  );

  const renderInvoicesTab = () => (
    <>
      <View style={styles.filterContainer}>
        <TouchableOpacity style={[styles.filterButton, styles.filterButtonActive]}>
          <Text style={styles.filterButtonTextActive}>All</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.filterButton}>
          <Text style={styles.filterButtonText}>Pending</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.filterButton}>
          <Text style={styles.filterButtonText}>Paid</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.filterButton}>
          <Text style={styles.filterButtonText}>Overdue</Text>
        </TouchableOpacity>
      </View>

      <FlatList
        data={financialData.invoices}
        keyExtractor={(item) => item.id}
        renderItem={({ item }) => (
          <TouchableOpacity 
            style={styles.invoiceItem}
            onPress={() => navigation.navigate('FinancialDetail', { financialId: item.id })}
          >
            <View style={styles.invoiceDetails}>
              <Text style={styles.invoiceCustomer}>{item.customer}</Text>
              <Text style={styles.invoiceDueDate}>Due: {formatDate(item.dueDate)}</Text>
            </View>
            <View style={styles.invoiceAmountContainer}>
              <Text style={styles.invoiceAmount}>{formatCurrency(item.amount)}</Text>
              <View style={[
                styles.invoiceStatusBadge,
                { 
                  backgroundColor: 
                    item.status === 'paid' ? '#dcfce7' : 
                    item.status === 'pending' ? '#f3f4f6' : '#fee2e2'
                }
              ]}>
                <Text style={[
                  styles.invoiceStatusText,
                  { 
                    color: 
                      item.status === 'paid' ? '#22c55e' : 
                      item.status === 'pending' ? '#6b7280' : '#ef4444'
                  }
                ]}>
                  {item.status.charAt(0).toUpperCase() + item.status.slice(1)}
                </Text>
              </View>
            </View>
          </TouchableOpacity>
        )}
        style={styles.list}
      />

      <TouchableOpacity style={styles.addButton}>
        <Ionicons name="add" size={24} color="white" />
      </TouchableOpacity>
    </>
  );

  const renderBudgetsTab = () => (
    <>
      <View style={styles.budgetSummary}>
        <Text style={styles.budgetSummaryTitle}>Total Budget</Text>
        <Text style={styles.budgetSummaryAmount}>
          {formatCurrency(
            financialData.budgets.reduce((total: number, budget: any) => total + budget.budgeted, 0)
          )}
        </Text>
        <Text style={styles.budgetSummarySpent}>
          Spent: {formatCurrency(
            financialData.budgets.reduce((total: number, budget: any) => total + budget.spent, 0)
          )}
        </Text>
      </View>

      <FlatList
        data={financialData.budgets}
        keyExtractor={(item) => item.id}
        renderItem={({ item }) => (
          <View style={styles.budgetItemFull}>
            <View style={styles.budgetHeader}>
              <Text style={styles.budgetCategory}>{item.category}</Text>
              <Text style={styles.budgetRemaining}>{formatCurrency(item.remaining)} remaining</Text>
            </View>
            <View style={styles.budgetProgressContainer}>
              <View
                style={[
                  styles.budgetProgress,
                  { 
                    width: `${(item.spent / item.budgeted) * 100}%`,
                    backgroundColor: item.spent > item.budgeted ? '#ef4444' : '#3b82f6'
                  }
                ]}
              />
            </View>
            <View style={styles.budgetDetails}>
              <Text style={styles.budgetSpent}>Spent: {formatCurrency(item.spent)}</Text>
              <Text style={styles.budgetTotal}>of {formatCurrency(item.budgeted)}</Text>
            </View>
          </View>
        )}
        style={styles.list}
      />

      <TouchableOpacity style={styles.addButton}>
        <Ionicons name="add" size={24} color="white" />
      </TouchableOpacity>
    </>
  );

  return (
    <View style={styles.container}>
      <View style={styles.tabContainer}>
        <TouchableOpacity
          style={[styles.tabButton, activeTab === 'overview' && styles.activeTabButton]}
          onPress={() => setActiveTab('overview')}
        >
          <Text style={[styles.tabButtonText, activeTab === 'overview' && styles.activeTabButtonText]}>Overview</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tabButton, activeTab === 'transactions' && styles.activeTabButton]}
          onPress={() => setActiveTab('transactions')}
        >
          <Text style={[styles.tabButtonText, activeTab === 'transactions' && styles.activeTabButtonText]}>Transactions</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tabButton, activeTab === 'invoices' && styles.activeTabButton]}
          onPress={() => setActiveTab('invoices')}
        >
          <Text style={[styles.tabButtonText, activeTab === 'invoices' && styles.activeTabButtonText]}>Invoices</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tabButton, activeTab === 'budgets' && styles.activeTabButton]}
          onPress={() => setActiveTab('budgets')}
        >
          <Text style={[styles.tabButtonText, activeTab === 'budgets' && styles.activeTabButtonText]}>Budgets</Text>
        </TouchableOpacity>
      </View>

      <ScrollView
        style={styles.content}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} colors={['#3b82f6']} />
        }
      >
        {activeTab === 'overview' && renderOverviewTab()}
        {activeTab === 'transactions' && renderTransactionsTab()}
        {activeTab === 'invoices' && renderInvoicesTab()}
        {activeTab === 'budgets' && renderBudgetsTab()}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9fafb',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  tabButton: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
  },
  activeTabButton: {
    borderBottomWidth: 2,
    borderBottomColor: '#3b82f6',
  },
  tabButtonText: {
    color: '#6b7280',
    fontSize: 14,
  },
  activeTabButtonText: {
    color: '#3b82f6',
    fontWeight: 'bold',
  },
  content: {
    flex: 1,
  },
  summaryContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 15,
    backgroundColor: 'white',
    margin: 15,
    borderRadius: 10,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
  },
  summaryCard: {
    flex: 1,
    alignItems: 'center',
  },
  summaryLabel: {
    fontSize: 14,
    color: '#6b7280',
    marginBottom: 5,
  },
  summaryValue: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  section: {
    backgroundColor: 'white',
    borderRadius: 10,
    margin: 15,
    marginTop: 5,
    padding: 15,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  seeAllText: {
    color: '#3b82f6',
    fontSize: 14,
  },
  invoiceStatusContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  invoiceStatusItem: {
    flex: 1,
    alignItems: 'center',
  },
  invoiceStatusNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#3b82f6',
  },
  invoiceStatusLabel: {
    fontSize: 14,
    color: '#6b7280',
    marginTop: 5,
  },
  transactionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  transactionIconContainer: {
    marginRight: 12,
  },
  transactionDetails: {
    flex: 1,
  },
  transactionDescription: {
    fontSize: 16,
    fontWeight: '500',
  },
  transactionDate: {
    fontSize: 14,
    color: '#6b7280',
    marginTop: 2,
  },
  transactionAmount: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  budgetItem: {
    marginBottom: 15,
  },
  budgetHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 5,
  },
  budgetCategory: {
    fontSize: 16,
    fontWeight: '500',
  },
  budgetRemaining: {
    fontSize: 14,
    color: '#3b82f6',
  },
  budgetProgressContainer: {
    height: 8,
    backgroundColor: '#f3f4f6',
    borderRadius: 4,
    marginBottom: 5,
  },
  budgetProgress: {
    height: 8,
    backgroundColor: '#3b82f6',
    borderRadius: 4,
  },
  budgetDetails: {
    flexDirection: 'row',
  },
  budgetSpent: {
    fontSize: 14,
    color: '#6b7280',
  },
  budgetTotal: {
    fontSize: 14,
    color: '#6b7280',
    marginLeft: 5,
  },
  filterContainer: {
    flexDirection: 'row',
    padding: 15,
    backgroundColor: 'white',
  },
  filterButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
    marginRight: 8,
  },
  filterButtonActive: {
    backgroundColor: '#3b82f6',
  },
  filterButtonText: {
    color: '#6b7280',
  },
  filterButtonTextActive: {
    color: 'white',
  },
  list: {
    backgroundColor: 'white',
    flex: 1,
  },
  invoiceItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  invoiceDetails: {
    flex: 1,
  },
  invoiceCustomer: {
    fontSize: 16,
    fontWeight: '500',
  },
  invoiceDueDate: {
    fontSize: 14,
    color: '#6b7280',
    marginTop: 2,
  },
  invoiceAmountContainer: {
    alignItems: 'flex-end',
  },
  invoiceAmount: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  invoiceStatusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  invoiceStatusText: {
    fontSize: 12,
    fontWeight: '500',
  },
  budgetSummary: {
    backgroundColor: 'white',
    padding: 20,
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  budgetSummaryTitle: {
    fontSize: 16,
    color: '#6b7280',
  },
  budgetSummaryAmount: {
    fontSize: 28,
    fontWeight: 'bold',
    marginVertical: 5,
  },
  budgetSummarySpent: {
    fontSize: 16,
    color: '#6b7280',
  },
  budgetItemFull: {
    padding: 15,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  addButton: {
    position: 'absolute',
    bottom: 20,
    right: 20,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: '#3b82f6',
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
  },
});

export default FinancesScreen;