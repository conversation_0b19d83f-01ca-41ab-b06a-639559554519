import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../../../../shared/store/AuthProvider';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { MainTabParamList } from '../navigation/MainNavigator';
import { CompositeScreenProps } from '@react-navigation/native';
import { BottomTabScreenProps } from '@react-navigation/bottom-tabs';
import { MainStackParamList } from '../navigation/MainNavigator';
import { fieldService } from '../../../../shared/services/fieldService';
import { taskService } from '../../../../shared/services/taskService';
import { weatherService } from '../../../../shared/services/weatherService';

// Mock services for employee, finance, and inventory data
// These would be replaced with actual services once implemented
const employeeService = {
  getEmployees: async (farmId: string) => {
    // Mock data
    return [
      { id: '1', name: 'John Doe', role: 'Field Worker' },
      { id: '2', name: 'Jane Smith', role: 'Equipment Operator' },
      { id: '3', name: 'Bob Johnson', role: 'Manager' },
    ];
  }
};

const financeService = {
  getFinancialSummary: async (farmId: string) => {
    // Mock data
    return {
      revenue: 125000,
      expenses: 75000,
      profit: 50000,
      pendingInvoices: 3
    };
  }
};

const inventoryService = {
  getInventorySummary: async (farmId: string) => {
    // Mock data
    return {
      lowStock: 5,
      totalItems: 120,
      recentDeliveries: 2
    };
  }
};

const equipmentService = {
  getEquipmentSummary: async (farmId: string) => {
    // Mock data
    return {
      totalEquipment: 15,
      needsMaintenance: 3,
      inUse: 8
    };
  }
};

type DashboardScreenProps = CompositeScreenProps<
  BottomTabScreenProps<MainTabParamList, 'Dashboard'>,
  NativeStackScreenProps<MainStackParamList>
>;

const DashboardScreen: React.FC<DashboardScreenProps> = ({ navigation }) => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [fieldCount, setFieldCount] = useState(0);
  const [activeTaskCount, setActiveTaskCount] = useState(0);
  const [employeeCount, setEmployeeCount] = useState(0);
  const [financialSummary, setFinancialSummary] = useState<any>(null);
  const [inventorySummary, setInventorySummary] = useState<any>(null);
  const [equipmentSummary, setEquipmentSummary] = useState<any>(null);
  const [recentActivity, setRecentActivity] = useState<any[]>([]);

  const loadData = async () => {
    try {
      setLoading(true);

      if (!user?.farmId) {
        console.error('No farm ID available');
        setLoading(false);
        return;
      }

      // Load field count
      const fields = await fieldService.getFields(user.farmId);
      setFieldCount(fields.length);

      // Load active tasks
      const tasks = await taskService.getTasks(user.farmId, { status: 'active' });
      setActiveTaskCount(tasks.length);

      // Load employee count
      const employees = await employeeService.getEmployees(user.farmId);
      setEmployeeCount(employees.length);

      // Load financial summary
      const finances = await financeService.getFinancialSummary(user.farmId);
      setFinancialSummary(finances);

      // Load inventory summary
      const inventory = await inventoryService.getInventorySummary(user.farmId);
      setInventorySummary(inventory);

      // Load equipment summary
      const equipment = await equipmentService.getEquipmentSummary(user.farmId);
      setEquipmentSummary(equipment);

      // Load recent activity (mock data for now)
      setRecentActivity([
        { 
          type: 'task_assigned', 
          description: 'Task "Harvest Field 3" assigned to John Doe', 
          time: '2 hours ago' 
        },
        { 
          type: 'invoice_paid', 
          description: 'Invoice #1234 paid by Customer XYZ', 
          time: '5 hours ago' 
        },
        { 
          type: 'inventory_low', 
          description: 'Fertilizer XYZ is running low (5 units remaining)', 
          time: '1 day ago' 
        },
        { 
          type: 'equipment_maintenance', 
          description: 'Tractor #2 is due for maintenance', 
          time: '2 days ago' 
        }
      ]);

      setLoading(false);
    } catch (error) {
      console.error('Error loading dashboard data:', error);
      setLoading(false);
    }
  };

  useEffect(() => {
    loadData();
  }, [user?.farmId]);

  const onRefresh = async () => {
    setRefreshing(true);
    await loadData();
    setRefreshing(false);
  };

  const formatCurrency = (amount: number) => {
    return `$${amount.toLocaleString()}`;
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#3b82f6" />
      </View>
    );
  }

  return (
    <ScrollView
      style={styles.container}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={onRefresh} colors={['#3b82f6']} />
      }
    >
      <View style={styles.header}>
        <Text style={styles.greeting}>Hello, {user?.name || 'Manager'}</Text>
        <Text style={styles.subGreeting}>Here's your farm management overview</Text>
      </View>

      <View style={styles.quickStats}>
        <TouchableOpacity
          style={styles.statCard}
          onPress={() => navigation.navigate('Tasks')}
        >
          <Ionicons name="list" size={24} color="#3b82f6" />
          <Text style={styles.statNumber}>{activeTaskCount}</Text>
          <Text style={styles.statLabel}>Active Tasks</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.statCard}
          onPress={() => navigation.navigate('Employees')}
        >
          <Ionicons name="people" size={24} color="#3b82f6" />
          <Text style={styles.statNumber}>{employeeCount}</Text>
          <Text style={styles.statLabel}>Employees</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.statCard}
          onPress={() => navigation.navigate('Finances')}
        >
          <Ionicons name="cash" size={24} color="#3b82f6" />
          <Text style={styles.statNumber}>
            {financialSummary ? financialSummary.pendingInvoices : '--'}
          </Text>
          <Text style={styles.statLabel}>Pending Invoices</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Financial Overview</Text>
          <TouchableOpacity onPress={() => navigation.navigate('Finances')}>
            <Text style={styles.seeAllText}>See All</Text>
          </TouchableOpacity>
        </View>
        {financialSummary ? (
          <View style={styles.financialOverview}>
            <View style={styles.financialItem}>
              <Text style={styles.financialLabel}>Revenue</Text>
              <Text style={[styles.financialValue, { color: '#22c55e' }]}>
                {formatCurrency(financialSummary.revenue)}
              </Text>
            </View>
            <View style={styles.financialItem}>
              <Text style={styles.financialLabel}>Expenses</Text>
              <Text style={[styles.financialValue, { color: '#ef4444' }]}>
                {formatCurrency(financialSummary.expenses)}
              </Text>
            </View>
            <View style={styles.financialItem}>
              <Text style={styles.financialLabel}>Profit</Text>
              <Text style={[styles.financialValue, { color: '#3b82f6' }]}>
                {formatCurrency(financialSummary.profit)}
              </Text>
            </View>
          </View>
        ) : (
          <Text style={styles.emptyText}>No financial data available</Text>
        )}
      </View>

      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Quick Actions</Text>
        </View>
        <View style={styles.actionButtons}>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => navigation.navigate('Tasks')}
          >
            <Ionicons name="add-circle-outline" size={24} color="#3b82f6" />
            <Text style={styles.actionButtonText}>Create Task</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => navigation.navigate('Employees')}
          >
            <Ionicons name="person-add-outline" size={24} color="#3b82f6" />
            <Text style={styles.actionButtonText}>Add Employee</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => navigation.navigate('FieldHealth')}
          >
            <Ionicons name="leaf-outline" size={24} color="#3b82f6" />
            <Text style={styles.actionButtonText}>Field Health</Text>
          </TouchableOpacity>
        </View>
      </View>

      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Inventory & Equipment</Text>
        </View>
        <View style={styles.inventoryEquipmentContainer}>
          <TouchableOpacity 
            style={styles.inventoryEquipmentCard}
            onPress={() => navigation.navigate('Inventory')}
          >
            <Ionicons name="cube" size={24} color="#3b82f6" />
            <Text style={styles.inventoryEquipmentTitle}>Inventory</Text>
            {inventorySummary && (
              <>
                <Text style={styles.inventoryEquipmentStat}>
                  <Ionicons name="alert-circle" size={16} color="#f59e0b" /> {inventorySummary.lowStock} items low
                </Text>
                <Text style={styles.inventoryEquipmentStat}>
                  <Ionicons name="cube" size={16} color="#3b82f6" /> {inventorySummary.totalItems} total items
                </Text>
              </>
            )}
          </TouchableOpacity>

          <TouchableOpacity 
            style={styles.inventoryEquipmentCard}
            onPress={() => navigation.navigate('Equipment')}
          >
            <Ionicons name="construct" size={24} color="#3b82f6" />
            <Text style={styles.inventoryEquipmentTitle}>Equipment</Text>
            {equipmentSummary && (
              <>
                <Text style={styles.inventoryEquipmentStat}>
                  <Ionicons name="warning" size={16} color="#f59e0b" /> {equipmentSummary.needsMaintenance} need maintenance
                </Text>
                <Text style={styles.inventoryEquipmentStat}>
                  <Ionicons name="checkmark-circle" size={16} color="#22c55e" /> {equipmentSummary.inUse} in use
                </Text>
              </>
            )}
          </TouchableOpacity>
        </View>
      </View>

      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Recent Activity</Text>
        </View>
        {recentActivity.length > 0 ? (
          recentActivity.map((activity, index) => (
            <View key={index} style={styles.activityItem}>
              <Ionicons
                name={
                  activity.type === 'task_assigned'
                    ? 'list'
                    : activity.type === 'invoice_paid'
                    ? 'cash'
                    : activity.type === 'inventory_low'
                    ? 'cube'
                    : 'construct'
                }
                size={20}
                color="#3b82f6"
              />
              <View style={styles.activityContent}>
                <Text style={styles.activityText}>{activity.description}</Text>
                <Text style={styles.activityTime}>{activity.time}</Text>
              </View>
            </View>
          ))
        ) : (
          <Text style={styles.emptyText}>No recent activity</Text>
        )}
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9fafb',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    padding: 20,
    backgroundColor: '#3b82f6',
  },
  greeting: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
  },
  subGreeting: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.8)',
    marginTop: 5,
  },
  quickStats: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 15,
    backgroundColor: 'white',
    borderRadius: 10,
    margin: 15,
    marginTop: -20,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  statCard: {
    alignItems: 'center',
    flex: 1,
  },
  statNumber: {
    fontSize: 20,
    fontWeight: 'bold',
    marginTop: 5,
  },
  statLabel: {
    fontSize: 14,
    color: '#6b7280',
  },
  section: {
    backgroundColor: 'white',
    borderRadius: 10,
    margin: 15,
    marginTop: 5,
    padding: 15,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  seeAllText: {
    color: '#3b82f6',
    fontSize: 14,
  },
  financialOverview: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  financialItem: {
    flex: 1,
    alignItems: 'center',
  },
  financialLabel: {
    fontSize: 14,
    color: '#6b7280',
    marginBottom: 5,
  },
  financialValue: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  actionButton: {
    alignItems: 'center',
    padding: 10,
    flex: 1,
  },
  actionButtonText: {
    marginTop: 5,
    fontSize: 14,
    textAlign: 'center',
  },
  inventoryEquipmentContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  inventoryEquipmentCard: {
    flex: 1,
    padding: 15,
    backgroundColor: '#f3f4f6',
    borderRadius: 8,
    marginHorizontal: 5,
  },
  inventoryEquipmentTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginTop: 5,
    marginBottom: 10,
  },
  inventoryEquipmentStat: {
    fontSize: 14,
    marginTop: 5,
    flexDirection: 'row',
    alignItems: 'center',
  },
  activityItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    paddingVertical: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  activityContent: {
    marginLeft: 10,
    flex: 1,
  },
  activityText: {
    fontSize: 14,
  },
  activityTime: {
    fontSize: 12,
    color: '#6b7280',
    marginTop: 2,
  },
  emptyText: {
    textAlign: 'center',
    color: '#6b7280',
    padding: 10,
  },
});

export default DashboardScreen;