import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  Image,
  Alert
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { RouteProp, useNavigation, useRoute } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { MainStackParamList } from '../navigation/MainNavigator';

// Mock data for financial transactions
const mockFinancialData = [
  {
    id: '1',
    type: 'invoice',
    number: 'INV-2023-0125',
    date: '2023-06-15',
    dueDate: '2023-07-15',
    status: 'paid',
    amount: 12500.00,
    description: 'Grain delivery - Corn (250 tons)',
    category: 'Sales',
    paymentMethod: 'Bank Transfer',
    paymentDate: '2023-06-20',
    customer: {
      name: 'Midwest Grain Co.',
      contact: '<PERSON>',
      email: 's<PERSON><PERSON><PERSON>@midwestgrain.com',
      phone: '(*************'
    },
    items: [
      {
        id: 'item1',
        description: 'Corn - Grade A',
        quantity: 250,
        unit: 'tons',
        unitPrice: 50.00,
        total: 12500.00
      }
    ],
    notes: 'Delivery completed on time. Customer very satisfied with quality.',
    attachments: [
      {
        id: 'att1',
        name: 'Delivery_Receipt.pdf',
        type: 'pdf',
        url: 'https://example.com/delivery_receipt.pdf',
        size: '156 KB',
        uploadDate: '2023-06-15'
      },
      {
        id: 'att2',
        name: 'Quality_Certificate.pdf',
        type: 'pdf',
        url: 'https://example.com/quality_certificate.pdf',
        size: '245 KB',
        uploadDate: '2023-06-15'
      }
    ],
    relatedTransactions: [
      {
        id: 'rel1',
        type: 'payment',
        number: 'PMT-2023-0098',
        date: '2023-06-20',
        amount: 12500.00,
        status: 'completed'
      }
    ]
  },
  {
    id: '2',
    type: 'expense',
    number: 'EXP-2023-0087',
    date: '2023-06-10',
    status: 'approved',
    amount: 4750.00,
    description: 'Fertilizer purchase - Spring application',
    category: 'Supplies',
    paymentMethod: 'Credit Card',
    paymentDate: '2023-06-10',
    vendor: {
      name: 'AgriSupplies Inc.',
      contact: 'Mike Roberts',
      email: '<EMAIL>',
      phone: '(*************'
    },
    items: [
      {
        id: 'item1',
        description: 'Nitrogen Fertilizer',
        quantity: 50,
        unit: 'bags',
        unitPrice: 65.00,
        total: 3250.00
      },
      {
        id: 'item2',
        description: 'Phosphate Fertilizer',
        quantity: 25,
        unit: 'bags',
        unitPrice: 60.00,
        total: 1500.00
      }
    ],
    notes: 'Purchased for spring application on corn fields.',
    attachments: [
      {
        id: 'att1',
        name: 'Invoice_AS_4567.pdf',
        type: 'pdf',
        url: 'https://example.com/invoice_as_4567.pdf',
        size: '128 KB',
        uploadDate: '2023-06-10'
      },
      {
        id: 'att2',
        name: 'Receipt_Photo.jpg',
        type: 'image',
        url: 'https://example.com/receipt_photo.jpg',
        size: '1.2 MB',
        uploadDate: '2023-06-10'
      }
    ],
    relatedTransactions: []
  }
];

type FinancialDetailScreenRouteProp = RouteProp<MainStackParamList, 'FinancialDetail'>;
type FinancialDetailScreenNavigationProp = NativeStackNavigationProp<MainStackParamList>;

const FinancialDetailScreen: React.FC = () => {
  const route = useRoute<FinancialDetailScreenRouteProp>();
  const navigation = useNavigation<FinancialDetailScreenNavigationProp>();
  const { financialId } = route.params;
  
  const [transaction, setTransaction] = useState<typeof mockFinancialData[0] | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'details' | 'attachments' | 'related'>('details');
  
  // Fetch transaction data
  useEffect(() => {
    const fetchTransaction = async () => {
      setIsLoading(true);
      // Simulate API call
      setTimeout(() => {
        const foundTransaction = mockFinancialData.find(item => item.id === financialId);
        if (foundTransaction) {
          setTransaction(foundTransaction);
        }
        setIsLoading(false);
      }, 1000);
    };
    
    fetchTransaction();
  }, [financialId]);
  
  // Handle edit transaction
  const handleEditTransaction = () => {
    Alert.alert('Edit Transaction', 'This feature will be implemented soon.');
    // In a real app, navigate to an edit transaction form
    // navigation.navigate('EditTransaction', { financialId });
  };
  
  // Handle download attachment
  const handleDownloadAttachment = (attachment: typeof mockFinancialData[0]['attachments'][0]) => {
    Alert.alert('Download Attachment', `Downloading ${attachment.name}...`);
    // In a real app, implement file download functionality
  };
  
  // Handle view related transaction
  const handleViewRelatedTransaction = (transactionId: string) => {
    navigation.navigate('FinancialDetail', { financialId: transactionId });
  };
  
  // Get status color
  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'paid':
      case 'completed':
      case 'approved':
        return '#4CAF50'; // Green
      case 'pending':
        return '#FF9800'; // Orange
      case 'overdue':
        return '#F44336'; // Red
      case 'draft':
        return '#9E9E9E'; // Grey
      default:
        return '#3b82f6'; // Blue
    }
  };
  
  // Render status badge
  const renderStatusBadge = (status: string) => {
    const color = getStatusColor(status);
    
    return (
      <View style={[styles.statusBadge, { backgroundColor: color }]}>
        <Text style={styles.statusText}>{status.charAt(0).toUpperCase() + status.slice(1)}</Text>
      </View>
    );
  };
  
  // Render details tab
  const renderDetailsTab = () => {
    if (!transaction) return null;
    
    return (
      <View style={styles.tabContent}>
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Transaction Information</Text>
          
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Type:</Text>
            <Text style={styles.detailValue}>
              {transaction.type.charAt(0).toUpperCase() + transaction.type.slice(1)}
            </Text>
          </View>
          
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Number:</Text>
            <Text style={styles.detailValue}>{transaction.number}</Text>
          </View>
          
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Date:</Text>
            <Text style={styles.detailValue}>{transaction.date}</Text>
          </View>
          
          {transaction.dueDate && (
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Due Date:</Text>
              <Text style={styles.detailValue}>{transaction.dueDate}</Text>
            </View>
          )}
          
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Amount:</Text>
            <Text style={styles.detailValue}>${transaction.amount.toLocaleString()}</Text>
          </View>
          
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Category:</Text>
            <Text style={styles.detailValue}>{transaction.category}</Text>
          </View>
          
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Payment Method:</Text>
            <Text style={styles.detailValue}>{transaction.paymentMethod}</Text>
          </View>
          
          {transaction.paymentDate && (
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Payment Date:</Text>
              <Text style={styles.detailValue}>{transaction.paymentDate}</Text>
            </View>
          )}
        </View>
        
        {transaction.customer && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Customer Information</Text>
            
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Name:</Text>
              <Text style={styles.detailValue}>{transaction.customer.name}</Text>
            </View>
            
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Contact:</Text>
              <Text style={styles.detailValue}>{transaction.customer.contact}</Text>
            </View>
            
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Email:</Text>
              <Text style={styles.detailValue}>{transaction.customer.email}</Text>
            </View>
            
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Phone:</Text>
              <Text style={styles.detailValue}>{transaction.customer.phone}</Text>
            </View>
          </View>
        )}
        
        {transaction.vendor && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Vendor Information</Text>
            
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Name:</Text>
              <Text style={styles.detailValue}>{transaction.vendor.name}</Text>
            </View>
            
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Contact:</Text>
              <Text style={styles.detailValue}>{transaction.vendor.contact}</Text>
            </View>
            
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Email:</Text>
              <Text style={styles.detailValue}>{transaction.vendor.email}</Text>
            </View>
            
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Phone:</Text>
              <Text style={styles.detailValue}>{transaction.vendor.phone}</Text>
            </View>
          </View>
        )}
        
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Items</Text>
          
          {transaction.items.map((item, index) => (
            <View key={item.id} style={styles.itemContainer}>
              <View style={styles.itemHeader}>
                <Text style={styles.itemDescription}>{item.description}</Text>
                <Text style={styles.itemTotal}>${item.total.toLocaleString()}</Text>
              </View>
              
              <View style={styles.itemDetails}>
                <Text style={styles.itemQuantity}>
                  {item.quantity} {item.unit} × ${item.unitPrice.toFixed(2)}
                </Text>
              </View>
              
              {index < transaction.items.length - 1 && <View style={styles.itemDivider} />}
            </View>
          ))}
          
          <View style={styles.totalContainer}>
            <Text style={styles.totalLabel}>Total</Text>
            <Text style={styles.totalAmount}>
              ${transaction.amount.toLocaleString()}
            </Text>
          </View>
        </View>
        
        {transaction.notes && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Notes</Text>
            <Text style={styles.notesText}>{transaction.notes}</Text>
          </View>
        )}
      </View>
    );
  };
  
  // Render attachments tab
  const renderAttachmentsTab = () => {
    if (!transaction || !transaction.attachments.length) {
      return (
        <View style={styles.emptyContainer}>
          <Ionicons name="document-outline" size={48} color="#ccc" />
          <Text style={styles.emptyText}>No attachments found</Text>
        </View>
      );
    }
    
    return (
      <View style={styles.tabContent}>
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Attachments</Text>
          
          {transaction.attachments.map(attachment => (
            <TouchableOpacity
              key={attachment.id}
              style={styles.attachmentItem}
              onPress={() => handleDownloadAttachment(attachment)}
            >
              <View style={styles.attachmentIconContainer}>
                <Ionicons
                  name={attachment.type === 'pdf' ? 'document-text' : 'image'}
                  size={24}
                  color="#3b82f6"
                />
              </View>
              
              <View style={styles.attachmentDetails}>
                <Text style={styles.attachmentName}>{attachment.name}</Text>
                <Text style={styles.attachmentMeta}>
                  {attachment.size} • {attachment.uploadDate}
                </Text>
              </View>
              
              <Ionicons name="download-outline" size={24} color="#666" />
            </TouchableOpacity>
          ))}
        </View>
      </View>
    );
  };
  
  // Render related transactions tab
  const renderRelatedTab = () => {
    if (!transaction || !transaction.relatedTransactions.length) {
      return (
        <View style={styles.emptyContainer}>
          <Ionicons name="git-network-outline" size={48} color="#ccc" />
          <Text style={styles.emptyText}>No related transactions found</Text>
        </View>
      );
    }
    
    return (
      <View style={styles.tabContent}>
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Related Transactions</Text>
          
          {transaction.relatedTransactions.map(relatedTransaction => (
            <TouchableOpacity
              key={relatedTransaction.id}
              style={styles.relatedItem}
              onPress={() => handleViewRelatedTransaction(relatedTransaction.id)}
            >
              <View style={styles.relatedHeader}>
                <Text style={styles.relatedType}>
                  {relatedTransaction.type.charAt(0).toUpperCase() + relatedTransaction.type.slice(1)}
                </Text>
                <View style={[
                  styles.relatedStatusBadge,
                  { backgroundColor: getStatusColor(relatedTransaction.status) }
                ]}>
                  <Text style={styles.relatedStatusText}>
                    {relatedTransaction.status.charAt(0).toUpperCase() + relatedTransaction.status.slice(1)}
                  </Text>
                </View>
              </View>
              
              <View style={styles.relatedDetails}>
                <Text style={styles.relatedNumber}>{relatedTransaction.number}</Text>
                <Text style={styles.relatedAmount}>
                  ${relatedTransaction.amount.toLocaleString()}
                </Text>
              </View>
              
              <View style={styles.relatedFooter}>
                <Text style={styles.relatedDate}>{relatedTransaction.date}</Text>
                <Ionicons name="chevron-forward" size={16} color="#ccc" />
              </View>
            </TouchableOpacity>
          ))}
        </View>
      </View>
    );
  };
  
  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#3b82f6" />
      </View>
    );
  }
  
  if (!transaction) {
    return (
      <View style={styles.errorContainer}>
        <Ionicons name="alert-circle-outline" size={48} color="#666" />
        <Text style={styles.errorText}>Transaction not found</Text>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Text style={styles.backButtonText}>Go Back</Text>
        </TouchableOpacity>
      </View>
    );
  }
  
  return (
    <View style={styles.container}>
      <ScrollView>
        <View style={styles.header}>
          <View style={styles.headerTop}>
            <Text style={styles.transactionType}>
              {transaction.type === 'invoice' ? 'Invoice' : 'Expense'}
            </Text>
            {renderStatusBadge(transaction.status)}
          </View>
          
          <Text style={styles.transactionNumber}>{transaction.number}</Text>
          <Text style={styles.transactionDescription}>{transaction.description}</Text>
          <Text style={styles.transactionAmount}>${transaction.amount.toLocaleString()}</Text>
        </View>
        
        <View style={styles.tabsContainer}>
          <TouchableOpacity
            style={[styles.tabButton, activeTab === 'details' && styles.activeTabButton]}
            onPress={() => setActiveTab('details')}
          >
            <Text style={[styles.tabButtonText, activeTab === 'details' && styles.activeTabButtonText]}>
              Details
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.tabButton, activeTab === 'attachments' && styles.activeTabButton]}
            onPress={() => setActiveTab('attachments')}
          >
            <Text style={[styles.tabButtonText, activeTab === 'attachments' && styles.activeTabButtonText]}>
              Attachments ({transaction.attachments.length})
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.tabButton, activeTab === 'related' && styles.activeTabButton]}
            onPress={() => setActiveTab('related')}
          >
            <Text style={[styles.tabButtonText, activeTab === 'related' && styles.activeTabButtonText]}>
              Related ({transaction.relatedTransactions.length})
            </Text>
          </TouchableOpacity>
        </View>
        
        {activeTab === 'details' && renderDetailsTab()}
        {activeTab === 'attachments' && renderAttachmentsTab()}
        {activeTab === 'related' && renderRelatedTab()}
      </ScrollView>
      
      <TouchableOpacity style={styles.editButton} onPress={handleEditTransaction}>
        <Ionicons name="create" size={24} color="#fff" />
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  errorText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
  backButton: {
    marginTop: 16,
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: '#3b82f6',
    borderRadius: 4,
  },
  backButtonText: {
    color: '#fff',
    fontWeight: '500',
  },
  header: {
    backgroundColor: '#fff',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  headerTop: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  transactionType: {
    fontSize: 14,
    color: '#666',
    fontWeight: '500',
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  statusText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  transactionNumber: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  transactionDescription: {
    fontSize: 16,
    color: '#333',
    marginBottom: 8,
  },
  transactionAmount: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#3b82f6',
  },
  tabsContainer: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  tabButton: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
  },
  activeTabButton: {
    borderBottomWidth: 2,
    borderBottomColor: '#3b82f6',
  },
  tabButtonText: {
    color: '#666',
    fontWeight: '500',
  },
  activeTabButtonText: {
    color: '#3b82f6',
    fontWeight: 'bold',
  },
  tabContent: {
    padding: 16,
  },
  section: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 1,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 12,
    color: '#333',
  },
  detailRow: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  detailLabel: {
    width: 120,
    fontSize: 14,
    color: '#666',
  },
  detailValue: {
    flex: 1,
    fontSize: 14,
    color: '#333',
  },
  itemContainer: {
    marginBottom: 8,
  },
  itemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  itemDescription: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
    flex: 1,
  },
  itemTotal: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
  },
  itemDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  itemQuantity: {
    fontSize: 12,
    color: '#666',
  },
  itemDivider: {
    height: 1,
    backgroundColor: '#f0f0f0',
    marginVertical: 8,
  },
  totalContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 16,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  totalLabel: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  totalAmount: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#3b82f6',
  },
  notesText: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
  attachmentItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  attachmentIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 4,
    backgroundColor: '#f0f7ff',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  attachmentDetails: {
    flex: 1,
  },
  attachmentName: {
    fontSize: 14,
    color: '#333',
    marginBottom: 4,
  },
  attachmentMeta: {
    fontSize: 12,
    color: '#999',
  },
  relatedItem: {
    backgroundColor: '#f9f9f9',
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
  },
  relatedHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  relatedType: {
    fontSize: 14,
    fontWeight: '500',
    color: '#666',
  },
  relatedStatusBadge: {
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
  },
  relatedStatusText: {
    color: '#fff',
    fontSize: 10,
    fontWeight: 'bold',
  },
  relatedDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  relatedNumber: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
  },
  relatedAmount: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#3b82f6',
  },
  relatedFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  relatedDate: {
    fontSize: 12,
    color: '#999',
  },
  emptyContainer: {
    padding: 32,
    alignItems: 'center',
    justifyContent: 'center',
  },
  emptyText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
  editButton: {
    position: 'absolute',
    bottom: 24,
    right: 24,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: '#3b82f6',
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
});

export default FinancialDetailScreen;