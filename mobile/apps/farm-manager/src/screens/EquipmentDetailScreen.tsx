import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  ActivityIndicator,
  Alert,
  FlatList
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { RouteProp, useNavigation, useRoute } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { MainStackParamList } from '../navigation/MainNavigator';

// Mock data for equipment
const mockEquipment = [
  {
    id: '1',
    name: 'Tractor - <PERSON> 8R',
    type: 'Tractor',
    status: 'Operational',
    lastMaintenance: '2023-05-15',
    nextMaintenance: '2023-08-15',
    hoursUsed: 1250,
    location: 'Main Barn',
    assignedTo: '<PERSON>',
    purchaseDate: '2020-03-10',
    purchasePrice: 250000,
    currentValue: 210000,
    image: 'https://example.com/tractor.jpg',
    description: 'High-performance row crop tractor with 310 HP engine and premium cab features.',
    serialNumber: 'JD8R310-2020-12345',
    manufacturer: '<PERSON>',
    model: '8R 310',
    year: 2020,
    fuelType: 'Diesel',
    maintenanceHistory: [
      {
        id: 'm1',
        date: '2023-05-15',
        type: 'Regular Service',
        description: 'Oil change, filter replacement, and general inspection',
        cost: 850,
        technician: 'Mike Johnson',
        hoursAtService: 1200
      },
      {
        id: 'm2',
        date: '2023-01-20',
        type: 'Regular Service',
        description: 'Oil change, filter replacement, and general inspection',
        cost: 850,
        technician: 'Mike Johnson',
        hoursAtService: 1050
      },
      {
        id: 'm3',
        date: '2022-09-10',
        type: 'Repair',
        description: 'Hydraulic system repair and hose replacement',
        cost: 1650,
        technician: 'Robert Smith',
        hoursAtService: 900
      }
    ],
    specifications: {
      engine: 'John Deere PowerTech 9.0L',
      horsepower: 310,
      transmission: 'e23 PowerShift',
      hydraulicCapacity: '85 gallons',
      fuelCapacity: '140 gallons',
      weight: '31,000 lbs',
      dimensions: '21.5ft L x 12.5ft W x 11ft H'
    }
  },
  {
    id: '2',
    name: 'Combine - Case IH 8250',
    type: 'Harvester',
    status: 'Maintenance Required',
    lastMaintenance: '2023-02-20',
    nextMaintenance: '2023-06-20',
    hoursUsed: 890,
    location: 'Equipment Shed',
    assignedTo: 'Unassigned',
    purchaseDate: '2021-01-15',
    purchasePrice: 375000,
    currentValue: 350000,
    image: 'https://example.com/combine.jpg',
    description: 'Advanced harvesting combine with high-capacity grain tank and precision cutting system.',
    serialNumber: 'CASE8250-2021-54321',
    manufacturer: 'Case IH',
    model: '8250 Axial-Flow',
    year: 2021,
    fuelType: 'Diesel',
    maintenanceHistory: [
      {
        id: 'm1',
        date: '2023-02-20',
        type: 'Regular Service',
        description: 'Oil change, filter replacement, and general inspection',
        cost: 1200,
        technician: 'Robert Smith',
        hoursAtService: 850
      },
      {
        id: 'm2',
        date: '2022-11-05',
        type: 'Repair',
        description: 'Threshing system adjustment and belt replacement',
        cost: 2200,
        technician: 'Mike Johnson',
        hoursAtService: 780
      }
    ],
    specifications: {
      engine: 'Case IH FPT 12.9L',
      horsepower: 480,
      grainTankCapacity: '410 bushels',
      fuelCapacity: '210 gallons',
      weight: '42,000 lbs',
      dimensions: '36ft L x 12ft W x 13ft H'
    }
  }
];

type EquipmentDetailScreenRouteProp = RouteProp<MainStackParamList, 'EquipmentDetail'>;
type EquipmentDetailScreenNavigationProp = NativeStackNavigationProp<MainStackParamList>;

const EquipmentDetailScreen: React.FC = () => {
  const route = useRoute<EquipmentDetailScreenRouteProp>();
  const navigation = useNavigation<EquipmentDetailScreenNavigationProp>();
  const { equipmentId } = route.params;
  
  const [equipment, setEquipment] = useState<typeof mockEquipment[0] | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'details' | 'maintenance' | 'specifications'>('details');

  // Fetch equipment data
  useEffect(() => {
    const fetchEquipment = async () => {
      setIsLoading(true);
      // Simulate API call
      setTimeout(() => {
        const foundEquipment = mockEquipment.find(item => item.id === equipmentId);
        if (foundEquipment) {
          setEquipment(foundEquipment);
        }
        setIsLoading(false);
      }, 1000);
    };

    fetchEquipment();
  }, [equipmentId]);

  // Handle edit equipment
  const handleEditEquipment = () => {
    Alert.alert('Edit Equipment', 'This feature will be implemented soon.');
    // In a real app, navigate to an edit equipment form
    // navigation.navigate('EditEquipment', { equipmentId });
  };

  // Handle schedule maintenance
  const handleScheduleMaintenance = () => {
    Alert.alert('Schedule Maintenance', 'This feature will be implemented soon.');
    // In a real app, navigate to a maintenance scheduling form
    // navigation.navigate('ScheduleMaintenance', { equipmentId });
  };

  // Render status badge with appropriate color
  const renderStatusBadge = (status: string) => {
    let color = '#4CAF50'; // Default green for operational
    
    if (status === 'Maintenance Required') {
      color = '#FF9800'; // Orange for maintenance
    } else if (status === 'Under Repair') {
      color = '#F44336'; // Red for repair
    } else if (status === 'Out of Service') {
      color = '#9E9E9E'; // Grey for out of service
    }
    
    return (
      <View style={[styles.statusBadge, { backgroundColor: color }]}>
        <Text style={styles.statusText}>{status}</Text>
      </View>
    );
  };

  // Render maintenance history item
  const renderMaintenanceItem = ({ item }: { item: typeof mockEquipment[0]['maintenanceHistory'][0] }) => (
    <View style={styles.maintenanceItem}>
      <View style={styles.maintenanceHeader}>
        <Text style={styles.maintenanceDate}>{item.date}</Text>
        <Text style={styles.maintenanceType}>{item.type}</Text>
      </View>
      
      <Text style={styles.maintenanceDescription}>{item.description}</Text>
      
      <View style={styles.maintenanceDetails}>
        <View style={styles.detailRow}>
          <Ionicons name="cash-outline" size={16} color="#666" />
          <Text style={styles.detailText}>Cost: ${item.cost.toLocaleString()}</Text>
        </View>
        
        <View style={styles.detailRow}>
          <Ionicons name="person-outline" size={16} color="#666" />
          <Text style={styles.detailText}>Technician: {item.technician}</Text>
        </View>
        
        <View style={styles.detailRow}>
          <Ionicons name="time-outline" size={16} color="#666" />
          <Text style={styles.detailText}>Hours at service: {item.hoursAtService}</Text>
        </View>
      </View>
    </View>
  );

  // Render details tab
  const renderDetailsTab = () => {
    if (!equipment) return null;
    
    return (
      <View style={styles.tabContent}>
        <View style={styles.sectionContainer}>
          <Text style={styles.sectionTitle}>General Information</Text>
          
          <View style={styles.detailRow}>
            <Ionicons name="information-circle-outline" size={16} color="#666" />
            <Text style={styles.detailText}>Type: {equipment.type}</Text>
          </View>
          
          <View style={styles.detailRow}>
            <Ionicons name="location-outline" size={16} color="#666" />
            <Text style={styles.detailText}>Location: {equipment.location}</Text>
          </View>
          
          <View style={styles.detailRow}>
            <Ionicons name="person-outline" size={16} color="#666" />
            <Text style={styles.detailText}>Assigned to: {equipment.assignedTo}</Text>
          </View>
          
          <View style={styles.detailRow}>
            <Ionicons name="time-outline" size={16} color="#666" />
            <Text style={styles.detailText}>Hours used: {equipment.hoursUsed}</Text>
          </View>
          
          <View style={styles.detailRow}>
            <Ionicons name="calendar-outline" size={16} color="#666" />
            <Text style={styles.detailText}>Last maintenance: {equipment.lastMaintenance}</Text>
          </View>
          
          <View style={styles.detailRow}>
            <Ionicons name="calendar-outline" size={16} color="#666" />
            <Text style={styles.detailText}>Next maintenance: {equipment.nextMaintenance}</Text>
          </View>
        </View>
        
        <View style={styles.sectionContainer}>
          <Text style={styles.sectionTitle}>Description</Text>
          <Text style={styles.descriptionText}>{equipment.description}</Text>
        </View>
        
        <View style={styles.sectionContainer}>
          <Text style={styles.sectionTitle}>Purchase Information</Text>
          
          <View style={styles.detailRow}>
            <Ionicons name="calendar-outline" size={16} color="#666" />
            <Text style={styles.detailText}>Purchase date: {equipment.purchaseDate}</Text>
          </View>
          
          <View style={styles.detailRow}>
            <Ionicons name="cash-outline" size={16} color="#666" />
            <Text style={styles.detailText}>Purchase price: ${equipment.purchasePrice.toLocaleString()}</Text>
          </View>
          
          <View style={styles.detailRow}>
            <Ionicons name="trending-down-outline" size={16} color="#666" />
            <Text style={styles.detailText}>Current value: ${equipment.currentValue.toLocaleString()}</Text>
          </View>
        </View>
        
        <View style={styles.sectionContainer}>
          <Text style={styles.sectionTitle}>Identification</Text>
          
          <View style={styles.detailRow}>
            <Ionicons name="barcode-outline" size={16} color="#666" />
            <Text style={styles.detailText}>Serial number: {equipment.serialNumber}</Text>
          </View>
          
          <View style={styles.detailRow}>
            <Ionicons name="business-outline" size={16} color="#666" />
            <Text style={styles.detailText}>Manufacturer: {equipment.manufacturer}</Text>
          </View>
          
          <View style={styles.detailRow}>
            <Ionicons name="construct-outline" size={16} color="#666" />
            <Text style={styles.detailText}>Model: {equipment.model}</Text>
          </View>
          
          <View style={styles.detailRow}>
            <Ionicons name="calendar-number-outline" size={16} color="#666" />
            <Text style={styles.detailText}>Year: {equipment.year}</Text>
          </View>
          
          <View style={styles.detailRow}>
            <Ionicons name="flame-outline" size={16} color="#666" />
            <Text style={styles.detailText}>Fuel type: {equipment.fuelType}</Text>
          </View>
        </View>
      </View>
    );
  };

  // Render maintenance tab
  const renderMaintenanceTab = () => {
    if (!equipment) return null;
    
    return (
      <View style={styles.tabContent}>
        <View style={styles.maintenanceHeader}>
          <Text style={styles.maintenanceTitle}>Maintenance History</Text>
          <TouchableOpacity 
            style={styles.scheduleButton}
            onPress={handleScheduleMaintenance}
          >
            <Ionicons name="calendar" size={16} color="#fff" />
            <Text style={styles.scheduleButtonText}>Schedule</Text>
          </TouchableOpacity>
        </View>
        
        <FlatList
          data={equipment.maintenanceHistory}
          keyExtractor={item => item.id}
          renderItem={renderMaintenanceItem}
          contentContainerStyle={styles.maintenanceList}
          ListEmptyComponent={
            <View style={styles.emptyContainer}>
              <Ionicons name="alert-circle-outline" size={48} color="#666" />
              <Text style={styles.emptyText}>No maintenance history found</Text>
            </View>
          }
        />
      </View>
    );
  };

  // Render specifications tab
  const renderSpecificationsTab = () => {
    if (!equipment || !equipment.specifications) return null;
    
    const { specifications } = equipment;
    
    return (
      <View style={styles.tabContent}>
        <View style={styles.sectionContainer}>
          <Text style={styles.sectionTitle}>Technical Specifications</Text>
          
          {Object.entries(specifications).map(([key, value]) => (
            <View key={key} style={styles.detailRow}>
              <Ionicons name="hardware-chip-outline" size={16} color="#666" />
              <Text style={styles.detailText}>
                {key.charAt(0).toUpperCase() + key.slice(1)}: {value}
              </Text>
            </View>
          ))}
        </View>
      </View>
    );
  };

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#3b82f6" />
      </View>
    );
  }

  if (!equipment) {
    return (
      <View style={styles.errorContainer}>
        <Ionicons name="alert-circle-outline" size={48} color="#666" />
        <Text style={styles.errorText}>Equipment not found</Text>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Text style={styles.backButtonText}>Go Back</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <ScrollView>
        <View style={styles.header}>
          <Text style={styles.equipmentName}>{equipment.name}</Text>
          {renderStatusBadge(equipment.status)}
        </View>
        
        <View style={styles.imageContainer}>
          <Image
            source={{ uri: 'https://via.placeholder.com/400x300' }} // Placeholder since the mock URLs aren't real
            style={styles.equipmentImage}
            resizeMode="cover"
          />
        </View>
        
        <View style={styles.tabsContainer}>
          <TouchableOpacity
            style={[styles.tabButton, activeTab === 'details' && styles.activeTabButton]}
            onPress={() => setActiveTab('details')}
          >
            <Text style={[styles.tabButtonText, activeTab === 'details' && styles.activeTabButtonText]}>
              Details
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.tabButton, activeTab === 'maintenance' && styles.activeTabButton]}
            onPress={() => setActiveTab('maintenance')}
          >
            <Text style={[styles.tabButtonText, activeTab === 'maintenance' && styles.activeTabButtonText]}>
              Maintenance
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.tabButton, activeTab === 'specifications' && styles.activeTabButton]}
            onPress={() => setActiveTab('specifications')}
          >
            <Text style={[styles.tabButtonText, activeTab === 'specifications' && styles.activeTabButtonText]}>
              Specs
            </Text>
          </TouchableOpacity>
        </View>
        
        {activeTab === 'details' && renderDetailsTab()}
        {activeTab === 'maintenance' && renderMaintenanceTab()}
        {activeTab === 'specifications' && renderSpecificationsTab()}
      </ScrollView>
      
      <TouchableOpacity style={styles.editButton} onPress={handleEditEquipment}>
        <Ionicons name="create" size={24} color="#fff" />
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#fff',
  },
  equipmentName: {
    fontSize: 20,
    fontWeight: 'bold',
    flex: 1,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  statusText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  imageContainer: {
    width: '100%',
    height: 200,
    backgroundColor: '#e0e0e0',
  },
  equipmentImage: {
    width: '100%',
    height: '100%',
  },
  tabsContainer: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  tabButton: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
  },
  activeTabButton: {
    borderBottomWidth: 2,
    borderBottomColor: '#3b82f6',
  },
  tabButtonText: {
    color: '#666',
    fontWeight: '500',
  },
  activeTabButtonText: {
    color: '#3b82f6',
    fontWeight: 'bold',
  },
  tabContent: {
    padding: 16,
  },
  sectionContainer: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 1,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 12,
    color: '#333',
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  detailText: {
    marginLeft: 8,
    color: '#666',
    fontSize: 14,
  },
  descriptionText: {
    color: '#666',
    fontSize: 14,
    lineHeight: 20,
  },
  maintenanceHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  maintenanceTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  scheduleButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#3b82f6',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 4,
  },
  scheduleButtonText: {
    color: '#fff',
    marginLeft: 4,
    fontWeight: '500',
  },
  maintenanceList: {
    paddingBottom: 16,
  },
  maintenanceItem: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 1,
  },
  maintenanceHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  maintenanceDate: {
    fontWeight: 'bold',
    color: '#333',
  },
  maintenanceType: {
    color: '#3b82f6',
    fontWeight: '500',
  },
  maintenanceDescription: {
    color: '#666',
    fontSize: 14,
    marginBottom: 12,
  },
  maintenanceDetails: {
    marginTop: 8,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  errorText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
  backButton: {
    marginTop: 16,
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: '#3b82f6',
    borderRadius: 4,
  },
  backButtonText: {
    color: '#fff',
    fontWeight: '500',
  },
  emptyContainer: {
    padding: 32,
    alignItems: 'center',
  },
  emptyText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
  editButton: {
    position: 'absolute',
    bottom: 24,
    right: 24,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: '#3b82f6',
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
});

export default EquipmentDetailScreen;