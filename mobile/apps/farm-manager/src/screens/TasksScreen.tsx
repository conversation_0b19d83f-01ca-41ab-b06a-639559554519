import React, { useState } from 'react';
import { View, Text, StyleSheet, FlatList, TouchableOpacity, TextInput, Modal, ScrollView } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { MainStackParamList } from '../navigation/MainNavigator';

type TasksScreenNavigationProp = NativeStackNavigationProp<MainStackParamList, 'MainTabs'>;

interface Task {
  id: string;
  title: string;
  description: string;
  status: string;
  dueDate: string;
  priority: string;
  assignedTo: string;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

interface Employee {
  id: string;
  name: string;
  role: string;
}

const TasksScreen = () => {
  const navigation = useNavigation<TasksScreenNavigationProp>();
  const [searchQuery, setSearchQuery] = useState('');
  const [filterStatus, setFilterStatus] = useState<string | null>(null);
  const [isCreateModalVisible, setCreateModalVisible] = useState(false);
  const [newTask, setNewTask] = useState({
    title: '',
    description: '',
    dueDate: new Date().toISOString().split('T')[0],
    priority: 'medium',
    assignedTo: '',
  });

  // Mock data - would be fetched from API in a real implementation
  const mockTasks: Task[] = [
    {
      id: '1',
      title: 'Check irrigation system in Field A',
      description: 'Inspect all sprinklers and ensure proper water flow',
      status: 'pending',
      dueDate: '2023-06-25',
      priority: 'high',
      assignedTo: 'emp123',
      createdBy: 'manager456',
      createdAt: '2023-06-20T10:00:00Z',
      updatedAt: '2023-06-20T10:00:00Z',
    },
    {
      id: '2',
      title: 'Harvest tomatoes in Greenhouse 2',
      description: 'Harvest ripe tomatoes and place in designated containers',
      status: 'in_progress',
      dueDate: '2023-06-23',
      priority: 'medium',
      assignedTo: 'emp456',
      createdBy: 'manager456',
      createdAt: '2023-06-19T14:30:00Z',
      updatedAt: '2023-06-21T09:15:00Z',
    },
    {
      id: '3',
      title: 'Complete safety training module',
      description: 'Ensure all employees complete the safety training',
      status: 'completed',
      dueDate: '2023-06-18',
      priority: 'low',
      assignedTo: 'emp789',
      createdBy: 'manager456',
      createdAt: '2023-06-15T11:20:00Z',
      updatedAt: '2023-06-18T16:45:00Z',
    },
    {
      id: '4',
      title: 'Repair fence in south pasture',
      description: 'Fix damaged sections of fence using supplies in maintenance shed',
      status: 'pending',
      dueDate: '2023-06-30',
      priority: 'medium',
      assignedTo: 'emp123',
      createdBy: 'manager456',
      createdAt: '2023-06-21T08:00:00Z',
      updatedAt: '2023-06-21T08:00:00Z',
    },
    {
      id: '5',
      title: 'Order new seeds for fall planting',
      description: 'Research and order seeds for fall crop rotation',
      status: 'pending',
      dueDate: '2023-07-15',
      priority: 'high',
      assignedTo: 'emp456',
      createdBy: 'manager456',
      createdAt: '2023-06-22T09:30:00Z',
      updatedAt: '2023-06-22T09:30:00Z',
    },
  ];

  // Mock employees data
  const mockEmployees: Employee[] = [
    { id: 'emp123', name: 'John Doe', role: 'Field Worker' },
    { id: 'emp456', name: 'Jane Smith', role: 'Equipment Operator' },
    { id: 'emp789', name: 'Bob Johnson', role: 'Maintenance' },
  ];

  // Filter tasks based on search query and status filter
  const filteredTasks = mockTasks.filter(task => {
    const matchesSearch = task.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         task.description.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesFilter = filterStatus ? task.status === filterStatus : true;
    return matchesSearch && matchesFilter;
  });

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return '#F44336';
      case 'medium':
        return '#FF9800';
      case 'low':
        return '#4CAF50';
      default:
        return '#757575';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'pending':
        return 'Pending';
      case 'in_progress':
        return 'In Progress';
      case 'completed':
        return 'Completed';
      case 'cancelled':
        return 'Cancelled';
      default:
        return status;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return '#FF9800';
      case 'in_progress':
        return '#2196F3';
      case 'completed':
        return '#4CAF50';
      case 'cancelled':
        return '#F44336';
      default:
        return '#757575';
    }
  };

  const getEmployeeName = (employeeId: string) => {
    const employee = mockEmployees.find(emp => emp.id === employeeId);
    return employee ? employee.name : 'Unassigned';
  };

  const handleCreateTask = () => {
    // In a real implementation, this would send the data to an API
    console.log('Creating new task:', newTask);
    
    // Reset form and close modal
    setNewTask({
      title: '',
      description: '',
      dueDate: new Date().toISOString().split('T')[0],
      priority: 'medium',
      assignedTo: '',
    });
    setCreateModalVisible(false);
  };

  const renderTaskItem = ({ item }: { item: Task }) => (
    <TouchableOpacity 
      style={styles.taskItem}
      onPress={() => navigation.navigate('TaskDetail', { taskId: item.id })}
    >
      <View style={styles.taskHeader}>
        <View style={[styles.priorityIndicator, { backgroundColor: getPriorityColor(item.priority) }]} />
        <Text style={styles.taskTitle}>{item.title}</Text>
      </View>
      <Text style={styles.taskDescription} numberOfLines={2}>{item.description}</Text>
      <View style={styles.taskAssignee}>
        <Ionicons name="person" size={14} color="#757575" />
        <Text style={styles.assigneeText}>{getEmployeeName(item.assignedTo)}</Text>
      </View>
      <View style={styles.taskFooter}>
        <View style={[styles.statusBadge, { backgroundColor: getStatusColor(item.status) }]}>
          <Text style={styles.statusText}>{getStatusLabel(item.status)}</Text>
        </View>
        <Text style={styles.dueDate}>Due: {new Date(item.dueDate).toLocaleDateString()}</Text>
      </View>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      <View style={styles.searchContainer}>
        <Ionicons name="search" size={20} color="#757575" style={styles.searchIcon} />
        <TextInput
          style={styles.searchInput}
          placeholder="Search tasks..."
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
      </View>

      <View style={styles.filterContainer}>
        <TouchableOpacity
          style={[styles.filterButton, filterStatus === null && styles.filterButtonActive]}
          onPress={() => setFilterStatus(null)}
        >
          <Text style={[styles.filterButtonText, filterStatus === null && styles.filterButtonTextActive]}>All</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.filterButton, filterStatus === 'pending' && styles.filterButtonActive]}
          onPress={() => setFilterStatus('pending')}
        >
          <Text style={[styles.filterButtonText, filterStatus === 'pending' && styles.filterButtonTextActive]}>Pending</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.filterButton, filterStatus === 'in_progress' && styles.filterButtonActive]}
          onPress={() => setFilterStatus('in_progress')}
        >
          <Text style={[styles.filterButtonText, filterStatus === 'in_progress' && styles.filterButtonTextActive]}>In Progress</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.filterButton, filterStatus === 'completed' && styles.filterButtonActive]}
          onPress={() => setFilterStatus('completed')}
        >
          <Text style={[styles.filterButtonText, filterStatus === 'completed' && styles.filterButtonTextActive]}>Completed</Text>
        </TouchableOpacity>
      </View>

      <FlatList
        data={filteredTasks}
        renderItem={renderTaskItem}
        keyExtractor={item => item.id}
        contentContainerStyle={styles.taskList}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Ionicons name="list" size={50} color="#BDBDBD" />
            <Text style={styles.emptyText}>No tasks found</Text>
          </View>
        }
      />

      <TouchableOpacity 
        style={styles.fab}
        onPress={() => setCreateModalVisible(true)}
      >
        <Ionicons name="add" size={24} color="#fff" />
      </TouchableOpacity>

      {/* Create Task Modal */}
      <Modal
        animationType="slide"
        transparent={true}
        visible={isCreateModalVisible}
        onRequestClose={() => setCreateModalVisible(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Create New Task</Text>
              <TouchableOpacity onPress={() => setCreateModalVisible(false)}>
                <Ionicons name="close" size={24} color="#000" />
              </TouchableOpacity>
            </View>
            
            <ScrollView style={styles.modalBody}>
              <Text style={styles.inputLabel}>Title</Text>
              <TextInput
                style={styles.input}
                placeholder="Task title"
                value={newTask.title}
                onChangeText={(text) => setNewTask({...newTask, title: text})}
              />
              
              <Text style={styles.inputLabel}>Description</Text>
              <TextInput
                style={[styles.input, styles.textArea]}
                placeholder="Task description"
                multiline
                numberOfLines={4}
                value={newTask.description}
                onChangeText={(text) => setNewTask({...newTask, description: text})}
              />
              
              <Text style={styles.inputLabel}>Due Date</Text>
              <TextInput
                style={styles.input}
                placeholder="YYYY-MM-DD"
                value={newTask.dueDate}
                onChangeText={(text) => setNewTask({...newTask, dueDate: text})}
              />
              
              <Text style={styles.inputLabel}>Priority</Text>
              <View style={styles.priorityContainer}>
                <TouchableOpacity
                  style={[
                    styles.priorityButton,
                    newTask.priority === 'low' && styles.priorityButtonActive,
                    {borderColor: '#4CAF50'}
                  ]}
                  onPress={() => setNewTask({...newTask, priority: 'low'})}
                >
                  <Text style={[
                    styles.priorityButtonText,
                    newTask.priority === 'low' && {color: '#4CAF50'}
                  ]}>Low</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[
                    styles.priorityButton,
                    newTask.priority === 'medium' && styles.priorityButtonActive,
                    {borderColor: '#FF9800'}
                  ]}
                  onPress={() => setNewTask({...newTask, priority: 'medium'})}
                >
                  <Text style={[
                    styles.priorityButtonText,
                    newTask.priority === 'medium' && {color: '#FF9800'}
                  ]}>Medium</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[
                    styles.priorityButton,
                    newTask.priority === 'high' && styles.priorityButtonActive,
                    {borderColor: '#F44336'}
                  ]}
                  onPress={() => setNewTask({...newTask, priority: 'high'})}
                >
                  <Text style={[
                    styles.priorityButtonText,
                    newTask.priority === 'high' && {color: '#F44336'}
                  ]}>High</Text>
                </TouchableOpacity>
              </View>
              
              <Text style={styles.inputLabel}>Assign To</Text>
              <View style={styles.employeeContainer}>
                {mockEmployees.map(employee => (
                  <TouchableOpacity
                    key={employee.id}
                    style={[
                      styles.employeeButton,
                      newTask.assignedTo === employee.id && styles.employeeButtonActive
                    ]}
                    onPress={() => setNewTask({...newTask, assignedTo: employee.id})}
                  >
                    <Text style={[
                      styles.employeeButtonText,
                      newTask.assignedTo === employee.id && styles.employeeButtonTextActive
                    ]}>{employee.name}</Text>
                    <Text style={[
                      styles.employeeRole,
                      newTask.assignedTo === employee.id && styles.employeeRoleActive
                    ]}>{employee.role}</Text>
                  </TouchableOpacity>
                ))}
              </View>
            </ScrollView>
            
            <View style={styles.modalFooter}>
              <TouchableOpacity
                style={styles.cancelButton}
                onPress={() => setCreateModalVisible(false)}
              >
                <Text style={styles.cancelButtonText}>Cancel</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[
                  styles.createButton,
                  (!newTask.title || !newTask.description || !newTask.assignedTo) && styles.createButtonDisabled
                ]}
                onPress={handleCreateTask}
                disabled={!newTask.title || !newTask.description || !newTask.assignedTo}
              >
                <Text style={styles.createButtonText}>Create Task</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderRadius: 8,
    margin: 15,
    paddingHorizontal: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  searchIcon: {
    marginRight: 10,
  },
  searchInput: {
    flex: 1,
    height: 50,
    fontSize: 16,
  },
  filterContainer: {
    flexDirection: 'row',
    paddingHorizontal: 15,
    marginBottom: 10,
  },
  filterButton: {
    paddingVertical: 8,
    paddingHorizontal: 12,
    marginRight: 8,
    borderRadius: 20,
    backgroundColor: '#E0E0E0',
  },
  filterButtonActive: {
    backgroundColor: '#3b82f6',
  },
  filterButtonText: {
    color: '#757575',
    fontSize: 14,
  },
  filterButtonTextActive: {
    color: '#fff',
  },
  taskList: {
    padding: 15,
    paddingBottom: 80, // Add padding for FAB
  },
  taskItem: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 15,
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  taskHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  priorityIndicator: {
    width: 4,
    height: 20,
    borderRadius: 2,
    marginRight: 10,
  },
  taskTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    flex: 1,
  },
  taskDescription: {
    fontSize: 14,
    color: '#757575',
    marginBottom: 10,
  },
  taskAssignee: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  assigneeText: {
    fontSize: 14,
    color: '#757575',
    marginLeft: 5,
  },
  taskFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  statusBadge: {
    paddingVertical: 4,
    paddingHorizontal: 8,
    borderRadius: 4,
  },
  statusText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '500',
  },
  dueDate: {
    fontSize: 12,
    color: '#757575',
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 50,
  },
  emptyText: {
    fontSize: 16,
    color: '#757575',
    marginTop: 10,
  },
  fab: {
    position: 'absolute',
    bottom: 20,
    right: 20,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: '#3b82f6',
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    width: '90%',
    maxHeight: '80%',
    backgroundColor: '#fff',
    borderRadius: 8,
    overflow: 'hidden',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  modalBody: {
    padding: 15,
    maxHeight: 400,
  },
  modalFooter: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    padding: 15,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 5,
    marginTop: 10,
  },
  input: {
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 4,
    padding: 10,
    fontSize: 16,
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  priorityContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 5,
  },
  priorityButton: {
    flex: 1,
    borderWidth: 1,
    borderRadius: 4,
    padding: 10,
    marginHorizontal: 5,
    alignItems: 'center',
  },
  priorityButtonActive: {
    backgroundColor: 'rgba(0, 0, 0, 0.05)',
  },
  priorityButtonText: {
    fontWeight: '500',
  },
  employeeContainer: {
    marginTop: 5,
  },
  employeeButton: {
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 4,
    padding: 10,
    marginBottom: 10,
  },
  employeeButtonActive: {
    backgroundColor: '#3b82f6',
    borderColor: '#3b82f6',
  },
  employeeButtonText: {
    fontSize: 16,
    fontWeight: '500',
  },
  employeeButtonTextActive: {
    color: '#fff',
  },
  employeeRole: {
    fontSize: 14,
    color: '#757575',
    marginTop: 5,
  },
  employeeRoleActive: {
    color: 'rgba(255, 255, 255, 0.8)',
  },
  cancelButton: {
    paddingVertical: 10,
    paddingHorizontal: 15,
    marginRight: 10,
  },
  cancelButtonText: {
    color: '#757575',
    fontSize: 16,
  },
  createButton: {
    backgroundColor: '#3b82f6',
    paddingVertical: 10,
    paddingHorizontal: 15,
    borderRadius: 4,
  },
  createButtonDisabled: {
    backgroundColor: '#BDBDBD',
  },
  createButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '500',
  },
});

export default TasksScreen;