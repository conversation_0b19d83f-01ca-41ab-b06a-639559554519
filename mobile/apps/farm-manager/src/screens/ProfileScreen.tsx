import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  Switch,
  Alert,
  ActivityIndicator
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { useAuth } from '../../../../shared/store/AuthProvider';

// Mock user data
const mockUserData = {
  id: '1',
  name: '<PERSON>',
  email: '<EMAIL>',
  role: 'Farm Manager',
  phone: '(*************',
  profileImage: 'https://example.com/profile.jpg',
  farmName: 'Smith Family Farms',
  location: 'Midwest, USA',
  joinDate: '2021-03-15',
  lastLogin: '2023-06-20 09:45 AM'
};

const ProfileScreen: React.FC = () => {
  const navigation = useNavigation();
  const { logout } = useAuth();
  
  const [userData, setUserData] = useState(mockUserData);
  const [isLoading, setIsLoading] = useState(true);
  
  // App settings
  const [notificationsEnabled, setNotificationsEnabled] = useState(true);
  const [darkModeEnabled, setDarkModeEnabled] = useState(false);
  const [locationTrackingEnabled, setLocationTrackingEnabled] = useState(true);
  const [dataBackupEnabled, setDataBackupEnabled] = useState(true);
  const [autoSyncEnabled, setAutoSyncEnabled] = useState(true);
  
  // Notification settings
  const [taskAssignmentNotifications, setTaskAssignmentNotifications] = useState(true);
  const [dueDateNotifications, setDueDateNotifications] = useState(true);
  const [statusChangeNotifications, setStatusChangeNotifications] = useState(true);
  const [systemAnnouncementNotifications, setSystemAnnouncementNotifications] = useState(true);
  
  // Fetch user data
  useEffect(() => {
    const fetchUserData = async () => {
      setIsLoading(true);
      // Simulate API call
      setTimeout(() => {
        setUserData(mockUserData);
        setIsLoading(false);
      }, 1000);
    };
    
    fetchUserData();
  }, []);
  
  // Handle edit profile
  const handleEditProfile = () => {
    Alert.alert('Edit Profile', 'This feature will be implemented soon.');
    // In a real app, navigate to an edit profile form
    // navigation.navigate('EditProfile');
  };
  
  // Handle logout
  const handleLogout = () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to log out?',
      [
        {
          text: 'Cancel',
          style: 'cancel'
        },
        {
          text: 'Logout',
          onPress: () => {
            // Perform logout
            logout();
          },
          style: 'destructive'
        }
      ]
    );
  };
  
  // Handle help and support
  const handleHelpSupport = () => {
    Alert.alert('Help & Support', 'This feature will be implemented soon.');
    // In a real app, navigate to a help and support screen
    // navigation.navigate('HelpSupport');
  };
  
  // Handle about
  const handleAbout = () => {
    Alert.alert('About', 'NxtAcre Farm Manager App\nVersion 1.0.0\n© 2023 NxtAcre');
  };
  
  // Handle terms and privacy
  const handleTermsPrivacy = () => {
    Alert.alert('Terms & Privacy', 'This feature will be implemented soon.');
    // In a real app, navigate to a terms and privacy screen
    // navigation.navigate('TermsPrivacy');
  };
  
  // Handle data management
  const handleDataManagement = () => {
    Alert.alert('Data Management', 'This feature will be implemented soon.');
    // In a real app, navigate to a data management screen
    // navigation.navigate('DataManagement');
  };
  
  // Render settings section
  const renderSettingsSection = () => {
    return (
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>App Settings</Text>
        
        <View style={styles.settingItem}>
          <View style={styles.settingLabelContainer}>
            <Ionicons name="notifications-outline" size={20} color="#666" />
            <Text style={styles.settingLabel}>Notifications</Text>
          </View>
          <Switch
            value={notificationsEnabled}
            onValueChange={setNotificationsEnabled}
            trackColor={{ false: '#e0e0e0', true: '#bbd6ff' }}
            thumbColor={notificationsEnabled ? '#3b82f6' : '#f4f3f4'}
          />
        </View>
        
        <View style={styles.settingItem}>
          <View style={styles.settingLabelContainer}>
            <Ionicons name="moon-outline" size={20} color="#666" />
            <Text style={styles.settingLabel}>Dark Mode</Text>
          </View>
          <Switch
            value={darkModeEnabled}
            onValueChange={setDarkModeEnabled}
            trackColor={{ false: '#e0e0e0', true: '#bbd6ff' }}
            thumbColor={darkModeEnabled ? '#3b82f6' : '#f4f3f4'}
          />
        </View>
        
        <View style={styles.settingItem}>
          <View style={styles.settingLabelContainer}>
            <Ionicons name="location-outline" size={20} color="#666" />
            <Text style={styles.settingLabel}>Location Tracking</Text>
          </View>
          <Switch
            value={locationTrackingEnabled}
            onValueChange={setLocationTrackingEnabled}
            trackColor={{ false: '#e0e0e0', true: '#bbd6ff' }}
            thumbColor={locationTrackingEnabled ? '#3b82f6' : '#f4f3f4'}
          />
        </View>
        
        <View style={styles.settingItem}>
          <View style={styles.settingLabelContainer}>
            <Ionicons name="cloud-upload-outline" size={20} color="#666" />
            <Text style={styles.settingLabel}>Data Backup</Text>
          </View>
          <Switch
            value={dataBackupEnabled}
            onValueChange={setDataBackupEnabled}
            trackColor={{ false: '#e0e0e0', true: '#bbd6ff' }}
            thumbColor={dataBackupEnabled ? '#3b82f6' : '#f4f3f4'}
          />
        </View>
        
        <View style={styles.settingItem}>
          <View style={styles.settingLabelContainer}>
            <Ionicons name="sync-outline" size={20} color="#666" />
            <Text style={styles.settingLabel}>Auto Sync</Text>
          </View>
          <Switch
            value={autoSyncEnabled}
            onValueChange={setAutoSyncEnabled}
            trackColor={{ false: '#e0e0e0', true: '#bbd6ff' }}
            thumbColor={autoSyncEnabled ? '#3b82f6' : '#f4f3f4'}
          />
        </View>
      </View>
    );
  };
  
  // Render notification settings section
  const renderNotificationSettingsSection = () => {
    if (!notificationsEnabled) return null;
    
    return (
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Notification Settings</Text>
        
        <View style={styles.settingItem}>
          <View style={styles.settingLabelContainer}>
            <Ionicons name="checkmark-circle-outline" size={20} color="#666" />
            <Text style={styles.settingLabel}>Task Assignments</Text>
          </View>
          <Switch
            value={taskAssignmentNotifications}
            onValueChange={setTaskAssignmentNotifications}
            trackColor={{ false: '#e0e0e0', true: '#bbd6ff' }}
            thumbColor={taskAssignmentNotifications ? '#3b82f6' : '#f4f3f4'}
          />
        </View>
        
        <View style={styles.settingItem}>
          <View style={styles.settingLabelContainer}>
            <Ionicons name="calendar-outline" size={20} color="#666" />
            <Text style={styles.settingLabel}>Due Date Alerts</Text>
          </View>
          <Switch
            value={dueDateNotifications}
            onValueChange={setDueDateNotifications}
            trackColor={{ false: '#e0e0e0', true: '#bbd6ff' }}
            thumbColor={dueDateNotifications ? '#3b82f6' : '#f4f3f4'}
          />
        </View>
        
        <View style={styles.settingItem}>
          <View style={styles.settingLabelContainer}>
            <Ionicons name="refresh-outline" size={20} color="#666" />
            <Text style={styles.settingLabel}>Status Changes</Text>
          </View>
          <Switch
            value={statusChangeNotifications}
            onValueChange={setStatusChangeNotifications}
            trackColor={{ false: '#e0e0e0', true: '#bbd6ff' }}
            thumbColor={statusChangeNotifications ? '#3b82f6' : '#f4f3f4'}
          />
        </View>
        
        <View style={styles.settingItem}>
          <View style={styles.settingLabelContainer}>
            <Ionicons name="megaphone-outline" size={20} color="#666" />
            <Text style={styles.settingLabel}>System Announcements</Text>
          </View>
          <Switch
            value={systemAnnouncementNotifications}
            onValueChange={setSystemAnnouncementNotifications}
            trackColor={{ false: '#e0e0e0', true: '#bbd6ff' }}
            thumbColor={systemAnnouncementNotifications ? '#3b82f6' : '#f4f3f4'}
          />
        </View>
      </View>
    );
  };
  
  // Render support section
  const renderSupportSection = () => {
    return (
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Support</Text>
        
        <TouchableOpacity style={styles.menuItem} onPress={handleHelpSupport}>
          <View style={styles.menuIconContainer}>
            <Ionicons name="help-circle-outline" size={20} color="#666" />
          </View>
          <Text style={styles.menuItemText}>Help & Support</Text>
          <Ionicons name="chevron-forward" size={20} color="#ccc" />
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.menuItem} onPress={handleAbout}>
          <View style={styles.menuIconContainer}>
            <Ionicons name="information-circle-outline" size={20} color="#666" />
          </View>
          <Text style={styles.menuItemText}>About</Text>
          <Ionicons name="chevron-forward" size={20} color="#ccc" />
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.menuItem} onPress={handleTermsPrivacy}>
          <View style={styles.menuIconContainer}>
            <Ionicons name="document-text-outline" size={20} color="#666" />
          </View>
          <Text style={styles.menuItemText}>Terms & Privacy</Text>
          <Ionicons name="chevron-forward" size={20} color="#ccc" />
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.menuItem} onPress={handleDataManagement}>
          <View style={styles.menuIconContainer}>
            <Ionicons name="server-outline" size={20} color="#666" />
          </View>
          <Text style={styles.menuItemText}>Data Management</Text>
          <Ionicons name="chevron-forward" size={20} color="#ccc" />
        </TouchableOpacity>
      </View>
    );
  };
  
  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#3b82f6" />
      </View>
    );
  }
  
  return (
    <View style={styles.container}>
      <ScrollView>
        <View style={styles.profileHeader}>
          <View style={styles.profileImageContainer}>
            <Image
              source={{ uri: 'https://via.placeholder.com/150' }} // Placeholder since the mock URL isn't real
              style={styles.profileImage}
            />
            <TouchableOpacity style={styles.editImageButton} onPress={handleEditProfile}>
              <Ionicons name="camera" size={16} color="#fff" />
            </TouchableOpacity>
          </View>
          
          <Text style={styles.profileName}>{userData.name}</Text>
          <Text style={styles.profileRole}>{userData.role}</Text>
          <Text style={styles.profileFarm}>{userData.farmName}</Text>
          
          <TouchableOpacity style={styles.editProfileButton} onPress={handleEditProfile}>
            <Text style={styles.editProfileButtonText}>Edit Profile</Text>
          </TouchableOpacity>
        </View>
        
        <View style={styles.profileDetails}>
          <View style={styles.detailItem}>
            <Ionicons name="mail-outline" size={20} color="#666" />
            <Text style={styles.detailText}>{userData.email}</Text>
          </View>
          
          <View style={styles.detailItem}>
            <Ionicons name="call-outline" size={20} color="#666" />
            <Text style={styles.detailText}>{userData.phone}</Text>
          </View>
          
          <View style={styles.detailItem}>
            <Ionicons name="location-outline" size={20} color="#666" />
            <Text style={styles.detailText}>{userData.location}</Text>
          </View>
          
          <View style={styles.detailItem}>
            <Ionicons name="calendar-outline" size={20} color="#666" />
            <Text style={styles.detailText}>Joined: {userData.joinDate}</Text>
          </View>
          
          <View style={styles.detailItem}>
            <Ionicons name="time-outline" size={20} color="#666" />
            <Text style={styles.detailText}>Last Login: {userData.lastLogin}</Text>
          </View>
        </View>
        
        {renderSettingsSection()}
        {renderNotificationSettingsSection()}
        {renderSupportSection()}
        
        <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
          <Ionicons name="log-out-outline" size={20} color="#fff" />
          <Text style={styles.logoutButtonText}>Logout</Text>
        </TouchableOpacity>
        
        <View style={styles.versionContainer}>
          <Text style={styles.versionText}>NxtAcre Farm Manager v1.0.0</Text>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  profileHeader: {
    backgroundColor: '#fff',
    padding: 24,
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  profileImageContainer: {
    position: 'relative',
    marginBottom: 16,
  },
  profileImage: {
    width: 100,
    height: 100,
    borderRadius: 50,
  },
  editImageButton: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    backgroundColor: '#3b82f6',
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#fff',
  },
  profileName: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  profileRole: {
    fontSize: 16,
    color: '#666',
    marginBottom: 4,
  },
  profileFarm: {
    fontSize: 14,
    color: '#666',
    marginBottom: 16,
  },
  editProfileButton: {
    backgroundColor: '#3b82f6',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 4,
  },
  editProfileButtonText: {
    color: '#fff',
    fontWeight: '500',
  },
  profileDetails: {
    backgroundColor: '#fff',
    padding: 16,
    marginTop: 16,
  },
  detailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  detailText: {
    marginLeft: 12,
    color: '#666',
    fontSize: 14,
  },
  section: {
    backgroundColor: '#fff',
    padding: 16,
    marginTop: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 16,
    color: '#333',
  },
  settingItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  settingLabelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  settingLabel: {
    marginLeft: 12,
    fontSize: 14,
    color: '#333',
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  menuIconContainer: {
    width: 24,
    alignItems: 'center',
  },
  menuItemText: {
    flex: 1,
    marginLeft: 12,
    fontSize: 14,
    color: '#333',
  },
  logoutButton: {
    backgroundColor: '#f44336',
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 16,
    marginTop: 24,
    marginBottom: 16,
    paddingVertical: 12,
    borderRadius: 4,
  },
  logoutButtonText: {
    color: '#fff',
    fontWeight: 'bold',
    marginLeft: 8,
  },
  versionContainer: {
    alignItems: 'center',
    marginBottom: 24,
  },
  versionText: {
    fontSize: 12,
    color: '#999',
  },
});

export default ProfileScreen;