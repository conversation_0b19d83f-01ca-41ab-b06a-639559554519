import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Alert, ActivityIndicator, Modal, TextInput } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useRoute, useNavigation } from '@react-navigation/native';
import { RouteProp } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { MainStackParamList } from '../navigation/MainNavigator';

type TaskDetailScreenRouteProp = RouteProp<MainStackParamList, 'TaskDetail'>;
type TaskDetailScreenNavigationProp = NativeStackNavigationProp<MainStackParamList, 'TaskDetail'>;

interface Task {
  id: string;
  title: string;
  description: string;
  status: string;
  dueDate: string;
  priority: string;
  assignedTo: string;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

interface Employee {
  id: string;
  name: string;
  role: string;
}

const TaskDetailScreen = () => {
  const route = useRoute<TaskDetailScreenRouteProp>();
  const navigation = useNavigation<TaskDetailScreenNavigationProp>();
  const { taskId } = route.params;
  const [task, setTask] = useState<Task | null>(null);
  const [loading, setLoading] = useState(true);
  const [isEditModalVisible, setEditModalVisible] = useState(false);
  const [isReassignModalVisible, setReassignModalVisible] = useState(false);
  const [editedTask, setEditedTask] = useState<Partial<Task>>({});

  // Mock employees data
  const mockEmployees: Employee[] = [
    { id: 'emp123', name: 'John Doe', role: 'Field Worker' },
    { id: 'emp456', name: 'Jane Smith', role: 'Equipment Operator' },
    { id: 'emp789', name: 'Bob Johnson', role: 'Maintenance' },
  ];

  useEffect(() => {
    // In a real implementation, this would fetch the task from an API
    // For now, we'll use mock data
    const fetchTask = async () => {
      try {
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 500));
        
        // Mock task data
        const mockTask: Task = {
          id: taskId,
          title: 'Check irrigation system in Field A',
          description: 'Inspect all sprinklers and ensure proper water flow. Check for any leaks or damaged components. Report any issues immediately.',
          status: 'pending',
          dueDate: '2023-06-25',
          priority: 'high',
          assignedTo: 'emp123',
          createdBy: 'manager456',
          createdAt: '2023-06-20T10:00:00Z',
          updatedAt: '2023-06-20T10:00:00Z',
        };
        
        setTask(mockTask);
        setEditedTask({
          title: mockTask.title,
          description: mockTask.description,
          dueDate: mockTask.dueDate,
          priority: mockTask.priority,
        });
      } catch (error) {
        console.error('Error fetching task:', error);
        Alert.alert('Error', 'Failed to load task details');
      } finally {
        setLoading(false);
      }
    };

    fetchTask();
  }, [taskId]);

  const handleStatusChange = (newStatus: 'pending' | 'in_progress' | 'completed' | 'cancelled') => {
    if (!task) return;

    Alert.alert(
      'Update Status',
      `Are you sure you want to mark this task as ${newStatus.replace('_', ' ')}?`,
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Update',
          onPress: () => {
            // In a real implementation, this would update the task status via API
            setTask({
              ...task,
              status: newStatus,
              updatedAt: new Date().toISOString(),
            });
            
            // Show success message
            Alert.alert('Success', 'Task status updated successfully');
          },
        },
      ]
    );
  };

  const handleEditTask = () => {
    if (!task || !editedTask) return;

    // In a real implementation, this would update the task via API
    setTask({
      ...task,
      ...editedTask,
      updatedAt: new Date().toISOString(),
    });
    
    // Close modal and show success message
    setEditModalVisible(false);
    Alert.alert('Success', 'Task details updated successfully');
  };

  const handleReassignTask = (employeeId: string) => {
    if (!task) return;

    // In a real implementation, this would update the task via API
    setTask({
      ...task,
      assignedTo: employeeId,
      updatedAt: new Date().toISOString(),
    });
    
    // Close modal and show success message
    setReassignModalVisible(false);
    Alert.alert('Success', 'Task reassigned successfully');
  };

  const getEmployeeName = (employeeId: string) => {
    const employee = mockEmployees.find(emp => emp.id === employeeId);
    return employee ? employee.name : 'Unassigned';
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return '#F44336';
      case 'medium':
        return '#FF9800';
      case 'low':
        return '#4CAF50';
      default:
        return '#757575';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'pending':
        return 'Pending';
      case 'in_progress':
        return 'In Progress';
      case 'completed':
        return 'Completed';
      case 'cancelled':
        return 'Cancelled';
      default:
        return status;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return '#FF9800';
      case 'in_progress':
        return '#2196F3';
      case 'completed':
        return '#4CAF50';
      case 'cancelled':
        return '#F44336';
      default:
        return '#757575';
    }
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#3b82f6" />
        <Text style={styles.loadingText}>Loading task details...</Text>
      </View>
    );
  }

  if (!task) {
    return (
      <View style={styles.errorContainer}>
        <Ionicons name="alert-circle-outline" size={50} color="#F44336" />
        <Text style={styles.errorText}>Task not found</Text>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Text style={styles.backButtonText}>Go Back</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <View style={[styles.priorityIndicator, { backgroundColor: getPriorityColor(task.priority) }]} />
        <Text style={styles.title}>{task.title}</Text>
      </View>

      <View style={styles.section}>
        <View style={styles.statusContainer}>
          <Text style={styles.sectionLabel}>Status:</Text>
          <View style={[styles.statusBadge, { backgroundColor: getStatusColor(task.status) }]}>
            <Text style={styles.statusText}>{getStatusLabel(task.status)}</Text>
          </View>
        </View>

        <View style={styles.infoRow}>
          <Text style={styles.sectionLabel}>Priority:</Text>
          <Text style={[styles.priorityText, { color: getPriorityColor(task.priority) }]}>
            {task.priority.charAt(0).toUpperCase() + task.priority.slice(1)}
          </Text>
        </View>

        <View style={styles.infoRow}>
          <Text style={styles.sectionLabel}>Due Date:</Text>
          <Text style={styles.infoText}>{new Date(task.dueDate).toLocaleDateString()}</Text>
        </View>

        <View style={styles.infoRow}>
          <Text style={styles.sectionLabel}>Assigned To:</Text>
          <Text style={styles.infoText}>{getEmployeeName(task.assignedTo)}</Text>
        </View>

        <View style={styles.infoRow}>
          <Text style={styles.sectionLabel}>Created:</Text>
          <Text style={styles.infoText}>{new Date(task.createdAt).toLocaleString()}</Text>
        </View>

        {task.updatedAt !== task.createdAt && (
          <View style={styles.infoRow}>
            <Text style={styles.sectionLabel}>Last Updated:</Text>
            <Text style={styles.infoText}>{new Date(task.updatedAt).toLocaleString()}</Text>
          </View>
        )}
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Description</Text>
        <Text style={styles.description}>{task.description}</Text>
      </View>

      <View style={styles.actionsContainer}>
        <Text style={styles.sectionTitle}>Actions</Text>
        
        <View style={styles.actionButtons}>
          <TouchableOpacity 
            style={[styles.actionButton, { backgroundColor: '#3b82f6' }]}
            onPress={() => setEditModalVisible(true)}
          >
            <Ionicons name="create" size={20} color="#fff" />
            <Text style={styles.actionButtonText}>Edit</Text>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={[styles.actionButton, { backgroundColor: '#9c27b0' }]}
            onPress={() => setReassignModalVisible(true)}
          >
            <Ionicons name="people" size={20} color="#fff" />
            <Text style={styles.actionButtonText}>Reassign</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.statusActionButtons}>
          {task.status !== 'in_progress' && task.status !== 'completed' && (
            <TouchableOpacity 
              style={[styles.statusButton, { backgroundColor: '#2196F3' }]}
              onPress={() => handleStatusChange('in_progress')}
            >
              <Ionicons name="play" size={20} color="#fff" />
              <Text style={styles.actionButtonText}>Start Task</Text>
            </TouchableOpacity>
          )}
          
          {task.status !== 'completed' && (
            <TouchableOpacity 
              style={[styles.statusButton, { backgroundColor: '#4CAF50' }]}
              onPress={() => handleStatusChange('completed')}
            >
              <Ionicons name="checkmark" size={20} color="#fff" />
              <Text style={styles.actionButtonText}>Complete</Text>
            </TouchableOpacity>
          )}
          
          {task.status !== 'cancelled' && (
            <TouchableOpacity 
              style={[styles.statusButton, { backgroundColor: '#F44336' }]}
              onPress={() => handleStatusChange('cancelled')}
            >
              <Ionicons name="close" size={20} color="#fff" />
              <Text style={styles.actionButtonText}>Cancel</Text>
            </TouchableOpacity>
          )}
        </View>
      </View>

      {/* Edit Task Modal */}
      <Modal
        animationType="slide"
        transparent={true}
        visible={isEditModalVisible}
        onRequestClose={() => setEditModalVisible(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Edit Task</Text>
              <TouchableOpacity onPress={() => setEditModalVisible(false)}>
                <Ionicons name="close" size={24} color="#000" />
              </TouchableOpacity>
            </View>
            
            <ScrollView style={styles.modalBody}>
              <Text style={styles.inputLabel}>Title</Text>
              <TextInput
                style={styles.input}
                placeholder="Task title"
                value={editedTask.title}
                onChangeText={(text) => setEditedTask({...editedTask, title: text})}
              />
              
              <Text style={styles.inputLabel}>Description</Text>
              <TextInput
                style={[styles.input, styles.textArea]}
                placeholder="Task description"
                multiline
                numberOfLines={4}
                value={editedTask.description}
                onChangeText={(text) => setEditedTask({...editedTask, description: text})}
              />
              
              <Text style={styles.inputLabel}>Due Date</Text>
              <TextInput
                style={styles.input}
                placeholder="YYYY-MM-DD"
                value={editedTask.dueDate}
                onChangeText={(text) => setEditedTask({...editedTask, dueDate: text})}
              />
              
              <Text style={styles.inputLabel}>Priority</Text>
              <View style={styles.priorityContainer}>
                <TouchableOpacity
                  style={[
                    styles.priorityButton,
                    editedTask.priority === 'low' && styles.priorityButtonActive,
                    {borderColor: '#4CAF50'}
                  ]}
                  onPress={() => setEditedTask({...editedTask, priority: 'low'})}
                >
                  <Text style={[
                    styles.priorityButtonText,
                    editedTask.priority === 'low' && {color: '#4CAF50'}
                  ]}>Low</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[
                    styles.priorityButton,
                    editedTask.priority === 'medium' && styles.priorityButtonActive,
                    {borderColor: '#FF9800'}
                  ]}
                  onPress={() => setEditedTask({...editedTask, priority: 'medium'})}
                >
                  <Text style={[
                    styles.priorityButtonText,
                    editedTask.priority === 'medium' && {color: '#FF9800'}
                  ]}>Medium</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[
                    styles.priorityButton,
                    editedTask.priority === 'high' && styles.priorityButtonActive,
                    {borderColor: '#F44336'}
                  ]}
                  onPress={() => setEditedTask({...editedTask, priority: 'high'})}
                >
                  <Text style={[
                    styles.priorityButtonText,
                    editedTask.priority === 'high' && {color: '#F44336'}
                  ]}>High</Text>
                </TouchableOpacity>
              </View>
            </ScrollView>
            
            <View style={styles.modalFooter}>
              <TouchableOpacity
                style={styles.cancelButton}
                onPress={() => setEditModalVisible(false)}
              >
                <Text style={styles.cancelButtonText}>Cancel</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[
                  styles.saveButton,
                  (!editedTask.title || !editedTask.description) && styles.saveButtonDisabled
                ]}
                onPress={handleEditTask}
                disabled={!editedTask.title || !editedTask.description}
              >
                <Text style={styles.saveButtonText}>Save Changes</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {/* Reassign Task Modal */}
      <Modal
        animationType="slide"
        transparent={true}
        visible={isReassignModalVisible}
        onRequestClose={() => setReassignModalVisible(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Reassign Task</Text>
              <TouchableOpacity onPress={() => setReassignModalVisible(false)}>
                <Ionicons name="close" size={24} color="#000" />
              </TouchableOpacity>
            </View>
            
            <ScrollView style={styles.modalBody}>
              <Text style={styles.modalSubtitle}>Select an employee to assign this task:</Text>
              
              <View style={styles.employeeContainer}>
                {mockEmployees.map(employee => (
                  <TouchableOpacity
                    key={employee.id}
                    style={[
                      styles.employeeButton,
                      task.assignedTo === employee.id && styles.employeeButtonActive
                    ]}
                    onPress={() => handleReassignTask(employee.id)}
                  >
                    <Text style={[
                      styles.employeeButtonText,
                      task.assignedTo === employee.id && styles.employeeButtonTextActive
                    ]}>{employee.name}</Text>
                    <Text style={[
                      styles.employeeRole,
                      task.assignedTo === employee.id && styles.employeeRoleActive
                    ]}>{employee.role}</Text>
                  </TouchableOpacity>
                ))}
              </View>
            </ScrollView>
            
            <View style={styles.modalFooter}>
              <TouchableOpacity
                style={styles.cancelButton}
                onPress={() => setReassignModalVisible(false)}
              >
                <Text style={styles.cancelButtonText}>Cancel</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#757575',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    padding: 20,
  },
  errorText: {
    marginTop: 10,
    fontSize: 18,
    color: '#F44336',
    marginBottom: 20,
  },
  backButton: {
    backgroundColor: '#3b82f6',
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 5,
  },
  backButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '500',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  priorityIndicator: {
    width: 4,
    height: 24,
    borderRadius: 2,
    marginRight: 15,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    flex: 1,
  },
  section: {
    backgroundColor: '#fff',
    padding: 20,
    marginBottom: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 15,
  },
  statusBadge: {
    paddingVertical: 4,
    paddingHorizontal: 8,
    borderRadius: 4,
    marginLeft: 10,
  },
  statusText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '500',
  },
  infoRow: {
    flexDirection: 'row',
    marginBottom: 10,
  },
  sectionLabel: {
    fontSize: 16,
    color: '#757575',
    width: 100,
  },
  priorityText: {
    fontSize: 16,
    fontWeight: '500',
  },
  infoText: {
    fontSize: 16,
    color: '#212121',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#212121',
  },
  description: {
    fontSize: 16,
    color: '#212121',
    lineHeight: 24,
  },
  actionsContainer: {
    backgroundColor: '#fff',
    padding: 20,
    marginBottom: 20,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 15,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 5,
    paddingVertical: 10,
    paddingHorizontal: 15,
    flex: 1,
    marginHorizontal: 5,
  },
  statusActionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    flexWrap: 'wrap',
  },
  statusButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 5,
    paddingVertical: 10,
    paddingHorizontal: 15,
    marginTop: 10,
    flex: 1,
    marginHorizontal: 5,
  },
  actionButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 5,
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    width: '90%',
    maxHeight: '80%',
    backgroundColor: '#fff',
    borderRadius: 8,
    overflow: 'hidden',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  modalSubtitle: {
    fontSize: 16,
    marginBottom: 15,
  },
  modalBody: {
    padding: 15,
    maxHeight: 400,
  },
  modalFooter: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    padding: 15,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 5,
    marginTop: 10,
  },
  input: {
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 4,
    padding: 10,
    fontSize: 16,
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  priorityContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 5,
  },
  priorityButton: {
    flex: 1,
    borderWidth: 1,
    borderRadius: 4,
    padding: 10,
    marginHorizontal: 5,
    alignItems: 'center',
  },
  priorityButtonActive: {
    backgroundColor: 'rgba(0, 0, 0, 0.05)',
  },
  priorityButtonText: {
    fontWeight: '500',
  },
  employeeContainer: {
    marginTop: 5,
  },
  employeeButton: {
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 4,
    padding: 10,
    marginBottom: 10,
  },
  employeeButtonActive: {
    backgroundColor: '#3b82f6',
    borderColor: '#3b82f6',
  },
  employeeButtonText: {
    fontSize: 16,
    fontWeight: '500',
  },
  employeeButtonTextActive: {
    color: '#fff',
  },
  employeeRole: {
    fontSize: 14,
    color: '#757575',
    marginTop: 5,
  },
  employeeRoleActive: {
    color: 'rgba(255, 255, 255, 0.8)',
  },
  cancelButton: {
    paddingVertical: 10,
    paddingHorizontal: 15,
    marginRight: 10,
  },
  cancelButtonText: {
    color: '#757575',
    fontSize: 16,
  },
  saveButton: {
    backgroundColor: '#3b82f6',
    paddingVertical: 10,
    paddingHorizontal: 15,
    borderRadius: 4,
  },
  saveButtonDisabled: {
    backgroundColor: '#BDBDBD',
  },
  saveButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '500',
  },
});

export default TaskDetailScreen;