import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  Image,
  Alert
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { RouteProp, useNavigation, useRoute } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { MainStackParamList } from '../navigation/MainNavigator';

// Mock data for inventory items
const mockInventoryData = [
  {
    id: '1',
    name: 'Nitrogen Fertilizer',
    category: 'Fertilizer',
    sku: 'FERT-N-50',
    description: 'High-quality nitrogen fertilizer for corn and wheat crops.',
    unit: 'bag',
    unitWeight: '50 lbs',
    currentStock: 125,
    reorderPoint: 50,
    optimalStock: 200,
    location: 'Main Warehouse - Section A3',
    supplier: {
      name: 'AgriSupplies Inc.',
      contact: '<PERSON>',
      email: '<EMAIL>',
      phone: '(*************'
    },
    lastPurchase: {
      date: '2023-05-10',
      quantity: 50,
      price: 65.00,
      total: 3250.00
    },
    purchaseHistory: [
      {
        id: 'ph1',
        date: '2023-05-10',
        quantity: 50,
        price: 65.00,
        total: 3250.00,
        supplier: 'AgriSupplies Inc.',
        invoice: 'INV-2023-0087'
      },
      {
        id: 'ph2',
        date: '2023-02-15',
        quantity: 100,
        price: 62.50,
        total: 6250.00,
        supplier: 'AgriSupplies Inc.',
        invoice: 'INV-2023-0042'
      }
    ],
    usageHistory: [
      {
        id: 'uh1',
        date: '2023-06-05',
        quantity: 25,
        field: 'North Field',
        crop: 'Corn',
        user: 'John Smith'
      },
      {
        id: 'uh2',
        date: '2023-05-20',
        quantity: 30,
        field: 'West Field',
        crop: 'Wheat',
        user: 'Sarah Johnson'
      }
    ],
    image: 'https://example.com/nitrogen_fertilizer.jpg',
    notes: 'Store in a dry place. Keep away from direct sunlight.',
    tags: ['fertilizer', 'nitrogen', 'crop inputs'],
    createdAt: '2022-01-15',
    updatedAt: '2023-05-10'
  },
  {
    id: '2',
    name: 'Corn Seed - Pioneer P1234',
    category: 'Seeds',
    sku: 'SEED-CORN-P1234',
    description: 'High-yield corn seed variety with drought resistance.',
    unit: 'bag',
    unitWeight: '80,000 kernels',
    currentStock: 45,
    reorderPoint: 20,
    optimalStock: 100,
    location: 'Seed Storage - Climate Controlled Room',
    supplier: {
      name: 'Pioneer Seeds',
      contact: 'Jennifer Adams',
      email: '<EMAIL>',
      phone: '(*************'
    },
    lastPurchase: {
      date: '2023-01-20',
      quantity: 50,
      price: 325.00,
      total: 16250.00
    },
    purchaseHistory: [
      {
        id: 'ph1',
        date: '2023-01-20',
        quantity: 50,
        price: 325.00,
        total: 16250.00,
        supplier: 'Pioneer Seeds',
        invoice: 'INV-2023-0015'
      },
      {
        id: 'ph2',
        date: '2022-01-15',
        quantity: 45,
        price: 310.00,
        total: 13950.00,
        supplier: 'Pioneer Seeds',
        invoice: 'INV-2022-0022'
      }
    ],
    usageHistory: [
      {
        id: 'uh1',
        date: '2023-04-10',
        quantity: 5,
        field: 'East Field',
        crop: 'Corn',
        user: 'John Smith'
      }
    ],
    image: 'https://example.com/corn_seed.jpg',
    notes: 'Store in climate-controlled environment. Optimal planting depth: 1.5-2 inches.',
    tags: ['seed', 'corn', 'pioneer'],
    createdAt: '2022-01-15',
    updatedAt: '2023-01-20'
  }
];

type InventoryDetailScreenRouteProp = RouteProp<MainStackParamList, 'InventoryDetail'>;
type InventoryDetailScreenNavigationProp = NativeStackNavigationProp<MainStackParamList>;

const InventoryDetailScreen: React.FC = () => {
  const route = useRoute<InventoryDetailScreenRouteProp>();
  const navigation = useNavigation<InventoryDetailScreenNavigationProp>();
  const { inventoryId } = route.params;
  
  const [inventory, setInventory] = useState<typeof mockInventoryData[0] | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'details' | 'purchases' | 'usage'>('details');
  
  // Fetch inventory data
  useEffect(() => {
    const fetchInventory = async () => {
      setIsLoading(true);
      // Simulate API call
      setTimeout(() => {
        const foundInventory = mockInventoryData.find(item => item.id === inventoryId);
        if (foundInventory) {
          setInventory(foundInventory);
        }
        setIsLoading(false);
      }, 1000);
    };
    
    fetchInventory();
  }, [inventoryId]);
  
  // Handle edit inventory
  const handleEditInventory = () => {
    Alert.alert('Edit Inventory', 'This feature will be implemented soon.');
    // In a real app, navigate to an edit inventory form
    // navigation.navigate('EditInventory', { inventoryId });
  };
  
  // Handle add stock
  const handleAddStock = () => {
    Alert.alert('Add Stock', 'This feature will be implemented soon.');
    // In a real app, navigate to an add stock form
    // navigation.navigate('AddStock', { inventoryId });
  };
  
  // Handle use stock
  const handleUseStock = () => {
    Alert.alert('Use Stock', 'This feature will be implemented soon.');
    // In a real app, navigate to a use stock form
    // navigation.navigate('UseStock', { inventoryId });
  };
  
  // Get stock status color
  const getStockStatusColor = (currentStock: number, reorderPoint: number, optimalStock: number) => {
    if (currentStock <= reorderPoint) {
      return '#F44336'; // Red - Low stock
    } else if (currentStock < optimalStock) {
      return '#FF9800'; // Orange - Below optimal
    } else {
      return '#4CAF50'; // Green - Good stock
    }
  };
  
  // Render stock status
  const renderStockStatus = (currentStock: number, reorderPoint: number, optimalStock: number) => {
    let status = 'Good Stock';
    let color = '#4CAF50';
    
    if (currentStock <= reorderPoint) {
      status = 'Low Stock';
      color = '#F44336';
    } else if (currentStock < optimalStock) {
      status = 'Below Optimal';
      color = '#FF9800';
    }
    
    return (
      <View style={[styles.stockStatusBadge, { backgroundColor: color }]}>
        <Text style={styles.stockStatusText}>{status}</Text>
      </View>
    );
  };
  
  // Render details tab
  const renderDetailsTab = () => {
    if (!inventory) return null;
    
    const stockStatusColor = getStockStatusColor(
      inventory.currentStock,
      inventory.reorderPoint,
      inventory.optimalStock
    );
    
    return (
      <View style={styles.tabContent}>
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Inventory Information</Text>
          
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Category:</Text>
            <Text style={styles.detailValue}>{inventory.category}</Text>
          </View>
          
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>SKU:</Text>
            <Text style={styles.detailValue}>{inventory.sku}</Text>
          </View>
          
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Unit:</Text>
            <Text style={styles.detailValue}>{inventory.unit}</Text>
          </View>
          
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Unit Weight:</Text>
            <Text style={styles.detailValue}>{inventory.unitWeight}</Text>
          </View>
          
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Location:</Text>
            <Text style={styles.detailValue}>{inventory.location}</Text>
          </View>
        </View>
        
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Stock Information</Text>
          
          <View style={styles.stockContainer}>
            <View style={styles.stockItem}>
              <Text style={styles.stockValue}>{inventory.currentStock}</Text>
              <Text style={styles.stockLabel}>Current</Text>
            </View>
            
            <View style={styles.stockItem}>
              <Text style={[styles.stockValue, { color: '#F44336' }]}>{inventory.reorderPoint}</Text>
              <Text style={styles.stockLabel}>Reorder Point</Text>
            </View>
            
            <View style={styles.stockItem}>
              <Text style={[styles.stockValue, { color: '#4CAF50' }]}>{inventory.optimalStock}</Text>
              <Text style={styles.stockLabel}>Optimal</Text>
            </View>
          </View>
          
          <View style={styles.stockBarContainer}>
            <View style={styles.stockBarBackground}>
              <View 
                style={[
                  styles.stockBarFill, 
                  { 
                    width: `${Math.min(100, (inventory.currentStock / inventory.optimalStock) * 100)}%`,
                    backgroundColor: stockStatusColor
                  }
                ]} 
              />
              <View 
                style={[
                  styles.reorderLine, 
                  { left: `${(inventory.reorderPoint / inventory.optimalStock) * 100}%` }
                ]} 
              />
            </View>
          </View>
          
          <View style={styles.stockButtonsContainer}>
            <TouchableOpacity 
              style={[styles.stockButton, styles.addStockButton]}
              onPress={handleAddStock}
            >
              <Ionicons name="add-circle-outline" size={16} color="#fff" />
              <Text style={styles.stockButtonText}>Add Stock</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={[styles.stockButton, styles.useStockButton]}
              onPress={handleUseStock}
            >
              <Ionicons name="remove-circle-outline" size={16} color="#fff" />
              <Text style={styles.stockButtonText}>Use Stock</Text>
            </TouchableOpacity>
          </View>
        </View>
        
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Supplier Information</Text>
          
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Name:</Text>
            <Text style={styles.detailValue}>{inventory.supplier.name}</Text>
          </View>
          
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Contact:</Text>
            <Text style={styles.detailValue}>{inventory.supplier.contact}</Text>
          </View>
          
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Email:</Text>
            <Text style={styles.detailValue}>{inventory.supplier.email}</Text>
          </View>
          
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Phone:</Text>
            <Text style={styles.detailValue}>{inventory.supplier.phone}</Text>
          </View>
        </View>
        
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Last Purchase</Text>
          
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Date:</Text>
            <Text style={styles.detailValue}>{inventory.lastPurchase.date}</Text>
          </View>
          
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Quantity:</Text>
            <Text style={styles.detailValue}>{inventory.lastPurchase.quantity} {inventory.unit}s</Text>
          </View>
          
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Price:</Text>
            <Text style={styles.detailValue}>${inventory.lastPurchase.price.toFixed(2)} per {inventory.unit}</Text>
          </View>
          
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Total:</Text>
            <Text style={styles.detailValue}>${inventory.lastPurchase.total.toLocaleString()}</Text>
          </View>
        </View>
        
        {inventory.description && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Description</Text>
            <Text style={styles.descriptionText}>{inventory.description}</Text>
          </View>
        )}
        
        {inventory.notes && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Notes</Text>
            <Text style={styles.notesText}>{inventory.notes}</Text>
          </View>
        )}
        
        {inventory.tags && inventory.tags.length > 0 && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Tags</Text>
            <View style={styles.tagsContainer}>
              {inventory.tags.map((tag, index) => (
                <View key={index} style={styles.tag}>
                  <Text style={styles.tagText}>{tag}</Text>
                </View>
              ))}
            </View>
          </View>
        )}
      </View>
    );
  };
  
  // Render purchases tab
  const renderPurchasesTab = () => {
    if (!inventory || !inventory.purchaseHistory.length) {
      return (
        <View style={styles.emptyContainer}>
          <Ionicons name="cart-outline" size={48} color="#ccc" />
          <Text style={styles.emptyText}>No purchase history found</Text>
        </View>
      );
    }
    
    return (
      <View style={styles.tabContent}>
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Purchase History</Text>
          
          {inventory.purchaseHistory.map(purchase => (
            <View key={purchase.id} style={styles.historyItem}>
              <View style={styles.historyHeader}>
                <Text style={styles.historyDate}>{purchase.date}</Text>
                <Text style={styles.historyInvoice}>{purchase.invoice}</Text>
              </View>
              
              <View style={styles.historyDetails}>
                <View style={styles.historyDetailItem}>
                  <Ionicons name="cube-outline" size={16} color="#666" />
                  <Text style={styles.historyDetailText}>
                    {purchase.quantity} {inventory.unit}s
                  </Text>
                </View>
                
                <View style={styles.historyDetailItem}>
                  <Ionicons name="pricetag-outline" size={16} color="#666" />
                  <Text style={styles.historyDetailText}>
                    ${purchase.price.toFixed(2)} per {inventory.unit}
                  </Text>
                </View>
                
                <View style={styles.historyDetailItem}>
                  <Ionicons name="cash-outline" size={16} color="#666" />
                  <Text style={styles.historyDetailText}>
                    Total: ${purchase.total.toLocaleString()}
                  </Text>
                </View>
                
                <View style={styles.historyDetailItem}>
                  <Ionicons name="business-outline" size={16} color="#666" />
                  <Text style={styles.historyDetailText}>
                    Supplier: {purchase.supplier}
                  </Text>
                </View>
              </View>
            </View>
          ))}
        </View>
      </View>
    );
  };
  
  // Render usage tab
  const renderUsageTab = () => {
    if (!inventory || !inventory.usageHistory.length) {
      return (
        <View style={styles.emptyContainer}>
          <Ionicons name="analytics-outline" size={48} color="#ccc" />
          <Text style={styles.emptyText}>No usage history found</Text>
        </View>
      );
    }
    
    return (
      <View style={styles.tabContent}>
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Usage History</Text>
          
          {inventory.usageHistory.map(usage => (
            <View key={usage.id} style={styles.historyItem}>
              <View style={styles.historyHeader}>
                <Text style={styles.historyDate}>{usage.date}</Text>
                <Text style={styles.historyQuantity}>
                  {usage.quantity} {inventory.unit}s
                </Text>
              </View>
              
              <View style={styles.historyDetails}>
                <View style={styles.historyDetailItem}>
                  <Ionicons name="map-outline" size={16} color="#666" />
                  <Text style={styles.historyDetailText}>
                    Field: {usage.field}
                  </Text>
                </View>
                
                <View style={styles.historyDetailItem}>
                  <Ionicons name="leaf-outline" size={16} color="#666" />
                  <Text style={styles.historyDetailText}>
                    Crop: {usage.crop}
                  </Text>
                </View>
                
                <View style={styles.historyDetailItem}>
                  <Ionicons name="person-outline" size={16} color="#666" />
                  <Text style={styles.historyDetailText}>
                    Used by: {usage.user}
                  </Text>
                </View>
              </View>
            </View>
          ))}
        </View>
      </View>
    );
  };
  
  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#3b82f6" />
      </View>
    );
  }
  
  if (!inventory) {
    return (
      <View style={styles.errorContainer}>
        <Ionicons name="alert-circle-outline" size={48} color="#666" />
        <Text style={styles.errorText}>Inventory item not found</Text>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Text style={styles.backButtonText}>Go Back</Text>
        </TouchableOpacity>
      </View>
    );
  }
  
  return (
    <View style={styles.container}>
      <ScrollView>
        <View style={styles.header}>
          <View style={styles.headerTop}>
            <Text style={styles.inventoryCategory}>{inventory.category}</Text>
            {renderStockStatus(inventory.currentStock, inventory.reorderPoint, inventory.optimalStock)}
          </View>
          
          <Text style={styles.inventoryName}>{inventory.name}</Text>
          <Text style={styles.inventorySku}>{inventory.sku}</Text>
          
          <View style={styles.stockSummary}>
            <Text style={styles.stockSummaryText}>
              Current Stock: <Text style={styles.stockSummaryValue}>{inventory.currentStock} {inventory.unit}s</Text>
            </Text>
          </View>
        </View>
        
        <View style={styles.tabsContainer}>
          <TouchableOpacity
            style={[styles.tabButton, activeTab === 'details' && styles.activeTabButton]}
            onPress={() => setActiveTab('details')}
          >
            <Text style={[styles.tabButtonText, activeTab === 'details' && styles.activeTabButtonText]}>
              Details
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.tabButton, activeTab === 'purchases' && styles.activeTabButton]}
            onPress={() => setActiveTab('purchases')}
          >
            <Text style={[styles.tabButtonText, activeTab === 'purchases' && styles.activeTabButtonText]}>
              Purchases ({inventory.purchaseHistory.length})
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.tabButton, activeTab === 'usage' && styles.activeTabButton]}
            onPress={() => setActiveTab('usage')}
          >
            <Text style={[styles.tabButtonText, activeTab === 'usage' && styles.activeTabButtonText]}>
              Usage ({inventory.usageHistory.length})
            </Text>
          </TouchableOpacity>
        </View>
        
        {activeTab === 'details' && renderDetailsTab()}
        {activeTab === 'purchases' && renderPurchasesTab()}
        {activeTab === 'usage' && renderUsageTab()}
      </ScrollView>
      
      <TouchableOpacity style={styles.editButton} onPress={handleEditInventory}>
        <Ionicons name="create" size={24} color="#fff" />
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  errorText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
  backButton: {
    marginTop: 16,
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: '#3b82f6',
    borderRadius: 4,
  },
  backButtonText: {
    color: '#fff',
    fontWeight: '500',
  },
  header: {
    backgroundColor: '#fff',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  headerTop: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  inventoryCategory: {
    fontSize: 14,
    color: '#666',
    fontWeight: '500',
  },
  stockStatusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  stockStatusText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  inventoryName: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  inventorySku: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
  },
  stockSummary: {
    marginTop: 8,
  },
  stockSummaryText: {
    fontSize: 16,
    color: '#333',
  },
  stockSummaryValue: {
    fontWeight: 'bold',
    color: '#3b82f6',
  },
  tabsContainer: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  tabButton: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
  },
  activeTabButton: {
    borderBottomWidth: 2,
    borderBottomColor: '#3b82f6',
  },
  tabButtonText: {
    color: '#666',
    fontWeight: '500',
  },
  activeTabButtonText: {
    color: '#3b82f6',
    fontWeight: 'bold',
  },
  tabContent: {
    padding: 16,
  },
  section: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 1,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 12,
    color: '#333',
  },
  detailRow: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  detailLabel: {
    width: 120,
    fontSize: 14,
    color: '#666',
  },
  detailValue: {
    flex: 1,
    fontSize: 14,
    color: '#333',
  },
  stockContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  stockItem: {
    alignItems: 'center',
  },
  stockValue: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#3b82f6',
    marginBottom: 4,
  },
  stockLabel: {
    fontSize: 12,
    color: '#666',
  },
  stockBarContainer: {
    marginBottom: 16,
  },
  stockBarBackground: {
    height: 12,
    backgroundColor: '#e0e0e0',
    borderRadius: 6,
    position: 'relative',
  },
  stockBarFill: {
    height: '100%',
    borderRadius: 6,
  },
  reorderLine: {
    position: 'absolute',
    width: 2,
    height: '140%',
    backgroundColor: '#F44336',
    top: '-20%',
  },
  stockButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  stockButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 4,
    flex: 1,
    marginHorizontal: 4,
  },
  addStockButton: {
    backgroundColor: '#4CAF50',
  },
  useStockButton: {
    backgroundColor: '#FF9800',
  },
  stockButtonText: {
    color: '#fff',
    fontWeight: '500',
    marginLeft: 4,
  },
  descriptionText: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
  notesText: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  tag: {
    backgroundColor: '#e0e0e0',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 16,
    marginRight: 8,
    marginBottom: 8,
  },
  tagText: {
    fontSize: 12,
    color: '#666',
  },
  historyItem: {
    backgroundColor: '#f9f9f9',
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
  },
  historyHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  historyDate: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
  },
  historyInvoice: {
    fontSize: 14,
    color: '#3b82f6',
  },
  historyQuantity: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#3b82f6',
  },
  historyDetails: {
    marginTop: 4,
  },
  historyDetailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  historyDetailText: {
    fontSize: 14,
    color: '#666',
    marginLeft: 8,
  },
  emptyContainer: {
    padding: 32,
    alignItems: 'center',
    justifyContent: 'center',
  },
  emptyText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
  editButton: {
    position: 'absolute',
    bottom: 24,
    right: 24,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: '#3b82f6',
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
});

export default InventoryDetailScreen;