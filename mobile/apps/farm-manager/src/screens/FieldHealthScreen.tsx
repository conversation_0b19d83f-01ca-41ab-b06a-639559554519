import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  Image,
  Dimensions
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { MainStackParamList } from '../navigation/MainNavigator';

// Get screen dimensions for responsive layouts
const { width } = Dimensions.get('window');

// Mock data for field health
const mockFieldHealthData = {
  lastUpdated: '2023-06-20 09:30 AM',
  fields: [
    {
      id: '1',
      name: 'North Field',
      crop: 'Corn',
      acres: 120,
      plantDate: '2023-04-10',
      harvestDate: '2023-09-15',
      healthIndex: 87,
      ndviScore: 0.82,
      soilMoisture: 76,
      issues: [
        {
          id: 'i1',
          type: 'Nutrient Deficiency',
          severity: 'Low',
          location: 'Northeast corner',
          detectedDate: '2023-06-15',
          status: 'Monitoring',
          description: 'Slight nitrogen deficiency detected in small area.'
        }
      ],
      healthHistory: [
        { date: '2023-06-20', ndvi: 0.82, moisture: 76 },
        { date: '2023-06-13', ndvi: 0.79, moisture: 72 },
        { date: '2023-06-06', ndvi: 0.75, moisture: 68 },
        { date: '2023-05-30', ndvi: 0.72, moisture: 65 },
        { date: '2023-05-23', ndvi: 0.68, moisture: 70 },
        { date: '2023-05-16', ndvi: 0.65, moisture: 73 }
      ],
      image: 'https://example.com/north_field_ndvi.jpg'
    },
    {
      id: '2',
      name: 'South Meadow',
      crop: 'Soybeans',
      acres: 85,
      plantDate: '2023-05-05',
      harvestDate: '2023-10-10',
      healthIndex: 92,
      ndviScore: 0.88,
      soilMoisture: 82,
      issues: [],
      healthHistory: [
        { date: '2023-06-20', ndvi: 0.88, moisture: 82 },
        { date: '2023-06-13', ndvi: 0.85, moisture: 79 },
        { date: '2023-06-06', ndvi: 0.82, moisture: 75 },
        { date: '2023-05-30', ndvi: 0.78, moisture: 72 },
        { date: '2023-05-23', ndvi: 0.74, moisture: 76 },
        { date: '2023-05-16', ndvi: 0.70, moisture: 78 }
      ],
      image: 'https://example.com/south_meadow_ndvi.jpg'
    },
    {
      id: '3',
      name: 'East Ridge',
      crop: 'Wheat',
      acres: 65,
      plantDate: '2023-03-15',
      harvestDate: '2023-07-20',
      healthIndex: 75,
      ndviScore: 0.71,
      soilMoisture: 62,
      issues: [
        {
          id: 'i1',
          type: 'Pest Infestation',
          severity: 'Medium',
          location: 'Southern portion',
          detectedDate: '2023-06-10',
          status: 'Treatment Scheduled',
          description: 'Aphid infestation detected in approximately 15 acres.'
        },
        {
          id: 'i2',
          type: 'Water Stress',
          severity: 'Medium',
          location: 'Western edge',
          detectedDate: '2023-06-12',
          status: 'Irrigation Scheduled',
          description: 'Drought stress detected due to irrigation system malfunction.'
        }
      ],
      healthHistory: [
        { date: '2023-06-20', ndvi: 0.71, moisture: 62 },
        { date: '2023-06-13', ndvi: 0.73, moisture: 65 },
        { date: '2023-06-06', ndvi: 0.76, moisture: 68 },
        { date: '2023-05-30', ndvi: 0.79, moisture: 72 },
        { date: '2023-05-23', ndvi: 0.81, moisture: 75 },
        { date: '2023-05-16', ndvi: 0.83, moisture: 78 }
      ],
      image: 'https://example.com/east_ridge_ndvi.jpg'
    }
  ]
};

type FieldHealthScreenNavigationProp = NativeStackNavigationProp<MainStackParamList>;

// Simple line chart component for health history
const LineChart = ({ data, height = 150, width: chartWidth = width - 64, yKey = 'ndvi', color = '#3b82f6' }) => {
  if (!data || data.length === 0) return null;
  
  const values = data.map(item => item[yKey]);
  const maxValue = Math.max(...values);
  const minValue = Math.min(...values);
  const range = maxValue - minValue;
  
  // Padding to avoid drawing at the very edges
  const paddingHorizontal = 20;
  const paddingVertical = 20;
  const availableWidth = chartWidth - (paddingHorizontal * 2);
  const availableHeight = height - (paddingVertical * 2);
  
  // Calculate points
  const points = data.map((item, index) => {
    const x = paddingHorizontal + (index / (data.length - 1)) * availableWidth;
    const normalizedValue = (item[yKey] - minValue) / (range || 1);
    const y = height - (paddingVertical + normalizedValue * availableHeight);
    return { x, y };
  });
  
  // Create path
  const path = points.reduce((acc, point, index) => {
    if (index === 0) {
      return `M ${point.x},${point.y}`;
    }
    return `${acc} L ${point.x},${point.y}`;
  }, '');
  
  return (
    <View style={{ height, width: chartWidth }}>
      <View style={styles.chartContainer}>
        {/* Y-axis labels */}
        <View style={styles.yAxisLabels}>
          <Text style={styles.axisLabel}>{maxValue.toFixed(2)}</Text>
          <Text style={styles.axisLabel}>{((maxValue + minValue) / 2).toFixed(2)}</Text>
          <Text style={styles.axisLabel}>{minValue.toFixed(2)}</Text>
        </View>
        
        {/* Chart area */}
        <View style={styles.chartArea}>
          {/* Grid lines */}
          <View style={[styles.gridLine, { top: paddingVertical }]} />
          <View style={[styles.gridLine, { top: height / 2 }]} />
          <View style={[styles.gridLine, { top: height - paddingVertical }]} />
          
          {/* SVG-like path using View with absolute positioning */}
          {points.map((point, index) => {
            if (index === 0) return null;
            const prevPoint = points[index - 1];
            
            // Calculate line length and angle
            const dx = point.x - prevPoint.x;
            const dy = point.y - prevPoint.y;
            const length = Math.sqrt(dx * dx + dy * dy);
            const angle = Math.atan2(dy, dx) * 180 / Math.PI;
            
            return (
              <View
                key={index}
                style={{
                  position: 'absolute',
                  width: length,
                  height: 2,
                  backgroundColor: color,
                  left: prevPoint.x,
                  top: prevPoint.y,
                  transform: [
                    { translateY: -1 },
                    { rotate: `${angle}deg` },
                    { translateY: 1 }
                  ],
                  transformOrigin: 'left center',
                }}
              />
            );
          })}
          
          {/* Data points */}
          {points.map((point, index) => (
            <View
              key={`point-${index}`}
              style={{
                position: 'absolute',
                width: 8,
                height: 8,
                borderRadius: 4,
                backgroundColor: color,
                left: point.x - 4,
                top: point.y - 4,
              }}
            />
          ))}
        </View>
      </View>
      
      {/* X-axis labels */}
      <View style={styles.xAxisLabels}>
        {data.map((item, index) => {
          // Only show some labels to avoid crowding
          if (index % 2 !== 0 && index !== data.length - 1) return null;
          
          const x = paddingHorizontal + (index / (data.length - 1)) * availableWidth;
          
          return (
            <Text
              key={index}
              style={[styles.axisLabel, { position: 'absolute', left: x - 20, width: 40, textAlign: 'center' }]}
            >
              {item.date.split('-')[2]}
            </Text>
          );
        })}
      </View>
    </View>
  );
};

// Health indicator component
const HealthIndicator = ({ value, label, color, icon }) => {
  // Determine color based on value
  let indicatorColor = color;
  if (!color) {
    if (value >= 80) {
      indicatorColor = '#4CAF50'; // Green
    } else if (value >= 60) {
      indicatorColor = '#FF9800'; // Orange
    } else {
      indicatorColor = '#F44336'; // Red
    }
  }
  
  return (
    <View style={styles.healthIndicator}>
      <View style={[styles.indicatorCircle, { borderColor: indicatorColor }]}>
        <Text style={[styles.indicatorValue, { color: indicatorColor }]}>{value}</Text>
      </View>
      <View style={styles.indicatorLabelContainer}>
        <Ionicons name={icon} size={16} color="#666" style={styles.indicatorIcon} />
        <Text style={styles.indicatorLabel}>{label}</Text>
      </View>
    </View>
  );
};

// Field health card component
const FieldHealthCard = ({ field, onPress }) => {
  // Determine health status color
  let healthColor;
  if (field.healthIndex >= 80) {
    healthColor = '#4CAF50'; // Green
  } else if (field.healthIndex >= 60) {
    healthColor = '#FF9800'; // Orange
  } else {
    healthColor = '#F44336'; // Red
  }
  
  return (
    <TouchableOpacity style={styles.fieldCard} onPress={onPress}>
      <View style={styles.fieldCardHeader}>
        <Text style={styles.fieldName}>{field.name}</Text>
        <View style={[styles.healthBadge, { backgroundColor: healthColor }]}>
          <Text style={styles.healthBadgeText}>{field.healthIndex}%</Text>
        </View>
      </View>
      
      <View style={styles.fieldCardContent}>
        <View style={styles.fieldInfo}>
          <View style={styles.fieldInfoRow}>
            <Ionicons name="leaf-outline" size={16} color="#666" />
            <Text style={styles.fieldInfoText}>Crop: {field.crop}</Text>
          </View>
          
          <View style={styles.fieldInfoRow}>
            <Ionicons name="resize-outline" size={16} color="#666" />
            <Text style={styles.fieldInfoText}>Size: {field.acres} acres</Text>
          </View>
          
          <View style={styles.fieldInfoRow}>
            <Ionicons name="water-outline" size={16} color="#666" />
            <Text style={styles.fieldInfoText}>Moisture: {field.soilMoisture}%</Text>
          </View>
        </View>
        
        <View style={styles.fieldImageContainer}>
          <Image
            source={{ uri: 'https://via.placeholder.com/100' }} // Placeholder since the mock URLs aren't real
            style={styles.fieldImage}
            resizeMode="cover"
          />
        </View>
      </View>
      
      {field.issues.length > 0 && (
        <View style={styles.issuesBadge}>
          <Ionicons name="alert-circle" size={16} color="#fff" />
          <Text style={styles.issuesBadgeText}>{field.issues.length} {field.issues.length === 1 ? 'Issue' : 'Issues'}</Text>
        </View>
      )}
    </TouchableOpacity>
  );
};

const FieldHealthScreen: React.FC = () => {
  const navigation = useNavigation<FieldHealthScreenNavigationProp>();
  const [isLoading, setIsLoading] = useState(true);
  const [fieldHealthData, setFieldHealthData] = useState(mockFieldHealthData);
  const [selectedField, setSelectedField] = useState<typeof mockFieldHealthData.fields[0] | null>(null);
  const [activeTab, setActiveTab] = useState<'overview' | 'ndvi' | 'moisture'>('overview');
  
  // Fetch field health data
  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      // Simulate API call
      setTimeout(() => {
        setFieldHealthData(mockFieldHealthData);
        // Set the first field as selected by default
        if (mockFieldHealthData.fields.length > 0) {
          setSelectedField(mockFieldHealthData.fields[0]);
        }
        setIsLoading(false);
      }, 1000);
    };
    
    fetchData();
  }, []);
  
  // Handle field selection
  const handleFieldSelect = (field) => {
    setSelectedField(field);
  };
  
  // Handle issue press
  const handleIssuePress = (issue) => {
    Alert.alert(
      `${issue.type} - ${issue.severity} Severity`,
      `Location: ${issue.location}\nDetected: ${issue.detectedDate}\nStatus: ${issue.status}\n\n${issue.description}`,
      [{ text: 'OK' }]
    );
  };
  
  // Render field selector
  const renderFieldSelector = () => {
    return (
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.fieldSelectorContainer}
      >
        {fieldHealthData.fields.map(field => (
          <TouchableOpacity
            key={field.id}
            style={[
              styles.fieldSelectorItem,
              selectedField?.id === field.id && styles.fieldSelectorItemActive
            ]}
            onPress={() => handleFieldSelect(field)}
          >
            <Text
              style={[
                styles.fieldSelectorText,
                selectedField?.id === field.id && styles.fieldSelectorTextActive
              ]}
            >
              {field.name}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    );
  };
  
  // Render tab selector
  const renderTabSelector = () => {
    return (
      <View style={styles.tabsContainer}>
        <TouchableOpacity
          style={[styles.tabButton, activeTab === 'overview' && styles.activeTabButton]}
          onPress={() => setActiveTab('overview')}
        >
          <Text style={[styles.tabButtonText, activeTab === 'overview' && styles.activeTabButtonText]}>
            Overview
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.tabButton, activeTab === 'ndvi' && styles.activeTabButton]}
          onPress={() => setActiveTab('ndvi')}
        >
          <Text style={[styles.tabButtonText, activeTab === 'ndvi' && styles.activeTabButtonText]}>
            NDVI
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.tabButton, activeTab === 'moisture' && styles.activeTabButton]}
          onPress={() => setActiveTab('moisture')}
        >
          <Text style={[styles.tabButtonText, activeTab === 'moisture' && styles.activeTabButtonText]}>
            Moisture
          </Text>
        </TouchableOpacity>
      </View>
    );
  };
  
  // Render overview tab
  const renderOverviewTab = () => {
    if (!selectedField) return null;
    
    return (
      <View style={styles.tabContent}>
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Field Information</Text>
          
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Crop:</Text>
            <Text style={styles.detailValue}>{selectedField.crop}</Text>
          </View>
          
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Size:</Text>
            <Text style={styles.detailValue}>{selectedField.acres} acres</Text>
          </View>
          
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Plant Date:</Text>
            <Text style={styles.detailValue}>{selectedField.plantDate}</Text>
          </View>
          
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Harvest Date:</Text>
            <Text style={styles.detailValue}>{selectedField.harvestDate}</Text>
          </View>
        </View>
        
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Health Indicators</Text>
          
          <View style={styles.healthIndicatorsContainer}>
            <HealthIndicator
              value={`${selectedField.healthIndex}%`}
              label="Health Index"
              icon="pulse-outline"
            />
            
            <HealthIndicator
              value={selectedField.ndviScore.toFixed(2)}
              label="NDVI Score"
              color="#3b82f6"
              icon="leaf-outline"
            />
            
            <HealthIndicator
              value={`${selectedField.soilMoisture}%`}
              label="Soil Moisture"
              color="#00BCD4"
              icon="water-outline"
            />
          </View>
        </View>
        
        {selectedField.issues.length > 0 && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Issues Detected</Text>
            
            {selectedField.issues.map(issue => (
              <TouchableOpacity
                key={issue.id}
                style={styles.issueItem}
                onPress={() => handleIssuePress(issue)}
              >
                <View style={styles.issueHeader}>
                  <View style={styles.issueTypeContainer}>
                    <Ionicons
                      name={
                        issue.type.includes('Pest') ? 'bug-outline' :
                        issue.type.includes('Water') ? 'water-outline' :
                        issue.type.includes('Nutrient') ? 'flask-outline' :
                        'alert-circle-outline'
                      }
                      size={16}
                      color="#F44336"
                    />
                    <Text style={styles.issueType}>{issue.type}</Text>
                  </View>
                  
                  <View style={[
                    styles.issueSeverityBadge,
                    {
                      backgroundColor:
                        issue.severity === 'High' ? '#F44336' :
                        issue.severity === 'Medium' ? '#FF9800' :
                        '#FFC107'
                    }
                  ]}>
                    <Text style={styles.issueSeverityText}>{issue.severity}</Text>
                  </View>
                </View>
                
                <Text style={styles.issueLocation}>Location: {issue.location}</Text>
                <Text style={styles.issueStatus}>Status: {issue.status}</Text>
                
                <View style={styles.issueFooter}>
                  <Text style={styles.issueDate}>Detected: {issue.detectedDate}</Text>
                  <Ionicons name="chevron-forward" size={16} color="#ccc" />
                </View>
              </TouchableOpacity>
            ))}
          </View>
        )}
      </View>
    );
  };
  
  // Render NDVI tab
  const renderNdviTab = () => {
    if (!selectedField) return null;
    
    return (
      <View style={styles.tabContent}>
        <View style={styles.section}>
          <View style={styles.chartHeader}>
            <Text style={styles.sectionTitle}>NDVI Trend</Text>
            <Text style={styles.chartSubtitle}>Current: {selectedField.ndviScore.toFixed(2)}</Text>
          </View>
          
          <LineChart
            data={selectedField.healthHistory}
            yKey="ndvi"
            color="#3b82f6"
          />
          
          <View style={styles.chartLegend}>
            <Text style={styles.chartLegendText}>
              NDVI (Normalized Difference Vegetation Index) measures plant health based on how plants reflect light.
              Higher values indicate healthier vegetation.
            </Text>
          </View>
        </View>
        
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>NDVI Map</Text>
          
          <View style={styles.mapContainer}>
            <Image
              source={{ uri: 'https://via.placeholder.com/400x300' }} // Placeholder since the mock URLs aren't real
              style={styles.mapImage}
              resizeMode="cover"
            />
            
            <View style={styles.mapLegend}>
              <View style={styles.mapLegendItem}>
                <View style={[styles.mapLegendColor, { backgroundColor: '#F44336' }]} />
                <Text style={styles.mapLegendText}>Low (0.0-0.3)</Text>
              </View>
              
              <View style={styles.mapLegendItem}>
                <View style={[styles.mapLegendColor, { backgroundColor: '#FF9800' }]} />
                <Text style={styles.mapLegendText}>Medium (0.3-0.6)</Text>
              </View>
              
              <View style={styles.mapLegendItem}>
                <View style={[styles.mapLegendColor, { backgroundColor: '#FFEB3B' }]} />
                <Text style={styles.mapLegendText}>Good (0.6-0.8)</Text>
              </View>
              
              <View style={styles.mapLegendItem}>
                <View style={[styles.mapLegendColor, { backgroundColor: '#4CAF50' }]} />
                <Text style={styles.mapLegendText}>Excellent (0.8-1.0)</Text>
              </View>
            </View>
          </View>
        </View>
      </View>
    );
  };
  
  // Render moisture tab
  const renderMoistureTab = () => {
    if (!selectedField) return null;
    
    return (
      <View style={styles.tabContent}>
        <View style={styles.section}>
          <View style={styles.chartHeader}>
            <Text style={styles.sectionTitle}>Soil Moisture Trend</Text>
            <Text style={styles.chartSubtitle}>Current: {selectedField.soilMoisture}%</Text>
          </View>
          
          <LineChart
            data={selectedField.healthHistory}
            yKey="moisture"
            color="#00BCD4"
          />
          
          <View style={styles.chartLegend}>
            <Text style={styles.chartLegendText}>
              Soil moisture percentage indicates water content in the soil.
              Optimal levels vary by crop and growth stage.
            </Text>
          </View>
        </View>
        
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Moisture Map</Text>
          
          <View style={styles.mapContainer}>
            <Image
              source={{ uri: 'https://via.placeholder.com/400x300' }} // Placeholder since the mock URLs aren't real
              style={styles.mapImage}
              resizeMode="cover"
            />
            
            <View style={styles.mapLegend}>
              <View style={styles.mapLegendItem}>
                <View style={[styles.mapLegendColor, { backgroundColor: '#F44336' }]} />
                <Text style={styles.mapLegendText}>Dry (0-40%)</Text>
              </View>
              
              <View style={styles.mapLegendItem}>
                <View style={[styles.mapLegendColor, { backgroundColor: '#FF9800' }]} />
                <Text style={styles.mapLegendText}>Low (40-60%)</Text>
              </View>
              
              <View style={styles.mapLegendItem}>
                <View style={[styles.mapLegendColor, { backgroundColor: '#2196F3' }]} />
                <Text style={styles.mapLegendText}>Optimal (60-80%)</Text>
              </View>
              
              <View style={styles.mapLegendItem}>
                <View style={[styles.mapLegendColor, { backgroundColor: '#0D47A1' }]} />
                <Text style={styles.mapLegendText}>Wet (80-100%)</Text>
              </View>
            </View>
          </View>
        </View>
      </View>
    );
  };
  
  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#3b82f6" />
      </View>
    );
  }
  
  return (
    <View style={styles.container}>
      <ScrollView>
        <View style={styles.header}>
          <Text style={styles.headerTitle}>Field Health Analytics</Text>
          <Text style={styles.headerSubtitle}>Last updated: {fieldHealthData.lastUpdated}</Text>
        </View>
        
        <View style={styles.fieldsOverviewSection}>
          <Text style={styles.sectionTitle}>Fields Overview</Text>
          
          {fieldHealthData.fields.map(field => (
            <FieldHealthCard
              key={field.id}
              field={field}
              onPress={() => handleFieldSelect(field)}
            />
          ))}
        </View>
        
        {selectedField && (
          <View style={styles.fieldDetailSection}>
            <Text style={styles.fieldDetailTitle}>{selectedField.name} Details</Text>
            
            {renderFieldSelector()}
            {renderTabSelector()}
            
            {activeTab === 'overview' && renderOverviewTab()}
            {activeTab === 'ndvi' && renderNdviTab()}
            {activeTab === 'moisture' && renderMoistureTab()}
          </View>
        )}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    backgroundColor: '#fff',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  headerSubtitle: {
    fontSize: 14,
    color: '#666',
  },
  fieldsOverviewSection: {
    padding: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 12,
    color: '#333',
  },
  fieldCard: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 1,
    position: 'relative',
  },
  fieldCardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  fieldName: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  healthBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  healthBadgeText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  fieldCardContent: {
    flexDirection: 'row',
  },
  fieldInfo: {
    flex: 1,
  },
  fieldInfoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  fieldInfoText: {
    marginLeft: 8,
    color: '#666',
    fontSize: 14,
  },
  fieldImageContainer: {
    width: 80,
    height: 80,
    borderRadius: 4,
    overflow: 'hidden',
  },
  fieldImage: {
    width: '100%',
    height: '100%',
  },
  issuesBadge: {
    position: 'absolute',
    top: 16,
    right: 16,
    backgroundColor: '#F44336',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    flexDirection: 'row',
    alignItems: 'center',
  },
  issuesBadgeText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
    marginLeft: 4,
  },
  fieldDetailSection: {
    padding: 16,
    backgroundColor: '#fff',
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  fieldDetailTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  fieldSelectorContainer: {
    paddingBottom: 12,
  },
  fieldSelectorItem: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 16,
    backgroundColor: '#f0f0f0',
    marginRight: 8,
  },
  fieldSelectorItemActive: {
    backgroundColor: '#3b82f6',
  },
  fieldSelectorText: {
    color: '#666',
    fontWeight: '500',
  },
  fieldSelectorTextActive: {
    color: '#fff',
  },
  tabsContainer: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
    marginBottom: 16,
  },
  tabButton: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
  },
  activeTabButton: {
    borderBottomWidth: 2,
    borderBottomColor: '#3b82f6',
  },
  tabButtonText: {
    color: '#666',
    fontWeight: '500',
  },
  activeTabButtonText: {
    color: '#3b82f6',
    fontWeight: 'bold',
  },
  tabContent: {
    paddingBottom: 16,
  },
  section: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 1,
  },
  detailRow: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  detailLabel: {
    width: 100,
    fontSize: 14,
    color: '#666',
  },
  detailValue: {
    flex: 1,
    fontSize: 14,
    color: '#333',
  },
  healthIndicatorsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  healthIndicator: {
    alignItems: 'center',
  },
  indicatorCircle: {
    width: 64,
    height: 64,
    borderRadius: 32,
    borderWidth: 3,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  indicatorValue: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  indicatorLabelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  indicatorIcon: {
    marginRight: 4,
  },
  indicatorLabel: {
    fontSize: 12,
    color: '#666',
  },
  issueItem: {
    backgroundColor: '#f9f9f9',
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
  },
  issueHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  issueTypeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  issueType: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
    marginLeft: 8,
  },
  issueSeverityBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  issueSeverityText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  issueLocation: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  issueStatus: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
  },
  issueFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  issueDate: {
    fontSize: 12,
    color: '#999',
  },
  chartHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  chartSubtitle: {
    fontSize: 14,
    color: '#3b82f6',
    fontWeight: 'bold',
  },
  chartContainer: {
    flexDirection: 'row',
    height: 150,
  },
  yAxisLabels: {
    width: 40,
    height: '100%',
    justifyContent: 'space-between',
    alignItems: 'flex-end',
    paddingRight: 4,
    paddingVertical: 20,
  },
  chartArea: {
    flex: 1,
    height: '100%',
    position: 'relative',
  },
  gridLine: {
    position: 'absolute',
    left: 0,
    right: 0,
    height: 1,
    backgroundColor: '#e0e0e0',
  },
  xAxisLabels: {
    height: 20,
    position: 'relative',
  },
  axisLabel: {
    fontSize: 10,
    color: '#999',
  },
  chartLegend: {
    marginTop: 16,
    padding: 8,
    backgroundColor: '#f0f7ff',
    borderRadius: 4,
  },
  chartLegendText: {
    fontSize: 12,
    color: '#666',
    lineHeight: 16,
  },
  mapContainer: {
    borderRadius: 8,
    overflow: 'hidden',
    marginBottom: 16,
  },
  mapImage: {
    width: '100%',
    height: 200,
  },
  mapLegend: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    padding: 8,
    backgroundColor: '#f9f9f9',
  },
  mapLegendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 16,
    marginBottom: 4,
  },
  mapLegendColor: {
    width: 12,
    height: 12,
    borderRadius: 2,
    marginRight: 4,
  },
  mapLegendText: {
    fontSize: 10,
    color: '#666',
  },
});

export default FieldHealthScreen;