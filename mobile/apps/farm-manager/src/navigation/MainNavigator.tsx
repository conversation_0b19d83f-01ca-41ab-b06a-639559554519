import React from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { Ionicons } from '@expo/vector-icons';
import DashboardScreen from '../screens/DashboardScreen';
import TasksScreen from '../screens/TasksScreen';
import EmployeesScreen from '../screens/EmployeesScreen';
import FinancesScreen from '../screens/FinancesScreen';
import InventoryScreen from '../screens/InventoryScreen';
import EquipmentScreen from '../screens/EquipmentScreen';
import AnalyticsScreen from '../screens/AnalyticsScreen';
import ProfileScreen from '../screens/ProfileScreen';
import TaskDetailScreen from '../screens/TaskDetailScreen';
import EmployeeDetailScreen from '../screens/EmployeeDetailScreen';
import FinancialDetailScreen from '../screens/FinancialDetailScreen';
import InventoryDetailScreen from '../screens/InventoryDetailScreen';
import EquipmentDetailScreen from '../screens/EquipmentDetailScreen';
import FieldHealthScreen from '../screens/FieldHealthScreen';

export type MainStackParamList = {
  MainTabs: undefined;
  TaskDetail: { taskId: string };
  EmployeeDetail: { employeeId: string };
  FinancialDetail: { financialId: string };
  InventoryDetail: { inventoryId: string };
  EquipmentDetail: { equipmentId: string };
  FieldHealth: undefined;
};

export type MainTabParamList = {
  Dashboard: undefined;
  Tasks: undefined;
  Employees: undefined;
  Finances: undefined;
  Inventory: undefined;
  Equipment: undefined;
  Analytics: undefined;
  Profile: undefined;
};

const Stack = createNativeStackNavigator<MainStackParamList>();
const Tab = createBottomTabNavigator<MainTabParamList>();

const MainTabs: React.FC = () => {
  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName: keyof typeof Ionicons.glyphMap = 'home';

          if (route.name === 'Dashboard') {
            iconName = focused ? 'speedometer' : 'speedometer-outline';
          } else if (route.name === 'Tasks') {
            iconName = focused ? 'list' : 'list-outline';
          } else if (route.name === 'Employees') {
            iconName = focused ? 'people' : 'people-outline';
          } else if (route.name === 'Finances') {
            iconName = focused ? 'cash' : 'cash-outline';
          } else if (route.name === 'Inventory') {
            iconName = focused ? 'cube' : 'cube-outline';
          } else if (route.name === 'Equipment') {
            iconName = focused ? 'construct' : 'construct-outline';
          } else if (route.name === 'Analytics') {
            iconName = focused ? 'bar-chart' : 'bar-chart-outline';
          } else if (route.name === 'Profile') {
            iconName = focused ? 'person' : 'person-outline';
          }

          return <Ionicons name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: '#3b82f6',
        tabBarInactiveTintColor: 'gray',
        headerShown: true,
        headerStyle: {
          backgroundColor: '#3b82f6',
        },
        headerTintColor: '#fff',
        headerTitleStyle: {
          fontWeight: 'bold',
        },
      })}
    >
      <Tab.Screen name="Dashboard" component={DashboardScreen} options={{ title: 'Dashboard' }} />
      <Tab.Screen name="Tasks" component={TasksScreen} options={{ title: 'Tasks' }} />
      <Tab.Screen name="Employees" component={EmployeesScreen} options={{ title: 'Employees' }} />
      <Tab.Screen name="Finances" component={FinancesScreen} options={{ title: 'Finances' }} />
      <Tab.Screen name="Inventory" component={InventoryScreen} options={{ title: 'Inventory' }} />
      <Tab.Screen name="Equipment" component={EquipmentScreen} options={{ title: 'Equipment' }} />
      <Tab.Screen name="Analytics" component={AnalyticsScreen} options={{ title: 'Analytics' }} />
      <Tab.Screen name="Profile" component={ProfileScreen} options={{ title: 'Profile' }} />
    </Tab.Navigator>
  );
};

const MainNavigator: React.FC = () => {
  return (
    <Stack.Navigator
      initialRouteName="MainTabs"
      screenOptions={{
        headerShown: false,
      }}
    >
      <Stack.Screen name="MainTabs" component={MainTabs} />
      <Stack.Screen 
        name="TaskDetail" 
        component={TaskDetailScreen} 
        options={{ 
          headerShown: true,
          headerStyle: { backgroundColor: '#3b82f6' },
          headerTintColor: '#fff',
          title: 'Task Details'
        }} 
      />
      <Stack.Screen 
        name="EmployeeDetail" 
        component={EmployeeDetailScreen} 
        options={{ 
          headerShown: true,
          headerStyle: { backgroundColor: '#3b82f6' },
          headerTintColor: '#fff',
          title: 'Employee Details'
        }} 
      />
      <Stack.Screen 
        name="FinancialDetail" 
        component={FinancialDetailScreen} 
        options={{ 
          headerShown: true,
          headerStyle: { backgroundColor: '#3b82f6' },
          headerTintColor: '#fff',
          title: 'Financial Details'
        }} 
      />
      <Stack.Screen 
        name="InventoryDetail" 
        component={InventoryDetailScreen} 
        options={{ 
          headerShown: true,
          headerStyle: { backgroundColor: '#3b82f6' },
          headerTintColor: '#fff',
          title: 'Inventory Details'
        }} 
      />
      <Stack.Screen 
        name="EquipmentDetail" 
        component={EquipmentDetailScreen} 
        options={{ 
          headerShown: true,
          headerStyle: { backgroundColor: '#3b82f6' },
          headerTintColor: '#fff',
          title: 'Equipment Details'
        }} 
      />
      <Stack.Screen 
        name="FieldHealth" 
        component={FieldHealthScreen} 
        options={{ 
          headerShown: true,
          headerStyle: { backgroundColor: '#3b82f6' },
          headerTintColor: '#fff',
          title: 'Field Health'
        }} 
      />
    </Stack.Navigator>
  );
};

export default MainNavigator;