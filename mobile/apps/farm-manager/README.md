# NxtAcre Farm Manager App

## Overview

The NxtAcre Farm Manager App is a mobile application designed for farm owners and managers to monitor and manage their farm operations. It provides a comprehensive dashboard with farm performance metrics, financial overview, inventory and equipment status, and recent activity.

## Features

- **Dashboard**: View key farm metrics, financial data, inventory status, and recent activity
- **Task Management**: Create, assign, and monitor tasks (planned)
- **Employee Management**: Manage farm workers and their schedules (planned)
- **Financial Overview**: Track revenue, expenses, and profit
- **Inventory Monitoring**: Monitor inventory levels and receive low stock alerts
- **Equipment Tracking**: Track equipment status and maintenance needs
- **Field Health Analytics**: Monitor crop health and field conditions (planned)
- **Market Price Tracking**: Track commodity prices and market trends (planned)

## Getting Started

### Prerequisites

- Node.js (v16 or higher)
- npm (v7 or higher)
- Expo CLI

### Installation

1. Clone the repository
2. Navigate to the project directory
   ```
   cd qbooks/mobile
   ```
3. Install dependencies
   ```
   npm install
   ```
4. Start the Expo development server
   ```
   npm start
   ```
5. Select the Farm Manager App from the menu or run
   ```
   npm run farm-manager
   ```

## Project Structure

```
farm-manager/
├── assets/             # Images, fonts, and other static assets
├── src/                # Source code
│   ├── components/     # Reusable UI components
│   ├── navigation/     # Navigation configuration
│   │   ├── AppNavigator.tsx     # Main app navigator
│   │   ├── AuthNavigator.tsx    # Authentication navigator
│   │   └── MainNavigator.tsx    # Main content navigator
│   └── screens/        # Screen components
│       └── DashboardScreen.tsx  # Main dashboard screen
├── App.tsx             # Main app component
├── app.json            # Expo configuration
├── DEVELOPMENT_SUMMARY.md  # Development progress summary
└── README.md           # This file
```

## Development Status

The app is currently in the early stages of development. The basic infrastructure and dashboard screen have been implemented, but most of the detailed screens and features are still planned.

For more details on the development status and next steps, see the [DEVELOPMENT_SUMMARY.md](./DEVELOPMENT_SUMMARY.md) file.

## Contributing

1. Follow the project's coding style and patterns
2. Use TypeScript for type safety
3. Use functional components with hooks for React components
4. Keep components modular and reusable
5. Use the shared code infrastructure for common functionality

## Related Apps

The NxtAcre Farm Manager App is part of the NxtAcre Farm Management Platform, which includes several other mobile applications:

- **Field Operations App**: For field operators and workers to manage field operations
- **Employee App**: For farm employees to track time, view tasks, and communicate
- **Inventory & Equipment App**: For inventory and equipment managers
- **Financial Manager App**: For financial managers and accountants
- **Marketplace App**: For buying and selling farm products
- **Driver App**: For delivery drivers and transport staff
- **Drive Tracker App**: For tracking business travel for tax purposes

## License

This project is proprietary and confidential. Unauthorized copying, distribution, or use is strictly prohibited.