import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, FlatList, TouchableOpacity, TextInput, ActivityIndicator } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { InvoicesScreenNavigationProp, Invoice } from '../types/navigation';

// Mock data for initial development
const mockInvoices: Invoice[] = [
  { 
    id: '1', 
    invoiceNumber: 'INV-2023-001', 
    amount: 5000, 
    description: 'Crop sale', 
    customer: 'Green Valley Farms',
    issueDate: '2023-07-28',
    dueDate: '2023-08-28',
    status: 'sent',
    createdBy: '<PERSON>',
    createdAt: '2023-07-28T10:30:00Z',
    updatedAt: '2023-07-28T14:45:00Z'
  },
  { 
    id: '2', 
    invoiceNumber: 'INV-2023-002', 
    amount: 3500, 
    description: 'Equipment rental', 
    customer: 'Smith Farms',
    issueDate: '2023-07-22',
    dueDate: '2023-08-22',
    status: 'paid',
    createdBy: '<PERSON>',
    createdAt: '2023-07-22T09:15:00Z',
    updatedAt: '2023-07-25T11:20:00Z'
  },
  { 
    id: '3', 
    invoiceNumber: 'INV-2023-003', 
    amount: 12000, 
    description: 'Grain sale', 
    customer: 'Midwest Grain Co.',
    issueDate: '2023-07-15',
    dueDate: '2023-08-15',
    status: 'overdue',
    createdBy: 'John Doe',
    createdAt: '2023-07-15T08:00:00Z',
    updatedAt: '2023-07-15T10:10:00Z'
  },
  { 
    id: '4', 
    invoiceNumber: 'INV-2023-004', 
    amount: 1500, 
    description: 'Consulting services', 
    customer: 'Johnson Family Farm',
    issueDate: '2023-07-10',
    dueDate: '2023-08-10',
    status: 'paid',
    createdBy: 'Jane Smith',
    createdAt: '2023-07-10T14:30:00Z',
    updatedAt: '2023-07-20T16:45:00Z'
  },
  { 
    id: '5', 
    invoiceNumber: 'INV-2023-005', 
    amount: 8500, 
    description: 'Livestock sale', 
    customer: 'County Livestock Auction',
    issueDate: '2023-07-05',
    dueDate: '2023-08-05',
    status: 'overdue',
    createdBy: 'John Doe',
    createdAt: '2023-07-05T11:20:00Z',
    updatedAt: '2023-07-05T15:30:00Z'
  },
  { 
    id: '6', 
    invoiceNumber: 'INV-2023-006', 
    amount: 2200, 
    description: 'Farm tour services', 
    customer: 'Springfield Elementary School',
    issueDate: '2023-07-01',
    dueDate: '2023-08-01',
    status: 'sent',
    createdBy: 'Jane Smith',
    createdAt: '2023-07-01T13:15:00Z',
    updatedAt: '2023-07-01T17:45:00Z'
  },
  { 
    id: '7', 
    invoiceNumber: 'INV-2023-007', 
    amount: 4500, 
    description: 'Organic produce delivery', 
    customer: 'Farm to Table Restaurant',
    issueDate: '2023-06-28',
    dueDate: '2023-07-28',
    status: 'draft',
    createdBy: 'John Doe',
    createdAt: '2023-06-28T09:30:00Z',
    updatedAt: '2023-06-28T10:45:00Z'
  }
];

const InvoicesScreen = () => {
  const navigation = useNavigation<InvoicesScreenNavigationProp>();
  const [loading, setLoading] = useState(true);
  const [invoices, setInvoices] = useState<Invoice[]>([]);
  const [filteredInvoices, setFilteredInvoices] = useState<Invoice[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'draft' | 'sent' | 'paid' | 'overdue' | 'cancelled'>('all');

  useEffect(() => {
    // Simulate API call
    const timer = setTimeout(() => {
      setInvoices(mockInvoices);
      setFilteredInvoices(mockInvoices);
      setLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  useEffect(() => {
    // Apply filters when invoices, searchQuery, or statusFilter changes
    let filtered = invoices;
    
    // Apply search filter
    if (searchQuery) {
      filtered = filtered.filter(invoice => 
        invoice.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        invoice.customer.toLowerCase().includes(searchQuery.toLowerCase()) ||
        invoice.invoiceNumber.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }
    
    // Apply status filter
    if (statusFilter !== 'all') {
      filtered = filtered.filter(invoice => invoice.status === statusFilter);
    }
    
    setFilteredInvoices(filtered);
  }, [invoices, searchQuery, statusFilter]);

  const formatCurrency = (amount: number) => {
    return `$${amount.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'draft':
        return '#9E9E9E';
      case 'sent':
        return '#2196F3';
      case 'paid':
        return '#4CAF50';
      case 'overdue':
        return '#F44336';
      case 'cancelled':
        return '#FF9800';
      default:
        return '#9E9E9E';
    }
  };

  const renderInvoiceItem = ({ item }: { item: Invoice }) => (
    <TouchableOpacity 
      style={styles.invoiceCard}
      onPress={() => navigation.navigate('InvoiceDetail', { invoiceId: item.id })}
    >
      <View style={styles.invoiceHeader}>
        <View>
          <Text style={styles.invoiceNumber}>{item.invoiceNumber}</Text>
          <Text style={styles.invoiceCustomer}>{item.customer}</Text>
        </View>
        <Text style={styles.invoiceAmount}>{formatCurrency(item.amount)}</Text>
      </View>
      <View style={styles.invoiceDetails}>
        <View style={styles.invoiceDetail}>
          <Ionicons name="document-text-outline" size={16} color="#666" />
          <Text style={styles.invoiceDetailText}>{item.description}</Text>
        </View>
        <View style={styles.invoiceDetail}>
          <Ionicons name="calendar-outline" size={16} color="#666" />
          <Text style={styles.invoiceDetailText}>Due: {formatDate(item.dueDate)}</Text>
        </View>
        <View style={styles.invoiceStatusContainer}>
          <View style={[styles.statusIndicator, { backgroundColor: getStatusColor(item.status) }]} />
          <Text style={styles.invoiceStatus}>
            {item.status.charAt(0).toUpperCase() + item.status.slice(1)}
          </Text>
        </View>
      </View>
    </TouchableOpacity>
  );

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#4CAF50" />
        <Text style={styles.loadingText}>Loading invoices...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Invoices</Text>
        <TouchableOpacity 
          style={styles.addButton}
          onPress={() => navigation.navigate('InvoiceDetail', { invoiceId: 'new' })}
        >
          <Ionicons name="add" size={24} color="white" />
        </TouchableOpacity>
      </View>

      <View style={styles.searchContainer}>
        <Ionicons name="search" size={20} color="#999" style={styles.searchIcon} />
        <TextInput
          style={styles.searchInput}
          placeholder="Search invoices..."
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
      </View>

      <View style={styles.filterContainer}>
        <TouchableOpacity 
          style={[styles.filterButton, statusFilter === 'all' && styles.filterButtonActive]}
          onPress={() => setStatusFilter('all')}
        >
          <Text style={[styles.filterButtonText, statusFilter === 'all' && styles.filterButtonTextActive]}>All</Text>
        </TouchableOpacity>
        <TouchableOpacity 
          style={[styles.filterButton, statusFilter === 'draft' && styles.filterButtonActive]}
          onPress={() => setStatusFilter('draft')}
        >
          <Text style={[styles.filterButtonText, statusFilter === 'draft' && styles.filterButtonTextActive]}>Draft</Text>
        </TouchableOpacity>
        <TouchableOpacity 
          style={[styles.filterButton, statusFilter === 'sent' && styles.filterButtonActive]}
          onPress={() => setStatusFilter('sent')}
        >
          <Text style={[styles.filterButtonText, statusFilter === 'sent' && styles.filterButtonTextActive]}>Sent</Text>
        </TouchableOpacity>
        <TouchableOpacity 
          style={[styles.filterButton, statusFilter === 'paid' && styles.filterButtonActive]}
          onPress={() => setStatusFilter('paid')}
        >
          <Text style={[styles.filterButtonText, statusFilter === 'paid' && styles.filterButtonTextActive]}>Paid</Text>
        </TouchableOpacity>
        <TouchableOpacity 
          style={[styles.filterButton, statusFilter === 'overdue' && styles.filterButtonActive]}
          onPress={() => setStatusFilter('overdue')}
        >
          <Text style={[styles.filterButtonText, statusFilter === 'overdue' && styles.filterButtonTextActive]}>Overdue</Text>
        </TouchableOpacity>
      </View>

      {filteredInvoices.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Ionicons name="document-text-outline" size={64} color="#ccc" />
          <Text style={styles.emptyText}>No invoices found</Text>
          <Text style={styles.emptySubtext}>Try adjusting your filters or create a new invoice</Text>
        </View>
      ) : (
        <FlatList
          data={filteredInvoices}
          renderItem={renderInvoiceItem}
          keyExtractor={item => item.id}
          contentContainerStyle={styles.listContainer}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#4CAF50',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
  },
  addButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    borderRadius: 8,
    margin: 15,
    paddingHorizontal: 15,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  searchIcon: {
    marginRight: 10,
  },
  searchInput: {
    flex: 1,
    height: 50,
    fontSize: 16,
  },
  filterContainer: {
    flexDirection: 'row',
    paddingHorizontal: 15,
    marginBottom: 10,
    flexWrap: 'wrap',
  },
  filterButton: {
    paddingHorizontal: 15,
    paddingVertical: 8,
    marginRight: 10,
    marginBottom: 10,
    borderRadius: 20,
    backgroundColor: '#f0f0f0',
  },
  filterButtonActive: {
    backgroundColor: '#4CAF50',
  },
  filterButtonText: {
    fontSize: 14,
    color: '#666',
  },
  filterButtonTextActive: {
    color: 'white',
    fontWeight: 'bold',
  },
  listContainer: {
    padding: 15,
    paddingTop: 5,
  },
  invoiceCard: {
    backgroundColor: 'white',
    borderRadius: 10,
    padding: 15,
    marginBottom: 10,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  invoiceHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 10,
  },
  invoiceNumber: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  invoiceCustomer: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  invoiceAmount: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#4CAF50',
  },
  invoiceDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    flexWrap: 'wrap',
  },
  invoiceDetail: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 10,
    marginBottom: 5,
  },
  invoiceDetailText: {
    fontSize: 14,
    color: '#666',
    marginLeft: 5,
  },
  invoiceStatusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusIndicator: {
    width: 10,
    height: 10,
    borderRadius: 5,
    marginRight: 5,
  },
  invoiceStatus: {
    fontSize: 14,
    color: '#666',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#666',
    marginTop: 10,
  },
  emptySubtext: {
    fontSize: 14,
    color: '#999',
    textAlign: 'center',
    marginTop: 5,
  },
});

export default InvoicesScreen;