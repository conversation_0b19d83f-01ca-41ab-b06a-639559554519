import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, FlatList, TouchableOpacity, TextInput, ActivityIndicator, ScrollView } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { IncomeScreenNavigationProp, Income } from '../types/navigation';

// Mock data for initial development
const mockIncome: Income[] = [
  { 
    id: '1', 
    amount: 5000, 
    description: 'Crop sale', 
    category: 'Sales', 
    date: '2023-07-28', 
    source: 'Local Market',
    createdBy: '<PERSON>',
    createdAt: '2023-07-28T10:30:00Z',
    updatedAt: '2023-07-28T14:45:00Z'
  },
  { 
    id: '2', 
    amount: 3500, 
    description: 'Equipment rental', 
    category: 'Services', 
    date: '2023-07-22', 
    source: 'Neighbor Farm',
    createdBy: '<PERSON>',
    createdAt: '2023-07-22T09:15:00Z',
    updatedAt: '2023-07-22T11:20:00Z'
  },
  { 
    id: '3', 
    amount: 12000, 
    description: 'Grain sale', 
    category: 'Sales', 
    date: '2023-07-15', 
    source: 'Grain Cooperative',
    createdBy: 'John Doe',
    createdAt: '2023-07-15T08:00:00Z',
    updatedAt: '2023-07-15T10:10:00Z'
  },
  { 
    id: '4', 
    amount: 1500, 
    description: 'Consulting services', 
    category: 'Services', 
    date: '2023-07-10', 
    source: 'Smith Farms',
    createdBy: 'Jane Smith',
    createdAt: '2023-07-10T14:30:00Z',
    updatedAt: '2023-07-10T16:45:00Z'
  },
  { 
    id: '5', 
    amount: 8500, 
    description: 'Livestock sale', 
    category: 'Sales', 
    date: '2023-07-05', 
    source: 'County Auction',
    createdBy: 'John Doe',
    createdAt: '2023-07-05T11:20:00Z',
    updatedAt: '2023-07-05T15:30:00Z'
  },
  { 
    id: '6', 
    amount: 2200, 
    description: 'Farm tour', 
    category: 'Tourism', 
    date: '2023-07-01', 
    source: 'Local School',
    createdBy: 'Jane Smith',
    createdAt: '2023-07-01T13:15:00Z',
    updatedAt: '2023-07-01T17:45:00Z'
  },
  { 
    id: '7', 
    amount: 15000, 
    description: 'Government subsidy', 
    category: 'Subsidies', 
    date: '2023-06-28', 
    source: 'Department of Agriculture',
    createdBy: 'John Doe',
    createdAt: '2023-06-28T09:30:00Z',
    updatedAt: '2023-06-28T10:45:00Z'
  }
];

const IncomeScreen = () => {
  const navigation = useNavigation<IncomeScreenNavigationProp>();
  const [loading, setLoading] = useState(true);
  const [income, setIncome] = useState<Income[]>([]);
  const [filteredIncome, setFilteredIncome] = useState<Income[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [categoryFilter, setCategoryFilter] = useState<string>('all');

  useEffect(() => {
    // Simulate API call
    const timer = setTimeout(() => {
      setIncome(mockIncome);
      setFilteredIncome(mockIncome);
      setLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  useEffect(() => {
    // Apply filters when income, searchQuery, or categoryFilter changes
    let filtered = income;

    // Apply search filter
    if (searchQuery) {
      filtered = filtered.filter(item => 
        item.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.source.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.category.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Apply category filter
    if (categoryFilter !== 'all') {
      filtered = filtered.filter(item => item.category === categoryFilter);
    }

    setFilteredIncome(filtered);
  }, [income, searchQuery, categoryFilter]);

  const formatCurrency = (amount: number) => {
    return `$${amount.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' });
  };

  // Get unique categories for filter
  const categories = ['all', ...Array.from(new Set(income.map(item => item.category)))];

  const renderIncomeItem = ({ item }: { item: Income }) => (
    <TouchableOpacity 
      style={styles.incomeCard}
      onPress={() => navigation.navigate('IncomeDetail', { incomeId: item.id })}
    >
      <View style={styles.incomeHeader}>
        <Text style={styles.incomeDescription}>{item.description}</Text>
        <Text style={styles.incomeAmount}>{formatCurrency(item.amount)}</Text>
      </View>
      <View style={styles.incomeDetails}>
        <View style={styles.incomeDetail}>
          <Ionicons name="calendar-outline" size={16} color="#666" />
          <Text style={styles.incomeDetailText}>{formatDate(item.date)}</Text>
        </View>
        <View style={styles.incomeDetail}>
          <Ionicons name="pricetag-outline" size={16} color="#666" />
          <Text style={styles.incomeDetailText}>{item.category}</Text>
        </View>
        <View style={styles.incomeDetail}>
          <Ionicons name="business-outline" size={16} color="#666" />
          <Text style={styles.incomeDetailText}>{item.source}</Text>
        </View>
      </View>
    </TouchableOpacity>
  );

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#4CAF50" />
        <Text style={styles.loadingText}>Loading income data...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Income</Text>
        <TouchableOpacity 
          style={styles.addButton}
          onPress={() => navigation.navigate('IncomeDetail', { incomeId: 'new' })}
        >
          <Ionicons name="add" size={24} color="white" />
        </TouchableOpacity>
      </View>

      <View style={styles.searchContainer}>
        <Ionicons name="search" size={20} color="#999" style={styles.searchIcon} />
        <TextInput
          style={styles.searchInput}
          placeholder="Search income..."
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
      </View>

      <View style={styles.filterContainer}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          {categories.map((category) => (
            <TouchableOpacity 
              key={category}
              style={[styles.filterButton, categoryFilter === category && styles.filterButtonActive]}
              onPress={() => setCategoryFilter(category)}
            >
              <Text 
                style={[styles.filterButtonText, categoryFilter === category && styles.filterButtonTextActive]}
              >
                {category === 'all' ? 'All' : category}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>

      {filteredIncome.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Ionicons name="cash-outline" size={64} color="#ccc" />
          <Text style={styles.emptyText}>No income entries found</Text>
          <Text style={styles.emptySubtext}>Try adjusting your filters or add a new income entry</Text>
        </View>
      ) : (
        <FlatList
          data={filteredIncome}
          renderItem={renderIncomeItem}
          keyExtractor={item => item.id}
          contentContainerStyle={styles.listContainer}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#4CAF50',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
  },
  addButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    borderRadius: 8,
    margin: 15,
    paddingHorizontal: 15,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  searchIcon: {
    marginRight: 10,
  },
  searchInput: {
    flex: 1,
    height: 50,
    fontSize: 16,
  },
  filterContainer: {
    paddingHorizontal: 15,
    marginBottom: 10,
  },
  filterButton: {
    paddingHorizontal: 15,
    paddingVertical: 8,
    marginRight: 10,
    borderRadius: 20,
    backgroundColor: '#f0f0f0',
  },
  filterButtonActive: {
    backgroundColor: '#4CAF50',
  },
  filterButtonText: {
    fontSize: 14,
    color: '#666',
  },
  filterButtonTextActive: {
    color: 'white',
    fontWeight: 'bold',
  },
  listContainer: {
    padding: 15,
    paddingTop: 5,
  },
  incomeCard: {
    backgroundColor: 'white',
    borderRadius: 10,
    padding: 15,
    marginBottom: 10,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  incomeHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  incomeDescription: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    flex: 1,
  },
  incomeAmount: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#4CAF50',
  },
  incomeDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  incomeDetail: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  incomeDetailText: {
    fontSize: 14,
    color: '#666',
    marginLeft: 5,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#666',
    marginTop: 10,
  },
  emptySubtext: {
    fontSize: 14,
    color: '#999',
    textAlign: 'center',
    marginTop: 5,
  },
});

export default IncomeScreen;
