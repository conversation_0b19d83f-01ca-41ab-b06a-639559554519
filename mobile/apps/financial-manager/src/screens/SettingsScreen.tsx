import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Switch, Alert } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { SettingsScreenNavigationProp } from '../types/navigation';

const SettingsScreen = () => {
  const navigation = useNavigation<SettingsScreenNavigationProp>();
  
  // Settings state
  const [notificationsEnabled, setNotificationsEnabled] = useState(true);
  const [emailNotificationsEnabled, setEmailNotificationsEnabled] = useState(true);
  const [darkModeEnabled, setDarkModeEnabled] = useState(false);
  const [biometricAuthEnabled, setBiometricAuthEnabled] = useState(false);
  const [autoSyncEnabled, setAutoSyncEnabled] = useState(true);
  const [defaultCurrency, setDefaultCurrency] = useState('USD');
  const [syncFrequency, setSyncFrequency] = useState('hourly');
  
  // Mock user data
  const userData = {
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'Farm Manager',
    accountType: 'Premium',
    lastSync: '2023-08-01T14:30:00Z'
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric', 
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const handleLogout = () => {
    Alert.alert(
      'Confirm Logout',
      'Are you sure you want to log out?',
      [
        {
          text: 'Cancel',
          style: 'cancel'
        },
        {
          text: 'Logout',
          style: 'destructive',
          onPress: () => {
            // Implement logout logic here
            console.log('User logged out');
          }
        }
      ]
    );
  };

  const handleSyncNow = () => {
    Alert.alert(
      'Sync Data',
      'Sync all financial data with the server?',
      [
        {
          text: 'Cancel',
          style: 'cancel'
        },
        {
          text: 'Sync',
          onPress: () => {
            // Implement sync logic here
            console.log('Syncing data...');
          }
        }
      ]
    );
  };

  const handleClearCache = () => {
    Alert.alert(
      'Clear Cache',
      'This will clear all cached data. App data will not be lost, but the app may run slower initially. Continue?',
      [
        {
          text: 'Cancel',
          style: 'cancel'
        },
        {
          text: 'Clear',
          style: 'destructive',
          onPress: () => {
            // Implement cache clearing logic here
            console.log('Cache cleared');
          }
        }
      ]
    );
  };

  const renderSettingItem = (
    icon: string, 
    title: string, 
    value?: string | React.ReactNode,
    onPress?: () => void
  ) => (
    <TouchableOpacity 
      style={styles.settingItem}
      onPress={onPress}
      disabled={!onPress}
    >
      <View style={styles.settingIconContainer}>
        <Ionicons name={icon as any} size={24} color="#4CAF50" />
      </View>
      <View style={styles.settingContent}>
        <Text style={styles.settingTitle}>{title}</Text>
        {typeof value === 'string' ? (
          <Text style={styles.settingValue}>{value}</Text>
        ) : (
          value
        )}
      </View>
      {onPress && (
        <Ionicons name="chevron-forward" size={20} color="#ccc" />
      )}
    </TouchableOpacity>
  );

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Settings</Text>
      </View>

      <View style={styles.userSection}>
        <View style={styles.userAvatar}>
          <Text style={styles.userInitials}>{userData.name.split(' ').map(n => n[0]).join('')}</Text>
        </View>
        <View style={styles.userInfo}>
          <Text style={styles.userName}>{userData.name}</Text>
          <Text style={styles.userEmail}>{userData.email}</Text>
          <Text style={styles.userRole}>{userData.role}</Text>
        </View>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Account</Text>
        {renderSettingItem('person-outline', 'Profile', 'Edit your profile information', () => {})}
        {renderSettingItem('card-outline', 'Subscription', userData.accountType, () => {})}
        {renderSettingItem('key-outline', 'Change Password', '', () => {})}
        {renderSettingItem('log-out-outline', 'Logout', '', handleLogout)}
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Notifications</Text>
        {renderSettingItem(
          'notifications-outline', 
          'Push Notifications', 
          <Switch
            value={notificationsEnabled}
            onValueChange={setNotificationsEnabled}
            trackColor={{ false: '#ccc', true: '#4CAF50' }}
            thumbColor="white"
          />
        )}
        {renderSettingItem(
          'mail-outline', 
          'Email Notifications', 
          <Switch
            value={emailNotificationsEnabled}
            onValueChange={setEmailNotificationsEnabled}
            trackColor={{ false: '#ccc', true: '#4CAF50' }}
            thumbColor="white"
          />
        )}
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Appearance</Text>
        {renderSettingItem(
          'moon-outline', 
          'Dark Mode', 
          <Switch
            value={darkModeEnabled}
            onValueChange={setDarkModeEnabled}
            trackColor={{ false: '#ccc', true: '#4CAF50' }}
            thumbColor="white"
          />
        )}
        {renderSettingItem('text-outline', 'Font Size', 'Medium', () => {})}
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Security</Text>
        {renderSettingItem(
          'finger-print-outline', 
          'Biometric Authentication', 
          <Switch
            value={biometricAuthEnabled}
            onValueChange={setBiometricAuthEnabled}
            trackColor={{ false: '#ccc', true: '#4CAF50' }}
            thumbColor="white"
          />
        )}
        {renderSettingItem('lock-closed-outline', 'App Lock', 'Disabled', () => {})}
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Data & Sync</Text>
        {renderSettingItem(
          'sync-outline', 
          'Auto Sync', 
          <Switch
            value={autoSyncEnabled}
            onValueChange={setAutoSyncEnabled}
            trackColor={{ false: '#ccc', true: '#4CAF50' }}
            thumbColor="white"
          />
        )}
        {renderSettingItem('time-outline', 'Sync Frequency', syncFrequency, () => {})}
        {renderSettingItem('refresh-outline', 'Sync Now', `Last sync: ${formatDate(userData.lastSync)}`, handleSyncNow)}
        {renderSettingItem('trash-outline', 'Clear Cache', '', handleClearCache)}
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Preferences</Text>
        {renderSettingItem('cash-outline', 'Default Currency', defaultCurrency, () => {})}
        {renderSettingItem('calendar-outline', 'Fiscal Year Start', 'January', () => {})}
        {renderSettingItem('analytics-outline', 'Default Report View', 'Overview', () => {})}
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>About</Text>
        {renderSettingItem('information-circle-outline', 'App Version', '1.0.0')}
        {renderSettingItem('document-text-outline', 'Terms of Service', '', () => {})}
        {renderSettingItem('shield-outline', 'Privacy Policy', '', () => {})}
        {renderSettingItem('help-circle-outline', 'Help & Support', '', () => {})}
      </View>

      <View style={styles.footer}>
        <Text style={styles.footerText}>NxtAcre Financial Manager</Text>
        <Text style={styles.footerVersion}>Version 1.0.0</Text>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    padding: 20,
    backgroundColor: '#4CAF50',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
  },
  userSection: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 20,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  userAvatar: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#4CAF50',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
  },
  userInitials: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
  },
  userInfo: {
    flex: 1,
  },
  userName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  userEmail: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  userRole: {
    fontSize: 14,
    color: '#4CAF50',
    marginTop: 2,
  },
  section: {
    marginTop: 20,
    backgroundColor: 'white',
    borderRadius: 10,
    marginHorizontal: 15,
    padding: 15,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  settingIconContainer: {
    width: 40,
    alignItems: 'center',
  },
  settingContent: {
    flex: 1,
    marginLeft: 10,
  },
  settingTitle: {
    fontSize: 16,
    color: '#333',
  },
  settingValue: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  footer: {
    padding: 20,
    alignItems: 'center',
  },
  footerText: {
    fontSize: 14,
    color: '#666',
  },
  footerVersion: {
    fontSize: 12,
    color: '#999',
    marginTop: 5,
  },
});

export default SettingsScreen;