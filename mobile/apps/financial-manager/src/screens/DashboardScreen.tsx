import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, ActivityIndicator } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { DashboardScreenNavigationProp } from '../types/navigation';

// Mock data for initial development
const mockFinancialSummary = {
  totalRevenue: 125000,
  totalExpenses: 78500,
  netProfit: 46500,
  pendingInvoices: 12500,
  pendingExpenses: 3200,
  cashOnHand: 58000
};

const mockRecentTransactions = [
  { id: '1', type: 'expense', amount: 1250, description: 'Equipment repair', date: '2023-08-01', category: 'Maintenance' },
  { id: '2', type: 'income', amount: 5000, description: 'Crop sale', date: '2023-07-28', category: 'Sales' },
  { id: '3', type: 'expense', amount: 750, description: 'Fuel purchase', date: '2023-07-25', category: 'Operations' },
  { id: '4', type: 'income', amount: 3500, description: 'Equipment rental', date: '2023-07-22', category: 'Services' },
];

const DashboardScreen = () => {
  const navigation = useNavigation<DashboardScreenNavigationProp>();
  const [loading, setLoading] = useState(true);
  const [financialSummary, setFinancialSummary] = useState(mockFinancialSummary);
  const [recentTransactions, setRecentTransactions] = useState(mockRecentTransactions);

  useEffect(() => {
    // Simulate API call
    const timer = setTimeout(() => {
      setLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  const formatCurrency = (amount: number) => {
    return `$${amount.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' });
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#4CAF50" />
        <Text style={styles.loadingText}>Loading financial data...</Text>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Financial Dashboard</Text>
        <Text style={styles.headerSubtitle}>Financial overview of your farm</Text>
      </View>

      <View style={styles.summaryContainer}>
        <View style={styles.summaryCard}>
          <Text style={styles.summaryTitle}>Revenue</Text>
          <Text style={styles.summaryAmount}>{formatCurrency(financialSummary.totalRevenue)}</Text>
          <View style={styles.summaryIconContainer}>
            <Ionicons name="trending-up" size={24} color="#4CAF50" />
          </View>
        </View>

        <View style={styles.summaryCard}>
          <Text style={styles.summaryTitle}>Expenses</Text>
          <Text style={styles.summaryAmount}>{formatCurrency(financialSummary.totalExpenses)}</Text>
          <View style={styles.summaryIconContainer}>
            <Ionicons name="trending-down" size={24} color="#F44336" />
          </View>
        </View>

        <View style={styles.summaryCard}>
          <Text style={styles.summaryTitle}>Net Profit</Text>
          <Text style={styles.summaryAmount}>{formatCurrency(financialSummary.netProfit)}</Text>
          <View style={styles.summaryIconContainer}>
            <Ionicons name="cash" size={24} color="#4CAF50" />
          </View>
        </View>
      </View>

      <View style={styles.alertsContainer}>
        <View style={styles.alertCard}>
          <Ionicons name="alert-circle" size={24} color="#FF9800" />
          <View style={styles.alertTextContainer}>
            <Text style={styles.alertTitle}>Pending Invoices</Text>
            <Text style={styles.alertAmount}>{formatCurrency(financialSummary.pendingInvoices)}</Text>
          </View>
          <TouchableOpacity 
            style={styles.alertButton}
            onPress={() => navigation.navigate('Invoices')}
          >
            <Text style={styles.alertButtonText}>View</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.alertCard}>
          <Ionicons name="alert-circle" size={24} color="#FF9800" />
          <View style={styles.alertTextContainer}>
            <Text style={styles.alertTitle}>Pending Expenses</Text>
            <Text style={styles.alertAmount}>{formatCurrency(financialSummary.pendingExpenses)}</Text>
          </View>
          <TouchableOpacity 
            style={styles.alertButton}
            onPress={() => navigation.navigate('Expenses')}
          >
            <Text style={styles.alertButtonText}>View</Text>
          </TouchableOpacity>
        </View>
      </View>

      <View style={styles.sectionContainer}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Recent Transactions</Text>
          <TouchableOpacity>
            <Text style={styles.sectionLink}>View All</Text>
          </TouchableOpacity>
        </View>

        {recentTransactions.map((transaction) => (
          <TouchableOpacity 
            key={transaction.id} 
            style={styles.transactionCard}
            onPress={() => {
              if (transaction.type === 'expense') {
                navigation.navigate('ExpenseDetail', { expenseId: transaction.id });
              } else {
                navigation.navigate('IncomeDetail', { incomeId: transaction.id });
              }
            }}
          >
            <View style={styles.transactionIconContainer}>
              <Ionicons 
                name={transaction.type === 'expense' ? 'arrow-down-circle' : 'arrow-up-circle'} 
                size={24} 
                color={transaction.type === 'expense' ? '#F44336' : '#4CAF50'} 
              />
            </View>
            <View style={styles.transactionDetails}>
              <Text style={styles.transactionDescription}>{transaction.description}</Text>
              <Text style={styles.transactionCategory}>{transaction.category}</Text>
            </View>
            <View style={styles.transactionAmountContainer}>
              <Text 
                style={[
                  styles.transactionAmount, 
                  { color: transaction.type === 'expense' ? '#F44336' : '#4CAF50' }
                ]}
              >
                {transaction.type === 'expense' ? '-' : '+'}{formatCurrency(transaction.amount)}
              </Text>
              <Text style={styles.transactionDate}>{formatDate(transaction.date)}</Text>
            </View>
          </TouchableOpacity>
        ))}
      </View>

      <View style={styles.quickActionsContainer}>
        <Text style={styles.sectionTitle}>Quick Actions</Text>
        <View style={styles.quickActionsGrid}>
          <TouchableOpacity 
            style={styles.quickActionButton}
            onPress={() => navigation.navigate('Expenses')}
          >
            <Ionicons name="add-circle" size={24} color="#4CAF50" />
            <Text style={styles.quickActionText}>Add Expense</Text>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={styles.quickActionButton}
            onPress={() => navigation.navigate('Income')}
          >
            <Ionicons name="add-circle" size={24} color="#4CAF50" />
            <Text style={styles.quickActionText}>Add Income</Text>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={styles.quickActionButton}
            onPress={() => navigation.navigate('Invoices')}
          >
            <Ionicons name="document-text" size={24} color="#4CAF50" />
            <Text style={styles.quickActionText}>Create Invoice</Text>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={styles.quickActionButton}
            onPress={() => navigation.navigate('Reports')}
          >
            <Ionicons name="bar-chart" size={24} color="#4CAF50" />
            <Text style={styles.quickActionText}>View Reports</Text>
          </TouchableOpacity>
        </View>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  header: {
    padding: 20,
    backgroundColor: '#4CAF50',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
  },
  headerSubtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.8)',
    marginTop: 5,
  },
  summaryContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 15,
    backgroundColor: 'white',
    borderRadius: 10,
    margin: 15,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  summaryCard: {
    flex: 1,
    alignItems: 'center',
    padding: 10,
  },
  summaryTitle: {
    fontSize: 14,
    color: '#666',
    marginBottom: 5,
  },
  summaryAmount: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  summaryIconContainer: {
    marginTop: 5,
  },
  alertsContainer: {
    margin: 15,
    marginTop: 0,
  },
  alertCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    borderRadius: 10,
    padding: 15,
    marginBottom: 10,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  alertTextContainer: {
    flex: 1,
    marginLeft: 15,
  },
  alertTitle: {
    fontSize: 16,
    color: '#333',
  },
  alertAmount: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  alertButton: {
    backgroundColor: '#4CAF50',
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 5,
  },
  alertButtonText: {
    color: 'white',
    fontWeight: 'bold',
  },
  sectionContainer: {
    margin: 15,
    marginTop: 0,
    backgroundColor: 'white',
    borderRadius: 10,
    padding: 15,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  sectionLink: {
    fontSize: 14,
    color: '#4CAF50',
  },
  transactionCard: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  transactionIconContainer: {
    marginRight: 15,
  },
  transactionDetails: {
    flex: 1,
  },
  transactionDescription: {
    fontSize: 16,
    color: '#333',
  },
  transactionCategory: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  transactionAmountContainer: {
    alignItems: 'flex-end',
  },
  transactionAmount: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  transactionDate: {
    fontSize: 12,
    color: '#999',
    marginTop: 2,
  },
  quickActionsContainer: {
    margin: 15,
    marginTop: 0,
    backgroundColor: 'white',
    borderRadius: 10,
    padding: 15,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  quickActionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 10,
  },
  quickActionButton: {
    width: '50%',
    alignItems: 'center',
    padding: 15,
  },
  quickActionText: {
    marginTop: 8,
    fontSize: 14,
    color: '#333',
    textAlign: 'center',
  },
});

export default DashboardScreen;