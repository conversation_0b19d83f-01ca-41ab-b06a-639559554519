import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, ActivityIndicator } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { ReportsScreenNavigationProp } from '../types/navigation';

// Mock data for initial development
const mockReportData = {
  revenueByMonth: [
    { month: 'Jan', amount: 25000 },
    { month: 'Feb', amount: 28000 },
    { month: 'Mar', amount: 32000 },
    { month: 'Apr', amount: 30000 },
    { month: 'May', amount: 35000 },
    { month: 'Jun', amount: 40000 },
    { month: 'Jul', amount: 38000 },
  ],
  expensesByMonth: [
    { month: 'Jan', amount: 18000 },
    { month: 'Feb', amount: 19500 },
    { month: 'Mar', amount: 22000 },
    { month: 'Apr', amount: 21000 },
    { month: 'May', amount: 24000 },
    { month: 'Jun', amount: 26000 },
    { month: 'Jul', amount: 25000 },
  ],
  expensesByCategory: [
    { category: 'Operations', amount: 45000 },
    { category: 'Supplies', amount: 35000 },
    { category: 'Maintenance', amount: 25000 },
    { category: 'Administrative', amount: 15000 },
    { category: 'Other', amount: 10000 },
  ],
  revenueBySource: [
    { source: 'Crop Sales', amount: 120000 },
    { source: 'Livestock', amount: 80000 },
    { source: 'Services', amount: 30000 },
    { source: 'Subsidies', amount: 20000 },
    { source: 'Other', amount: 10000 },
  ],
  outstandingInvoices: 25000,
  pendingExpenses: 8000,
  cashOnHand: 65000,
  projectedRevenue: 42000,
};

const ReportsScreen = () => {
  const navigation = useNavigation<ReportsScreenNavigationProp>();
  const [loading, setLoading] = useState(true);
  const [reportData, setReportData] = useState(mockReportData);
  const [selectedPeriod, setSelectedPeriod] = useState<'month' | 'quarter' | 'year'>('month');
  const [selectedReport, setSelectedReport] = useState<'overview' | 'revenue' | 'expenses' | 'profit'>('overview');

  useEffect(() => {
    // Simulate API call
    const timer = setTimeout(() => {
      setLoading(false);
    }, 1500);

    return () => clearTimeout(timer);
  }, []);

  const formatCurrency = (amount: number) => {
    return `$${amount.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
  };

  // Simple bar chart component
  const BarChart = ({ data, valueKey, labelKey, color }: { data: any[], valueKey: string, labelKey: string, color: string }) => {
    const maxValue = Math.max(...data.map(item => item[valueKey]));
    
    return (
      <View style={styles.chartContainer}>
        {data.map((item, index) => (
          <View key={index} style={styles.chartRow}>
            <Text style={styles.chartLabel}>{item[labelKey]}</Text>
            <View style={styles.barContainer}>
              <View 
                style={[
                  styles.bar, 
                  { 
                    width: `${(item[valueKey] / maxValue) * 100}%`,
                    backgroundColor: color
                  }
                ]} 
              />
              <Text style={styles.barValue}>{formatCurrency(item[valueKey])}</Text>
            </View>
          </View>
        ))}
      </View>
    );
  };

  // Simple pie chart representation (just colored squares with values)
  const PieChartLegend = ({ data, valueKey, labelKey, colors }: { data: any[], valueKey: string, labelKey: string, colors: string[] }) => {
    const total = data.reduce((sum, item) => sum + item[valueKey], 0);
    
    return (
      <View style={styles.pieChartContainer}>
        {data.map((item, index) => (
          <View key={index} style={styles.pieChartItem}>
            <View style={[styles.pieChartColor, { backgroundColor: colors[index % colors.length] }]} />
            <View style={styles.pieChartLabelContainer}>
              <Text style={styles.pieChartLabel}>{item[labelKey]}</Text>
              <Text style={styles.pieChartValue}>{formatCurrency(item[valueKey])}</Text>
              <Text style={styles.pieChartPercentage}>
                {((item[valueKey] / total) * 100).toFixed(1)}%
              </Text>
            </View>
          </View>
        ))}
      </View>
    );
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#4CAF50" />
        <Text style={styles.loadingText}>Loading financial reports...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Financial Reports</Text>
      </View>

      <View style={styles.periodSelector}>
        <TouchableOpacity 
          style={[styles.periodButton, selectedPeriod === 'month' && styles.periodButtonActive]}
          onPress={() => setSelectedPeriod('month')}
        >
          <Text style={[styles.periodButtonText, selectedPeriod === 'month' && styles.periodButtonTextActive]}>
            Monthly
          </Text>
        </TouchableOpacity>
        <TouchableOpacity 
          style={[styles.periodButton, selectedPeriod === 'quarter' && styles.periodButtonActive]}
          onPress={() => setSelectedPeriod('quarter')}
        >
          <Text style={[styles.periodButtonText, selectedPeriod === 'quarter' && styles.periodButtonTextActive]}>
            Quarterly
          </Text>
        </TouchableOpacity>
        <TouchableOpacity 
          style={[styles.periodButton, selectedPeriod === 'year' && styles.periodButtonActive]}
          onPress={() => setSelectedPeriod('year')}
        >
          <Text style={[styles.periodButtonText, selectedPeriod === 'year' && styles.periodButtonTextActive]}>
            Yearly
          </Text>
        </TouchableOpacity>
      </View>

      <View style={styles.reportSelector}>
        <TouchableOpacity 
          style={[styles.reportButton, selectedReport === 'overview' && styles.reportButtonActive]}
          onPress={() => setSelectedReport('overview')}
        >
          <Ionicons 
            name="stats-chart" 
            size={20} 
            color={selectedReport === 'overview' ? 'white' : '#666'} 
          />
          <Text style={[styles.reportButtonText, selectedReport === 'overview' && styles.reportButtonTextActive]}>
            Overview
          </Text>
        </TouchableOpacity>
        <TouchableOpacity 
          style={[styles.reportButton, selectedReport === 'revenue' && styles.reportButtonActive]}
          onPress={() => setSelectedReport('revenue')}
        >
          <Ionicons 
            name="trending-up" 
            size={20} 
            color={selectedReport === 'revenue' ? 'white' : '#666'} 
          />
          <Text style={[styles.reportButtonText, selectedReport === 'revenue' && styles.reportButtonTextActive]}>
            Revenue
          </Text>
        </TouchableOpacity>
        <TouchableOpacity 
          style={[styles.reportButton, selectedReport === 'expenses' && styles.reportButtonActive]}
          onPress={() => setSelectedReport('expenses')}
        >
          <Ionicons 
            name="trending-down" 
            size={20} 
            color={selectedReport === 'expenses' ? 'white' : '#666'} 
          />
          <Text style={[styles.reportButtonText, selectedReport === 'expenses' && styles.reportButtonTextActive]}>
            Expenses
          </Text>
        </TouchableOpacity>
        <TouchableOpacity 
          style={[styles.reportButton, selectedReport === 'profit' && styles.reportButtonActive]}
          onPress={() => setSelectedReport('profit')}
        >
          <Ionicons 
            name="cash" 
            size={20} 
            color={selectedReport === 'profit' ? 'white' : '#666'} 
          />
          <Text style={[styles.reportButtonText, selectedReport === 'profit' && styles.reportButtonTextActive]}>
            Profit
          </Text>
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.reportContent}>
        {selectedReport === 'overview' && (
          <>
            <View style={styles.summaryContainer}>
              <View style={styles.summaryCard}>
                <Text style={styles.summaryTitle}>Revenue (YTD)</Text>
                <Text style={styles.summaryAmount}>
                  {formatCurrency(reportData.revenueByMonth.reduce((sum, item) => sum + item.amount, 0))}
                </Text>
              </View>
              <View style={styles.summaryCard}>
                <Text style={styles.summaryTitle}>Expenses (YTD)</Text>
                <Text style={styles.summaryAmount}>
                  {formatCurrency(reportData.expensesByMonth.reduce((sum, item) => sum + item.amount, 0))}
                </Text>
              </View>
              <View style={styles.summaryCard}>
                <Text style={styles.summaryTitle}>Net Profit (YTD)</Text>
                <Text style={styles.summaryAmount}>
                  {formatCurrency(
                    reportData.revenueByMonth.reduce((sum, item) => sum + item.amount, 0) - 
                    reportData.expensesByMonth.reduce((sum, item) => sum + item.amount, 0)
                  )}
                </Text>
              </View>
            </View>

            <View style={styles.sectionContainer}>
              <Text style={styles.sectionTitle}>Revenue vs Expenses</Text>
              <View style={styles.chartContainer}>
                {reportData.revenueByMonth.map((item, index) => (
                  <View key={index} style={styles.comparisonChartRow}>
                    <Text style={styles.chartLabel}>{item.month}</Text>
                    <View style={styles.comparisonBarContainer}>
                      <View style={styles.comparisonBars}>
                        <View 
                          style={[
                            styles.comparisonBar, 
                            { 
                              width: `${(item.amount / 50000) * 100}%`,
                              backgroundColor: '#4CAF50'
                            }
                          ]} 
                        />
                        <View 
                          style={[
                            styles.comparisonBar, 
                            { 
                              width: `${(reportData.expensesByMonth[index].amount / 50000) * 100}%`,
                              backgroundColor: '#F44336'
                            }
                          ]} 
                        />
                      </View>
                      <View style={styles.comparisonValues}>
                        <Text style={[styles.comparisonValue, { color: '#4CAF50' }]}>
                          {formatCurrency(item.amount)}
                        </Text>
                        <Text style={[styles.comparisonValue, { color: '#F44336' }]}>
                          {formatCurrency(reportData.expensesByMonth[index].amount)}
                        </Text>
                      </View>
                    </View>
                  </View>
                ))}
              </View>
              <View style={styles.chartLegend}>
                <View style={styles.legendItem}>
                  <View style={[styles.legendColor, { backgroundColor: '#4CAF50' }]} />
                  <Text style={styles.legendText}>Revenue</Text>
                </View>
                <View style={styles.legendItem}>
                  <View style={[styles.legendColor, { backgroundColor: '#F44336' }]} />
                  <Text style={styles.legendText}>Expenses</Text>
                </View>
              </View>
            </View>

            <View style={styles.sectionContainer}>
              <Text style={styles.sectionTitle}>Financial Health</Text>
              <View style={styles.financialHealthContainer}>
                <View style={styles.financialHealthItem}>
                  <Text style={styles.financialHealthLabel}>Outstanding Invoices</Text>
                  <Text style={styles.financialHealthValue}>{formatCurrency(reportData.outstandingInvoices)}</Text>
                </View>
                <View style={styles.financialHealthItem}>
                  <Text style={styles.financialHealthLabel}>Pending Expenses</Text>
                  <Text style={styles.financialHealthValue}>{formatCurrency(reportData.pendingExpenses)}</Text>
                </View>
                <View style={styles.financialHealthItem}>
                  <Text style={styles.financialHealthLabel}>Cash on Hand</Text>
                  <Text style={styles.financialHealthValue}>{formatCurrency(reportData.cashOnHand)}</Text>
                </View>
                <View style={styles.financialHealthItem}>
                  <Text style={styles.financialHealthLabel}>Projected Revenue (Next Month)</Text>
                  <Text style={styles.financialHealthValue}>{formatCurrency(reportData.projectedRevenue)}</Text>
                </View>
              </View>
            </View>
          </>
        )}

        {selectedReport === 'revenue' && (
          <>
            <View style={styles.sectionContainer}>
              <Text style={styles.sectionTitle}>Revenue by Month</Text>
              <BarChart 
                data={reportData.revenueByMonth} 
                valueKey="amount" 
                labelKey="month" 
                color="#4CAF50" 
              />
            </View>

            <View style={styles.sectionContainer}>
              <Text style={styles.sectionTitle}>Revenue by Source</Text>
              <PieChartLegend 
                data={reportData.revenueBySource} 
                valueKey="amount" 
                labelKey="source" 
                colors={['#4CAF50', '#8BC34A', '#CDDC39', '#FFEB3B', '#FFC107']} 
              />
            </View>
          </>
        )}

        {selectedReport === 'expenses' && (
          <>
            <View style={styles.sectionContainer}>
              <Text style={styles.sectionTitle}>Expenses by Month</Text>
              <BarChart 
                data={reportData.expensesByMonth} 
                valueKey="amount" 
                labelKey="month" 
                color="#F44336" 
              />
            </View>

            <View style={styles.sectionContainer}>
              <Text style={styles.sectionTitle}>Expenses by Category</Text>
              <PieChartLegend 
                data={reportData.expensesByCategory} 
                valueKey="amount" 
                labelKey="category" 
                colors={['#F44336', '#E91E63', '#9C27B0', '#673AB7', '#3F51B5']} 
              />
            </View>
          </>
        )}

        {selectedReport === 'profit' && (
          <>
            <View style={styles.sectionContainer}>
              <Text style={styles.sectionTitle}>Monthly Profit</Text>
              <View style={styles.chartContainer}>
                {reportData.revenueByMonth.map((item, index) => {
                  const profit = item.amount - reportData.expensesByMonth[index].amount;
                  const isPositive = profit >= 0;
                  
                  return (
                    <View key={index} style={styles.chartRow}>
                      <Text style={styles.chartLabel}>{item.month}</Text>
                      <View style={styles.barContainer}>
                        <View 
                          style={[
                            styles.bar, 
                            { 
                              width: `${(Math.abs(profit) / 20000) * 100}%`,
                              backgroundColor: isPositive ? '#4CAF50' : '#F44336'
                            }
                          ]} 
                        />
                        <Text style={[styles.barValue, { color: isPositive ? '#4CAF50' : '#F44336' }]}>
                          {formatCurrency(profit)}
                        </Text>
                      </View>
                    </View>
                  );
                })}
              </View>
            </View>

            <View style={styles.sectionContainer}>
              <Text style={styles.sectionTitle}>Profit Margin</Text>
              <View style={styles.chartContainer}>
                {reportData.revenueByMonth.map((item, index) => {
                  const profit = item.amount - reportData.expensesByMonth[index].amount;
                  const margin = (profit / item.amount) * 100;
                  const isPositive = margin >= 0;
                  
                  return (
                    <View key={index} style={styles.chartRow}>
                      <Text style={styles.chartLabel}>{item.month}</Text>
                      <View style={styles.barContainer}>
                        <View 
                          style={[
                            styles.bar, 
                            { 
                              width: `${Math.min(Math.abs(margin), 50)}%`,
                              backgroundColor: isPositive ? '#4CAF50' : '#F44336'
                            }
                          ]} 
                        />
                        <Text style={[styles.barValue, { color: isPositive ? '#4CAF50' : '#F44336' }]}>
                          {margin.toFixed(1)}%
                        </Text>
                      </View>
                    </View>
                  );
                })}
              </View>
            </View>
          </>
        )}

        <View style={styles.exportContainer}>
          <TouchableOpacity style={styles.exportButton}>
            <Ionicons name="download-outline" size={20} color="white" />
            <Text style={styles.exportButtonText}>Export Report</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  header: {
    padding: 20,
    backgroundColor: '#4CAF50',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
  },
  periodSelector: {
    flexDirection: 'row',
    backgroundColor: 'white',
    padding: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  periodButton: {
    flex: 1,
    paddingVertical: 8,
    alignItems: 'center',
    borderRadius: 5,
  },
  periodButtonActive: {
    backgroundColor: '#e8f5e9',
  },
  periodButtonText: {
    fontSize: 14,
    color: '#666',
  },
  periodButtonTextActive: {
    color: '#4CAF50',
    fontWeight: 'bold',
  },
  reportSelector: {
    flexDirection: 'row',
    backgroundColor: 'white',
    padding: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  reportButton: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 8,
    borderRadius: 5,
  },
  reportButtonActive: {
    backgroundColor: '#4CAF50',
  },
  reportButtonText: {
    fontSize: 14,
    color: '#666',
    marginLeft: 5,
  },
  reportButtonTextActive: {
    color: 'white',
    fontWeight: 'bold',
  },
  reportContent: {
    flex: 1,
    padding: 15,
  },
  summaryContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  summaryCard: {
    flex: 1,
    backgroundColor: 'white',
    borderRadius: 10,
    padding: 15,
    marginHorizontal: 5,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  summaryTitle: {
    fontSize: 14,
    color: '#666',
    marginBottom: 5,
  },
  summaryAmount: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  sectionContainer: {
    backgroundColor: 'white',
    borderRadius: 10,
    padding: 15,
    marginBottom: 20,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 15,
  },
  chartContainer: {
    marginTop: 10,
  },
  chartRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  chartLabel: {
    width: 50,
    fontSize: 14,
    color: '#666',
  },
  barContainer: {
    flex: 1,
    marginLeft: 10,
  },
  bar: {
    height: 20,
    borderRadius: 5,
  },
  barValue: {
    position: 'absolute',
    right: 5,
    top: 0,
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
    lineHeight: 20,
  },
  comparisonChartRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 15,
  },
  comparisonBarContainer: {
    flex: 1,
    marginLeft: 10,
  },
  comparisonBars: {
    flexDirection: 'column',
  },
  comparisonBar: {
    height: 15,
    borderRadius: 3,
    marginBottom: 2,
  },
  comparisonValues: {
    flexDirection: 'column',
    position: 'absolute',
    right: 5,
    top: 0,
  },
  comparisonValue: {
    fontSize: 12,
    fontWeight: 'bold',
    lineHeight: 15,
    marginBottom: 2,
  },
  chartLegend: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 10,
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 10,
  },
  legendColor: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 5,
  },
  legendText: {
    fontSize: 14,
    color: '#666',
  },
  pieChartContainer: {
    marginTop: 10,
  },
  pieChartItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  pieChartColor: {
    width: 20,
    height: 20,
    borderRadius: 4,
    marginRight: 10,
  },
  pieChartLabelContainer: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  pieChartLabel: {
    fontSize: 14,
    color: '#333',
    flex: 1,
  },
  pieChartValue: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
    marginRight: 10,
  },
  pieChartPercentage: {
    fontSize: 14,
    color: '#666',
    width: 50,
    textAlign: 'right',
  },
  financialHealthContainer: {
    marginTop: 10,
  },
  financialHealthItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  financialHealthLabel: {
    fontSize: 14,
    color: '#666',
  },
  financialHealthValue: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
  },
  exportContainer: {
    alignItems: 'center',
    marginVertical: 20,
  },
  exportButton: {
    flexDirection: 'row',
    backgroundColor: '#4CAF50',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 5,
    alignItems: 'center',
  },
  exportButtonText: {
    color: 'white',
    fontWeight: 'bold',
    marginLeft: 10,
  },
});

export default ReportsScreen;