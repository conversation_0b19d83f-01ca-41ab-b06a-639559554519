import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, FlatList, TouchableOpacity, TextInput, ActivityIndicator } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { ExpensesScreenNavigationProp, Expense } from '../types/navigation';

// Mock data for initial development
const mockExpenses: Expense[] = [
  { 
    id: '1', 
    amount: 1250, 
    description: 'Equipment repair', 
    category: 'Maintenance', 
    date: '2023-08-01', 
    status: 'approved',
    createdBy: '<PERSON>',
    createdAt: '2023-08-01T10:30:00Z',
    updatedAt: '2023-08-01T14:45:00Z'
  },
  { 
    id: '2', 
    amount: 750, 
    description: 'Fuel purchase', 
    category: 'Operations', 
    date: '2023-07-25', 
    status: 'approved',
    createdBy: '<PERSON>',
    createdAt: '2023-07-25T09:15:00Z',
    updatedAt: '2023-07-25T11:20:00Z'
  },
  { 
    id: '3', 
    amount: 2500, 
    description: 'Seed purchase', 
    category: 'Supplies', 
    date: '2023-07-20', 
    status: 'approved',
    createdBy: '<PERSON>',
    createdAt: '2023-07-20T08:00:00Z',
    updatedAt: '2023-07-20T10:10:00Z'
  },
  { 
    id: '4', 
    amount: 500, 
    description: 'Office supplies', 
    category: 'Administrative', 
    date: '2023-07-18', 
    status: 'approved',
    createdBy: 'Jane Smith',
    createdAt: '2023-07-18T14:30:00Z',
    updatedAt: '2023-07-18T16:45:00Z'
  },
  { 
    id: '5', 
    amount: 1800, 
    description: 'Equipment rental', 
    category: 'Operations', 
    date: '2023-07-15', 
    status: 'pending',
    createdBy: 'John Doe',
    createdAt: '2023-07-15T11:20:00Z',
    updatedAt: '2023-07-15T11:20:00Z'
  },
  { 
    id: '6', 
    amount: 350, 
    description: 'Vehicle maintenance', 
    category: 'Maintenance', 
    date: '2023-07-10', 
    status: 'pending',
    createdBy: 'Jane Smith',
    createdAt: '2023-07-10T13:15:00Z',
    updatedAt: '2023-07-10T13:15:00Z'
  },
  { 
    id: '7', 
    amount: 1200, 
    description: 'Fertilizer purchase', 
    category: 'Supplies', 
    date: '2023-07-05', 
    status: 'rejected',
    createdBy: 'John Doe',
    createdAt: '2023-07-05T09:30:00Z',
    updatedAt: '2023-07-06T10:45:00Z'
  }
];

const ExpensesScreen = () => {
  const navigation = useNavigation<ExpensesScreenNavigationProp>();
  const [loading, setLoading] = useState(true);
  const [expenses, setExpenses] = useState<Expense[]>([]);
  const [filteredExpenses, setFilteredExpenses] = useState<Expense[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'pending' | 'approved' | 'rejected'>('all');

  useEffect(() => {
    // Simulate API call
    const timer = setTimeout(() => {
      setExpenses(mockExpenses);
      setFilteredExpenses(mockExpenses);
      setLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  useEffect(() => {
    // Apply filters when expenses, searchQuery, or statusFilter changes
    let filtered = expenses;
    
    // Apply search filter
    if (searchQuery) {
      filtered = filtered.filter(expense => 
        expense.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        expense.category.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }
    
    // Apply status filter
    if (statusFilter !== 'all') {
      filtered = filtered.filter(expense => expense.status === statusFilter);
    }
    
    setFilteredExpenses(filtered);
  }, [expenses, searchQuery, statusFilter]);

  const formatCurrency = (amount: number) => {
    return `$${amount.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved':
        return '#4CAF50';
      case 'pending':
        return '#FF9800';
      case 'rejected':
        return '#F44336';
      default:
        return '#999';
    }
  };

  const renderExpenseItem = ({ item }: { item: Expense }) => (
    <TouchableOpacity 
      style={styles.expenseCard}
      onPress={() => navigation.navigate('ExpenseDetail', { expenseId: item.id })}
    >
      <View style={styles.expenseHeader}>
        <Text style={styles.expenseDescription}>{item.description}</Text>
        <Text style={styles.expenseAmount}>{formatCurrency(item.amount)}</Text>
      </View>
      <View style={styles.expenseDetails}>
        <View style={styles.expenseDetail}>
          <Ionicons name="calendar-outline" size={16} color="#666" />
          <Text style={styles.expenseDetailText}>{formatDate(item.date)}</Text>
        </View>
        <View style={styles.expenseDetail}>
          <Ionicons name="pricetag-outline" size={16} color="#666" />
          <Text style={styles.expenseDetailText}>{item.category}</Text>
        </View>
        <View style={styles.expenseDetail}>
          <View style={[styles.statusIndicator, { backgroundColor: getStatusColor(item.status) }]} />
          <Text style={styles.expenseDetailText}>
            {item.status.charAt(0).toUpperCase() + item.status.slice(1)}
          </Text>
        </View>
      </View>
    </TouchableOpacity>
  );

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#4CAF50" />
        <Text style={styles.loadingText}>Loading expenses...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Expenses</Text>
        <TouchableOpacity 
          style={styles.addButton}
          onPress={() => navigation.navigate('ExpenseDetail', { expenseId: 'new' })}
        >
          <Ionicons name="add" size={24} color="white" />
        </TouchableOpacity>
      </View>

      <View style={styles.searchContainer}>
        <Ionicons name="search" size={20} color="#999" style={styles.searchIcon} />
        <TextInput
          style={styles.searchInput}
          placeholder="Search expenses..."
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
      </View>

      <View style={styles.filterContainer}>
        <TouchableOpacity 
          style={[styles.filterButton, statusFilter === 'all' && styles.filterButtonActive]}
          onPress={() => setStatusFilter('all')}
        >
          <Text style={[styles.filterButtonText, statusFilter === 'all' && styles.filterButtonTextActive]}>All</Text>
        </TouchableOpacity>
        <TouchableOpacity 
          style={[styles.filterButton, statusFilter === 'pending' && styles.filterButtonActive]}
          onPress={() => setStatusFilter('pending')}
        >
          <Text style={[styles.filterButtonText, statusFilter === 'pending' && styles.filterButtonTextActive]}>Pending</Text>
        </TouchableOpacity>
        <TouchableOpacity 
          style={[styles.filterButton, statusFilter === 'approved' && styles.filterButtonActive]}
          onPress={() => setStatusFilter('approved')}
        >
          <Text style={[styles.filterButtonText, statusFilter === 'approved' && styles.filterButtonTextActive]}>Approved</Text>
        </TouchableOpacity>
        <TouchableOpacity 
          style={[styles.filterButton, statusFilter === 'rejected' && styles.filterButtonActive]}
          onPress={() => setStatusFilter('rejected')}
        >
          <Text style={[styles.filterButtonText, statusFilter === 'rejected' && styles.filterButtonTextActive]}>Rejected</Text>
        </TouchableOpacity>
      </View>

      {filteredExpenses.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Ionicons name="document-text-outline" size={64} color="#ccc" />
          <Text style={styles.emptyText}>No expenses found</Text>
          <Text style={styles.emptySubtext}>Try adjusting your filters or add a new expense</Text>
        </View>
      ) : (
        <FlatList
          data={filteredExpenses}
          renderItem={renderExpenseItem}
          keyExtractor={item => item.id}
          contentContainerStyle={styles.listContainer}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#4CAF50',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
  },
  addButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    borderRadius: 8,
    margin: 15,
    paddingHorizontal: 15,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  searchIcon: {
    marginRight: 10,
  },
  searchInput: {
    flex: 1,
    height: 50,
    fontSize: 16,
  },
  filterContainer: {
    flexDirection: 'row',
    paddingHorizontal: 15,
    marginBottom: 10,
  },
  filterButton: {
    paddingHorizontal: 15,
    paddingVertical: 8,
    marginRight: 10,
    borderRadius: 20,
    backgroundColor: '#f0f0f0',
  },
  filterButtonActive: {
    backgroundColor: '#4CAF50',
  },
  filterButtonText: {
    fontSize: 14,
    color: '#666',
  },
  filterButtonTextActive: {
    color: 'white',
    fontWeight: 'bold',
  },
  listContainer: {
    padding: 15,
    paddingTop: 5,
  },
  expenseCard: {
    backgroundColor: 'white',
    borderRadius: 10,
    padding: 15,
    marginBottom: 10,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  expenseHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  expenseDescription: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    flex: 1,
  },
  expenseAmount: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#F44336',
  },
  expenseDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  expenseDetail: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  expenseDetailText: {
    fontSize: 14,
    color: '#666',
    marginLeft: 5,
  },
  statusIndicator: {
    width: 10,
    height: 10,
    borderRadius: 5,
    marginRight: 5,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#666',
    marginTop: 10,
  },
  emptySubtext: {
    fontSize: 14,
    color: '#999',
    textAlign: 'center',
    marginTop: 5,
  },
});

export default ExpensesScreen;