import { StackNavigationProp } from '@react-navigation/stack';
import { BottomTabNavigationProp } from '@react-navigation/bottom-tabs';
import { CompositeNavigationProp, RouteProp } from '@react-navigation/native';

// Financial types
export interface Expense {
  id: string;
  amount: number;
  description: string;
  category: string;
  date: string;
  status: 'pending' | 'approved' | 'rejected';
  receiptUrl?: string;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

export interface Income {
  id: string;
  amount: number;
  description: string;
  category: string;
  date: string;
  source: string;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

export interface Invoice {
  id: string;
  invoiceNumber: string;
  amount: number;
  description: string;
  customer: string;
  issueDate: string;
  dueDate: string;
  status: 'draft' | 'sent' | 'paid' | 'overdue' | 'cancelled';
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

export interface Budget {
  id: string;
  name: string;
  amount: number;
  category: string;
  period: 'monthly' | 'quarterly' | 'yearly';
  startDate: string;
  endDate: string;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

// Root Stack Parameter List
export type RootStackParamList = {
  Login: undefined;
  Main: undefined;
  ExpenseDetail: { expenseId: string };
  IncomeDetail: { incomeId: string };
  InvoiceDetail: { invoiceId: string };
  BudgetDetail: { budgetId: string };
  ReceiptCapture: { expenseId?: string };
};

// Main Tab Parameter List
export type MainTabParamList = {
  Dashboard: undefined;
  Expenses: undefined;
  Income: undefined;
  Invoices: undefined;
  Reports: undefined;
  Settings: undefined;
};

// Navigation Types
export type DashboardScreenNavigationProp = CompositeNavigationProp<
  BottomTabNavigationProp<MainTabParamList, 'Dashboard'>,
  StackNavigationProp<RootStackParamList>
>;

export type ExpensesScreenNavigationProp = CompositeNavigationProp<
  BottomTabNavigationProp<MainTabParamList, 'Expenses'>,
  StackNavigationProp<RootStackParamList>
>;

export type IncomeScreenNavigationProp = CompositeNavigationProp<
  BottomTabNavigationProp<MainTabParamList, 'Income'>,
  StackNavigationProp<RootStackParamList>
>;

export type InvoicesScreenNavigationProp = CompositeNavigationProp<
  BottomTabNavigationProp<MainTabParamList, 'Invoices'>,
  StackNavigationProp<RootStackParamList>
>;

export type ReportsScreenNavigationProp = CompositeNavigationProp<
  BottomTabNavigationProp<MainTabParamList, 'Reports'>,
  StackNavigationProp<RootStackParamList>
>;

export type SettingsScreenNavigationProp = CompositeNavigationProp<
  BottomTabNavigationProp<MainTabParamList, 'Settings'>,
  StackNavigationProp<RootStackParamList>
>;

export type ExpenseDetailScreenNavigationProp = StackNavigationProp<
  RootStackParamList,
  'ExpenseDetail'
>;

export type ExpenseDetailScreenRouteProp = RouteProp<
  RootStackParamList,
  'ExpenseDetail'
>;

export type IncomeDetailScreenNavigationProp = StackNavigationProp<
  RootStackParamList,
  'IncomeDetail'
>;

export type IncomeDetailScreenRouteProp = RouteProp<
  RootStackParamList,
  'IncomeDetail'
>;

export type InvoiceDetailScreenNavigationProp = StackNavigationProp<
  RootStackParamList,
  'InvoiceDetail'
>;

export type InvoiceDetailScreenRouteProp = RouteProp<
  RootStackParamList,
  'InvoiceDetail'
>;

export type BudgetDetailScreenNavigationProp = StackNavigationProp<
  RootStackParamList,
  'BudgetDetail'
>;

export type BudgetDetailScreenRouteProp = RouteProp<
  RootStackParamList,
  'BudgetDetail'
>;

export type ReceiptCaptureScreenNavigationProp = StackNavigationProp<
  RootStackParamList,
  'ReceiptCapture'
>;

export type ReceiptCaptureScreenRouteProp = RouteProp<
  RootStackParamList,
  'ReceiptCapture'
>;