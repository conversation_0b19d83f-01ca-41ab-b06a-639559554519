import React from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createStackNavigator } from '@react-navigation/stack';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../../../../shared/services/auth/AuthContext';

// Screens
import DashboardScreen from '../screens/DashboardScreen';
import ExpensesScreen from '../screens/ExpensesScreen';
import IncomeScreen from '../screens/IncomeScreen';
import InvoicesScreen from '../screens/InvoicesScreen';
import ReportsScreen from '../screens/ReportsScreen';
import SettingsScreen from '../screens/SettingsScreen';
import LoginScreen from '../../../../shared/components/auth/LoginScreen';
import ExpenseDetailScreen from '../screens/ExpenseDetailScreen';
import IncomeDetailScreen from '../screens/IncomeDetailScreen';
import InvoiceDetailScreen from '../screens/InvoiceDetailScreen';
import BudgetDetailScreen from '../screens/BudgetDetailScreen';
import ReceiptCaptureScreen from '../screens/ReceiptCaptureScreen';

// Types
import { RootStackParamList, MainTabParamList } from '../types/navigation';

const Stack = createStackNavigator<RootStackParamList>();
const Tab = createBottomTabNavigator<MainTabParamList>();

const MainTabs = () => {
  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName: string;

          if (route.name === 'Dashboard') {
            iconName = focused ? 'stats-chart' : 'stats-chart-outline';
          } else if (route.name === 'Expenses') {
            iconName = focused ? 'cash' : 'cash-outline';
          } else if (route.name === 'Income') {
            iconName = focused ? 'trending-up' : 'trending-up-outline';
          } else if (route.name === 'Invoices') {
            iconName = focused ? 'document-text' : 'document-text-outline';
          } else if (route.name === 'Reports') {
            iconName = focused ? 'bar-chart' : 'bar-chart-outline';
          } else if (route.name === 'Settings') {
            iconName = focused ? 'settings' : 'settings-outline';
          }

          return <Ionicons name={iconName as any} size={size} color={color} />;
        },
        tabBarActiveTintColor: '#4CAF50',
        tabBarInactiveTintColor: 'gray',
      })}
    >
      <Tab.Screen name="Dashboard" component={DashboardScreen} options={{ title: 'Dashboard' }} />
      <Tab.Screen name="Expenses" component={ExpensesScreen} options={{ title: 'Expenses' }} />
      <Tab.Screen name="Income" component={IncomeScreen} options={{ title: 'Income' }} />
      <Tab.Screen name="Invoices" component={InvoicesScreen} options={{ title: 'Invoices' }} />
      <Tab.Screen name="Reports" component={ReportsScreen} options={{ title: 'Reports' }} />
      <Tab.Screen name="Settings" component={SettingsScreen} options={{ title: 'Settings' }} />
    </Tab.Navigator>
  );
};

const AppNavigator = () => {
  const { isAuthenticated } = useAuth();

  return (
    <Stack.Navigator>
      {!isAuthenticated ? (
        <Stack.Screen 
          name="Login" 
          component={LoginScreen} 
          options={{ headerShown: false }}
        />
      ) : (
        <>
          <Stack.Screen 
            name="Main" 
            component={MainTabs} 
            options={{ headerShown: false }}
          />
          <Stack.Screen 
            name="ExpenseDetail" 
            component={ExpenseDetailScreen} 
            options={{ title: 'Expense Details' }}
          />
          <Stack.Screen 
            name="IncomeDetail" 
            component={IncomeDetailScreen} 
            options={{ title: 'Income Details' }}
          />
          <Stack.Screen 
            name="InvoiceDetail" 
            component={InvoiceDetailScreen} 
            options={{ title: 'Invoice Details' }}
          />
          <Stack.Screen 
            name="BudgetDetail" 
            component={BudgetDetailScreen} 
            options={{ title: 'Budget Details' }}
          />
          <Stack.Screen 
            name="ReceiptCapture" 
            component={ReceiptCaptureScreen} 
            options={{ title: 'Capture Receipt' }}
          />
        </>
      )}
    </Stack.Navigator>
  );
};

export default AppNavigator;