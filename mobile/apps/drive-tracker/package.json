{"name": "nxtacre-drive-tracker", "version": "1.0.0", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "eject": "expo eject", "test": "jest --watchAll"}, "jest": {"preset": "jest-expo"}, "dependencies": {"@expo/vector-icons": "^13.0.0", "@react-navigation/bottom-tabs": "^6.5.8", "@react-navigation/native": "^6.1.7", "@react-navigation/native-stack": "^6.9.13", "expo": "~48.0.18", "expo-asset": "~8.9.1", "expo-constants": "~14.2.1", "expo-font": "~11.1.1", "expo-linking": "~4.0.1", "expo-location": "~15.1.1", "expo-notifications": "~0.18.1", "expo-splash-screen": "~0.18.2", "expo-status-bar": "~1.4.4", "expo-system-ui": "~2.2.1", "expo-task-manager": "~11.1.1", "expo-web-browser": "~12.1.1", "react": "18.2.0", "react-dom": "18.2.0", "react-native": "0.71.8", "react-native-gesture-handler": "~2.9.0", "react-native-maps": "1.3.2", "react-native-reanimated": "~2.14.4", "react-native-safe-area-context": "4.5.0", "react-native-screens": "~3.20.0", "react-native-web": "~0.18.10", "zustand": "^4.3.8"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~18.0.14", "jest": "^29.2.1", "jest-expo": "~48.0.0", "react-test-renderer": "18.2.0", "typescript": "^4.9.4"}, "private": true, "description": "Drive Tracker App for NxtAcre Farm Management Platform", "author": "NxtAcre Team", "license": "UNLICENSED"}