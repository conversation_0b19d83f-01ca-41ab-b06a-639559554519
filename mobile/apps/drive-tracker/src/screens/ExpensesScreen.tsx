import React, { useState } from 'react';
import { View, Text, StyleSheet, FlatList, TouchableOpacity, TextInput, ScrollView } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { ExpensesScreenNavigationProp } from '../types/navigation';

// Mock data - would be fetched from API in a real implementation
const mockExpenses = [
  {
    id: '1',
    date: '2023-06-20',
    category: 'Fuel',
    amount: 45.67,
    description: 'Regular unleaded gas',
    vehicle: { id: '1', name: 'My Car' },
    trip: { id: '1', startLocation: 'Home', endLocation: 'Office' },
  },
  {
    id: '2',
    date: '2023-06-18',
    category: 'Maintenance',
    amount: 120.00,
    description: 'Oil change and filter',
    vehicle: { id: '1', name: 'My Car' },
    trip: null,
  },
  {
    id: '3',
    date: '2023-06-15',
    category: 'Parking',
    amount: 15.00,
    description: 'Downtown parking garage',
    vehicle: { id: '1', name: 'My Car' },
    trip: { id: '2', startLocation: 'Office', endLocation: 'Client Meeting' },
  },
  {
    id: '4',
    date: '2023-06-10',
    category: 'Tolls',
    amount: 8.50,
    description: 'Highway toll',
    vehicle: { id: '2', name: 'Work Truck' },
    trip: { id: '3', startLocation: 'Office', endLocation: 'Supplier' },
  },
  {
    id: '5',
    date: '2023-06-05',
    category: 'Insurance',
    amount: 150.00,
    description: 'Monthly insurance premium',
    vehicle: { id: '1', name: 'My Car' },
    trip: null,
  },
];

const ExpensesScreen = () => {
  const navigation = useNavigation<ExpensesScreenNavigationProp>();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);

  // Filter expenses based on search query and selected category
  const filteredExpenses = mockExpenses.filter(expense => {
    const matchesSearch = 
      expense.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      expense.category.toLowerCase().includes(searchQuery.toLowerCase()) ||
      expense.vehicle.name.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesCategory = selectedCategory ? expense.category === selectedCategory : true;

    return matchesSearch && matchesCategory;
  });

  // Get unique categories for filter buttons
  const categories = Array.from(new Set(mockExpenses.map(expense => expense.category)));

  // Calculate total expenses
  const totalExpenses = filteredExpenses.reduce((sum, expense) => sum + expense.amount, 0);

  const renderExpenseItem = ({ item }: { item: typeof mockExpenses[0] }) => (
    <TouchableOpacity 
      style={styles.expenseItem}
      onPress={() => navigation.navigate('ExpenseDetail', { expenseId: item.id })}
    >
      <View style={styles.expenseIconContainer}>
        <Ionicons 
          name={getCategoryIcon(item.category)} 
          size={24} 
          color="#4CAF50" 
        />
      </View>
      <View style={styles.expenseInfo}>
        <Text style={styles.expenseDescription}>{item.description}</Text>
        <Text style={styles.expenseDetails}>
          {item.category} • {item.vehicle.name} • {new Date(item.date).toLocaleDateString()}
        </Text>
        {item.trip && (
          <Text style={styles.tripInfo}>
            Trip: {item.trip.startLocation} → {item.trip.endLocation}
          </Text>
        )}
      </View>
      <View style={styles.expenseAmount}>
        <Text style={styles.amountValue}>${item.amount.toFixed(2)}</Text>
        <Ionicons name="chevron-forward" size={20} color="#757575" />
      </View>
    </TouchableOpacity>
  );

  // Helper function to get icon based on expense category
  const getCategoryIcon = (category: string): string => {
    switch (category.toLowerCase()) {
      case 'fuel':
        return 'speedometer-outline';
      case 'maintenance':
        return 'construct-outline';
      case 'parking':
        return 'car-outline';
      case 'tolls':
        return 'cash-outline';
      case 'insurance':
        return 'shield-checkmark-outline';
      default:
        return 'receipt-outline';
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.searchContainer}>
        <Ionicons name="search" size={20} color="#757575" style={styles.searchIcon} />
        <TextInput
          style={styles.searchInput}
          placeholder="Search expenses..."
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
      </View>

      <View style={styles.filterContainer}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false} contentContainerStyle={styles.filterScrollContent}>
          <TouchableOpacity 
            style={[
              styles.filterButton, 
              selectedCategory === null && styles.filterButtonActive
            ]}
            onPress={() => setSelectedCategory(null)}
          >
            <Text style={[
              styles.filterButtonText,
              selectedCategory === null && styles.filterButtonTextActive
            ]}>All</Text>
          </TouchableOpacity>

          {categories.map(category => (
            <TouchableOpacity 
              key={category}
              style={[
                styles.filterButton, 
                selectedCategory === category && styles.filterButtonActive
              ]}
              onPress={() => setSelectedCategory(category === selectedCategory ? null : category)}
            >
              <Text style={[
                styles.filterButtonText,
                selectedCategory === category && styles.filterButtonTextActive
              ]}>{category}</Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>

      <View style={styles.summaryContainer}>
        <Text style={styles.summaryText}>
          Showing {filteredExpenses.length} expenses • Total: ${totalExpenses.toFixed(2)}
        </Text>
      </View>

      <FlatList
        data={filteredExpenses}
        renderItem={renderExpenseItem}
        keyExtractor={item => item.id}
        contentContainerStyle={styles.listContainer}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Ionicons name="receipt-outline" size={48} color="#CCCCCC" />
            <Text style={styles.emptyText}>No expenses found</Text>
            <Text style={styles.emptySubtext}>Try adjusting your search or filters</Text>
          </View>
        }
      />

      <TouchableOpacity 
        style={styles.addButton}
        onPress={() => navigation.navigate('AddExpense')}
      >
        <Ionicons name="add" size={24} color="#FFFFFF" />
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    margin: 15,
    paddingHorizontal: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  searchIcon: {
    marginRight: 10,
  },
  searchInput: {
    flex: 1,
    height: 40,
    fontSize: 16,
  },
  filterContainer: {
    marginHorizontal: 15,
    marginBottom: 10,
  },
  filterScrollContent: {
    paddingRight: 15,
  },
  filterButton: {
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: '#FFFFFF',
    marginRight: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 1,
    elevation: 1,
  },
  filterButtonActive: {
    backgroundColor: '#4CAF50',
  },
  filterButtonText: {
    color: '#757575',
    fontWeight: '500',
  },
  filterButtonTextActive: {
    color: '#FFFFFF',
  },
  summaryContainer: {
    marginHorizontal: 15,
    marginBottom: 10,
  },
  summaryText: {
    color: '#757575',
    fontSize: 14,
  },
  listContainer: {
    paddingHorizontal: 15,
    paddingBottom: 80, // Space for FAB
  },
  expenseItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    padding: 15,
    marginBottom: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  expenseIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#E8F5E9',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
  },
  expenseInfo: {
    flex: 1,
  },
  expenseDescription: {
    fontSize: 16,
    fontWeight: '500',
    color: '#212121',
  },
  expenseDetails: {
    fontSize: 12,
    color: '#757575',
    marginTop: 2,
  },
  tripInfo: {
    fontSize: 12,
    color: '#4CAF50',
    marginTop: 2,
  },
  expenseAmount: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  amountValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#212121',
    marginRight: 5,
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 50,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#757575',
    marginTop: 10,
  },
  emptySubtext: {
    fontSize: 14,
    color: '#9E9E9E',
    marginTop: 5,
    textAlign: 'center',
  },
  addButton: {
    position: 'absolute',
    bottom: 20,
    right: 20,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: '#4CAF50',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
});

export default ExpensesScreen;
