import React, { useState } from 'react';
import { View, Text, StyleSheet, FlatList, TouchableOpacity, TextInput } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { VehiclesScreenNavigationProp } from '../types/navigation';

const VehiclesScreen = () => {
  const navigation = useNavigation<VehiclesScreenNavigationProp>();
  const [searchQuery, setSearchQuery] = useState('');

  // Mock data - would be fetched from API in a real implementation
  const vehicles = [
    { 
      id: '1', 
      name: 'My Car', 
      make: 'Toyota', 
      model: 'Camry', 
      year: 2020,
      licensePlate: 'ABC123',
      isDefault: true,
      mpg: 32.5,
    },
    { 
      id: '2', 
      name: 'Work Truck', 
      make: 'Ford', 
      model: 'F-150', 
      year: 2019,
      licensePlate: 'XYZ789',
      isDefault: false,
      mpg: 18.2,
    },
    { 
      id: '3', 
      name: 'Family SUV', 
      make: 'Honda', 
      model: 'CR-V', 
      year: 2021,
      licensePlate: 'DEF456',
      isDefault: false,
      mpg: 28.7,
    },
  ];

  // Filter vehicles based on search query
  const filteredVehicles = vehicles.filter(vehicle => {
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      return (
        vehicle.name.toLowerCase().includes(query) ||
        vehicle.make.toLowerCase().includes(query) ||
        vehicle.model.toLowerCase().includes(query) ||
        vehicle.licensePlate.toLowerCase().includes(query) ||
        vehicle.year.toString().includes(query)
      );
    }
    return true;
  });

  const renderVehicleItem = ({ item }: { item: typeof vehicles[0] }) => (
    <TouchableOpacity 
      style={styles.vehicleItem}
      onPress={() => navigation.navigate('VehicleDetail', { vehicleId: item.id })}
    >
      <View style={styles.vehicleIconContainer}>
        <Ionicons name="car" size={24} color="#2196F3" />
        {item.isDefault && (
          <View style={styles.defaultBadge}>
            <Ionicons name="star" size={12} color="#fff" />
          </View>
        )}
      </View>
      <View style={styles.vehicleInfo}>
        <Text style={styles.vehicleName}>{item.name}</Text>
        <Text style={styles.vehicleDetails}>{item.year} {item.make} {item.model}</Text>
        <Text style={styles.vehiclePlate}>License: {item.licensePlate}</Text>
      </View>
      <View style={styles.vehicleMpg}>
        <Text style={styles.mpgValue}>{item.mpg.toFixed(1)}</Text>
        <Text style={styles.mpgLabel}>MPG</Text>
      </View>
      <Ionicons name="chevron-forward" size={20} color="#757575" />
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      <View style={styles.searchContainer}>
        <Ionicons name="search" size={20} color="#757575" style={styles.searchIcon} />
        <TextInput
          style={styles.searchInput}
          placeholder="Search vehicles..."
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
        {searchQuery ? (
          <TouchableOpacity onPress={() => setSearchQuery('')}>
            <Ionicons name="close-circle" size={20} color="#757575" />
          </TouchableOpacity>
        ) : null}
      </View>

      {filteredVehicles.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Ionicons name="car-outline" size={64} color="#CCCCCC" />
          <Text style={styles.emptyText}>No vehicles found</Text>
          <Text style={styles.emptySubtext}>
            {searchQuery 
              ? 'Try a different search term'
              : 'Add a vehicle to get started'}
          </Text>
        </View>
      ) : (
        <FlatList
          data={filteredVehicles}
          renderItem={renderVehicleItem}
          keyExtractor={item => item.id}
          contentContainerStyle={styles.listContent}
        />
      )}

      <TouchableOpacity 
        style={styles.fab}
        onPress={() => navigation.navigate('AddVehicle')}
      >
        <Ionicons name="add" size={24} color="#fff" />
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderRadius: 8,
    margin: 15,
    paddingHorizontal: 15,
    paddingVertical: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  searchIcon: {
    marginRight: 10,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    height: 40,
  },
  listContent: {
    paddingBottom: 80, // Extra space for FAB
  },
  vehicleItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 15,
    marginHorizontal: 15,
    marginBottom: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  vehicleIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
    position: 'relative',
  },
  defaultBadge: {
    position: 'absolute',
    top: -5,
    right: -5,
    backgroundColor: '#FFC107',
    borderRadius: 10,
    width: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  vehicleInfo: {
    flex: 1,
  },
  vehicleName: {
    fontSize: 16,
    fontWeight: '500',
  },
  vehicleDetails: {
    fontSize: 14,
    color: '#212121',
  },
  vehiclePlate: {
    fontSize: 12,
    color: '#757575',
    marginTop: 2,
  },
  vehicleMpg: {
    alignItems: 'center',
    marginRight: 10,
  },
  mpgValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2196F3',
  },
  mpgLabel: {
    fontSize: 12,
    color: '#757575',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#757575',
    marginTop: 20,
  },
  emptySubtext: {
    fontSize: 14,
    color: '#9E9E9E',
    textAlign: 'center',
    marginTop: 10,
  },
  fab: {
    position: 'absolute',
    bottom: 20,
    right: 20,
    backgroundColor: '#4CAF50',
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 5,
    elevation: 5,
  },
});

export default VehiclesScreen;