import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Alert, TextInput } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation, useRoute } from '@react-navigation/native';
import { VehicleDetailScreenNavigationProp, VehicleDetailScreenRouteProp } from '../types/navigation';

const VehicleDetailScreen = () => {
  const navigation = useNavigation<VehicleDetailScreenNavigationProp>();
  const route = useRoute<VehicleDetailScreenRouteProp>();
  const { vehicleId } = route.params;
  const [isEditing, setIsEditing] = useState(false);

  // Mock data - would be fetched from API in a real implementation
  const [vehicle, setVehicle] = useState({
    id: vehicleId,
    name: 'My Car',
    make: 'Toyota',
    model: 'Camry',
    year: 2020,
    licensePlate: 'ABC123',
    vin: '1HGCM82633A123456',
    mpg: 32.5,
    isDefault: true,
    purchaseDate: '2020-01-15',
    notes: 'Regular maintenance every 5,000 miles',
  });

  // State for edited values
  const [editedVehicle, setEditedVehicle] = useState({ ...vehicle });

  // Stats for this vehicle
  const vehicleStats = {
    totalTrips: 28,
    businessTrips: 18,
    personalTrips: 10,
    totalMiles: 345.8,
    businessMiles: 225.3,
    personalMiles: 120.5,
    avgMpg: 31.2,
    fuelCost: 142.75,
    maintenanceCost: 85.50,
  };

  const handleSave = () => {
    // In a real implementation, this would update the vehicle in the database
    setVehicle(editedVehicle);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setEditedVehicle({ ...vehicle });
    setIsEditing(false);
  };

  const handleSetDefault = () => {
    // In a real implementation, this would update all vehicles to set this one as default
    setVehicle({ ...vehicle, isDefault: true });
  };

  const handleDeleteVehicle = () => {
    Alert.alert(
      'Delete Vehicle',
      'Are you sure you want to delete this vehicle? All associated trips and expenses will remain in the system.',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => {
            // In a real implementation, this would delete the vehicle from the database
            navigation.goBack();
          },
        },
      ]
    );
  };

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>
          {isEditing ? 'Edit Vehicle' : vehicle.name}
        </Text>
        {!isEditing && (
          <Text style={styles.headerSubtitle}>
            {vehicle.year} {vehicle.make} {vehicle.model}
          </Text>
        )}
      </View>

      <View style={styles.card}>
        {isEditing ? (
          // Edit mode
          <>
            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Vehicle Name</Text>
              <TextInput
                style={styles.input}
                value={editedVehicle.name}
                onChangeText={(text) => setEditedVehicle({ ...editedVehicle, name: text })}
                placeholder="e.g., My Car"
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Make</Text>
              <TextInput
                style={styles.input}
                value={editedVehicle.make}
                onChangeText={(text) => setEditedVehicle({ ...editedVehicle, make: text })}
                placeholder="e.g., Toyota"
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Model</Text>
              <TextInput
                style={styles.input}
                value={editedVehicle.model}
                onChangeText={(text) => setEditedVehicle({ ...editedVehicle, model: text })}
                placeholder="e.g., Camry"
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Year</Text>
              <TextInput
                style={styles.input}
                value={editedVehicle.year.toString()}
                onChangeText={(text) => setEditedVehicle({ ...editedVehicle, year: parseInt(text) || 0 })}
                keyboardType="number-pad"
                placeholder="e.g., 2020"
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>License Plate</Text>
              <TextInput
                style={styles.input}
                value={editedVehicle.licensePlate}
                onChangeText={(text) => setEditedVehicle({ ...editedVehicle, licensePlate: text })}
                placeholder="e.g., ABC123"
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>VIN (Optional)</Text>
              <TextInput
                style={styles.input}
                value={editedVehicle.vin}
                onChangeText={(text) => setEditedVehicle({ ...editedVehicle, vin: text })}
                placeholder="e.g., 1HGCM82633A123456"
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>MPG (Optional)</Text>
              <TextInput
                style={styles.input}
                value={editedVehicle.mpg?.toString()}
                onChangeText={(text) => setEditedVehicle({ ...editedVehicle, mpg: parseFloat(text) || 0 })}
                keyboardType="decimal-pad"
                placeholder="e.g., 32.5"
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Notes (Optional)</Text>
              <TextInput
                style={[styles.input, styles.textArea]}
                value={editedVehicle.notes}
                onChangeText={(text) => setEditedVehicle({ ...editedVehicle, notes: text })}
                placeholder="Add any notes about this vehicle"
                multiline
                numberOfLines={4}
              />
            </View>

            <View style={styles.editButtons}>
              <TouchableOpacity 
                style={[styles.editButton, styles.cancelButton]}
                onPress={handleCancel}
              >
                <Text style={styles.editButtonText}>Cancel</Text>
              </TouchableOpacity>
              <TouchableOpacity 
                style={[styles.editButton, styles.saveButton]}
                onPress={handleSave}
              >
                <Text style={styles.editButtonText}>Save</Text>
              </TouchableOpacity>
            </View>
          </>
        ) : (
          // View mode
          <>
            <View style={styles.detailRow}>
              <View style={styles.detailIconContainer}>
                <Ionicons name="car-outline" size={20} color="#4CAF50" />
              </View>
              <View style={styles.detailContent}>
                <Text style={styles.detailLabel}>Vehicle Details</Text>
                <Text style={styles.detailValue}>{vehicle.year} {vehicle.make} {vehicle.model}</Text>
                <Text style={styles.detailSubvalue}>License: {vehicle.licensePlate}</Text>
                {vehicle.vin && <Text style={styles.detailSubvalue}>VIN: {vehicle.vin}</Text>}
              </View>
            </View>

            <View style={styles.detailRow}>
              <View style={styles.detailIconContainer}>
                <Ionicons name="speedometer-outline" size={20} color="#4CAF50" />
              </View>
              <View style={styles.detailContent}>
                <Text style={styles.detailLabel}>Fuel Efficiency</Text>
                <Text style={styles.detailValue}>{vehicle.mpg?.toFixed(1) || 'N/A'} MPG</Text>
              </View>
            </View>

            {vehicle.notes && (
              <View style={styles.detailRow}>
                <View style={styles.detailIconContainer}>
                  <Ionicons name="document-text-outline" size={20} color="#4CAF50" />
                </View>
                <View style={styles.detailContent}>
                  <Text style={styles.detailLabel}>Notes</Text>
                  <Text style={styles.detailValue}>{vehicle.notes}</Text>
                </View>
              </View>
            )}

            <View style={styles.defaultContainer}>
              {vehicle.isDefault ? (
                <View style={styles.defaultStatus}>
                  <Ionicons name="star" size={20} color="#FFC107" />
                  <Text style={styles.defaultText}>This is your default vehicle</Text>
                </View>
              ) : (
                <TouchableOpacity 
                  style={styles.defaultButton}
                  onPress={handleSetDefault}
                >
                  <Ionicons name="star-outline" size={20} color="#757575" />
                  <Text style={styles.defaultButtonText}>Set as Default Vehicle</Text>
                </TouchableOpacity>
              )}
            </View>
          </>
        )}
      </View>

      {!isEditing && (
        <>
          <View style={styles.card}>
            <Text style={styles.cardTitle}>Vehicle Statistics</Text>
            
            <View style={styles.statsRow}>
              <View style={styles.statItem}>
                <Text style={styles.statValue}>{vehicleStats.totalTrips}</Text>
                <Text style={styles.statLabel}>Total Trips</Text>
              </View>
              <View style={styles.statItem}>
                <Text style={styles.statValue}>{vehicleStats.totalMiles.toFixed(1)}</Text>
                <Text style={styles.statLabel}>Total Miles</Text>
              </View>
              <View style={styles.statItem}>
                <Text style={styles.statValue}>{vehicleStats.avgMpg.toFixed(1)}</Text>
                <Text style={styles.statLabel}>Avg MPG</Text>
              </View>
            </View>
            
            <View style={styles.statsRow}>
              <View style={styles.statItem}>
                <Text style={styles.statValue}>{vehicleStats.businessMiles.toFixed(1)}</Text>
                <Text style={styles.statLabel}>Business Miles</Text>
              </View>
              <View style={styles.statItem}>
                <Text style={styles.statValue}>${vehicleStats.fuelCost.toFixed(2)}</Text>
                <Text style={styles.statLabel}>Fuel Cost</Text>
              </View>
              <View style={styles.statItem}>
                <Text style={styles.statValue}>${vehicleStats.maintenanceCost.toFixed(2)}</Text>
                <Text style={styles.statLabel}>Maintenance</Text>
              </View>
            </View>
          </View>

          <View style={styles.actionButtons}>
            <TouchableOpacity 
              style={styles.actionButton}
              onPress={() => setIsEditing(true)}
            >
              <Ionicons name="create-outline" size={20} color="#fff" />
              <Text style={styles.actionButtonText}>Edit Vehicle</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={[styles.actionButton, styles.deleteButton]}
              onPress={handleDeleteVehicle}
            >
              <Ionicons name="trash-outline" size={20} color="#fff" />
              <Text style={styles.actionButtonText}>Delete Vehicle</Text>
            </TouchableOpacity>
          </View>
        </>
      )}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    padding: 20,
    backgroundColor: '#4CAF50',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#fff',
  },
  headerSubtitle: {
    fontSize: 14,
    color: '#e0e0e0',
    marginTop: 5,
  },
  card: {
    backgroundColor: '#fff',
    borderRadius: 10,
    margin: 15,
    padding: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 15,
  },
  detailRow: {
    flexDirection: 'row',
    marginBottom: 15,
  },
  detailIconContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
  },
  detailContent: {
    flex: 1,
  },
  detailLabel: {
    fontSize: 12,
    color: '#757575',
  },
  detailValue: {
    fontSize: 16,
    fontWeight: '500',
  },
  detailSubvalue: {
    fontSize: 12,
    color: '#757575',
    marginTop: 2,
  },
  defaultContainer: {
    marginTop: 10,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
    paddingTop: 15,
  },
  defaultStatus: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  defaultText: {
    marginLeft: 10,
    fontSize: 14,
    color: '#757575',
  },
  defaultButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 10,
    borderRadius: 5,
    backgroundColor: '#f0f0f0',
  },
  defaultButtonText: {
    marginLeft: 10,
    fontSize: 14,
    color: '#757575',
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 15,
  },
  statItem: {
    alignItems: 'center',
    flex: 1,
  },
  statValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2196F3',
  },
  statLabel: {
    fontSize: 12,
    color: '#757575',
    textAlign: 'center',
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    margin: 15,
    marginTop: 0,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#4CAF50',
    borderRadius: 8,
    padding: 15,
    flex: 0.48,
  },
  deleteButton: {
    backgroundColor: '#F44336',
  },
  actionButtonText: {
    color: '#fff',
    fontWeight: 'bold',
    marginLeft: 10,
  },
  inputGroup: {
    marginBottom: 15,
  },
  inputLabel: {
    fontSize: 14,
    color: '#757575',
    marginBottom: 5,
  },
  input: {
    backgroundColor: '#f5f5f5',
    borderRadius: 5,
    padding: 10,
    fontSize: 16,
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  editButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 15,
  },
  editButton: {
    flex: 0.48,
    padding: 15,
    borderRadius: 8,
    alignItems: 'center',
  },
  saveButton: {
    backgroundColor: '#4CAF50',
  },
  cancelButton: {
    backgroundColor: '#9E9E9E',
  },
  editButtonText: {
    color: '#fff',
    fontWeight: 'bold',
  },
});

export default VehicleDetailScreen;