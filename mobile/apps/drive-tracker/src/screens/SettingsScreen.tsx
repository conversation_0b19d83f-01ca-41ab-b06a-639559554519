import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Switch, Alert } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { SettingsScreenNavigationProp } from '../types/navigation';

const SettingsScreen = () => {
  const navigation = useNavigation<SettingsScreenNavigationProp>();
  
  // Settings state
  const [settings, setSettings] = useState({
    notifications: {
      tripDetection: true,
      tripSummaries: true,
      taxReminders: true,
      expenseReminders: false,
      systemAnnouncements: true,
    },
    tracking: {
      autoDetectTrips: true,
      trackInBackground: true,
      highAccuracyMode: false,
      recordStops: true,
      minimumTripDistance: 0.1, // miles
      minimumTripDuration: 1, // minutes
    },
    privacy: {
      shareAnalytics: true,
      shareLocation: false,
      dataRetention: '12 months',
    },
    appearance: {
      darkMode: false,
      compactView: false,
      showMileageInKm: false,
      defaultTripCategory: 'business',
    },
    sync: {
      autoSync: true,
      syncOnWifiOnly: false,
      syncFrequency: 'hourly',
    },
    export: {
      defaultFormat: 'PDF',
      includeRouteMap: true,
      includeTripNotes: true,
    },
  });

  // Toggle setting function
  const toggleSetting = (category, setting) => {
    setSettings(prevSettings => ({
      ...prevSettings,
      [category]: {
        ...prevSettings[category],
        [setting]: !prevSettings[category][setting]
      }
    }));
  };

  // Clear app data function
  const clearAppData = () => {
    Alert.alert(
      'Clear App Data',
      'Are you sure you want to clear all app data? This action cannot be undone.',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Clear Data',
          onPress: () => {
            // In a real implementation, this would clear all local data
            Alert.alert('Data Cleared', 'All app data has been cleared.');
          },
          style: 'destructive',
        },
      ]
    );
  };

  // Reset settings function
  const resetSettings = () => {
    Alert.alert(
      'Reset Settings',
      'Are you sure you want to reset all settings to default values?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Reset',
          onPress: () => {
            // In a real implementation, this would reset settings to defaults
            Alert.alert('Settings Reset', 'All settings have been reset to default values.');
          },
          style: 'destructive',
        },
      ]
    );
  };

  return (
    <ScrollView style={styles.container}>
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Notifications</Text>
        
        <View style={styles.settingItem}>
          <View style={styles.settingInfo}>
            <Text style={styles.settingText}>Trip Detection</Text>
            <Text style={styles.settingDescription}>Notify when a new trip is detected</Text>
          </View>
          <Switch
            value={settings.notifications.tripDetection}
            onValueChange={() => toggleSetting('notifications', 'tripDetection')}
            trackColor={{ false: '#767577', true: '#81b0ff' }}
            thumbColor={settings.notifications.tripDetection ? '#4CAF50' : '#f4f3f4'}
          />
        </View>
        
        <View style={styles.settingItem}>
          <View style={styles.settingInfo}>
            <Text style={styles.settingText}>Trip Summaries</Text>
            <Text style={styles.settingDescription}>Receive daily and weekly trip summaries</Text>
          </View>
          <Switch
            value={settings.notifications.tripSummaries}
            onValueChange={() => toggleSetting('notifications', 'tripSummaries')}
            trackColor={{ false: '#767577', true: '#81b0ff' }}
            thumbColor={settings.notifications.tripSummaries ? '#4CAF50' : '#f4f3f4'}
          />
        </View>
        
        <View style={styles.settingItem}>
          <View style={styles.settingInfo}>
            <Text style={styles.settingText}>Tax Reminders</Text>
            <Text style={styles.settingDescription}>Receive quarterly and annual tax filing reminders</Text>
          </View>
          <Switch
            value={settings.notifications.taxReminders}
            onValueChange={() => toggleSetting('notifications', 'taxReminders')}
            trackColor={{ false: '#767577', true: '#81b0ff' }}
            thumbColor={settings.notifications.taxReminders ? '#4CAF50' : '#f4f3f4'}
          />
        </View>
        
        <View style={styles.settingItem}>
          <View style={styles.settingInfo}>
            <Text style={styles.settingText}>Expense Reminders</Text>
            <Text style={styles.settingDescription}>Reminders to add expenses for your trips</Text>
          </View>
          <Switch
            value={settings.notifications.expenseReminders}
            onValueChange={() => toggleSetting('notifications', 'expenseReminders')}
            trackColor={{ false: '#767577', true: '#81b0ff' }}
            thumbColor={settings.notifications.expenseReminders ? '#4CAF50' : '#f4f3f4'}
          />
        </View>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Tracking</Text>
        
        <View style={styles.settingItem}>
          <View style={styles.settingInfo}>
            <Text style={styles.settingText}>Auto-detect Trips</Text>
            <Text style={styles.settingDescription}>Automatically detect and record driving activity</Text>
          </View>
          <Switch
            value={settings.tracking.autoDetectTrips}
            onValueChange={() => toggleSetting('tracking', 'autoDetectTrips')}
            trackColor={{ false: '#767577', true: '#81b0ff' }}
            thumbColor={settings.tracking.autoDetectTrips ? '#4CAF50' : '#f4f3f4'}
          />
        </View>
        
        <View style={styles.settingItem}>
          <View style={styles.settingInfo}>
            <Text style={styles.settingText}>Track in Background</Text>
            <Text style={styles.settingDescription}>Continue tracking when app is in background</Text>
          </View>
          <Switch
            value={settings.tracking.trackInBackground}
            onValueChange={() => toggleSetting('tracking', 'trackInBackground')}
            trackColor={{ false: '#767577', true: '#81b0ff' }}
            thumbColor={settings.tracking.trackInBackground ? '#4CAF50' : '#f4f3f4'}
          />
        </View>
        
        <View style={styles.settingItem}>
          <View style={styles.settingInfo}>
            <Text style={styles.settingText}>High Accuracy Mode</Text>
            <Text style={styles.settingDescription}>Use GPS for more accurate tracking (higher battery usage)</Text>
          </View>
          <Switch
            value={settings.tracking.highAccuracyMode}
            onValueChange={() => toggleSetting('tracking', 'highAccuracyMode')}
            trackColor={{ false: '#767577', true: '#81b0ff' }}
            thumbColor={settings.tracking.highAccuracyMode ? '#4CAF50' : '#f4f3f4'}
          />
        </View>
        
        <View style={styles.settingItem}>
          <View style={styles.settingInfo}>
            <Text style={styles.settingText}>Record Stops</Text>
            <Text style={styles.settingDescription}>Record stops during trips</Text>
          </View>
          <Switch
            value={settings.tracking.recordStops}
            onValueChange={() => toggleSetting('tracking', 'recordStops')}
            trackColor={{ false: '#767577', true: '#81b0ff' }}
            thumbColor={settings.tracking.recordStops ? '#4CAF50' : '#f4f3f4'}
          />
        </View>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Appearance</Text>
        
        <View style={styles.settingItem}>
          <View style={styles.settingInfo}>
            <Text style={styles.settingText}>Dark Mode</Text>
            <Text style={styles.settingDescription}>Use dark theme throughout the app</Text>
          </View>
          <Switch
            value={settings.appearance.darkMode}
            onValueChange={() => toggleSetting('appearance', 'darkMode')}
            trackColor={{ false: '#767577', true: '#81b0ff' }}
            thumbColor={settings.appearance.darkMode ? '#4CAF50' : '#f4f3f4'}
          />
        </View>
        
        <View style={styles.settingItem}>
          <View style={styles.settingInfo}>
            <Text style={styles.settingText}>Compact View</Text>
            <Text style={styles.settingDescription}>Show more items per screen with compact layout</Text>
          </View>
          <Switch
            value={settings.appearance.compactView}
            onValueChange={() => toggleSetting('appearance', 'compactView')}
            trackColor={{ false: '#767577', true: '#81b0ff' }}
            thumbColor={settings.appearance.compactView ? '#4CAF50' : '#f4f3f4'}
          />
        </View>
        
        <View style={styles.settingItem}>
          <View style={styles.settingInfo}>
            <Text style={styles.settingText}>Show Mileage in Kilometers</Text>
            <Text style={styles.settingDescription}>Display distances in kilometers instead of miles</Text>
          </View>
          <Switch
            value={settings.appearance.showMileageInKm}
            onValueChange={() => toggleSetting('appearance', 'showMileageInKm')}
            trackColor={{ false: '#767577', true: '#81b0ff' }}
            thumbColor={settings.appearance.showMileageInKm ? '#4CAF50' : '#f4f3f4'}
          />
        </View>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Synchronization</Text>
        
        <View style={styles.settingItem}>
          <View style={styles.settingInfo}>
            <Text style={styles.settingText}>Auto Sync</Text>
            <Text style={styles.settingDescription}>Automatically sync data with the server</Text>
          </View>
          <Switch
            value={settings.sync.autoSync}
            onValueChange={() => toggleSetting('sync', 'autoSync')}
            trackColor={{ false: '#767577', true: '#81b0ff' }}
            thumbColor={settings.sync.autoSync ? '#4CAF50' : '#f4f3f4'}
          />
        </View>
        
        <View style={styles.settingItem}>
          <View style={styles.settingInfo}>
            <Text style={styles.settingText}>Sync on Wi-Fi Only</Text>
            <Text style={styles.settingDescription}>Only sync when connected to Wi-Fi</Text>
          </View>
          <Switch
            value={settings.sync.syncOnWifiOnly}
            onValueChange={() => toggleSetting('sync', 'syncOnWifiOnly')}
            trackColor={{ false: '#767577', true: '#81b0ff' }}
            thumbColor={settings.sync.syncOnWifiOnly ? '#4CAF50' : '#f4f3f4'}
          />
        </View>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Data Management</Text>
        
        <TouchableOpacity style={styles.actionButton} onPress={clearAppData}>
          <Ionicons name="trash-outline" size={20} color="#FF5252" style={styles.actionIcon} />
          <Text style={styles.actionButtonTextDanger}>Clear App Data</Text>
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.actionButton} onPress={resetSettings}>
          <Ionicons name="refresh-outline" size={20} color="#FF9800" style={styles.actionIcon} />
          <Text style={styles.actionButtonTextWarning}>Reset Settings</Text>
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.actionButton}>
          <Ionicons name="cloud-download-outline" size={20} color="#4CAF50" style={styles.actionIcon} />
          <Text style={styles.actionButtonText}>Export All Data</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.footer}>
        <Text style={styles.versionText}>Drive Tracker v1.0.0</Text>
        <Text style={styles.buildText}>Build 2023.06.23</Text>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  section: {
    backgroundColor: '#FFFFFF',
    borderRadius: 10,
    margin: 15,
    marginBottom: 10,
    padding: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 15,
    color: '#212121',
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  settingInfo: {
    flex: 1,
    marginRight: 10,
  },
  settingText: {
    fontSize: 16,
    color: '#212121',
    marginBottom: 3,
  },
  settingDescription: {
    fontSize: 12,
    color: '#757575',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  actionIcon: {
    marginRight: 15,
  },
  actionButtonText: {
    fontSize: 16,
    color: '#4CAF50',
  },
  actionButtonTextWarning: {
    fontSize: 16,
    color: '#FF9800',
  },
  actionButtonTextDanger: {
    fontSize: 16,
    color: '#FF5252',
  },
  footer: {
    alignItems: 'center',
    marginVertical: 20,
  },
  versionText: {
    fontSize: 14,
    color: '#757575',
    marginBottom: 5,
  },
  buildText: {
    fontSize: 12,
    color: '#9E9E9E',
  },
});

export default SettingsScreen;