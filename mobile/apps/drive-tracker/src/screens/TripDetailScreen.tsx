import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Image, Alert } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation, useRoute } from '@react-navigation/native';
import { TripDetailScreenNavigationProp, TripDetailScreenRouteProp } from '../types/navigation';

const TripDetailScreen = () => {
  const navigation = useNavigation<TripDetailScreenNavigationProp>();
  const route = useRoute<TripDetailScreenRouteProp>();
  const { tripId } = route.params;
  const [tripCategory, setTripCategory] = useState<'business' | 'personal'>('business');

  // Mock data - would be fetched from API in a real implementation
  const trip = {
    id: tripId,
    date: '2023-06-20',
    startTime: '08:30 AM',
    endTime: '08:55 AM',
    startLocation: 'Home',
    startAddress: '123 Main St, Anytown, CA 12345',
    endLocation: 'Office',
    endAddress: '456 Business Ave, Anytown, CA 12345',
    distance: 12.5,
    duration: 25, // minutes
    category: tripCategory,
    purpose: 'Commute to work',
    notes: 'Traffic was light today',
    vehicle: {
      id: '1',
      name: 'My Car',
      make: 'Toyota',
      model: 'Camry',
      year: 2020,
    },
    mapImageUrl: 'https://via.placeholder.com/600x300',
  };

  const handleCategoryChange = (category: 'business' | 'personal') => {
    setTripCategory(category);
    // In a real implementation, this would update the trip in the database
  };

  const handleAddExpense = () => {
    navigation.navigate('AddExpense', { tripId });
  };

  const handleDeleteTrip = () => {
    Alert.alert(
      'Delete Trip',
      'Are you sure you want to delete this trip? This action cannot be undone.',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => {
            // In a real implementation, this would delete the trip from the database
            navigation.goBack();
          },
        },
      ]
    );
  };

  return (
    <ScrollView style={styles.container}>
      <View style={styles.mapContainer}>
        <Image source={{ uri: trip.mapImageUrl }} style={styles.mapImage} />
      </View>

      <View style={styles.tripHeader}>
        <Text style={styles.tripTitle}>{trip.startLocation} → {trip.endLocation}</Text>
        <Text style={styles.tripDate}>
          {new Date(trip.date).toLocaleDateString('en-US', { weekday: 'long', month: 'long', day: 'numeric' })}
        </Text>
      </View>

      <View style={styles.card}>
        <Text style={styles.cardTitle}>Trip Details</Text>
        
        <View style={styles.detailRow}>
          <View style={styles.detailIconContainer}>
            <Ionicons name="time-outline" size={20} color="#4CAF50" />
          </View>
          <View style={styles.detailContent}>
            <Text style={styles.detailLabel}>Time</Text>
            <Text style={styles.detailValue}>{trip.startTime} - {trip.endTime}</Text>
          </View>
        </View>

        <View style={styles.detailRow}>
          <View style={styles.detailIconContainer}>
            <Ionicons name="location-outline" size={20} color="#4CAF50" />
          </View>
          <View style={styles.detailContent}>
            <Text style={styles.detailLabel}>Start Location</Text>
            <Text style={styles.detailValue}>{trip.startLocation}</Text>
            <Text style={styles.detailSubvalue}>{trip.startAddress}</Text>
          </View>
        </View>

        <View style={styles.detailRow}>
          <View style={styles.detailIconContainer}>
            <Ionicons name="location" size={20} color="#4CAF50" />
          </View>
          <View style={styles.detailContent}>
            <Text style={styles.detailLabel}>End Location</Text>
            <Text style={styles.detailValue}>{trip.endLocation}</Text>
            <Text style={styles.detailSubvalue}>{trip.endAddress}</Text>
          </View>
        </View>

        <View style={styles.detailRow}>
          <View style={styles.detailIconContainer}>
            <Ionicons name="speedometer-outline" size={20} color="#4CAF50" />
          </View>
          <View style={styles.detailContent}>
            <Text style={styles.detailLabel}>Distance</Text>
            <Text style={styles.detailValue}>{trip.distance.toFixed(1)} miles</Text>
          </View>
        </View>

        <View style={styles.detailRow}>
          <View style={styles.detailIconContainer}>
            <Ionicons name="hourglass-outline" size={20} color="#4CAF50" />
          </View>
          <View style={styles.detailContent}>
            <Text style={styles.detailLabel}>Duration</Text>
            <Text style={styles.detailValue}>{trip.duration} minutes</Text>
          </View>
        </View>

        <View style={styles.detailRow}>
          <View style={styles.detailIconContainer}>
            <Ionicons name="car-outline" size={20} color="#4CAF50" />
          </View>
          <View style={styles.detailContent}>
            <Text style={styles.detailLabel}>Vehicle</Text>
            <Text style={styles.detailValue}>{trip.vehicle.name}</Text>
            <Text style={styles.detailSubvalue}>{trip.vehicle.year} {trip.vehicle.make} {trip.vehicle.model}</Text>
          </View>
        </View>
      </View>

      <View style={styles.card}>
        <Text style={styles.cardTitle}>Trip Category</Text>
        <Text style={styles.cardSubtitle}>Categorize this trip for tax purposes</Text>
        
        <View style={styles.categoryButtons}>
          <TouchableOpacity 
            style={[
              styles.categoryButton, 
              tripCategory === 'business' && styles.categoryButtonActive
            ]}
            onPress={() => handleCategoryChange('business')}
          >
            <Ionicons 
              name="briefcase-outline" 
              size={24} 
              color={tripCategory === 'business' ? '#fff' : '#2196F3'} 
            />
            <Text style={[
              styles.categoryButtonText, 
              tripCategory === 'business' && styles.categoryButtonTextActive
            ]}>
              Business
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={[
              styles.categoryButton, 
              tripCategory === 'personal' && styles.categoryButtonActive,
              tripCategory === 'personal' && { backgroundColor: '#FF9800' }
            ]}
            onPress={() => handleCategoryChange('personal')}
          >
            <Ionicons 
              name="home-outline" 
              size={24} 
              color={tripCategory === 'personal' ? '#fff' : '#FF9800'} 
            />
            <Text style={[
              styles.categoryButtonText, 
              tripCategory === 'personal' && styles.categoryButtonTextActive
            ]}>
              Personal
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      {trip.purpose || trip.notes ? (
        <View style={styles.card}>
          <Text style={styles.cardTitle}>Notes</Text>
          
          {trip.purpose && (
            <View style={styles.noteItem}>
              <Text style={styles.noteLabel}>Purpose:</Text>
              <Text style={styles.noteText}>{trip.purpose}</Text>
            </View>
          )}
          
          {trip.notes && (
            <View style={styles.noteItem}>
              <Text style={styles.noteLabel}>Additional Notes:</Text>
              <Text style={styles.noteText}>{trip.notes}</Text>
            </View>
          )}
        </View>
      ) : null}

      <View style={styles.actionButtons}>
        <TouchableOpacity 
          style={styles.actionButton}
          onPress={handleAddExpense}
        >
          <Ionicons name="cash-outline" size={20} color="#fff" />
          <Text style={styles.actionButtonText}>Add Expense</Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={[styles.actionButton, styles.deleteButton]}
          onPress={handleDeleteTrip}
        >
          <Ionicons name="trash-outline" size={20} color="#fff" />
          <Text style={styles.actionButtonText}>Delete Trip</Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  mapContainer: {
    width: '100%',
    height: 200,
    backgroundColor: '#e0e0e0',
  },
  mapImage: {
    width: '100%',
    height: '100%',
  },
  tripHeader: {
    padding: 20,
    backgroundColor: '#4CAF50',
  },
  tripTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#fff',
  },
  tripDate: {
    fontSize: 14,
    color: '#e0e0e0',
    marginTop: 5,
  },
  card: {
    backgroundColor: '#fff',
    borderRadius: 10,
    margin: 15,
    padding: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  cardSubtitle: {
    fontSize: 14,
    color: '#757575',
    marginBottom: 15,
  },
  detailRow: {
    flexDirection: 'row',
    marginBottom: 15,
  },
  detailIconContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
  },
  detailContent: {
    flex: 1,
  },
  detailLabel: {
    fontSize: 12,
    color: '#757575',
  },
  detailValue: {
    fontSize: 16,
    fontWeight: '500',
  },
  detailSubvalue: {
    fontSize: 12,
    color: '#757575',
    marginTop: 2,
  },
  categoryButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  categoryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#f0f0f0',
    borderRadius: 8,
    padding: 15,
    flex: 0.48,
  },
  categoryButtonActive: {
    backgroundColor: '#2196F3',
  },
  categoryButtonText: {
    fontWeight: 'bold',
    marginLeft: 10,
  },
  categoryButtonTextActive: {
    color: '#fff',
  },
  noteItem: {
    marginBottom: 10,
  },
  noteLabel: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 3,
  },
  noteText: {
    fontSize: 14,
    color: '#212121',
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    margin: 15,
    marginTop: 0,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#4CAF50',
    borderRadius: 8,
    padding: 15,
    flex: 0.48,
  },
  deleteButton: {
    backgroundColor: '#F44336',
  },
  actionButtonText: {
    color: '#fff',
    fontWeight: 'bold',
    marginLeft: 10,
  },
});

export default TripDetailScreen;