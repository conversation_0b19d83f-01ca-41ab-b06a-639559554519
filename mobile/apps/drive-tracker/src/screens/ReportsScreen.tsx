import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, ActivityIndicator } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { ReportsScreenNavigationProp } from '../types/navigation';

// Mock data - would be fetched from API in a real implementation
const mockReportData = {
  currentYear: 2023,
  availableYears: [2023, 2022, 2021],
  availableQuarters: [
    { id: 'Q1-2023', label: 'Q1 2023', startDate: '2023-01-01', endDate: '2023-03-31' },
    { id: 'Q2-2023', label: 'Q2 2023', startDate: '2023-04-01', endDate: '2023-06-30' },
    { id: 'Q3-2023', label: 'Q3 2023', startDate: '2023-07-01', endDate: '2023-09-30' },
    { id: 'Q4-2023', label: 'Q4 2023', startDate: '2023-10-01', endDate: '2023-12-31' },
  ],
  mileageSummary: {
    businessMiles: 1245.8,
    personalMiles: 678.2,
    totalMiles: 1924.0,
    standardRate: 0.655, // 2023 IRS standard mileage rate
    potentialDeduction: 816.00,
  },
  monthlyBreakdown: [
    { month: 'January', businessMiles: 145.2, personalMiles: 78.5 },
    { month: 'February', businessMiles: 132.8, personalMiles: 92.3 },
    { month: 'March', businessMiles: 167.5, personalMiles: 85.7 },
    { month: 'April', businessMiles: 178.9, personalMiles: 102.4 },
    { month: 'May', businessMiles: 201.3, personalMiles: 88.6 },
    { month: 'June', businessMiles: 420.1, personalMiles: 230.7 },
  ],
  expenseSummary: {
    fuel: 678.45,
    maintenance: 345.20,
    insurance: 450.00,
    parking: 120.75,
    tolls: 85.50,
    total: 1679.90,
  },
};

const ReportsScreen = () => {
  const navigation = useNavigation<ReportsScreenNavigationProp>();
  const [selectedYear, setSelectedYear] = useState(mockReportData.currentYear);
  const [selectedPeriod, setSelectedPeriod] = useState(mockReportData.availableQuarters[1].id); // Q2 2023
  const [isGenerating, setIsGenerating] = useState(false);
  const [activeTab, setActiveTab] = useState('mileage'); // 'mileage' or 'expenses'

  // Get the selected quarter data
  const selectedQuarter = mockReportData.availableQuarters.find(q => q.id === selectedPeriod);

  // Function to generate and download report
  const generateReport = () => {
    setIsGenerating(true);
    // Simulate report generation
    setTimeout(() => {
      setIsGenerating(false);
      alert('Report generated and saved to your device.');
    }, 2000);
  };

  // Function to share report
  const shareReport = () => {
    alert('Report shared via email.');
  };

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Tax Reports</Text>
        <Text style={styles.headerSubtitle}>Generate tax-ready reports for your business mileage and expenses</Text>
      </View>

      <View style={styles.periodSelector}>
        <Text style={styles.sectionTitle}>Report Period</Text>
        
        <View style={styles.yearSelector}>
          <Text style={styles.selectorLabel}>Year:</Text>
          <View style={styles.yearButtons}>
            {mockReportData.availableYears.map(year => (
              <TouchableOpacity
                key={year}
                style={[
                  styles.yearButton,
                  selectedYear === year && styles.selectedYearButton
                ]}
                onPress={() => setSelectedYear(year)}
              >
                <Text style={[
                  styles.yearButtonText,
                  selectedYear === year && styles.selectedYearButtonText
                ]}>{year}</Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        <View style={styles.quarterSelector}>
          <Text style={styles.selectorLabel}>Period:</Text>
          <ScrollView 
            horizontal 
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.quarterButtons}
          >
            {mockReportData.availableQuarters.map(quarter => (
              <TouchableOpacity
                key={quarter.id}
                style={[
                  styles.quarterButton,
                  selectedPeriod === quarter.id && styles.selectedQuarterButton
                ]}
                onPress={() => setSelectedPeriod(quarter.id)}
              >
                <Text style={[
                  styles.quarterButtonText,
                  selectedPeriod === quarter.id && styles.selectedQuarterButtonText
                ]}>{quarter.label}</Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>
      </View>

      <View style={styles.tabSelector}>
        <TouchableOpacity
          style={[styles.tabButton, activeTab === 'mileage' && styles.activeTabButton]}
          onPress={() => setActiveTab('mileage')}
        >
          <Ionicons 
            name="speedometer-outline" 
            size={20} 
            color={activeTab === 'mileage' ? '#FFFFFF' : '#757575'} 
          />
          <Text style={[
            styles.tabButtonText,
            activeTab === 'mileage' && styles.activeTabButtonText
          ]}>Mileage</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.tabButton, activeTab === 'expenses' && styles.activeTabButton]}
          onPress={() => setActiveTab('expenses')}
        >
          <Ionicons 
            name="cash-outline" 
            size={20} 
            color={activeTab === 'expenses' ? '#FFFFFF' : '#757575'} 
          />
          <Text style={[
            styles.tabButtonText,
            activeTab === 'expenses' && styles.activeTabButtonText
          ]}>Expenses</Text>
        </TouchableOpacity>
      </View>

      {activeTab === 'mileage' ? (
        <View style={styles.reportContent}>
          <View style={styles.summaryCard}>
            <Text style={styles.summaryTitle}>Mileage Summary</Text>
            <Text style={styles.summarySubtitle}>
              {selectedQuarter?.startDate} to {selectedQuarter?.endDate}
            </Text>
            
            <View style={styles.summaryRow}>
              <View style={styles.summaryItem}>
                <Text style={styles.summaryValue}>{mockReportData.mileageSummary.businessMiles.toFixed(1)}</Text>
                <Text style={styles.summaryLabel}>Business Miles</Text>
              </View>
              <View style={styles.summaryItem}>
                <Text style={styles.summaryValue}>{mockReportData.mileageSummary.personalMiles.toFixed(1)}</Text>
                <Text style={styles.summaryLabel}>Personal Miles</Text>
              </View>
              <View style={styles.summaryItem}>
                <Text style={styles.summaryValue}>{mockReportData.mileageSummary.totalMiles.toFixed(1)}</Text>
                <Text style={styles.summaryLabel}>Total Miles</Text>
              </View>
            </View>
            
            <View style={styles.deductionContainer}>
              <View style={styles.deductionItem}>
                <Text style={styles.deductionLabel}>Standard Mileage Rate:</Text>
                <Text style={styles.deductionValue}>${mockReportData.mileageSummary.standardRate.toFixed(3)}/mile</Text>
              </View>
              <View style={styles.deductionItem}>
                <Text style={styles.deductionLabel}>Potential Tax Deduction:</Text>
                <Text style={styles.deductionValue}>${mockReportData.mileageSummary.potentialDeduction.toFixed(2)}</Text>
              </View>
            </View>
          </View>

          <View style={styles.monthlyBreakdownCard}>
            <Text style={styles.cardTitle}>Monthly Breakdown</Text>
            
            {mockReportData.monthlyBreakdown.map((month, index) => (
              <View key={month.month} style={[
                styles.monthRow,
                index < mockReportData.monthlyBreakdown.length - 1 && styles.monthRowBorder
              ]}>
                <Text style={styles.monthName}>{month.month}</Text>
                <View style={styles.monthData}>
                  <View style={styles.mileageItem}>
                    <Text style={styles.mileageValue}>{month.businessMiles.toFixed(1)}</Text>
                    <Text style={styles.mileageLabel}>Business</Text>
                  </View>
                  <View style={styles.mileageItem}>
                    <Text style={styles.mileageValue}>{month.personalMiles.toFixed(1)}</Text>
                    <Text style={styles.mileageLabel}>Personal</Text>
                  </View>
                  <View style={styles.mileageItem}>
                    <Text style={styles.mileageValue}>{(month.businessMiles + month.personalMiles).toFixed(1)}</Text>
                    <Text style={styles.mileageLabel}>Total</Text>
                  </View>
                </View>
              </View>
            ))}
          </View>
        </View>
      ) : (
        <View style={styles.reportContent}>
          <View style={styles.summaryCard}>
            <Text style={styles.summaryTitle}>Expense Summary</Text>
            <Text style={styles.summarySubtitle}>
              {selectedQuarter?.startDate} to {selectedQuarter?.endDate}
            </Text>
            
            <View style={styles.expenseSummaryContainer}>
              {Object.entries(mockReportData.expenseSummary).map(([category, amount]) => {
                if (category === 'total') return null;
                return (
                  <View key={category} style={styles.expenseCategory}>
                    <Text style={styles.expenseCategoryName}>{category.charAt(0).toUpperCase() + category.slice(1)}</Text>
                    <Text style={styles.expenseCategoryAmount}>${amount.toFixed(2)}</Text>
                  </View>
                );
              })}
              
              <View style={styles.expenseTotalRow}>
                <Text style={styles.expenseTotalLabel}>Total Expenses:</Text>
                <Text style={styles.expenseTotalValue}>${mockReportData.expenseSummary.total.toFixed(2)}</Text>
              </View>
            </View>
          </View>
        </View>
      )}

      <View style={styles.actionButtons}>
        <TouchableOpacity 
          style={styles.actionButton}
          onPress={generateReport}
          disabled={isGenerating}
        >
          {isGenerating ? (
            <ActivityIndicator size="small" color="#FFFFFF" />
          ) : (
            <>
              <Ionicons name="download-outline" size={20} color="#FFFFFF" />
              <Text style={styles.actionButtonText}>Generate PDF Report</Text>
            </>
          )}
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={[styles.actionButton, styles.shareButton]}
          onPress={shareReport}
        >
          <Ionicons name="share-outline" size={20} color="#FFFFFF" />
          <Text style={styles.actionButtonText}>Share Report</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.infoCard}>
        <Ionicons name="information-circle-outline" size={24} color="#4CAF50" />
        <View style={styles.infoContent}>
          <Text style={styles.infoTitle}>Tax Information</Text>
          <Text style={styles.infoText}>
            The standard mileage rate for 2023 is $0.655 per mile for business use. 
            Keep these reports for your tax records. Consult with a tax professional 
            for specific advice regarding your tax situation.
          </Text>
        </View>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    backgroundColor: '#4CAF50',
    padding: 20,
    paddingBottom: 30,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  headerSubtitle: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.8)',
    marginTop: 5,
  },
  periodSelector: {
    backgroundColor: '#FFFFFF',
    borderRadius: 10,
    margin: 15,
    marginTop: -15,
    padding: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  yearSelector: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 15,
  },
  selectorLabel: {
    fontSize: 16,
    fontWeight: '500',
    marginRight: 10,
    width: 60,
  },
  yearButtons: {
    flexDirection: 'row',
  },
  yearButton: {
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: '#F0F0F0',
    marginRight: 10,
  },
  selectedYearButton: {
    backgroundColor: '#4CAF50',
  },
  yearButtonText: {
    color: '#757575',
    fontWeight: '500',
  },
  selectedYearButtonText: {
    color: '#FFFFFF',
  },
  quarterSelector: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  quarterButtons: {
    paddingRight: 15,
  },
  quarterButton: {
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: '#F0F0F0',
    marginRight: 10,
  },
  selectedQuarterButton: {
    backgroundColor: '#4CAF50',
  },
  quarterButtonText: {
    color: '#757575',
    fontWeight: '500',
  },
  selectedQuarterButtonText: {
    color: '#FFFFFF',
  },
  tabSelector: {
    flexDirection: 'row',
    marginHorizontal: 15,
    marginBottom: 15,
  },
  tabButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#F0F0F0',
    borderRadius: 8,
    paddingVertical: 10,
    paddingHorizontal: 20,
    flex: 1,
    marginHorizontal: 5,
  },
  activeTabButton: {
    backgroundColor: '#4CAF50',
  },
  tabButtonText: {
    color: '#757575',
    fontWeight: '500',
    marginLeft: 5,
  },
  activeTabButtonText: {
    color: '#FFFFFF',
  },
  reportContent: {
    marginBottom: 15,
  },
  summaryCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 10,
    margin: 15,
    marginTop: 0,
    padding: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  summaryTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  summarySubtitle: {
    fontSize: 14,
    color: '#757575',
    marginBottom: 15,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 15,
  },
  summaryItem: {
    alignItems: 'center',
    flex: 1,
  },
  summaryValue: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#4CAF50',
  },
  summaryLabel: {
    fontSize: 12,
    color: '#757575',
    marginTop: 5,
  },
  deductionContainer: {
    backgroundColor: '#F9FFF9',
    borderRadius: 8,
    padding: 15,
    marginTop: 5,
  },
  deductionItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 5,
  },
  deductionLabel: {
    fontSize: 14,
    color: '#757575',
  },
  deductionValue: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#212121',
  },
  monthlyBreakdownCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 10,
    margin: 15,
    marginTop: 0,
    padding: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 15,
  },
  monthRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 10,
  },
  monthRowBorder: {
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  monthName: {
    width: 80,
    fontSize: 14,
    fontWeight: '500',
  },
  monthData: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  mileageItem: {
    alignItems: 'center',
    width: 70,
  },
  mileageValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#212121',
  },
  mileageLabel: {
    fontSize: 12,
    color: '#757575',
  },
  expenseSummaryContainer: {
    marginTop: 10,
  },
  expenseCategory: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  expenseCategoryName: {
    fontSize: 16,
    color: '#212121',
  },
  expenseCategoryAmount: {
    fontSize: 16,
    fontWeight: '500',
    color: '#212121',
  },
  expenseTotalRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 15,
    marginTop: 5,
  },
  expenseTotalLabel: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#212121',
  },
  expenseTotalValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#4CAF50',
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginHorizontal: 15,
    marginBottom: 15,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#4CAF50',
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 15,
    flex: 0.48,
  },
  shareButton: {
    backgroundColor: '#2196F3',
  },
  actionButtonText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
    marginLeft: 8,
  },
  infoCard: {
    flexDirection: 'row',
    backgroundColor: '#F9FFF9',
    borderRadius: 10,
    margin: 15,
    marginTop: 0,
    padding: 15,
    marginBottom: 30,
  },
  infoContent: {
    flex: 1,
    marginLeft: 10,
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  infoText: {
    fontSize: 14,
    color: '#757575',
    lineHeight: 20,
  },
});

export default ReportsScreen;