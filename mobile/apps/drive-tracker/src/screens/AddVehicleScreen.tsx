import React, { useState } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  ScrollView, 
  TextInput, 
  TouchableOpacity, 
  Switch,
  Alert,
  KeyboardAvoidingView,
  Platform
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { AddVehicleScreenNavigationProp } from '../types/navigation';

const AddVehicleScreen = () => {
  const navigation = useNavigation<AddVehicleScreenNavigationProp>();
  
  // Form state
  const [vehicleData, setVehicleData] = useState({
    name: '',
    make: '',
    model: '',
    year: '',
    licensePlate: '',
    vin: '',
    odometer: '',
    isDefault: true,
    notes: '',
  });

  // Validation state
  const [errors, setErrors] = useState({
    name: '',
    make: '',
    model: '',
    year: '',
  });

  // Handle text input changes
  const handleChange = (field, value) => {
    setVehicleData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Clear error when user types
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  // Toggle default vehicle
  const toggleDefault = () => {
    setVehicleData(prev => ({
      ...prev,
      isDefault: !prev.isDefault
    }));
  };

  // Validate form
  const validateForm = () => {
    let isValid = true;
    const newErrors = { name: '', make: '', model: '', year: '' };
    
    if (!vehicleData.name.trim()) {
      newErrors.name = 'Vehicle name is required';
      isValid = false;
    }
    
    if (!vehicleData.make.trim()) {
      newErrors.make = 'Make is required';
      isValid = false;
    }
    
    if (!vehicleData.model.trim()) {
      newErrors.model = 'Model is required';
      isValid = false;
    }
    
    if (!vehicleData.year.trim()) {
      newErrors.year = 'Year is required';
      isValid = false;
    } else if (!/^\d{4}$/.test(vehicleData.year)) {
      newErrors.year = 'Please enter a valid 4-digit year';
      isValid = false;
    }
    
    setErrors(newErrors);
    return isValid;
  };

  // Handle form submission
  const handleSubmit = () => {
    if (validateForm()) {
      // In a real implementation, this would save the vehicle to the database
      Alert.alert(
        'Success',
        `Vehicle "${vehicleData.name}" has been added.`,
        [
          {
            text: 'OK',
            onPress: () => navigation.goBack()
          }
        ]
      );
    }
  };

  // Handle cancel
  const handleCancel = () => {
    Alert.alert(
      'Discard Changes',
      'Are you sure you want to discard your changes?',
      [
        {
          text: 'Continue Editing',
          style: 'cancel',
        },
        {
          text: 'Discard',
          onPress: () => navigation.goBack(),
          style: 'destructive',
        },
      ]
    );
  };

  return (
    <KeyboardAvoidingView
      style={{ flex: 1 }}
      behavior={Platform.OS === 'ios' ? 'padding' : undefined}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 64 : 0}
    >
      <ScrollView style={styles.container}>
        <View style={styles.formSection}>
          <Text style={styles.sectionTitle}>Vehicle Information</Text>
          
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Vehicle Name*</Text>
            <TextInput
              style={[styles.input, errors.name ? styles.inputError : null]}
              value={vehicleData.name}
              onChangeText={(text) => handleChange('name', text)}
              placeholder="e.g. My Car, Work Truck"
            />
            {errors.name ? <Text style={styles.errorText}>{errors.name}</Text> : null}
          </View>
          
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Make*</Text>
            <TextInput
              style={[styles.input, errors.make ? styles.inputError : null]}
              value={vehicleData.make}
              onChangeText={(text) => handleChange('make', text)}
              placeholder="e.g. Toyota, Ford"
            />
            {errors.make ? <Text style={styles.errorText}>{errors.make}</Text> : null}
          </View>
          
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Model*</Text>
            <TextInput
              style={[styles.input, errors.model ? styles.inputError : null]}
              value={vehicleData.model}
              onChangeText={(text) => handleChange('model', text)}
              placeholder="e.g. Camry, F-150"
            />
            {errors.model ? <Text style={styles.errorText}>{errors.model}</Text> : null}
          </View>
          
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Year*</Text>
            <TextInput
              style={[styles.input, errors.year ? styles.inputError : null]}
              value={vehicleData.year}
              onChangeText={(text) => handleChange('year', text)}
              placeholder="e.g. 2023"
              keyboardType="number-pad"
              maxLength={4}
            />
            {errors.year ? <Text style={styles.errorText}>{errors.year}</Text> : null}
          </View>
        </View>
        
        <View style={styles.formSection}>
          <Text style={styles.sectionTitle}>Additional Details</Text>
          
          <View style={styles.inputGroup}>
            <Text style={styles.label}>License Plate</Text>
            <TextInput
              style={styles.input}
              value={vehicleData.licensePlate}
              onChangeText={(text) => handleChange('licensePlate', text)}
              placeholder="e.g. ABC123"
            />
          </View>
          
          <View style={styles.inputGroup}>
            <Text style={styles.label}>VIN</Text>
            <TextInput
              style={styles.input}
              value={vehicleData.vin}
              onChangeText={(text) => handleChange('vin', text)}
              placeholder="Vehicle Identification Number"
            />
          </View>
          
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Current Odometer</Text>
            <TextInput
              style={styles.input}
              value={vehicleData.odometer}
              onChangeText={(text) => handleChange('odometer', text)}
              placeholder="e.g. 12500"
              keyboardType="number-pad"
            />
          </View>
        </View>
        
        <View style={styles.formSection}>
          <Text style={styles.sectionTitle}>Preferences</Text>
          
          <View style={styles.switchContainer}>
            <View style={styles.switchInfo}>
              <Text style={styles.switchLabel}>Set as Default Vehicle</Text>
              <Text style={styles.switchDescription}>This vehicle will be selected by default for new trips</Text>
            </View>
            <Switch
              value={vehicleData.isDefault}
              onValueChange={toggleDefault}
              trackColor={{ false: '#767577', true: '#81b0ff' }}
              thumbColor={vehicleData.isDefault ? '#4CAF50' : '#f4f3f4'}
            />
          </View>
        </View>
        
        <View style={styles.formSection}>
          <Text style={styles.sectionTitle}>Notes</Text>
          
          <TextInput
            style={[styles.input, styles.textArea]}
            value={vehicleData.notes}
            onChangeText={(text) => handleChange('notes', text)}
            placeholder="Add any additional notes about this vehicle"
            multiline
            numberOfLines={4}
            textAlignVertical="top"
          />
        </View>
        
        <View style={styles.buttonContainer}>
          <TouchableOpacity style={styles.cancelButton} onPress={handleCancel}>
            <Text style={styles.cancelButtonText}>Cancel</Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.saveButton} onPress={handleSubmit}>
            <Text style={styles.saveButtonText}>Save Vehicle</Text>
          </TouchableOpacity>
        </View>
        
        <View style={styles.requiredFieldsNote}>
          <Text style={styles.requiredFieldsText}>* Required fields</Text>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  formSection: {
    backgroundColor: '#FFFFFF',
    borderRadius: 10,
    margin: 15,
    marginBottom: 10,
    padding: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 15,
    color: '#212121',
  },
  inputGroup: {
    marginBottom: 15,
  },
  label: {
    fontSize: 16,
    color: '#212121',
    marginBottom: 5,
  },
  input: {
    backgroundColor: '#F5F5F5',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },
  inputError: {
    borderColor: '#FF5252',
  },
  errorText: {
    color: '#FF5252',
    fontSize: 12,
    marginTop: 5,
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  switchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 5,
  },
  switchInfo: {
    flex: 1,
    marginRight: 10,
  },
  switchLabel: {
    fontSize: 16,
    color: '#212121',
    marginBottom: 3,
  },
  switchDescription: {
    fontSize: 12,
    color: '#757575',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    margin: 15,
  },
  saveButton: {
    backgroundColor: '#4CAF50',
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 20,
    flex: 0.48,
    alignItems: 'center',
  },
  saveButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
  cancelButton: {
    backgroundColor: '#F5F5F5',
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 20,
    flex: 0.48,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },
  cancelButtonText: {
    color: '#757575',
    fontSize: 16,
  },
  requiredFieldsNote: {
    margin: 15,
    marginTop: 0,
  },
  requiredFieldsText: {
    color: '#757575',
    fontSize: 12,
  },
});

export default AddVehicleScreen;