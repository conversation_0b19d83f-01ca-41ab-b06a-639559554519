import React, { useState } from 'react';
import { View, Text, StyleSheet, FlatList, TouchableOpacity, TextInput } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { TripsScreenNavigationProp } from '../types/navigation';

const TripsScreen = () => {
  const navigation = useNavigation<TripsScreenNavigationProp>();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFilter, setSelectedFilter] = useState('all');

  // Mock data - would be fetched from API in a real implementation
  const trips = [
    { 
      id: '1', 
      date: '2023-06-20', 
      startLocation: 'Home', 
      endLocation: 'Office', 
      distance: 12.5, 
      duration: 25, // minutes
      category: 'business' as const 
    },
    { 
      id: '2', 
      date: '2023-06-19', 
      startLocation: 'Office', 
      endLocation: 'Client Meeting', 
      distance: 8.3, 
      duration: 18,
      category: 'business' as const 
    },
    { 
      id: '3', 
      date: '2023-06-18', 
      startLocation: 'Home', 
      endLocation: 'Grocery Store', 
      distance: 3.7, 
      duration: 10,
      category: 'personal' as const 
    },
    { 
      id: '4', 
      date: '2023-06-17', 
      startLocation: 'Home', 
      endLocation: 'Restaurant', 
      distance: 5.2, 
      duration: 15,
      category: 'personal' as const 
    },
    { 
      id: '5', 
      date: '2023-06-16', 
      startLocation: 'Office', 
      endLocation: 'Home', 
      distance: 12.5, 
      duration: 28,
      category: 'business' as const 
    },
    { 
      id: '6', 
      date: '2023-06-15', 
      startLocation: 'Home', 
      endLocation: 'Office', 
      distance: 12.5, 
      duration: 26,
      category: 'business' as const 
    },
    { 
      id: '7', 
      date: '2023-06-14', 
      startLocation: 'Office', 
      endLocation: 'Client Site', 
      distance: 15.8, 
      duration: 35,
      category: 'business' as const 
    },
    { 
      id: '8', 
      date: '2023-06-13', 
      startLocation: 'Client Site', 
      endLocation: 'Home', 
      distance: 18.2, 
      duration: 40,
      category: 'business' as const 
    },
  ];

  // Filter trips based on selected filter and search query
  const filteredTrips = trips.filter(trip => {
    // Filter by category
    if (selectedFilter === 'business' && trip.category !== 'business') return false;
    if (selectedFilter === 'personal' && trip.category !== 'personal') return false;
    
    // Filter by search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      return (
        trip.startLocation.toLowerCase().includes(query) ||
        trip.endLocation.toLowerCase().includes(query) ||
        trip.date.includes(query)
      );
    }
    
    return true;
  });

  // Group trips by date
  const groupedTrips: { [key: string]: typeof trips } = {};
  filteredTrips.forEach(trip => {
    if (!groupedTrips[trip.date]) {
      groupedTrips[trip.date] = [];
    }
    groupedTrips[trip.date].push(trip);
  });

  // Convert grouped trips to array for FlatList
  const groupedTripsArray = Object.entries(groupedTrips).map(([date, trips]) => ({
    date,
    trips,
  }));

  const renderTripItem = ({ item }: { item: typeof trips[0] }) => (
    <TouchableOpacity 
      style={styles.tripItem}
      onPress={() => navigation.navigate('TripDetail', { tripId: item.id })}
    >
      <View style={styles.tripIconContainer}>
        <Ionicons 
          name={item.category === 'business' ? 'briefcase-outline' : 'home-outline'} 
          size={24} 
          color={item.category === 'business' ? '#2196F3' : '#FF9800'} 
        />
      </View>
      <View style={styles.tripInfo}>
        <Text style={styles.tripRoute}>{item.startLocation} → {item.endLocation}</Text>
        <Text style={styles.tripDetails}>
          {item.distance.toFixed(1)} mi • {item.duration} min
        </Text>
      </View>
      <View style={styles.tripCategory}>
        <Text style={[
          styles.categoryText, 
          { color: item.category === 'business' ? '#2196F3' : '#FF9800' }
        ]}>
          {item.category.charAt(0).toUpperCase() + item.category.slice(1)}
        </Text>
      </View>
      <Ionicons name="chevron-forward" size={20} color="#757575" />
    </TouchableOpacity>
  );

  const renderDateGroup = ({ item }: { item: { date: string; trips: typeof trips } }) => (
    <View style={styles.dateGroup}>
      <Text style={styles.dateHeader}>
        {new Date(item.date).toLocaleDateString('en-US', { weekday: 'short', month: 'short', day: 'numeric' })}
      </Text>
      {item.trips.map(trip => renderTripItem({ item: trip }))}
    </View>
  );

  return (
    <View style={styles.container}>
      <View style={styles.searchContainer}>
        <Ionicons name="search" size={20} color="#757575" style={styles.searchIcon} />
        <TextInput
          style={styles.searchInput}
          placeholder="Search trips..."
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
        {searchQuery ? (
          <TouchableOpacity onPress={() => setSearchQuery('')}>
            <Ionicons name="close-circle" size={20} color="#757575" />
          </TouchableOpacity>
        ) : null}
      </View>

      <View style={styles.filterContainer}>
        <TouchableOpacity 
          style={[styles.filterButton, selectedFilter === 'all' && styles.filterButtonActive]}
          onPress={() => setSelectedFilter('all')}
        >
          <Text style={[styles.filterText, selectedFilter === 'all' && styles.filterTextActive]}>All</Text>
        </TouchableOpacity>
        <TouchableOpacity 
          style={[styles.filterButton, selectedFilter === 'business' && styles.filterButtonActive]}
          onPress={() => setSelectedFilter('business')}
        >
          <Text style={[styles.filterText, selectedFilter === 'business' && styles.filterTextActive]}>Business</Text>
        </TouchableOpacity>
        <TouchableOpacity 
          style={[styles.filterButton, selectedFilter === 'personal' && styles.filterButtonActive]}
          onPress={() => setSelectedFilter('personal')}
        >
          <Text style={[styles.filterText, selectedFilter === 'personal' && styles.filterTextActive]}>Personal</Text>
        </TouchableOpacity>
      </View>

      {filteredTrips.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Ionicons name="car-outline" size={64} color="#CCCCCC" />
          <Text style={styles.emptyText}>No trips found</Text>
          <Text style={styles.emptySubtext}>
            {searchQuery 
              ? 'Try a different search term or filter'
              : 'Turn on tracking to start recording your trips'}
          </Text>
        </View>
      ) : (
        <FlatList
          data={groupedTripsArray}
          renderItem={renderDateGroup}
          keyExtractor={item => item.date}
          contentContainerStyle={styles.listContent}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderRadius: 8,
    margin: 15,
    paddingHorizontal: 15,
    paddingVertical: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  searchIcon: {
    marginRight: 10,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    height: 40,
  },
  filterContainer: {
    flexDirection: 'row',
    marginHorizontal: 15,
    marginBottom: 15,
  },
  filterButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 20,
    marginRight: 10,
    backgroundColor: '#E0E0E0',
  },
  filterButtonActive: {
    backgroundColor: '#4CAF50',
  },
  filterText: {
    color: '#757575',
    fontWeight: '500',
  },
  filterTextActive: {
    color: '#fff',
  },
  listContent: {
    paddingBottom: 20,
  },
  dateGroup: {
    marginBottom: 15,
  },
  dateHeader: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#757575',
    marginLeft: 15,
    marginBottom: 5,
  },
  tripItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 15,
    marginHorizontal: 15,
    marginBottom: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  tripIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 10,
  },
  tripInfo: {
    flex: 1,
  },
  tripRoute: {
    fontSize: 16,
    fontWeight: '500',
  },
  tripDetails: {
    fontSize: 12,
    color: '#757575',
    marginTop: 3,
  },
  tripCategory: {
    marginRight: 10,
  },
  categoryText: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#757575',
    marginTop: 20,
  },
  emptySubtext: {
    fontSize: 14,
    color: '#9E9E9E',
    textAlign: 'center',
    marginTop: 10,
  },
});

export default TripsScreen;