import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Switch } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { HomeScreenNavigationProp } from '../types/navigation';
import { useAuth } from '../../../../shared/services/auth/AuthContext';

const HomeScreen = () => {
  const navigation = useNavigation<HomeScreenNavigationProp>();
  const { user } = useAuth();
  const [isTracking, setIsTracking] = useState(false);

  // Mock data - would be fetched from API in a real implementation
  const recentTrips = [
    { 
      id: '1', 
      date: '2023-06-20', 
      startLocation: 'Home', 
      endLocation: 'Office', 
      distance: 12.5, 
      category: 'business' as const 
    },
    { 
      id: '2', 
      date: '2023-06-19', 
      startLocation: 'Office', 
      endLocation: 'Client Meeting', 
      distance: 8.3, 
      category: 'business' as const 
    },
    { 
      id: '3', 
      date: '2023-06-18', 
      startLocation: 'Home', 
      endLocation: 'Grocery Store', 
      distance: 3.7, 
      category: 'personal' as const 
    },
  ];

  const tripStats = {
    totalTrips: 42,
    businessTrips: 28,
    personalTrips: 14,
    businessMiles: 345.8,
    personalMiles: 127.2,
    totalMiles: 473.0,
    potentialDeduction: 201.99, // Based on business miles * standard mileage rate
  };

  const vehicles = [
    { id: '1', name: 'My Car', make: 'Toyota', model: 'Camry', year: 2020 },
    { id: '2', name: 'Work Truck', make: 'Ford', model: 'F-150', year: 2019 },
  ];

  const toggleTracking = () => {
    setIsTracking(!isTracking);
    // In a real implementation, this would start/stop the location tracking service
  };

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.greeting}>Hello, {user?.name || 'Driver'}</Text>
        <Text style={styles.date}>{new Date().toLocaleDateString('en-US', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' })}</Text>
      </View>

      <View style={styles.trackingCard}>
        <Text style={styles.trackingTitle}>Drive Tracking</Text>
        <View style={styles.trackingControls}>
          <Text style={styles.trackingStatus}>
            {isTracking ? 'Tracking is ON' : 'Tracking is OFF'}
          </Text>
          <Switch
            value={isTracking}
            onValueChange={toggleTracking}
            trackColor={{ false: '#767577', true: '#81b0ff' }}
            thumbColor={isTracking ? '#4CAF50' : '#f4f3f4'}
          />
        </View>
        <Text style={styles.trackingDescription}>
          {isTracking 
            ? 'Your drives are being automatically recorded. You can categorize them later.' 
            : 'Turn on tracking to automatically record your drives.'}
        </Text>
      </View>

      <View style={styles.statsCard}>
        <Text style={styles.sectionTitle}>Trip Statistics</Text>
        <View style={styles.statsRow}>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{tripStats.totalTrips}</Text>
            <Text style={styles.statLabel}>Total Trips</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{tripStats.totalMiles.toFixed(1)}</Text>
            <Text style={styles.statLabel}>Total Miles</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>${tripStats.potentialDeduction.toFixed(2)}</Text>
            <Text style={styles.statLabel}>Tax Deduction</Text>
          </View>
        </View>
        <View style={styles.statsRow}>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{tripStats.businessTrips}</Text>
            <Text style={styles.statLabel}>Business Trips</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{tripStats.businessMiles.toFixed(1)}</Text>
            <Text style={styles.statLabel}>Business Miles</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{tripStats.personalTrips}</Text>
            <Text style={styles.statLabel}>Personal Trips</Text>
          </View>
        </View>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Quick Actions</Text>
        <View style={styles.quickActions}>
          <TouchableOpacity 
            style={styles.actionButton}
            onPress={() => navigation.navigate('Trips')}
          >
            <Ionicons name="list-outline" size={24} color="#fff" />
            <Text style={styles.actionButtonText}>View All Trips</Text>
          </TouchableOpacity>

          <TouchableOpacity 
            style={styles.actionButton}
            onPress={() => navigation.navigate('Vehicles')}
          >
            <Ionicons name="car-outline" size={24} color="#fff" />
            <Text style={styles.actionButtonText}>Manage Vehicles</Text>
          </TouchableOpacity>
        </View>
      </View>

      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Recent Trips</Text>
          <TouchableOpacity onPress={() => navigation.navigate('Trips')}>
            <Text style={styles.seeAllLink}>See All</Text>
          </TouchableOpacity>
        </View>
        
        {recentTrips.map(trip => (
          <TouchableOpacity 
            key={trip.id}
            style={styles.tripItem}
            onPress={() => navigation.navigate('TripDetail', { tripId: trip.id })}
          >
            <View style={styles.tripIconContainer}>
              <Ionicons 
                name={trip.category === 'business' ? 'briefcase-outline' : 'home-outline'} 
                size={24} 
                color={trip.category === 'business' ? '#2196F3' : '#FF9800'} 
              />
            </View>
            <View style={styles.tripInfo}>
              <Text style={styles.tripRoute}>{trip.startLocation} → {trip.endLocation}</Text>
              <Text style={styles.tripDate}>{new Date(trip.date).toLocaleDateString()}</Text>
            </View>
            <View style={styles.tripDistance}>
              <Text style={styles.distanceValue}>{trip.distance.toFixed(1)}</Text>
              <Text style={styles.distanceUnit}>mi</Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color="#757575" />
          </TouchableOpacity>
        ))}
      </View>

      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>My Vehicles</Text>
          <TouchableOpacity onPress={() => navigation.navigate('Vehicles')}>
            <Text style={styles.seeAllLink}>See All</Text>
          </TouchableOpacity>
        </View>
        
        {vehicles.map(vehicle => (
          <TouchableOpacity 
            key={vehicle.id}
            style={styles.vehicleItem}
            onPress={() => navigation.navigate('VehicleDetail', { vehicleId: vehicle.id })}
          >
            <Ionicons name="car" size={24} color="#2196F3" />
            <View style={styles.vehicleInfo}>
              <Text style={styles.vehicleName}>{vehicle.name}</Text>
              <Text style={styles.vehicleDetails}>{vehicle.year} {vehicle.make} {vehicle.model}</Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color="#757575" />
          </TouchableOpacity>
        ))}
        
        <TouchableOpacity 
          style={styles.addButton}
          onPress={() => navigation.navigate('AddVehicle')}
        >
          <Ionicons name="add-circle-outline" size={20} color="#4CAF50" />
          <Text style={styles.addButtonText}>Add New Vehicle</Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    padding: 20,
    backgroundColor: '#4CAF50',
  },
  greeting: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
  },
  date: {
    fontSize: 14,
    color: '#e0e0e0',
    marginTop: 5,
  },
  trackingCard: {
    backgroundColor: '#fff',
    borderRadius: 10,
    margin: 15,
    padding: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  trackingTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  trackingControls: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  trackingStatus: {
    fontSize: 16,
    fontWeight: '500',
  },
  trackingDescription: {
    fontSize: 14,
    color: '#757575',
  },
  statsCard: {
    backgroundColor: '#fff',
    borderRadius: 10,
    margin: 15,
    padding: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 10,
  },
  statItem: {
    alignItems: 'center',
    flex: 1,
  },
  statValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2196F3',
  },
  statLabel: {
    fontSize: 12,
    color: '#757575',
    textAlign: 'center',
  },
  section: {
    margin: 15,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  seeAllLink: {
    color: '#2196F3',
    fontSize: 14,
  },
  quickActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#4CAF50',
    borderRadius: 8,
    padding: 15,
    flex: 0.48,
    justifyContent: 'center',
    marginBottom: 15,
  },
  actionButtonText: {
    color: '#fff',
    fontWeight: 'bold',
    marginLeft: 10,
  },
  tripItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 15,
    marginBottom: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  tripIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 10,
  },
  tripInfo: {
    flex: 1,
  },
  tripRoute: {
    fontSize: 16,
    fontWeight: '500',
  },
  tripDate: {
    fontSize: 12,
    color: '#757575',
  },
  tripDistance: {
    flexDirection: 'row',
    alignItems: 'baseline',
    marginRight: 10,
  },
  distanceValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2196F3',
  },
  distanceUnit: {
    fontSize: 12,
    color: '#757575',
    marginLeft: 2,
  },
  vehicleItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 15,
    marginBottom: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  vehicleInfo: {
    flex: 1,
    marginLeft: 15,
  },
  vehicleName: {
    fontSize: 16,
    fontWeight: '500',
  },
  vehicleDetails: {
    fontSize: 12,
    color: '#757575',
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 10,
    marginTop: 5,
  },
  addButtonText: {
    color: '#4CAF50',
    marginLeft: 5,
    fontWeight: '500',
  },
});

export default HomeScreen;