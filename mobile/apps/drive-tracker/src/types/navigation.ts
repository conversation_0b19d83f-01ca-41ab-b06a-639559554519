import { StackNavigationProp } from '@react-navigation/stack';
import { BottomTabNavigationProp } from '@react-navigation/bottom-tabs';
import { CompositeNavigationProp, RouteProp } from '@react-navigation/native';

// Trip type definition
export interface Trip {
  id: string;
  startTime: string;
  endTime: string;
  startLocation: string;
  endLocation: string;
  distance: number;
  duration: number;
  category: 'business' | 'personal';
  purpose?: string;
  notes?: string;
  routeData?: any;
  vehicleId: string;
  expenseId?: string;
}

// Vehicle type definition
export interface Vehicle {
  id: string;
  name: string;
  make: string;
  model: string;
  year: number;
  licensePlate: string;
  vin?: string;
  mpg?: number;
  isDefault: boolean;
}

// Expense type definition
export interface Expense {
  id: string;
  date: string;
  amount: number;
  category: string;
  description: string;
  receiptUrl?: string;
  vehicleId: string;
  tripId?: string;
}

// Root Stack Parameter List
export type RootStackParamList = {
  Login: undefined;
  Main: undefined;
  TripDetail: { tripId: string };
  VehicleDetail: { vehicleId: string };
  ExpenseDetail: { expenseId: string };
  AddVehicle: undefined;
  AddExpense: { vehicleId?: string; tripId?: string };
  Settings: undefined;
};

// Main Tab Parameter List
export type MainTabParamList = {
  Home: undefined;
  Trips: undefined;
  Vehicles: undefined;
  Expenses: undefined;
  Reports: undefined;
  Profile: undefined;
};

// Navigation Types
export type HomeScreenNavigationProp = CompositeNavigationProp<
  BottomTabNavigationProp<MainTabParamList, 'Home'>,
  StackNavigationProp<RootStackParamList>
>;

export type TripsScreenNavigationProp = CompositeNavigationProp<
  BottomTabNavigationProp<MainTabParamList, 'Trips'>,
  StackNavigationProp<RootStackParamList>
>;

export type VehiclesScreenNavigationProp = CompositeNavigationProp<
  BottomTabNavigationProp<MainTabParamList, 'Vehicles'>,
  StackNavigationProp<RootStackParamList>
>;

export type ExpensesScreenNavigationProp = CompositeNavigationProp<
  BottomTabNavigationProp<MainTabParamList, 'Expenses'>,
  StackNavigationProp<RootStackParamList>
>;

export type ReportsScreenNavigationProp = CompositeNavigationProp<
  BottomTabNavigationProp<MainTabParamList, 'Reports'>,
  StackNavigationProp<RootStackParamList>
>;

export type ProfileScreenNavigationProp = CompositeNavigationProp<
  BottomTabNavigationProp<MainTabParamList, 'Profile'>,
  StackNavigationProp<RootStackParamList>
>;

export type TripDetailScreenNavigationProp = StackNavigationProp<
  RootStackParamList,
  'TripDetail'
>;

export type TripDetailScreenRouteProp = RouteProp<
  RootStackParamList,
  'TripDetail'
>;

export type VehicleDetailScreenNavigationProp = StackNavigationProp<
  RootStackParamList,
  'VehicleDetail'
>;

export type VehicleDetailScreenRouteProp = RouteProp<
  RootStackParamList,
  'VehicleDetail'
>;

export type ExpenseDetailScreenNavigationProp = StackNavigationProp<
  RootStackParamList,
  'ExpenseDetail'
>;

export type ExpenseDetailScreenRouteProp = RouteProp<
  RootStackParamList,
  'ExpenseDetail'
>;

export type AddVehicleScreenNavigationProp = StackNavigationProp<
  RootStackParamList,
  'AddVehicle'
>;

export type AddVehicleScreenRouteProp = RouteProp<
  RootStackParamList,
  'AddVehicle'
>;

export type AddExpenseScreenNavigationProp = StackNavigationProp<
  RootStackParamList,
  'AddExpense'
>;

export type AddExpenseScreenRouteProp = RouteProp<
  RootStackParamList,
  'AddExpense'
>;

export type SettingsScreenNavigationProp = StackNavigationProp<
  RootStackParamList,
  'Settings'
>;

export type SettingsScreenRouteProp = RouteProp<
  RootStackParamList,
  'Settings'
>;
