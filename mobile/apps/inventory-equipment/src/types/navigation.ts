import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RouteProp } from '@react-navigation/native';
import { MainStackParamList, MainTabParamList } from '../navigation/MainNavigator';
import { AuthStackParamList } from '../navigation/AuthNavigator';

// Auth Navigation Types
export type LoginScreenNavigationProp = NativeStackNavigationProp<AuthStackParamList, 'Login'>;
export type RegisterScreenNavigationProp = NativeStackNavigationProp<AuthStackParamList, 'Register'>;
export type ForgotPasswordScreenNavigationProp = NativeStackNavigationProp<AuthStackParamList, 'ForgotPassword'>;

export type LoginScreenRouteProp = RouteProp<AuthStackParamList, 'Login'>;
export type RegisterScreenRouteProp = RouteProp<AuthStackParamList, 'Register'>;
export type ForgotPasswordScreenRouteProp = RouteProp<AuthStackParamList, 'ForgotPassword'>;

// Main Navigation Types
export type InventoryScreenNavigationProp = NativeStackNavigationProp<MainTabParamList, 'Inventory'>;
export type EquipmentScreenNavigationProp = NativeStackNavigationProp<MainTabParamList, 'Equipment'>;
export type MaintenanceScreenNavigationProp = NativeStackNavigationProp<MainTabParamList, 'Maintenance'>;
export type PartsScreenNavigationProp = NativeStackNavigationProp<MainTabParamList, 'Parts'>;
export type SettingsScreenNavigationProp = NativeStackNavigationProp<MainTabParamList, 'Settings'>;

// Detail Screen Navigation Types
export type InventoryDetailScreenNavigationProp = NativeStackNavigationProp<MainStackParamList, 'InventoryDetail'>;
export type EquipmentDetailScreenNavigationProp = NativeStackNavigationProp<MainStackParamList, 'EquipmentDetail'>;
export type AddInventoryScreenNavigationProp = NativeStackNavigationProp<MainStackParamList, 'AddInventory'>;

export type InventoryDetailScreenRouteProp = RouteProp<MainStackParamList, 'InventoryDetail'>;
export type EquipmentDetailScreenRouteProp = RouteProp<MainStackParamList, 'EquipmentDetail'>;
export type AddInventoryScreenRouteProp = RouteProp<MainStackParamList, 'AddInventory'>;

// Data Types
export interface InventoryItem {
  id: string;
  name: string;
  category: string;
  quantity: number;
  unit: string;
  location: string;
  supplier?: string;
  lastUpdated: string;
  minimumLevel?: number;
  notes?: string;
}

export interface Equipment {
  id: string;
  name: string;
  type: string;
  status: 'operational' | 'maintenance' | 'out_of_service';
  location: string;
  lastMaintenance?: string;
  nextMaintenance?: string;
  purchaseDate?: string;
  serialNumber?: string;
  manufacturer?: string;
  notes?: string;
}

export interface MaintenanceRecord {
  id: string;
  equipmentId: string;
  type: 'scheduled' | 'repair' | 'inspection';
  status: 'scheduled' | 'in_progress' | 'completed' | 'cancelled';
  scheduledDate: string;
  completedDate?: string;
  description: string;
  technician?: string;
  cost?: number;
  notes?: string;
}

export interface Part {
  id: string;
  name: string;
  partNumber: string;
  quantity: number;
  location: string;
  compatibleEquipment: string[];
  supplier?: string;
  lastOrdered?: string;
  minimumLevel?: number;
  notes?: string;
}
