import React from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { Ionicons } from '@expo/vector-icons';
import InventoryScreen from '../screens/InventoryScreen';
import EquipmentScreen from '../screens/EquipmentScreen';
import MaintenanceScreen from '../screens/MaintenanceScreen';
import PartsInventoryScreen from '../screens/PartsInventoryScreen';
import ProfileScreen from '../screens/ProfileScreen';
import InventoryDetailScreen from '../screens/InventoryDetailScreen';
import EquipmentDetailScreen from '../screens/EquipmentDetailScreen';
import AddInventoryScreen from '../screens/AddInventoryScreen';

export type MainStackParamList = {
  MainTabs: undefined;
  InventoryDetail: { inventoryId: string };
  EquipmentDetail: { equipmentId: string };
  AddInventory: undefined;
};

export type MainTabParamList = {
  Inventory: undefined;
  Equipment: undefined;
  Maintenance: undefined;
  Parts: undefined;
  Profile: undefined;
};

const Stack = createNativeStackNavigator<MainStackParamList>();
const Tab = createBottomTabNavigator<MainTabParamList>();

const MainTabs: React.FC = () => {
  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName: keyof typeof Ionicons.glyphMap = 'home';

          if (route.name === 'Inventory') {
            iconName = focused ? 'cube' : 'cube-outline';
          } else if (route.name === 'Equipment') {
            iconName = focused ? 'construct' : 'construct-outline';
          } else if (route.name === 'Maintenance') {
            iconName = focused ? 'calendar' : 'calendar-outline';
          } else if (route.name === 'Parts') {
            iconName = focused ? 'hardware-chip' : 'hardware-chip-outline';
          } else if (route.name === 'Profile') {
            iconName = focused ? 'person' : 'person-outline';
          }

          return <Ionicons name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: '#3b82f6',
        tabBarInactiveTintColor: 'gray',
        headerShown: true,
        headerStyle: {
          backgroundColor: '#3b82f6',
        },
        headerTintColor: '#fff',
        headerTitleStyle: {
          fontWeight: 'bold',
        },
      })}
    >
      <Tab.Screen name="Inventory" component={InventoryScreen} options={{ title: 'Inventory' }} />
      <Tab.Screen name="Equipment" component={EquipmentScreen} options={{ title: 'Equipment' }} />
      <Tab.Screen name="Maintenance" component={MaintenanceScreen} options={{ title: 'Maintenance' }} />
      <Tab.Screen name="Parts" component={PartsInventoryScreen} options={{ title: 'Parts' }} />
      <Tab.Screen name="Profile" component={ProfileScreen} options={{ title: 'Profile' }} />
    </Tab.Navigator>
  );
};

const MainNavigator: React.FC = () => {
  return (
    <Stack.Navigator
      initialRouteName="MainTabs"
      screenOptions={{
        headerShown: false,
      }}
    >
      <Stack.Screen name="MainTabs" component={MainTabs} />
      <Stack.Screen 
        name="InventoryDetail" 
        component={InventoryDetailScreen} 
        options={{ 
          headerShown: true,
          headerStyle: { backgroundColor: '#3b82f6' },
          headerTintColor: '#fff',
          title: 'Inventory Details'
        }} 
      />
      <Stack.Screen 
        name="EquipmentDetail" 
        component={EquipmentDetailScreen} 
        options={{ 
          headerShown: true,
          headerStyle: { backgroundColor: '#3b82f6' },
          headerTintColor: '#fff',
          title: 'Equipment Details'
        }} 
      />
      <Stack.Screen 
        name="AddInventory" 
        component={AddInventoryScreen} 
        options={{ 
          headerShown: true,
          headerStyle: { backgroundColor: '#3b82f6' },
          headerTintColor: '#fff',
          title: 'Add Inventory Item'
        }} 
      />
    </Stack.Navigator>
  );
};

export default MainNavigator;
