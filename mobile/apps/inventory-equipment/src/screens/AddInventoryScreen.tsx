import React, { useState } from 'react';
import { View, Text, StyleSheet, TextInput, ScrollView, TouchableOpacity, Alert, KeyboardAvoidingView, Platform } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { InventoryItem } from '../types/navigation';

const AddInventoryScreen: React.FC = () => {
  const navigation = useNavigation();
  const [name, setName] = useState('');
  const [category, setCategory] = useState('');
  const [quantity, setQuantity] = useState('');
  const [unit, setUnit] = useState('');
  const [location, setLocation] = useState('');
  const [supplier, setSupplier] = useState('');
  const [minimumLevel, setMinimumLevel] = useState('');
  const [notes, setNotes] = useState('');

  // Categories for selection
  const categories = ['Seeds', 'Fertilizer', 'Pesticide', 'Feed', 'Supplies'];
  const [showCategoryPicker, setShowCategoryPicker] = useState(false);

  // Units for selection
  const units = ['kg', 'L', 'pieces', 'packets', 'bags', 'boxes'];
  const [showUnitPicker, setShowUnitPicker] = useState(false);

  const handleSave = () => {
    // Validate required fields
    if (!name || !category || !quantity || !unit || !location) {
      Alert.alert('Missing Information', 'Please fill in all required fields.');
      return;
    }

    // Create new inventory item
    const newItem: Partial<InventoryItem> = {
      name,
      category,
      quantity: Number(quantity),
      unit,
      location,
      supplier: supplier || undefined,
      lastUpdated: new Date().toISOString().split('T')[0],
      minimumLevel: minimumLevel ? Number(minimumLevel) : undefined,
      notes: notes || undefined,
    };

    // In a real implementation, this would save to an API
    console.log('Saving new inventory item:', newItem);
    Alert.alert(
      'Success',
      'Inventory item added successfully!',
      [{ text: 'OK', onPress: () => navigation.goBack() }]
    );
  };

  return (
    <KeyboardAvoidingView 
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={100}
    >
      <ScrollView style={styles.scrollContainer}>
        <View style={styles.formContainer}>
          <Text style={styles.sectionTitle}>Add New Inventory Item</Text>
          
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Name *</Text>
            <TextInput
              style={styles.input}
              value={name}
              onChangeText={setName}
              placeholder="Enter item name"
            />
          </View>
          
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Category *</Text>
            <TouchableOpacity 
              style={styles.input}
              onPress={() => setShowCategoryPicker(!showCategoryPicker)}
            >
              <Text style={category ? styles.inputText : styles.placeholderText}>
                {category || 'Select a category'}
              </Text>
              <Ionicons name="chevron-down" size={20} color="#757575" />
            </TouchableOpacity>
            
            {showCategoryPicker && (
              <View style={styles.pickerContainer}>
                {categories.map((cat) => (
                  <TouchableOpacity
                    key={cat}
                    style={styles.pickerItem}
                    onPress={() => {
                      setCategory(cat);
                      setShowCategoryPicker(false);
                    }}
                  >
                    <Text style={[styles.pickerItemText, cat === category && styles.pickerItemTextSelected]}>
                      {cat}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            )}
          </View>
          
          <View style={styles.rowInputs}>
            <View style={[styles.inputGroup, { flex: 1, marginRight: 10 }]}>
              <Text style={styles.label}>Quantity *</Text>
              <TextInput
                style={styles.input}
                value={quantity}
                onChangeText={setQuantity}
                placeholder="Enter quantity"
                keyboardType="numeric"
              />
            </View>
            
            <View style={[styles.inputGroup, { flex: 1 }]}>
              <Text style={styles.label}>Unit *</Text>
              <TouchableOpacity 
                style={styles.input}
                onPress={() => setShowUnitPicker(!showUnitPicker)}
              >
                <Text style={unit ? styles.inputText : styles.placeholderText}>
                  {unit || 'Select unit'}
                </Text>
                <Ionicons name="chevron-down" size={20} color="#757575" />
              </TouchableOpacity>
              
              {showUnitPicker && (
                <View style={styles.pickerContainer}>
                  {units.map((u) => (
                    <TouchableOpacity
                      key={u}
                      style={styles.pickerItem}
                      onPress={() => {
                        setUnit(u);
                        setShowUnitPicker(false);
                      }}
                    >
                      <Text style={[styles.pickerItemText, u === unit && styles.pickerItemTextSelected]}>
                        {u}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              )}
            </View>
          </View>
          
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Location *</Text>
            <TextInput
              style={styles.input}
              value={location}
              onChangeText={setLocation}
              placeholder="Enter storage location"
            />
          </View>
          
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Supplier</Text>
            <TextInput
              style={styles.input}
              value={supplier}
              onChangeText={setSupplier}
              placeholder="Enter supplier name (optional)"
            />
          </View>
          
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Minimum Level</Text>
            <TextInput
              style={styles.input}
              value={minimumLevel}
              onChangeText={setMinimumLevel}
              placeholder="Enter minimum stock level (optional)"
              keyboardType="numeric"
            />
          </View>
          
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Notes</Text>
            <TextInput
              style={[styles.input, styles.textArea]}
              value={notes}
              onChangeText={setNotes}
              placeholder="Enter additional notes (optional)"
              multiline
              numberOfLines={4}
              textAlignVertical="top"
            />
          </View>
          
          <Text style={styles.requiredText}>* Required fields</Text>
          
          <View style={styles.buttonContainer}>
            <TouchableOpacity 
              style={styles.cancelButton}
              onPress={() => navigation.goBack()}
            >
              <Text style={styles.cancelButtonText}>Cancel</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={styles.saveButton}
              onPress={handleSave}
            >
              <Text style={styles.saveButtonText}>Save</Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollContainer: {
    flex: 1,
  },
  formContainer: {
    padding: 20,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 20,
    color: '#212121',
  },
  inputGroup: {
    marginBottom: 15,
  },
  label: {
    fontSize: 16,
    marginBottom: 5,
    color: '#424242',
  },
  input: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 12,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    fontSize: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  inputText: {
    color: '#212121',
    fontSize: 16,
  },
  placeholderText: {
    color: '#9E9E9E',
    fontSize: 16,
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  rowInputs: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  pickerContainer: {
    backgroundColor: '#fff',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    marginTop: 5,
    maxHeight: 200,
  },
  pickerItem: {
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  pickerItemText: {
    fontSize: 16,
    color: '#212121',
  },
  pickerItemTextSelected: {
    color: '#3b82f6',
    fontWeight: 'bold',
  },
  requiredText: {
    fontSize: 14,
    color: '#757575',
    marginTop: 10,
    marginBottom: 20,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 30,
  },
  cancelButton: {
    flex: 1,
    backgroundColor: '#E0E0E0',
    borderRadius: 8,
    padding: 15,
    alignItems: 'center',
    marginRight: 10,
  },
  cancelButtonText: {
    color: '#424242',
    fontSize: 16,
    fontWeight: '500',
  },
  saveButton: {
    flex: 1,
    backgroundColor: '#3b82f6',
    borderRadius: 8,
    padding: 15,
    alignItems: 'center',
  },
  saveButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '500',
  },
});

export default AddInventoryScreen;