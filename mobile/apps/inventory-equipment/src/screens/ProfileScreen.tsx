import React, { useState, useEffect } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  ScrollView, 
  TouchableOpacity, 
  Switch,
  Image,
  ActivityIndicator,
  Alert
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { ProfileScreenNavigationProp } from '../types/navigation';

interface UserProfile {
  id: string;
  name: string;
  email: string;
  role: string;
  department: string;
  avatar?: string;
  phone?: string;
  lastLogin: string;
}

interface AppSettings {
  notifications: boolean;
  darkMode: boolean;
  offlineMode: boolean;
  autoSync: boolean;
  syncInterval: number; // in minutes
  defaultView: 'inventory' | 'equipment' | 'maintenance' | 'parts';
}

const ProfileScreen = () => {
  const navigation = useNavigation<ProfileScreenNavigationProp>();
  const [loading, setLoading] = useState(true);
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [appSettings, setAppSettings] = useState<AppSettings>({
    notifications: true,
    darkMode: false,
    offlineMode: false,
    autoSync: true,
    syncInterval: 15,
    defaultView: 'inventory',
  });

  useEffect(() => {
    // In a real implementation, this would fetch user profile from an API
    const fetchUserProfile = async () => {
      try {
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Mock user profile data
        const mockUserProfile: UserProfile = {
          id: 'user123',
          name: 'Alex Johnson',
          email: '<EMAIL>',
          role: 'Inventory Manager',
          department: 'Operations',
          avatar: 'https://randomuser.me/api/portraits/men/32.jpg',
          phone: '(*************',
          lastLogin: '2023-07-01T08:30:00Z',
        };
        
        setUserProfile(mockUserProfile);
      } catch (error) {
        console.error('Error fetching user profile:', error);
        Alert.alert('Error', 'Failed to load user profile');
      } finally {
        setLoading(false);
      }
    };

    fetchUserProfile();
  }, []);

  const handleLogout = () => {
    Alert.alert(
      'Confirm Logout',
      'Are you sure you want to log out?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Logout',
          style: 'destructive',
          onPress: () => {
            // In a real app, this would clear auth tokens and navigate to login screen
            console.log('User logged out');
            // navigation.navigate('Login');
          },
        },
      ]
    );
  };

  const handleSyncData = () => {
    Alert.alert('Syncing Data', 'Synchronizing with server...');
    // In a real app, this would trigger a data sync with the server
    setTimeout(() => {
      Alert.alert('Sync Complete', 'All data has been synchronized with the server.');
    }, 2000);
  };

  const toggleSetting = (setting: keyof AppSettings, value: boolean) => {
    setAppSettings(prev => ({
      ...prev,
      [setting]: value,
    }));
  };

  const setDefaultView = (view: AppSettings['defaultView']) => {
    setAppSettings(prev => ({
      ...prev,
      defaultView: view,
    }));
  };

  const renderSettingSwitch = (
    label: string, 
    description: string, 
    setting: keyof AppSettings,
    disabled: boolean = false
  ) => (
    <View style={styles.settingItem}>
      <View style={styles.settingTextContainer}>
        <Text style={styles.settingLabel}>{label}</Text>
        <Text style={styles.settingDescription}>{description}</Text>
      </View>
      <Switch
        value={appSettings[setting] as boolean}
        onValueChange={(value) => toggleSetting(setting, value)}
        trackColor={{ false: '#d1d5db', true: '#bfdbfe' }}
        thumbColor={appSettings[setting] as boolean ? '#3b82f6' : '#f4f4f5'}
        disabled={disabled}
      />
    </View>
  );

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#3b82f6" />
        <Text style={styles.loadingText}>Loading profile...</Text>
      </View>
    );
  }

  if (!userProfile) {
    return (
      <View style={styles.errorContainer}>
        <Ionicons name="alert-circle" size={50} color="#ef4444" />
        <Text style={styles.errorText}>Failed to load user profile</Text>
        <TouchableOpacity style={styles.button} onPress={() => navigation.goBack()}>
          <Text style={styles.buttonText}>Go Back</Text>
        </TouchableOpacity>
      </View>
    );
  }

  const formatLastLogin = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString();
  };

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <View style={styles.profileContainer}>
          {userProfile.avatar ? (
            <Image source={{ uri: userProfile.avatar }} style={styles.avatar} />
          ) : (
            <View style={styles.avatarPlaceholder}>
              <Text style={styles.avatarInitials}>
                {userProfile.name.split(' ').map(n => n[0]).join('')}
              </Text>
            </View>
          )}
          <View style={styles.profileInfo}>
            <Text style={styles.userName}>{userProfile.name}</Text>
            <Text style={styles.userRole}>{userProfile.role}</Text>
            <Text style={styles.userDepartment}>{userProfile.department}</Text>
          </View>
        </View>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Account Information</Text>
        <View style={styles.card}>
          <View style={styles.infoItem}>
            <Ionicons name="mail" size={20} color="#3b82f6" style={styles.infoIcon} />
            <View style={styles.infoContent}>
              <Text style={styles.infoLabel}>Email</Text>
              <Text style={styles.infoValue}>{userProfile.email}</Text>
            </View>
          </View>
          
          {userProfile.phone && (
            <View style={styles.infoItem}>
              <Ionicons name="call" size={20} color="#3b82f6" style={styles.infoIcon} />
              <View style={styles.infoContent}>
                <Text style={styles.infoLabel}>Phone</Text>
                <Text style={styles.infoValue}>{userProfile.phone}</Text>
              </View>
            </View>
          )}
          
          <View style={styles.infoItem}>
            <Ionicons name="time" size={20} color="#3b82f6" style={styles.infoIcon} />
            <View style={styles.infoContent}>
              <Text style={styles.infoLabel}>Last Login</Text>
              <Text style={styles.infoValue}>{formatLastLogin(userProfile.lastLogin)}</Text>
            </View>
          </View>
        </View>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>App Settings</Text>
        <View style={styles.card}>
          {renderSettingSwitch(
            'Notifications', 
            'Receive alerts for low stock, maintenance due, etc.', 
            'notifications'
          )}
          
          {renderSettingSwitch(
            'Dark Mode', 
            'Use dark theme for the app (coming soon)', 
            'darkMode',
            true // Disabled as it's not implemented yet
          )}
          
          {renderSettingSwitch(
            'Offline Mode', 
            'Enable working without internet connection', 
            'offlineMode'
          )}
          
          {renderSettingSwitch(
            'Auto Sync', 
            'Automatically sync data when connection is available', 
            'autoSync'
          )}
        </View>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Default View</Text>
        <View style={styles.card}>
          <View style={styles.defaultViewContainer}>
            <TouchableOpacity 
              style={[
                styles.defaultViewOption,
                appSettings.defaultView === 'inventory' && styles.defaultViewOptionSelected
              ]}
              onPress={() => setDefaultView('inventory')}
            >
              <Ionicons 
                name="cube" 
                size={24} 
                color={appSettings.defaultView === 'inventory' ? '#fff' : '#3b82f6'} 
              />
              <Text style={[
                styles.defaultViewText,
                appSettings.defaultView === 'inventory' && styles.defaultViewTextSelected
              ]}>
                Inventory
              </Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={[
                styles.defaultViewOption,
                appSettings.defaultView === 'equipment' && styles.defaultViewOptionSelected
              ]}
              onPress={() => setDefaultView('equipment')}
            >
              <Ionicons 
                name="construct" 
                size={24} 
                color={appSettings.defaultView === 'equipment' ? '#fff' : '#3b82f6'} 
              />
              <Text style={[
                styles.defaultViewText,
                appSettings.defaultView === 'equipment' && styles.defaultViewTextSelected
              ]}>
                Equipment
              </Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={[
                styles.defaultViewOption,
                appSettings.defaultView === 'maintenance' && styles.defaultViewOptionSelected
              ]}
              onPress={() => setDefaultView('maintenance')}
            >
              <Ionicons 
                name="build" 
                size={24} 
                color={appSettings.defaultView === 'maintenance' ? '#fff' : '#3b82f6'} 
              />
              <Text style={[
                styles.defaultViewText,
                appSettings.defaultView === 'maintenance' && styles.defaultViewTextSelected
              ]}>
                Maintenance
              </Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={[
                styles.defaultViewOption,
                appSettings.defaultView === 'parts' && styles.defaultViewOptionSelected
              ]}
              onPress={() => setDefaultView('parts')}
            >
              <Ionicons 
                name="hardware-chip" 
                size={24} 
                color={appSettings.defaultView === 'parts' ? '#fff' : '#3b82f6'} 
              />
              <Text style={[
                styles.defaultViewText,
                appSettings.defaultView === 'parts' && styles.defaultViewTextSelected
              ]}>
                Parts
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Actions</Text>
        <View style={styles.card}>
          <TouchableOpacity style={styles.actionButton} onPress={handleSyncData}>
            <Ionicons name="sync" size={20} color="#fff" />
            <Text style={styles.actionButtonText}>Sync Data</Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.actionButton} onPress={() => console.log('Clear cache')}>
            <Ionicons name="trash-bin" size={20} color="#fff" />
            <Text style={styles.actionButtonText}>Clear Cache</Text>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={[styles.actionButton, styles.logoutButton]} 
            onPress={handleLogout}
          >
            <Ionicons name="log-out" size={20} color="#fff" />
            <Text style={styles.actionButtonText}>Logout</Text>
          </TouchableOpacity>
        </View>
      </View>

      <View style={styles.footer}>
        <Text style={styles.versionText}>Version 1.0.0</Text>
        <TouchableOpacity onPress={() => console.log('Show privacy policy')}>
          <Text style={styles.footerLink}>Privacy Policy</Text>
        </TouchableOpacity>
        <TouchableOpacity onPress={() => console.log('Show terms of service')}>
          <Text style={styles.footerLink}>Terms of Service</Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#757575',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    padding: 20,
  },
  errorText: {
    fontSize: 18,
    color: '#212121',
    marginTop: 10,
    marginBottom: 20,
    textAlign: 'center',
  },
  button: {
    backgroundColor: '#3b82f6',
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 4,
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '500',
  },
  header: {
    backgroundColor: '#3b82f6',
    paddingTop: 40,
    paddingBottom: 20,
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
  },
  profileContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  avatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
    borderWidth: 3,
    borderColor: '#fff',
  },
  avatarPlaceholder: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#bfdbfe',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 3,
    borderColor: '#fff',
  },
  avatarInitials: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#3b82f6',
  },
  profileInfo: {
    marginLeft: 15,
  },
  userName: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#fff',
  },
  userRole: {
    fontSize: 16,
    color: '#e0f2fe',
    marginTop: 2,
  },
  userDepartment: {
    fontSize: 14,
    color: '#e0f2fe',
    marginTop: 2,
  },
  section: {
    marginTop: 20,
    paddingHorizontal: 15,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#212121',
    marginBottom: 10,
  },
  card: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  infoIcon: {
    marginRight: 15,
  },
  infoContent: {
    flex: 1,
  },
  infoLabel: {
    fontSize: 14,
    color: '#757575',
  },
  infoValue: {
    fontSize: 16,
    color: '#212121',
    marginTop: 2,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  settingTextContainer: {
    flex: 1,
    marginRight: 10,
  },
  settingLabel: {
    fontSize: 16,
    color: '#212121',
  },
  settingDescription: {
    fontSize: 12,
    color: '#757575',
    marginTop: 2,
  },
  defaultViewContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  defaultViewOption: {
    width: '48%',
    backgroundColor: '#f0f9ff',
    borderRadius: 8,
    padding: 12,
    alignItems: 'center',
    marginBottom: 10,
  },
  defaultViewOptionSelected: {
    backgroundColor: '#3b82f6',
  },
  defaultViewText: {
    fontSize: 14,
    color: '#3b82f6',
    marginTop: 5,
  },
  defaultViewTextSelected: {
    color: '#fff',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#3b82f6',
    borderRadius: 4,
    padding: 12,
    marginBottom: 10,
  },
  logoutButton: {
    backgroundColor: '#ef4444',
  },
  actionButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '500',
    marginLeft: 10,
  },
  footer: {
    marginTop: 30,
    marginBottom: 30,
    alignItems: 'center',
  },
  versionText: {
    fontSize: 14,
    color: '#757575',
    marginBottom: 10,
  },
  footerLink: {
    fontSize: 14,
    color: '#3b82f6',
    marginBottom: 5,
  },
});

export default ProfileScreen;