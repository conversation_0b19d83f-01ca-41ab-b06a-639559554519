import React, { useState, useEffect } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  TouchableOpacity, 
  ActivityIndicator,
  Alert,
  Dimensions
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { BarcodeScannerScreenNavigationProp } from '../types/navigation';

// In a real app, you would import the actual barcode scanner component
// For example: import { BarCodeScanner } from 'expo-barcode-scanner';
// For this mock implementation, we'll simulate the scanner

const BarcodeScannerScreen = () => {
  const navigation = useNavigation<BarcodeScannerScreenNavigationProp>();
  const [hasPermission, setHasPermission] = useState<boolean | null>(null);
  const [scanned, setScanned] = useState(false);
  const [scanning, setScanning] = useState(true);
  const [flashOn, setFlashOn] = useState(false);
  const [scanHistory, setScanHistory] = useState<{ code: string; type: string; timestamp: Date }[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    // In a real implementation, this would request camera permissions
    const requestCameraPermission = async () => {
      try {
        // Simulate permission request
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Mock permission granted
        setHasPermission(true);
      } catch (error) {
        console.error('Error requesting camera permission:', error);
        setHasPermission(false);
      }
    };

    requestCameraPermission();
  }, []);

  const handleBarCodeScanned = ({ type, data }: { type: string; data: string }) => {
    setScanned(true);
    setScanning(false);
    
    // Add to scan history
    setScanHistory(prevHistory => [
      { code: data, type, timestamp: new Date() },
      ...prevHistory
    ]);
    
    // Look up the scanned item
    lookupScannedItem(data);
  };

  const lookupScannedItem = async (code: string) => {
    setLoading(true);
    try {
      // Simulate API call to look up the item
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // Mock response based on code
      if (code === 'SEED-CORN-001') {
        // Navigate to inventory detail
        navigation.navigate('InventoryDetail', { inventoryId: 'inv1' });
      } else if (code === 'JD8R-12345') {
        // Navigate to equipment detail
        navigation.navigate('EquipmentDetail', { equipmentId: 'eq1' });
      } else {
        // Item not found
        Alert.alert(
          'Item Not Found',
          `No item found with code: ${code}`,
          [
            { text: 'OK', onPress: () => setScanned(false) }
          ]
        );
      }
    } catch (error) {
      console.error('Error looking up scanned item:', error);
      Alert.alert('Error', 'Failed to look up the scanned item');
    } finally {
      setLoading(false);
    }
  };

  const handleScanAgain = () => {
    setScanned(false);
    setScanning(true);
  };

  const toggleFlash = () => {
    setFlashOn(!flashOn);
  };

  const simulateScan = () => {
    // For demo purposes, simulate scanning different codes
    const codes = [
      { type: 'QR', data: 'SEED-CORN-001' },
      { type: 'EAN-13', data: 'JD8R-12345' },
      { type: 'CODE-128', data: 'UNKNOWN-CODE-123' }
    ];
    
    const randomCode = codes[Math.floor(Math.random() * codes.length)];
    handleBarCodeScanned(randomCode);
  };

  if (hasPermission === null) {
    return (
      <View style={styles.permissionContainer}>
        <ActivityIndicator size="large" color="#3b82f6" />
        <Text style={styles.permissionText}>Requesting camera permission...</Text>
      </View>
    );
  }

  if (hasPermission === false) {
    return (
      <View style={styles.permissionContainer}>
        <Ionicons name="camera-off" size={50} color="#ef4444" />
        <Text style={styles.permissionText}>No access to camera</Text>
        <Text style={styles.permissionSubText}>
          Camera permission is required to scan barcodes and QR codes.
        </Text>
        <TouchableOpacity 
          style={styles.permissionButton}
          onPress={() => navigation.goBack()}
        >
          <Text style={styles.permissionButtonText}>Go Back</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color="#fff" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Scan Barcode/QR Code</Text>
        <TouchableOpacity onPress={toggleFlash} style={styles.flashButton}>
          <Ionicons 
            name={flashOn ? "flash" : "flash-off"} 
            size={24} 
            color="#fff" 
          />
        </TouchableOpacity>
      </View>

      <View style={styles.scannerContainer}>
        {/* In a real app, this would be the actual barcode scanner component */}
        {/* <BarCodeScanner
          onBarCodeScanned={scanned ? undefined : handleBarCodeScanned}
          style={StyleSheet.absoluteFillObject}
          flashMode={flashOn ? 'torch' : 'off'}
        /> */}
        
        {/* Mock scanner UI */}
        <View style={styles.mockScanner}>
          {scanning ? (
            <>
              <View style={styles.scannerFrame}>
                <View style={styles.scannerCorner1} />
                <View style={styles.scannerCorner2} />
                <View style={styles.scannerCorner3} />
                <View style={styles.scannerCorner4} />
              </View>
              <Text style={styles.scannerText}>Position barcode within frame</Text>
              
              {/* For demo purposes only - simulate a scan */}
              <TouchableOpacity 
                style={styles.simulateButton}
                onPress={simulateScan}
              >
                <Text style={styles.simulateButtonText}>Simulate Scan</Text>
              </TouchableOpacity>
            </>
          ) : (
            <View style={styles.scannedContainer}>
              {loading ? (
                <>
                  <ActivityIndicator size="large" color="#3b82f6" />
                  <Text style={styles.loadingText}>Looking up item...</Text>
                </>
              ) : (
                <>
                  <Ionicons name="checkmark-circle" size={50} color="#10b981" />
                  <Text style={styles.scannedText}>Code Scanned!</Text>
                  <TouchableOpacity 
                    style={styles.scanAgainButton}
                    onPress={handleScanAgain}
                  >
                    <Text style={styles.scanAgainButtonText}>Scan Again</Text>
                  </TouchableOpacity>
                </>
              )}
            </View>
          )}
        </View>
      </View>

      <View style={styles.historyContainer}>
        <Text style={styles.historyTitle}>Recent Scans</Text>
        {scanHistory.length === 0 ? (
          <Text style={styles.noHistoryText}>No recent scans</Text>
        ) : (
          scanHistory.slice(0, 3).map((scan, index) => (
            <TouchableOpacity 
              key={index} 
              style={styles.historyItem}
              onPress={() => lookupScannedItem(scan.code)}
            >
              <View style={styles.historyIconContainer}>
                <Ionicons 
                  name={scan.type.includes('QR') ? "qr-code" : "barcode"} 
                  size={24} 
                  color="#3b82f6" 
                />
              </View>
              <View style={styles.historyContent}>
                <Text style={styles.historyCode}>{scan.code}</Text>
                <Text style={styles.historyType}>{scan.type} • {scan.timestamp.toLocaleTimeString()}</Text>
              </View>
              <Ionicons name="chevron-forward" size={20} color="#757575" />
            </TouchableOpacity>
          ))
        )}
      </View>

      <View style={styles.footer}>
        <TouchableOpacity 
          style={styles.footerButton}
          onPress={() => navigation.navigate('Inventory')}
        >
          <Ionicons name="list" size={20} color="#3b82f6" />
          <Text style={styles.footerButtonText}>Inventory</Text>
        </TouchableOpacity>
        <TouchableOpacity 
          style={styles.footerButton}
          onPress={() => navigation.navigate('Equipment')}
        >
          <Ionicons name="construct" size={20} color="#3b82f6" />
          <Text style={styles.footerButtonText}>Equipment</Text>
        </TouchableOpacity>
        <TouchableOpacity 
          style={styles.footerButton}
          onPress={() => console.log('Manual entry')}
        >
          <Ionicons name="create" size={20} color="#3b82f6" />
          <Text style={styles.footerButtonText}>Manual Entry</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const { width } = Dimensions.get('window');
const scannerSize = width * 0.7;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  permissionContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    padding: 20,
  },
  permissionText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#212121',
    marginTop: 20,
    marginBottom: 10,
    textAlign: 'center',
  },
  permissionSubText: {
    fontSize: 14,
    color: '#757575',
    textAlign: 'center',
    marginBottom: 20,
  },
  permissionButton: {
    backgroundColor: '#3b82f6',
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 4,
  },
  permissionButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '500',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 15,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
  },
  backButton: {
    padding: 5,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#fff',
  },
  flashButton: {
    padding: 5,
  },
  scannerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  mockScanner: {
    flex: 1,
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
  },
  scannerFrame: {
    width: scannerSize,
    height: scannerSize,
    position: 'relative',
  },
  scannerCorner1: {
    position: 'absolute',
    top: 0,
    left: 0,
    width: 30,
    height: 30,
    borderTopWidth: 2,
    borderLeftWidth: 2,
    borderColor: '#3b82f6',
  },
  scannerCorner2: {
    position: 'absolute',
    top: 0,
    right: 0,
    width: 30,
    height: 30,
    borderTopWidth: 2,
    borderRightWidth: 2,
    borderColor: '#3b82f6',
  },
  scannerCorner3: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    width: 30,
    height: 30,
    borderBottomWidth: 2,
    borderLeftWidth: 2,
    borderColor: '#3b82f6',
  },
  scannerCorner4: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    width: 30,
    height: 30,
    borderBottomWidth: 2,
    borderRightWidth: 2,
    borderColor: '#3b82f6',
  },
  scannerText: {
    color: '#fff',
    fontSize: 16,
    marginTop: 20,
    textAlign: 'center',
  },
  simulateButton: {
    marginTop: 20,
    backgroundColor: '#3b82f6',
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 4,
  },
  simulateButtonText: {
    color: '#fff',
    fontSize: 16,
  },
  scannedContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  scannedText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 10,
    marginBottom: 20,
  },
  loadingText: {
    color: '#fff',
    fontSize: 16,
    marginTop: 10,
  },
  scanAgainButton: {
    backgroundColor: '#3b82f6',
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 4,
  },
  scanAgainButtonText: {
    color: '#fff',
    fontSize: 16,
  },
  historyContainer: {
    backgroundColor: '#fff',
    padding: 15,
    borderTopLeftRadius: 15,
    borderTopRightRadius: 15,
  },
  historyTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#212121',
    marginBottom: 10,
  },
  noHistoryText: {
    color: '#757575',
    fontSize: 14,
    textAlign: 'center',
    paddingVertical: 15,
  },
  historyItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  historyIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#f0f9ff',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 10,
  },
  historyContent: {
    flex: 1,
  },
  historyCode: {
    fontSize: 14,
    fontWeight: '500',
    color: '#212121',
  },
  historyType: {
    fontSize: 12,
    color: '#757575',
  },
  footer: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    paddingVertical: 10,
    paddingHorizontal: 15,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  footerButton: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 10,
  },
  footerButtonText: {
    fontSize: 12,
    color: '#3b82f6',
    marginTop: 5,
  },
});

export default BarcodeScannerScreen;