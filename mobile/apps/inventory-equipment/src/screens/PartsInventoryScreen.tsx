import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, FlatList, TouchableOpacity, TextInput, ActivityIndicator } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { PartsInventoryScreenNavigationProp, PartItem } from '../types/navigation';

const PartsInventoryScreen = () => {
  const navigation = useNavigation<PartsInventoryScreenNavigationProp>();
  const [searchQuery, setSearchQuery] = useState('');
  const [filterCategory, setFilterCategory] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [partsItems, setPartsItems] = useState<PartItem[]>([]);

  // Categories for filtering
  const categories = ['Engine', 'Hydraulic', 'Electrical', 'Transmission', 'Consumables'];

  useEffect(() => {
    // In a real implementation, this would fetch parts items from an API
    const fetchPartsItems = async () => {
      try {
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Mock parts data
        const mockPartsItems: PartItem[] = [
          {
            id: 'part1',
            name: 'Oil Filter',
            category: 'Engine',
            partNumber: 'OF-12345',
            quantity: 15,
            minimumLevel: 5,
            location: 'Shelf A1',
            supplier: 'AutoParts Inc.',
            price: 12.99,
            lastOrdered: '2023-05-15',
            compatibleEquipment: ['John Deere 8R Tractor', 'Case IH Harvester'],
            notes: 'Standard oil filter for diesel engines',
          },
          {
            id: 'part2',
            name: 'Hydraulic Hose',
            category: 'Hydraulic',
            partNumber: 'HH-67890',
            quantity: 3,
            minimumLevel: 5,
            location: 'Shelf B2',
            supplier: 'FluidPower Supply',
            price: 45.50,
            lastOrdered: '2023-04-20',
            compatibleEquipment: ['John Deere 8R Tractor', 'Valley Center Pivot'],
            notes: 'High-pressure hydraulic hose, 3/4 inch diameter',
          },
          {
            id: 'part3',
            name: 'Battery',
            category: 'Electrical',
            partNumber: 'BAT-54321',
            quantity: 2,
            minimumLevel: 2,
            location: 'Shelf C3',
            supplier: 'ElectroPower Co.',
            price: 89.99,
            lastOrdered: '2023-06-01',
            compatibleEquipment: ['John Deere 8R Tractor', 'Kubota Utility Vehicle'],
            notes: '12V heavy-duty battery',
          },
          {
            id: 'part4',
            name: 'Transmission Fluid',
            category: 'Transmission',
            partNumber: 'TF-13579',
            quantity: 8,
            minimumLevel: 3,
            location: 'Shelf D4',
            supplier: 'LubeTech',
            price: 22.75,
            lastOrdered: '2023-05-10',
            compatibleEquipment: ['John Deere 8R Tractor', 'Case IH Harvester', 'Kubota Utility Vehicle'],
            notes: 'Synthetic transmission fluid, 1 gallon containers',
          },
          {
            id: 'part5',
            name: 'Air Filter',
            category: 'Engine',
            partNumber: 'AF-24680',
            quantity: 0,
            minimumLevel: 4,
            location: 'Shelf A2',
            supplier: 'AutoParts Inc.',
            price: 18.50,
            lastOrdered: '2023-03-15',
            compatibleEquipment: ['John Deere 8R Tractor'],
            notes: 'Heavy-duty air filter for dusty conditions',
          },
          {
            id: 'part6',
            name: 'Spark Plugs',
            category: 'Engine',
            partNumber: 'SP-97531',
            quantity: 24,
            minimumLevel: 10,
            location: 'Shelf A3',
            supplier: 'AutoParts Inc.',
            price: 5.99,
            lastOrdered: '2023-06-10',
            compatibleEquipment: ['Kubota Utility Vehicle', 'Small Engines'],
            notes: 'Standard spark plugs, sold individually',
          },
          {
            id: 'part7',
            name: 'Fuses',
            category: 'Electrical',
            partNumber: 'FU-86420',
            quantity: 50,
            minimumLevel: 20,
            location: 'Shelf C1',
            supplier: 'ElectroPower Co.',
            price: 0.99,
            lastOrdered: '2023-05-25',
            compatibleEquipment: ['All Equipment'],
            notes: 'Assorted fuses, various amperage ratings',
          },
          {
            id: 'part8',
            name: 'Grease',
            category: 'Consumables',
            partNumber: 'GR-75319',
            quantity: 6,
            minimumLevel: 4,
            location: 'Shelf E1',
            supplier: 'LubeTech',
            price: 8.75,
            lastOrdered: '2023-06-05',
            compatibleEquipment: ['All Equipment'],
            notes: 'Multi-purpose grease, 14oz tubes',
          },
        ];
        
        setPartsItems(mockPartsItems);
      } catch (error) {
        console.error('Error fetching parts items:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchPartsItems();
  }, []);

  // Filter parts items based on search query and category filter
  const filteredItems = partsItems.filter(item => {
    const matchesSearch = item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         item.partNumber.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         item.category.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         item.supplier.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesFilter = filterCategory ? item.category === filterCategory : true;
    return matchesSearch && matchesFilter;
  });

  const getLowStockStatus = (item: PartItem) => {
    if (item.quantity === 0) {
      return 'out';
    } else if (item.quantity <= item.minimumLevel) {
      return 'low';
    }
    return 'normal';
  };

  const renderPartItem = ({ item }: { item: PartItem }) => {
    const stockStatus = getLowStockStatus(item);
    
    return (
      <TouchableOpacity 
        style={styles.partItem}
        onPress={() => navigation.navigate('PartDetail', { partId: item.id })}
      >
        <View style={styles.itemHeader}>
          <Text style={styles.itemName}>{item.name}</Text>
          {stockStatus === 'out' && (
            <View style={[styles.stockBadge, styles.outOfStockBadge]}>
              <Text style={styles.stockText}>Out of Stock</Text>
            </View>
          )}
          {stockStatus === 'low' && (
            <View style={[styles.stockBadge, styles.lowStockBadge]}>
              <Text style={styles.stockText}>Low Stock</Text>
            </View>
          )}
        </View>
        
        <View style={styles.itemDetails}>
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Part #:</Text>
            <Text style={styles.detailText}>{item.partNumber}</Text>
          </View>
          
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Category:</Text>
            <Text style={styles.detailText}>{item.category}</Text>
          </View>
          
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Quantity:</Text>
            <Text style={[
              styles.detailText, 
              stockStatus === 'out' && styles.outOfStockText,
              stockStatus === 'low' && styles.lowStockText
            ]}>
              {item.quantity} {item.quantity === 1 ? 'unit' : 'units'}
            </Text>
          </View>
          
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Location:</Text>
            <Text style={styles.detailText}>{item.location}</Text>
          </View>
        </View>
        
        <View style={styles.itemFooter}>
          <Text style={styles.compatibleText} numberOfLines={1} ellipsizeMode="tail">
            Compatible: {item.compatibleEquipment.join(', ')}
          </Text>
        </View>
      </TouchableOpacity>
    );
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#3b82f6" />
        <Text style={styles.loadingText}>Loading parts inventory...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.searchContainer}>
        <Ionicons name="search" size={20} color="#757575" style={styles.searchIcon} />
        <TextInput
          style={styles.searchInput}
          placeholder="Search parts..."
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
      </View>

      <View style={styles.filterContainer}>
        <TouchableOpacity
          style={[styles.filterButton, filterCategory === null && styles.filterButtonActive]}
          onPress={() => setFilterCategory(null)}
        >
          <Text style={[styles.filterButtonText, filterCategory === null && styles.filterButtonTextActive]}>All</Text>
        </TouchableOpacity>
        
        {categories.map((category) => (
          <TouchableOpacity
            key={category}
            style={[styles.filterButton, filterCategory === category && styles.filterButtonActive]}
            onPress={() => setFilterCategory(category)}
          >
            <Text style={[styles.filterButtonText, filterCategory === category && styles.filterButtonTextActive]}>
              {category}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      <View style={styles.statsContainer}>
        <View style={styles.statItem}>
          <Text style={styles.statValue}>{partsItems.length}</Text>
          <Text style={styles.statLabel}>Total Parts</Text>
        </View>
        <View style={styles.statItem}>
          <Text style={[styles.statValue, { color: '#ef4444' }]}>
            {partsItems.filter(item => getLowStockStatus(item) === 'low').length}
          </Text>
          <Text style={styles.statLabel}>Low Stock</Text>
        </View>
        <View style={styles.statItem}>
          <Text style={[styles.statValue, { color: '#ef4444' }]}>
            {partsItems.filter(item => getLowStockStatus(item) === 'out').length}
          </Text>
          <Text style={styles.statLabel}>Out of Stock</Text>
        </View>
      </View>

      <FlatList
        data={filteredItems}
        renderItem={renderPartItem}
        keyExtractor={item => item.id}
        contentContainerStyle={styles.partsList}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Ionicons name="construct" size={50} color="#BDBDBD" />
            <Text style={styles.emptyText}>No parts found</Text>
          </View>
        }
      />

      <View style={styles.actionButtonsContainer}>
        <TouchableOpacity 
          style={[styles.actionButton, { backgroundColor: '#3b82f6' }]}
          onPress={() => console.log('Order parts')}
        >
          <Ionicons name="cart" size={20} color="#fff" />
          <Text style={styles.actionButtonText}>Order Parts</Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={[styles.actionButton, { backgroundColor: '#10b981' }]}
          onPress={() => navigation.navigate('BarcodeScanner')}
        >
          <Ionicons name="barcode" size={20} color="#fff" />
          <Text style={styles.actionButtonText}>Scan Part</Text>
        </TouchableOpacity>
      </View>

      <TouchableOpacity 
        style={styles.fab}
        onPress={() => console.log('Add part')}
      >
        <Ionicons name="add" size={24} color="#fff" />
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#757575',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderRadius: 8,
    margin: 15,
    paddingHorizontal: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  searchIcon: {
    marginRight: 10,
  },
  searchInput: {
    flex: 1,
    height: 50,
    fontSize: 16,
  },
  filterContainer: {
    flexDirection: 'row',
    paddingHorizontal: 15,
    marginBottom: 10,
    flexWrap: 'wrap',
  },
  filterButton: {
    paddingVertical: 8,
    paddingHorizontal: 12,
    marginRight: 8,
    marginBottom: 8,
    borderRadius: 20,
    backgroundColor: '#E0E0E0',
  },
  filterButtonActive: {
    backgroundColor: '#3b82f6',
  },
  filterButtonText: {
    color: '#757575',
    fontSize: 14,
  },
  filterButtonTextActive: {
    color: '#fff',
  },
  statsContainer: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    margin: 15,
    marginTop: 0,
    marginBottom: 10,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 12,
  },
  statValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#212121',
  },
  statLabel: {
    fontSize: 12,
    color: '#757575',
    marginTop: 4,
  },
  partsList: {
    padding: 15,
    paddingTop: 0,
    paddingBottom: 120, // Add padding for action buttons and FAB
  },
  partItem: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 15,
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  itemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  itemName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#212121',
    flex: 1,
  },
  stockBadge: {
    paddingVertical: 4,
    paddingHorizontal: 8,
    borderRadius: 4,
  },
  lowStockBadge: {
    backgroundColor: '#f59e0b',
  },
  outOfStockBadge: {
    backgroundColor: '#ef4444',
  },
  stockText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '500',
  },
  itemDetails: {
    borderTopWidth: 1,
    borderTopColor: '#E0E0E0',
    paddingTop: 10,
  },
  detailRow: {
    flexDirection: 'row',
    marginBottom: 5,
  },
  detailLabel: {
    fontSize: 14,
    color: '#757575',
    width: 80,
  },
  detailText: {
    fontSize: 14,
    color: '#212121',
    flex: 1,
  },
  lowStockText: {
    color: '#f59e0b',
    fontWeight: '500',
  },
  outOfStockText: {
    color: '#ef4444',
    fontWeight: '500',
  },
  itemFooter: {
    marginTop: 10,
    paddingTop: 10,
    borderTopWidth: 1,
    borderTopColor: '#E0E0E0',
  },
  compatibleText: {
    fontSize: 12,
    color: '#757575',
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 50,
  },
  emptyText: {
    fontSize: 16,
    color: '#757575',
    marginTop: 10,
  },
  actionButtonsContainer: {
    position: 'absolute',
    bottom: 80,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingHorizontal: 15,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 25,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 3,
    elevation: 3,
  },
  actionButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 5,
  },
  fab: {
    position: 'absolute',
    bottom: 20,
    right: 20,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: '#3b82f6',
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
});

export default PartsInventoryScreen;