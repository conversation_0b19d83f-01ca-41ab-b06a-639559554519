import React, { useState, useEffect } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  ScrollView, 
  TouchableOpacity, 
  TextInput, 
  ActivityIndicator,
  Alert
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { RouteProp, useNavigation, useRoute } from '@react-navigation/native';
import { InventoryDetailScreenRouteProp, InventoryItem } from '../types/navigation';

const InventoryDetailScreen = () => {
  const navigation = useNavigation();
  const route = useRoute<InventoryDetailScreenRouteProp>();
  const { inventoryId } = route.params;
  
  const [loading, setLoading] = useState(true);
  const [editing, setEditing] = useState(false);
  const [inventoryItem, setInventoryItem] = useState<InventoryItem | null>(null);
  const [editedItem, setEditedItem] = useState<InventoryItem | null>(null);

  useEffect(() => {
    // In a real implementation, this would fetch the inventory item from an API
    const fetchInventoryItem = async () => {
      try {
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Mock inventory item data
        const mockInventoryItem: InventoryItem = {
          id: inventoryId,
          name: 'Corn Seeds',
          category: 'Seeds',
          quantity: 500,
          unit: 'kg',
          location: 'Warehouse A',
          supplier: 'AgriSeeds Inc.',
          lastUpdated: '2023-06-15',
          minimumLevel: 100,
          notes: 'High-yield variety, plant in spring',
          purchasePrice: 5.99,
          purchaseDate: '2023-05-10',
          expiryDate: '2024-05-10',
          barcodeId: 'SEED-CORN-001',
        };
        
        setInventoryItem(mockInventoryItem);
        setEditedItem(mockInventoryItem);
      } catch (error) {
        console.error('Error fetching inventory item:', error);
        Alert.alert('Error', 'Failed to load inventory item details');
      } finally {
        setLoading(false);
      }
    };

    fetchInventoryItem();
  }, [inventoryId]);

  const handleSave = async () => {
    if (!editedItem) return;
    
    setLoading(true);
    try {
      // Simulate API call to update the item
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Update the item in state
      setInventoryItem(editedItem);
      setEditing(false);
      Alert.alert('Success', 'Inventory item updated successfully');
    } catch (error) {
      console.error('Error updating inventory item:', error);
      Alert.alert('Error', 'Failed to update inventory item');
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    setEditedItem(inventoryItem);
    setEditing(false);
  };

  const handleDelete = async () => {
    Alert.alert(
      'Confirm Delete',
      'Are you sure you want to delete this inventory item?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            setLoading(true);
            try {
              // Simulate API call to delete the item
              await new Promise(resolve => setTimeout(resolve, 1000));
              
              Alert.alert('Success', 'Inventory item deleted successfully');
              navigation.goBack();
            } catch (error) {
              console.error('Error deleting inventory item:', error);
              Alert.alert('Error', 'Failed to delete inventory item');
              setLoading(false);
            }
          },
        },
      ]
    );
  };

  const handleUpdateQuantity = () => {
    if (!inventoryItem) return;
    
    Alert.prompt(
      'Update Quantity',
      `Current quantity: ${inventoryItem.quantity} ${inventoryItem.unit}`,
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Update',
          onPress: async (value) => {
            if (!value || isNaN(Number(value))) {
              Alert.alert('Error', 'Please enter a valid number');
              return;
            }
            
            setLoading(true);
            try {
              // Simulate API call to update the quantity
              await new Promise(resolve => setTimeout(resolve, 1000));
              
              const updatedItem = {
                ...inventoryItem,
                quantity: Number(value),
                lastUpdated: new Date().toISOString().split('T')[0],
              };
              
              setInventoryItem(updatedItem);
              setEditedItem(updatedItem);
              Alert.alert('Success', 'Quantity updated successfully');
            } catch (error) {
              console.error('Error updating quantity:', error);
              Alert.alert('Error', 'Failed to update quantity');
            } finally {
              setLoading(false);
            }
          },
        },
      ],
      'plain-text',
      inventoryItem.quantity.toString()
    );
  };

  const renderDetailItem = (label: string, value: string | number | undefined) => (
    <View style={styles.detailItem}>
      <Text style={styles.detailLabel}>{label}</Text>
      <Text style={styles.detailValue}>{value || 'N/A'}</Text>
    </View>
  );

  const renderEditableDetailItem = (
    label: string, 
    value: string | number | undefined, 
    field: keyof InventoryItem,
    keyboardType: 'default' | 'numeric' = 'default'
  ) => (
    <View style={styles.editableDetailItem}>
      <Text style={styles.detailLabel}>{label}</Text>
      <TextInput
        style={styles.editInput}
        value={value?.toString() || ''}
        onChangeText={(text) => {
          if (!editedItem) return;
          
          if (keyboardType === 'numeric') {
            setEditedItem({
              ...editedItem,
              [field]: text === '' ? '' : Number(text),
            });
          } else {
            setEditedItem({
              ...editedItem,
              [field]: text,
            });
          }
        }}
        keyboardType={keyboardType}
      />
    </View>
  );

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#3b82f6" />
        <Text style={styles.loadingText}>Loading inventory item...</Text>
      </View>
    );
  }

  if (!inventoryItem || !editedItem) {
    return (
      <View style={styles.errorContainer}>
        <Ionicons name="alert-circle" size={50} color="#ef4444" />
        <Text style={styles.errorText}>Failed to load inventory item</Text>
        <TouchableOpacity style={styles.button} onPress={() => navigation.goBack()}>
          <Text style={styles.buttonText}>Go Back</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color="#212121" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>
          {editing ? 'Edit Inventory Item' : inventoryItem.name}
        </Text>
        {!editing && (
          <TouchableOpacity onPress={() => setEditing(true)} style={styles.editButton}>
            <Ionicons name="create" size={24} color="#3b82f6" />
          </TouchableOpacity>
        )}
      </View>

      <ScrollView style={styles.content}>
        {!editing ? (
          // View mode
          <>
            <View style={styles.card}>
              <View style={styles.cardHeader}>
                <Text style={styles.cardTitle}>Basic Information</Text>
              </View>
              <View style={styles.cardContent}>
                {renderDetailItem('Name', inventoryItem.name)}
                {renderDetailItem('Category', inventoryItem.category)}
                {renderDetailItem('Quantity', `${inventoryItem.quantity} ${inventoryItem.unit}`)}
                {renderDetailItem('Minimum Level', inventoryItem.minimumLevel ? `${inventoryItem.minimumLevel} ${inventoryItem.unit}` : 'Not set')}
                {renderDetailItem('Location', inventoryItem.location)}
              </View>
            </View>

            <View style={styles.card}>
              <View style={styles.cardHeader}>
                <Text style={styles.cardTitle}>Supplier Information</Text>
              </View>
              <View style={styles.cardContent}>
                {renderDetailItem('Supplier', inventoryItem.supplier)}
                {renderDetailItem('Purchase Price', inventoryItem.purchasePrice ? `$${inventoryItem.purchasePrice}` : 'Not recorded')}
                {renderDetailItem('Purchase Date', inventoryItem.purchaseDate ? new Date(inventoryItem.purchaseDate).toLocaleDateString() : 'Not recorded')}
              </View>
            </View>

            <View style={styles.card}>
              <View style={styles.cardHeader}>
                <Text style={styles.cardTitle}>Additional Information</Text>
              </View>
              <View style={styles.cardContent}>
                {renderDetailItem('Barcode ID', inventoryItem.barcodeId)}
                {renderDetailItem('Expiry Date', inventoryItem.expiryDate ? new Date(inventoryItem.expiryDate).toLocaleDateString() : 'Not applicable')}
                {renderDetailItem('Last Updated', new Date(inventoryItem.lastUpdated).toLocaleDateString())}
                {renderDetailItem('Notes', inventoryItem.notes)}
              </View>
            </View>

            <View style={styles.actionsContainer}>
              <TouchableOpacity style={styles.actionButton} onPress={handleUpdateQuantity}>
                <Ionicons name="refresh" size={20} color="#fff" />
                <Text style={styles.actionButtonText}>Update Quantity</Text>
              </TouchableOpacity>
              <TouchableOpacity style={[styles.actionButton, styles.deleteButton]} onPress={handleDelete}>
                <Ionicons name="trash" size={20} color="#fff" />
                <Text style={styles.actionButtonText}>Delete Item</Text>
              </TouchableOpacity>
            </View>
          </>
        ) : (
          // Edit mode
          <>
            <View style={styles.card}>
              <View style={styles.cardHeader}>
                <Text style={styles.cardTitle}>Basic Information</Text>
              </View>
              <View style={styles.cardContent}>
                {renderEditableDetailItem('Name', editedItem.name, 'name')}
                {renderEditableDetailItem('Category', editedItem.category, 'category')}
                {renderEditableDetailItem('Quantity', editedItem.quantity, 'quantity', 'numeric')}
                {renderEditableDetailItem('Unit', editedItem.unit, 'unit')}
                {renderEditableDetailItem('Minimum Level', editedItem.minimumLevel, 'minimumLevel', 'numeric')}
                {renderEditableDetailItem('Location', editedItem.location, 'location')}
              </View>
            </View>

            <View style={styles.card}>
              <View style={styles.cardHeader}>
                <Text style={styles.cardTitle}>Supplier Information</Text>
              </View>
              <View style={styles.cardContent}>
                {renderEditableDetailItem('Supplier', editedItem.supplier, 'supplier')}
                {renderEditableDetailItem('Purchase Price', editedItem.purchasePrice, 'purchasePrice', 'numeric')}
                {renderEditableDetailItem('Purchase Date', editedItem.purchaseDate, 'purchaseDate')}
              </View>
            </View>

            <View style={styles.card}>
              <View style={styles.cardHeader}>
                <Text style={styles.cardTitle}>Additional Information</Text>
              </View>
              <View style={styles.cardContent}>
                {renderEditableDetailItem('Barcode ID', editedItem.barcodeId, 'barcodeId')}
                {renderEditableDetailItem('Expiry Date', editedItem.expiryDate, 'expiryDate')}
                {renderEditableDetailItem('Notes', editedItem.notes, 'notes')}
              </View>
            </View>

            <View style={styles.editActionsContainer}>
              <TouchableOpacity style={styles.cancelButton} onPress={handleCancel}>
                <Text style={styles.cancelButtonText}>Cancel</Text>
              </TouchableOpacity>
              <TouchableOpacity style={styles.saveButton} onPress={handleSave}>
                <Text style={styles.saveButtonText}>Save Changes</Text>
              </TouchableOpacity>
            </View>
          </>
        )}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#757575',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    padding: 20,
  },
  errorText: {
    fontSize: 18,
    color: '#212121',
    marginTop: 10,
    marginBottom: 20,
    textAlign: 'center',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 15,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  backButton: {
    marginRight: 10,
  },
  headerTitle: {
    flex: 1,
    fontSize: 18,
    fontWeight: 'bold',
    color: '#212121',
  },
  editButton: {
    padding: 5,
  },
  content: {
    flex: 1,
    padding: 15,
  },
  card: {
    backgroundColor: '#fff',
    borderRadius: 8,
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  cardHeader: {
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  cardTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#212121',
  },
  cardContent: {
    padding: 15,
  },
  detailItem: {
    marginBottom: 12,
  },
  detailLabel: {
    fontSize: 14,
    color: '#757575',
    marginBottom: 4,
  },
  detailValue: {
    fontSize: 16,
    color: '#212121',
  },
  editableDetailItem: {
    marginBottom: 15,
  },
  editInput: {
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 4,
    padding: 10,
    fontSize: 16,
    color: '#212121',
    backgroundColor: '#f9f9f9',
  },
  actionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 30,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#3b82f6',
    borderRadius: 4,
    padding: 12,
    flex: 1,
    marginRight: 10,
  },
  deleteButton: {
    backgroundColor: '#ef4444',
    marginRight: 0,
  },
  actionButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 5,
  },
  editActionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 30,
  },
  cancelButton: {
    flex: 1,
    padding: 12,
    borderRadius: 4,
    backgroundColor: '#e0e0e0',
    alignItems: 'center',
    marginRight: 10,
  },
  cancelButtonText: {
    color: '#212121',
    fontSize: 14,
    fontWeight: '500',
  },
  saveButton: {
    flex: 1,
    padding: 12,
    borderRadius: 4,
    backgroundColor: '#3b82f6',
    alignItems: 'center',
  },
  saveButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '500',
  },
  button: {
    backgroundColor: '#3b82f6',
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 4,
    marginTop: 10,
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '500',
  },
});

export default InventoryDetailScreen;