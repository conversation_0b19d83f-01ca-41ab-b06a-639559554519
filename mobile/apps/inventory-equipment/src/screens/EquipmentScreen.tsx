import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, FlatList, TouchableOpacity, TextInput, ActivityIndicator } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { EquipmentScreenNavigationProp, EquipmentItem } from '../types/navigation';

const EquipmentScreen = () => {
  const navigation = useNavigation<EquipmentScreenNavigationProp>();
  const [searchQuery, setSearchQuery] = useState('');
  const [filterCategory, setFilterCategory] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [equipmentItems, setEquipmentItems] = useState<EquipmentItem[]>([]);

  // Categories for filtering
  const categories = ['Tractors', 'Harvesters', 'Irrigation', 'Tools', 'Vehicles'];

  useEffect(() => {
    // In a real implementation, this would fetch equipment items from an API
    const fetchEquipmentItems = async () => {
      try {
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Mock equipment data
        const mockEquipmentItems: EquipmentItem[] = [
          {
            id: 'eq1',
            name: 'John Deere 8R Tractor',
            category: 'Tractors',
            status: 'operational',
            location: 'Field A',
            serialNumber: 'JD8R-12345',
            purchaseDate: '2022-03-15',
            lastMaintenance: '2023-05-20',
            nextMaintenance: '2023-08-20',
            hoursUsed: 1250,
            notes: 'Regular maintenance required every 500 hours',
          },
          {
            id: 'eq2',
            name: 'Case IH Harvester',
            category: 'Harvesters',
            status: 'maintenance',
            location: 'Maintenance Shed',
            serialNumber: 'CIH-67890',
            purchaseDate: '2021-07-10',
            lastMaintenance: '2023-06-05',
            nextMaintenance: '2023-07-05',
            hoursUsed: 980,
            notes: 'Currently undergoing scheduled maintenance',
          },
          {
            id: 'eq3',
            name: 'Valley Center Pivot',
            category: 'Irrigation',
            status: 'operational',
            location: 'Field B',
            serialNumber: 'VCP-54321',
            purchaseDate: '2020-04-22',
            lastMaintenance: '2023-04-10',
            nextMaintenance: '2023-10-10',
            hoursUsed: 3200,
            notes: 'Biannual maintenance schedule',
          },
          {
            id: 'eq4',
            name: 'Kubota Utility Vehicle',
            category: 'Vehicles',
            status: 'repair',
            location: 'Repair Shop',
            serialNumber: 'KUV-13579',
            purchaseDate: '2022-01-05',
            lastMaintenance: '2023-02-15',
            nextMaintenance: '2023-08-15',
            hoursUsed: 750,
            notes: 'Transmission issues, awaiting parts',
          },
          {
            id: 'eq5',
            name: 'Power Drill Set',
            category: 'Tools',
            status: 'operational',
            location: 'Tool Shed',
            serialNumber: 'PDS-24680',
            purchaseDate: '2022-11-30',
            lastMaintenance: '2023-05-01',
            nextMaintenance: '2023-11-01',
            hoursUsed: 120,
            notes: 'Regular cleaning and battery maintenance',
          },
          {
            id: 'eq6',
            name: 'Sprayer System',
            category: 'Tractors',
            status: 'operational',
            location: 'Equipment Shed',
            serialNumber: 'SS-97531',
            purchaseDate: '2021-09-18',
            lastMaintenance: '2023-06-01',
            nextMaintenance: '2023-09-01',
            hoursUsed: 450,
            notes: 'Check nozzles and filters regularly',
          },
        ];
        
        setEquipmentItems(mockEquipmentItems);
      } catch (error) {
        console.error('Error fetching equipment items:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchEquipmentItems();
  }, []);

  // Filter equipment items based on search query and category filter
  const filteredItems = equipmentItems.filter(item => {
    const matchesSearch = item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         item.category.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         item.location.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesFilter = filterCategory ? item.category === filterCategory : true;
    return matchesSearch && matchesFilter;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'operational':
        return '#10b981'; // green
      case 'maintenance':
        return '#f59e0b'; // amber
      case 'repair':
        return '#ef4444'; // red
      default:
        return '#6b7280'; // gray
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'operational':
        return 'Operational';
      case 'maintenance':
        return 'Maintenance';
      case 'repair':
        return 'Needs Repair';
      default:
        return 'Unknown';
    }
  };

  const renderEquipmentItem = ({ item }: { item: EquipmentItem }) => {
    const statusColor = getStatusColor(item.status);
    const statusLabel = getStatusLabel(item.status);
    
    // Calculate if maintenance is due soon (within 30 days)
    const today = new Date();
    const nextMaintenance = new Date(item.nextMaintenance);
    const daysDifference = Math.ceil((nextMaintenance.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
    const maintenanceSoon = daysDifference > 0 && daysDifference <= 30;
    
    return (
      <TouchableOpacity 
        style={styles.equipmentItem}
        onPress={() => navigation.navigate('EquipmentDetail', { equipmentId: item.id })}
      >
        <View style={styles.itemHeader}>
          <Text style={styles.itemName}>{item.name}</Text>
          <View style={[styles.statusBadge, { backgroundColor: statusColor }]}>
            <Text style={styles.statusText}>{statusLabel}</Text>
          </View>
        </View>
        
        <View style={styles.itemDetails}>
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Category:</Text>
            <Text style={styles.detailText}>{item.category}</Text>
          </View>
          
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Location:</Text>
            <Text style={styles.detailText}>{item.location}</Text>
          </View>
          
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Hours Used:</Text>
            <Text style={styles.detailText}>{item.hoursUsed}</Text>
          </View>
          
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Next Maintenance:</Text>
            <Text style={[
              styles.detailText, 
              maintenanceSoon && styles.maintenanceSoon
            ]}>
              {new Date(item.nextMaintenance).toLocaleDateString()}
              {maintenanceSoon && ' (Soon)'}
            </Text>
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#3b82f6" />
        <Text style={styles.loadingText}>Loading equipment items...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.searchContainer}>
        <Ionicons name="search" size={20} color="#757575" style={styles.searchIcon} />
        <TextInput
          style={styles.searchInput}
          placeholder="Search equipment..."
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
      </View>

      <View style={styles.filterContainer}>
        <TouchableOpacity
          style={[styles.filterButton, filterCategory === null && styles.filterButtonActive]}
          onPress={() => setFilterCategory(null)}
        >
          <Text style={[styles.filterButtonText, filterCategory === null && styles.filterButtonTextActive]}>All</Text>
        </TouchableOpacity>
        
        {categories.map((category) => (
          <TouchableOpacity
            key={category}
            style={[styles.filterButton, filterCategory === category && styles.filterButtonActive]}
            onPress={() => setFilterCategory(category)}
          >
            <Text style={[styles.filterButtonText, filterCategory === category && styles.filterButtonTextActive]}>
              {category}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      <FlatList
        data={filteredItems}
        renderItem={renderEquipmentItem}
        keyExtractor={item => item.id}
        contentContainerStyle={styles.equipmentList}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Ionicons name="construct" size={50} color="#BDBDBD" />
            <Text style={styles.emptyText}>No equipment items found</Text>
          </View>
        }
      />

      <TouchableOpacity 
        style={styles.fab}
        onPress={() => console.log('Add equipment item')}
      >
        <Ionicons name="add" size={24} color="#fff" />
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#757575',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderRadius: 8,
    margin: 15,
    paddingHorizontal: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  searchIcon: {
    marginRight: 10,
  },
  searchInput: {
    flex: 1,
    height: 50,
    fontSize: 16,
  },
  filterContainer: {
    flexDirection: 'row',
    paddingHorizontal: 15,
    marginBottom: 10,
    flexWrap: 'wrap',
  },
  filterButton: {
    paddingVertical: 8,
    paddingHorizontal: 12,
    marginRight: 8,
    marginBottom: 8,
    borderRadius: 20,
    backgroundColor: '#E0E0E0',
  },
  filterButtonActive: {
    backgroundColor: '#3b82f6',
  },
  filterButtonText: {
    color: '#757575',
    fontSize: 14,
  },
  filterButtonTextActive: {
    color: '#fff',
  },
  equipmentList: {
    padding: 15,
    paddingBottom: 80, // Add padding for FAB
  },
  equipmentItem: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 15,
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  itemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  itemName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#212121',
    flex: 1,
  },
  statusBadge: {
    paddingVertical: 4,
    paddingHorizontal: 8,
    borderRadius: 4,
  },
  statusText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '500',
  },
  itemDetails: {
    borderTopWidth: 1,
    borderTopColor: '#E0E0E0',
    paddingTop: 10,
  },
  detailRow: {
    flexDirection: 'row',
    marginBottom: 5,
  },
  detailLabel: {
    fontSize: 14,
    color: '#757575',
    width: 120,
  },
  detailText: {
    fontSize: 14,
    color: '#212121',
    flex: 1,
  },
  maintenanceSoon: {
    color: '#f59e0b',
    fontWeight: '500',
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 50,
  },
  emptyText: {
    fontSize: 16,
    color: '#757575',
    marginTop: 10,
  },
  fab: {
    position: 'absolute',
    bottom: 20,
    right: 20,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: '#3b82f6',
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
});

export default EquipmentScreen;