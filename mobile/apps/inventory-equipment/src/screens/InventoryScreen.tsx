import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, FlatList, TouchableOpacity, TextInput, ActivityIndicator } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { InventoryScreenNavigationProp, InventoryItem } from '../types/navigation';

const InventoryScreen = () => {
  const navigation = useNavigation<InventoryScreenNavigationProp>();
  const [searchQuery, setSearchQuery] = useState('');
  const [filterCategory, setFilterCategory] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [inventoryItems, setInventoryItems] = useState<InventoryItem[]>([]);

  // Categories for filtering
  const categories = ['Seeds', 'Fertilizer', 'Pesticide', 'Feed', 'Supplies'];

  useEffect(() => {
    // In a real implementation, this would fetch inventory items from an API
    const fetchInventoryItems = async () => {
      try {
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Mock inventory data
        const mockInventoryItems: InventoryItem[] = [
          {
            id: 'inv1',
            name: 'Corn Seeds',
            category: 'Seeds',
            quantity: 500,
            unit: 'kg',
            location: 'Warehouse A',
            supplier: 'AgriSeeds Inc.',
            lastUpdated: '2023-06-15',
            minimumLevel: 100,
            notes: 'High-yield variety, plant in spring',
          },
          {
            id: 'inv2',
            name: 'NPK Fertilizer',
            category: 'Fertilizer',
            quantity: 1000,
            unit: 'kg',
            location: 'Warehouse B',
            supplier: 'FarmChem Co.',
            lastUpdated: '2023-06-10',
            minimumLevel: 200,
          },
          {
            id: 'inv3',
            name: 'Organic Pesticide',
            category: 'Pesticide',
            quantity: 50,
            unit: 'L',
            location: 'Storage Room 1',
            supplier: 'EcoFarm Solutions',
            lastUpdated: '2023-06-20',
            minimumLevel: 10,
          },
          {
            id: 'inv4',
            name: 'Chicken Feed',
            category: 'Feed',
            quantity: 750,
            unit: 'kg',
            location: 'Feed Storage',
            supplier: 'Farm Nutrition Ltd.',
            lastUpdated: '2023-06-18',
            minimumLevel: 150,
          },
          {
            id: 'inv5',
            name: 'Irrigation Pipes',
            category: 'Supplies',
            quantity: 30,
            unit: 'pieces',
            location: 'Equipment Shed',
            lastUpdated: '2023-05-25',
          },
          {
            id: 'inv6',
            name: 'Tomato Seeds',
            category: 'Seeds',
            quantity: 100,
            unit: 'packets',
            location: 'Warehouse A',
            supplier: 'AgriSeeds Inc.',
            lastUpdated: '2023-06-12',
            minimumLevel: 20,
          },
        ];

        setInventoryItems(mockInventoryItems);
      } catch (error) {
        console.error('Error fetching inventory items:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchInventoryItems();
  }, []);

  // Filter inventory items based on search query and category filter
  const filteredItems = inventoryItems.filter(item => {
    const matchesSearch = item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         item.category.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         item.location.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesFilter = filterCategory ? item.category === filterCategory : true;
    return matchesSearch && matchesFilter;
  });

  const getLowStockStatus = (item: InventoryItem) => {
    if (item.minimumLevel && item.quantity <= item.minimumLevel) {
      return 'low';
    }
    return 'normal';
  };

  const renderInventoryItem = ({ item }: { item: InventoryItem }) => {
    const stockStatus = getLowStockStatus(item);

    return (
      <TouchableOpacity 
        style={styles.inventoryItem}
        onPress={() => navigation.navigate('InventoryDetail', { inventoryId: item.id })}
      >
        <View style={styles.itemHeader}>
          <Text style={styles.itemName}>{item.name}</Text>
          {stockStatus === 'low' && (
            <View style={styles.lowStockBadge}>
              <Text style={styles.lowStockText}>Low Stock</Text>
            </View>
          )}
        </View>

        <View style={styles.itemDetails}>
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Category:</Text>
            <Text style={styles.detailText}>{item.category}</Text>
          </View>

          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Quantity:</Text>
            <Text style={styles.detailText}>{item.quantity} {item.unit}</Text>
          </View>

          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Location:</Text>
            <Text style={styles.detailText}>{item.location}</Text>
          </View>

          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Last Updated:</Text>
            <Text style={styles.detailText}>{new Date(item.lastUpdated).toLocaleDateString()}</Text>
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#3b82f6" />
        <Text style={styles.loadingText}>Loading inventory items...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.searchContainer}>
        <Ionicons name="search" size={20} color="#757575" style={styles.searchIcon} />
        <TextInput
          style={styles.searchInput}
          placeholder="Search inventory..."
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
      </View>

      <View style={styles.filterContainer}>
        <TouchableOpacity
          style={[styles.filterButton, filterCategory === null && styles.filterButtonActive]}
          onPress={() => setFilterCategory(null)}
        >
          <Text style={[styles.filterButtonText, filterCategory === null && styles.filterButtonTextActive]}>All</Text>
        </TouchableOpacity>

        {categories.map((category) => (
          <TouchableOpacity
            key={category}
            style={[styles.filterButton, filterCategory === category && styles.filterButtonActive]}
            onPress={() => setFilterCategory(category)}
          >
            <Text style={[styles.filterButtonText, filterCategory === category && styles.filterButtonTextActive]}>
              {category}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      <FlatList
        data={filteredItems}
        renderItem={renderInventoryItem}
        keyExtractor={item => item.id}
        contentContainerStyle={styles.inventoryList}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Ionicons name="cube" size={50} color="#BDBDBD" />
            <Text style={styles.emptyText}>No inventory items found</Text>
          </View>
        }
      />

      <TouchableOpacity 
        style={styles.fab}
        onPress={() => navigation.navigate('AddInventory')}
      >
        <Ionicons name="add" size={24} color="#fff" />
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#757575',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderRadius: 8,
    margin: 15,
    paddingHorizontal: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  searchIcon: {
    marginRight: 10,
  },
  searchInput: {
    flex: 1,
    height: 50,
    fontSize: 16,
  },
  filterContainer: {
    flexDirection: 'row',
    paddingHorizontal: 15,
    marginBottom: 10,
    flexWrap: 'wrap',
  },
  filterButton: {
    paddingVertical: 8,
    paddingHorizontal: 12,
    marginRight: 8,
    marginBottom: 8,
    borderRadius: 20,
    backgroundColor: '#E0E0E0',
  },
  filterButtonActive: {
    backgroundColor: '#3b82f6',
  },
  filterButtonText: {
    color: '#757575',
    fontSize: 14,
  },
  filterButtonTextActive: {
    color: '#fff',
  },
  inventoryList: {
    padding: 15,
    paddingBottom: 80, // Add padding for FAB
  },
  inventoryItem: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 15,
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  itemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  itemName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#212121',
    flex: 1,
  },
  lowStockBadge: {
    backgroundColor: '#F44336',
    paddingVertical: 4,
    paddingHorizontal: 8,
    borderRadius: 4,
  },
  lowStockText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '500',
  },
  itemDetails: {
    borderTopWidth: 1,
    borderTopColor: '#E0E0E0',
    paddingTop: 10,
  },
  detailRow: {
    flexDirection: 'row',
    marginBottom: 5,
  },
  detailLabel: {
    fontSize: 14,
    color: '#757575',
    width: 100,
  },
  detailText: {
    fontSize: 14,
    color: '#212121',
    flex: 1,
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 50,
  },
  emptyText: {
    fontSize: 16,
    color: '#757575',
    marginTop: 10,
  },
  fab: {
    position: 'absolute',
    bottom: 20,
    right: 20,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: '#3b82f6',
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
});

export default InventoryScreen;
