import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, FlatList, TouchableOpacity, TextInput, ActivityIndicator } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { MaintenanceScreenNavigationProp, MaintenanceTask } from '../types/navigation';

const MaintenanceScreen = () => {
  const navigation = useNavigation<MaintenanceScreenNavigationProp>();
  const [searchQuery, setSearchQuery] = useState('');
  const [filterStatus, setFilterStatus] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [maintenanceTasks, setMaintenanceTasks] = useState<MaintenanceTask[]>([]);

  // Status options for filtering
  const statusOptions = ['scheduled', 'completed', 'overdue'];

  useEffect(() => {
    // In a real implementation, this would fetch maintenance tasks from an API
    const fetchMaintenanceTasks = async () => {
      try {
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Get current date for status calculation
        const today = new Date();
        
        // Mock maintenance tasks data
        const mockMaintenanceTasks: MaintenanceTask[] = [
          {
            id: 'mt1',
            equipmentId: 'eq1',
            equipmentName: 'John Deere 8R Tractor',
            taskType: 'Regular Service',
            description: 'Oil change, filter replacement, and general inspection',
            scheduledDate: '2023-08-20',
            status: 'scheduled',
            assignedTo: 'Mike Johnson',
            estimatedHours: 2,
            notes: 'Check hydraulic system carefully',
          },
          {
            id: 'mt2',
            equipmentId: 'eq2',
            equipmentName: 'Case IH Harvester',
            taskType: 'Repair',
            description: 'Fix hydraulic leak and replace worn belts',
            scheduledDate: '2023-06-05',
            completedDate: '2023-06-05',
            status: 'completed',
            assignedTo: 'Sarah Williams',
            estimatedHours: 4,
            actualHours: 3.5,
            notes: 'Replaced main hydraulic line and all drive belts',
            partsUsed: ['Hydraulic line', 'Drive belt set', 'Hydraulic fluid'],
          },
          {
            id: 'mt3',
            equipmentId: 'eq3',
            equipmentName: 'Valley Center Pivot',
            taskType: 'Inspection',
            description: 'Pre-season inspection and lubrication',
            scheduledDate: '2023-07-15',
            status: 'overdue',
            assignedTo: 'Mike Johnson',
            estimatedHours: 3,
            notes: 'Check all sprinkler heads and pressure regulators',
          },
          {
            id: 'mt4',
            equipmentId: 'eq4',
            equipmentName: 'Kubota Utility Vehicle',
            taskType: 'Repair',
            description: 'Transmission repair',
            scheduledDate: '2023-09-10',
            status: 'scheduled',
            assignedTo: 'Robert Chen',
            estimatedHours: 6,
            notes: 'Waiting for parts to arrive',
          },
          {
            id: 'mt5',
            equipmentId: 'eq5',
            equipmentName: 'Power Drill Set',
            taskType: 'Maintenance',
            description: 'Clean and lubricate all moving parts',
            scheduledDate: '2023-06-01',
            completedDate: '2023-06-02',
            status: 'completed',
            assignedTo: 'Sarah Williams',
            estimatedHours: 1,
            actualHours: 0.5,
            notes: 'Replaced worn brushes',
          },
          {
            id: 'mt6',
            equipmentId: 'eq6',
            equipmentName: 'Sprayer System',
            taskType: 'Regular Service',
            description: 'Clean nozzles and filters, calibrate spray rate',
            scheduledDate: '2023-07-01',
            status: 'overdue',
            assignedTo: 'Robert Chen',
            estimatedHours: 2,
            notes: 'Check for any clogged nozzles',
          },
        ];
        
        // Update status based on dates
        const updatedTasks = mockMaintenanceTasks.map(task => {
          if (task.status === 'scheduled') {
            const scheduledDate = new Date(task.scheduledDate);
            if (scheduledDate < today && !task.completedDate) {
              return { ...task, status: 'overdue' };
            }
          }
          return task;
        });
        
        setMaintenanceTasks(updatedTasks);
      } catch (error) {
        console.error('Error fetching maintenance tasks:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchMaintenanceTasks();
  }, []);

  // Filter maintenance tasks based on search query and status filter
  const filteredTasks = maintenanceTasks.filter(task => {
    const matchesSearch = 
      task.equipmentName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      task.taskType.toLowerCase().includes(searchQuery.toLowerCase()) ||
      task.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      task.assignedTo.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesFilter = filterStatus ? task.status === filterStatus : true;
    
    return matchesSearch && matchesFilter;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'scheduled':
        return '#3b82f6'; // blue
      case 'completed':
        return '#10b981'; // green
      case 'overdue':
        return '#ef4444'; // red
      default:
        return '#6b7280'; // gray
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'scheduled':
        return 'Scheduled';
      case 'completed':
        return 'Completed';
      case 'overdue':
        return 'Overdue';
      default:
        return 'Unknown';
    }
  };

  const renderMaintenanceTask = ({ item }: { item: MaintenanceTask }) => {
    const statusColor = getStatusColor(item.status);
    const statusLabel = getStatusLabel(item.status);
    
    return (
      <TouchableOpacity 
        style={styles.taskItem}
        onPress={() => navigation.navigate('MaintenanceDetail', { maintenanceId: item.id })}
      >
        <View style={styles.itemHeader}>
          <Text style={styles.itemName}>{item.taskType}</Text>
          <View style={[styles.statusBadge, { backgroundColor: statusColor }]}>
            <Text style={styles.statusText}>{statusLabel}</Text>
          </View>
        </View>
        
        <View style={styles.itemDetails}>
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Equipment:</Text>
            <Text style={styles.detailText}>{item.equipmentName}</Text>
          </View>
          
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Description:</Text>
            <Text style={styles.detailText}>{item.description}</Text>
          </View>
          
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Date:</Text>
            <Text style={styles.detailText}>
              {item.status === 'completed' && item.completedDate 
                ? new Date(item.completedDate).toLocaleDateString() 
                : new Date(item.scheduledDate).toLocaleDateString()}
            </Text>
          </View>
          
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Assigned To:</Text>
            <Text style={styles.detailText}>{item.assignedTo}</Text>
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#3b82f6" />
        <Text style={styles.loadingText}>Loading maintenance tasks...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.searchContainer}>
        <Ionicons name="search" size={20} color="#757575" style={styles.searchIcon} />
        <TextInput
          style={styles.searchInput}
          placeholder="Search maintenance tasks..."
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
      </View>

      <View style={styles.filterContainer}>
        <TouchableOpacity
          style={[styles.filterButton, filterStatus === null && styles.filterButtonActive]}
          onPress={() => setFilterStatus(null)}
        >
          <Text style={[styles.filterButtonText, filterStatus === null && styles.filterButtonTextActive]}>All</Text>
        </TouchableOpacity>
        
        {statusOptions.map((status) => (
          <TouchableOpacity
            key={status}
            style={[
              styles.filterButton, 
              filterStatus === status && styles.filterButtonActive,
              { backgroundColor: filterStatus === status ? getStatusColor(status) : '#E0E0E0' }
            ]}
            onPress={() => setFilterStatus(status)}
          >
            <Text style={[
              styles.filterButtonText, 
              filterStatus === status && styles.filterButtonTextActive
            ]}>
              {getStatusLabel(status)}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      <FlatList
        data={filteredTasks}
        renderItem={renderMaintenanceTask}
        keyExtractor={item => item.id}
        contentContainerStyle={styles.taskList}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Ionicons name="build" size={50} color="#BDBDBD" />
            <Text style={styles.emptyText}>No maintenance tasks found</Text>
          </View>
        }
      />

      <TouchableOpacity 
        style={styles.fab}
        onPress={() => console.log('Schedule new maintenance')}
      >
        <Ionicons name="add" size={24} color="#fff" />
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#757575',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderRadius: 8,
    margin: 15,
    paddingHorizontal: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  searchIcon: {
    marginRight: 10,
  },
  searchInput: {
    flex: 1,
    height: 50,
    fontSize: 16,
  },
  filterContainer: {
    flexDirection: 'row',
    paddingHorizontal: 15,
    marginBottom: 10,
    flexWrap: 'wrap',
  },
  filterButton: {
    paddingVertical: 8,
    paddingHorizontal: 12,
    marginRight: 8,
    marginBottom: 8,
    borderRadius: 20,
    backgroundColor: '#E0E0E0',
  },
  filterButtonActive: {
    backgroundColor: '#3b82f6',
  },
  filterButtonText: {
    color: '#757575',
    fontSize: 14,
  },
  filterButtonTextActive: {
    color: '#fff',
  },
  taskList: {
    padding: 15,
    paddingBottom: 80, // Add padding for FAB
  },
  taskItem: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 15,
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  itemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  itemName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#212121',
    flex: 1,
  },
  statusBadge: {
    paddingVertical: 4,
    paddingHorizontal: 8,
    borderRadius: 4,
  },
  statusText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '500',
  },
  itemDetails: {
    borderTopWidth: 1,
    borderTopColor: '#E0E0E0',
    paddingTop: 10,
  },
  detailRow: {
    flexDirection: 'row',
    marginBottom: 5,
  },
  detailLabel: {
    fontSize: 14,
    color: '#757575',
    width: 100,
  },
  detailText: {
    fontSize: 14,
    color: '#212121',
    flex: 1,
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 50,
  },
  emptyText: {
    fontSize: 16,
    color: '#757575',
    marginTop: 10,
  },
  fab: {
    position: 'absolute',
    bottom: 20,
    right: 20,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: '#3b82f6',
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
});

export default MaintenanceScreen;