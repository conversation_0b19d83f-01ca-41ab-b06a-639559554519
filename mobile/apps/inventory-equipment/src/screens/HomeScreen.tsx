import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, ActivityIndicator } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { HomeScreenNavigationProp } from '../types/navigation';

const HomeScreen = () => {
  const navigation = useNavigation<HomeScreenNavigationProp>();
  const [loading, setLoading] = useState(true);
  const [inventorySummary, setInventorySummary] = useState({
    totalItems: 0,
    lowStockItems: 0,
    categories: [] as { name: string; count: number }[],
  });
  const [equipmentSummary, setEquipmentSummary] = useState({
    totalEquipment: 0,
    needsMaintenance: 0,
    categories: [] as { name: string; count: number }[],
  });
  const [recentActivities, setRecentActivities] = useState<{ id: string; type: string; description: string; date: string }[]>([]);

  useEffect(() => {
    // In a real implementation, this would fetch data from an API
    const fetchDashboardData = async () => {
      try {
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Mock inventory summary data
        setInventorySummary({
          totalItems: 120,
          lowStockItems: 8,
          categories: [
            { name: 'Seeds', count: 25 },
            { name: 'Fertilizer', count: 15 },
            { name: 'Pesticide', count: 10 },
            { name: 'Feed', count: 30 },
            { name: 'Supplies', count: 40 },
          ],
        });
        
        // Mock equipment summary data
        setEquipmentSummary({
          totalEquipment: 45,
          needsMaintenance: 5,
          categories: [
            { name: 'Tractors', count: 8 },
            { name: 'Harvesters', count: 4 },
            { name: 'Irrigation', count: 12 },
            { name: 'Tools', count: 21 },
          ],
        });
        
        // Mock recent activities
        setRecentActivities([
          { 
            id: 'act1', 
            type: 'inventory', 
            description: 'Corn Seeds stock updated to 600kg', 
            date: '2023-07-01' 
          },
          { 
            id: 'act2', 
            type: 'equipment', 
            description: 'Tractor #3 maintenance completed', 
            date: '2023-06-30' 
          },
          { 
            id: 'act3', 
            type: 'inventory', 
            description: 'Ordered 200kg of NPK Fertilizer', 
            date: '2023-06-29' 
          },
          { 
            id: 'act4', 
            type: 'equipment', 
            description: 'New irrigation pump added to inventory', 
            date: '2023-06-28' 
          },
          { 
            id: 'act5', 
            type: 'maintenance', 
            description: 'Scheduled maintenance for Harvester #2', 
            date: '2023-06-27' 
          },
        ]);
      } catch (error) {
        console.error('Error fetching dashboard data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  const renderQuickAction = (icon: string, label: string, onPress: () => void, color: string = '#3b82f6') => (
    <TouchableOpacity style={styles.quickAction} onPress={onPress}>
      <View style={[styles.quickActionIcon, { backgroundColor: color }]}>
        <Ionicons name={icon as any} size={24} color="#fff" />
      </View>
      <Text style={styles.quickActionLabel}>{label}</Text>
    </TouchableOpacity>
  );

  const renderActivityItem = (activity: { id: string; type: string; description: string; date: string }) => {
    let icon = 'cube';
    let color = '#3b82f6';
    
    if (activity.type === 'equipment') {
      icon = 'construct';
      color = '#f59e0b';
    } else if (activity.type === 'maintenance') {
      icon = 'build';
      color = '#10b981';
    }
    
    return (
      <View key={activity.id} style={styles.activityItem}>
        <View style={[styles.activityIcon, { backgroundColor: color }]}>
          <Ionicons name={icon as any} size={16} color="#fff" />
        </View>
        <View style={styles.activityContent}>
          <Text style={styles.activityDescription}>{activity.description}</Text>
          <Text style={styles.activityDate}>{new Date(activity.date).toLocaleDateString()}</Text>
        </View>
      </View>
    );
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#3b82f6" />
        <Text style={styles.loadingText}>Loading dashboard...</Text>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Inventory & Equipment</Text>
      </View>
      
      <View style={styles.quickActionsContainer}>
        {renderQuickAction('cube', 'Inventory', () => navigation.navigate('Inventory'))}
        {renderQuickAction('construct', 'Equipment', () => navigation.navigate('Equipment'), '#f59e0b')}
        {renderQuickAction('build', 'Maintenance', () => navigation.navigate('Maintenance'), '#10b981')}
        {renderQuickAction('barcode', 'Scan', () => navigation.navigate('BarcodeScanner'), '#8b5cf6')}
      </View>
      
      <View style={styles.summaryContainer}>
        <Text style={styles.sectionTitle}>Inventory Summary</Text>
        <View style={styles.summaryCard}>
          <View style={styles.summaryRow}>
            <View style={styles.summaryItem}>
              <Text style={styles.summaryValue}>{inventorySummary.totalItems}</Text>
              <Text style={styles.summaryLabel}>Total Items</Text>
            </View>
            <View style={styles.summaryItem}>
              <Text style={[styles.summaryValue, { color: '#ef4444' }]}>{inventorySummary.lowStockItems}</Text>
              <Text style={styles.summaryLabel}>Low Stock</Text>
            </View>
          </View>
          <View style={styles.divider} />
          <Text style={styles.categoriesTitle}>Categories</Text>
          <View style={styles.categoriesContainer}>
            {inventorySummary.categories.map((category, index) => (
              <View key={index} style={styles.categoryItem}>
                <Text style={styles.categoryName}>{category.name}</Text>
                <Text style={styles.categoryCount}>{category.count}</Text>
              </View>
            ))}
          </View>
        </View>
      </View>
      
      <View style={styles.summaryContainer}>
        <Text style={styles.sectionTitle}>Equipment Summary</Text>
        <View style={styles.summaryCard}>
          <View style={styles.summaryRow}>
            <View style={styles.summaryItem}>
              <Text style={styles.summaryValue}>{equipmentSummary.totalEquipment}</Text>
              <Text style={styles.summaryLabel}>Total Equipment</Text>
            </View>
            <View style={styles.summaryItem}>
              <Text style={[styles.summaryValue, { color: '#f59e0b' }]}>{equipmentSummary.needsMaintenance}</Text>
              <Text style={styles.summaryLabel}>Needs Maintenance</Text>
            </View>
          </View>
          <View style={styles.divider} />
          <Text style={styles.categoriesTitle}>Categories</Text>
          <View style={styles.categoriesContainer}>
            {equipmentSummary.categories.map((category, index) => (
              <View key={index} style={styles.categoryItem}>
                <Text style={styles.categoryName}>{category.name}</Text>
                <Text style={styles.categoryCount}>{category.count}</Text>
              </View>
            ))}
          </View>
        </View>
      </View>
      
      <View style={styles.activitiesContainer}>
        <Text style={styles.sectionTitle}>Recent Activities</Text>
        <View style={styles.activitiesCard}>
          {recentActivities.map(activity => renderActivityItem(activity))}
        </View>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#757575',
  },
  header: {
    padding: 15,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#212121',
  },
  quickActionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 15,
  },
  quickAction: {
    alignItems: 'center',
    width: '23%',
  },
  quickActionIcon: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#3b82f6',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 5,
  },
  quickActionLabel: {
    fontSize: 12,
    color: '#757575',
    textAlign: 'center',
  },
  summaryContainer: {
    padding: 15,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#212121',
    marginBottom: 10,
  },
  summaryCard: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  summaryItem: {
    alignItems: 'center',
  },
  summaryValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#3b82f6',
  },
  summaryLabel: {
    fontSize: 14,
    color: '#757575',
    marginTop: 5,
  },
  divider: {
    height: 1,
    backgroundColor: '#e0e0e0',
    marginVertical: 15,
  },
  categoriesTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#212121',
    marginBottom: 10,
  },
  categoriesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  categoryItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '48%',
    marginBottom: 8,
    backgroundColor: '#f5f5f5',
    padding: 8,
    borderRadius: 4,
  },
  categoryName: {
    fontSize: 14,
    color: '#212121',
  },
  categoryCount: {
    fontSize: 14,
    fontWeight: '500',
    color: '#3b82f6',
  },
  activitiesContainer: {
    padding: 15,
    paddingBottom: 30,
  },
  activitiesCard: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  activityItem: {
    flexDirection: 'row',
    marginBottom: 15,
    alignItems: 'flex-start',
  },
  activityIcon: {
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: '#3b82f6',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 10,
  },
  activityContent: {
    flex: 1,
  },
  activityDescription: {
    fontSize: 14,
    color: '#212121',
    marginBottom: 3,
  },
  activityDate: {
    fontSize: 12,
    color: '#757575',
  },
});

export default HomeScreen;