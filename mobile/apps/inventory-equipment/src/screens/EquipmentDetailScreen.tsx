import React, { useState, useEffect } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  ScrollView, 
  TouchableOpacity, 
  TextInput, 
  ActivityIndicator,
  Alert
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { RouteProp, useNavigation, useRoute } from '@react-navigation/native';
import { EquipmentDetailScreenRouteProp, EquipmentItem } from '../types/navigation';

const EquipmentDetailScreen = () => {
  const navigation = useNavigation();
  const route = useRoute<EquipmentDetailScreenRouteProp>();
  const { equipmentId } = route.params;
  
  const [loading, setLoading] = useState(true);
  const [editing, setEditing] = useState(false);
  const [equipmentItem, setEquipmentItem] = useState<EquipmentItem | null>(null);
  const [editedItem, setEditedItem] = useState<EquipmentItem | null>(null);

  useEffect(() => {
    // In a real implementation, this would fetch the equipment item from an API
    const fetchEquipmentItem = async () => {
      try {
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Mock equipment item data
        const mockEquipmentItem: EquipmentItem = {
          id: equipmentId,
          name: 'John Deere 8R Tractor',
          category: 'Tractors',
          status: 'operational',
          location: 'Field A',
          serialNumber: 'JD8R-12345',
          purchaseDate: '2022-03-15',
          lastMaintenance: '2023-05-20',
          nextMaintenance: '2023-08-20',
          hoursUsed: 1250,
          notes: 'Regular maintenance required every 500 hours',
          purchasePrice: 250000,
          manufacturer: 'John Deere',
          model: '8R 340',
          year: 2022,
          warranty: '3 years or 2000 hours',
          warrantyExpiration: '2025-03-15',
          documents: ['manual.pdf', 'warranty.pdf', 'service_records.pdf'],
        };
        
        setEquipmentItem(mockEquipmentItem);
        setEditedItem(mockEquipmentItem);
      } catch (error) {
        console.error('Error fetching equipment item:', error);
        Alert.alert('Error', 'Failed to load equipment item details');
      } finally {
        setLoading(false);
      }
    };

    fetchEquipmentItem();
  }, [equipmentId]);

  const handleSave = async () => {
    if (!editedItem) return;
    
    setLoading(true);
    try {
      // Simulate API call to update the item
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Update the item in state
      setEquipmentItem(editedItem);
      setEditing(false);
      Alert.alert('Success', 'Equipment item updated successfully');
    } catch (error) {
      console.error('Error updating equipment item:', error);
      Alert.alert('Error', 'Failed to update equipment item');
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    setEditedItem(equipmentItem);
    setEditing(false);
  };

  const handleDelete = async () => {
    Alert.alert(
      'Confirm Delete',
      'Are you sure you want to delete this equipment item?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            setLoading(true);
            try {
              // Simulate API call to delete the item
              await new Promise(resolve => setTimeout(resolve, 1000));
              
              Alert.alert('Success', 'Equipment item deleted successfully');
              navigation.goBack();
            } catch (error) {
              console.error('Error deleting equipment item:', error);
              Alert.alert('Error', 'Failed to delete equipment item');
              setLoading(false);
            }
          },
        },
      ]
    );
  };

  const handleUpdateHours = () => {
    if (!equipmentItem) return;
    
    Alert.prompt(
      'Update Hours Used',
      `Current hours: ${equipmentItem.hoursUsed}`,
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Update',
          onPress: async (value) => {
            if (!value || isNaN(Number(value))) {
              Alert.alert('Error', 'Please enter a valid number');
              return;
            }
            
            setLoading(true);
            try {
              // Simulate API call to update the hours
              await new Promise(resolve => setTimeout(resolve, 1000));
              
              const updatedItem = {
                ...equipmentItem,
                hoursUsed: Number(value),
              };
              
              setEquipmentItem(updatedItem);
              setEditedItem(updatedItem);
              Alert.alert('Success', 'Hours updated successfully');
            } catch (error) {
              console.error('Error updating hours:', error);
              Alert.alert('Error', 'Failed to update hours');
            } finally {
              setLoading(false);
            }
          },
        },
      ],
      'plain-text',
      equipmentItem.hoursUsed.toString()
    );
  };

  const handleScheduleMaintenance = () => {
    if (!equipmentItem) return;
    
    Alert.prompt(
      'Schedule Maintenance',
      'Enter maintenance date (YYYY-MM-DD):',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Schedule',
          onPress: async (value) => {
            // Simple date validation
            const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
            if (!value || !dateRegex.test(value)) {
              Alert.alert('Error', 'Please enter a valid date in YYYY-MM-DD format');
              return;
            }
            
            setLoading(true);
            try {
              // Simulate API call to schedule maintenance
              await new Promise(resolve => setTimeout(resolve, 1000));
              
              const updatedItem = {
                ...equipmentItem,
                nextMaintenance: value,
                status: 'operational',
              };
              
              setEquipmentItem(updatedItem);
              setEditedItem(updatedItem);
              Alert.alert('Success', 'Maintenance scheduled successfully');
            } catch (error) {
              console.error('Error scheduling maintenance:', error);
              Alert.alert('Error', 'Failed to schedule maintenance');
            } finally {
              setLoading(false);
            }
          },
        },
      ],
      'plain-text',
      new Date().toISOString().split('T')[0]
    );
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'operational':
        return '#10b981'; // green
      case 'maintenance':
        return '#f59e0b'; // amber
      case 'repair':
        return '#ef4444'; // red
      default:
        return '#6b7280'; // gray
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'operational':
        return 'Operational';
      case 'maintenance':
        return 'Maintenance';
      case 'repair':
        return 'Needs Repair';
      default:
        return 'Unknown';
    }
  };

  const renderDetailItem = (label: string, value: string | number | undefined) => (
    <View style={styles.detailItem}>
      <Text style={styles.detailLabel}>{label}</Text>
      <Text style={styles.detailValue}>{value || 'N/A'}</Text>
    </View>
  );

  const renderEditableDetailItem = (
    label: string, 
    value: string | number | undefined, 
    field: keyof EquipmentItem,
    keyboardType: 'default' | 'numeric' = 'default'
  ) => (
    <View style={styles.editableDetailItem}>
      <Text style={styles.detailLabel}>{label}</Text>
      <TextInput
        style={styles.editInput}
        value={value?.toString() || ''}
        onChangeText={(text) => {
          if (!editedItem) return;
          
          if (keyboardType === 'numeric') {
            setEditedItem({
              ...editedItem,
              [field]: text === '' ? '' : Number(text),
            });
          } else {
            setEditedItem({
              ...editedItem,
              [field]: text,
            });
          }
        }}
        keyboardType={keyboardType}
      />
    </View>
  );

  const renderStatusSelector = () => {
    if (!editedItem) return null;
    
    const statuses = ['operational', 'maintenance', 'repair'];
    
    return (
      <View style={styles.editableDetailItem}>
        <Text style={styles.detailLabel}>Status</Text>
        <View style={styles.statusSelector}>
          {statuses.map(status => (
            <TouchableOpacity
              key={status}
              style={[
                styles.statusOption,
                { backgroundColor: getStatusColor(status) },
                editedItem.status === status && styles.statusOptionSelected
              ]}
              onPress={() => {
                if (!editedItem) return;
                setEditedItem({
                  ...editedItem,
                  status,
                });
              }}
            >
              <Text style={styles.statusOptionText}>{getStatusLabel(status)}</Text>
              {editedItem.status === status && (
                <Ionicons name="checkmark" size={16} color="#fff" style={styles.statusCheckmark} />
              )}
            </TouchableOpacity>
          ))}
        </View>
      </View>
    );
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#3b82f6" />
        <Text style={styles.loadingText}>Loading equipment item...</Text>
      </View>
    );
  }

  if (!equipmentItem || !editedItem) {
    return (
      <View style={styles.errorContainer}>
        <Ionicons name="alert-circle" size={50} color="#ef4444" />
        <Text style={styles.errorText}>Failed to load equipment item</Text>
        <TouchableOpacity style={styles.button} onPress={() => navigation.goBack()}>
          <Text style={styles.buttonText}>Go Back</Text>
        </TouchableOpacity>
      </View>
    );
  }

  // Calculate if maintenance is due soon (within 30 days)
  const today = new Date();
  const nextMaintenance = new Date(equipmentItem.nextMaintenance);
  const daysDifference = Math.ceil((nextMaintenance.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
  const maintenanceSoon = daysDifference > 0 && daysDifference <= 30;
  const maintenanceOverdue = daysDifference < 0;

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color="#212121" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>
          {editing ? 'Edit Equipment Item' : equipmentItem.name}
        </Text>
        {!editing && (
          <TouchableOpacity onPress={() => setEditing(true)} style={styles.editButton}>
            <Ionicons name="create" size={24} color="#3b82f6" />
          </TouchableOpacity>
        )}
      </View>

      <ScrollView style={styles.content}>
        {!editing ? (
          // View mode
          <>
            <View style={styles.statusContainer}>
              <View style={[styles.statusBadge, { backgroundColor: getStatusColor(equipmentItem.status) }]}>
                <Text style={styles.statusText}>{getStatusLabel(equipmentItem.status)}</Text>
              </View>
              
              {maintenanceOverdue && (
                <View style={[styles.maintenanceBadge, { backgroundColor: '#ef4444' }]}>
                  <Text style={styles.maintenanceText}>Maintenance Overdue</Text>
                </View>
              )}
              
              {maintenanceSoon && !maintenanceOverdue && (
                <View style={[styles.maintenanceBadge, { backgroundColor: '#f59e0b' }]}>
                  <Text style={styles.maintenanceText}>Maintenance Due Soon</Text>
                </View>
              )}
            </View>

            <View style={styles.card}>
              <View style={styles.cardHeader}>
                <Text style={styles.cardTitle}>Basic Information</Text>
              </View>
              <View style={styles.cardContent}>
                {renderDetailItem('Name', equipmentItem.name)}
                {renderDetailItem('Category', equipmentItem.category)}
                {renderDetailItem('Manufacturer', equipmentItem.manufacturer)}
                {renderDetailItem('Model', equipmentItem.model)}
                {renderDetailItem('Year', equipmentItem.year)}
                {renderDetailItem('Serial Number', equipmentItem.serialNumber)}
                {renderDetailItem('Location', equipmentItem.location)}
              </View>
            </View>

            <View style={styles.card}>
              <View style={styles.cardHeader}>
                <Text style={styles.cardTitle}>Maintenance Information</Text>
              </View>
              <View style={styles.cardContent}>
                {renderDetailItem('Hours Used', equipmentItem.hoursUsed)}
                {renderDetailItem('Last Maintenance', new Date(equipmentItem.lastMaintenance).toLocaleDateString())}
                {renderDetailItem('Next Maintenance', new Date(equipmentItem.nextMaintenance).toLocaleDateString())}
              </View>
            </View>

            <View style={styles.card}>
              <View style={styles.cardHeader}>
                <Text style={styles.cardTitle}>Purchase Information</Text>
              </View>
              <View style={styles.cardContent}>
                {renderDetailItem('Purchase Date', new Date(equipmentItem.purchaseDate).toLocaleDateString())}
                {renderDetailItem('Purchase Price', equipmentItem.purchasePrice ? `$${equipmentItem.purchasePrice.toLocaleString()}` : 'Not recorded')}
                {renderDetailItem('Warranty', equipmentItem.warranty)}
                {renderDetailItem('Warranty Expiration', equipmentItem.warrantyExpiration ? new Date(equipmentItem.warrantyExpiration).toLocaleDateString() : 'Not applicable')}
              </View>
            </View>

            <View style={styles.card}>
              <View style={styles.cardHeader}>
                <Text style={styles.cardTitle}>Additional Information</Text>
              </View>
              <View style={styles.cardContent}>
                {renderDetailItem('Notes', equipmentItem.notes)}
              </View>
            </View>

            {equipmentItem.documents && equipmentItem.documents.length > 0 && (
              <View style={styles.card}>
                <View style={styles.cardHeader}>
                  <Text style={styles.cardTitle}>Documents</Text>
                </View>
                <View style={styles.cardContent}>
                  {equipmentItem.documents.map((doc, index) => (
                    <TouchableOpacity key={index} style={styles.documentItem}>
                      <Ionicons name="document-text" size={20} color="#3b82f6" />
                      <Text style={styles.documentName}>{doc}</Text>
                      <Ionicons name="download" size={20} color="#757575" />
                    </TouchableOpacity>
                  ))}
                </View>
              </View>
            )}

            <View style={styles.actionsContainer}>
              <TouchableOpacity style={styles.actionButton} onPress={handleUpdateHours}>
                <Ionicons name="time" size={20} color="#fff" />
                <Text style={styles.actionButtonText}>Update Hours</Text>
              </TouchableOpacity>
              <TouchableOpacity 
                style={[styles.actionButton, { backgroundColor: '#10b981' }]} 
                onPress={handleScheduleMaintenance}
              >
                <Ionicons name="calendar" size={20} color="#fff" />
                <Text style={styles.actionButtonText}>Schedule Maintenance</Text>
              </TouchableOpacity>
            </View>

            <TouchableOpacity style={[styles.actionButton, styles.deleteButton, { marginBottom: 30 }]} onPress={handleDelete}>
              <Ionicons name="trash" size={20} color="#fff" />
              <Text style={styles.actionButtonText}>Delete Equipment</Text>
            </TouchableOpacity>
          </>
        ) : (
          // Edit mode
          <>
            <View style={styles.card}>
              <View style={styles.cardHeader}>
                <Text style={styles.cardTitle}>Basic Information</Text>
              </View>
              <View style={styles.cardContent}>
                {renderEditableDetailItem('Name', editedItem.name, 'name')}
                {renderEditableDetailItem('Category', editedItem.category, 'category')}
                {renderEditableDetailItem('Manufacturer', editedItem.manufacturer, 'manufacturer')}
                {renderEditableDetailItem('Model', editedItem.model, 'model')}
                {renderEditableDetailItem('Year', editedItem.year, 'year', 'numeric')}
                {renderEditableDetailItem('Serial Number', editedItem.serialNumber, 'serialNumber')}
                {renderEditableDetailItem('Location', editedItem.location, 'location')}
                {renderStatusSelector()}
              </View>
            </View>

            <View style={styles.card}>
              <View style={styles.cardHeader}>
                <Text style={styles.cardTitle}>Maintenance Information</Text>
              </View>
              <View style={styles.cardContent}>
                {renderEditableDetailItem('Hours Used', editedItem.hoursUsed, 'hoursUsed', 'numeric')}
                {renderEditableDetailItem('Last Maintenance', editedItem.lastMaintenance, 'lastMaintenance')}
                {renderEditableDetailItem('Next Maintenance', editedItem.nextMaintenance, 'nextMaintenance')}
              </View>
            </View>

            <View style={styles.card}>
              <View style={styles.cardHeader}>
                <Text style={styles.cardTitle}>Purchase Information</Text>
              </View>
              <View style={styles.cardContent}>
                {renderEditableDetailItem('Purchase Date', editedItem.purchaseDate, 'purchaseDate')}
                {renderEditableDetailItem('Purchase Price', editedItem.purchasePrice, 'purchasePrice', 'numeric')}
                {renderEditableDetailItem('Warranty', editedItem.warranty, 'warranty')}
                {renderEditableDetailItem('Warranty Expiration', editedItem.warrantyExpiration, 'warrantyExpiration')}
              </View>
            </View>

            <View style={styles.card}>
              <View style={styles.cardHeader}>
                <Text style={styles.cardTitle}>Additional Information</Text>
              </View>
              <View style={styles.cardContent}>
                {renderEditableDetailItem('Notes', editedItem.notes, 'notes')}
              </View>
            </View>

            <View style={styles.editActionsContainer}>
              <TouchableOpacity style={styles.cancelButton} onPress={handleCancel}>
                <Text style={styles.cancelButtonText}>Cancel</Text>
              </TouchableOpacity>
              <TouchableOpacity style={styles.saveButton} onPress={handleSave}>
                <Text style={styles.saveButtonText}>Save Changes</Text>
              </TouchableOpacity>
            </View>
          </>
        )}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#757575',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    padding: 20,
  },
  errorText: {
    fontSize: 18,
    color: '#212121',
    marginTop: 10,
    marginBottom: 20,
    textAlign: 'center',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 15,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  backButton: {
    marginRight: 10,
  },
  headerTitle: {
    flex: 1,
    fontSize: 18,
    fontWeight: 'bold',
    color: '#212121',
  },
  editButton: {
    padding: 5,
  },
  content: {
    flex: 1,
    padding: 15,
  },
  statusContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 15,
  },
  statusBadge: {
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 4,
    marginRight: 10,
    marginBottom: 10,
  },
  statusText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '500',
  },
  maintenanceBadge: {
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 4,
    marginBottom: 10,
  },
  maintenanceText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '500',
  },
  card: {
    backgroundColor: '#fff',
    borderRadius: 8,
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  cardHeader: {
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  cardTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#212121',
  },
  cardContent: {
    padding: 15,
  },
  detailItem: {
    marginBottom: 12,
  },
  detailLabel: {
    fontSize: 14,
    color: '#757575',
    marginBottom: 4,
  },
  detailValue: {
    fontSize: 16,
    color: '#212121',
  },
  editableDetailItem: {
    marginBottom: 15,
  },
  editInput: {
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 4,
    padding: 10,
    fontSize: 16,
    color: '#212121',
    backgroundColor: '#f9f9f9',
  },
  statusSelector: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 5,
  },
  statusOption: {
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 4,
    marginRight: 10,
    marginBottom: 5,
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusOptionSelected: {
    borderWidth: 2,
    borderColor: '#fff',
  },
  statusOptionText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '500',
  },
  statusCheckmark: {
    marginLeft: 5,
  },
  documentItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  documentName: {
    flex: 1,
    fontSize: 14,
    color: '#212121',
    marginLeft: 10,
  },
  actionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 15,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#3b82f6',
    borderRadius: 4,
    padding: 12,
    flex: 1,
    marginRight: 10,
  },
  deleteButton: {
    backgroundColor: '#ef4444',
    marginRight: 0,
  },
  actionButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 5,
  },
  editActionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 30,
  },
  cancelButton: {
    flex: 1,
    padding: 12,
    borderRadius: 4,
    backgroundColor: '#e0e0e0',
    alignItems: 'center',
    marginRight: 10,
  },
  cancelButtonText: {
    color: '#212121',
    fontSize: 14,
    fontWeight: '500',
  },
  saveButton: {
    flex: 1,
    padding: 12,
    borderRadius: 4,
    backgroundColor: '#3b82f6',
    alignItems: 'center',
  },
  saveButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '500',
  },
  button: {
    backgroundColor: '#3b82f6',
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 4,
    marginTop: 10,
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '500',
  },
});

export default EquipmentDetailScreen;