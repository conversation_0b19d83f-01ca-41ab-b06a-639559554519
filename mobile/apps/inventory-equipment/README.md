# NxtAcre Inventory & Equipment App

## Overview

The NxtAcre Inventory & Equipment App is designed for inventory managers, equipment managers, and maintenance staff to efficiently track and manage inventory and equipment on the farm. This app provides specialized tools for inventory tracking, equipment maintenance scheduling, and parts management.

## Features

### Inventory Management
- Inventory tracking and monitoring
- Supply ordering and receiving
- Low stock alerts
- Barcode/QR code scanning
- Inventory transaction recording
- Supplier directory access

### Equipment Management
- Equipment maintenance scheduling
- Equipment inspection and reporting
- Maintenance record keeping
- Equipment service history
- Parts inventory management
- Parts ordering

## Getting Started

### Prerequisites
- Node.js (v16 or higher)
- npm (v7 or higher)
- Expo CLI

### Installation
1. Clone the repository
2. Navigate to the app directory:
   ```
   cd mobile/apps/inventory-equipment
   ```
3. Install dependencies:
   ```
   npm install
   ```
4. Start the development server:
   ```
   npm start
   ```

## Development Status

This app is currently in the initial development phase. See the DEVELOPMENT_SUMMARY.md file for detailed information about the current development status and planned features.

## Project Structure

```
inventory-equipment/
├── App.tsx              # Main application component
├── app.json             # Expo configuration
├── src/
│   ├── components/      # Reusable UI components
│   ├── navigation/      # Navigation configuration
│   ├── screens/         # Screen components
│   │   ├── InventoryScreen.tsx       # Inventory management screen
│   │   ├── InventoryDetailScreen.tsx # Inventory item detail screen
│   │   ├── EquipmentScreen.tsx       # Equipment management screen
│   │   ├── EquipmentDetailScreen.tsx # Equipment detail screen
│   │   ├── MaintenanceScreen.tsx     # Maintenance management screen
│   │   ├── PartsScreen.tsx           # Parts inventory screen
│   │   └── SettingsScreen.tsx        # App settings screen
│   └── types/           # TypeScript type definitions
```

## Contributing

When contributing to this app, please follow the existing code style and patterns. Make sure to test your changes thoroughly before submitting a pull request.