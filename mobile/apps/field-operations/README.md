# NxtAcre Field Operations App

## Overview

The Field Operations App is part of the NxtAcre Farm Management Platform, designed specifically for farm operators, field workers, and equipment operators. This app focuses on field-level operations, providing tools for mapping, GPS tracking, task management, and weather monitoring.

## Features

- **Field Mapping**: Create and view field boundaries with interactive maps
- **GPS Tracking**: Track field operations with high-precision location services
- **Task Management**: View and complete assigned tasks
- **Weather Data**: Access field-level weather forecasts
- **Offline Support**: Work in remote areas with limited connectivity

## Getting Started

### Prerequisites

- Node.js (v16 or higher)
- npm (v7 or higher) or Yarn
- Expo CLI (`npm install -g expo-cli`)
- iOS Simulator or Android Emulator (optional for local development)
- Physical iOS or Android device with Expo Go app (for testing)

### Installation

1. Clone the repository
2. Navigate to the Field Operations App directory:
   ```
   cd mobile/apps/field-operations
   ```
3. Install dependencies:
   ```
   npm install
   ```
   or
   ```
   yarn install
   ```

### Running the App

#### Development Mode

Start the development server:
```
npm start
```
or
```
yarn start
```

This will open the Expo Developer Tools in your browser. From there, you can:
- Run on iOS Simulator (macOS only)
- Run on Android Emulator
- Scan the QR code with your device using the Expo Go app

#### Building for Production

To create a production build:
```
expo build:android
```
or
```
expo build:ios
```

## Project Structure

```
field-operations/
├── assets/             # Images, fonts, and other static assets
├── src/                # Source code
│   ├── components/     # Reusable React components
│   ├── navigation/     # Navigation configuration
│   │   ├── AppNavigator.tsx     # Main navigation container
│   │   ├── AuthNavigator.tsx    # Authentication flow
│   │   └── MainNavigator.tsx    # Main app flow
│   └── screens/        # Screen components
│       ├── HomeScreen.tsx       # Dashboard screen
│       ├── FieldsScreen.tsx     # Field list screen
│       ├── MapScreen.tsx        # Map view screen
│       └── ...                  # Other screens
├── App.tsx             # Main application component
├── app.json            # Expo configuration
└── package.json        # Project dependencies and scripts
```

## Shared Code

This app uses shared code from the `mobile/shared` directory, including:
- Authentication services
- Data services
- UI components
- Utility functions

## Development Guidelines

1. Follow the existing code style and patterns
2. Use TypeScript for type safety
3. Keep components modular and reusable
4. Use the shared code for common functionality
5. Ensure offline functionality works properly
6. Test on both iOS and Android devices

## Offline Support

The app is designed to work offline with data synchronization when connectivity is restored. Key features that work offline include:
- Viewing cached field data
- Recording GPS tracks
- Creating field boundaries
- Completing tasks

## Location Services

This app requires location permissions to function properly. It uses:
- Foreground location tracking for mapping and navigation
- Background location tracking for field operation recording (optional)

## Contributing

1. Create a feature branch from `develop`
2. Implement your changes
3. Test thoroughly on both iOS and Android
4. Submit a pull request

## License

This project is proprietary and confidential. Unauthorized copying, distribution, or use is strictly prohibited.