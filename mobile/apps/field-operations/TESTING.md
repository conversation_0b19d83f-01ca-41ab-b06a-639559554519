# NxtAcre Field Operations App - Testing Guide

This document outlines the testing procedures for the Field Operations App to ensure all implemented features work correctly.

## Prerequisites

Before testing, ensure you have:

1. Set up the development environment according to the README.md
2. Installed the Expo Go app on a physical device (recommended for location and camera testing)
3. Access to a test account with farm data

## General Testing

For all screens, verify:
- U<PERSON> renders correctly without layout issues
- Loading states display appropriately
- Error handling works as expected
- Navigation between screens functions properly
- Data persistence works when applicable

## Screen-Specific Testing

### HomeScreen

1. **Dashboard Loading**
   - Verify field count, active task count, and weather data load correctly
   - Test pull-to-refresh functionality

2. **Quick Actions**
   - Verify all quick action buttons navigate to the correct screens
   - Test navigation to Record GPS, View Map, and Offline Maps

3. **Recent Activity**
   - Verify recent activity loads and displays correctly
   - Check empty state when no activity is available

### FieldsScreen

1. **Field List**
   - Verify fields load and display correctly
   - Test search and filtering functionality
   - Verify field details (name, acres, crop type) display correctly

2. **Navigation**
   - Test navigation to field detail screen when a field is tapped

### FieldDetailScreen

1. **Field Information**
   - Verify field details (name, acres, status, crop type) display correctly
   - Test map loading with field boundaries

2. **Weather Data**
   - Verify weather data for the field location loads correctly

3. **Tasks**
   - Verify tasks associated with the field display correctly
   - Test navigation to task detail screen
   - Test task creation functionality

4. **Actions**
   - Test edit field and crop scouting buttons

### TasksScreen

1. **Task List**
   - Verify tasks load and display correctly
   - Test filtering by status (All, Active, Completed)
   - Verify task details (title, status, due date) display correctly

2. **Task Completion**
   - Test marking a task as complete
   - Verify task disappears from active filter after completion

3. **Navigation**
   - Test navigation to task detail screen when a task is tapped

### TaskDetailScreen

1. **Task Information**
   - Verify task details (title, description, status, priority) display correctly
   - Test field association if applicable

2. **Task Actions**
   - Test completing a task
   - Test cancelling a task
   - Verify status updates correctly after actions

### WeatherScreen

1. **Weather Data**
   - Verify current weather data loads correctly
   - Test forecast data display
   - Verify field selection changes the weather location

2. **UI Elements**
   - Test all weather details display correctly (temperature, humidity, wind, etc.)
   - Verify forecast cards display correctly

### ProfileScreen

1. **User Information**
   - Verify user profile information displays correctly

2. **Settings**
   - Test toggling each setting (notifications, location tracking, etc.)
   - Verify settings persist after app restart

3. **Actions**
   - Test logout functionality
   - Test clear cache functionality

### RecordingScreen

1. **Field Selection**
   - Verify fields load correctly for selection
   - Test selecting different fields

2. **Operation Type**
   - Test selecting different operation types

3. **Recording**
   - Test starting a recording
   - Verify GPS tracking works correctly
   - Test pausing and resuming recording
   - Test stopping and saving a recording
   - Test discarding a recording

4. **Map Display**
   - Verify current location displays on the map
   - Test tracking line displays correctly during recording

### CropScoutingScreen

1. **Field Information**
   - Verify field details display correctly

2. **Observation Types**
   - Test selecting different observation types
   - Test severity selection

3. **Notes and Photos**
   - Test adding notes
   - Test taking photos with camera
   - Test selecting photos from gallery
   - Test removing photos

4. **Saving**
   - Test saving observations
   - Verify saved observations persist

### OfflineFieldMappingScreen

1. **Field List**
   - Verify fields load correctly
   - Test field details display

2. **Download Management**
   - Test downloading offline maps
   - Verify download progress displays correctly
   - Test removing offline maps

3. **Storage Information**
   - Verify storage usage information displays correctly
   - Test storage bar updates after downloads/removals

4. **Connectivity**
   - Test behavior when offline
   - Verify connectivity status indicator works

## Integration Testing

1. **Data Flow**
   - Verify data flows correctly between screens
   - Test that changes in one screen reflect in others

2. **Offline Mode**
   - Test app behavior when switching to airplane mode
   - Verify offline indicators display correctly
   - Test synchronization when coming back online

3. **Authentication**
   - Test login/logout flow
   - Verify authentication persists between app restarts

## Performance Testing

1. **Load Times**
   - Measure and verify acceptable load times for all screens
   - Test performance with large datasets

2. **Memory Usage**
   - Monitor memory usage during extended use
   - Check for memory leaks

3. **Battery Impact**
   - Test battery usage during GPS recording
   - Verify background location tracking is optimized

## Reporting Issues

When reporting issues:

1. Clearly describe the steps to reproduce
2. Include device information (model, OS version)
3. Add screenshots or screen recordings when possible
4. Note network conditions if relevant (online/offline)

## Automated Testing (Future)

Future improvements to testing should include:

1. Unit tests for core functionality
2. Integration tests for screen flows
3. End-to-end tests for critical user journeys
4. Automated UI tests with tools like Detox