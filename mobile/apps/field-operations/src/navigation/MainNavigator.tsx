import React from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { Ionicons } from '@expo/vector-icons';
import HomeScreen from '../screens/HomeScreen';
import MapScreen from '../screens/MapScreen';
import FieldsScreen from '../screens/FieldsScreen';
import TasksScreen from '../screens/TasksScreen';
import ProfileScreen from '../screens/ProfileScreen';
import FieldDetailScreen from '../screens/FieldDetailScreen';
import TaskDetailScreen from '../screens/TaskDetailScreen';
import RecordingScreen from '../screens/RecordingScreen';
import CropScoutingScreen from '../screens/CropScoutingScreen';
import OfflineFieldMappingScreen from '../screens/OfflineFieldMappingScreen';
import WeatherScreen from '../screens/WeatherScreen';
import ABLineNavigationScreen from '../screens/ABLineNavigationScreen';

export type MainStackParamList = {
  MainTabs: undefined;
  FieldDetail: { fieldId: string };
  TaskDetail: { taskId: string };
  Recording: undefined;
  CropScouting: { fieldId: string };
  OfflineFieldMapping: undefined;
  ABLineNavigation: { fieldId?: string };
};

export type MainTabParamList = {
  Home: undefined;
  Fields: undefined;
  Map: undefined;
  Tasks: undefined;
  Weather: undefined;
  Profile: undefined;
};

const Stack = createNativeStackNavigator<MainStackParamList>();
const Tab = createBottomTabNavigator<MainTabParamList>();

const MainTabs: React.FC = () => {
  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName: keyof typeof Ionicons.glyphMap = 'home';

          if (route.name === 'Home') {
            iconName = focused ? 'home' : 'home-outline';
          } else if (route.name === 'Fields') {
            iconName = focused ? 'grid' : 'grid-outline';
          } else if (route.name === 'Map') {
            iconName = focused ? 'map' : 'map-outline';
          } else if (route.name === 'Tasks') {
            iconName = focused ? 'list' : 'list-outline';
          } else if (route.name === 'Weather') {
            iconName = focused ? 'cloud' : 'cloud-outline';
          } else if (route.name === 'Profile') {
            iconName = focused ? 'person' : 'person-outline';
          }

          return <Ionicons name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: '#22c55e',
        tabBarInactiveTintColor: 'gray',
        headerShown: true,
        headerStyle: {
          backgroundColor: '#22c55e',
        },
        headerTintColor: '#fff',
        headerTitleStyle: {
          fontWeight: 'bold',
        },
      })}
    >
      <Tab.Screen name="Home" component={HomeScreen} options={{ title: 'Dashboard' }} />
      <Tab.Screen name="Fields" component={FieldsScreen} options={{ title: 'Fields' }} />
      <Tab.Screen name="Map" component={MapScreen} options={{ title: 'Map' }} />
      <Tab.Screen name="Tasks" component={TasksScreen} options={{ title: 'Tasks' }} />
      <Tab.Screen name="Weather" component={WeatherScreen} options={{ title: 'Weather' }} />
      <Tab.Screen name="Profile" component={ProfileScreen} options={{ title: 'Profile' }} />
    </Tab.Navigator>
  );
};

const MainNavigator: React.FC = () => {
  return (
    <Stack.Navigator
      initialRouteName="MainTabs"
      screenOptions={{
        headerShown: false,
      }}
    >
      <Stack.Screen name="MainTabs" component={MainTabs} />
      <Stack.Screen 
        name="FieldDetail" 
        component={FieldDetailScreen} 
        options={{ 
          headerShown: true,
          headerStyle: { backgroundColor: '#22c55e' },
          headerTintColor: '#fff',
          title: 'Field Details'
        }} 
      />
      <Stack.Screen 
        name="TaskDetail" 
        component={TaskDetailScreen} 
        options={{ 
          headerShown: true,
          headerStyle: { backgroundColor: '#22c55e' },
          headerTintColor: '#fff',
          title: 'Task Details'
        }} 
      />
      <Stack.Screen 
        name="Recording" 
        component={RecordingScreen} 
        options={{ 
          headerShown: true,
          headerStyle: { backgroundColor: '#22c55e' },
          headerTintColor: '#fff',
          title: 'GPS Recording'
        }} 
      />
      <Stack.Screen 
        name="CropScouting" 
        component={CropScoutingScreen} 
        options={{ 
          headerShown: true,
          headerStyle: { backgroundColor: '#22c55e' },
          headerTintColor: '#fff',
          title: 'Crop Scouting'
        }} 
      />
      <Stack.Screen 
        name="OfflineFieldMapping" 
        component={OfflineFieldMappingScreen} 
        options={{ 
          headerShown: true,
          headerStyle: { backgroundColor: '#22c55e' },
          headerTintColor: '#fff',
          title: 'Offline Field Mapping'
        }} 
      />
      <Stack.Screen 
        name="ABLineNavigation" 
        component={ABLineNavigationScreen} 
        options={{ 
          headerShown: true,
          headerStyle: { backgroundColor: '#22c55e' },
          headerTintColor: '#fff',
          title: 'AB Line Navigation'
        }} 
      />
    </Stack.Navigator>
  );
};

export default MainNavigator;
