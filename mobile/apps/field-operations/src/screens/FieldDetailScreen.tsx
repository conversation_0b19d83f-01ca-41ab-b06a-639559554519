import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../../../../shared/store/AuthProvider';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { MainStackParamList } from '../navigation/MainNavigator';
import { fieldService } from '../../../../shared/services/fieldService';
import { taskService } from '../../../../shared/services/taskService';
import { weatherService } from '../../../../shared/services/weatherService';
import MapView, { Polygon, Marker, PROVIDER_GOOGLE } from 'react-native-maps';

type FieldDetailScreenProps = NativeStackScreenProps<MainStackParamList, 'FieldDetail'>;

type Field = {
  id: string;
  name: string;
  acres: number;
  cropType?: string;
  plantingDate?: string;
  harvestDate?: string;
  status: 'active' | 'inactive';
  notes?: string;
  boundaries: {
    type: string;
    coordinates: number[][][];
  };
  latitude: number;
  longitude: number;
  createdAt: string;
  updatedAt: string;
};

type Task = {
  id: string;
  title: string;
  status: 'active' | 'completed' | 'cancelled';
  dueDate: string;
};

const FieldDetailScreen: React.FC<FieldDetailScreenProps> = ({ route, navigation }) => {
  const { fieldId } = route.params;
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [field, setField] = useState<Field | null>(null);
  const [tasks, setTasks] = useState<Task[]>([]);
  const [weatherData, setWeatherData] = useState<any>(null);
  const [mapReady, setMapReady] = useState(false);

  const loadFieldData = async () => {
    try {
      setLoading(true);
      
      // Load field details
      const fieldData = await fieldService.getFieldById(fieldId);
      setField(fieldData);
      
      // Load tasks for this field
      const fieldTasks = await taskService.getTasks(user?.farmId || '', { fieldId });
      setTasks(fieldTasks);
      
      // Load weather data for this field
      if (fieldData.latitude && fieldData.longitude) {
        const weather = await weatherService.getWeather(fieldData.latitude, fieldData.longitude);
        setWeatherData(weather);
      }
      
      setLoading(false);
    } catch (error) {
      console.error('Error loading field details:', error);
      setLoading(false);
      Alert.alert('Error', 'Failed to load field details. Please try again.');
    }
  };

  useEffect(() => {
    loadFieldData();
  }, [fieldId]);

  const handleEditField = () => {
    navigation.navigate('EditField', { fieldId });
  };

  const handleCreateTask = () => {
    navigation.navigate('CreateTask', { fieldId });
  };

  const handleCropScouting = () => {
    navigation.navigate('CropScouting', { fieldId });
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'Not set';
    return new Date(dateString).toLocaleDateString();
  };

  const getPolygonCoordinates = () => {
    if (!field || !field.boundaries || !field.boundaries.coordinates) return [];
    
    // Convert GeoJSON coordinates to MapView polygon format
    const coordinates = field.boundaries.coordinates[0].map(coord => ({
      latitude: coord[1],
      longitude: coord[0],
    }));
    
    return coordinates;
  };

  const getMapRegion = () => {
    if (!field) return null;
    
    return {
      latitude: field.latitude,
      longitude: field.longitude,
      latitudeDelta: 0.05,
      longitudeDelta: 0.05,
    };
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#22c55e" />
      </View>
    );
  }

  if (!field) {
    return (
      <View style={styles.errorContainer}>
        <Ionicons name="alert-circle-outline" size={48} color="#ef4444" />
        <Text style={styles.errorText}>Field not found</Text>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Text style={styles.backButtonText}>Go Back</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <View style={styles.titleContainer}>
          <Text style={styles.title}>{field.name}</Text>
          <View
            style={[
              styles.statusBadge,
              { backgroundColor: field.status === 'active' ? '#22c55e' : '#9ca3af' },
            ]}
          >
            <Text style={styles.statusText}>
              {field.status === 'active' ? 'Active' : 'Inactive'}
            </Text>
          </View>
        </View>
        <Text style={styles.acreage}>{field.acres} acres</Text>
      </View>

      <View style={styles.mapContainer}>
        {!mapReady && (
          <View style={styles.mapLoadingOverlay}>
            <ActivityIndicator size="large" color="#22c55e" />
          </View>
        )}
        <MapView
          style={styles.map}
          provider={PROVIDER_GOOGLE}
          initialRegion={getMapRegion() || undefined}
          onMapReady={() => setMapReady(true)}
        >
          <Polygon
            coordinates={getPolygonCoordinates()}
            fillColor="rgba(34, 197, 94, 0.2)"
            strokeColor="#22c55e"
            strokeWidth={2}
          />
          <Marker
            coordinate={{
              latitude: field.latitude,
              longitude: field.longitude,
            }}
            title={field.name}
          />
        </MapView>
        <TouchableOpacity
          style={styles.fullMapButton}
          onPress={() => navigation.navigate('Map', { fieldId })}
        >
          <Ionicons name="expand-outline" size={20} color="white" />
          <Text style={styles.fullMapButtonText}>View Full Map</Text>
        </TouchableOpacity>
      </View>

      {weatherData && (
        <View style={styles.weatherCard}>
          <View style={styles.weatherHeader}>
            <Ionicons name="partly-sunny-outline" size={24} color="#22c55e" />
            <Text style={styles.weatherTitle}>Current Weather</Text>
          </View>
          <View style={styles.weatherContent}>
            <Text style={styles.temperature}>{Math.round(weatherData.temperature)}°</Text>
            <View style={styles.weatherDetails}>
              <Text style={styles.weatherDescription}>
                {weatherData.description.charAt(0).toUpperCase() + weatherData.description.slice(1)}
              </Text>
              <View style={styles.weatherItem}>
                <Ionicons name="water-outline" size={16} color="#6b7280" />
                <Text style={styles.weatherItemText}>{weatherData.humidity}% humidity</Text>
              </View>
              <View style={styles.weatherItem}>
                <Ionicons name="leaf-outline" size={16} color="#6b7280" />
                <Text style={styles.weatherItemText}>{Math.round(weatherData.windSpeed)} mph wind</Text>
              </View>
            </View>
          </View>
        </View>
      )}

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Crop Information</Text>
        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>Crop Type:</Text>
          <Text style={styles.infoValue}>{field.cropType || 'Not specified'}</Text>
        </View>
        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>Planting Date:</Text>
          <Text style={styles.infoValue}>{formatDate(field.plantingDate)}</Text>
        </View>
        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>Harvest Date:</Text>
          <Text style={styles.infoValue}>{formatDate(field.harvestDate)}</Text>
        </View>
      </View>

      {field.notes && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Notes</Text>
          <Text style={styles.notes}>{field.notes}</Text>
        </View>
      )}

      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Tasks</Text>
          <TouchableOpacity onPress={handleCreateTask}>
            <Ionicons name="add-circle-outline" size={24} color="#22c55e" />
          </TouchableOpacity>
        </View>
        {tasks.length > 0 ? (
          tasks.map((task) => (
            <TouchableOpacity
              key={task.id}
              style={styles.taskItem}
              onPress={() => navigation.navigate('TaskDetail', { taskId: task.id })}
            >
              <View style={styles.taskHeader}>
                <Text style={styles.taskTitle}>{task.title}</Text>
                <View
                  style={[
                    styles.taskStatusBadge,
                    {
                      backgroundColor:
                        task.status === 'active'
                          ? '#22c55e'
                          : task.status === 'completed'
                          ? '#3b82f6'
                          : '#ef4444',
                    },
                  ]}
                >
                  <Text style={styles.taskStatusText}>
                    {task.status.charAt(0).toUpperCase() + task.status.slice(1)}
                  </Text>
                </View>
              </View>
              <View style={styles.taskFooter}>
                <Ionicons name="calendar-outline" size={14} color="#6b7280" />
                <Text style={styles.taskDate}>
                  Due: {new Date(task.dueDate).toLocaleDateString()}
                </Text>
              </View>
            </TouchableOpacity>
          ))
        ) : (
          <Text style={styles.emptyText}>No tasks assigned to this field</Text>
        )}
      </View>

      <View style={styles.actionButtons}>
        <TouchableOpacity style={styles.actionButton} onPress={handleEditField}>
          <Ionicons name="create-outline" size={20} color="white" />
          <Text style={styles.actionButtonText}>Edit Field</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.actionButton} onPress={handleCropScouting}>
          <Ionicons name="leaf-outline" size={20} color="white" />
          <Text style={styles.actionButtonText}>Crop Scouting</Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9fafb',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 18,
    color: '#4b5563',
    marginTop: 10,
    marginBottom: 20,
  },
  backButton: {
    backgroundColor: '#22c55e',
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 5,
  },
  backButtonText: {
    color: 'white',
    fontWeight: 'bold',
  },
  header: {
    backgroundColor: '#22c55e',
    padding: 20,
  },
  titleContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
    flex: 1,
  },
  statusBadge: {
    paddingHorizontal: 10,
    paddingVertical: 5,
    borderRadius: 15,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  statusText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 12,
  },
  acreage: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.8)',
    marginTop: 5,
  },
  mapContainer: {
    height: 200,
    margin: 15,
    marginTop: -20,
    borderRadius: 10,
    overflow: 'hidden',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  mapLoadingOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(255, 255, 255, 0.7)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1,
  },
  map: {
    ...StyleSheet.absoluteFillObject,
  },
  fullMapButton: {
    position: 'absolute',
    bottom: 10,
    right: 10,
    backgroundColor: 'rgba(34, 197, 94, 0.8)',
    paddingVertical: 5,
    paddingHorizontal: 10,
    borderRadius: 5,
    flexDirection: 'row',
    alignItems: 'center',
  },
  fullMapButtonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 12,
    marginLeft: 5,
  },
  weatherCard: {
    backgroundColor: 'white',
    margin: 15,
    borderRadius: 10,
    overflow: 'hidden',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
  },
  weatherHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  weatherTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 10,
    color: '#111827',
  },
  weatherContent: {
    flexDirection: 'row',
    padding: 15,
    alignItems: 'center',
  },
  temperature: {
    fontSize: 36,
    fontWeight: 'bold',
    color: '#111827',
    marginRight: 20,
  },
  weatherDetails: {
    flex: 1,
  },
  weatherDescription: {
    fontSize: 16,
    color: '#4b5563',
    marginBottom: 5,
  },
  weatherItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 5,
  },
  weatherItemText: {
    fontSize: 14,
    color: '#6b7280',
    marginLeft: 5,
  },
  section: {
    backgroundColor: 'white',
    margin: 15,
    marginTop: 5,
    borderRadius: 10,
    padding: 15,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#111827',
    marginBottom: 10,
  },
  infoRow: {
    flexDirection: 'row',
    marginBottom: 10,
  },
  infoLabel: {
    fontSize: 16,
    color: '#6b7280',
    width: 120,
  },
  infoValue: {
    fontSize: 16,
    color: '#111827',
    flex: 1,
  },
  notes: {
    fontSize: 16,
    color: '#4b5563',
    lineHeight: 24,
  },
  taskItem: {
    backgroundColor: '#f9fafb',
    borderRadius: 8,
    padding: 12,
    marginBottom: 10,
  },
  taskHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 5,
  },
  taskTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#111827',
    flex: 1,
  },
  taskStatusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 3,
    borderRadius: 12,
  },
  taskStatusText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 10,
  },
  taskFooter: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  taskDate: {
    fontSize: 12,
    color: '#6b7280',
    marginLeft: 5,
  },
  emptyText: {
    textAlign: 'center',
    color: '#6b7280',
    padding: 10,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    margin: 15,
    marginTop: 5,
    marginBottom: 30,
  },
  actionButton: {
    backgroundColor: '#22c55e',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 15,
    borderRadius: 10,
    flex: 1,
    marginHorizontal: 5,
  },
  actionButtonText: {
    color: 'white',
    fontWeight: 'bold',
    marginLeft: 8,
  },
});

export default FieldDetailScreen;