import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  ActivityIndicator,
  RefreshControl,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../../../../shared/store/AuthProvider';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { MainTabParamList } from '../navigation/MainNavigator';
import { CompositeScreenProps } from '@react-navigation/native';
import { BottomTabScreenProps } from '@react-navigation/bottom-tabs';
import { MainStackParamList } from '../navigation/MainNavigator';
import { taskService } from '../../../../shared/services/taskService';

type TasksScreenProps = CompositeScreenProps<
  BottomTabScreenProps<MainTabParamList, 'Tasks'>,
  NativeStackScreenProps<MainStackParamList>
>;

type Task = {
  id: string;
  title: string;
  description: string;
  status: 'active' | 'completed' | 'cancelled';
  priority: 'low' | 'medium' | 'high';
  dueDate: string;
  fieldId?: string;
  fieldName?: string;
  assignedTo?: string;
  createdAt: string;
  updatedAt: string;
};

const TasksScreen: React.FC<TasksScreenProps> = ({ navigation }) => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [tasks, setTasks] = useState<Task[]>([]);
  const [filter, setFilter] = useState<'all' | 'active' | 'completed'>('active');

  const loadTasks = async () => {
    try {
      setLoading(true);

      if (!user?.farmId) {
        console.error('No farm ID available');
        setLoading(false);
        return;
      }

      // Load tasks based on filter
      const statusFilter = filter === 'all' ? undefined : filter;
      const fetchedTasks = await taskService.getTasks(user.farmId, { status: statusFilter });
      setTasks(fetchedTasks);

      setLoading(false);
    } catch (error) {
      console.error('Error loading tasks:', error);
      setLoading(false);
    }
  };

  useEffect(() => {
    loadTasks();
  }, [user?.farmId, filter]);

  const onRefresh = async () => {
    setRefreshing(true);
    await loadTasks();
    setRefreshing(false);
  };

  const handleCompleteTask = async (taskId: string) => {
    Alert.alert(
      'Complete Task',
      'Are you sure you want to mark this task as completed?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Complete',
          onPress: async () => {
            try {
              await taskService.updateTaskStatus(taskId, 'completed');
              // Refresh the task list
              loadTasks();
            } catch (error) {
              console.error('Error completing task:', error);
              Alert.alert('Error', 'Failed to complete task. Please try again.');
            }
          },
        },
      ]
    );
  };

  const renderPriorityIcon = (priority: string) => {
    switch (priority) {
      case 'high':
        return <Ionicons name="alert-circle" size={16} color="#ef4444" />;
      case 'medium':
        return <Ionicons name="alert-circle" size={16} color="#f59e0b" />;
      case 'low':
        return <Ionicons name="alert-circle" size={16} color="#22c55e" />;
      default:
        return null;
    }
  };

  const renderTaskItem = ({ item }: { item: Task }) => (
    <TouchableOpacity
      style={styles.taskItem}
      onPress={() => navigation.navigate('TaskDetail', { taskId: item.id })}
    >
      <View style={styles.taskHeader}>
        <View style={styles.taskTitleContainer}>
          {renderPriorityIcon(item.priority)}
          <Text style={styles.taskTitle}>{item.title}</Text>
        </View>
        {item.status === 'active' && (
          <TouchableOpacity
            style={styles.completeButton}
            onPress={() => handleCompleteTask(item.id)}
          >
            <Ionicons name="checkmark-circle-outline" size={24} color="#22c55e" />
          </TouchableOpacity>
        )}
      </View>
      <Text style={styles.taskDescription} numberOfLines={2}>
        {item.description}
      </Text>
      <View style={styles.taskFooter}>
        {item.fieldName && (
          <View style={styles.taskField}>
            <Ionicons name="location-outline" size={14} color="#6b7280" />
            <Text style={styles.taskFieldText}>{item.fieldName}</Text>
          </View>
        )}
        <View style={styles.taskDueDate}>
          <Ionicons name="calendar-outline" size={14} color="#6b7280" />
          <Text style={styles.taskDueDateText}>
            {new Date(item.dueDate).toLocaleDateString()}
          </Text>
        </View>
      </View>
    </TouchableOpacity>
  );

  if (loading && !refreshing) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#22c55e" />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.filterContainer}>
        <TouchableOpacity
          style={[styles.filterButton, filter === 'all' && styles.filterButtonActive]}
          onPress={() => setFilter('all')}
        >
          <Text
            style={[styles.filterButtonText, filter === 'all' && styles.filterButtonTextActive]}
          >
            All
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.filterButton, filter === 'active' && styles.filterButtonActive]}
          onPress={() => setFilter('active')}
        >
          <Text
            style={[styles.filterButtonText, filter === 'active' && styles.filterButtonTextActive]}
          >
            Active
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.filterButton, filter === 'completed' && styles.filterButtonActive]}
          onPress={() => setFilter('completed')}
        >
          <Text
            style={[
              styles.filterButtonText,
              filter === 'completed' && styles.filterButtonTextActive,
            ]}
          >
            Completed
          </Text>
        </TouchableOpacity>
      </View>

      <FlatList
        data={tasks}
        renderItem={renderTaskItem}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.taskList}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} colors={['#22c55e']} />
        }
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Ionicons name="list" size={48} color="#d1d5db" />
            <Text style={styles.emptyText}>No tasks found</Text>
            <Text style={styles.emptySubText}>
              {filter === 'active'
                ? 'You have no active tasks'
                : filter === 'completed'
                ? 'You have no completed tasks'
                : 'You have no tasks'}
            </Text>
          </View>
        }
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9fafb',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  filterContainer: {
    flexDirection: 'row',
    padding: 10,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  filterButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 20,
    marginRight: 8,
  },
  filterButtonActive: {
    backgroundColor: '#22c55e',
  },
  filterButtonText: {
    color: '#6b7280',
    fontWeight: '500',
  },
  filterButtonTextActive: {
    color: 'white',
  },
  taskList: {
    padding: 15,
  },
  taskItem: {
    backgroundColor: 'white',
    borderRadius: 10,
    padding: 15,
    marginBottom: 10,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
  },
  taskHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 5,
  },
  taskTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  taskTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 5,
  },
  completeButton: {
    padding: 5,
  },
  taskDescription: {
    fontSize: 14,
    color: '#4b5563',
    marginBottom: 10,
  },
  taskFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 5,
  },
  taskField: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  taskFieldText: {
    fontSize: 12,
    color: '#6b7280',
    marginLeft: 4,
  },
  taskDueDate: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  taskDueDateText: {
    fontSize: 12,
    color: '#6b7280',
    marginLeft: 4,
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 40,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#9ca3af',
    marginTop: 10,
  },
  emptySubText: {
    fontSize: 14,
    color: '#9ca3af',
    marginTop: 5,
    textAlign: 'center',
  },
});

export default TasksScreen;