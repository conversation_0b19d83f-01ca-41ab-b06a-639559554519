import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  Modal,
  TextInput,
  ScrollView,
  Switch,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../../../../shared/store/AuthProvider';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { MainStackParamList } from '../navigation/MainNavigator';
import MapView, { <PERSON>er, Polyline, Circle, PROVIDER_GOOGLE } from 'react-native-maps';
import * as Location from 'expo-location';
import { fieldService } from '../../../../shared/services/fieldService';
import Slider from '@react-native-community/slider';

type ABLineNavigationScreenProps = NativeStackScreenProps<MainStackParamList, 'ABLineNavigation'>;

type Field = {
  id: string;
  name: string;
  boundary: Array<{
    latitude: number;
    longitude: number;
  }>;
};

type ABLine = {
  id: string;
  name: string;
  fieldId: string;
  type: 'straight' | 'curved' | 'pivot';
  points: Array<{
    latitude: number;
    longitude: number;
  }>;
  // For pivot lines
  pivotPoint?: {
    latitude: number;
    longitude: number;
  };
  radius?: number;
  // For all line types
  spacing: number; // spacing between guidance lines in meters
  color: string;
};

const ABLineNavigationScreen: React.FC<ABLineNavigationScreenProps> = ({ navigation, route }) => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [fields, setFields] = useState<Field[]>([]);
  const [selectedField, setSelectedField] = useState<Field | null>(null);
  const [abLines, setAbLines] = useState<ABLine[]>([]);
  const [selectedABLine, setSelectedABLine] = useState<ABLine | null>(null);
  const [currentLocation, setCurrentLocation] = useState<Location.LocationObject | null>(null);
  const [navigationActive, setNavigationActive] = useState(false);
  const [showCreateLineModal, setShowCreateLineModal] = useState(false);
  const [lineCreationMode, setLineCreationMode] = useState<'straight' | 'curved' | 'pivot'>('straight');
  const [newLineName, setNewLineName] = useState('');
  const [newLineSpacing, setNewLineSpacing] = useState('10'); // Default spacing in meters
  const [newLineColor, setNewLineColor] = useState('#3b82f6'); // Default color
  const [drawingPoints, setDrawingPoints] = useState<Array<{ latitude: number; longitude: number }>>([]);
  const [isDrawing, setIsDrawing] = useState(false);
  const [pivotPoint, setPivotPoint] = useState<{ latitude: number; longitude: number } | null>(null);
  const [pivotRadius, setPivotRadius] = useState(100); // Default radius in meters
  const [showPivotControls, setShowPivotControls] = useState(false);
  const [guidanceLines, setGuidanceLines] = useState<Array<Array<{ latitude: number; longitude: number }>>>([]);
  const [showGuidanceLines, setShowGuidanceLines] = useState(true);
  const [currentLineIndex, setCurrentLineIndex] = useState(0);
  const [offTrackDistance, setOffTrackDistance] = useState(0);
  const [heading, setHeading] = useState(0);
  const [voiceGuidance, setVoiceGuidance] = useState(true);
  const mapRef = useRef<MapView>(null);

  // Mock data for initial development - would be replaced with actual API calls
  const mockABLines: ABLine[] = [
    {
      id: 'line1',
      name: 'North Field Straight Line',
      fieldId: 'field1',
      type: 'straight',
      points: [
        { latitude: 37.7749, longitude: -122.4194 },
        { latitude: 37.7749, longitude: -122.4294 }
      ],
      spacing: 10,
      color: '#3b82f6'
    },
    {
      id: 'line2',
      name: 'East Field Curved Path',
      fieldId: 'field2',
      type: 'curved',
      points: [
        { latitude: 37.7749, longitude: -122.4194 },
        { latitude: 37.7739, longitude: -122.4184 },
        { latitude: 37.7729, longitude: -122.4174 },
        { latitude: 37.7719, longitude: -122.4164 }
      ],
      spacing: 15,
      color: '#8b5cf6'
    },
    {
      id: 'line3',
      name: 'Center Pivot',
      fieldId: 'field3',
      type: 'pivot',
      points: [],
      pivotPoint: { latitude: 37.7749, longitude: -122.4194 },
      radius: 200,
      spacing: 20,
      color: '#ef4444'
    }
  ];

  useEffect(() => {
    const fetchFieldsAndLocation = async () => {
      try {
        setLoading(true);

        // Request location permissions
        const { status } = await Location.requestForegroundPermissionsAsync();
        if (status !== 'granted') {
          Alert.alert('Permission Denied', 'Location permission is required for navigation.');
          setLoading(false);
          return;
        }

        // Get current location
        const location = await Location.getCurrentPositionAsync({});
        setCurrentLocation(location);

        // Fetch fields
        if (user?.farmId) {
          const fieldsData = await fieldService.getFields(user.farmId);
          setFields(fieldsData);
          
          // If a fieldId was passed in route params, select that field
          if (route.params?.fieldId) {
            const field = fieldsData.find(f => f.id === route.params.fieldId);
            if (field) {
              setSelectedField(field);
              
              // Fetch AB lines for this field (using mock data for now)
              const fieldABLines = mockABLines.filter(line => line.fieldId === field.id);
              setAbLines(fieldABLines);
            }
          }
        } else {
          // For development, use mock data
          setAbLines(mockABLines);
        }

        setLoading(false);
      } catch (error) {
        console.error('Error fetching data:', error);
        Alert.alert('Error', 'Could not load navigation data. Please try again later.');
        setLoading(false);
      }
    };

    fetchFieldsAndLocation();

    // Start location updates
    const locationSubscription = Location.watchPositionAsync(
      {
        accuracy: Location.Accuracy.Highest,
        distanceInterval: 1, // minimum distance (in meters) between updates
        timeInterval: 1000, // minimum time (in ms) between updates
      },
      (location) => {
        setCurrentLocation(location);
        
        // Update heading
        if (location.coords.heading !== null) {
          setHeading(location.coords.heading);
        }
        
        // Calculate off-track distance if navigation is active
        if (navigationActive && selectedABLine) {
          const distance = calculateOffTrackDistance(
            location.coords.latitude,
            location.coords.longitude,
            selectedABLine,
            currentLineIndex
          );
          setOffTrackDistance(distance);
          
          // Provide voice guidance based on distance (would be implemented with a speech library)
          if (voiceGuidance && Math.abs(distance) > 2) {
            // This would trigger voice guidance in a real implementation
            console.log(`Voice guidance: Move ${distance > 0 ? 'right' : 'left'} ${Math.abs(distance).toFixed(1)} meters`);
          }
        }
      }
    );

    return () => {
      locationSubscription.then(sub => sub.remove());
    };
  }, [user?.farmId, route.params?.fieldId, navigationActive, selectedABLine, currentLineIndex, voiceGuidance]);

  // Generate guidance lines when an AB line is selected
  useEffect(() => {
    if (selectedABLine) {
      const lines = generateGuidanceLines(selectedABLine);
      setGuidanceLines(lines);
      setCurrentLineIndex(Math.floor(lines.length / 2)); // Start with the middle line
    } else {
      setGuidanceLines([]);
      setCurrentLineIndex(0);
    }
  }, [selectedABLine]);

  const selectField = (field: Field) => {
    setSelectedField(field);
    
    // Fetch AB lines for this field (using mock data for now)
    const fieldABLines = mockABLines.filter(line => line.fieldId === field.id);
    setAbLines(fieldABLines);
    setSelectedABLine(null);
    
    // Center map on field
    if (mapRef.current) {
      const coordinates = [...field.boundary];
      mapRef.current.fitToCoordinates(coordinates, {
        edgePadding: { top: 50, right: 50, bottom: 50, left: 50 },
        animated: true,
      });
    }
  };

  const selectABLine = (line: ABLine) => {
    setSelectedABLine(line);
    
    // Center map on line
    if (mapRef.current) {
      let coordinates = [...line.points];
      if (line.type === 'pivot' && line.pivotPoint) {
        coordinates = [line.pivotPoint];
      }
      
      mapRef.current.fitToCoordinates(coordinates, {
        edgePadding: { top: 50, right: 50, bottom: 50, left: 50 },
        animated: true,
      });
    }
  };

  const startNavigation = () => {
    if (!selectedABLine) {
      Alert.alert('Select Line', 'Please select an AB line before starting navigation.');
      return;
    }
    
    setNavigationActive(true);
    
    // Center map on current location with heading up
    if (mapRef.current && currentLocation) {
      mapRef.current.animateCamera({
        center: {
          latitude: currentLocation.coords.latitude,
          longitude: currentLocation.coords.longitude,
        },
        heading: heading,
        pitch: 45,
        zoom: 19,
      });
    }
  };

  const stopNavigation = () => {
    setNavigationActive(false);
    
    // Reset map view
    if (mapRef.current && selectedField) {
      const coordinates = [...selectedField.boundary];
      mapRef.current.fitToCoordinates(coordinates, {
        edgePadding: { top: 50, right: 50, bottom: 50, left: 50 },
        animated: true,
      });
    }
  };

  const openCreateLineModal = () => {
    if (!selectedField) {
      Alert.alert('Select Field', 'Please select a field before creating an AB line.');
      return;
    }
    
    setNewLineName('');
    setNewLineSpacing('10');
    setNewLineColor('#3b82f6');
    setLineCreationMode('straight');
    setDrawingPoints([]);
    setPivotPoint(null);
    setPivotRadius(100);
    setShowPivotControls(false);
    setShowCreateLineModal(true);
  };

  const startDrawingLine = () => {
    setIsDrawing(true);
    setDrawingPoints([]);
    setShowCreateLineModal(false);
    
    let message = '';
    if (lineCreationMode === 'straight') {
      message = 'Tap on the map to set point A, then tap again to set point B.';
    } else if (lineCreationMode === 'curved') {
      message = 'Tap on the map to add points to your curved line. Tap "Save Line" when finished.';
    } else if (lineCreationMode === 'pivot') {
      message = 'Tap on the map to set the pivot center point.';
    }
    
    Alert.alert('Drawing Mode', message);
  };

  const handleMapPress = (event: any) => {
    if (!isDrawing) return;
    
    const { coordinate } = event.nativeEvent;
    
    if (lineCreationMode === 'straight') {
      if (drawingPoints.length === 0) {
        // First point (A)
        setDrawingPoints([coordinate]);
        Alert.alert('Point A Set', 'Now tap to set point B.');
      } else if (drawingPoints.length === 1) {
        // Second point (B)
        setDrawingPoints([...drawingPoints, coordinate]);
        finishDrawing();
      }
    } else if (lineCreationMode === 'curved') {
      setDrawingPoints([...drawingPoints, coordinate]);
    } else if (lineCreationMode === 'pivot') {
      setPivotPoint(coordinate);
      setShowPivotControls(true);
      Alert.alert('Pivot Center Set', 'Adjust the radius using the slider.');
    }
  };

  const finishDrawing = () => {
    if (lineCreationMode === 'straight' && drawingPoints.length !== 2) {
      Alert.alert('Error', 'A straight line must have exactly 2 points.');
      return;
    }
    
    if (lineCreationMode === 'curved' && drawingPoints.length < 3) {
      Alert.alert('Error', 'A curved line must have at least 3 points.');
      return;
    }
    
    if (lineCreationMode === 'pivot' && !pivotPoint) {
      Alert.alert('Error', 'Please set a pivot center point.');
      return;
    }
    
    setIsDrawing(false);
    setShowCreateLineModal(true);
  };

  const cancelDrawing = () => {
    setIsDrawing(false);
    setDrawingPoints([]);
    setPivotPoint(null);
    setShowPivotControls(false);
  };

  const saveLine = () => {
    if (!newLineName.trim()) {
      Alert.alert('Error', 'Please enter a line name.');
      return;
    }
    
    if (isNaN(parseFloat(newLineSpacing)) || parseFloat(newLineSpacing) <= 0) {
      Alert.alert('Error', 'Please enter a valid spacing value greater than 0.');
      return;
    }
    
    try {
      // Create a new AB line
      const newLine: ABLine = {
        id: `line-${Date.now()}`,
        name: newLineName,
        fieldId: selectedField?.id || '',
        type: lineCreationMode,
        points: drawingPoints,
        spacing: parseFloat(newLineSpacing),
        color: newLineColor,
      };
      
      if (lineCreationMode === 'pivot' && pivotPoint) {
        newLine.pivotPoint = pivotPoint;
        newLine.radius = pivotRadius;
        newLine.points = generatePivotPoints(pivotPoint, pivotRadius);
      }
      
      // In a real app, this would save to a database
      // For now, just add to the local state
      setAbLines([...abLines, newLine]);
      setSelectedABLine(newLine);
      
      // Reset state
      setShowCreateLineModal(false);
      setDrawingPoints([]);
      setPivotPoint(null);
      setShowPivotControls(false);
      
      Alert.alert('Success', 'AB line created successfully!');
    } catch (error) {
      console.error('Error saving AB line:', error);
      Alert.alert('Error', 'Could not save AB line. Please try again later.');
    }
  };

  const generatePivotPoints = (center: { latitude: number; longitude: number }, radius: number) => {
    const points = [];
    const numPoints = 36; // One point every 10 degrees
    
    for (let i = 0; i < numPoints; i++) {
      const angle = (i / numPoints) * 2 * Math.PI;
      const lat = center.latitude + (radius / 111111) * Math.cos(angle);
      const lng = center.longitude + (radius / (111111 * Math.cos(center.latitude * (Math.PI / 180)))) * Math.sin(angle);
      points.push({ latitude: lat, longitude: lng });
    }
    
    return points;
  };

  const generateGuidanceLines = (abLine: ABLine) => {
    const lines = [];
    
    if (abLine.type === 'straight') {
      // Generate parallel lines for straight AB line
      const pointA = abLine.points[0];
      const pointB = abLine.points[1];
      
      // Calculate perpendicular vector
      const dx = pointB.longitude - pointA.longitude;
      const dy = pointB.latitude - pointA.latitude;
      const length = Math.sqrt(dx * dx + dy * dy);
      const perpX = -dy / length;
      const perpY = dx / length;
      
      // Convert spacing from meters to degrees
      const spacingLat = abLine.spacing / 111111;
      const spacingLng = abLine.spacing / (111111 * Math.cos(pointA.latitude * (Math.PI / 180)));
      
      // Generate 5 lines on each side of the original line
      for (let i = -5; i <= 5; i++) {
        const offsetX = i * spacingLng * perpX;
        const offsetY = i * spacingLat * perpY;
        
        const linePointA = {
          latitude: pointA.latitude + offsetY,
          longitude: pointA.longitude + offsetX,
        };
        
        const linePointB = {
          latitude: pointB.latitude + offsetY,
          longitude: pointB.longitude + offsetX,
        };
        
        lines.push([linePointA, linePointB]);
      }
    } else if (abLine.type === 'curved') {
      // For curved lines, we'd need a more complex algorithm to generate parallel curves
      // This is a simplified version that just offsets each point
      const points = abLine.points;
      
      // Generate 5 lines on each side of the original line
      for (let i = -5; i <= 5; i++) {
        const offsetLine = [];
        
        for (let j = 0; j < points.length - 1; j++) {
          const pointA = points[j];
          const pointB = points[j + 1];
          
          // Calculate perpendicular vector
          const dx = pointB.longitude - pointA.longitude;
          const dy = pointB.latitude - pointA.latitude;
          const length = Math.sqrt(dx * dx + dy * dy);
          const perpX = -dy / length;
          const perpY = dx / length;
          
          // Convert spacing from meters to degrees
          const spacingLat = abLine.spacing / 111111;
          const spacingLng = abLine.spacing / (111111 * Math.cos(pointA.latitude * (Math.PI / 180)));
          
          const offsetX = i * spacingLng * perpX;
          const offsetY = i * spacingLat * perpY;
          
          offsetLine.push({
            latitude: pointA.latitude + offsetY,
            longitude: pointA.longitude + offsetX,
          });
          
          if (j === points.length - 2) {
            offsetLine.push({
              latitude: pointB.latitude + offsetY,
              longitude: pointB.longitude + offsetX,
            });
          }
        }
        
        lines.push(offsetLine);
      }
    } else if (abLine.type === 'pivot' && abLine.pivotPoint) {
      // For pivot, generate concentric circles
      const center = abLine.pivotPoint;
      const baseRadius = abLine.radius || 100;
      
      // Generate 5 circles inside and 5 outside the original circle
      for (let i = -5; i <= 5; i++) {
        const radius = baseRadius + (i * abLine.spacing);
        if (radius <= 0) continue;
        
        const circlePoints = generatePivotPoints(center, radius);
        lines.push(circlePoints);
      }
    }
    
    return lines;
  };

  const calculateOffTrackDistance = (
    lat: number,
    lng: number,
    abLine: ABLine,
    lineIndex: number
  ) => {
    if (!guidanceLines[lineIndex]) return 0;
    
    if (abLine.type === 'straight') {
      // For straight lines, calculate perpendicular distance to the line
      const line = guidanceLines[lineIndex];
      const pointA = line[0];
      const pointB = line[1];
      
      // Calculate distance from point to line
      const A = lat - pointA.latitude;
      const B = lng - pointA.longitude;
      const C = pointB.latitude - pointA.latitude;
      const D = pointB.longitude - pointA.longitude;
      
      const dot = A * C + B * D;
      const lenSq = C * C + D * D;
      const param = dot / lenSq;
      
      let xx, yy;
      
      if (param < 0) {
        xx = pointA.latitude;
        yy = pointA.longitude;
      } else if (param > 1) {
        xx = pointB.latitude;
        yy = pointB.longitude;
      } else {
        xx = pointA.latitude + param * C;
        yy = pointA.longitude + param * D;
      }
      
      const dx = lat - xx;
      const dy = lng - yy;
      
      // Convert to meters
      const distLat = dx * 111111;
      const distLng = dy * 111111 * Math.cos(lat * (Math.PI / 180));
      
      return Math.sqrt(distLat * distLat + distLng * distLng);
    } else if (abLine.type === 'curved') {
      // For curved lines, find the closest point on the line
      const line = guidanceLines[lineIndex];
      let minDist = Number.MAX_VALUE;
      
      for (let i = 0; i < line.length - 1; i++) {
        const pointA = line[i];
        const pointB = line[i + 1];
        
        // Calculate distance from point to line segment
        const A = lat - pointA.latitude;
        const B = lng - pointA.longitude;
        const C = pointB.latitude - pointA.latitude;
        const D = pointB.longitude - pointA.longitude;
        
        const dot = A * C + B * D;
        const lenSq = C * C + D * D;
        const param = lenSq !== 0 ? dot / lenSq : -1;
        
        let xx, yy;
        
        if (param < 0) {
          xx = pointA.latitude;
          yy = pointA.longitude;
        } else if (param > 1) {
          xx = pointB.latitude;
          yy = pointB.longitude;
        } else {
          xx = pointA.latitude + param * C;
          yy = pointA.longitude + param * D;
        }
        
        const dx = lat - xx;
        const dy = lng - yy;
        
        // Convert to meters
        const distLat = dx * 111111;
        const distLng = dy * 111111 * Math.cos(lat * (Math.PI / 180));
        
        const dist = Math.sqrt(distLat * distLat + distLng * distLng);
        if (dist < minDist) {
          minDist = dist;
        }
      }
      
      return minDist;
    } else if (abLine.type === 'pivot' && abLine.pivotPoint) {
      // For pivot, calculate distance from point to circle
      const center = abLine.pivotPoint;
      const line = guidanceLines[lineIndex];
      
      // Calculate distance from point to center
      const dx = lat - center.latitude;
      const dy = lng - center.longitude;
      
      // Convert to meters
      const distLat = dx * 111111;
      const distLng = dy * 111111 * Math.cos(lat * (Math.PI / 180));
      
      const distToCenter = Math.sqrt(distLat * distLat + distLng * distLng);
      
      // Calculate radius of the current guidance line
      const linePoint = line[0];
      const lineDx = linePoint.latitude - center.latitude;
      const lineDy = linePoint.longitude - center.longitude;
      const lineDistLat = lineDx * 111111;
      const lineDistLng = lineDy * 111111 * Math.cos(center.latitude * (Math.PI / 180));
      const lineRadius = Math.sqrt(lineDistLat * lineDistLat + lineDistLng * lineDistLng);
      
      // Return distance to circle
      return Math.abs(distToCenter - lineRadius);
    }
    
    return 0;
  };

  const moveToNextLine = () => {
    if (currentLineIndex < guidanceLines.length - 1) {
      setCurrentLineIndex(currentLineIndex + 1);
    }
  };

  const moveToPreviousLine = () => {
    if (currentLineIndex > 0) {
      setCurrentLineIndex(currentLineIndex - 1);
    }
  };

  const renderFieldPolygons = () => {
    return fields.map((field) => (
      <Polyline
        key={field.id}
        coordinates={field.boundary}
        strokeColor={selectedField?.id === field.id ? '#22c55e' : '#9ca3af'}
        strokeWidth={2}
        onPress={() => selectField(field)}
      />
    ));
  };

  const renderABLines = () => {
    return abLines.map((line) => {
      if (line.type === 'straight' || line.type === 'curved') {
        return (
          <Polyline
            key={line.id}
            coordinates={line.points}
            strokeColor={selectedABLine?.id === line.id ? line.color : '#9ca3af'}
            strokeWidth={selectedABLine?.id === line.id ? 4 : 2}
            onPress={() => selectABLine(line)}
          />
        );
      } else if (line.type === 'pivot' && line.pivotPoint) {
        return (
          <Circle
            key={line.id}
            center={line.pivotPoint}
            radius={line.radius || 100}
            strokeColor={selectedABLine?.id === line.id ? line.color : '#9ca3af'}
            strokeWidth={selectedABLine?.id === line.id ? 4 : 2}
            fillColor="transparent"
            onPress={() => selectABLine(line)}
          />
        );
      }
      return null;
    });
  };

  const renderGuidanceLines = () => {
    if (!showGuidanceLines || !selectedABLine) return null;
    
    return guidanceLines.map((line, index) => (
      <Polyline
        key={`guidance-${index}`}
        coordinates={line}
        strokeColor={index === currentLineIndex ? selectedABLine.color : '#9ca3af'}
        strokeWidth={index === currentLineIndex ? 3 : 1}
        strokeDashPattern={index === currentLineIndex ? undefined : [5, 5]}
      />
    ));
  };

  const renderDrawingLine = () => {
    if (!isDrawing) return null;
    
    if (lineCreationMode === 'straight' || lineCreationMode === 'curved') {
      return (
        <>
          {drawingPoints.length > 0 && (
            <Polyline
              coordinates={drawingPoints}
              strokeColor="#ef4444"
              strokeWidth={3}
            />
          )}
          
          {drawingPoints.map((point, index) => (
            <Marker
              key={`drawing-point-${index}`}
              coordinate={point}
              anchor={{ x: 0.5, y: 0.5 }}
            >
              <View style={styles.drawingPoint} />
            </Marker>
          ))}
        </>
      );
    } else if (lineCreationMode === 'pivot' && pivotPoint) {
      return (
        <>
          <Marker
            coordinate={pivotPoint}
            anchor={{ x: 0.5, y: 0.5 }}
          >
            <View style={styles.pivotPoint} />
          </Marker>
          
          <Circle
            center={pivotPoint}
            radius={pivotRadius}
            strokeColor="#ef4444"
            strokeWidth={2}
            fillColor="rgba(239, 68, 68, 0.1)"
          />
        </>
      );
    }
    
    return null;
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#3b82f6" />
        <Text style={styles.loadingText}>Loading navigation data...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <MapView
        ref={mapRef}
        style={styles.map}
        provider={PROVIDER_GOOGLE}
        initialRegion={
          currentLocation
            ? {
                latitude: currentLocation.coords.latitude,
                longitude: currentLocation.coords.longitude,
                latitudeDelta: 0.05,
                longitudeDelta: 0.05,
              }
            : undefined
        }
        showsUserLocation
        followsUserLocation={navigationActive}
        onPress={handleMapPress}
      >
        {renderFieldPolygons()}
        {renderABLines()}
        {renderGuidanceLines()}
        {renderDrawingLine()}
      </MapView>

      {isDrawing && (
        <View style={styles.drawingControls}>
          <TouchableOpacity style={styles.cancelButton} onPress={cancelDrawing}>
            <Ionicons name="close" size={24} color="white" />
            <Text style={styles.buttonText}>Cancel</Text>
          </TouchableOpacity>
          
          {(lineCreationMode === 'curved' || (lineCreationMode === 'pivot' && showPivotControls)) && (
            <TouchableOpacity style={styles.saveButton} onPress={finishDrawing}>
              <Ionicons name="checkmark" size={24} color="white" />
              <Text style={styles.buttonText}>Save Line</Text>
            </TouchableOpacity>
          )}
        </View>
      )}

      {showPivotControls && (
        <View style={styles.pivotControls}>
          <Text style={styles.pivotRadiusText}>Radius: {pivotRadius} m</Text>
          <Slider
            style={styles.radiusSlider}
            minimumValue={10}
            maximumValue={500}
            step={5}
            value={pivotRadius}
            onValueChange={setPivotRadius}
            minimumTrackTintColor="#3b82f6"
            maximumTrackTintColor="#d1d5db"
            thumbTintColor="#3b82f6"
          />
        </View>
      )}

      {!isDrawing && !navigationActive && (
        <View style={styles.controlPanel}>
          <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.fieldSelector}>
            {fields.map((field) => (
              <TouchableOpacity
                key={field.id}
                style={[
                  styles.fieldButton,
                  selectedField?.id === field.id && styles.fieldButtonActive,
                ]}
                onPress={() => selectField(field)}
              >
                <Text
                  style={[
                    styles.fieldButtonText,
                    selectedField?.id === field.id && styles.fieldButtonTextActive,
                  ]}
                >
                  {field.name}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>

          {selectedField && (
            <>
              <View style={styles.abLineSelector}>
                <Text style={styles.sectionTitle}>AB Lines</Text>
                <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                  {abLines.map((line) => (
                    <TouchableOpacity
                      key={line.id}
                      style={[
                        styles.abLineButton,
                        selectedABLine?.id === line.id && styles.abLineButtonActive,
                        { borderColor: line.color },
                      ]}
                      onPress={() => selectABLine(line)}
                    >
                      <View style={[styles.lineTypeIndicator, { backgroundColor: line.color }]}>
                        <Ionicons
                          name={
                            line.type === 'straight'
                              ? 'remove'
                              : line.type === 'curved'
                              ? 'git-branch'
                              : 'radio-button-on'
                          }
                          size={16}
                          color="white"
                        />
                      </View>
                      <Text
                        style={[
                          styles.abLineButtonText,
                          selectedABLine?.id === line.id && styles.abLineButtonTextActive,
                        ]}
                      >
                        {line.name}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </ScrollView>
              </View>

              <View style={styles.actionButtons}>
                <TouchableOpacity style={styles.createLineButton} onPress={openCreateLineModal}>
                  <Ionicons name="add" size={20} color="white" />
                  <Text style={styles.buttonText}>Create AB Line</Text>
                </TouchableOpacity>

                {selectedABLine && (
                  <TouchableOpacity style={styles.startNavButton} onPress={startNavigation}>
                    <Ionicons name="navigate" size={20} color="white" />
                    <Text style={styles.buttonText}>Start Navigation</Text>
                  </TouchableOpacity>
                )}
              </View>
            </>
          )}
        </View>
      )}

      {navigationActive && (
        <View style={styles.navigationPanel}>
          <View style={styles.navigationHeader}>
            <Text style={styles.navigationTitle}>Navigating</Text>
            <Text style={styles.navigationLine}>{selectedABLine?.name}</Text>
          </View>

          <View style={styles.navigationInfo}>
            <View style={styles.offTrackContainer}>
              <Text style={styles.offTrackLabel}>Off-Track:</Text>
              <Text
                style={[
                  styles.offTrackValue,
                  Math.abs(offTrackDistance) < 0.5
                    ? styles.offTrackGood
                    : Math.abs(offTrackDistance) < 2
                    ? styles.offTrackWarning
                    : styles.offTrackBad,
                ]}
              >
                {offTrackDistance.toFixed(1)} m {offTrackDistance > 0 ? 'Right' : offTrackDistance < 0 ? 'Left' : ''}
              </Text>
            </View>

            <View style={styles.navigationControls}>
              <TouchableOpacity style={styles.lineChangeButton} onPress={moveToPreviousLine}>
                <Ionicons name="arrow-back" size={24} color="white" />
              </TouchableOpacity>

              <Text style={styles.currentLineText}>Line {currentLineIndex + 1}/{guidanceLines.length}</Text>

              <TouchableOpacity style={styles.lineChangeButton} onPress={moveToNextLine}>
                <Ionicons name="arrow-forward" size={24} color="white" />
              </TouchableOpacity>
            </View>

            <View style={styles.navigationSettings}>
              <View style={styles.settingRow}>
                <Text style={styles.settingLabel}>Show Guidance Lines</Text>
                <Switch
                  value={showGuidanceLines}
                  onValueChange={setShowGuidanceLines}
                  trackColor={{ false: '#d1d5db', true: '#bfdbfe' }}
                  thumbColor={showGuidanceLines ? '#3b82f6' : '#f4f3f4'}
                />
              </View>

              <View style={styles.settingRow}>
                <Text style={styles.settingLabel}>Voice Guidance</Text>
                <Switch
                  value={voiceGuidance}
                  onValueChange={setVoiceGuidance}
                  trackColor={{ false: '#d1d5db', true: '#bfdbfe' }}
                  thumbColor={voiceGuidance ? '#3b82f6' : '#f4f3f4'}
                />
              </View>
            </View>

            <TouchableOpacity style={styles.stopNavButton} onPress={stopNavigation}>
              <Ionicons name="stop" size={20} color="white" />
              <Text style={styles.buttonText}>Stop Navigation</Text>
            </TouchableOpacity>
          </View>
        </View>
      )}

      <Modal
        visible={showCreateLineModal}
        transparent
        animationType="slide"
        onRequestClose={() => setShowCreateLineModal(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>Create AB Line</Text>

            <Text style={styles.inputLabel}>Line Name</Text>
            <TextInput
              style={styles.input}
              value={newLineName}
              onChangeText={setNewLineName}
              placeholder="Enter line name"
            />

            <Text style={styles.inputLabel}>Line Type</Text>
            <View style={styles.lineTypeSelector}>
              <TouchableOpacity
                style={[
                  styles.lineTypeButton,
                  lineCreationMode === 'straight' && styles.lineTypeButtonActive,
                ]}
                onPress={() => setLineCreationMode('straight')}
              >
                <Ionicons name="remove" size={24} color={lineCreationMode === 'straight' ? 'white' : '#4b5563'} />
                <Text
                  style={[
                    styles.lineTypeButtonText,
                    lineCreationMode === 'straight' && styles.lineTypeButtonTextActive,
                  ]}
                >
                  Straight
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[
                  styles.lineTypeButton,
                  lineCreationMode === 'curved' && styles.lineTypeButtonActive,
                ]}
                onPress={() => setLineCreationMode('curved')}
              >
                <Ionicons name="git-branch" size={24} color={lineCreationMode === 'curved' ? 'white' : '#4b5563'} />
                <Text
                  style={[
                    styles.lineTypeButtonText,
                    lineCreationMode === 'curved' && styles.lineTypeButtonTextActive,
                  ]}
                >
                  Curved
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[
                  styles.lineTypeButton,
                  lineCreationMode === 'pivot' && styles.lineTypeButtonActive,
                ]}
                onPress={() => setLineCreationMode('pivot')}
              >
                <Ionicons name="radio-button-on" size={24} color={lineCreationMode === 'pivot' ? 'white' : '#4b5563'} />
                <Text
                  style={[
                    styles.lineTypeButtonText,
                    lineCreationMode === 'pivot' && styles.lineTypeButtonTextActive,
                  ]}
                >
                  Pivot
                </Text>
              </TouchableOpacity>
            </View>

            <Text style={styles.inputLabel}>Line Spacing (meters)</Text>
            <TextInput
              style={styles.input}
              value={newLineSpacing}
              onChangeText={setNewLineSpacing}
              placeholder="Enter spacing in meters"
              keyboardType="numeric"
            />

            <Text style={styles.inputLabel}>Line Color</Text>
            <View style={styles.colorSelector}>
              {['#3b82f6', '#8b5cf6', '#ef4444', '#f59e0b', '#22c55e'].map((color) => (
                <TouchableOpacity
                  key={color}
                  style={[
                    styles.colorButton,
                    { backgroundColor: color },
                    newLineColor === color && styles.colorButtonActive,
                  ]}
                  onPress={() => setNewLineColor(color)}
                />
              ))}
            </View>

            <View style={styles.modalButtons}>
              <TouchableOpacity
                style={[styles.modalButton, styles.modalCancelButton]}
                onPress={() => setShowCreateLineModal(false)}
              >
                <Text style={styles.modalCancelButtonText}>Cancel</Text>
              </TouchableOpacity>

              {drawingPoints.length > 0 || (lineCreationMode === 'pivot' && pivotPoint) ? (
                <TouchableOpacity
                  style={[styles.modalButton, styles.modalSaveButton]}
                  onPress={saveLine}
                >
                  <Text style={styles.modalSaveButtonText}>Save Line</Text>
                </TouchableOpacity>
              ) : (
                <TouchableOpacity
                  style={[styles.modalButton, styles.modalDrawButton]}
                  onPress={startDrawingLine}
                >
                  <Text style={styles.modalDrawButtonText}>Draw on Map</Text>
                </TouchableOpacity>
              )}
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#4b5563',
  },
  map: {
    flex: 1,
  },
  controlPanel: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'white',
    borderTopLeftRadius: 15,
    borderTopRightRadius: 15,
    padding: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 5,
  },
  fieldSelector: {
    flexDirection: 'row',
    marginBottom: 15,
  },
  fieldButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 20,
    marginRight: 8,
    backgroundColor: '#f3f4f6',
  },
  fieldButtonActive: {
    backgroundColor: '#3b82f6',
  },
  fieldButtonText: {
    color: '#4b5563',
    fontWeight: '500',
  },
  fieldButtonTextActive: {
    color: 'white',
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#111827',
    marginBottom: 10,
  },
  abLineSelector: {
    marginBottom: 15,
  },
  abLineButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 20,
    marginRight: 8,
    backgroundColor: '#f3f4f6',
    borderWidth: 1,
    borderColor: '#d1d5db',
  },
  abLineButtonActive: {
    backgroundColor: '#f3f4f6',
    borderWidth: 2,
  },
  lineTypeIndicator: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#4b5563',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 6,
  },
  abLineButtonText: {
    color: '#4b5563',
    fontWeight: '500',
  },
  abLineButtonTextActive: {
    color: '#111827',
    fontWeight: 'bold',
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  createLineButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#6b7280',
    paddingVertical: 10,
    paddingHorizontal: 15,
    borderRadius: 8,
    flex: 1,
    marginRight: 8,
  },
  startNavButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#3b82f6',
    paddingVertical: 10,
    paddingHorizontal: 15,
    borderRadius: 8,
    flex: 1,
  },
  buttonText: {
    color: 'white',
    fontWeight: 'bold',
    marginLeft: 5,
  },
  drawingControls: {
    position: 'absolute',
    bottom: 20,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingHorizontal: 20,
  },
  cancelButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#ef4444',
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 8,
    flex: 1,
    marginRight: 10,
  },
  saveButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#3b82f6',
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 8,
    flex: 1,
  },
  drawingPoint: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: '#ef4444',
    borderWidth: 2,
    borderColor: 'white',
  },
  pivotPoint: {
    width: 16,
    height: 16,
    borderRadius: 8,
    backgroundColor: '#ef4444',
    borderWidth: 2,
    borderColor: 'white',
  },
  pivotControls: {
    position: 'absolute',
    bottom: 80,
    left: 20,
    right: 20,
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 5,
  },
  pivotRadiusText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#111827',
    marginBottom: 10,
  },
  radiusSlider: {
    width: '100%',
    height: 40,
  },
  navigationPanel: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'white',
    borderTopLeftRadius: 15,
    borderTopRightRadius: 15,
    padding: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 5,
  },
  navigationHeader: {
    alignItems: 'center',
    marginBottom: 15,
  },
  navigationTitle: {
    fontSize: 14,
    color: '#6b7280',
  },
  navigationLine: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#111827',
  },
  navigationInfo: {
    marginBottom: 10,
  },
  offTrackContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 15,
    backgroundColor: '#f3f4f6',
    padding: 10,
    borderRadius: 8,
  },
  offTrackLabel: {
    fontSize: 16,
    color: '#4b5563',
    marginRight: 10,
  },
  offTrackValue: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  offTrackGood: {
    color: '#22c55e',
  },
  offTrackWarning: {
    color: '#f59e0b',
  },
  offTrackBad: {
    color: '#ef4444',
  },
  navigationControls: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  lineChangeButton: {
    backgroundColor: '#3b82f6',
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  currentLineText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#111827',
  },
  navigationSettings: {
    marginBottom: 15,
  },
  settingRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  settingLabel: {
    fontSize: 16,
    color: '#4b5563',
  },
  stopNavButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#ef4444',
    paddingVertical: 10,
    paddingHorizontal: 15,
    borderRadius: 8,
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    width: '90%',
    backgroundColor: 'white',
    borderRadius: 10,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#111827',
    marginBottom: 15,
    textAlign: 'center',
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#4b5563',
    marginBottom: 5,
  },
  input: {
    borderWidth: 1,
    borderColor: '#d1d5db',
    borderRadius: 8,
    padding: 10,
    marginBottom: 15,
    fontSize: 16,
  },
  lineTypeSelector: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 15,
  },
  lineTypeButton: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 10,
    marginHorizontal: 5,
    borderRadius: 8,
    backgroundColor: '#f3f4f6',
  },
  lineTypeButtonActive: {
    backgroundColor: '#3b82f6',
  },
  lineTypeButtonText: {
    color: '#4b5563',
    fontWeight: '500',
    marginTop: 5,
  },
  lineTypeButtonTextActive: {
    color: 'white',
  },
  colorSelector: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  colorButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    borderWidth: 2,
    borderColor: 'transparent',
  },
  colorButtonActive: {
    borderColor: '#111827',
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  modalButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  modalCancelButton: {
    backgroundColor: '#f3f4f6',
    marginRight: 10,
  },
  modalCancelButtonText: {
    color: '#4b5563',
    fontWeight: 'bold',
  },
  modalDrawButton: {
    backgroundColor: '#3b82f6',
  },
  modalDrawButtonText: {
    color: 'white',
    fontWeight: 'bold',
  },
  modalSaveButton: {
    backgroundColor: '#22c55e',
  },
  modalSaveButtonText: {
    color: 'white',
    fontWeight: 'bold',
  },
});

export default ABLineNavigationScreen;