import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Switch,
  Alert,
  ActivityIndicator,
  Image,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../../../../shared/store/AuthProvider';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { MainTabParamList } from '../navigation/MainNavigator';
import { CompositeScreenProps } from '@react-navigation/native';
import { BottomTabScreenProps } from '@react-navigation/bottom-tabs';
import { MainStackParamList } from '../navigation/MainNavigator';
import AsyncStorage from '@react-native-async-storage/async-storage';

type ProfileScreenProps = CompositeScreenProps<
  BottomTabScreenProps<MainTabParamList, 'Profile'>,
  NativeStackScreenProps<MainStackParamList>
>;

const ProfileScreen: React.FC<ProfileScreenProps> = ({ navigation }) => {
  const { user, logout } = useAuth();
  const [loading, setLoading] = useState(false);
  const [settings, setSettings] = useState({
    enableNotifications: true,
    enableLocationTracking: true,
    enableOfflineMode: true,
    useSatelliteView: false,
    useMetricUnits: false,
    enableDataSync: true,
  });

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      const savedSettings = await AsyncStorage.getItem('userSettings');
      if (savedSettings) {
        setSettings(JSON.parse(savedSettings));
      }
    } catch (error) {
      console.error('Error loading settings:', error);
    }
  };

  const saveSettings = async (newSettings: typeof settings) => {
    try {
      await AsyncStorage.setItem('userSettings', JSON.stringify(newSettings));
    } catch (error) {
      console.error('Error saving settings:', error);
    }
  };

  const handleToggleSetting = (setting: keyof typeof settings) => {
    const newSettings = {
      ...settings,
      [setting]: !settings[setting],
    };
    setSettings(newSettings);
    saveSettings(newSettings);
  };

  const handleLogout = () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to log out?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Logout',
          onPress: async () => {
            setLoading(true);
            try {
              await logout();
              // Navigation will be handled by the auth provider
            } catch (error) {
              console.error('Error logging out:', error);
              setLoading(false);
              Alert.alert('Error', 'Failed to log out. Please try again.');
            }
          },
        },
      ]
    );
  };

  const handleClearCache = () => {
    Alert.alert(
      'Clear Cache',
      'This will clear all cached data. You may need to download data again. Continue?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Clear',
          onPress: async () => {
            setLoading(true);
            try {
              // Clear all app data except auth tokens
              const authToken = await AsyncStorage.getItem('authToken');
              const refreshToken = await AsyncStorage.getItem('refreshToken');
              
              await AsyncStorage.clear();
              
              // Restore auth tokens
              if (authToken) await AsyncStorage.setItem('authToken', authToken);
              if (refreshToken) await AsyncStorage.setItem('refreshToken', refreshToken);
              
              // Restore settings
              await saveSettings(settings);
              
              setLoading(false);
              Alert.alert('Success', 'Cache cleared successfully');
            } catch (error) {
              console.error('Error clearing cache:', error);
              setLoading(false);
              Alert.alert('Error', 'Failed to clear cache. Please try again.');
            }
          },
        },
      ]
    );
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#22c55e" />
      </View>
    );
  }

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <View style={styles.profileImageContainer}>
          {user?.profileImage ? (
            <Image source={{ uri: user.profileImage }} style={styles.profileImage} />
          ) : (
            <View style={styles.profileImagePlaceholder}>
              <Text style={styles.profileImagePlaceholderText}>
                {user?.name?.charAt(0) || user?.email?.charAt(0) || 'U'}
              </Text>
            </View>
          )}
        </View>
        <Text style={styles.userName}>{user?.name || 'User'}</Text>
        <Text style={styles.userEmail}>{user?.email || 'No email provided'}</Text>
        <Text style={styles.userRole}>{user?.role || 'Field Operator'}</Text>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Account</Text>
        <TouchableOpacity style={styles.menuItem} onPress={() => navigation.navigate('EditProfile')}>
          <Ionicons name="person-outline" size={22} color="#22c55e" />
          <Text style={styles.menuItemText}>Edit Profile</Text>
          <Ionicons name="chevron-forward" size={20} color="#9ca3af" />
        </TouchableOpacity>
        <TouchableOpacity style={styles.menuItem} onPress={() => navigation.navigate('ChangePassword')}>
          <Ionicons name="lock-closed-outline" size={22} color="#22c55e" />
          <Text style={styles.menuItemText}>Change Password</Text>
          <Ionicons name="chevron-forward" size={20} color="#9ca3af" />
        </TouchableOpacity>
        <TouchableOpacity style={styles.menuItem} onPress={() => navigation.navigate('NotificationSettings')}>
          <Ionicons name="notifications-outline" size={22} color="#22c55e" />
          <Text style={styles.menuItemText}>Notification Settings</Text>
          <Ionicons name="chevron-forward" size={20} color="#9ca3af" />
        </TouchableOpacity>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>App Settings</Text>
        <View style={styles.settingItem}>
          <View style={styles.settingTextContainer}>
            <Ionicons name="notifications-outline" size={22} color="#22c55e" />
            <Text style={styles.settingText}>Enable Notifications</Text>
          </View>
          <Switch
            value={settings.enableNotifications}
            onValueChange={() => handleToggleSetting('enableNotifications')}
            trackColor={{ false: '#d1d5db', true: '#86efac' }}
            thumbColor={settings.enableNotifications ? '#22c55e' : '#f3f4f6'}
          />
        </View>
        <View style={styles.settingItem}>
          <View style={styles.settingTextContainer}>
            <Ionicons name="location-outline" size={22} color="#22c55e" />
            <Text style={styles.settingText}>Enable Location Tracking</Text>
          </View>
          <Switch
            value={settings.enableLocationTracking}
            onValueChange={() => handleToggleSetting('enableLocationTracking')}
            trackColor={{ false: '#d1d5db', true: '#86efac' }}
            thumbColor={settings.enableLocationTracking ? '#22c55e' : '#f3f4f6'}
          />
        </View>
        <View style={styles.settingItem}>
          <View style={styles.settingTextContainer}>
            <Ionicons name="cloud-offline-outline" size={22} color="#22c55e" />
            <Text style={styles.settingText}>Enable Offline Mode</Text>
          </View>
          <Switch
            value={settings.enableOfflineMode}
            onValueChange={() => handleToggleSetting('enableOfflineMode')}
            trackColor={{ false: '#d1d5db', true: '#86efac' }}
            thumbColor={settings.enableOfflineMode ? '#22c55e' : '#f3f4f6'}
          />
        </View>
        <View style={styles.settingItem}>
          <View style={styles.settingTextContainer}>
            <Ionicons name="map-outline" size={22} color="#22c55e" />
            <Text style={styles.settingText}>Use Satellite View</Text>
          </View>
          <Switch
            value={settings.useSatelliteView}
            onValueChange={() => handleToggleSetting('useSatelliteView')}
            trackColor={{ false: '#d1d5db', true: '#86efac' }}
            thumbColor={settings.useSatelliteView ? '#22c55e' : '#f3f4f6'}
          />
        </View>
        <View style={styles.settingItem}>
          <View style={styles.settingTextContainer}>
            <Ionicons name="options-outline" size={22} color="#22c55e" />
            <Text style={styles.settingText}>Use Metric Units</Text>
          </View>
          <Switch
            value={settings.useMetricUnits}
            onValueChange={() => handleToggleSetting('useMetricUnits')}
            trackColor={{ false: '#d1d5db', true: '#86efac' }}
            thumbColor={settings.useMetricUnits ? '#22c55e' : '#f3f4f6'}
          />
        </View>
        <View style={styles.settingItem}>
          <View style={styles.settingTextContainer}>
            <Ionicons name="sync-outline" size={22} color="#22c55e" />
            <Text style={styles.settingText}>Enable Data Sync</Text>
          </View>
          <Switch
            value={settings.enableDataSync}
            onValueChange={() => handleToggleSetting('enableDataSync')}
            trackColor={{ false: '#d1d5db', true: '#86efac' }}
            thumbColor={settings.enableDataSync ? '#22c55e' : '#f3f4f6'}
          />
        </View>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Support</Text>
        <TouchableOpacity style={styles.menuItem} onPress={() => navigation.navigate('HelpCenter')}>
          <Ionicons name="help-circle-outline" size={22} color="#22c55e" />
          <Text style={styles.menuItemText}>Help Center</Text>
          <Ionicons name="chevron-forward" size={20} color="#9ca3af" />
        </TouchableOpacity>
        <TouchableOpacity style={styles.menuItem} onPress={() => navigation.navigate('ContactSupport')}>
          <Ionicons name="chatbox-outline" size={22} color="#22c55e" />
          <Text style={styles.menuItemText}>Contact Support</Text>
          <Ionicons name="chevron-forward" size={20} color="#9ca3af" />
        </TouchableOpacity>
        <TouchableOpacity style={styles.menuItem} onPress={() => navigation.navigate('PrivacyPolicy')}>
          <Ionicons name="shield-outline" size={22} color="#22c55e" />
          <Text style={styles.menuItemText}>Privacy Policy</Text>
          <Ionicons name="chevron-forward" size={20} color="#9ca3af" />
        </TouchableOpacity>
        <TouchableOpacity style={styles.menuItem} onPress={() => navigation.navigate('TermsOfService')}>
          <Ionicons name="document-text-outline" size={22} color="#22c55e" />
          <Text style={styles.menuItemText}>Terms of Service</Text>
          <Ionicons name="chevron-forward" size={20} color="#9ca3af" />
        </TouchableOpacity>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Data Management</Text>
        <TouchableOpacity style={styles.menuItem} onPress={handleClearCache}>
          <Ionicons name="trash-outline" size={22} color="#22c55e" />
          <Text style={styles.menuItemText}>Clear Cache</Text>
          <Ionicons name="chevron-forward" size={20} color="#9ca3af" />
        </TouchableOpacity>
        <TouchableOpacity style={styles.menuItem} onPress={() => navigation.navigate('SyncData')}>
          <Ionicons name="sync-outline" size={22} color="#22c55e" />
          <Text style={styles.menuItemText}>Sync Data</Text>
          <Ionicons name="chevron-forward" size={20} color="#9ca3af" />
        </TouchableOpacity>
        <TouchableOpacity style={styles.menuItem} onPress={() => navigation.navigate('DownloadOfflineData')}>
          <Ionicons name="download-outline" size={22} color="#22c55e" />
          <Text style={styles.menuItemText}>Download Offline Data</Text>
          <Ionicons name="chevron-forward" size={20} color="#9ca3af" />
        </TouchableOpacity>
      </View>

      <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
        <Ionicons name="log-out-outline" size={22} color="white" />
        <Text style={styles.logoutButtonText}>Logout</Text>
      </TouchableOpacity>

      <View style={styles.versionContainer}>
        <Text style={styles.versionText}>Field Operations App v1.0.0</Text>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9fafb',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    backgroundColor: '#22c55e',
    padding: 20,
    alignItems: 'center',
    paddingBottom: 30,
  },
  profileImageContainer: {
    marginBottom: 15,
  },
  profileImage: {
    width: 100,
    height: 100,
    borderRadius: 50,
    borderWidth: 3,
    borderColor: 'white',
  },
  profileImagePlaceholder: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 3,
    borderColor: 'white',
  },
  profileImagePlaceholderText: {
    fontSize: 40,
    color: 'white',
    fontWeight: 'bold',
  },
  userName: {
    fontSize: 22,
    fontWeight: 'bold',
    color: 'white',
  },
  userEmail: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.8)',
    marginTop: 5,
  },
  userRole: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.8)',
    marginTop: 5,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: 10,
    paddingVertical: 3,
    borderRadius: 15,
  },
  section: {
    backgroundColor: 'white',
    margin: 15,
    marginTop: 15,
    borderRadius: 10,
    overflow: 'hidden',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#4b5563',
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  menuItemText: {
    fontSize: 16,
    color: '#111827',
    flex: 1,
    marginLeft: 15,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  settingTextContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  settingText: {
    fontSize: 16,
    color: '#111827',
    marginLeft: 15,
  },
  logoutButton: {
    backgroundColor: '#ef4444',
    margin: 15,
    padding: 15,
    borderRadius: 10,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  logoutButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 10,
  },
  versionContainer: {
    alignItems: 'center',
    padding: 20,
    paddingBottom: 40,
  },
  versionText: {
    fontSize: 14,
    color: '#9ca3af',
  },
});

export default ProfileScreen;