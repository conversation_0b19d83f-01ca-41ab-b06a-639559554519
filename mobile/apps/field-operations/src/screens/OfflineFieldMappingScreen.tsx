import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  Switch,
  ProgressBarAndroid,
  ProgressViewIOS,
  Platform,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../../../../shared/store/AuthProvider';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { MainStackParamList } from '../navigation/MainNavigator';
import { fieldService } from '../../../../shared/services/fieldService';
import AsyncStorage from '@react-native-async-storage/async-storage';
import NetInfo from '@react-native-community/netinfo';

type OfflineFieldMappingScreenProps = NativeStackScreenProps<MainStackParamList, 'OfflineFieldMapping'>;

type Field = {
  id: string;
  name: string;
  acres: number;
  cropType?: string;
  latitude: number;
  longitude: number;
};

type OfflineMapStatus = {
  fieldId: string;
  downloaded: boolean;
  lastUpdated: number;
  size: number;
};

const OfflineFieldMappingScreen: React.FC<OfflineFieldMappingScreenProps> = ({ navigation }) => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [fields, setFields] = useState<Field[]>([]);
  const [offlineStatus, setOfflineStatus] = useState<{ [key: string]: OfflineMapStatus }>({});
  const [isConnected, setIsConnected] = useState<boolean | null>(null);
  const [downloadProgress, setDownloadProgress] = useState<{ [key: string]: number }>({});
  const [isDownloading, setIsDownloading] = useState<{ [key: string]: boolean }>({});
  const [totalStorage, setTotalStorage] = useState<number>(0);
  const [availableStorage, setAvailableStorage] = useState<number>(0);

  useEffect(() => {
    checkConnectivity();
    loadFields();
    loadOfflineStatus();
    checkStorageStatus();

    // Subscribe to network state updates
    const unsubscribe = NetInfo.addEventListener(state => {
      setIsConnected(state.isConnected);
    });

    return () => {
      unsubscribe();
    };
  }, []);

  const checkConnectivity = async () => {
    const netInfo = await NetInfo.fetch();
    setIsConnected(netInfo.isConnected);
  };

  const loadFields = async () => {
    try {
      setLoading(true);
      if (!user?.farmId) {
        console.error('No farm ID available');
        setLoading(false);
        return;
      }

      const fetchedFields = await fieldService.getFields(user.farmId);
      setFields(fetchedFields);
      setLoading(false);
    } catch (error) {
      console.error('Error loading fields:', error);
      setLoading(false);
    }
  };

  const loadOfflineStatus = async () => {
    try {
      const statusJson = await AsyncStorage.getItem('offlineMapStatus');
      if (statusJson) {
        const status = JSON.parse(statusJson);
        setOfflineStatus(status);
      }
    } catch (error) {
      console.error('Error loading offline status:', error);
    }
  };

  const checkStorageStatus = async () => {
    try {
      // This is a simplified approach - in a real app, you would use a native module
      // to get actual device storage information
      const offlineMapSizeJson = await AsyncStorage.getItem('offlineMapTotalSize');
      const totalSize = offlineMapSizeJson ? parseInt(JSON.parse(offlineMapSizeJson)) : 0;
      setTotalStorage(totalSize);
      
      // Simulate available storage (500MB)
      setAvailableStorage(500 * 1024 * 1024 - totalSize);
    } catch (error) {
      console.error('Error checking storage status:', error);
    }
  };

  const toggleOfflineMap = async (field: Field) => {
    const fieldId = field.id;
    
    if (offlineStatus[fieldId]?.downloaded) {
      // Remove offline map
      Alert.alert(
        'Remove Offline Map',
        `Are you sure you want to remove the offline map for ${field.name}?`,
        [
          {
            text: 'Cancel',
            style: 'cancel',
          },
          {
            text: 'Remove',
            onPress: () => removeOfflineMap(field),
          },
        ]
      );
    } else {
      // Download offline map
      if (!isConnected) {
        Alert.alert(
          'No Internet Connection',
          'You need an internet connection to download offline maps.',
          [{ text: 'OK' }]
        );
        return;
      }
      
      // Estimate map size based on field size (simplified approach)
      const estimatedSize = Math.round(field.acres * 0.5 * 1024 * 1024); // 0.5MB per acre
      
      if (estimatedSize > availableStorage) {
        Alert.alert(
          'Insufficient Storage',
          'You do not have enough storage space to download this map.',
          [{ text: 'OK' }]
        );
        return;
      }
      
      Alert.alert(
        'Download Offline Map',
        `This will download approximately ${formatSize(estimatedSize)} of map data for offline use. Continue?`,
        [
          {
            text: 'Cancel',
            style: 'cancel',
          },
          {
            text: 'Download',
            onPress: () => downloadOfflineMap(field, estimatedSize),
          },
        ]
      );
    }
  };

  const downloadOfflineMap = async (field: Field, estimatedSize: number) => {
    const fieldId = field.id;
    
    try {
      // Set downloading state
      setIsDownloading({ ...isDownloading, [fieldId]: true });
      setDownloadProgress({ ...downloadProgress, [fieldId]: 0 });
      
      // Simulate download progress
      let progress = 0;
      const interval = setInterval(() => {
        progress += 0.05;
        if (progress >= 1) {
          clearInterval(interval);
          completeDownload(field, estimatedSize);
        } else {
          setDownloadProgress({ ...downloadProgress, [fieldId]: progress });
        }
      }, 200);
      
    } catch (error) {
      console.error('Error downloading offline map:', error);
      setIsDownloading({ ...isDownloading, [fieldId]: false });
      Alert.alert('Error', 'Failed to download offline map. Please try again.');
    }
  };

  const completeDownload = async (field: Field, size: number) => {
    const fieldId = field.id;
    
    try {
      // Update offline status
      const newStatus: OfflineMapStatus = {
        fieldId,
        downloaded: true,
        lastUpdated: Date.now(),
        size,
      };
      
      const updatedStatus = { ...offlineStatus, [fieldId]: newStatus };
      setOfflineStatus(updatedStatus);
      await AsyncStorage.setItem('offlineMapStatus', JSON.stringify(updatedStatus));
      
      // Update total storage
      const newTotalStorage = totalStorage + size;
      setTotalStorage(newTotalStorage);
      setAvailableStorage(availableStorage - size);
      await AsyncStorage.setItem('offlineMapTotalSize', JSON.stringify(newTotalStorage));
      
      // Reset download state
      setIsDownloading({ ...isDownloading, [fieldId]: false });
      
      Alert.alert('Download Complete', `Offline map for ${field.name} has been downloaded successfully.`);
    } catch (error) {
      console.error('Error completing download:', error);
      setIsDownloading({ ...isDownloading, [fieldId]: false });
      Alert.alert('Error', 'Failed to complete download. Please try again.');
    }
  };

  const removeOfflineMap = async (field: Field) => {
    const fieldId = field.id;
    
    try {
      // Get the size of the map being removed
      const size = offlineStatus[fieldId]?.size || 0;
      
      // Update offline status
      const updatedStatus = { ...offlineStatus };
      delete updatedStatus[fieldId];
      setOfflineStatus(updatedStatus);
      await AsyncStorage.setItem('offlineMapStatus', JSON.stringify(updatedStatus));
      
      // Update total storage
      const newTotalStorage = totalStorage - size;
      setTotalStorage(newTotalStorage);
      setAvailableStorage(availableStorage + size);
      await AsyncStorage.setItem('offlineMapTotalSize', JSON.stringify(newTotalStorage));
      
      Alert.alert('Map Removed', `Offline map for ${field.name} has been removed.`);
    } catch (error) {
      console.error('Error removing offline map:', error);
      Alert.alert('Error', 'Failed to remove offline map. Please try again.');
    }
  };

  const formatSize = (bytes: number): string => {
    if (bytes < 1024) {
      return `${bytes} B`;
    } else if (bytes < 1024 * 1024) {
      return `${(bytes / 1024).toFixed(1)} KB`;
    } else if (bytes < 1024 * 1024 * 1024) {
      return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
    } else {
      return `${(bytes / (1024 * 1024 * 1024)).toFixed(1)} GB`;
    }
  };

  const formatDate = (timestamp: number): string => {
    return new Date(timestamp).toLocaleDateString(undefined, {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const renderProgressBar = (progress: number) => {
    return Platform.OS === 'android' ? (
      <ProgressBarAndroid
        styleAttr="Horizontal"
        indeterminate={false}
        progress={progress}
        color="#22c55e"
        style={styles.progressBar}
      />
    ) : (
      <ProgressViewIOS
        progress={progress}
        progressTintColor="#22c55e"
        style={styles.progressBar}
      />
    );
  };

  const renderItem = ({ item }: { item: Field }) => {
    const fieldId = item.id;
    const isMapDownloaded = offlineStatus[fieldId]?.downloaded || false;
    const lastUpdated = offlineStatus[fieldId]?.lastUpdated;
    const mapSize = offlineStatus[fieldId]?.size || 0;
    const isFieldDownloading = isDownloading[fieldId] || false;
    const progress = downloadProgress[fieldId] || 0;

    return (
      <View style={styles.fieldItem}>
        <View style={styles.fieldInfo}>
          <Text style={styles.fieldName}>{item.name}</Text>
          <Text style={styles.fieldDetails}>
            {item.acres} acres {item.cropType ? `• ${item.cropType}` : ''}
          </Text>
          {isMapDownloaded && lastUpdated && (
            <Text style={styles.lastUpdated}>
              Last updated: {formatDate(lastUpdated)}
            </Text>
          )}
          {isMapDownloaded && (
            <Text style={styles.mapSize}>Size: {formatSize(mapSize)}</Text>
          )}
        </View>
        
        <View style={styles.downloadControls}>
          {isFieldDownloading ? (
            <View style={styles.downloadingContainer}>
              {renderProgressBar(progress)}
              <Text style={styles.downloadingText}>{Math.round(progress * 100)}%</Text>
            </View>
          ) : (
            <Switch
              value={isMapDownloaded}
              onValueChange={() => toggleOfflineMap(item)}
              trackColor={{ false: '#d1d5db', true: '#86efac' }}
              thumbColor={isMapDownloaded ? '#22c55e' : '#f3f4f6'}
              disabled={!isConnected && !isMapDownloaded}
            />
          )}
        </View>
      </View>
    );
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#22c55e" />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Offline Field Mapping</Text>
        <View style={styles.connectionStatus}>
          <View
            style={[
              styles.connectionIndicator,
              { backgroundColor: isConnected ? '#22c55e' : '#ef4444' },
            ]}
          />
          <Text style={styles.connectionText}>
            {isConnected ? 'Online' : 'Offline'}
          </Text>
        </View>
      </View>

      <View style={styles.storageInfo}>
        <Text style={styles.storageTitle}>Storage Usage</Text>
        <View style={styles.storageBarContainer}>
          <View
            style={[
              styles.storageBar,
              { width: `${Math.min(100, (totalStorage / (totalStorage + availableStorage)) * 100)}%` },
            ]}
          />
        </View>
        <View style={styles.storageDetails}>
          <Text style={styles.storageText}>
            {formatSize(totalStorage)} used
          </Text>
          <Text style={styles.storageText}>
            {formatSize(availableStorage)} available
          </Text>
        </View>
      </View>

      <View style={styles.listContainer}>
        <Text style={styles.sectionTitle}>Available Fields</Text>
        {fields.length > 0 ? (
          <FlatList
            data={fields}
            renderItem={renderItem}
            keyExtractor={(item) => item.id}
            contentContainerStyle={styles.fieldList}
          />
        ) : (
          <View style={styles.emptyContainer}>
            <Ionicons name="map-outline" size={48} color="#d1d5db" />
            <Text style={styles.emptyText}>No fields available</Text>
            <Text style={styles.emptySubText}>
              Add fields to your farm to download them for offline use
            </Text>
          </View>
        )}
      </View>

      <View style={styles.infoContainer}>
        <Ionicons name="information-circle-outline" size={20} color="#6b7280" />
        <Text style={styles.infoText}>
          Downloaded maps allow you to view and work with field boundaries even when you don't have an internet connection.
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9fafb',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    backgroundColor: '#22c55e',
    padding: 20,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: 'white',
  },
  connectionStatus: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: 10,
    paddingVertical: 5,
    borderRadius: 15,
  },
  connectionIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 5,
  },
  connectionText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '500',
  },
  storageInfo: {
    backgroundColor: 'white',
    margin: 15,
    padding: 15,
    borderRadius: 10,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
  },
  storageTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#111827',
    marginBottom: 10,
  },
  storageBarContainer: {
    height: 8,
    backgroundColor: '#f3f4f6',
    borderRadius: 4,
    overflow: 'hidden',
    marginBottom: 5,
  },
  storageBar: {
    height: '100%',
    backgroundColor: '#22c55e',
  },
  storageDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  storageText: {
    fontSize: 12,
    color: '#6b7280',
  },
  listContainer: {
    flex: 1,
    backgroundColor: 'white',
    margin: 15,
    marginTop: 0,
    borderRadius: 10,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    overflow: 'hidden',
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#111827',
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  fieldList: {
    paddingBottom: 15,
  },
  fieldItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  fieldInfo: {
    flex: 1,
  },
  fieldName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#111827',
    marginBottom: 2,
  },
  fieldDetails: {
    fontSize: 14,
    color: '#6b7280',
    marginBottom: 2,
  },
  lastUpdated: {
    fontSize: 12,
    color: '#6b7280',
  },
  mapSize: {
    fontSize: 12,
    color: '#6b7280',
  },
  downloadControls: {
    width: 100,
    alignItems: 'flex-end',
  },
  downloadingContainer: {
    width: '100%',
    alignItems: 'center',
  },
  progressBar: {
    width: '100%',
    marginBottom: 5,
  },
  downloadingText: {
    fontSize: 12,
    color: '#6b7280',
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 40,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#9ca3af',
    marginTop: 10,
  },
  emptySubText: {
    fontSize: 14,
    color: '#9ca3af',
    marginTop: 5,
    textAlign: 'center',
  },
  infoContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: 'rgba(59, 130, 246, 0.1)',
    margin: 15,
    marginTop: 0,
    padding: 15,
    borderRadius: 10,
  },
  infoText: {
    fontSize: 12,
    color: '#6b7280',
    flex: 1,
    marginLeft: 10,
  },
});

export default OfflineFieldMappingScreen;