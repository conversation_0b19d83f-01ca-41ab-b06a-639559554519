import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  ActivityIndicator,
  Image,
  Platform,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../../../../shared/store/AuthProvider';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { MainStackParamList } from '../navigation/MainNavigator';
import { fieldService } from '../../../../shared/services/fieldService';
import * as Location from 'expo-location';
import * as ImagePicker from 'expo-image-picker';
import MapView, { Marker, PROVIDER_GOOGLE } from 'react-native-maps';
import AsyncStorage from '@react-native-async-storage/async-storage';

type CropScoutingScreenProps = NativeStackScreenProps<MainStackParamList, 'CropScouting'>;

type Field = {
  id: string;
  name: string;
  cropType?: string;
  latitude: number;
  longitude: number;
};

type ScoutingObservation = {
  id: string;
  fieldId: string;
  fieldName: string;
  cropType?: string;
  observationType: string;
  severity: 'low' | 'medium' | 'high';
  notes: string;
  images: string[];
  latitude: number;
  longitude: number;
  timestamp: number;
  synced: boolean;
};

const CropScoutingScreen: React.FC<CropScoutingScreenProps> = ({ route, navigation }) => {
  const { fieldId } = route.params;
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [field, setField] = useState<Field | null>(null);
  const [observationType, setObservationType] = useState<string>('');
  const [severity, setSeverity] = useState<'low' | 'medium' | 'high'>('medium');
  const [notes, setNotes] = useState<string>('');
  const [images, setImages] = useState<string[]>([]);
  const [currentLocation, setCurrentLocation] = useState<{
    latitude: number;
    longitude: number;
  } | null>(null);
  const [mapReady, setMapReady] = useState(false);
  const [locationPermission, setLocationPermission] = useState(false);

  // Observation types
  const observationTypes = [
    'Pest',
    'Disease',
    'Weed',
    'Nutrient Deficiency',
    'Water Stress',
    'Growth Stage',
    'Damage',
    'Other',
  ];

  useEffect(() => {
    checkPermissions();
    loadFieldData();
    getCurrentLocation();
  }, []);

  const checkPermissions = async () => {
    // Check camera permissions
    if (Platform.OS !== 'web') {
      const { status: cameraStatus } = await ImagePicker.requestCameraPermissionsAsync();
      if (cameraStatus !== 'granted') {
        Alert.alert(
          'Camera Permission',
          'Camera permission is needed to take photos for scouting observations.',
          [{ text: 'OK' }]
        );
      }
    }

    // Check location permissions
    const { status: locationStatus } = await Location.requestForegroundPermissionsAsync();
    if (locationStatus !== 'granted') {
      Alert.alert(
        'Location Permission',
        'Location permission is needed to record the position of your observations.',
        [{ text: 'OK' }]
      );
      return;
    }

    setLocationPermission(true);
  };

  const loadFieldData = async () => {
    try {
      setLoading(true);
      const fieldData = await fieldService.getFieldById(fieldId);
      setField(fieldData);
      setLoading(false);
    } catch (error) {
      console.error('Error loading field details:', error);
      setLoading(false);
      Alert.alert('Error', 'Failed to load field details. Please try again.');
    }
  };

  const getCurrentLocation = async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        return;
      }

      const location = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.Highest,
      });

      setCurrentLocation({
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
      });
    } catch (error) {
      console.error('Error getting current location:', error);
    }
  };

  const takePhoto = async () => {
    try {
      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        setImages([...images, result.assets[0].uri]);
      }
    } catch (error) {
      console.error('Error taking photo:', error);
      Alert.alert('Error', 'Failed to take photo. Please try again.');
    }
  };

  const pickImage = async () => {
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        setImages([...images, result.assets[0].uri]);
      }
    } catch (error) {
      console.error('Error picking image:', error);
      Alert.alert('Error', 'Failed to pick image. Please try again.');
    }
  };

  const removeImage = (index: number) => {
    const newImages = [...images];
    newImages.splice(index, 1);
    setImages(newImages);
  };

  const saveObservation = async () => {
    if (!observationType) {
      Alert.alert('Observation Type Required', 'Please select an observation type.');
      return;
    }

    if (!currentLocation) {
      Alert.alert('Location Required', 'Unable to determine your current location. Please try again.');
      return;
    }

    try {
      const newObservation: ScoutingObservation = {
        id: Date.now().toString(),
        fieldId: field?.id || '',
        fieldName: field?.name || '',
        cropType: field?.cropType,
        observationType,
        severity,
        notes,
        images,
        latitude: currentLocation.latitude,
        longitude: currentLocation.longitude,
        timestamp: Date.now(),
        synced: false,
      };

      // Save to local storage
      const observationsJson = await AsyncStorage.getItem('scoutingObservations');
      const observations: ScoutingObservation[] = observationsJson
        ? JSON.parse(observationsJson)
        : [];
      observations.push(newObservation);
      await AsyncStorage.setItem('scoutingObservations', JSON.stringify(observations));

      // Show success message
      Alert.alert(
        'Observation Saved',
        'Your scouting observation has been saved successfully.',
        [
          {
            text: 'View Observations',
            onPress: () => navigation.navigate('ScoutingObservations', { fieldId }),
          },
          {
            text: 'Add Another',
            onPress: () => {
              setObservationType('');
              setSeverity('medium');
              setNotes('');
              setImages([]);
            },
          },
          {
            text: 'Done',
            onPress: () => navigation.goBack(),
          },
        ]
      );
    } catch (error) {
      console.error('Error saving observation:', error);
      Alert.alert('Error', 'Failed to save observation. Please try again.');
    }
  };

  const getMapRegion = () => {
    if (currentLocation) {
      return {
        latitude: currentLocation.latitude,
        longitude: currentLocation.longitude,
        latitudeDelta: 0.005,
        longitudeDelta: 0.005,
      };
    } else if (field) {
      return {
        latitude: field.latitude,
        longitude: field.longitude,
        latitudeDelta: 0.05,
        longitudeDelta: 0.05,
      };
    }
    return null;
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#22c55e" />
      </View>
    );
  }

  if (!field) {
    return (
      <View style={styles.errorContainer}>
        <Ionicons name="alert-circle-outline" size={48} color="#ef4444" />
        <Text style={styles.errorText}>Field not found</Text>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Text style={styles.backButtonText}>Go Back</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Crop Scouting</Text>
        <Text style={styles.fieldName}>{field.name}</Text>
        {field.cropType && <Text style={styles.cropType}>Crop: {field.cropType}</Text>}
      </View>

      <View style={styles.mapContainer}>
        {!mapReady && (
          <View style={styles.mapLoadingOverlay}>
            <ActivityIndicator size="large" color="#22c55e" />
          </View>
        )}
        <MapView
          style={styles.map}
          provider={PROVIDER_GOOGLE}
          initialRegion={getMapRegion() || undefined}
          onMapReady={() => setMapReady(true)}
          showsUserLocation
        >
          {currentLocation && (
            <Marker
              coordinate={{
                latitude: currentLocation.latitude,
                longitude: currentLocation.longitude,
              }}
              title="Current Location"
            >
              <View style={styles.currentLocationMarker}>
                <Ionicons name="location" size={20} color="white" />
              </View>
            </Marker>
          )}
        </MapView>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Observation Type</Text>
        <View style={styles.observationTypeContainer}>
          {observationTypes.map((type) => (
            <TouchableOpacity
              key={type}
              style={[
                styles.observationTypeButton,
                observationType === type && styles.observationTypeButtonActive,
              ]}
              onPress={() => setObservationType(type)}
            >
              <Text
                style={[
                  styles.observationTypeButtonText,
                  observationType === type && styles.observationTypeButtonTextActive,
                ]}
              >
                {type}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Severity</Text>
        <View style={styles.severityContainer}>
          <TouchableOpacity
            style={[
              styles.severityButton,
              severity === 'low' && styles.severityButtonLowActive,
            ]}
            onPress={() => setSeverity('low')}
          >
            <Text
              style={[
                styles.severityButtonText,
                severity === 'low' && styles.severityButtonTextActive,
              ]}
            >
              Low
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[
              styles.severityButton,
              severity === 'medium' && styles.severityButtonMediumActive,
            ]}
            onPress={() => setSeverity('medium')}
          >
            <Text
              style={[
                styles.severityButtonText,
                severity === 'medium' && styles.severityButtonTextActive,
              ]}
            >
              Medium
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[
              styles.severityButton,
              severity === 'high' && styles.severityButtonHighActive,
            ]}
            onPress={() => setSeverity('high')}
          >
            <Text
              style={[
                styles.severityButtonText,
                severity === 'high' && styles.severityButtonTextActive,
              ]}
            >
              High
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Notes</Text>
        <TextInput
          style={styles.notesInput}
          placeholder="Enter detailed observations..."
          multiline
          numberOfLines={4}
          value={notes}
          onChangeText={setNotes}
        />
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Photos</Text>
        <View style={styles.photoButtons}>
          <TouchableOpacity style={styles.photoButton} onPress={takePhoto}>
            <Ionicons name="camera-outline" size={24} color="white" />
            <Text style={styles.photoButtonText}>Take Photo</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.photoButton} onPress={pickImage}>
            <Ionicons name="image-outline" size={24} color="white" />
            <Text style={styles.photoButtonText}>Choose Photo</Text>
          </TouchableOpacity>
        </View>

        {images.length > 0 && (
          <View style={styles.imagesContainer}>
            {images.map((image, index) => (
              <View key={index} style={styles.imageWrapper}>
                <Image source={{ uri: image }} style={styles.image} />
                <TouchableOpacity
                  style={styles.removeImageButton}
                  onPress={() => removeImage(index)}
                >
                  <Ionicons name="close-circle" size={24} color="#ef4444" />
                </TouchableOpacity>
              </View>
            ))}
          </View>
        )}
      </View>

      <TouchableOpacity style={styles.saveButton} onPress={saveObservation}>
        <Ionicons name="save-outline" size={24} color="white" />
        <Text style={styles.saveButtonText}>Save Observation</Text>
      </TouchableOpacity>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9fafb',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 18,
    color: '#4b5563',
    marginTop: 10,
    marginBottom: 20,
  },
  backButton: {
    backgroundColor: '#22c55e',
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 5,
  },
  backButtonText: {
    color: 'white',
    fontWeight: 'bold',
  },
  header: {
    backgroundColor: '#22c55e',
    padding: 20,
    alignItems: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 5,
  },
  fieldName: {
    fontSize: 18,
    color: 'white',
    marginBottom: 5,
  },
  cropType: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.8)',
  },
  mapContainer: {
    height: 200,
    margin: 15,
    marginTop: -20,
    borderRadius: 10,
    overflow: 'hidden',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  mapLoadingOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(255, 255, 255, 0.7)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1,
  },
  map: {
    ...StyleSheet.absoluteFillObject,
  },
  currentLocationMarker: {
    backgroundColor: '#22c55e',
    borderRadius: 20,
    padding: 5,
    borderWidth: 2,
    borderColor: 'white',
  },
  section: {
    backgroundColor: 'white',
    margin: 15,
    marginTop: 5,
    borderRadius: 10,
    padding: 15,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#111827',
    marginBottom: 15,
  },
  observationTypeContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  observationTypeButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 20,
    marginRight: 8,
    marginBottom: 8,
    backgroundColor: '#f3f4f6',
  },
  observationTypeButtonActive: {
    backgroundColor: '#22c55e',
  },
  observationTypeButtonText: {
    color: '#4b5563',
    fontWeight: '500',
  },
  observationTypeButtonTextActive: {
    color: 'white',
  },
  severityContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  severityButton: {
    flex: 1,
    paddingVertical: 10,
    alignItems: 'center',
    borderRadius: 5,
    marginHorizontal: 5,
    backgroundColor: '#f3f4f6',
  },
  severityButtonLowActive: {
    backgroundColor: '#22c55e',
  },
  severityButtonMediumActive: {
    backgroundColor: '#f59e0b',
  },
  severityButtonHighActive: {
    backgroundColor: '#ef4444',
  },
  severityButtonText: {
    fontWeight: 'bold',
    color: '#4b5563',
  },
  severityButtonTextActive: {
    color: 'white',
  },
  notesInput: {
    borderWidth: 1,
    borderColor: '#d1d5db',
    borderRadius: 5,
    padding: 10,
    minHeight: 100,
    textAlignVertical: 'top',
  },
  photoButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 15,
  },
  photoButton: {
    backgroundColor: '#3b82f6',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 10,
    borderRadius: 5,
    flex: 1,
    marginHorizontal: 5,
  },
  photoButtonText: {
    color: 'white',
    fontWeight: 'bold',
    marginLeft: 5,
  },
  imagesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  imageWrapper: {
    width: '48%',
    aspectRatio: 1,
    margin: '1%',
    position: 'relative',
  },
  image: {
    width: '100%',
    height: '100%',
    borderRadius: 5,
  },
  removeImageButton: {
    position: 'absolute',
    top: -10,
    right: -10,
    backgroundColor: 'white',
    borderRadius: 15,
  },
  saveButton: {
    backgroundColor: '#22c55e',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 15,
    borderRadius: 10,
    margin: 15,
    marginTop: 5,
    marginBottom: 30,
  },
  saveButtonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 18,
    marginLeft: 10,
  },
});

export default CropScoutingScreen;