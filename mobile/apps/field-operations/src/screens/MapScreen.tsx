import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  Modal,
  TextInput,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../../../../shared/store/AuthProvider';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { MainTabParamList } from '../navigation/MainNavigator';
import { CompositeScreenProps } from '@react-navigation/native';
import { BottomTabScreenProps } from '@react-navigation/bottom-tabs';
import { MainStackParamList } from '../navigation/MainNavigator';
import { fieldService } from '../../../../shared/services/fieldService';
import MapView, { Marker, Polygon, PROVIDER_GOOGLE } from 'react-native-maps';
import * as Location from 'expo-location';

type MapScreenProps = CompositeScreenProps<
  BottomTabScreenProps<MainTabParamList, 'Map'>,
  NativeStackScreenProps<MainStackParamList>
>;

const MapScreen: React.FC<MapScreenProps> = ({ navigation }) => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [fields, setFields] = useState<any[]>([]);
  const [region, setRegion] = useState({
    latitude: 37.78825,
    longitude: -122.4324,
    latitudeDelta: 0.0922,
    longitudeDelta: 0.0421,
  });
  const [isDrawing, setIsDrawing] = useState(false);
  const [drawingPoints, setDrawingPoints] = useState<any[]>([]);
  const [showSaveModal, setShowSaveModal] = useState(false);
  const [newFieldName, setNewFieldName] = useState('');
  const [newFieldCrop, setNewFieldCrop] = useState('');
  const mapRef = useRef<MapView>(null);

  useEffect(() => {
    const fetchFieldsAndLocation = async () => {
      try {
        setLoading(true);

        // Request location permissions
        const { status } = await Location.requestForegroundPermissionsAsync();
        if (status !== 'granted') {
          Alert.alert('Permission Denied', 'Location permission is required for this feature.');
          setLoading(false);
          return;
        }

        // Get current location
        const location = await Location.getCurrentPositionAsync({});
        const { latitude, longitude } = location.coords;
        
        setRegion({
          latitude,
          longitude,
          latitudeDelta: 0.05,
          longitudeDelta: 0.05,
        });

        // Fetch fields
        if (user?.farmId) {
          const fieldsData = await fieldService.getFields(user.farmId);
          setFields(fieldsData);
        }

        setLoading(false);
      } catch (error) {
        console.error('Error fetching data:', error);
        Alert.alert('Error', 'Could not load map data. Please try again later.');
        setLoading(false);
      }
    };

    fetchFieldsAndLocation();
  }, [user?.farmId]);

  const handleMapPress = (event: any) => {
    if (!isDrawing) return;

    const { coordinate } = event.nativeEvent;
    setDrawingPoints([...drawingPoints, coordinate]);
  };

  const startDrawing = () => {
    setIsDrawing(true);
    setDrawingPoints([]);
    Alert.alert(
      'Drawing Mode',
      'Tap on the map to add points to your field boundary. Tap "Save Field" when finished.'
    );
  };

  const cancelDrawing = () => {
    setIsDrawing(false);
    setDrawingPoints([]);
  };

  const saveField = async () => {
    if (drawingPoints.length < 3) {
      Alert.alert('Error', 'A field must have at least 3 points to form a boundary.');
      return;
    }

    if (!newFieldName.trim()) {
      Alert.alert('Error', 'Please enter a field name.');
      return;
    }

    try {
      const newField = {
        name: newFieldName,
        crop: newFieldCrop,
        boundary: drawingPoints,
        farmId: user?.farmId,
      };

      await fieldService.createField(newField);
      
      // Refresh fields
      const fieldsData = await fieldService.getFields(user?.farmId || '');
      setFields(fieldsData);
      
      // Reset drawing state
      setIsDrawing(false);
      setDrawingPoints([]);
      setShowSaveModal(false);
      setNewFieldName('');
      setNewFieldCrop('');
      
      Alert.alert('Success', 'Field created successfully!');
    } catch (error) {
      console.error('Error saving field:', error);
      Alert.alert('Error', 'Could not save field. Please try again later.');
    }
  };

  const finishDrawing = () => {
    if (drawingPoints.length < 3) {
      Alert.alert('Error', 'A field must have at least 3 points to form a boundary.');
      return;
    }
    setShowSaveModal(true);
  };

  const renderFieldPolygons = () => {
    return fields.map((field) => (
      <Polygon
        key={field.id}
        coordinates={field.boundary}
        strokeColor="#22c55e"
        fillColor="rgba(34, 197, 94, 0.2)"
        strokeWidth={2}
        onPress={() => navigation.navigate('FieldDetail', { fieldId: field.id })}
      />
    ));
  };

  return (
    <View style={styles.container}>
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#22c55e" />
        </View>
      ) : (
        <>
          <MapView
            ref={mapRef}
            style={styles.map}
            provider={PROVIDER_GOOGLE}
            initialRegion={region}
            onPress={handleMapPress}
            showsUserLocation
            showsMyLocationButton
          >
            {renderFieldPolygons()}
            
            {drawingPoints.length > 0 && (
              <Polygon
                coordinates={drawingPoints}
                strokeColor="#ff6b6b"
                fillColor="rgba(255, 107, 107, 0.2)"
                strokeWidth={2}
              />
            )}
            
            {drawingPoints.map((point, index) => (
              <Marker
                key={`point-${index}`}
                coordinate={point}
                pinColor="#ff6b6b"
                anchor={{ x: 0.5, y: 0.5 }}
              >
                <View style={styles.drawingPoint} />
              </Marker>
            ))}
          </MapView>

          <View style={styles.mapControls}>
            {!isDrawing ? (
              <TouchableOpacity style={styles.mapButton} onPress={startDrawing}>
                <Ionicons name="create-outline" size={24} color="white" />
                <Text style={styles.mapButtonText}>Draw Field</Text>
              </TouchableOpacity>
            ) : (
              <View style={styles.drawingControls}>
                <TouchableOpacity style={styles.drawingButton} onPress={cancelDrawing}>
                  <Ionicons name="close-outline" size={24} color="white" />
                  <Text style={styles.drawingButtonText}>Cancel</Text>
                </TouchableOpacity>
                
                <TouchableOpacity 
                  style={[styles.drawingButton, { backgroundColor: '#22c55e' }]} 
                  onPress={finishDrawing}
                >
                  <Ionicons name="checkmark-outline" size={24} color="white" />
                  <Text style={styles.drawingButtonText}>Save Field</Text>
                </TouchableOpacity>
              </View>
            )}
          </View>

          <Modal
            visible={showSaveModal}
            transparent
            animationType="slide"
            onRequestClose={() => setShowSaveModal(false)}
          >
            <View style={styles.modalContainer}>
              <View style={styles.modalContent}>
                <Text style={styles.modalTitle}>Save New Field</Text>
                
                <Text style={styles.inputLabel}>Field Name (required)</Text>
                <TextInput
                  style={styles.input}
                  value={newFieldName}
                  onChangeText={setNewFieldName}
                  placeholder="Enter field name"
                />
                
                <Text style={styles.inputLabel}>Current Crop (optional)</Text>
                <TextInput
                  style={styles.input}
                  value={newFieldCrop}
                  onChangeText={setNewFieldCrop}
                  placeholder="Enter current crop"
                />
                
                <View style={styles.modalButtons}>
                  <TouchableOpacity 
                    style={[styles.modalButton, styles.cancelButton]} 
                    onPress={() => setShowSaveModal(false)}
                  >
                    <Text style={styles.cancelButtonText}>Cancel</Text>
                  </TouchableOpacity>
                  
                  <TouchableOpacity 
                    style={[styles.modalButton, styles.saveButton]} 
                    onPress={saveField}
                  >
                    <Text style={styles.saveButtonText}>Save Field</Text>
                  </TouchableOpacity>
                </View>
              </View>
            </View>
          </Modal>
        </>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  map: {
    flex: 1,
  },
  mapControls: {
    position: 'absolute',
    bottom: 20,
    left: 0,
    right: 0,
    alignItems: 'center',
  },
  mapButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#22c55e',
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 30,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
  },
  mapButtonText: {
    color: 'white',
    fontWeight: 'bold',
    marginLeft: 5,
  },
  drawingControls: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '80%',
  },
  drawingButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#6b7280',
    paddingVertical: 10,
    paddingHorizontal: 15,
    borderRadius: 30,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
  },
  drawingButtonText: {
    color: 'white',
    fontWeight: 'bold',
    marginLeft: 5,
  },
  drawingPoint: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: '#ff6b6b',
    borderWidth: 2,
    borderColor: 'white',
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    width: '80%',
    backgroundColor: 'white',
    borderRadius: 10,
    padding: 20,
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 15,
    textAlign: 'center',
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  input: {
    borderWidth: 1,
    borderColor: '#d1d5db',
    borderRadius: 5,
    padding: 10,
    marginBottom: 15,
    fontSize: 16,
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 10,
  },
  modalButton: {
    paddingVertical: 10,
    paddingHorizontal: 15,
    borderRadius: 5,
    flex: 1,
    alignItems: 'center',
  },
  cancelButton: {
    backgroundColor: '#f3f4f6',
    marginRight: 10,
  },
  cancelButtonText: {
    color: '#6b7280',
    fontWeight: 'bold',
  },
  saveButton: {
    backgroundColor: '#22c55e',
  },
  saveButtonText: {
    color: 'white',
    fontWeight: 'bold',
  },
});

export default MapScreen;