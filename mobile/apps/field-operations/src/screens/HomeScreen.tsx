import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../../../../shared/store/AuthProvider';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { MainTabParamList } from '../navigation/MainNavigator';
import { CompositeScreenProps } from '@react-navigation/native';
import { BottomTabScreenProps } from '@react-navigation/bottom-tabs';
import { MainStackParamList } from '../navigation/MainNavigator';
import { fieldService } from '../../../../shared/services/fieldService';
import { taskService } from '../../../../shared/services/taskService';
import { weatherService } from '../../../../shared/services/weatherService';

type HomeScreenProps = CompositeScreenProps<
  BottomTabScreenProps<MainTabParamList, 'Home'>,
  NativeStackScreenProps<MainStackParamList>
>;

const HomeScreen: React.FC<HomeScreenProps> = ({ navigation }) => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [fieldCount, setFieldCount] = useState(0);
  const [activeTaskCount, setActiveTaskCount] = useState(0);
  const [weatherData, setWeatherData] = useState<any>(null);
  const [recentActivity, setRecentActivity] = useState<any[]>([]);

  const loadData = async () => {
    try {
      setLoading(true);

      if (!user?.farmId) {
        console.error('No farm ID available');
        setLoading(false);
        return;
      }

      // Load field count
      const fields = await fieldService.getFields(user.farmId);
      setFieldCount(fields.length);

      // Load active tasks
      const tasks = await taskService.getTasks(user.farmId, { status: 'active' });
      setActiveTaskCount(tasks.length);

      // Load weather data
      if (fields.length > 0 && fields[0].latitude && fields[0].longitude) {
        const weather = await weatherService.getWeather(fields[0].latitude, fields[0].longitude);
        setWeatherData(weather);
      }

      // Load recent activity
      const activity = await fieldService.getRecentActivity(user.farmId);
      setRecentActivity(activity);

      setLoading(false);
    } catch (error) {
      console.error('Error loading dashboard data:', error);
      setLoading(false);
    }
  };

  useEffect(() => {
    loadData();
  }, [user?.farmId]);

  const onRefresh = async () => {
    setRefreshing(true);
    await loadData();
    setRefreshing(false);
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#22c55e" />
      </View>
    );
  }

  return (
    <ScrollView
      style={styles.container}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={onRefresh} colors={['#22c55e']} />
      }
    >
      <View style={styles.header}>
        <Text style={styles.greeting}>Hello, {user?.name || 'Farmer'}</Text>
        <Text style={styles.subGreeting}>Here's your field operations overview</Text>
      </View>

      <View style={styles.quickStats}>
        <TouchableOpacity
          style={styles.statCard}
          onPress={() => navigation.navigate('Fields')}
        >
          <Ionicons name="grid" size={24} color="#22c55e" />
          <Text style={styles.statNumber}>{fieldCount}</Text>
          <Text style={styles.statLabel}>Fields</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.statCard}
          onPress={() => navigation.navigate('Tasks')}
        >
          <Ionicons name="list" size={24} color="#22c55e" />
          <Text style={styles.statNumber}>{activeTaskCount}</Text>
          <Text style={styles.statLabel}>Active Tasks</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.statCard}
          onPress={() => navigation.navigate('Weather')}
        >
          <Ionicons name="cloud" size={24} color="#22c55e" />
          <Text style={styles.statNumber}>
            {weatherData ? `${Math.round(weatherData.temperature)}°` : '--'}
          </Text>
          <Text style={styles.statLabel}>Weather</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Quick Actions</Text>
        </View>
        <View style={styles.actionButtons}>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => navigation.navigate('Recording')}
          >
            <Ionicons name="navigate-circle-outline" size={24} color="#22c55e" />
            <Text style={styles.actionButtonText}>Record GPS</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => navigation.navigate('Map')}
          >
            <Ionicons name="map-outline" size={24} color="#22c55e" />
            <Text style={styles.actionButtonText}>View Map</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => navigation.navigate('OfflineFieldMapping')}
          >
            <Ionicons name="download-outline" size={24} color="#22c55e" />
            <Text style={styles.actionButtonText}>Offline Maps</Text>
          </TouchableOpacity>
        </View>
      </View>

      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Recent Activity</Text>
        </View>
        {recentActivity.length > 0 ? (
          recentActivity.map((activity, index) => (
            <View key={index} style={styles.activityItem}>
              <Ionicons
                name={
                  activity.type === 'field_update'
                    ? 'create-outline'
                    : activity.type === 'task_completed'
                    ? 'checkmark-circle-outline'
                    : 'time-outline'
                }
                size={20}
                color="#22c55e"
              />
              <View style={styles.activityContent}>
                <Text style={styles.activityText}>{activity.description}</Text>
                <Text style={styles.activityTime}>{activity.time}</Text>
              </View>
            </View>
          ))
        ) : (
          <Text style={styles.emptyText}>No recent activity</Text>
        )}
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9fafb',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    padding: 20,
    backgroundColor: '#22c55e',
  },
  greeting: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
  },
  subGreeting: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.8)',
    marginTop: 5,
  },
  quickStats: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 15,
    backgroundColor: 'white',
    borderRadius: 10,
    margin: 15,
    marginTop: -20,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  statCard: {
    alignItems: 'center',
    flex: 1,
  },
  statNumber: {
    fontSize: 20,
    fontWeight: 'bold',
    marginTop: 5,
  },
  statLabel: {
    fontSize: 14,
    color: '#6b7280',
  },
  section: {
    backgroundColor: 'white',
    borderRadius: 10,
    margin: 15,
    marginTop: 5,
    padding: 15,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  actionButton: {
    alignItems: 'center',
    padding: 10,
    flex: 1,
  },
  actionButtonText: {
    marginTop: 5,
    fontSize: 14,
    textAlign: 'center',
  },
  activityItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    paddingVertical: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  activityContent: {
    marginLeft: 10,
    flex: 1,
  },
  activityText: {
    fontSize: 14,
  },
  activityTime: {
    fontSize: 12,
    color: '#6b7280',
    marginTop: 2,
  },
  emptyText: {
    textAlign: 'center',
    color: '#6b7280',
    padding: 10,
  },
});

export default HomeScreen;