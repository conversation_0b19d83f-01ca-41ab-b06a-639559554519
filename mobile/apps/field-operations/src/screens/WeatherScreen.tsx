import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  ActivityIndicator,
  RefreshControl,
  TouchableOpacity,
  Image,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../../../../shared/store/AuthProvider';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { MainTabParamList } from '../navigation/MainNavigator';
import { CompositeScreenProps } from '@react-navigation/native';
import { BottomTabScreenProps } from '@react-navigation/bottom-tabs';
import { MainStackParamList } from '../navigation/MainNavigator';
import { weatherService } from '../../../../shared/services/weatherService';
import { fieldService } from '../../../../shared/services/fieldService';

type WeatherScreenProps = CompositeScreenProps<
  BottomTabScreenProps<MainTabParamList, 'Weather'>,
  NativeStackScreenProps<MainStackParamList>
>;

type Field = {
  id: string;
  name: string;
  latitude: number;
  longitude: number;
};

type WeatherData = {
  temperature: number;
  feelsLike: number;
  humidity: number;
  windSpeed: number;
  windDirection: string;
  pressure: number;
  description: string;
  icon: string;
  precipitation: number;
  forecast: Array<{
    date: string;
    temperature: {
      min: number;
      max: number;
    };
    description: string;
    icon: string;
    precipitation: number;
  }>;
};

const WeatherScreen: React.FC<WeatherScreenProps> = ({ navigation }) => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [fields, setFields] = useState<Field[]>([]);
  const [selectedField, setSelectedField] = useState<Field | null>(null);
  const [weatherData, setWeatherData] = useState<WeatherData | null>(null);

  const loadFields = async () => {
    try {
      if (!user?.farmId) {
        console.error('No farm ID available');
        return;
      }

      const fetchedFields = await fieldService.getFields(user.farmId);
      setFields(fetchedFields);

      if (fetchedFields.length > 0) {
        setSelectedField(fetchedFields[0]);
      }
    } catch (error) {
      console.error('Error loading fields:', error);
    }
  };

  const loadWeatherData = async () => {
    try {
      setLoading(true);

      if (!selectedField) {
        setLoading(false);
        return;
      }

      const weather = await weatherService.getWeather(
        selectedField.latitude,
        selectedField.longitude
      );
      setWeatherData(weather);

      setLoading(false);
    } catch (error) {
      console.error('Error loading weather data:', error);
      setLoading(false);
    }
  };

  useEffect(() => {
    loadFields();
  }, [user?.farmId]);

  useEffect(() => {
    if (selectedField) {
      loadWeatherData();
    }
  }, [selectedField]);

  const onRefresh = async () => {
    setRefreshing(true);
    await loadWeatherData();
    setRefreshing(false);
  };

  const getWindDirectionIcon = (direction: string) => {
    const directions: { [key: string]: string } = {
      N: 'arrow-up',
      NE: 'arrow-up-forward',
      E: 'arrow-forward',
      SE: 'arrow-down-forward',
      S: 'arrow-down',
      SW: 'arrow-down-back',
      W: 'arrow-back',
      NW: 'arrow-up-back',
    };

    return directions[direction] || 'arrow-up';
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { weekday: 'short', month: 'short', day: 'numeric' });
  };

  if (loading && !refreshing) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#22c55e" />
      </View>
    );
  }

  if (!selectedField) {
    return (
      <View style={styles.errorContainer}>
        <Ionicons name="alert-circle-outline" size={48} color="#ef4444" />
        <Text style={styles.errorText}>No fields available</Text>
        <Text style={styles.errorSubText}>
          Add fields to your farm to view weather information
        </Text>
      </View>
    );
  }

  return (
    <ScrollView
      style={styles.container}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={onRefresh} colors={['#22c55e']} />
      }
    >
      {fields.length > 1 && (
        <View style={styles.fieldSelector}>
          <Text style={styles.fieldSelectorLabel}>Select Field:</Text>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            {fields.map((field) => (
              <TouchableOpacity
                key={field.id}
                style={[
                  styles.fieldButton,
                  selectedField?.id === field.id && styles.fieldButtonActive,
                ]}
                onPress={() => setSelectedField(field)}
              >
                <Text
                  style={[
                    styles.fieldButtonText,
                    selectedField?.id === field.id && styles.fieldButtonTextActive,
                  ]}
                >
                  {field.name}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>
      )}

      {weatherData ? (
        <>
          <View style={styles.currentWeather}>
            <View style={styles.currentWeatherHeader}>
              <Text style={styles.locationText}>{selectedField.name}</Text>
              <Text style={styles.dateText}>{new Date().toLocaleDateString()}</Text>
            </View>

            <View style={styles.currentWeatherContent}>
              <View style={styles.temperatureContainer}>
                <Text style={styles.temperature}>{Math.round(weatherData.temperature)}°</Text>
                <Text style={styles.weatherDescription}>
                  {weatherData.description.charAt(0).toUpperCase() +
                    weatherData.description.slice(1)}
                </Text>
              </View>

              {weatherData.icon && (
                <Image
                  source={{
                    uri: `https://openweathermap.org/img/wn/${weatherData.icon}@4x.png`,
                  }}
                  style={styles.weatherIcon}
                />
              )}
            </View>

            <View style={styles.weatherDetails}>
              <View style={styles.weatherDetailItem}>
                <Ionicons name="thermometer-outline" size={20} color="#6b7280" />
                <Text style={styles.weatherDetailLabel}>Feels Like</Text>
                <Text style={styles.weatherDetailValue}>
                  {Math.round(weatherData.feelsLike)}°
                </Text>
              </View>

              <View style={styles.weatherDetailItem}>
                <Ionicons name="water-outline" size={20} color="#6b7280" />
                <Text style={styles.weatherDetailLabel}>Humidity</Text>
                <Text style={styles.weatherDetailValue}>{weatherData.humidity}%</Text>
              </View>

              <View style={styles.weatherDetailItem}>
                <Ionicons name="speedometer-outline" size={20} color="#6b7280" />
                <Text style={styles.weatherDetailLabel}>Pressure</Text>
                <Text style={styles.weatherDetailValue}>{weatherData.pressure} hPa</Text>
              </View>

              <View style={styles.weatherDetailItem}>
                <Ionicons name="rainy-outline" size={20} color="#6b7280" />
                <Text style={styles.weatherDetailLabel}>Precipitation</Text>
                <Text style={styles.weatherDetailValue}>
                  {weatherData.precipitation} mm
                </Text>
              </View>

              <View style={styles.weatherDetailItem}>
                <Ionicons name="leaf-outline" size={20} color="#6b7280" />
                <Text style={styles.weatherDetailLabel}>Wind</Text>
                <View style={styles.windInfo}>
                  <Text style={styles.weatherDetailValue}>
                    {Math.round(weatherData.windSpeed)} mph
                  </Text>
                  <Ionicons
                    name={getWindDirectionIcon(weatherData.windDirection)}
                    size={16}
                    color="#6b7280"
                    style={styles.windDirectionIcon}
                  />
                </View>
              </View>
            </View>
          </View>

          <View style={styles.forecastSection}>
            <Text style={styles.sectionTitle}>5-Day Forecast</Text>
            <View style={styles.forecastContainer}>
              {weatherData.forecast.map((day, index) => (
                <View key={index} style={styles.forecastDay}>
                  <Text style={styles.forecastDate}>{formatDate(day.date)}</Text>
                  {day.icon && (
                    <Image
                      source={{
                        uri: `https://openweathermap.org/img/wn/${day.icon}.png`,
                      }}
                      style={styles.forecastIcon}
                    />
                  )}
                  <Text style={styles.forecastTemp}>
                    {Math.round(day.temperature.max)}° / {Math.round(day.temperature.min)}°
                  </Text>
                  <Text style={styles.forecastDescription}>
                    {day.description.charAt(0).toUpperCase() + day.description.slice(1)}
                  </Text>
                  <View style={styles.forecastPrecipitation}>
                    <Ionicons name="rainy-outline" size={14} color="#6b7280" />
                    <Text style={styles.forecastPrecipitationText}>{day.precipitation} mm</Text>
                  </View>
                </View>
              ))}
            </View>
          </View>

          <View style={styles.weatherNotes}>
            <Text style={styles.weatherNotesText}>
              Weather data is updated every hour. Last updated:{' '}
              {new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
            </Text>
          </View>
        </>
      ) : (
        <View style={styles.errorContainer}>
          <Ionicons name="cloud-offline-outline" size={48} color="#ef4444" />
          <Text style={styles.errorText}>Weather data unavailable</Text>
          <Text style={styles.errorSubText}>
            Unable to load weather data for this location. Please try again later.
          </Text>
        </View>
      )}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9fafb',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  errorText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#4b5563',
    marginTop: 10,
  },
  errorSubText: {
    fontSize: 14,
    color: '#6b7280',
    textAlign: 'center',
    marginTop: 5,
  },
  fieldSelector: {
    backgroundColor: 'white',
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  fieldSelectorLabel: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#4b5563',
    marginBottom: 10,
  },
  fieldButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 20,
    marginRight: 8,
    backgroundColor: '#f3f4f6',
  },
  fieldButtonActive: {
    backgroundColor: '#22c55e',
  },
  fieldButtonText: {
    color: '#4b5563',
    fontWeight: '500',
  },
  fieldButtonTextActive: {
    color: 'white',
  },
  currentWeather: {
    backgroundColor: 'white',
    margin: 15,
    borderRadius: 10,
    overflow: 'hidden',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
  },
  currentWeatherHeader: {
    padding: 15,
    backgroundColor: '#22c55e',
  },
  locationText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: 'white',
  },
  dateText: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.8)',
    marginTop: 2,
  },
  currentWeatherContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
  },
  temperatureContainer: {
    flex: 1,
  },
  temperature: {
    fontSize: 48,
    fontWeight: 'bold',
    color: '#111827',
  },
  weatherDescription: {
    fontSize: 16,
    color: '#4b5563',
    marginTop: 5,
  },
  weatherIcon: {
    width: 100,
    height: 100,
  },
  weatherDetails: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    borderTopWidth: 1,
    borderTopColor: '#f3f4f6',
    padding: 10,
  },
  weatherDetailItem: {
    width: '50%',
    padding: 10,
    flexDirection: 'row',
    alignItems: 'center',
  },
  weatherDetailLabel: {
    fontSize: 14,
    color: '#6b7280',
    marginLeft: 8,
    width: 80,
  },
  weatherDetailValue: {
    fontSize: 14,
    fontWeight: '500',
    color: '#111827',
  },
  windInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  windDirectionIcon: {
    marginLeft: 5,
  },
  forecastSection: {
    margin: 15,
    marginTop: 5,
    backgroundColor: 'white',
    borderRadius: 10,
    padding: 15,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 15,
    color: '#111827',
  },
  forecastContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  forecastDay: {
    alignItems: 'center',
    flex: 1,
  },
  forecastDate: {
    fontSize: 12,
    fontWeight: '500',
    color: '#4b5563',
    marginBottom: 5,
  },
  forecastIcon: {
    width: 40,
    height: 40,
  },
  forecastTemp: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#111827',
    marginTop: 5,
  },
  forecastDescription: {
    fontSize: 12,
    color: '#6b7280',
    marginTop: 2,
    textAlign: 'center',
  },
  forecastPrecipitation: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 5,
  },
  forecastPrecipitationText: {
    fontSize: 12,
    color: '#6b7280',
    marginLeft: 2,
  },
  weatherNotes: {
    padding: 15,
    marginBottom: 20,
  },
  weatherNotesText: {
    fontSize: 12,
    color: '#9ca3af',
    textAlign: 'center',
    fontStyle: 'italic',
  },
});

export default WeatherScreen;