import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../../../../shared/store/AuthProvider';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { MainStackParamList } from '../navigation/MainNavigator';
import { taskService } from '../../../../shared/services/taskService';
import { fieldService } from '../../../../shared/services/fieldService';

type TaskDetailScreenProps = NativeStackScreenProps<MainStackParamList, 'TaskDetail'>;

type Task = {
  id: string;
  title: string;
  description: string;
  status: 'active' | 'completed' | 'cancelled';
  priority: 'low' | 'medium' | 'high';
  dueDate: string;
  fieldId?: string;
  fieldName?: string;
  assignedTo?: string;
  assignedToName?: string;
  notes?: string;
  createdAt: string;
  updatedAt: string;
};

const TaskDetailScreen: React.FC<TaskDetailScreenProps> = ({ route, navigation }) => {
  const { taskId } = route.params;
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [task, setTask] = useState<Task | null>(null);
  const [field, setField] = useState<any>(null);

  const loadTask = async () => {
    try {
      setLoading(true);
      const taskData = await taskService.getTaskById(taskId);
      setTask(taskData);

      if (taskData.fieldId) {
        const fieldData = await fieldService.getFieldById(taskData.fieldId);
        setField(fieldData);
      }

      setLoading(false);
    } catch (error) {
      console.error('Error loading task details:', error);
      setLoading(false);
      Alert.alert('Error', 'Failed to load task details. Please try again.');
    }
  };

  useEffect(() => {
    loadTask();
  }, [taskId]);

  const handleCompleteTask = async () => {
    if (!task) return;

    Alert.alert(
      'Complete Task',
      'Are you sure you want to mark this task as completed?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Complete',
          onPress: async () => {
            try {
              await taskService.updateTaskStatus(taskId, 'completed');
              // Refresh the task data
              loadTask();
            } catch (error) {
              console.error('Error completing task:', error);
              Alert.alert('Error', 'Failed to complete task. Please try again.');
            }
          },
        },
      ]
    );
  };

  const handleCancelTask = async () => {
    if (!task) return;

    Alert.alert(
      'Cancel Task',
      'Are you sure you want to cancel this task?',
      [
        {
          text: 'No',
          style: 'cancel',
        },
        {
          text: 'Yes, Cancel Task',
          style: 'destructive',
          onPress: async () => {
            try {
              await taskService.updateTaskStatus(taskId, 'cancelled');
              // Refresh the task data
              loadTask();
            } catch (error) {
              console.error('Error cancelling task:', error);
              Alert.alert('Error', 'Failed to cancel task. Please try again.');
            }
          },
        },
      ]
    );
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return '#ef4444';
      case 'medium':
        return '#f59e0b';
      case 'low':
        return '#22c55e';
      default:
        return '#6b7280';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return '#22c55e';
      case 'completed':
        return '#3b82f6';
      case 'cancelled':
        return '#ef4444';
      default:
        return '#6b7280';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active':
        return 'Active';
      case 'completed':
        return 'Completed';
      case 'cancelled':
        return 'Cancelled';
      default:
        return status;
    }
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#22c55e" />
      </View>
    );
  }

  if (!task) {
    return (
      <View style={styles.errorContainer}>
        <Ionicons name="alert-circle-outline" size={48} color="#ef4444" />
        <Text style={styles.errorText}>Task not found</Text>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Text style={styles.backButtonText}>Go Back</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <View style={styles.titleContainer}>
          <Text style={styles.title}>{task.title}</Text>
          <View
            style={[
              styles.statusBadge,
              { backgroundColor: getStatusColor(task.status) },
            ]}
          >
            <Text style={styles.statusText}>{getStatusText(task.status)}</Text>
          </View>
        </View>
        <View style={styles.priorityContainer}>
          <Ionicons
            name="alert-circle"
            size={16}
            color={getPriorityColor(task.priority)}
          />
          <Text
            style={[
              styles.priorityText,
              { color: getPriorityColor(task.priority) },
            ]}
          >
            {task.priority.charAt(0).toUpperCase() + task.priority.slice(1)} Priority
          </Text>
        </View>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Description</Text>
        <Text style={styles.description}>{task.description || 'No description provided'}</Text>
      </View>

      {task.notes && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Notes</Text>
          <Text style={styles.description}>{task.notes}</Text>
        </View>
      )}

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Details</Text>
        <View style={styles.detailRow}>
          <View style={styles.detailItem}>
            <Ionicons name="calendar-outline" size={18} color="#6b7280" />
            <Text style={styles.detailLabel}>Due Date:</Text>
            <Text style={styles.detailValue}>
              {new Date(task.dueDate).toLocaleDateString()}
            </Text>
          </View>
        </View>

        {task.assignedToName && (
          <View style={styles.detailRow}>
            <View style={styles.detailItem}>
              <Ionicons name="person-outline" size={18} color="#6b7280" />
              <Text style={styles.detailLabel}>Assigned To:</Text>
              <Text style={styles.detailValue}>{task.assignedToName}</Text>
            </View>
          </View>
        )}

        {field && (
          <View style={styles.detailRow}>
            <View style={styles.detailItem}>
              <Ionicons name="location-outline" size={18} color="#6b7280" />
              <Text style={styles.detailLabel}>Field:</Text>
              <Text style={styles.detailValue}>{field.name}</Text>
            </View>
          </View>
        )}

        <View style={styles.detailRow}>
          <View style={styles.detailItem}>
            <Ionicons name="time-outline" size={18} color="#6b7280" />
            <Text style={styles.detailLabel}>Created:</Text>
            <Text style={styles.detailValue}>
              {new Date(task.createdAt).toLocaleDateString()}
            </Text>
          </View>
        </View>

        <View style={styles.detailRow}>
          <View style={styles.detailItem}>
            <Ionicons name="refresh-outline" size={18} color="#6b7280" />
            <Text style={styles.detailLabel}>Updated:</Text>
            <Text style={styles.detailValue}>
              {new Date(task.updatedAt).toLocaleDateString()}
            </Text>
          </View>
        </View>
      </View>

      {field && (
        <TouchableOpacity
          style={styles.fieldButton}
          onPress={() => navigation.navigate('FieldDetail', { fieldId: field.id })}
        >
          <Ionicons name="map-outline" size={20} color="white" />
          <Text style={styles.fieldButtonText}>View Field</Text>
        </TouchableOpacity>
      )}

      {task.status === 'active' && (
        <View style={styles.actionButtons}>
          <TouchableOpacity
            style={styles.completeButton}
            onPress={handleCompleteTask}
          >
            <Ionicons name="checkmark-circle-outline" size={20} color="white" />
            <Text style={styles.buttonText}>Complete Task</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.cancelButton}
            onPress={handleCancelTask}
          >
            <Ionicons name="close-circle-outline" size={20} color="white" />
            <Text style={styles.buttonText}>Cancel Task</Text>
          </TouchableOpacity>
        </View>
      )}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9fafb',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 18,
    color: '#4b5563',
    marginTop: 10,
    marginBottom: 20,
  },
  backButton: {
    backgroundColor: '#22c55e',
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 5,
  },
  backButtonText: {
    color: 'white',
    fontWeight: 'bold',
  },
  header: {
    backgroundColor: 'white',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  titleContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    flex: 1,
  },
  statusBadge: {
    paddingHorizontal: 10,
    paddingVertical: 5,
    borderRadius: 15,
    marginLeft: 10,
  },
  statusText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 12,
  },
  priorityContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  priorityText: {
    marginLeft: 5,
    fontSize: 14,
    fontWeight: '500',
  },
  section: {
    backgroundColor: 'white',
    padding: 20,
    marginTop: 10,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#4b5563',
  },
  description: {
    fontSize: 16,
    lineHeight: 24,
    color: '#4b5563',
  },
  detailRow: {
    marginBottom: 10,
  },
  detailItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  detailLabel: {
    fontSize: 14,
    color: '#6b7280',
    marginLeft: 8,
    width: 100,
  },
  detailValue: {
    fontSize: 14,
    color: '#111827',
    flex: 1,
  },
  fieldButton: {
    backgroundColor: '#3b82f6',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 15,
    marginHorizontal: 20,
    marginTop: 20,
    borderRadius: 5,
  },
  fieldButtonText: {
    color: 'white',
    fontWeight: 'bold',
    marginLeft: 8,
  },
  actionButtons: {
    flexDirection: 'column',
    marginHorizontal: 20,
    marginTop: 20,
    marginBottom: 30,
  },
  completeButton: {
    backgroundColor: '#22c55e',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 15,
    borderRadius: 5,
    marginBottom: 10,
  },
  cancelButton: {
    backgroundColor: '#ef4444',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 15,
    borderRadius: 5,
  },
  buttonText: {
    color: 'white',
    fontWeight: 'bold',
    marginLeft: 8,
  },
});

export default TaskDetailScreen;