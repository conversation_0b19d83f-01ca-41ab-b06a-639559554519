import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  ScrollView,
  Platform,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../../../../shared/store/AuthProvider';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { MainStackParamList } from '../navigation/MainNavigator';
import MapView, { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, PROVIDER_GOOGLE } from 'react-native-maps';
import * as Location from 'expo-location';
import * as TaskManager from 'expo-task-manager';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { fieldService } from '../../../../shared/services/fieldService';

type RecordingScreenProps = NativeStackScreenProps<MainStackParamList, 'Recording'>;

const LOCATION_TRACKING = 'location-tracking';
const LOCATION_TRACKING_INTERVAL = 5000; // 5 seconds

type Field = {
  id: string;
  name: string;
  latitude: number;
  longitude: number;
};

type LocationPoint = {
  latitude: number;
  longitude: number;
  timestamp: number;
  accuracy?: number;
  altitude?: number;
  speed?: number;
  heading?: number;
};

type RecordingSession = {
  id: string;
  fieldId: string;
  fieldName: string;
  operationType: string;
  startTime: number;
  endTime?: number;
  points: LocationPoint[];
  distance: number;
  duration: number;
  status: 'active' | 'paused' | 'completed';
};

// Define the task
TaskManager.defineTask(LOCATION_TRACKING, async ({ data, error }) => {
  if (error) {
    console.error('LOCATION_TRACKING task error:', error);
    return;
  }
  if (data) {
    const { locations } = data as { locations: Location.LocationObject[] };
    const location = locations[0];
    
    if (location) {
      try {
        // Get current recording session
        const sessionJson = await AsyncStorage.getItem('currentRecordingSession');
        if (sessionJson) {
          const session: RecordingSession = JSON.parse(sessionJson);
          
          // Only add points if recording is active
          if (session.status === 'active') {
            const newPoint: LocationPoint = {
              latitude: location.coords.latitude,
              longitude: location.coords.longitude,
              timestamp: location.timestamp,
              accuracy: location.coords.accuracy,
              altitude: location.coords.altitude,
              speed: location.coords.speed,
              heading: location.coords.heading,
            };
            
            session.points.push(newPoint);
            
            // Calculate distance and duration
            if (session.points.length > 1) {
              const lastPoint = session.points[session.points.length - 2];
              const distance = calculateDistance(
                lastPoint.latitude,
                lastPoint.longitude,
                newPoint.latitude,
                newPoint.longitude
              );
              session.distance += distance;
            }
            
            session.duration = Date.now() - session.startTime;
            
            // Save updated session
            await AsyncStorage.setItem('currentRecordingSession', JSON.stringify(session));
          }
        }
      } catch (error) {
        console.error('Error saving location:', error);
      }
    }
  }
});

const RecordingScreen: React.FC<RecordingScreenProps> = ({ navigation }) => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [fields, setFields] = useState<Field[]>([]);
  const [selectedField, setSelectedField] = useState<Field | null>(null);
  const [operationType, setOperationType] = useState<string>('');
  const [recording, setRecording] = useState(false);
  const [paused, setPaused] = useState(false);
  const [currentSession, setCurrentSession] = useState<RecordingSession | null>(null);
  const [locationPermission, setLocationPermission] = useState(false);
  const [currentLocation, setCurrentLocation] = useState<LocationPoint | null>(null);
  const [mapReady, setMapReady] = useState(false);
  const mapRef = useRef<MapView>(null);

  // Operation types
  const operationTypes = [
    'Planting',
    'Harvesting',
    'Spraying',
    'Fertilizing',
    'Tilling',
    'Scouting',
    'Other',
  ];

  useEffect(() => {
    checkPermissions();
    loadFields();
    checkExistingSession();

    // Start location updates for current position even when not recording
    startLocationUpdates();

    return () => {
      // Clean up location updates when component unmounts
      stopLocationUpdates();
    };
  }, []);

  const checkPermissions = async () => {
    const { status: foregroundStatus } = await Location.requestForegroundPermissionsAsync();
    
    if (foregroundStatus !== 'granted') {
      Alert.alert(
        'Permission Required',
        'This app needs location permissions to record GPS tracks.',
        [{ text: 'OK' }]
      );
      return;
    }

    if (Platform.OS === 'android') {
      const { status: backgroundStatus } = await Location.requestBackgroundPermissionsAsync();
      if (backgroundStatus !== 'granted') {
        Alert.alert(
          'Background Permission',
          'Background location permission is needed for tracking when the app is minimized.',
          [{ text: 'OK' }]
        );
      }
    }

    setLocationPermission(true);
  };

  const loadFields = async () => {
    try {
      if (!user?.farmId) {
        console.error('No farm ID available');
        setLoading(false);
        return;
      }

      const fetchedFields = await fieldService.getFields(user.farmId);
      setFields(fetchedFields);
      setLoading(false);
    } catch (error) {
      console.error('Error loading fields:', error);
      setLoading(false);
    }
  };

  const checkExistingSession = async () => {
    try {
      const sessionJson = await AsyncStorage.getItem('currentRecordingSession');
      if (sessionJson) {
        const session: RecordingSession = JSON.parse(sessionJson);
        setCurrentSession(session);
        
        // Find and set the selected field
        if (session.fieldId) {
          const field = fields.find(f => f.id === session.fieldId);
          if (field) setSelectedField(field);
        }
        
        setOperationType(session.operationType);
        
        if (session.status === 'active') {
          setRecording(true);
          setPaused(false);
        } else if (session.status === 'paused') {
          setRecording(true);
          setPaused(true);
        }
      }
    } catch (error) {
      console.error('Error checking existing session:', error);
    }
  };

  const startLocationUpdates = async () => {
    try {
      await Location.startLocationUpdatesAsync(LOCATION_TRACKING, {
        accuracy: Location.Accuracy.Highest,
        distanceInterval: 5, // minimum distance (in meters) between location updates
        timeInterval: LOCATION_TRACKING_INTERVAL,
        foregroundService: {
          notificationTitle: 'GPS Tracking',
          notificationBody: 'Recording your field operations',
        },
      });
      
      // Also start watching position for map updates
      Location.watchPositionAsync(
        {
          accuracy: Location.Accuracy.Highest,
          distanceInterval: 5,
          timeInterval: 3000,
        },
        (location) => {
          const newLocation: LocationPoint = {
            latitude: location.coords.latitude,
            longitude: location.coords.longitude,
            timestamp: location.timestamp,
            accuracy: location.coords.accuracy,
            altitude: location.coords.altitude,
            speed: location.coords.speed,
            heading: location.coords.heading,
          };
          setCurrentLocation(newLocation);
          
          // Center map on current location if recording
          if (recording && mapRef.current && !paused) {
            mapRef.current.animateToRegion({
              latitude: newLocation.latitude,
              longitude: newLocation.longitude,
              latitudeDelta: 0.005,
              longitudeDelta: 0.005,
            });
          }
        }
      );
    } catch (error) {
      console.error('Error starting location updates:', error);
    }
  };

  const stopLocationUpdates = async () => {
    try {
      const isTracking = await Location.hasStartedLocationUpdatesAsync(LOCATION_TRACKING);
      if (isTracking) {
        await Location.stopLocationUpdatesAsync(LOCATION_TRACKING);
      }
    } catch (error) {
      console.error('Error stopping location updates:', error);
    }
  };

  const startRecording = async () => {
    if (!selectedField) {
      Alert.alert('Field Required', 'Please select a field before starting recording.');
      return;
    }

    if (!operationType) {
      Alert.alert('Operation Type Required', 'Please select an operation type before starting recording.');
      return;
    }

    try {
      // Create a new recording session
      const newSession: RecordingSession = {
        id: Date.now().toString(),
        fieldId: selectedField.id,
        fieldName: selectedField.name,
        operationType,
        startTime: Date.now(),
        points: [],
        distance: 0,
        duration: 0,
        status: 'active',
      };

      // Save the session
      await AsyncStorage.setItem('currentRecordingSession', JSON.stringify(newSession));
      setCurrentSession(newSession);
      setRecording(true);
      setPaused(false);

      // Ensure location tracking is started
      const isTracking = await Location.hasStartedLocationUpdatesAsync(LOCATION_TRACKING);
      if (!isTracking) {
        await startLocationUpdates();
      }
    } catch (error) {
      console.error('Error starting recording:', error);
      Alert.alert('Error', 'Failed to start recording. Please try again.');
    }
  };

  const pauseRecording = async () => {
    if (!currentSession) return;

    try {
      const updatedSession = { ...currentSession, status: 'paused' as const };
      await AsyncStorage.setItem('currentRecordingSession', JSON.stringify(updatedSession));
      setCurrentSession(updatedSession);
      setPaused(true);
    } catch (error) {
      console.error('Error pausing recording:', error);
      Alert.alert('Error', 'Failed to pause recording. Please try again.');
    }
  };

  const resumeRecording = async () => {
    if (!currentSession) return;

    try {
      const updatedSession = { ...currentSession, status: 'active' as const };
      await AsyncStorage.setItem('currentRecordingSession', JSON.stringify(updatedSession));
      setCurrentSession(updatedSession);
      setPaused(false);
    } catch (error) {
      console.error('Error resuming recording:', error);
      Alert.alert('Error', 'Failed to resume recording. Please try again.');
    }
  };

  const stopRecording = async () => {
    if (!currentSession) return;

    Alert.alert(
      'Stop Recording',
      'Are you sure you want to stop recording? This will save your current session.',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Stop',
          onPress: async () => {
            try {
              // Update session status
              const updatedSession = {
                ...currentSession,
                endTime: Date.now(),
                status: 'completed' as const,
              };

              // Save to completed sessions
              const sessionsJson = await AsyncStorage.getItem('completedRecordingSessions');
              const sessions: RecordingSession[] = sessionsJson ? JSON.parse(sessionsJson) : [];
              sessions.push(updatedSession);
              await AsyncStorage.setItem('completedRecordingSessions', JSON.stringify(sessions));

              // Clear current session
              await AsyncStorage.removeItem('currentRecordingSession');
              setCurrentSession(null);
              setRecording(false);
              setPaused(false);

              // Show success message
              Alert.alert(
                'Recording Saved',
                'Your recording has been saved successfully.',
                [
                  {
                    text: 'View Sessions',
                    onPress: () => navigation.navigate('RecordingSessions'),
                  },
                  {
                    text: 'OK',
                  },
                ]
              );
            } catch (error) {
              console.error('Error stopping recording:', error);
              Alert.alert('Error', 'Failed to stop recording. Please try again.');
            }
          },
        },
      ]
    );
  };

  const discardRecording = async () => {
    if (!currentSession) return;

    Alert.alert(
      'Discard Recording',
      'Are you sure you want to discard this recording? This action cannot be undone.',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Discard',
          style: 'destructive',
          onPress: async () => {
            try {
              // Clear current session
              await AsyncStorage.removeItem('currentRecordingSession');
              setCurrentSession(null);
              setRecording(false);
              setPaused(false);
            } catch (error) {
              console.error('Error discarding recording:', error);
              Alert.alert('Error', 'Failed to discard recording. Please try again.');
            }
          },
        },
      ]
    );
  };

  const calculateDistance = (lat1: number, lon1: number, lat2: number, lon2: number): number => {
    const R = 6371e3; // Earth radius in meters
    const φ1 = (lat1 * Math.PI) / 180;
    const φ2 = (lat2 * Math.PI) / 180;
    const Δφ = ((lat2 - lat1) * Math.PI) / 180;
    const Δλ = ((lon2 - lon1) * Math.PI) / 180;

    const a =
      Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
      Math.cos(φ1) * Math.cos(φ2) * Math.sin(Δλ / 2) * Math.sin(Δλ / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    const distance = R * c;

    return distance;
  };

  const formatDuration = (milliseconds: number): string => {
    const seconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    
    const displayHours = hours.toString().padStart(2, '0');
    const displayMinutes = (minutes % 60).toString().padStart(2, '0');
    const displaySeconds = (seconds % 60).toString().padStart(2, '0');
    
    return `${displayHours}:${displayMinutes}:${displaySeconds}`;
  };

  const formatDistance = (meters: number): string => {
    if (meters < 1000) {
      return `${meters.toFixed(0)} m`;
    } else {
      return `${(meters / 1000).toFixed(2)} km`;
    }
  };

  const getMapRegion = () => {
    if (currentLocation) {
      return {
        latitude: currentLocation.latitude,
        longitude: currentLocation.longitude,
        latitudeDelta: 0.005,
        longitudeDelta: 0.005,
      };
    } else if (selectedField) {
      return {
        latitude: selectedField.latitude,
        longitude: selectedField.longitude,
        latitudeDelta: 0.05,
        longitudeDelta: 0.05,
      };
    }
    return null;
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#22c55e" />
      </View>
    );
  }

  if (!locationPermission) {
    return (
      <View style={styles.permissionContainer}>
        <Ionicons name="location-off-outline" size={64} color="#ef4444" />
        <Text style={styles.permissionTitle}>Location Permission Required</Text>
        <Text style={styles.permissionText}>
          This feature requires location permissions to record GPS tracks.
        </Text>
        <TouchableOpacity style={styles.permissionButton} onPress={checkPermissions}>
          <Text style={styles.permissionButtonText}>Grant Permission</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.mapContainer}>
        {!mapReady && (
          <View style={styles.mapLoadingOverlay}>
            <ActivityIndicator size="large" color="#22c55e" />
          </View>
        )}
        <MapView
          ref={mapRef}
          style={styles.map}
          provider={PROVIDER_GOOGLE}
          initialRegion={getMapRegion() || undefined}
          onMapReady={() => setMapReady(true)}
          showsUserLocation
          followsUserLocation
        >
          {currentSession?.points && currentSession.points.length > 1 && (
            <Polyline
              coordinates={currentSession.points}
              strokeColor="#22c55e"
              strokeWidth={4}
            />
          )}
          {currentLocation && (
            <Marker
              coordinate={{
                latitude: currentLocation.latitude,
                longitude: currentLocation.longitude,
              }}
            >
              <View style={styles.currentLocationMarker}>
                <Ionicons name="navigate" size={20} color="white" />
              </View>
            </Marker>
          )}
        </MapView>
      </View>

      <ScrollView style={styles.content}>
        {!recording ? (
          <>
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Select Field</Text>
              <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.fieldSelector}>
                {fields.map((field) => (
                  <TouchableOpacity
                    key={field.id}
                    style={[
                      styles.fieldButton,
                      selectedField?.id === field.id && styles.fieldButtonActive,
                    ]}
                    onPress={() => setSelectedField(field)}
                  >
                    <Text
                      style={[
                        styles.fieldButtonText,
                        selectedField?.id === field.id && styles.fieldButtonTextActive,
                      ]}
                    >
                      {field.name}
                    </Text>
                  </TouchableOpacity>
                ))}
              </ScrollView>
            </View>

            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Operation Type</Text>
              <View style={styles.operationTypeContainer}>
                {operationTypes.map((type) => (
                  <TouchableOpacity
                    key={type}
                    style={[
                      styles.operationTypeButton,
                      operationType === type && styles.operationTypeButtonActive,
                    ]}
                    onPress={() => setOperationType(type)}
                  >
                    <Text
                      style={[
                        styles.operationTypeButtonText,
                        operationType === type && styles.operationTypeButtonTextActive,
                      ]}
                    >
                      {type}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            <TouchableOpacity
              style={styles.startButton}
              onPress={startRecording}
              disabled={!selectedField || !operationType}
            >
              <Ionicons name="play" size={24} color="white" />
              <Text style={styles.startButtonText}>Start Recording</Text>
            </TouchableOpacity>
          </>
        ) : (
          <>
            <View style={styles.recordingInfo}>
              <View style={styles.recordingHeader}>
                <View style={styles.recordingStatus}>
                  {paused ? (
                    <View style={styles.pausedIndicator}>
                      <Text style={styles.pausedText}>PAUSED</Text>
                    </View>
                  ) : (
                    <View style={styles.recordingIndicator}>
                      <View style={styles.recordingDot} />
                      <Text style={styles.recordingText}>RECORDING</Text>
                    </View>
                  )}
                </View>
                <Text style={styles.fieldName}>{currentSession?.fieldName}</Text>
                <Text style={styles.operationType}>{currentSession?.operationType}</Text>
              </View>

              <View style={styles.statsContainer}>
                <View style={styles.statItem}>
                  <Ionicons name="time-outline" size={24} color="#22c55e" />
                  <Text style={styles.statValue}>
                    {formatDuration(currentSession?.duration || 0)}
                  </Text>
                  <Text style={styles.statLabel}>Duration</Text>
                </View>

                <View style={styles.statItem}>
                  <Ionicons name="speedometer-outline" size={24} color="#22c55e" />
                  <Text style={styles.statValue}>
                    {formatDistance(currentSession?.distance || 0)}
                  </Text>
                  <Text style={styles.statLabel}>Distance</Text>
                </View>

                <View style={styles.statItem}>
                  <Ionicons name="location-outline" size={24} color="#22c55e" />
                  <Text style={styles.statValue}>
                    {currentSession?.points.length || 0}
                  </Text>
                  <Text style={styles.statLabel}>Points</Text>
                </View>
              </View>
            </View>

            <View style={styles.recordingControls}>
              {paused ? (
                <TouchableOpacity style={styles.resumeButton} onPress={resumeRecording}>
                  <Ionicons name="play" size={24} color="white" />
                  <Text style={styles.controlButtonText}>Resume</Text>
                </TouchableOpacity>
              ) : (
                <TouchableOpacity style={styles.pauseButton} onPress={pauseRecording}>
                  <Ionicons name="pause" size={24} color="white" />
                  <Text style={styles.controlButtonText}>Pause</Text>
                </TouchableOpacity>
              )}

              <TouchableOpacity style={styles.stopButton} onPress={stopRecording}>
                <Ionicons name="stop" size={24} color="white" />
                <Text style={styles.controlButtonText}>Stop</Text>
              </TouchableOpacity>

              <TouchableOpacity style={styles.discardButton} onPress={discardRecording}>
                <Ionicons name="trash" size={24} color="white" />
                <Text style={styles.controlButtonText}>Discard</Text>
              </TouchableOpacity>
            </View>
          </>
        )}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9fafb',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  permissionContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  permissionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#111827',
    marginTop: 20,
    marginBottom: 10,
  },
  permissionText: {
    fontSize: 16,
    color: '#4b5563',
    textAlign: 'center',
    marginBottom: 20,
  },
  permissionButton: {
    backgroundColor: '#22c55e',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
  },
  permissionButtonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 16,
  },
  mapContainer: {
    height: 300,
    width: '100%',
  },
  mapLoadingOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(255, 255, 255, 0.7)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1,
  },
  map: {
    ...StyleSheet.absoluteFillObject,
  },
  currentLocationMarker: {
    backgroundColor: '#22c55e',
    borderRadius: 20,
    padding: 5,
    borderWidth: 2,
    borderColor: 'white',
  },
  content: {
    flex: 1,
  },
  section: {
    backgroundColor: 'white',
    margin: 15,
    borderRadius: 10,
    padding: 15,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#111827',
    marginBottom: 15,
  },
  fieldSelector: {
    flexDirection: 'row',
  },
  fieldButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 20,
    marginRight: 8,
    backgroundColor: '#f3f4f6',
  },
  fieldButtonActive: {
    backgroundColor: '#22c55e',
  },
  fieldButtonText: {
    color: '#4b5563',
    fontWeight: '500',
  },
  fieldButtonTextActive: {
    color: 'white',
  },
  operationTypeContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  operationTypeButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 20,
    marginRight: 8,
    marginBottom: 8,
    backgroundColor: '#f3f4f6',
  },
  operationTypeButtonActive: {
    backgroundColor: '#22c55e',
  },
  operationTypeButtonText: {
    color: '#4b5563',
    fontWeight: '500',
  },
  operationTypeButtonTextActive: {
    color: 'white',
  },
  startButton: {
    backgroundColor: '#22c55e',
    margin: 15,
    padding: 15,
    borderRadius: 10,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  startButtonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
    marginLeft: 10,
  },
  recordingInfo: {
    backgroundColor: 'white',
    margin: 15,
    borderRadius: 10,
    padding: 15,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
  },
  recordingHeader: {
    alignItems: 'center',
    marginBottom: 15,
  },
  recordingStatus: {
    marginBottom: 10,
  },
  recordingIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(239, 68, 68, 0.1)',
    paddingVertical: 5,
    paddingHorizontal: 10,
    borderRadius: 15,
  },
  recordingDot: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: '#ef4444',
    marginRight: 5,
  },
  recordingText: {
    color: '#ef4444',
    fontWeight: 'bold',
  },
  pausedIndicator: {
    backgroundColor: 'rgba(245, 158, 11, 0.1)',
    paddingVertical: 5,
    paddingHorizontal: 10,
    borderRadius: 15,
  },
  pausedText: {
    color: '#f59e0b',
    fontWeight: 'bold',
  },
  fieldName: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#111827',
  },
  operationType: {
    fontSize: 16,
    color: '#4b5563',
    marginTop: 5,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 15,
    borderTopWidth: 1,
    borderTopColor: '#f3f4f6',
    paddingTop: 15,
  },
  statItem: {
    alignItems: 'center',
    flex: 1,
  },
  statValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#111827',
    marginTop: 5,
  },
  statLabel: {
    fontSize: 14,
    color: '#6b7280',
    marginTop: 2,
  },
  recordingControls: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    margin: 15,
  },
  pauseButton: {
    backgroundColor: '#f59e0b',
    flex: 1,
    padding: 15,
    borderRadius: 10,
    marginRight: 5,
    alignItems: 'center',
  },
  resumeButton: {
    backgroundColor: '#22c55e',
    flex: 1,
    padding: 15,
    borderRadius: 10,
    marginRight: 5,
    alignItems: 'center',
  },
  stopButton: {
    backgroundColor: '#3b82f6',
    flex: 1,
    padding: 15,
    borderRadius: 10,
    marginHorizontal: 5,
    alignItems: 'center',
  },
  discardButton: {
    backgroundColor: '#ef4444',
    flex: 1,
    padding: 15,
    borderRadius: 10,
    marginLeft: 5,
    alignItems: 'center',
  },
  controlButtonText: {
    color: 'white',
    fontWeight: 'bold',
    marginTop: 5,
  },
});

export default RecordingScreen;