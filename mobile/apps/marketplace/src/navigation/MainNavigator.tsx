import React from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { Ionicons } from '@expo/vector-icons';

// Import screens (to be created)
import HomeScreen from '../screens/HomeScreen';
import ProductsScreen from '../screens/ProductsScreen';
import OrdersScreen from '../screens/OrdersScreen';
import SellerScreen from '../screens/SellerScreen';
import ProfileScreen from '../screens/ProfileScreen';
import ProductDetailScreen from '../screens/ProductDetailScreen';
import OrderDetailScreen from '../screens/OrderDetailScreen';
import CartScreen from '../screens/CartScreen';
import CheckoutScreen from '../screens/CheckoutScreen';
import SellerDashboardScreen from '../screens/SellerDashboardScreen';
import SellerProductsScreen from '../screens/SellerProductsScreen';
import SellerOrdersScreen from '../screens/SellerOrdersScreen';
import AddProductScreen from '../screens/AddProductScreen';
import EditProductScreen from '../screens/EditProductScreen';
import MessagesScreen from '../screens/MessagesScreen';
import ConversationScreen from '../screens/ConversationScreen';
import ReviewsScreen from '../screens/ReviewsScreen';
import PaymentMethodsScreen from '../screens/PaymentMethodsScreen';

export type MainStackParamList = {
  MainTabs: undefined;
  ProductDetail: { productId: string };
  OrderDetail: { orderId: string };
  Cart: undefined;
  Checkout: undefined;
  SellerDashboard: undefined;
  SellerProducts: undefined;
  SellerOrders: undefined;
  AddProduct: undefined;
  EditProduct: { productId: string };
  Messages: undefined;
  Conversation: { conversationId: string; recipientName: string };
  Reviews: { productId?: string; sellerId?: string };
  PaymentMethods: undefined;
};

export type MainTabParamList = {
  Home: undefined;
  Products: undefined;
  Orders: undefined;
  Seller: undefined;
  Profile: undefined;
};

const Stack = createNativeStackNavigator<MainStackParamList>();
const Tab = createBottomTabNavigator<MainTabParamList>();

const MainTabs: React.FC = () => {
  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName: keyof typeof Ionicons.glyphMap = 'home';

          if (route.name === 'Home') {
            iconName = focused ? 'home' : 'home-outline';
          } else if (route.name === 'Products') {
            iconName = focused ? 'grid' : 'grid-outline';
          } else if (route.name === 'Orders') {
            iconName = focused ? 'list' : 'list-outline';
          } else if (route.name === 'Seller') {
            iconName = focused ? 'storefront' : 'storefront-outline';
          } else if (route.name === 'Profile') {
            iconName = focused ? 'person' : 'person-outline';
          }

          return <Ionicons name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: '#3b82f6',
        tabBarInactiveTintColor: 'gray',
        headerShown: true,
        headerStyle: {
          backgroundColor: '#3b82f6',
        },
        headerTintColor: '#fff',
        headerTitleStyle: {
          fontWeight: 'bold',
        },
      })}
    >
      <Tab.Screen name="Home" component={HomeScreen} options={{ title: 'Marketplace' }} />
      <Tab.Screen name="Products" component={ProductsScreen} options={{ title: 'Products' }} />
      <Tab.Screen name="Orders" component={OrdersScreen} options={{ title: 'My Orders' }} />
      <Tab.Screen name="Seller" component={SellerScreen} options={{ title: 'Seller Hub' }} />
      <Tab.Screen name="Profile" component={ProfileScreen} options={{ title: 'Profile' }} />
    </Tab.Navigator>
  );
};

const MainNavigator: React.FC = () => {
  return (
    <Stack.Navigator
      initialRouteName="MainTabs"
      screenOptions={{
        headerShown: false,
      }}
    >
      <Stack.Screen name="MainTabs" component={MainTabs} />
      <Stack.Screen 
        name="ProductDetail" 
        component={ProductDetailScreen} 
        options={{ 
          headerShown: true,
          headerStyle: { backgroundColor: '#3b82f6' },
          headerTintColor: '#fff',
          title: 'Product Details'
        }} 
      />
      <Stack.Screen 
        name="OrderDetail" 
        component={OrderDetailScreen} 
        options={{ 
          headerShown: true,
          headerStyle: { backgroundColor: '#3b82f6' },
          headerTintColor: '#fff',
          title: 'Order Details'
        }} 
      />
      <Stack.Screen 
        name="Cart" 
        component={CartScreen} 
        options={{ 
          headerShown: true,
          headerStyle: { backgroundColor: '#3b82f6' },
          headerTintColor: '#fff',
          title: 'Shopping Cart'
        }} 
      />
      <Stack.Screen 
        name="Checkout" 
        component={CheckoutScreen} 
        options={{ 
          headerShown: true,
          headerStyle: { backgroundColor: '#3b82f6' },
          headerTintColor: '#fff',
          title: 'Checkout'
        }} 
      />
      <Stack.Screen 
        name="SellerDashboard" 
        component={SellerDashboardScreen} 
        options={{ 
          headerShown: true,
          headerStyle: { backgroundColor: '#3b82f6' },
          headerTintColor: '#fff',
          title: 'Seller Dashboard'
        }} 
      />
      <Stack.Screen 
        name="SellerProducts" 
        component={SellerProductsScreen} 
        options={{ 
          headerShown: true,
          headerStyle: { backgroundColor: '#3b82f6' },
          headerTintColor: '#fff',
          title: 'My Products'
        }} 
      />
      <Stack.Screen 
        name="SellerOrders" 
        component={SellerOrdersScreen} 
        options={{ 
          headerShown: true,
          headerStyle: { backgroundColor: '#3b82f6' },
          headerTintColor: '#fff',
          title: 'Seller Orders'
        }} 
      />
      <Stack.Screen 
        name="AddProduct" 
        component={AddProductScreen} 
        options={{ 
          headerShown: true,
          headerStyle: { backgroundColor: '#3b82f6' },
          headerTintColor: '#fff',
          title: 'Add Product'
        }} 
      />
      <Stack.Screen 
        name="EditProduct" 
        component={EditProductScreen} 
        options={{ 
          headerShown: true,
          headerStyle: { backgroundColor: '#3b82f6' },
          headerTintColor: '#fff',
          title: 'Edit Product'
        }} 
      />
      <Stack.Screen 
        name="Messages" 
        component={MessagesScreen} 
        options={{ 
          headerShown: true,
          headerStyle: { backgroundColor: '#3b82f6' },
          headerTintColor: '#fff',
          title: 'Messages'
        }} 
      />
      <Stack.Screen 
        name="Conversation" 
        component={ConversationScreen} 
        options={({ route }) => ({ 
          headerShown: true,
          headerStyle: { backgroundColor: '#3b82f6' },
          headerTintColor: '#fff',
          title: route.params.recipientName
        })} 
      />
      <Stack.Screen 
        name="Reviews" 
        component={ReviewsScreen} 
        options={{ 
          headerShown: true,
          headerStyle: { backgroundColor: '#3b82f6' },
          headerTintColor: '#fff',
          title: 'Reviews'
        }} 
      />
      <Stack.Screen 
        name="PaymentMethods" 
        component={PaymentMethodsScreen} 
        options={{ 
          headerShown: true,
          headerStyle: { backgroundColor: '#3b82f6' },
          headerTintColor: '#fff',
          title: 'Payment Methods'
        }} 
      />
    </Stack.Navigator>
  );
};

export default MainNavigator;