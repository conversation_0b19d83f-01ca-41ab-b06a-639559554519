import React, { useState, useEffect } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  FlatList, 
  TouchableOpacity, 
  Image, 
  TextInput, 
  ActivityIndicator,
  RefreshControl,
  Modal,
  ScrollView
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { MainStackParamList } from '../navigation/MainNavigator';

type NavigationProp = NativeStackNavigationProp<MainStackParamList>;
type ProductsScreenRouteProp = RouteProp<{ Products: { categoryId?: string } }, 'Products'>;

// Mock data for products
const ALL_PRODUCTS = [
  { id: '1', name: 'Organic Tomatoes', price: 3.99, image: 'https://via.placeholder.com/150', seller: 'Green Valley Farm', category: '1' },
  { id: '2', name: 'Fresh Eggs (Dozen)', price: 4.50, image: 'https://via.placeholder.com/150', seller: 'Happy Hens Farm', category: '2' },
  { id: '3', name: 'Grass-Fed Beef', price: 12.99, image: 'https://via.placeholder.com/150', seller: 'Meadow Ranch', category: '3' },
  { id: '4', name: 'Honey (16oz)', price: 8.75, image: 'https://via.placeholder.com/150', seller: 'Busy Bee Apiary', category: '1' },
  { id: '5', name: 'Organic Apples', price: 2.99, image: 'https://via.placeholder.com/150', seller: 'Green Valley Farm', category: '1' },
  { id: '6', name: 'Goat Cheese', price: 6.50, image: 'https://via.placeholder.com/150', seller: 'Mountain Dairy', category: '2' },
  { id: '7', name: 'Tractor Parts', price: 45.99, image: 'https://via.placeholder.com/150', seller: 'Farm Supply Co', category: '4' },
  { id: '8', name: 'Heirloom Seeds', price: 3.25, image: 'https://via.placeholder.com/150', seller: 'Heritage Seeds', category: '5' },
  { id: '9', name: 'Organic Fertilizer', price: 15.99, image: 'https://via.placeholder.com/150', seller: 'Green Growth', category: '6' },
  { id: '10', name: 'Chicken Feed', price: 22.50, image: 'https://via.placeholder.com/150', seller: 'Farm Supply Co', category: '6' },
];

// Mock data for categories
const CATEGORIES = [
  { id: '1', name: 'Produce' },
  { id: '2', name: 'Dairy' },
  { id: '3', name: 'Meat' },
  { id: '4', name: 'Equipment' },
  { id: '5', name: 'Seeds' },
  { id: '6', name: 'Supplies' },
];

const ProductsScreen: React.FC = () => {
  const navigation = useNavigation<NavigationProp>();
  const route = useRoute<ProductsScreenRouteProp>();
  const initialCategoryId = route.params?.categoryId;

  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [products, setProducts] = useState<typeof ALL_PRODUCTS>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string | null>(initialCategoryId || null);
  const [showFilters, setShowFilters] = useState(false);
  const [sortBy, setSortBy] = useState<'name' | 'price_low' | 'price_high'>('name');

  useEffect(() => {
    // Simulate loading data
    const timer = setTimeout(() => {
      filterProducts();
      setLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, [initialCategoryId]);

  const filterProducts = () => {
    let filtered = [...ALL_PRODUCTS];

    // Apply category filter
    if (selectedCategory) {
      filtered = filtered.filter(product => product.category === selectedCategory);
    }

    // Apply search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(
        product => 
          product.name.toLowerCase().includes(query) || 
          product.seller.toLowerCase().includes(query)
      );
    }

    // Apply sorting
    if (sortBy === 'name') {
      filtered.sort((a, b) => a.name.localeCompare(b.name));
    } else if (sortBy === 'price_low') {
      filtered.sort((a, b) => a.price - b.price);
    } else if (sortBy === 'price_high') {
      filtered.sort((a, b) => b.price - a.price);
    }

    setProducts(filtered);
  };

  useEffect(() => {
    filterProducts();
  }, [searchQuery, selectedCategory, sortBy]);

  const onRefresh = React.useCallback(() => {
    setRefreshing(true);
    // Simulate refreshing data
    setTimeout(() => {
      filterProducts();
      setRefreshing(false);
    }, 1000);
  }, [searchQuery, selectedCategory, sortBy]);

  const handleProductPress = (productId: string) => {
    navigation.navigate('ProductDetail', { productId });
  };

  const handleCategoryPress = (categoryId: string) => {
    setSelectedCategory(categoryId === selectedCategory ? null : categoryId);
  };

  const handleCartPress = () => {
    navigation.navigate('Cart');
  };

  const renderProductItem = ({ item }: { item: typeof ALL_PRODUCTS[0] }) => (
    <TouchableOpacity 
      style={styles.productCard}
      onPress={() => handleProductPress(item.id)}
    >
      <Image source={{ uri: item.image }} style={styles.productImage} />
      <View style={styles.productInfo}>
        <Text style={styles.productName}>{item.name}</Text>
        <Text style={styles.productSeller}>{item.seller}</Text>
        <Text style={styles.productPrice}>${typeof item.price === 'number' ? item.price.toFixed(2) : item.price}</Text>
      </View>
    </TouchableOpacity>
  );

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#3b82f6" />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Header with search and cart */}
      <View style={styles.header}>
        <View style={styles.searchContainer}>
          <Ionicons name="search" size={20} color="#666" style={styles.searchIcon} />
          <TextInput
            style={styles.searchInput}
            placeholder="Search products, sellers..."
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
          {searchQuery ? (
            <TouchableOpacity onPress={() => setSearchQuery('')}>
              <Ionicons name="close-circle" size={20} color="#666" />
            </TouchableOpacity>
          ) : null}
        </View>
        <TouchableOpacity style={styles.iconButton} onPress={() => setShowFilters(true)}>
          <Ionicons name="options-outline" size={24} color="#3b82f6" />
        </TouchableOpacity>
        <TouchableOpacity style={styles.iconButton} onPress={handleCartPress}>
          <Ionicons name="cart-outline" size={24} color="#3b82f6" />
        </TouchableOpacity>
      </View>

      {/* Category filters */}
      <ScrollView 
        horizontal 
        showsHorizontalScrollIndicator={false} 
        style={styles.categoriesScroll}
        contentContainerStyle={styles.categoriesContainer}
      >
        {CATEGORIES.map(category => (
          <TouchableOpacity 
            key={category.id} 
            style={[
              styles.categoryChip,
              selectedCategory === category.id && styles.categoryChipSelected
            ]}
            onPress={() => handleCategoryPress(category.id)}
          >
            <Text 
              style={[
                styles.categoryChipText,
                selectedCategory === category.id && styles.categoryChipTextSelected
              ]}
            >
              {category.name}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>

      {/* Results count and sort button */}
      <View style={styles.resultsHeader}>
        <Text style={styles.resultsCount}>{products.length} products</Text>
        <TouchableOpacity 
          style={styles.sortButton}
          onPress={() => setShowFilters(true)}
        >
          <Text style={styles.sortButtonText}>
            Sort: {sortBy === 'name' ? 'Name' : sortBy === 'price_low' ? 'Price: Low to High' : 'Price: High to Low'}
          </Text>
          <Ionicons name="chevron-down" size={16} color="#3b82f6" />
        </TouchableOpacity>
      </View>

      {/* Products list */}
      <FlatList
        data={products}
        renderItem={renderProductItem}
        keyExtractor={item => item.id}
        numColumns={2}
        contentContainerStyle={styles.productsContainer}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Ionicons name="search" size={48} color="#ccc" />
            <Text style={styles.emptyText}>No products found</Text>
            <Text style={styles.emptySubtext}>Try adjusting your search or filters</Text>
          </View>
        }
      />

      {/* Filters modal */}
      <Modal
        visible={showFilters}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowFilters(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Filter & Sort</Text>
              <TouchableOpacity onPress={() => setShowFilters(false)}>
                <Ionicons name="close" size={24} color="#000" />
              </TouchableOpacity>
            </View>

            <Text style={styles.modalSectionTitle}>Sort By</Text>
            <TouchableOpacity 
              style={styles.modalOption}
              onPress={() => {
                setSortBy('name');
                setShowFilters(false);
              }}
            >
              <Text style={styles.modalOptionText}>Name</Text>
              {sortBy === 'name' && <Ionicons name="checkmark" size={20} color="#3b82f6" />}
            </TouchableOpacity>
            <TouchableOpacity 
              style={styles.modalOption}
              onPress={() => {
                setSortBy('price_low');
                setShowFilters(false);
              }}
            >
              <Text style={styles.modalOptionText}>Price: Low to High</Text>
              {sortBy === 'price_low' && <Ionicons name="checkmark" size={20} color="#3b82f6" />}
            </TouchableOpacity>
            <TouchableOpacity 
              style={styles.modalOption}
              onPress={() => {
                setSortBy('price_high');
                setShowFilters(false);
              }}
            >
              <Text style={styles.modalOptionText}>Price: High to Low</Text>
              {sortBy === 'price_high' && <Ionicons name="checkmark" size={20} color="#3b82f6" />}
            </TouchableOpacity>

            <Text style={styles.modalSectionTitle}>Categories</Text>
            <View style={styles.modalCategoriesContainer}>
              {CATEGORIES.map(category => (
                <TouchableOpacity 
                  key={category.id} 
                  style={[
                    styles.modalCategoryChip,
                    selectedCategory === category.id && styles.modalCategoryChipSelected
                  ]}
                  onPress={() => setSelectedCategory(
                    category.id === selectedCategory ? null : category.id
                  )}
                >
                  <Text 
                    style={[
                      styles.modalCategoryChipText,
                      selectedCategory === category.id && styles.modalCategoryChipTextSelected
                    ]}
                  >
                    {category.name}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>

            <TouchableOpacity 
              style={styles.applyButton}
              onPress={() => setShowFilters(false)}
            >
              <Text style={styles.applyButtonText}>Apply</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 15,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  searchContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f0f0f0',
    borderRadius: 8,
    paddingHorizontal: 10,
    marginRight: 10,
  },
  searchIcon: {
    marginRight: 10,
  },
  searchInput: {
    flex: 1,
    height: 40,
    fontSize: 16,
  },
  iconButton: {
    padding: 5,
    marginLeft: 5,
  },
  categoriesScroll: {
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  categoriesContainer: {
    paddingHorizontal: 15,
    paddingVertical: 10,
  },
  categoryChip: {
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: '#f0f0f0',
    marginRight: 10,
  },
  categoryChipSelected: {
    backgroundColor: '#3b82f6',
  },
  categoryChipText: {
    fontSize: 14,
    color: '#333',
  },
  categoryChipTextSelected: {
    color: '#fff',
  },
  resultsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 15,
  },
  resultsCount: {
    fontSize: 14,
    color: '#666',
  },
  sortButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  sortButtonText: {
    fontSize: 14,
    color: '#3b82f6',
    marginRight: 5,
  },
  productsContainer: {
    padding: 10,
  },
  productCard: {
    flex: 1,
    margin: 5,
    backgroundColor: '#fff',
    borderRadius: 8,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  productImage: {
    width: '100%',
    height: 150,
  },
  productInfo: {
    padding: 10,
  },
  productName: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  productSeller: {
    fontSize: 12,
    color: '#666',
    marginTop: 2,
  },
  productPrice: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#3b82f6',
    marginTop: 5,
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 50,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 10,
    color: '#333',
  },
  emptySubtext: {
    fontSize: 14,
    color: '#666',
    marginTop: 5,
    textAlign: 'center',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: '#fff',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 20,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  modalSectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginTop: 15,
    marginBottom: 10,
  },
  modalOption: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  modalOptionText: {
    fontSize: 16,
  },
  modalCategoriesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 5,
  },
  modalCategoryChip: {
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: '#f0f0f0',
    marginRight: 10,
    marginBottom: 10,
  },
  modalCategoryChipSelected: {
    backgroundColor: '#3b82f6',
  },
  modalCategoryChipText: {
    fontSize: 14,
    color: '#333',
  },
  modalCategoryChipTextSelected: {
    color: '#fff',
  },
  applyButton: {
    backgroundColor: '#3b82f6',
    borderRadius: 8,
    padding: 15,
    alignItems: 'center',
    marginTop: 20,
  },
  applyButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default ProductsScreen;
