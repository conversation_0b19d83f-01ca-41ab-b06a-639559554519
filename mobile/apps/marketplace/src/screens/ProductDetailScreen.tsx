import React, { useState, useEffect } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  ScrollView, 
  Image, 
  TouchableOpacity, 
  ActivityIndicator,
  Alert,
  Share
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation, useRoute } from '@react-navigation/native';

// Mock data for a single product
const mockProduct = {
  id: '1',
  name: 'Organic Tomatoes',
  price: 4.99,
  unit: 'lb',
  category: 'Vegetables',
  seller: {
    id: '1',
    name: 'Green Valley Farm',
    rating: 4.8,
    reviewCount: 156
  },
  description: 'Fresh organic tomatoes grown without pesticides. Perfect for salads, sandwiches, or cooking.',
  images: [
    'https://images.unsplash.com/photo-1592924357228-91a4daadcfea?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1470&q=80',
    'https://images.unsplash.com/photo-1582284540020-8acbe03f4924?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1470&q=80',
  ],
  stock: 48,
  rating: 4.7,
  reviewCount: 32,
  specifications: [
    { name: 'Origin', value: 'Local' },
    { name: 'Organic', value: 'Yes' },
    { name: 'Harvest Date', value: '2023-06-15' },
  ],
  tags: ['Organic', 'Local', 'Fresh', 'Vegetables'],
  relatedProducts: [
    { id: '2', name: 'Organic Cucumbers', price: 3.49, image: 'https://images.unsplash.com/photo-1604977042946-1eecc30f269e?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1470&q=80' },
    { id: '3', name: 'Bell Peppers', price: 2.99, image: 'https://images.unsplash.com/photo-1563565375-f3fdfdbefa83?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1470&q=80' },
    { id: '4', name: 'Fresh Basil', price: 1.99, image: 'https://images.unsplash.com/photo-1600692858906-c11bbc8c4968?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1470&q=80' },
  ]
};

const ProductDetailScreen = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { productId } = route.params || { productId: '1' };
  
  const [product, setProduct] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [quantity, setQuantity] = useState(1);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  
  // Fetch product data
  useEffect(() => {
    // In a real app, this would fetch data from an API
    const fetchProduct = async () => {
      try {
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 500));
        
        // For demo purposes, we'll use the mock data
        setProduct(mockProduct);
        setLoading(false);
      } catch (err) {
        setError('Failed to load product details');
        setLoading(false);
      }
    };
    
    fetchProduct();
  }, [productId]);
  
  // Handle quantity change
  const increaseQuantity = () => {
    if (quantity < product.stock) {
      setQuantity(quantity + 1);
    }
  };
  
  const decreaseQuantity = () => {
    if (quantity > 1) {
      setQuantity(quantity - 1);
    }
  };
  
  // Handle add to cart
  const handleAddToCart = () => {
    Alert.alert(
      'Success',
      `Added ${quantity} ${quantity === 1 ? 'item' : 'items'} to cart`,
      [{ text: 'OK' }]
    );
  };
  
  // Handle buy now
  const handleBuyNow = () => {
    Alert.alert(
      'Proceed to Checkout',
      `You are about to purchase ${quantity} ${quantity === 1 ? 'item' : 'items'}`,
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Checkout', onPress: () => Alert.alert('Success', 'Proceeding to checkout...') }
      ]
    );
  };
  
  // Handle share
  const handleShare = async () => {
    try {
      await Share.share({
        message: `Check out ${product.name} on NxtAcre Marketplace! Price: $${product.price}/${product.unit}`,
        title: 'Share Product',
      });
    } catch (error) {
      Alert.alert('Error', 'Failed to share product');
    }
  };
  
  // Handle contact seller
  const handleContactSeller = () => {
    Alert.alert('Contact Seller', `You are about to message ${product.seller.name}`, [
      { text: 'Cancel', style: 'cancel' },
      { text: 'Message', onPress: () => Alert.alert('Success', 'Opening message thread...') }
    ]);
  };
  
  // Handle view seller
  const handleViewSeller = () => {
    // In a real app, this would navigate to the seller's profile
    Alert.alert('View Seller', `Viewing ${product.seller.name}'s profile`);
  };
  
  // Handle view related product
  const handleViewRelatedProduct = (productId) => {
    // In a real app, this would navigate to the related product's detail screen
    Alert.alert('View Product', `Viewing product with ID: ${productId}`);
  };
  
  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#4CAF50" />
        <Text style={styles.loadingText}>Loading product details...</Text>
      </View>
    );
  }
  
  if (error) {
    return (
      <View style={styles.errorContainer}>
        <Ionicons name="alert-circle-outline" size={48} color="#FF5252" />
        <Text style={styles.errorText}>{error}</Text>
        <TouchableOpacity style={styles.retryButton} onPress={() => navigation.goBack()}>
          <Text style={styles.retryButtonText}>Go Back</Text>
        </TouchableOpacity>
      </View>
    );
  }
  
  if (!product) {
    return (
      <View style={styles.errorContainer}>
        <Ionicons name="alert-circle-outline" size={48} color="#FF5252" />
        <Text style={styles.errorText}>Product not found</Text>
        <TouchableOpacity style={styles.retryButton} onPress={() => navigation.goBack()}>
          <Text style={styles.retryButtonText}>Go Back</Text>
        </TouchableOpacity>
      </View>
    );
  }
  
  return (
    <View style={styles.container}>
      <ScrollView>
        {/* Product Images */}
        <View style={styles.imageContainer}>
          <Image 
            source={{ uri: product.images[selectedImageIndex] }} 
            style={styles.mainImage}
            resizeMode="cover"
          />
          
          {product.images.length > 1 && (
            <View style={styles.thumbnailContainer}>
              {product.images.map((image, index) => (
                <TouchableOpacity 
                  key={index}
                  style={[
                    styles.thumbnailButton,
                    selectedImageIndex === index && styles.selectedThumbnail
                  ]}
                  onPress={() => setSelectedImageIndex(index)}
                >
                  <Image 
                    source={{ uri: image }} 
                    style={styles.thumbnailImage}
                    resizeMode="cover"
                  />
                </TouchableOpacity>
              ))}
            </View>
          )}
        </View>
        
        {/* Product Info */}
        <View style={styles.infoContainer}>
          <Text style={styles.category}>{product.category}</Text>
          <Text style={styles.name}>{product.name}</Text>
          
          <View style={styles.priceRow}>
            <Text style={styles.price}>${product.price}</Text>
            <Text style={styles.unit}>/{product.unit}</Text>
          </View>
          
          <View style={styles.ratingContainer}>
            <View style={styles.starsContainer}>
              {[1, 2, 3, 4, 5].map((star) => (
                <Ionicons 
                  key={star}
                  name={star <= Math.round(product.rating) ? "star" : "star-outline"} 
                  size={16} 
                  color="#FFD700" 
                />
              ))}
            </View>
            <Text style={styles.ratingText}>{product.rating} ({product.reviewCount} reviews)</Text>
          </View>
          
          <View style={styles.sellerContainer}>
            <Text style={styles.sellerLabel}>Sold by:</Text>
            <TouchableOpacity onPress={handleViewSeller}>
              <Text style={styles.sellerName}>{product.seller.name}</Text>
            </TouchableOpacity>
            <View style={styles.sellerRating}>
              <Ionicons name="star" size={14} color="#FFD700" />
              <Text style={styles.sellerRatingText}>{product.seller.rating} ({product.seller.reviewCount})</Text>
            </View>
          </View>
          
          <View style={styles.stockContainer}>
            <Ionicons name="cube-outline" size={16} color="#4CAF50" />
            <Text style={styles.stockText}>
              {product.stock > 0 
                ? `${product.stock} in stock` 
                : 'Out of stock'}
            </Text>
          </View>
        </View>
        
        {/* Quantity Selector */}
        <View style={styles.quantityContainer}>
          <Text style={styles.quantityLabel}>Quantity:</Text>
          <View style={styles.quantitySelector}>
            <TouchableOpacity 
              style={styles.quantityButton}
              onPress={decreaseQuantity}
              disabled={quantity <= 1}
            >
              <Ionicons 
                name="remove" 
                size={20} 
                color={quantity <= 1 ? "#BDBDBD" : "#212121"} 
              />
            </TouchableOpacity>
            
            <Text style={styles.quantityValue}>{quantity}</Text>
            
            <TouchableOpacity 
              style={styles.quantityButton}
              onPress={increaseQuantity}
              disabled={quantity >= product.stock}
            >
              <Ionicons 
                name="add" 
                size={20} 
                color={quantity >= product.stock ? "#BDBDBD" : "#212121"} 
              />
            </TouchableOpacity>
          </View>
        </View>
        
        {/* Description */}
        <View style={styles.sectionContainer}>
          <Text style={styles.sectionTitle}>Description</Text>
          <Text style={styles.description}>{product.description}</Text>
        </View>
        
        {/* Specifications */}
        <View style={styles.sectionContainer}>
          <Text style={styles.sectionTitle}>Specifications</Text>
          {product.specifications.map((spec, index) => (
            <View key={index} style={styles.specRow}>
              <Text style={styles.specName}>{spec.name}</Text>
              <Text style={styles.specValue}>{spec.value}</Text>
            </View>
          ))}
        </View>
        
        {/* Tags */}
        <View style={styles.sectionContainer}>
          <Text style={styles.sectionTitle}>Tags</Text>
          <View style={styles.tagsContainer}>
            {product.tags.map((tag, index) => (
              <View key={index} style={styles.tag}>
                <Text style={styles.tagText}>{tag}</Text>
              </View>
            ))}
          </View>
        </View>
        
        {/* Related Products */}
        <View style={styles.sectionContainer}>
          <Text style={styles.sectionTitle}>You May Also Like</Text>
          <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.relatedProductsScroll}>
            {product.relatedProducts.map((relatedProduct) => (
              <TouchableOpacity 
                key={relatedProduct.id} 
                style={styles.relatedProductCard}
                onPress={() => handleViewRelatedProduct(relatedProduct.id)}
              >
                <Image 
                  source={{ uri: relatedProduct.image }} 
                  style={styles.relatedProductImage}
                  resizeMode="cover"
                />
                <Text style={styles.relatedProductName}>{relatedProduct.name}</Text>
                <Text style={styles.relatedProductPrice}>${relatedProduct.price}</Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>
        
        {/* Seller Contact */}
        <TouchableOpacity style={styles.contactSellerButton} onPress={handleContactSeller}>
          <Ionicons name="chatbubble-outline" size={20} color="#4CAF50" />
          <Text style={styles.contactSellerText}>Contact Seller</Text>
        </TouchableOpacity>
        
        {/* Share Button */}
        <TouchableOpacity style={styles.shareButton} onPress={handleShare}>
          <Ionicons name="share-social-outline" size={20} color="#4CAF50" />
          <Text style={styles.shareButtonText}>Share This Product</Text>
        </TouchableOpacity>
      </ScrollView>
      
      {/* Bottom Action Buttons */}
      <View style={styles.actionButtonsContainer}>
        <TouchableOpacity 
          style={[styles.actionButton, styles.addToCartButton]}
          onPress={handleAddToCart}
        >
          <Ionicons name="cart-outline" size={20} color="#FFFFFF" />
          <Text style={styles.actionButtonText}>Add to Cart</Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={[styles.actionButton, styles.buyNowButton]}
          onPress={handleBuyNow}
        >
          <Text style={styles.actionButtonText}>Buy Now</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#757575',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    padding: 20,
  },
  errorText: {
    marginTop: 10,
    fontSize: 16,
    color: '#757575',
    textAlign: 'center',
    marginBottom: 20,
  },
  retryButton: {
    backgroundColor: '#4CAF50',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  retryButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
  imageContainer: {
    backgroundColor: '#FFFFFF',
    width: '100%',
  },
  mainImage: {
    width: '100%',
    height: 300,
  },
  thumbnailContainer: {
    flexDirection: 'row',
    padding: 10,
    justifyContent: 'center',
  },
  thumbnailButton: {
    width: 60,
    height: 60,
    borderRadius: 5,
    marginHorizontal: 5,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    overflow: 'hidden',
  },
  selectedThumbnail: {
    borderColor: '#4CAF50',
    borderWidth: 2,
  },
  thumbnailImage: {
    width: '100%',
    height: '100%',
  },
  infoContainer: {
    backgroundColor: '#FFFFFF',
    padding: 15,
    marginBottom: 10,
  },
  category: {
    fontSize: 14,
    color: '#757575',
    marginBottom: 5,
  },
  name: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#212121',
    marginBottom: 10,
  },
  priceRow: {
    flexDirection: 'row',
    alignItems: 'baseline',
    marginBottom: 10,
  },
  price: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#4CAF50',
  },
  unit: {
    fontSize: 16,
    color: '#757575',
    marginLeft: 2,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 15,
  },
  starsContainer: {
    flexDirection: 'row',
    marginRight: 5,
  },
  ratingText: {
    fontSize: 14,
    color: '#757575',
  },
  sellerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  sellerLabel: {
    fontSize: 14,
    color: '#757575',
    marginRight: 5,
  },
  sellerName: {
    fontSize: 14,
    fontWeight: '500',
    color: '#4CAF50',
    marginRight: 10,
  },
  sellerRating: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  sellerRatingText: {
    fontSize: 12,
    color: '#757575',
    marginLeft: 2,
  },
  stockContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  stockText: {
    fontSize: 14,
    color: '#4CAF50',
    marginLeft: 5,
  },
  quantityContainer: {
    backgroundColor: '#FFFFFF',
    padding: 15,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 10,
  },
  quantityLabel: {
    fontSize: 16,
    color: '#212121',
  },
  quantitySelector: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 5,
  },
  quantityButton: {
    padding: 10,
    alignItems: 'center',
    justifyContent: 'center',
  },
  quantityValue: {
    paddingHorizontal: 15,
    fontSize: 16,
    fontWeight: '500',
  },
  sectionContainer: {
    backgroundColor: '#FFFFFF',
    padding: 15,
    marginBottom: 10,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#212121',
    marginBottom: 10,
  },
  description: {
    fontSize: 16,
    color: '#424242',
    lineHeight: 24,
  },
  specRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  specName: {
    fontSize: 14,
    color: '#757575',
  },
  specValue: {
    fontSize: 14,
    color: '#212121',
    fontWeight: '500',
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  tag: {
    backgroundColor: '#F0F0F0',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
    marginRight: 8,
    marginBottom: 8,
  },
  tagText: {
    fontSize: 12,
    color: '#757575',
  },
  relatedProductsScroll: {
    marginTop: 5,
  },
  relatedProductCard: {
    width: 150,
    marginRight: 15,
  },
  relatedProductImage: {
    width: '100%',
    height: 100,
    borderRadius: 8,
    marginBottom: 5,
  },
  relatedProductName: {
    fontSize: 14,
    color: '#212121',
    marginBottom: 3,
  },
  relatedProductPrice: {
    fontSize: 14,
    fontWeight: '500',
    color: '#4CAF50',
  },
  contactSellerButton: {
    backgroundColor: '#FFFFFF',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 15,
    marginBottom: 10,
  },
  contactSellerText: {
    fontSize: 16,
    color: '#4CAF50',
    fontWeight: '500',
    marginLeft: 5,
  },
  shareButton: {
    backgroundColor: '#FFFFFF',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 15,
    marginBottom: 80, // Extra space for the bottom action buttons
  },
  shareButtonText: {
    fontSize: 16,
    color: '#4CAF50',
    fontWeight: '500',
    marginLeft: 5,
  },
  actionButtonsContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    flexDirection: 'row',
    backgroundColor: '#FFFFFF',
    padding: 10,
    borderTopWidth: 1,
    borderTopColor: '#E0E0E0',
  },
  actionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderRadius: 8,
    marginHorizontal: 5,
  },
  addToCartButton: {
    backgroundColor: '#4CAF50',
  },
  buyNowButton: {
    backgroundColor: '#FF9800',
  },
  actionButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 5,
  },
});

export default ProductDetailScreen;