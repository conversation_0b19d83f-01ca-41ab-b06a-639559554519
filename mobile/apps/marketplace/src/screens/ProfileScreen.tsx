import React, { useState, useEffect } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  ScrollView, 
  TouchableOpacity, 
  Image, 
  ActivityIndicator,
  RefreshControl,
  Switch,
  Alert
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { MainStackParamList } from '../navigation/MainNavigator';
import { useAuth } from '../../../../shared/store/AuthProvider';

type NavigationProp = NativeStackNavigationProp<MainStackParamList>;

// Mock data for user profile
const USER_PROFILE = {
  name: '<PERSON>',
  email: '<EMAIL>',
  phone: '(*************',
  profileImage: 'https://via.placeholder.com/150',
  address: {
    street: '123 Farm Road',
    city: 'Farmville',
    state: 'CA',
    zipCode: '95123'
  },
  isSeller: true,
  notificationSettings: {
    orderUpdates: true,
    promotions: false,
    newProducts: true,
    messages: true
  },
  paymentMethods: [
    { id: '1', type: 'visa', last4: '4242', isDefault: true },
    { id: '2', type: 'mastercard', last4: '5555', isDefault: false }
  ]
};

const ProfileScreen: React.FC = () => {
  const navigation = useNavigation<NavigationProp>();
  const { logout } = useAuth();
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [userProfile, setUserProfile] = useState(USER_PROFILE);
  const [notificationSettings, setNotificationSettings] = useState(USER_PROFILE.notificationSettings);

  useEffect(() => {
    // Simulate loading data
    const timer = setTimeout(() => {
      setLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  const onRefresh = React.useCallback(() => {
    setRefreshing(true);
    // Simulate refreshing data
    setTimeout(() => {
      setRefreshing(false);
    }, 1000);
  }, []);

  const handleEditProfile = () => {
    // Navigate to edit profile screen
    console.log('Edit profile pressed');
  };

  const handlePaymentMethodsPress = () => {
    navigation.navigate('PaymentMethods');
  };

  const handleMessagesPress = () => {
    navigation.navigate('Messages');
  };

  const handleReviewsPress = () => {
    navigation.navigate('Reviews', { sellerId: 'current-user-id' });
  };

  const handleLogout = () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        {
          text: 'Cancel',
          style: 'cancel'
        },
        {
          text: 'Logout',
          onPress: () => logout()
        }
      ]
    );
  };

  const toggleNotificationSetting = (setting: keyof typeof notificationSettings) => {
    setNotificationSettings(prev => ({
      ...prev,
      [setting]: !prev[setting]
    }));
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#3b82f6" />
      </View>
    );
  }

  return (
    <ScrollView 
      style={styles.container}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
      }
    >
      {/* Profile Header */}
      <View style={styles.profileHeader}>
        <Image 
          source={{ uri: userProfile.profileImage }} 
          style={styles.profileImage} 
        />
        <View style={styles.profileInfo}>
          <Text style={styles.profileName}>{userProfile.name}</Text>
          <Text style={styles.profileEmail}>{userProfile.email}</Text>
          {userProfile.isSeller && (
            <View style={styles.sellerBadge}>
              <Ionicons name="storefront" size={12} color="#fff" />
              <Text style={styles.sellerBadgeText}>Seller</Text>
            </View>
          )}
        </View>
        <TouchableOpacity 
          style={styles.editButton}
          onPress={handleEditProfile}
        >
          <Ionicons name="pencil" size={16} color="#3b82f6" />
          <Text style={styles.editButtonText}>Edit</Text>
        </TouchableOpacity>
      </View>

      {/* Account Section */}
      <View style={styles.sectionContainer}>
        <Text style={styles.sectionTitle}>Account</Text>
        <TouchableOpacity style={styles.menuItem}>
          <Ionicons name="person-outline" size={24} color="#3b82f6" style={styles.menuIcon} />
          <View style={styles.menuTextContainer}>
            <Text style={styles.menuTitle}>Personal Information</Text>
            <Text style={styles.menuDescription}>Update your personal details</Text>
          </View>
          <Ionicons name="chevron-forward" size={20} color="#999" />
        </TouchableOpacity>
        <TouchableOpacity style={styles.menuItem}>
          <Ionicons name="lock-closed-outline" size={24} color="#3b82f6" style={styles.menuIcon} />
          <View style={styles.menuTextContainer}>
            <Text style={styles.menuTitle}>Security</Text>
            <Text style={styles.menuDescription}>Change password and security settings</Text>
          </View>
          <Ionicons name="chevron-forward" size={20} color="#999" />
        </TouchableOpacity>
        <TouchableOpacity style={styles.menuItem}>
          <Ionicons name="location-outline" size={24} color="#3b82f6" style={styles.menuIcon} />
          <View style={styles.menuTextContainer}>
            <Text style={styles.menuTitle}>Addresses</Text>
            <Text style={styles.menuDescription}>Manage your addresses</Text>
          </View>
          <Ionicons name="chevron-forward" size={20} color="#999" />
        </TouchableOpacity>
        <TouchableOpacity 
          style={styles.menuItem}
          onPress={handlePaymentMethodsPress}
        >
          <Ionicons name="card-outline" size={24} color="#3b82f6" style={styles.menuIcon} />
          <View style={styles.menuTextContainer}>
            <Text style={styles.menuTitle}>Payment Methods</Text>
            <Text style={styles.menuDescription}>Manage your payment options</Text>
          </View>
          <Ionicons name="chevron-forward" size={20} color="#999" />
        </TouchableOpacity>
      </View>

      {/* Marketplace Section */}
      <View style={styles.sectionContainer}>
        <Text style={styles.sectionTitle}>Marketplace</Text>
        <TouchableOpacity style={styles.menuItem}>
          <Ionicons name="heart-outline" size={24} color="#3b82f6" style={styles.menuIcon} />
          <View style={styles.menuTextContainer}>
            <Text style={styles.menuTitle}>Favorites</Text>
            <Text style={styles.menuDescription}>View your favorite products</Text>
          </View>
          <Ionicons name="chevron-forward" size={20} color="#999" />
        </TouchableOpacity>
        <TouchableOpacity 
          style={styles.menuItem}
          onPress={handleMessagesPress}
        >
          <Ionicons name="chatbubbles-outline" size={24} color="#3b82f6" style={styles.menuIcon} />
          <View style={styles.menuTextContainer}>
            <Text style={styles.menuTitle}>Messages</Text>
            <Text style={styles.menuDescription}>View your conversations</Text>
          </View>
          <Ionicons name="chevron-forward" size={20} color="#999" />
        </TouchableOpacity>
        <TouchableOpacity 
          style={styles.menuItem}
          onPress={handleReviewsPress}
        >
          <Ionicons name="star-outline" size={24} color="#3b82f6" style={styles.menuIcon} />
          <View style={styles.menuTextContainer}>
            <Text style={styles.menuTitle}>Reviews</Text>
            <Text style={styles.menuDescription}>View your reviews</Text>
          </View>
          <Ionicons name="chevron-forward" size={20} color="#999" />
        </TouchableOpacity>
        {userProfile.isSeller && (
          <TouchableOpacity style={styles.menuItem}>
            <Ionicons name="storefront-outline" size={24} color="#3b82f6" style={styles.menuIcon} />
            <View style={styles.menuTextContainer}>
              <Text style={styles.menuTitle}>Seller Account</Text>
              <Text style={styles.menuDescription}>Manage your seller profile</Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color="#999" />
          </TouchableOpacity>
        )}
      </View>

      {/* Notifications Section */}
      <View style={styles.sectionContainer}>
        <Text style={styles.sectionTitle}>Notifications</Text>
        <View style={styles.settingItem}>
          <View style={styles.settingTextContainer}>
            <Text style={styles.settingTitle}>Order Updates</Text>
            <Text style={styles.settingDescription}>Receive notifications about your orders</Text>
          </View>
          <Switch
            value={notificationSettings.orderUpdates}
            onValueChange={() => toggleNotificationSetting('orderUpdates')}
            trackColor={{ false: '#d1d5db', true: '#bfdbfe' }}
            thumbColor={notificationSettings.orderUpdates ? '#3b82f6' : '#f4f4f5'}
          />
        </View>
        <View style={styles.settingItem}>
          <View style={styles.settingTextContainer}>
            <Text style={styles.settingTitle}>Promotions</Text>
            <Text style={styles.settingDescription}>Receive notifications about deals and promotions</Text>
          </View>
          <Switch
            value={notificationSettings.promotions}
            onValueChange={() => toggleNotificationSetting('promotions')}
            trackColor={{ false: '#d1d5db', true: '#bfdbfe' }}
            thumbColor={notificationSettings.promotions ? '#3b82f6' : '#f4f4f5'}
          />
        </View>
        <View style={styles.settingItem}>
          <View style={styles.settingTextContainer}>
            <Text style={styles.settingTitle}>New Products</Text>
            <Text style={styles.settingDescription}>Receive notifications about new products</Text>
          </View>
          <Switch
            value={notificationSettings.newProducts}
            onValueChange={() => toggleNotificationSetting('newProducts')}
            trackColor={{ false: '#d1d5db', true: '#bfdbfe' }}
            thumbColor={notificationSettings.newProducts ? '#3b82f6' : '#f4f4f5'}
          />
        </View>
        <View style={styles.settingItem}>
          <View style={styles.settingTextContainer}>
            <Text style={styles.settingTitle}>Messages</Text>
            <Text style={styles.settingDescription}>Receive notifications about new messages</Text>
          </View>
          <Switch
            value={notificationSettings.messages}
            onValueChange={() => toggleNotificationSetting('messages')}
            trackColor={{ false: '#d1d5db', true: '#bfdbfe' }}
            thumbColor={notificationSettings.messages ? '#3b82f6' : '#f4f4f5'}
          />
        </View>
      </View>

      {/* Support Section */}
      <View style={styles.sectionContainer}>
        <Text style={styles.sectionTitle}>Support</Text>
        <TouchableOpacity style={styles.menuItem}>
          <Ionicons name="help-circle-outline" size={24} color="#3b82f6" style={styles.menuIcon} />
          <View style={styles.menuTextContainer}>
            <Text style={styles.menuTitle}>Help Center</Text>
            <Text style={styles.menuDescription}>Get help with your account</Text>
          </View>
          <Ionicons name="chevron-forward" size={20} color="#999" />
        </TouchableOpacity>
        <TouchableOpacity style={styles.menuItem}>
          <Ionicons name="document-text-outline" size={24} color="#3b82f6" style={styles.menuIcon} />
          <View style={styles.menuTextContainer}>
            <Text style={styles.menuTitle}>Terms of Service</Text>
            <Text style={styles.menuDescription}>Read our terms of service</Text>
          </View>
          <Ionicons name="chevron-forward" size={20} color="#999" />
        </TouchableOpacity>
        <TouchableOpacity style={styles.menuItem}>
          <Ionicons name="shield-checkmark-outline" size={24} color="#3b82f6" style={styles.menuIcon} />
          <View style={styles.menuTextContainer}>
            <Text style={styles.menuTitle}>Privacy Policy</Text>
            <Text style={styles.menuDescription}>Read our privacy policy</Text>
          </View>
          <Ionicons name="chevron-forward" size={20} color="#999" />
        </TouchableOpacity>
      </View>

      {/* Logout Button */}
      <TouchableOpacity 
        style={styles.logoutButton}
        onPress={handleLogout}
      >
        <Ionicons name="log-out-outline" size={20} color="#ef4444" />
        <Text style={styles.logoutButtonText}>Logout</Text>
      </TouchableOpacity>

      {/* App Version */}
      <Text style={styles.versionText}>Version 1.0.0</Text>

      {/* Bottom padding */}
      <View style={{ height: 20 }} />
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  profileHeader: {
    flexDirection: 'row',
    padding: 20,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  profileImage: {
    width: 70,
    height: 70,
    borderRadius: 35,
    marginRight: 15,
  },
  profileInfo: {
    flex: 1,
    justifyContent: 'center',
  },
  profileName: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  profileEmail: {
    fontSize: 14,
    color: '#666',
    marginTop: 4,
  },
  sellerBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#3b82f6',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    alignSelf: 'flex-start',
    marginTop: 6,
  },
  sellerBadgeText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
    marginLeft: 4,
  },
  editButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
  },
  editButtonText: {
    color: '#3b82f6',
    marginLeft: 4,
  },
  sectionContainer: {
    backgroundColor: '#fff',
    marginTop: 10,
    paddingVertical: 10,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    paddingHorizontal: 15,
    paddingVertical: 10,
    color: '#333',
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  menuIcon: {
    marginRight: 15,
  },
  menuTextContainer: {
    flex: 1,
  },
  menuTitle: {
    fontSize: 16,
    fontWeight: '500',
  },
  menuDescription: {
    fontSize: 12,
    color: '#666',
    marginTop: 2,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  settingTextContainer: {
    flex: 1,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '500',
  },
  settingDescription: {
    fontSize: 12,
    color: '#666',
    marginTop: 2,
  },
  logoutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#fff',
    marginTop: 10,
    paddingVertical: 15,
  },
  logoutButtonText: {
    color: '#ef4444',
    fontSize: 16,
    fontWeight: '500',
    marginLeft: 8,
  },
  versionText: {
    textAlign: 'center',
    color: '#999',
    fontSize: 12,
    marginTop: 10,
  },
});

export default ProfileScreen;