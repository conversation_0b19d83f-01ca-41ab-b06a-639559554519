import React, { useState, useEffect } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  FlatList, 
  TouchableOpacity, 
  ActivityIndicator,
  RefreshControl,
  TextInput
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { MainStackParamList } from '../navigation/MainNavigator';

type NavigationProp = NativeStackNavigationProp<MainStackParamList>;

// Mock data for orders
const ORDERS = [
  { 
    id: '1', 
    orderNumber: 'ORD-2023-001', 
    date: '2023-06-15', 
    status: 'delivered', 
    total: 45.99, 
    items: 3,
    seller: 'Green Valley Farm'
  },
  { 
    id: '2', 
    orderNumber: 'ORD-2023-002', 
    date: '2023-06-20', 
    status: 'processing', 
    total: 32.50, 
    items: 2,
    seller: 'Happy Hens Farm'
  },
  { 
    id: '3', 
    orderNumber: 'ORD-2023-003', 
    date: '2023-06-25', 
    status: 'shipped', 
    total: 78.25, 
    items: 5,
    seller: 'Meadow Ranch'
  },
  { 
    id: '4', 
    orderNumber: 'ORD-2023-004', 
    date: '2023-06-28', 
    status: 'cancelled', 
    total: 15.99, 
    items: 1,
    seller: 'Farm Supply Co'
  },
  { 
    id: '5', 
    orderNumber: 'ORD-2023-005', 
    date: '2023-07-02', 
    status: 'pending', 
    total: 54.75, 
    items: 4,
    seller: 'Green Valley Farm'
  },
];

type OrderStatus = 'all' | 'pending' | 'processing' | 'shipped' | 'delivered' | 'cancelled';

const OrdersScreen: React.FC = () => {
  const navigation = useNavigation<NavigationProp>();
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [orders, setOrders] = useState<typeof ORDERS>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<OrderStatus>('all');

  useEffect(() => {
    // Simulate loading data
    const timer = setTimeout(() => {
      filterOrders();
      setLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  const filterOrders = () => {
    let filtered = [...ORDERS];
    
    // Apply status filter
    if (statusFilter !== 'all') {
      filtered = filtered.filter(order => order.status === statusFilter);
    }
    
    // Apply search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(
        order => 
          order.orderNumber.toLowerCase().includes(query) || 
          order.seller.toLowerCase().includes(query)
      );
    }
    
    // Sort by date (newest first)
    filtered.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
    
    setOrders(filtered);
  };

  useEffect(() => {
    filterOrders();
  }, [searchQuery, statusFilter]);

  const onRefresh = React.useCallback(() => {
    setRefreshing(true);
    // Simulate refreshing data
    setTimeout(() => {
      filterOrders();
      setRefreshing(false);
    }, 1000);
  }, [searchQuery, statusFilter]);

  const handleOrderPress = (orderId: string) => {
    navigation.navigate('OrderDetail', { orderId });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return '#f59e0b'; // Amber
      case 'processing':
        return '#3b82f6'; // Blue
      case 'shipped':
        return '#8b5cf6'; // Purple
      case 'delivered':
        return '#10b981'; // Green
      case 'cancelled':
        return '#ef4444'; // Red
      default:
        return '#6b7280'; // Gray
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return 'time-outline';
      case 'processing':
        return 'construct-outline';
      case 'shipped':
        return 'car-outline';
      case 'delivered':
        return 'checkmark-circle-outline';
      case 'cancelled':
        return 'close-circle-outline';
      default:
        return 'help-circle-outline';
    }
  };

  const formatDate = (dateString: string) => {
    const options: Intl.DateTimeFormatOptions = { year: 'numeric', month: 'short', day: 'numeric' };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  const renderOrderItem = ({ item }: { item: typeof ORDERS[0] }) => (
    <TouchableOpacity 
      style={styles.orderCard}
      onPress={() => handleOrderPress(item.id)}
    >
      <View style={styles.orderHeader}>
        <Text style={styles.orderNumber}>{item.orderNumber}</Text>
        <View style={[styles.statusBadge, { backgroundColor: getStatusColor(item.status) }]}>
          <Ionicons name={getStatusIcon(item.status)} size={14} color="#fff" style={styles.statusIcon} />
          <Text style={styles.statusText}>{item.status.charAt(0).toUpperCase() + item.status.slice(1)}</Text>
        </View>
      </View>
      <View style={styles.orderDetails}>
        <View style={styles.orderDetail}>
          <Text style={styles.orderDetailLabel}>Date:</Text>
          <Text style={styles.orderDetailValue}>{formatDate(item.date)}</Text>
        </View>
        <View style={styles.orderDetail}>
          <Text style={styles.orderDetailLabel}>Items:</Text>
          <Text style={styles.orderDetailValue}>{item.items}</Text>
        </View>
        <View style={styles.orderDetail}>
          <Text style={styles.orderDetailLabel}>Seller:</Text>
          <Text style={styles.orderDetailValue}>{item.seller}</Text>
        </View>
        <View style={styles.orderDetail}>
          <Text style={styles.orderDetailLabel}>Total:</Text>
          <Text style={styles.orderDetailValue}>${item.total.toFixed(2)}</Text>
        </View>
      </View>
      <View style={styles.orderFooter}>
        <TouchableOpacity style={styles.orderAction}>
          <Ionicons name="chatbubble-outline" size={16} color="#3b82f6" />
          <Text style={styles.orderActionText}>Contact Seller</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.orderAction}>
          <Ionicons name="document-text-outline" size={16} color="#3b82f6" />
          <Text style={styles.orderActionText}>View Details</Text>
        </TouchableOpacity>
      </View>
    </TouchableOpacity>
  );

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#3b82f6" />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Search bar */}
      <View style={styles.searchContainer}>
        <Ionicons name="search" size={20} color="#666" style={styles.searchIcon} />
        <TextInput
          style={styles.searchInput}
          placeholder="Search orders by number or seller..."
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
        {searchQuery ? (
          <TouchableOpacity onPress={() => setSearchQuery('')}>
            <Ionicons name="close-circle" size={20} color="#666" />
          </TouchableOpacity>
        ) : null}
      </View>

      {/* Status filter tabs */}
      <View style={styles.filterTabs}>
        <ScrollableTab 
          tabs={[
            { id: 'all', label: 'All' },
            { id: 'pending', label: 'Pending' },
            { id: 'processing', label: 'Processing' },
            { id: 'shipped', label: 'Shipped' },
            { id: 'delivered', label: 'Delivered' },
            { id: 'cancelled', label: 'Cancelled' }
          ]}
          activeTab={statusFilter}
          onTabPress={(tabId) => setStatusFilter(tabId as OrderStatus)}
        />
      </View>

      {/* Orders list */}
      <FlatList
        data={orders}
        renderItem={renderOrderItem}
        keyExtractor={item => item.id}
        contentContainerStyle={styles.ordersContainer}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Ionicons name="receipt-outline" size={48} color="#ccc" />
            <Text style={styles.emptyText}>No orders found</Text>
            <Text style={styles.emptySubtext}>
              {statusFilter !== 'all' 
                ? `You don't have any ${statusFilter} orders` 
                : 'Start shopping to see your orders here'}
            </Text>
            {statusFilter !== 'all' && (
              <TouchableOpacity 
                style={styles.emptyButton}
                onPress={() => setStatusFilter('all')}
              >
                <Text style={styles.emptyButtonText}>View All Orders</Text>
              </TouchableOpacity>
            )}
          </View>
        }
      />
    </View>
  );
};

// Scrollable tabs component
const ScrollableTab = ({ 
  tabs, 
  activeTab, 
  onTabPress 
}: { 
  tabs: Array<{ id: string, label: string }>, 
  activeTab: string, 
  onTabPress: (tabId: string) => void 
}) => {
  return (
    <ScrollView 
      horizontal 
      showsHorizontalScrollIndicator={false} 
      contentContainerStyle={styles.tabsContainer}
    >
      {tabs.map(tab => (
        <TouchableOpacity
          key={tab.id}
          style={[
            styles.tab,
            activeTab === tab.id && styles.activeTab
          ]}
          onPress={() => onTabPress(tab.id)}
        >
          <Text 
            style={[
              styles.tabText,
              activeTab === tab.id && styles.activeTabText
            ]}
          >
            {tab.label}
          </Text>
        </TouchableOpacity>
      ))}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderRadius: 8,
    margin: 15,
    paddingHorizontal: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
  },
  searchIcon: {
    marginRight: 10,
  },
  searchInput: {
    flex: 1,
    height: 40,
    fontSize: 16,
  },
  filterTabs: {
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  tabsContainer: {
    paddingHorizontal: 15,
  },
  tab: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    marginRight: 8,
    borderBottomWidth: 2,
    borderBottomColor: 'transparent',
  },
  activeTab: {
    borderBottomColor: '#3b82f6',
  },
  tabText: {
    fontSize: 14,
    color: '#666',
  },
  activeTabText: {
    color: '#3b82f6',
    fontWeight: 'bold',
  },
  ordersContainer: {
    padding: 15,
  },
  orderCard: {
    backgroundColor: '#fff',
    borderRadius: 8,
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
    overflow: 'hidden',
  },
  orderHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  orderNumber: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusIcon: {
    marginRight: 4,
  },
  statusText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  orderDetails: {
    padding: 15,
  },
  orderDetail: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  orderDetailLabel: {
    width: 60,
    fontSize: 14,
    color: '#666',
  },
  orderDetailValue: {
    flex: 1,
    fontSize: 14,
    fontWeight: '500',
  },
  orderFooter: {
    flexDirection: 'row',
    borderTopWidth: 1,
    borderTopColor: '#eee',
  },
  orderAction: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
  },
  orderActionText: {
    marginLeft: 6,
    color: '#3b82f6',
    fontSize: 14,
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 50,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 10,
    color: '#333',
  },
  emptySubtext: {
    fontSize: 14,
    color: '#666',
    marginTop: 5,
    textAlign: 'center',
  },
  emptyButton: {
    marginTop: 15,
    paddingVertical: 8,
    paddingHorizontal: 16,
    backgroundColor: '#3b82f6',
    borderRadius: 20,
  },
  emptyButtonText: {
    color: '#fff',
    fontWeight: 'bold',
  },
});

export default OrdersScreen;