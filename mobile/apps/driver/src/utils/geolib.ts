// Geofencing utility functions

/**
 * Checks if a point is inside a circle
 * @param point The point to check (latitude, longitude)
 * @param center The center of the circle (latitude, longitude)
 * @param radius The radius of the circle in meters
 * @returns True if the point is inside the circle, false otherwise
 */
export const isPointInCircle = (
  point: { latitude: number; longitude: number },
  center: { latitude: number; longitude: number },
  radius: number
): boolean => {
  // Calculate distance between point and center using Haversine formula
  const distance = getDistance(point, center);
  return distance <= radius;
};

/**
 * Checks if a point is inside a polygon
 * @param point The point to check (latitude, longitude)
 * @param polygon An array of points (latitude, longitude) that form the polygon
 * @returns True if the point is inside the polygon, false otherwise
 */
export const isPointInPolygon = (
  point: { latitude: number; longitude: number },
  polygon: Array<{ latitude: number; longitude: number }>
): boolean => {
  // Ray casting algorithm for point in polygon detection
  let isInside = false;
  const x = point.longitude;
  const y = point.latitude;

  for (let i = 0, j = polygon.length - 1; i < polygon.length; j = i++) {
    const xi = polygon[i].longitude;
    const yi = polygon[i].latitude;
    const xj = polygon[j].longitude;
    const yj = polygon[j].latitude;

    const intersect = ((yi > y) !== (yj > y)) && (x < (xj - xi) * (y - yi) / (yj - yi) + xi);
    if (intersect) isInside = !isInside;
  }

  return isInside;
};

/**
 * Calculates the distance between two points using the Haversine formula
 * @param point1 The first point (latitude, longitude)
 * @param point2 The second point (latitude, longitude)
 * @returns The distance in meters
 */
export const getDistance = (
  point1: { latitude: number; longitude: number },
  point2: { latitude: number; longitude: number }
): number => {
  const R = 6371e3; // Earth's radius in meters
  const φ1 = (point1.latitude * Math.PI) / 180;
  const φ2 = (point2.latitude * Math.PI) / 180;
  const Δφ = ((point2.latitude - point1.latitude) * Math.PI) / 180;
  const Δλ = ((point2.longitude - point1.longitude) * Math.PI) / 180;

  const a =
    Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
    Math.cos(φ1) * Math.cos(φ2) * Math.sin(Δλ / 2) * Math.sin(Δλ / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

  return R * c;
};

// Export as a namespace for easier importing
export const geolib = {
  isPointInCircle,
  isPointInPolygon,
  getDistance,
};