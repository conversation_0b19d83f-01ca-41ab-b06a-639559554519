import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Switch, TextInput, Alert } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import navigationService, { NavigationConfig } from '../../../../shared/services/navigationService';

// Default vehicle profiles
const vehicleProfiles = [
  {
    type: 'car',
    name: 'Car',
    icon: 'car-outline',
    dimensions: {
      height: 5.5, // feet
      width: 6.5, // feet
      length: 16, // feet
      weight: 4000 // pounds
    }
  },
  {
    type: 'van',
    name: 'Van',
    icon: 'car-sport-outline',
    dimensions: {
      height: 8, // feet
      width: 7, // feet
      length: 20, // feet
      weight: 8000 // pounds
    }
  },
  {
    type: 'truck',
    name: 'Box Truck',
    icon: 'cube-outline',
    dimensions: {
      height: 13.5, // feet
      width: 8.5, // feet
      length: 33, // feet
      weight: 26000 // pounds
    }
  },
  {
    type: 'semi',
    name: 'Semi-Truck',
    icon: 'bus-outline',
    dimensions: {
      height: 13.5, // feet
      width: 8.5, // feet
      length: 53, // feet
      weight: 80000 // pounds
    }
  }
];

const VehicleProfileScreen: React.FC = () => {
  const navigation = useNavigation();
  
  // State for vehicle profile
  const [selectedVehicleType, setSelectedVehicleType] = useState<'car' | 'truck' | 'van' | 'semi'>('truck');
  const [height, setHeight] = useState<string>('13.5');
  const [width, setWidth] = useState<string>('8.5');
  const [length, setLength] = useState<string>('33');
  const [weight, setWeight] = useState<string>('26000');
  
  // State for routing preferences
  const [avoidTolls, setAvoidTolls] = useState<boolean>(false);
  const [avoidHighways, setAvoidHighways] = useState<boolean>(false);
  const [avoidFerries, setAvoidFerries] = useState<boolean>(true);
  const [preferFuelEfficient, setPreferFuelEfficient] = useState<boolean>(true);

  // Load current vehicle profile on mount
  useEffect(() => {
    // In a real app, this would load from persistent storage
    // For now, we'll just use the default profile from the navigation service
    const profile = navigationService.getDefaultVehicleProfile(selectedVehicleType);
    setHeight(profile.height.toString());
    setWidth(profile.width.toString());
    setLength(profile.length.toString());
    setWeight(profile.weight.toString());
  }, [selectedVehicleType]);

  // Select a vehicle type
  const selectVehicleType = (type: 'car' | 'truck' | 'van' | 'semi') => {
    setSelectedVehicleType(type);
    const profile = navigationService.getDefaultVehicleProfile(type);
    setHeight(profile.height.toString());
    setWidth(profile.width.toString());
    setLength(profile.length.toString());
    setWeight(profile.weight.toString());
  };

  // Save vehicle profile
  const saveProfile = () => {
    try {
      // Validate inputs
      const heightValue = parseFloat(height);
      const widthValue = parseFloat(width);
      const lengthValue = parseFloat(length);
      const weightValue = parseFloat(weight);

      if (isNaN(heightValue) || isNaN(widthValue) || isNaN(lengthValue) || isNaN(weightValue)) {
        Alert.alert('Invalid Input', 'Please enter valid numbers for all dimensions.');
        return;
      }

      // Create vehicle profile
      const vehicleProfile: NavigationConfig = {
        vehicleType: selectedVehicleType,
        dimensions: {
          height: heightValue,
          width: widthValue,
          length: lengthValue,
          weight: weightValue
        },
        routingPreferences: {
          avoidTolls,
          avoidHighways,
          avoidFerries,
          preferFuelEfficient
        }
      };

      // Initialize navigation service with new profile
      navigationService.initialize(vehicleProfile);

      // In a real app, save to persistent storage
      
      Alert.alert('Success', 'Vehicle profile saved successfully.', [
        { text: 'OK', onPress: () => navigation.goBack() }
      ]);
    } catch (error) {
      console.error('Error saving vehicle profile:', error);
      Alert.alert('Error', 'Failed to save vehicle profile. Please try again.');
    }
  };

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color="#333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Vehicle Profile</Text>
        <TouchableOpacity 
          style={styles.saveButton}
          onPress={saveProfile}
        >
          <Text style={styles.saveButtonText}>Save</Text>
        </TouchableOpacity>
      </View>

      {/* Vehicle Type Selection */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Vehicle Type</Text>
        <View style={styles.vehicleTypeContainer}>
          {vehicleProfiles.map(profile => (
            <TouchableOpacity
              key={profile.type}
              style={[
                styles.vehicleTypeOption,
                selectedVehicleType === profile.type && styles.selectedVehicleType
              ]}
              onPress={() => selectVehicleType(profile.type as 'car' | 'truck' | 'van' | 'semi')}
            >
              <Ionicons 
                name={profile.icon as any} 
                size={32} 
                color={selectedVehicleType === profile.type ? '#8b5cf6' : '#6b7280'} 
              />
              <Text style={[
                styles.vehicleTypeName,
                selectedVehicleType === profile.type && styles.selectedVehicleTypeName
              ]}>
                {profile.name}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      {/* Vehicle Dimensions */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Vehicle Dimensions</Text>
        
        <View style={styles.inputRow}>
          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Height (ft)</Text>
            <TextInput
              style={styles.input}
              value={height}
              onChangeText={setHeight}
              keyboardType="decimal-pad"
              placeholder="Height"
            />
          </View>
          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Width (ft)</Text>
            <TextInput
              style={styles.input}
              value={width}
              onChangeText={setWidth}
              keyboardType="decimal-pad"
              placeholder="Width"
            />
          </View>
        </View>

        <View style={styles.inputRow}>
          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Length (ft)</Text>
            <TextInput
              style={styles.input}
              value={length}
              onChangeText={setLength}
              keyboardType="decimal-pad"
              placeholder="Length"
            />
          </View>
          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Weight (lbs)</Text>
            <TextInput
              style={styles.input}
              value={weight}
              onChangeText={setWeight}
              keyboardType="decimal-pad"
              placeholder="Weight"
            />
          </View>
        </View>
      </View>

      {/* Routing Preferences */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Routing Preferences</Text>
        
        <View style={styles.preferenceItem}>
          <View style={styles.preferenceTextContainer}>
            <Text style={styles.preferenceName}>Avoid Toll Roads</Text>
            <Text style={styles.preferenceDescription}>
              Choose routes that avoid toll roads when possible
            </Text>
          </View>
          <Switch
            value={avoidTolls}
            onValueChange={setAvoidTolls}
            trackColor={{ false: '#d1d5db', true: '#c4b5fd' }}
            thumbColor={avoidTolls ? '#8b5cf6' : '#f4f3f4'}
          />
        </View>

        <View style={styles.preferenceItem}>
          <View style={styles.preferenceTextContainer}>
            <Text style={styles.preferenceName}>Avoid Highways</Text>
            <Text style={styles.preferenceDescription}>
              Choose routes that avoid highways when possible
            </Text>
          </View>
          <Switch
            value={avoidHighways}
            onValueChange={setAvoidHighways}
            trackColor={{ false: '#d1d5db', true: '#c4b5fd' }}
            thumbColor={avoidHighways ? '#8b5cf6' : '#f4f3f4'}
          />
        </View>

        <View style={styles.preferenceItem}>
          <View style={styles.preferenceTextContainer}>
            <Text style={styles.preferenceName}>Avoid Ferries</Text>
            <Text style={styles.preferenceDescription}>
              Choose routes that avoid ferry crossings
            </Text>
          </View>
          <Switch
            value={avoidFerries}
            onValueChange={setAvoidFerries}
            trackColor={{ false: '#d1d5db', true: '#c4b5fd' }}
            thumbColor={avoidFerries ? '#8b5cf6' : '#f4f3f4'}
          />
        </View>

        <View style={styles.preferenceItem}>
          <View style={styles.preferenceTextContainer}>
            <Text style={styles.preferenceName}>Prefer Fuel-Efficient Routes</Text>
            <Text style={styles.preferenceDescription}>
              Choose routes optimized for fuel efficiency
            </Text>
          </View>
          <Switch
            value={preferFuelEfficient}
            onValueChange={setPreferFuelEfficient}
            trackColor={{ false: '#d1d5db', true: '#c4b5fd' }}
            thumbColor={preferFuelEfficient ? '#8b5cf6' : '#f4f3f4'}
          />
        </View>
      </View>

      {/* Information Section */}
      <View style={styles.infoSection}>
        <Ionicons name="information-circle-outline" size={20} color="#6b7280" />
        <Text style={styles.infoText}>
          Accurate vehicle dimensions help ensure you're routed on roads suitable for your vehicle. 
          Large vehicles may be restricted from certain roads due to height, width, or weight limitations.
        </Text>
      </View>

      {/* Save Button */}
      <TouchableOpacity 
        style={styles.saveProfileButton}
        onPress={saveProfile}
      >
        <Text style={styles.saveProfileButtonText}>Save Profile</Text>
      </TouchableOpacity>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9fafb',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1f2937',
  },
  saveButton: {
    padding: 8,
  },
  saveButtonText: {
    color: '#8b5cf6',
    fontWeight: '600',
  },
  section: {
    backgroundColor: 'white',
    marginTop: 16,
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderTopWidth: 1,
    borderBottomWidth: 1,
    borderColor: '#e5e7eb',
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 16,
  },
  vehicleTypeContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    flexWrap: 'wrap',
  },
  vehicleTypeOption: {
    width: '48%',
    backgroundColor: '#f9fafb',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#e5e7eb',
    padding: 12,
    alignItems: 'center',
    marginBottom: 12,
  },
  selectedVehicleType: {
    borderColor: '#8b5cf6',
    backgroundColor: '#f3f0ff',
  },
  vehicleTypeName: {
    marginTop: 8,
    fontSize: 14,
    color: '#4b5563',
  },
  selectedVehicleTypeName: {
    color: '#8b5cf6',
    fontWeight: '500',
  },
  inputRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  inputGroup: {
    width: '48%',
  },
  inputLabel: {
    fontSize: 14,
    color: '#4b5563',
    marginBottom: 6,
  },
  input: {
    backgroundColor: '#f9fafb',
    borderWidth: 1,
    borderColor: '#e5e7eb',
    borderRadius: 6,
    paddingHorizontal: 12,
    paddingVertical: 8,
    fontSize: 16,
  },
  preferenceItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  preferenceTextContainer: {
    flex: 1,
    marginRight: 16,
  },
  preferenceName: {
    fontSize: 16,
    color: '#1f2937',
    marginBottom: 4,
  },
  preferenceDescription: {
    fontSize: 14,
    color: '#6b7280',
  },
  infoSection: {
    flexDirection: 'row',
    backgroundColor: '#f3f0ff',
    marginTop: 16,
    marginHorizontal: 16,
    padding: 12,
    borderRadius: 8,
    alignItems: 'flex-start',
  },
  infoText: {
    flex: 1,
    marginLeft: 8,
    fontSize: 14,
    color: '#6b7280',
    lineHeight: 20,
  },
  saveProfileButton: {
    backgroundColor: '#8b5cf6',
    marginHorizontal: 16,
    marginVertical: 24,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  saveProfileButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default VehicleProfileScreen;