import React, { useState, useEffect, useRef } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Alert, ActivityIndicator, Dimensions, Switch } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import MapView, { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, PROVIDER_GOOGLE } from 'react-native-maps';
import * as Location from 'expo-location';
import etaUpdateService from '../../../../shared/services/etaUpdateService';

const { width, height } = Dimensions.get('window');

// Mock data for initial development
const mockDeliveries = [
  {
    id: 'del-001',
    customerName: 'Green Valley Farm',
    address: '1234 Farm Road, Greenville, CA',
    coordinates: {
      latitude: 37.7749,
      longitude: -122.4194,
    },
    status: 'In Progress',
  },
  {
    id: 'del-002',
    customerName: 'Sunrise Orchards',
    address: '567 Orchard Lane, Sunnydale, CA',
    coordinates: {
      latitude: 37.8049,
      longitude: -122.2711,
    },
    status: 'Scheduled',
  },
  {
    id: 'del-003',
    customerName: 'Blue Sky Dairy',
    address: '890 Dairy Road, Milkville, CA',
    coordinates: {
      latitude: 37.7249,
      longitude: -122.1694,
    },
    status: 'Scheduled',
  }
];

// Mock route data
const mockRoute = [
  { latitude: 37.7800, longitude: -122.4300 },
  { latitude: 37.7790, longitude: -122.4280 },
  { latitude: 37.7770, longitude: -122.4250 },
  { latitude: 37.7760, longitude: -122.4230 },
  { latitude: 37.7749, longitude: -122.4194 }
];

const NavigationScreen: React.FC = () => {
  const navigation = useNavigation();
  const mapRef = useRef<MapView>(null);

  const [loading, setLoading] = useState(true);
  const [deliveries, setDeliveries] = useState(mockDeliveries);
  const [currentLocation, setCurrentLocation] = useState<Location.LocationObject | null>(null);
  const [selectedDelivery, setSelectedDelivery] = useState<typeof mockDeliveries[0] | null>(null);
  const [navigationActive, setNavigationActive] = useState(false);
  const [route, setRoute] = useState<any[]>([]);
  const [eta, setEta] = useState<string>('');
  const [distance, setDistance] = useState<string>('');
  const [etaUpdateActive, setEtaUpdateActive] = useState(false);
  const [sendEtaUpdates, setSendEtaUpdates] = useState(true);
  const [etaDetails, setEtaDetails] = useState<{
    originalEta: string;
    currentStatus: 'on-time' | 'delayed' | 'early';
    minutesChanged?: number;
    lastUpdated: string;
  } | null>(null);

  // Request location permissions and get current location
  useEffect(() => {
    (async () => {
      let { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission Denied', 'Location permission is required for navigation.');
        setLoading(false);
        return;
      }

      try {
        let location = await Location.getCurrentPositionAsync({});
        setCurrentLocation(location);

        // Simulate fetching deliveries
        setTimeout(() => {
          setLoading(false);
        }, 1000);
      } catch (error) {
        console.error('Error getting location:', error);
        Alert.alert('Error', 'Could not get your current location.');
        setLoading(false);
      }
    })();
  }, []);

  // Start navigation to selected delivery
  const startNavigation = async () => {
    if (!selectedDelivery) return;

    // In a real app, this would fetch the route from a navigation API
    setRoute(mockRoute);
    setNavigationActive(true);
    setEta('25 min');
    setDistance('5.2 miles');

    // Initialize ETA details
    const now = new Date();
    const estimatedArrival = new Date(now.getTime() + 25 * 60000); // 25 minutes from now
    setEtaDetails({
      originalEta: formatTime(estimatedArrival),
      currentStatus: 'on-time',
      lastUpdated: formatTime(now),
    });

    // Fit map to show route
    if (mapRef.current && currentLocation) {
      mapRef.current.fitToCoordinates(
        [
          { 
            latitude: currentLocation.coords.latitude, 
            longitude: currentLocation.coords.longitude 
          },
          { 
            latitude: selectedDelivery.coordinates.latitude, 
            longitude: selectedDelivery.coordinates.longitude 
          }
        ],
        {
          edgePadding: { top: 50, right: 50, bottom: 50, left: 50 },
          animated: true,
        }
      );
    }

    // Start ETA updates if enabled
    if (sendEtaUpdates) {
      try {
        // In a real app, the customer ID would come from the delivery data
        const customerId = `cust-00${selectedDelivery.id.split('-')[1]}`;
        const success = await etaUpdateService.startETAUpdates(selectedDelivery.id, customerId);
        if (success) {
          setEtaUpdateActive(true);
          console.log(`Started ETA updates for delivery ${selectedDelivery.id}`);

          // Start periodic ETA updates
          startPeriodicEtaUpdates(selectedDelivery.id);
        } else {
          console.error(`Failed to start ETA updates for delivery ${selectedDelivery.id}`);
        }
      } catch (error) {
        console.error('Error starting ETA updates:', error);
      }
    }
  };

  // Format time for display (e.g., "3:45 PM")
  const formatTime = (date: Date): string => {
    return date.toLocaleTimeString([], { hour: 'numeric', minute: '2-digit' });
  };

  // Start periodic ETA updates
  const startPeriodicEtaUpdates = (deliveryId: string) => {
    // In a real app, this would use a more sophisticated approach to update ETAs
    // For this implementation, we'll simulate ETA changes with random variations

    const updateInterval = setInterval(() => {
      if (!navigationActive) {
        clearInterval(updateInterval);
        return;
      }

      // Get the current ETA update from the service
      const etaUpdate = etaUpdateService.getETAUpdate(deliveryId);
      if (!etaUpdate) return;

      // Update the ETA details
      const now = new Date();
      const originalEta = etaUpdate.originalETA;
      const currentEta = etaUpdate.currentETA;

      // Calculate time difference in minutes
      const diffMinutes = Math.round((currentEta.getTime() - originalEta.getTime()) / 60000);

      setEtaDetails({
        originalEta: formatTime(originalEta),
        currentStatus: etaUpdate.status,
        minutesChanged: Math.abs(diffMinutes),
        lastUpdated: formatTime(now),
      });

      // Update the simple ETA display
      setEta(`${Math.round((currentEta.getTime() - now.getTime()) / 60000)} min`);

    }, 30000); // Update every 30 seconds

    // Store the interval ID for cleanup
    return updateInterval;
  };

  // End navigation
  const endNavigation = async () => {
    // Stop ETA updates if active
    if (etaUpdateActive && selectedDelivery) {
      try {
        const success = await etaUpdateService.stopETAUpdates(selectedDelivery.id);
        if (success) {
          setEtaUpdateActive(false);
          console.log(`Stopped ETA updates for delivery ${selectedDelivery.id}`);
        } else {
          console.error(`Failed to stop ETA updates for delivery ${selectedDelivery.id}`);
        }
      } catch (error) {
        console.error('Error stopping ETA updates:', error);
      }
    }

    setNavigationActive(false);
    setRoute([]);
    setSelectedDelivery(null);
    setEtaDetails(null);
  };

  // Select a delivery to navigate to
  const selectDelivery = (delivery: typeof mockDeliveries[0]) => {
    setSelectedDelivery(delivery);

    // Center map on delivery location
    if (mapRef.current) {
      mapRef.current.animateToRegion({
        latitude: delivery.coordinates.latitude,
        longitude: delivery.coordinates.longitude,
        latitudeDelta: 0.01,
        longitudeDelta: 0.01,
      });
    }
  };

  // Show delivery details
  const showDeliveryDetails = (deliveryId: string) => {
    navigation.navigate('DeliveryDetail' as never, { deliveryId } as never);
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#8b5cf6" />
        <Text style={styles.loadingText}>Loading map and deliveries...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Map View */}
      <MapView
        ref={mapRef}
        style={styles.map}
        provider={PROVIDER_GOOGLE}
        initialRegion={currentLocation ? {
          latitude: currentLocation.coords.latitude,
          longitude: currentLocation.coords.longitude,
          latitudeDelta: 0.05,
          longitudeDelta: 0.05,
        } : undefined}
        showsUserLocation
        followsUserLocation
        showsMyLocationButton
        showsCompass
        showsScale
      >
        {/* Delivery Markers */}
        {deliveries.map(delivery => (
          <Marker
            key={delivery.id}
            coordinate={delivery.coordinates}
            title={delivery.customerName}
            description={delivery.address}
            pinColor={delivery.id === selectedDelivery?.id ? '#8b5cf6' : '#3b82f6'}
            onPress={() => selectDelivery(delivery)}
          />
        ))}

        {/* Navigation Route */}
        {navigationActive && route.length > 0 && (
          <Polyline
            coordinates={route}
            strokeWidth={4}
            strokeColor="#8b5cf6"
          />
        )}
      </MapView>

      {/* Navigation Info Panel */}
      {navigationActive && selectedDelivery && (
        <View style={styles.navigationPanel}>
          <View style={styles.navigationHeader}>
            <Text style={styles.navigationTitle}>Navigating to</Text>
            <TouchableOpacity 
              style={styles.closeButton}
              onPress={endNavigation}
            >
              <Ionicons name="close" size={24} color="#666" />
            </TouchableOpacity>
          </View>

          <Text style={styles.destinationName}>{selectedDelivery.customerName}</Text>
          <Text style={styles.destinationAddress}>{selectedDelivery.address}</Text>

          <View style={styles.navigationInfo}>
            <View style={styles.infoItem}>
              <Ionicons name="time-outline" size={20} color="#8b5cf6" />
              <Text style={styles.infoText}>ETA: {eta}</Text>
            </View>
            <View style={styles.infoItem}>
              <Ionicons name="map-outline" size={20} color="#8b5cf6" />
              <Text style={styles.infoText}>Distance: {distance}</Text>
            </View>
          </View>

          {/* ETA Update Status */}
          {etaUpdateActive && (
            <View style={styles.etaUpdateStatus}>
              <Ionicons name="notifications" size={16} color="#10b981" />
              <Text style={styles.etaUpdateText}>
                Customer ETA updates active
              </Text>
            </View>
          )}

          {/* Detailed ETA Information */}
          {etaDetails && (
            <View style={styles.etaDetailsContainer}>
              <Text style={styles.etaDetailsTitle}>ETA Details</Text>

              <View style={styles.etaDetailsRow}>
                <Text style={styles.etaDetailsLabel}>Original ETA:</Text>
                <Text style={styles.etaDetailsValue}>{etaDetails.originalEta}</Text>
              </View>

              <View style={styles.etaDetailsRow}>
                <Text style={styles.etaDetailsLabel}>Status:</Text>
                <View style={styles.etaStatusBadge}>
                  <Ionicons 
                    name={
                      etaDetails.currentStatus === 'on-time' ? 'checkmark-circle' : 
                      etaDetails.currentStatus === 'delayed' ? 'alert-circle' : 'time'
                    } 
                    size={14} 
                    color={
                      etaDetails.currentStatus === 'on-time' ? '#10b981' : 
                      etaDetails.currentStatus === 'delayed' ? '#ef4444' : '#3b82f6'
                    } 
                  />
                  <Text 
                    style={[
                      styles.etaStatusText,
                      {
                        color: 
                          etaDetails.currentStatus === 'on-time' ? '#10b981' : 
                          etaDetails.currentStatus === 'delayed' ? '#ef4444' : '#3b82f6'
                      }
                    ]}
                  >
                    {etaDetails.currentStatus === 'on-time' ? 'On Time' : 
                     etaDetails.currentStatus === 'delayed' ? 'Delayed' : 'Early'}
                    {etaDetails.minutesChanged ? ` (${etaDetails.minutesChanged} min)` : ''}
                  </Text>
                </View>
              </View>

              <View style={styles.etaDetailsRow}>
                <Text style={styles.etaDetailsLabel}>Last updated:</Text>
                <Text style={styles.etaDetailsValue}>{etaDetails.lastUpdated}</Text>
              </View>
            </View>
          )}

          <View style={styles.buttonGroup}>
            <TouchableOpacity 
              style={styles.detailsButton}
              onPress={() => showDeliveryDetails(selectedDelivery.id)}
            >
              <Text style={styles.detailsButtonText}>View Delivery Details</Text>
            </TouchableOpacity>

            <TouchableOpacity 
              style={styles.notificationsButton}
              onPress={() => navigation.navigate('CustomerNotifications' as never, { deliveryId: selectedDelivery.id } as never)}
            >
              <Ionicons name="notifications-outline" size={16} color="#8b5cf6" />
              <Text style={styles.notificationsButtonText}>View Notifications</Text>
            </TouchableOpacity>
          </View>
        </View>
      )}

      {/* Delivery Selection Panel */}
      {!navigationActive && selectedDelivery && (
        <View style={styles.selectionPanel}>
          <View style={styles.selectionHeader}>
            <Text style={styles.selectionTitle}>Selected Destination</Text>
            <TouchableOpacity 
              style={styles.closeButton}
              onPress={() => setSelectedDelivery(null)}
            >
              <Ionicons name="close" size={24} color="#666" />
            </TouchableOpacity>
          </View>

          <Text style={styles.destinationName}>{selectedDelivery.customerName}</Text>
          <Text style={styles.destinationAddress}>{selectedDelivery.address}</Text>

          <View style={styles.buttonContainer}>
            <TouchableOpacity 
              style={styles.navigationStartButton}
              onPress={startNavigation}
            >
              <Ionicons name="navigate" size={20} color="white" />
              <Text style={styles.navigationStartButtonText}>Start Navigation</Text>
            </TouchableOpacity>

            <TouchableOpacity 
              style={styles.detailsButton}
              onPress={() => showDeliveryDetails(selectedDelivery.id)}
            >
              <Text style={styles.detailsButtonText}>View Details</Text>
            </TouchableOpacity>
          </View>

          {/* ETA Updates Toggle */}
          <View style={styles.etaUpdatesToggle}>
            <Text style={styles.etaUpdatesLabel}>Send ETA updates to customer</Text>
            <Switch
              value={sendEtaUpdates}
              onValueChange={setSendEtaUpdates}
              trackColor={{ false: '#d1d5db', true: '#c4b5fd' }}
              thumbColor={sendEtaUpdates ? '#8b5cf6' : '#f4f3f4'}
            />
          </View>
        </View>
      )}

      {/* Deliveries List Button */}
      {!navigationActive && !selectedDelivery && (
        <View style={styles.topButtonsContainer}>
          <TouchableOpacity 
            style={styles.deliveriesButton}
            onPress={() => navigation.navigate('Deliveries' as never)}
          >
            <Ionicons name="list" size={20} color="white" />
            <Text style={styles.deliveriesButtonText}>View All Deliveries</Text>
          </TouchableOpacity>

          <TouchableOpacity 
            style={styles.locationTrackingButton}
            onPress={() => navigation.navigate('LocationTracking' as never)}
          >
            <Ionicons name="location" size={20} color="white" />
            <Text style={styles.locationTrackingButtonText}>Location Tracking</Text>
          </TouchableOpacity>

          <TouchableOpacity 
            style={styles.enhancedNavigationButton}
            onPress={() => navigation.navigate('EnhancedNavigation' as never, { deliveryId: selectedDelivery?.id || 'del-001' } as never)}
          >
            <Ionicons name="navigate-circle" size={20} color="white" />
            <Text style={styles.enhancedNavigationButtonText}>Enhanced Navigation</Text>
          </TouchableOpacity>

          <TouchableOpacity 
            style={styles.routeOptimizationButton}
            onPress={() => navigation.navigate('RouteOptimization' as never)}
          >
            <Ionicons name="analytics" size={20} color="white" />
            <Text style={styles.routeOptimizationButtonText}>Route Optimization</Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  map: {
    width,
    height,
  },
  navigationPanel: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'white',
    borderTopLeftRadius: 15,
    borderTopRightRadius: 15,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 5,
  },
  navigationHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  navigationTitle: {
    fontSize: 14,
    color: '#666',
  },
  closeButton: {
    padding: 5,
  },
  destinationName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  destinationAddress: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
    marginBottom: 10,
  },
  navigationInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginVertical: 10,
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  infoText: {
    fontSize: 14,
    color: '#333',
    marginLeft: 5,
  },
  buttonGroup: {
    marginTop: 5,
  },
  detailsButton: {
    paddingVertical: 10,
    alignItems: 'center',
    marginBottom: 8,
  },
  detailsButtonText: {
    color: '#8b5cf6',
    fontWeight: '500',
  },
  notificationsButton: {
    paddingVertical: 10,
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
  },
  notificationsButtonText: {
    color: '#8b5cf6',
    fontWeight: '500',
    marginLeft: 8,
  },
  selectionPanel: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'white',
    borderTopLeftRadius: 15,
    borderTopRightRadius: 15,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 5,
  },
  selectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  selectionTitle: {
    fontSize: 14,
    color: '#666',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 10,
  },
  navigationStartButton: {
    backgroundColor: '#8b5cf6',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 10,
    paddingHorizontal: 15,
    borderRadius: 8,
    flex: 1,
    marginRight: 10,
  },
  navigationStartButtonText: {
    color: 'white',
    fontWeight: 'bold',
    marginLeft: 5,
  },
  deliveriesButton: {
    backgroundColor: '#8b5cf6',
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 5,
  },
  deliveriesButtonText: {
    color: 'white',
    fontWeight: '500',
    marginLeft: 5,
  },
  topButtonsContainer: {
    position: 'absolute',
    top: 20,
    right: 20,
    flexDirection: 'column',
    alignItems: 'flex-end',
    gap: 10,
  },
  locationTrackingButton: {
    backgroundColor: '#10b981',
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 5,
  },
  locationTrackingButtonText: {
    color: 'white',
    fontWeight: '500',
    marginLeft: 5,
  },
  enhancedNavigationButton: {
    backgroundColor: '#3b82f6',
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    marginTop: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 5,
  },
  enhancedNavigationButtonText: {
    color: 'white',
    fontWeight: '500',
    marginLeft: 5,
  },
  routeOptimizationButton: {
    backgroundColor: '#f59e0b',
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    marginTop: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 5,
  },
  routeOptimizationButtonText: {
    color: 'white',
    fontWeight: '500',
    marginLeft: 5,
  },
  etaUpdateStatus: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#ecfdf5',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    marginTop: 10,
  },
  etaUpdateText: {
    color: '#10b981',
    marginLeft: 5,
    fontSize: 14,
    fontWeight: '500',
  },
  etaDetailsContainer: {
    backgroundColor: '#f9fafb',
    padding: 12,
    borderRadius: 8,
    marginTop: 15,
    borderWidth: 1,
    borderColor: '#e5e7eb',
  },
  etaDetailsTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#4b5563',
    marginBottom: 8,
  },
  etaDetailsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 6,
  },
  etaDetailsLabel: {
    fontSize: 13,
    color: '#6b7280',
  },
  etaDetailsValue: {
    fontSize: 13,
    color: '#111827',
    fontWeight: '500',
  },
  etaStatusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 12,
    backgroundColor: '#f3f4f6',
  },
  etaStatusText: {
    fontSize: 13,
    fontWeight: '500',
    marginLeft: 4,
  },
  etaUpdatesToggle: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: 15,
    paddingTop: 15,
    borderTopWidth: 1,
    borderTopColor: '#f3f4f6',
  },
  etaUpdatesLabel: {
    fontSize: 14,
    color: '#4b5563',
  },
});

export default NavigationScreen;
