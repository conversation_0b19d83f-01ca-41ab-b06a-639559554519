import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, FlatList, TouchableOpacity, TextInput, ActivityIndicator, Image } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { MainStackParamList } from '../navigation/MainNavigator';

type NavigationProp = NativeStackNavigationProp<MainStackParamList>;

// Mock data for initial development
const mockMessages = [
  {
    id: 'msg-001',
    sender: 'Dispatch',
    senderAvatar: null,
    preview: 'Please confirm your arrival at Green Valley Farm',
    timestamp: '2023-08-15T09:15:00Z',
    unread: true,
    type: 'direct'
  },
  {
    id: 'msg-002',
    sender: 'System',
    senderAvatar: null,
    preview: 'Your schedule has been updated for tomorrow',
    timestamp: '2023-08-15T08:30:00Z',
    unread: true,
    type: 'system'
  },
  {
    id: 'msg-003',
    sender: '<PERSON>',
    senderAvatar: 'https://randomuser.me/api/portraits/women/44.jpg',
    preview: 'The gate code for Sunrise Orchards has changed to 5678',
    timestamp: '2023-08-14T16:45:00Z',
    unread: false,
    type: 'direct'
  },
  {
    id: 'msg-004',
    sender: 'Maintenance',
    senderAvatar: null,
    preview: 'Your vehicle is due for maintenance next week',
    timestamp: '2023-08-14T14:20:00Z',
    unread: false,
    type: 'system'
  },
  {
    id: 'msg-005',
    sender: 'John Smith',
    senderAvatar: 'https://randomuser.me/api/portraits/men/32.jpg',
    preview: 'Can you pick up some extra supplies while you\'re out?',
    timestamp: '2023-08-14T11:05:00Z',
    unread: false,
    type: 'direct'
  },
  {
    id: 'msg-006',
    sender: 'System',
    senderAvatar: null,
    preview: 'New delivery assigned: Blue Sky Dairy',
    timestamp: '2023-08-13T17:30:00Z',
    unread: false,
    type: 'system'
  },
  {
    id: 'msg-007',
    sender: 'Michael Brown',
    senderAvatar: 'https://randomuser.me/api/portraits/men/67.jpg',
    preview: 'Thanks for the quick delivery yesterday!',
    timestamp: '2023-08-13T09:45:00Z',
    unread: false,
    type: 'direct'
  }
];

const MessagesScreen: React.FC = () => {
  const navigation = useNavigation<NavigationProp>();
  const [messages, setMessages] = useState(mockMessages);
  const [filteredMessages, setFilteredMessages] = useState(mockMessages);
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(true);
  const [activeFilter, setActiveFilter] = useState('all');

  // Simulate loading data
  useEffect(() => {
    setTimeout(() => {
      setLoading(false);
    }, 1000);
  }, []);

  // Filter messages based on search query and active filter
  useEffect(() => {
    setLoading(true);

    // Simulate API call delay
    setTimeout(() => {
      let filtered = messages;

      // Apply search filter
      if (searchQuery) {
        filtered = filtered.filter(message => 
          message.sender.toLowerCase().includes(searchQuery.toLowerCase()) ||
          message.preview.toLowerCase().includes(searchQuery.toLowerCase())
        );
      }

      // Apply type filter
      if (activeFilter !== 'all') {
        filtered = filtered.filter(message => {
          if (activeFilter === 'direct') {
            return message.type === 'direct';
          } else if (activeFilter === 'system') {
            return message.type === 'system';
          } else if (activeFilter === 'unread') {
            return message.unread;
          }
          return true;
        });
      }

      setFilteredMessages(filtered);
      setLoading(false);
    }, 500);
  }, [searchQuery, activeFilter, messages]);

  // Handle message press
  const handleMessagePress = (message: typeof mockMessages[0]) => {
    // In a real app, this would mark the message as read
    if (message.unread) {
      const updatedMessages = messages.map(m => 
        m.id === message.id ? { ...m, unread: false } : m
      );
      setMessages(updatedMessages);
    }

    // Navigate to conversation screen
    navigation.navigate('Conversation', { 
      conversationId: message.id, 
      recipientName: message.sender 
    });
  };

  // Format timestamp
  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));

    if (diffDays === 0) {
      // Today - show time
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else if (diffDays === 1) {
      // Yesterday
      return 'Yesterday';
    } else if (diffDays < 7) {
      // This week - show day name
      return date.toLocaleDateString([], { weekday: 'short' });
    } else {
      // Older - show date
      return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
    }
  };

  // Render each message item
  const renderMessageItem = ({ item }: { item: typeof mockMessages[0] }) => {
    return (
      <TouchableOpacity 
        style={[styles.messageCard, item.unread && styles.unreadMessageCard]}
        onPress={() => handleMessagePress(item)}
      >
        <View style={styles.messageIconContainer}>
          {item.type === 'system' ? (
            <View style={styles.systemIconContainer}>
              <Ionicons name="notifications" size={20} color="#8b5cf6" />
            </View>
          ) : (
            <View style={styles.avatarContainer}>
              {item.senderAvatar ? (
                <Image source={{ uri: item.senderAvatar }} style={styles.avatar} />
              ) : (
                <View style={styles.avatarPlaceholder}>
                  <Text style={styles.avatarText}>{item.sender.charAt(0)}</Text>
                </View>
              )}
            </View>
          )}
          {item.unread && <View style={styles.unreadIndicator} />}
        </View>

        <View style={styles.messageContent}>
          <View style={styles.messageHeader}>
            <Text style={[styles.messageSender, item.unread && styles.unreadText]}>{item.sender}</Text>
            <Text style={styles.messageTime}>{formatTimestamp(item.timestamp)}</Text>
          </View>
          <Text 
            style={[styles.messagePreview, item.unread && styles.unreadText]} 
            numberOfLines={2}
          >
            {item.preview}
          </Text>
        </View>

        <Ionicons name="chevron-forward" size={20} color="#8b5cf6" />
      </TouchableOpacity>
    );
  };

  return (
    <View style={styles.container}>
      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <Ionicons name="search" size={20} color="#666" style={styles.searchIcon} />
        <TextInput
          style={styles.searchInput}
          placeholder="Search messages..."
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
        {searchQuery ? (
          <TouchableOpacity onPress={() => setSearchQuery('')}>
            <Ionicons name="close-circle" size={20} color="#666" />
          </TouchableOpacity>
        ) : null}
      </View>

      {/* Filter Tabs */}
      <View style={styles.filterContainer}>
        <TouchableOpacity 
          style={[styles.filterTab, activeFilter === 'all' && styles.activeFilterTab]}
          onPress={() => setActiveFilter('all')}
        >
          <Text style={[styles.filterText, activeFilter === 'all' && styles.activeFilterText]}>All</Text>
        </TouchableOpacity>
        <TouchableOpacity 
          style={[styles.filterTab, activeFilter === 'unread' && styles.activeFilterTab]}
          onPress={() => setActiveFilter('unread')}
        >
          <Text style={[styles.filterText, activeFilter === 'unread' && styles.activeFilterText]}>Unread</Text>
        </TouchableOpacity>
        <TouchableOpacity 
          style={[styles.filterTab, activeFilter === 'direct' && styles.activeFilterTab]}
          onPress={() => setActiveFilter('direct')}
        >
          <Text style={[styles.filterText, activeFilter === 'direct' && styles.activeFilterText]}>Direct</Text>
        </TouchableOpacity>
        <TouchableOpacity 
          style={[styles.filterTab, activeFilter === 'system' && styles.activeFilterTab]}
          onPress={() => setActiveFilter('system')}
        >
          <Text style={[styles.filterText, activeFilter === 'system' && styles.activeFilterText]}>System</Text>
        </TouchableOpacity>
      </View>

      {/* Messages List */}
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#8b5cf6" />
        </View>
      ) : filteredMessages.length > 0 ? (
        <FlatList
          data={filteredMessages}
          renderItem={renderMessageItem}
          keyExtractor={item => item.id}
          contentContainerStyle={styles.listContainer}
        />
      ) : (
        <View style={styles.emptyContainer}>
          <Ionicons name="chatbubble-ellipses-outline" size={64} color="#ccc" />
          <Text style={styles.emptyText}>No messages found</Text>
          <Text style={styles.emptySubtext}>Try adjusting your search or filters</Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    borderRadius: 8,
    margin: 15,
    paddingHorizontal: 15,
    paddingVertical: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  searchIcon: {
    marginRight: 10,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: '#333',
  },
  filterContainer: {
    flexDirection: 'row',
    marginHorizontal: 15,
    marginBottom: 10,
  },
  filterTab: {
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 20,
    marginRight: 10,
    backgroundColor: '#e5e7eb',
  },
  activeFilterTab: {
    backgroundColor: '#8b5cf6',
  },
  filterText: {
    fontSize: 14,
    color: '#666',
  },
  activeFilterText: {
    color: 'white',
    fontWeight: '500',
  },
  listContainer: {
    padding: 15,
    paddingTop: 5,
  },
  messageCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    borderRadius: 10,
    padding: 15,
    marginBottom: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  unreadMessageCard: {
    backgroundColor: '#f9f7ff',
  },
  messageIconContainer: {
    position: 'relative',
    marginRight: 15,
  },
  avatarContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    overflow: 'hidden',
    backgroundColor: '#e5e7eb',
  },
  avatar: {
    width: 40,
    height: 40,
  },
  avatarPlaceholder: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#8b5cf6',
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
  systemIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#f3f0ff',
    justifyContent: 'center',
    alignItems: 'center',
  },
  unreadIndicator: {
    position: 'absolute',
    top: 0,
    right: 0,
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: '#8b5cf6',
    borderWidth: 2,
    borderColor: 'white',
  },
  messageContent: {
    flex: 1,
    marginRight: 10,
  },
  messageHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 5,
  },
  messageSender: {
    fontSize: 16,
    color: '#333',
    fontWeight: '500',
  },
  unreadText: {
    fontWeight: 'bold',
    color: '#333',
  },
  messageTime: {
    fontSize: 12,
    color: '#666',
  },
  messagePreview: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#666',
    marginTop: 10,
  },
  emptySubtext: {
    fontSize: 14,
    color: '#999',
    marginTop: 5,
    textAlign: 'center',
  },
});

export default MessagesScreen;
