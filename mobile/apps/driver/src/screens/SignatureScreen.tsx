import React, { useState, useRef } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Alert, ActivityIndicator } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { MainStackParamList } from '../navigation/MainNavigator';
import SignatureCanvas from 'react-native-signature-canvas';

type SignatureScreenRouteProp = RouteProp<MainStackParamList, 'Signature'>;
type NavigationProp = NativeStackNavigationProp<MainStackParamList>;

const SignatureScreen: React.FC = () => {
  const navigation = useNavigation<NavigationProp>();
  const route = useRoute<SignatureScreenRouteProp>();
  const { deliveryId } = route.params;
  
  const [signature, setSignature] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const signatureRef = useRef<any>(null);
  
  // Handle signature completion
  const handleSignature = (signature: string) => {
    setSignature(signature);
  };
  
  // Handle signature clear
  const handleClear = () => {
    signatureRef.current?.clearSignature();
    setSignature(null);
  };
  
  // Handle signature save
  const handleSave = async () => {
    if (!signature) {
      Alert.alert('Error', 'Please provide a signature before saving.');
      return;
    }
    
    setLoading(true);
    
    // In a real app, this would upload the signature to a server
    // and associate it with the delivery
    setTimeout(() => {
      setLoading(false);
      Alert.alert(
        'Success',
        'Signature saved successfully!',
        [
          {
            text: 'OK',
            onPress: () => navigation.goBack()
          }
        ]
      );
    }, 1500);
  };
  
  // Handle cancel
  const handleCancel = () => {
    Alert.alert(
      'Cancel Signature',
      'Are you sure you want to cancel? Your signature will not be saved.',
      [
        {
          text: 'No',
          style: 'cancel'
        },
        {
          text: 'Yes',
          onPress: () => navigation.goBack()
        }
      ]
    );
  };
  
  // Signature canvas style
  const style = `.m-signature-pad--footer {display: none; margin: 0px;}
                 .m-signature-pad {box-shadow: none; border: none;}
                 body {background-color: #f5f5f5;}`;
  
  return (
    <View style={styles.container}>
      <View style={styles.headerContainer}>
        <Text style={styles.headerTitle}>Capture Signature</Text>
        <Text style={styles.headerSubtitle}>Please sign below to confirm delivery</Text>
      </View>
      
      <View style={styles.signatureContainer}>
        <SignatureCanvas
          ref={signatureRef}
          onOK={handleSignature}
          onEmpty={() => setSignature(null)}
          descriptionText=""
          clearText="Clear"
          confirmText="Save"
          webStyle={style}
          backgroundColor="#f5f5f5"
          penColor="#333333"
          minWidth={2}
          maxWidth={5}
        />
      </View>
      
      <View style={styles.instructionsContainer}>
        <Ionicons name="information-circle-outline" size={20} color="#666" />
        <Text style={styles.instructionsText}>
          Use your finger to sign in the box above. Make sure the signature is clear and complete.
        </Text>
      </View>
      
      <View style={styles.actionsContainer}>
        <TouchableOpacity 
          style={styles.clearButton}
          onPress={handleClear}
          disabled={loading}
        >
          <Ionicons name="refresh" size={20} color="#666" />
          <Text style={styles.clearButtonText}>Clear</Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={[styles.saveButton, !signature && styles.disabledButton]}
          onPress={handleSave}
          disabled={!signature || loading}
        >
          {loading ? (
            <ActivityIndicator size="small" color="white" />
          ) : (
            <>
              <Ionicons name="checkmark-circle" size={20} color="white" />
              <Text style={styles.saveButtonText}>Save Signature</Text>
            </>
          )}
        </TouchableOpacity>
      </View>
      
      <TouchableOpacity 
        style={styles.cancelButton}
        onPress={handleCancel}
        disabled={loading}
      >
        <Text style={styles.cancelButtonText}>Cancel</Text>
      </TouchableOpacity>
      
      <View style={styles.deliveryInfoContainer}>
        <Text style={styles.deliveryInfoLabel}>Delivery ID:</Text>
        <Text style={styles.deliveryInfoValue}>{deliveryId}</Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
    padding: 20,
  },
  headerContainer: {
    marginBottom: 20,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 5,
  },
  headerSubtitle: {
    fontSize: 16,
    color: '#666',
  },
  signatureContainer: {
    height: 300,
    backgroundColor: 'white',
    borderRadius: 10,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: '#e5e7eb',
    marginBottom: 20,
  },
  instructionsContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: '#f0f9ff',
    borderRadius: 8,
    padding: 15,
    marginBottom: 20,
  },
  instructionsText: {
    fontSize: 14,
    color: '#666',
    marginLeft: 10,
    flex: 1,
  },
  actionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  clearButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: '#d1d5db',
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 20,
    flex: 1,
    marginRight: 10,
  },
  clearButtonText: {
    fontSize: 16,
    color: '#666',
    marginLeft: 8,
  },
  saveButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#8b5cf6',
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 20,
    flex: 2,
  },
  disabledButton: {
    backgroundColor: '#c4b5fd',
  },
  saveButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: 'white',
    marginLeft: 8,
  },
  cancelButton: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
  },
  cancelButtonText: {
    fontSize: 16,
    color: '#f87171',
  },
  deliveryInfoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 20,
  },
  deliveryInfoLabel: {
    fontSize: 14,
    color: '#666',
    marginRight: 5,
  },
  deliveryInfoValue: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
  },
});

export default SignatureScreen;