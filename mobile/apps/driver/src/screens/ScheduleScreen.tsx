import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, ActivityIndicator } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { MainStackParamList } from '../navigation/MainNavigator';

type NavigationProp = NativeStackNavigationProp<MainStackParamList>;

// Mock data for initial development
const mockSchedule = {
  date: new Date().toDateString(),
  shifts: [
    {
      id: 'shift-001',
      startTime: '08:00 AM',
      endTime: '05:00 PM',
      status: 'Active',
      breakTime: '12:00 PM - 01:00 PM',
      vehicle: 'Truck #103'
    }
  ],
  deliveries: [
    {
      id: 'del-001',
      customerName: 'Green Valley Farm',
      address: '1234 Farm Road, Greenville, CA',
      time: '10:30 AM',
      status: 'In Progress',
      type: 'Delivery',
      estimatedDuration: '45 min'
    },
    {
      id: 'del-002',
      customerName: 'Sunrise Orchards',
      address: '567 Orchard Lane, Sunnydale, CA',
      time: '01:15 PM',
      status: 'Scheduled',
      type: 'Delivery',
      estimatedDuration: '30 min'
    },
    {
      id: 'del-003',
      customerName: 'Blue Sky Dairy',
      address: '890 Dairy Road, Milkville, CA',
      time: '03:45 PM',
      status: 'Scheduled',
      type: 'Pickup',
      estimatedDuration: '40 min'
    }
  ],
  notes: 'Remember to call dispatch if any delivery takes longer than estimated.'
};

// Helper function to determine if a time is in the past, present, or future
const getTimeStatus = (timeString: string) => {
  const [time, period] = timeString.split(' ');
  const [hours, minutes] = time.split(':').map(Number);
  
  let hour = hours;
  if (period === 'PM' && hours !== 12) {
    hour += 12;
  } else if (period === 'AM' && hours === 12) {
    hour = 0;
  }
  
  const scheduleDate = new Date();
  scheduleDate.setHours(hour, minutes, 0);
  
  const now = new Date();
  
  // If the time is within 15 minutes (before or after), consider it "current"
  const fifteenMinutes = 15 * 60 * 1000;
  const diff = Math.abs(scheduleDate.getTime() - now.getTime());
  
  if (diff < fifteenMinutes) {
    return 'current';
  } else if (scheduleDate < now) {
    return 'past';
  } else {
    return 'future';
  }
};

const ScheduleScreen: React.FC = () => {
  const navigation = useNavigation<NavigationProp>();
  const [schedule, setSchedule] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [selectedDate, setSelectedDate] = useState(new Date());
  
  // Get dates for the week
  const getDatesForWeek = () => {
    const dates = [];
    const today = new Date();
    const dayOfWeek = today.getDay();
    
    // Start from Monday of current week
    const monday = new Date(today);
    monday.setDate(today.getDate() - dayOfWeek + (dayOfWeek === 0 ? -6 : 1));
    
    for (let i = 0; i < 7; i++) {
      const date = new Date(monday);
      date.setDate(monday.getDate() + i);
      dates.push(date);
    }
    
    return dates;
  };
  
  const weekDates = getDatesForWeek();
  
  useEffect(() => {
    // Simulate API call to fetch schedule
    setTimeout(() => {
      setSchedule(mockSchedule);
      setLoading(false);
    }, 1000);
  }, [selectedDate]);
  
  const handleDateSelect = (date: Date) => {
    setSelectedDate(date);
    setLoading(true);
  };
  
  const handleDeliveryPress = (deliveryId: string) => {
    navigation.navigate('DeliveryDetail', { deliveryId });
  };
  
  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#8b5cf6" />
        <Text style={styles.loadingText}>Loading schedule...</Text>
      </View>
    );
  }
  
  return (
    <ScrollView style={styles.container}>
      {/* Date Selector */}
      <View style={styles.dateSelector}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          {weekDates.map((date, index) => {
            const isSelected = date.toDateString() === selectedDate.toDateString();
            const isToday = date.toDateString() === new Date().toDateString();
            
            return (
              <TouchableOpacity
                key={index}
                style={[
                  styles.dateItem,
                  isSelected && styles.selectedDateItem,
                  isToday && styles.todayDateItem
                ]}
                onPress={() => handleDateSelect(date)}
              >
                <Text style={[
                  styles.dayName,
                  isSelected && styles.selectedDateText,
                  isToday && styles.todayDateText
                ]}>
                  {date.toLocaleDateString('en-US', { weekday: 'short' })}
                </Text>
                <Text style={[
                  styles.dayNumber,
                  isSelected && styles.selectedDateText,
                  isToday && styles.todayDateText
                ]}>
                  {date.getDate()}
                </Text>
              </TouchableOpacity>
            );
          })}
        </ScrollView>
      </View>
      
      {/* Shift Information */}
      {schedule.shifts.map((shift: any) => (
        <View key={shift.id} style={styles.shiftContainer}>
          <View style={styles.shiftHeader}>
            <View style={styles.shiftTitleContainer}>
              <Ionicons name="time" size={20} color="#8b5cf6" />
              <Text style={styles.shiftTitle}>Shift</Text>
            </View>
            <View style={[
              styles.statusBadge,
              { backgroundColor: shift.status === 'Active' ? '#10b98120' : '#f8718120' }
            ]}>
              <Text style={[
                styles.statusText,
                { color: shift.status === 'Active' ? '#10b981' : '#f87171' }
              ]}>
                {shift.status}
              </Text>
            </View>
          </View>
          
          <View style={styles.shiftDetails}>
            <View style={styles.shiftTime}>
              <Text style={styles.shiftTimeLabel}>Start:</Text>
              <Text style={styles.shiftTimeValue}>{shift.startTime}</Text>
            </View>
            <View style={styles.shiftTime}>
              <Text style={styles.shiftTimeLabel}>End:</Text>
              <Text style={styles.shiftTimeValue}>{shift.endTime}</Text>
            </View>
          </View>
          
          <View style={styles.shiftInfo}>
            <View style={styles.shiftInfoItem}>
              <Ionicons name="restaurant-outline" size={16} color="#666" />
              <Text style={styles.shiftInfoText}>Break: {shift.breakTime}</Text>
            </View>
            <View style={styles.shiftInfoItem}>
              <Ionicons name="car-outline" size={16} color="#666" />
              <Text style={styles.shiftInfoText}>Vehicle: {shift.vehicle}</Text>
            </View>
          </View>
        </View>
      ))}
      
      {/* Timeline */}
      <View style={styles.timelineContainer}>
        <View style={styles.timelineHeader}>
          <Ionicons name="calendar-outline" size={20} color="#8b5cf6" />
          <Text style={styles.timelineTitle}>Schedule</Text>
        </View>
        
        {schedule.deliveries.map((delivery: any, index: number) => {
          const timeStatus = getTimeStatus(delivery.time);
          let statusColor = '#8b5cf6'; // Default purple
          
          if (delivery.status === 'Completed') {
            statusColor = '#10b981'; // Green
          } else if (delivery.status === 'In Progress') {
            statusColor = '#3b82f6'; // Blue
          }
          
          return (
            <TouchableOpacity
              key={delivery.id}
              style={styles.timelineItem}
              onPress={() => handleDeliveryPress(delivery.id)}
            >
              <View style={styles.timelineLeft}>
                <View style={[
                  styles.timelineDot,
                  { backgroundColor: timeStatus === 'past' ? '#d1d5db' : statusColor }
                ]} />
                {index < schedule.deliveries.length - 1 && (
                  <View style={[
                    styles.timelineLine,
                    { backgroundColor: timeStatus === 'past' ? '#d1d5db' : '#e5e7eb' }
                  ]} />
                )}
              </View>
              
              <View style={styles.timelineTime}>
                <Text style={[
                  styles.timelineTimeText,
                  timeStatus === 'past' && styles.pastTimeText,
                  timeStatus === 'current' && styles.currentTimeText
                ]}>
                  {delivery.time}
                </Text>
                <Text style={styles.timelineDuration}>{delivery.estimatedDuration}</Text>
              </View>
              
              <View style={styles.timelineContent}>
                <View style={styles.timelineContentHeader}>
                  <Text style={styles.timelineCustomer}>{delivery.customerName}</Text>
                  <View style={[styles.typeChip, { backgroundColor: delivery.type === 'Pickup' ? '#3b82f620' : '#8b5cf620' }]}>
                    <Text style={[styles.typeText, { color: delivery.type === 'Pickup' ? '#3b82f6' : '#8b5cf6' }]}>
                      {delivery.type}
                    </Text>
                  </View>
                </View>
                <Text style={styles.timelineAddress}>{delivery.address}</Text>
                <View style={[styles.statusBadge, { backgroundColor: `${statusColor}20` }]}>
                  <Text style={[styles.statusText, { color: statusColor }]}>{delivery.status}</Text>
                </View>
              </View>
              
              <Ionicons name="chevron-forward" size={20} color="#8b5cf6" />
            </TouchableOpacity>
          );
        })}
      </View>
      
      {/* Notes */}
      {schedule.notes && (
        <View style={styles.notesContainer}>
          <View style={styles.notesHeader}>
            <Ionicons name="document-text-outline" size={20} color="#8b5cf6" />
            <Text style={styles.notesTitle}>Notes</Text>
          </View>
          <Text style={styles.notesText}>{schedule.notes}</Text>
        </View>
      )}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  dateSelector: {
    backgroundColor: 'white',
    paddingVertical: 15,
    paddingHorizontal: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  dateItem: {
    alignItems: 'center',
    justifyContent: 'center',
    width: 60,
    height: 70,
    borderRadius: 10,
    marginHorizontal: 5,
    backgroundColor: '#f9fafb',
  },
  selectedDateItem: {
    backgroundColor: '#8b5cf6',
  },
  todayDateItem: {
    borderWidth: 2,
    borderColor: '#8b5cf6',
  },
  dayName: {
    fontSize: 12,
    color: '#666',
    marginBottom: 5,
  },
  dayNumber: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  selectedDateText: {
    color: 'white',
  },
  todayDateText: {
    color: '#8b5cf6',
  },
  shiftContainer: {
    backgroundColor: 'white',
    margin: 15,
    marginBottom: 5,
    borderRadius: 10,
    padding: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  shiftHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  shiftTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  shiftTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginLeft: 8,
  },
  statusBadge: {
    borderRadius: 4,
    paddingHorizontal: 8,
    paddingVertical: 2,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '500',
  },
  shiftDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 10,
  },
  shiftTime: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  shiftTimeLabel: {
    fontSize: 14,
    color: '#666',
    marginRight: 5,
  },
  shiftTimeValue: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
  },
  shiftInfo: {
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
    paddingTop: 10,
  },
  shiftInfoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 5,
  },
  shiftInfoText: {
    fontSize: 14,
    color: '#666',
    marginLeft: 8,
  },
  timelineContainer: {
    backgroundColor: 'white',
    margin: 15,
    marginTop: 5,
    borderRadius: 10,
    padding: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  timelineHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 15,
  },
  timelineTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginLeft: 8,
  },
  timelineItem: {
    flexDirection: 'row',
    marginBottom: 20,
  },
  timelineLeft: {
    width: 20,
    alignItems: 'center',
  },
  timelineDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: '#8b5cf6',
  },
  timelineLine: {
    width: 2,
    height: '100%',
    backgroundColor: '#e5e7eb',
    marginTop: 5,
    marginBottom: -20,
  },
  timelineTime: {
    width: 80,
    marginRight: 10,
  },
  timelineTimeText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
  },
  pastTimeText: {
    color: '#9ca3af',
  },
  currentTimeText: {
    color: '#8b5cf6',
    fontWeight: 'bold',
  },
  timelineDuration: {
    fontSize: 12,
    color: '#666',
    marginTop: 2,
  },
  timelineContent: {
    flex: 1,
    marginRight: 10,
  },
  timelineContentHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 2,
  },
  timelineCustomer: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
  },
  typeChip: {
    borderRadius: 4,
    paddingHorizontal: 6,
    paddingVertical: 2,
  },
  typeText: {
    fontSize: 10,
    fontWeight: '500',
  },
  timelineAddress: {
    fontSize: 12,
    color: '#666',
    marginBottom: 5,
  },
  notesContainer: {
    backgroundColor: 'white',
    margin: 15,
    marginTop: 5,
    marginBottom: 20,
    borderRadius: 10,
    padding: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  notesHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  notesTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginLeft: 8,
  },
  notesText: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
});

export default ScheduleScreen;