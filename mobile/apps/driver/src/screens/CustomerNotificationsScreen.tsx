import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, FlatList, TouchableOpacity, ActivityIndicator, Alert } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation, useRoute } from '@react-navigation/native';
import etaUpdateService from '../../../../shared/services/etaUpdateService';

// Interface for notification history item
interface NotificationHistoryItem {
  id: string;
  deliveryId: string;
  customerId: string;
  customerName: string;
  type: 'start' | 'delay' | 'approaching' | 'arrival';
  sentAt: Date;
  status: 'success' | 'failed';
  channels: {
    push: boolean;
    sms: boolean;
    email: boolean;
  };
}

// Mock data for initial development
const mockNotificationHistory: NotificationHistoryItem[] = [
  {
    id: 'notif-001',
    deliveryId: 'del-001',
    customerId: 'cust-001',
    customerName: 'Green Valley Farm',
    type: 'start',
    sentAt: new Date(Date.now() - 60 * 60 * 1000), // 1 hour ago
    status: 'success',
    channels: {
      push: true,
      sms: true,
      email: true,
    },
  },
  {
    id: 'notif-002',
    deliveryId: 'del-001',
    customerId: 'cust-001',
    customerName: 'Green Valley Farm',
    type: 'delay',
    sentAt: new Date(Date.now() - 30 * 60 * 1000), // 30 minutes ago
    status: 'success',
    channels: {
      push: true,
      sms: true,
      email: false,
    },
  },
  {
    id: 'notif-003',
    deliveryId: 'del-001',
    customerId: 'cust-001',
    customerName: 'Green Valley Farm',
    type: 'approaching',
    sentAt: new Date(Date.now() - 15 * 60 * 1000), // 15 minutes ago
    status: 'success',
    channels: {
      push: true,
      sms: false,
      email: true,
    },
  },
];

const CustomerNotificationsScreen: React.FC = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const [loading, setLoading] = useState(true);
  const [notificationHistory, setNotificationHistory] = useState<NotificationHistoryItem[]>([]);
  const [selectedDeliveryId, setSelectedDeliveryId] = useState<string | null>(null);

  // Get delivery ID from route params if available
  useEffect(() => {
    if (route.params && (route.params as any).deliveryId) {
      setSelectedDeliveryId((route.params as any).deliveryId);
    }
  }, [route.params]);

  // Load notification history
  useEffect(() => {
    const loadNotificationHistory = async () => {
      try {
        // In a real app, this would fetch notification history from a backend API
        // For now, use mock data
        setTimeout(() => {
          setNotificationHistory(mockNotificationHistory);
          setLoading(false);
        }, 1000);
      } catch (error) {
        console.error('Error loading notification history:', error);
        Alert.alert('Error', 'Failed to load notification history');
        setLoading(false);
      }
    };

    loadNotificationHistory();
  }, []);

  // Format date for display
  const formatDate = (date: Date): string => {
    return date.toLocaleString([], {
      month: 'short',
      day: 'numeric',
      hour: 'numeric',
      minute: '2-digit',
    });
  };

  // Get icon for notification type
  const getNotificationTypeIcon = (type: string): string => {
    switch (type) {
      case 'start':
        return 'play';
      case 'delay':
        return 'time';
      case 'approaching':
        return 'navigate';
      case 'arrival':
        return 'checkmark-circle';
      default:
        return 'information-circle';
    }
  };

  // Get color for notification type
  const getNotificationTypeColor = (type: string): string => {
    switch (type) {
      case 'start':
        return '#3b82f6'; // Blue
      case 'delay':
        return '#ef4444'; // Red
      case 'approaching':
        return '#f59e0b'; // Amber
      case 'arrival':
        return '#10b981'; // Green
      default:
        return '#6b7280'; // Gray
    }
  };

  // Get title for notification type
  const getNotificationTypeTitle = (type: string): string => {
    switch (type) {
      case 'start':
        return 'Delivery Started';
      case 'delay':
        return 'Delivery Delayed';
      case 'approaching':
        return 'Approaching Destination';
      case 'arrival':
        return 'Delivery Arrived';
      default:
        return 'Notification';
    }
  };

  // Render notification item
  const renderNotificationItem = ({ item }: { item: NotificationHistoryItem }) => (
    <View style={styles.notificationItem}>
      <View style={[styles.notificationIconContainer, { backgroundColor: getNotificationTypeColor(item.type) }]}>
        <Ionicons name={getNotificationTypeIcon(item.type)} size={20} color="white" />
      </View>
      
      <View style={styles.notificationContent}>
        <Text style={styles.notificationType}>{getNotificationTypeTitle(item.type)}</Text>
        <Text style={styles.notificationCustomer}>{item.customerName}</Text>
        <Text style={styles.notificationTime}>{formatDate(item.sentAt)}</Text>
        
        <View style={styles.channelsContainer}>
          {item.channels.push && (
            <View style={styles.channelBadge}>
              <Ionicons name="phone-portrait-outline" size={12} color="#4b5563" />
              <Text style={styles.channelText}>Push</Text>
            </View>
          )}
          
          {item.channels.sms && (
            <View style={styles.channelBadge}>
              <Ionicons name="chatbubble-outline" size={12} color="#4b5563" />
              <Text style={styles.channelText}>SMS</Text>
            </View>
          )}
          
          {item.channels.email && (
            <View style={styles.channelBadge}>
              <Ionicons name="mail-outline" size={12} color="#4b5563" />
              <Text style={styles.channelText}>Email</Text>
            </View>
          )}
        </View>
      </View>
      
      <View style={styles.notificationStatus}>
        {item.status === 'success' ? (
          <Ionicons name="checkmark-circle" size={20} color="#10b981" />
        ) : (
          <Ionicons name="alert-circle" size={20} color="#ef4444" />
        )}
      </View>
    </View>
  );

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#8b5cf6" />
        <Text style={styles.loadingText}>Loading notification history...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color="#666" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Customer Notifications</Text>
      </View>
      
      {notificationHistory.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Ionicons name="notifications-off-outline" size={64} color="#d1d5db" />
          <Text style={styles.emptyText}>No notifications sent yet</Text>
        </View>
      ) : (
        <FlatList
          data={notificationHistory}
          renderItem={renderNotificationItem}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.listContent}
          showsVerticalScrollIndicator={false}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9fafb',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f9fafb',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  backButton: {
    marginRight: 16,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyText: {
    marginTop: 16,
    fontSize: 16,
    color: '#6b7280',
    textAlign: 'center',
  },
  listContent: {
    padding: 16,
  },
  notificationItem: {
    flexDirection: 'row',
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  notificationIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  notificationContent: {
    flex: 1,
  },
  notificationType: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 4,
  },
  notificationCustomer: {
    fontSize: 14,
    color: '#4b5563',
    marginBottom: 4,
  },
  notificationTime: {
    fontSize: 12,
    color: '#6b7280',
    marginBottom: 8,
  },
  channelsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  channelBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f3f4f6',
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
    marginRight: 8,
    marginBottom: 4,
  },
  channelText: {
    fontSize: 12,
    color: '#4b5563',
    marginLeft: 4,
  },
  notificationStatus: {
    justifyContent: 'center',
    alignItems: 'center',
    width: 24,
  },
});

export default CustomerNotificationsScreen;