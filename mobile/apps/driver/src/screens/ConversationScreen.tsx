import React, { useState, useEffect, useRef } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  FlatList, 
  TextInput, 
  TouchableOpacity, 
  KeyboardAvoidingView, 
  Platform, 
  ActivityIndicator,
  Image
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { MainStackParamList } from '../navigation/MainNavigator';

type ConversationScreenRouteProp = RouteProp<MainStackParamList, 'Conversation'>;
type NavigationProp = NativeStackNavigationProp<MainStackParamList>;

// Mock data for initial development
const mockConversations: Record<string, any> = {
  'msg-001': {
    id: 'msg-001',
    recipient: 'Dispatch',
    recipientAvatar: null,
    messages: [
      {
        id: '1',
        text: 'Please confirm your arrival at Green Valley Farm',
        sender: 'recipient',
        timestamp: '2023-08-15T09:15:00Z',
        status: 'read'
      },
      {
        id: '2',
        text: 'I\'ve just arrived at the farm',
        sender: 'user',
        timestamp: '2023-08-15T09:20:00Z',
        status: 'sent'
      },
      {
        id: '3',
        text: 'Great! Please proceed with the delivery',
        sender: 'recipient',
        timestamp: '2023-08-15T09:22:00Z',
        status: 'read'
      }
    ]
  },
  'msg-003': {
    id: 'msg-003',
    recipient: 'Sarah Johnson',
    recipientAvatar: 'https://randomuser.me/api/portraits/women/44.jpg',
    messages: [
      {
        id: '1',
        text: 'Hi, I\'m on my way to Sunrise Orchards',
        sender: 'user',
        timestamp: '2023-08-14T16:30:00Z',
        status: 'read'
      },
      {
        id: '2',
        text: 'Great! Just to let you know, the gate code has changed',
        sender: 'recipient',
        timestamp: '2023-08-14T16:40:00Z',
        status: 'read'
      },
      {
        id: '3',
        text: 'The gate code for Sunrise Orchards has changed to 5678',
        sender: 'recipient',
        timestamp: '2023-08-14T16:45:00Z',
        status: 'read'
      },
      {
        id: '4',
        text: 'Thanks for the update. I\'ll use the new code.',
        sender: 'user',
        timestamp: '2023-08-14T16:50:00Z',
        status: 'sent'
      }
    ]
  },
  'msg-005': {
    id: 'msg-005',
    recipient: 'John Smith',
    recipientAvatar: 'https://randomuser.me/api/portraits/men/32.jpg',
    messages: [
      {
        id: '1',
        text: 'Hey, are you making any deliveries near the hardware store today?',
        sender: 'recipient',
        timestamp: '2023-08-14T10:50:00Z',
        status: 'read'
      },
      {
        id: '2',
        text: 'Yes, I have a delivery at Blue Sky Dairy which is nearby',
        sender: 'user',
        timestamp: '2023-08-14T10:55:00Z',
        status: 'read'
      },
      {
        id: '3',
        text: 'Can you pick up some extra supplies while you\'re out?',
        sender: 'recipient',
        timestamp: '2023-08-14T11:05:00Z',
        status: 'read'
      },
      {
        id: '4',
        text: 'Sure, what do you need?',
        sender: 'user',
        timestamp: '2023-08-14T11:10:00Z',
        status: 'sent'
      }
    ]
  }
};

const ConversationScreen: React.FC = () => {
  const navigation = useNavigation<NavigationProp>();
  const route = useRoute<ConversationScreenRouteProp>();
  const { conversationId, recipientName } = route.params;
  
  const [conversation, setConversation] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [messageText, setMessageText] = useState('');
  const [sending, setSending] = useState(false);
  
  const flatListRef = useRef<FlatList>(null);
  
  // Fetch conversation data
  useEffect(() => {
    // Simulate API call to fetch conversation
    setTimeout(() => {
      const conversationData = mockConversations[conversationId];
      setConversation(conversationData);
      setLoading(false);
    }, 1000);
  }, [conversationId]);
  
  // Scroll to bottom when conversation loads or new message is added
  useEffect(() => {
    if (!loading && conversation?.messages?.length > 0) {
      setTimeout(() => {
        flatListRef.current?.scrollToEnd({ animated: true });
      }, 200);
    }
  }, [loading, conversation?.messages?.length]);
  
  // Handle sending a new message
  const handleSendMessage = () => {
    if (!messageText.trim()) return;
    
    setSending(true);
    
    // Create new message object
    const newMessage = {
      id: `${conversation.messages.length + 1}`,
      text: messageText.trim(),
      sender: 'user',
      timestamp: new Date().toISOString(),
      status: 'sending'
    };
    
    // Update conversation with new message
    const updatedConversation = {
      ...conversation,
      messages: [...conversation.messages, newMessage]
    };
    
    setConversation(updatedConversation);
    setMessageText('');
    
    // Simulate sending message to server
    setTimeout(() => {
      // Update message status to 'sent'
      const sentMessage = { ...newMessage, status: 'sent' };
      const updatedMessages = updatedConversation.messages.map((msg: any) => 
        msg.id === newMessage.id ? sentMessage : msg
      );
      
      setConversation({
        ...updatedConversation,
        messages: updatedMessages
      });
      
      setSending(false);
    }, 1000);
  };
  
  // Format timestamp
  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };
  
  // Render message item
  const renderMessageItem = ({ item }: { item: any }) => {
    const isUser = item.sender === 'user';
    
    return (
      <View style={[
        styles.messageContainer,
        isUser ? styles.userMessageContainer : styles.recipientMessageContainer
      ]}>
        <View style={[
          styles.messageBubble,
          isUser ? styles.userMessageBubble : styles.recipientMessageBubble
        ]}>
          <Text style={[
            styles.messageText,
            isUser ? styles.userMessageText : styles.recipientMessageText
          ]}>
            {item.text}
          </Text>
        </View>
        <View style={styles.messageFooter}>
          <Text style={styles.timestampText}>{formatTimestamp(item.timestamp)}</Text>
          {isUser && (
            <View style={styles.messageStatus}>
              {item.status === 'sending' && (
                <ActivityIndicator size="small" color="#8b5cf6" />
              )}
              {item.status === 'sent' && (
                <Ionicons name="checkmark" size={16} color="#8b5cf6" />
              )}
              {item.status === 'delivered' && (
                <Ionicons name="checkmark-done" size={16} color="#8b5cf6" />
              )}
              {item.status === 'read' && (
                <Ionicons name="checkmark-done" size={16} color="#3b82f6" />
              )}
            </View>
          )}
        </View>
      </View>
    );
  };
  
  // Render date separator
  const renderDateSeparator = (date: string) => {
    return (
      <View style={styles.dateSeparator}>
        <View style={styles.dateSeparatorLine} />
        <Text style={styles.dateSeparatorText}>{date}</Text>
        <View style={styles.dateSeparatorLine} />
      </View>
    );
  };
  
  // Group messages by date and add separators
  const getGroupedMessages = () => {
    if (!conversation?.messages) return [];
    
    const groupedMessages: any[] = [];
    let currentDate = '';
    
    conversation.messages.forEach((message: any) => {
      const messageDate = new Date(message.timestamp).toLocaleDateString();
      
      if (messageDate !== currentDate) {
        currentDate = messageDate;
        groupedMessages.push({
          id: `date-${messageDate}`,
          type: 'date',
          date: messageDate
        });
      }
      
      groupedMessages.push({
        ...message,
        type: 'message'
      });
    });
    
    return groupedMessages;
  };
  
  // Render item based on type (message or date separator)
  const renderItem = ({ item }: { item: any }) => {
    if (item.type === 'date') {
      return renderDateSeparator(item.date);
    } else {
      return renderMessageItem({ item });
    }
  };
  
  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#8b5cf6" />
        <Text style={styles.loadingText}>Loading conversation...</Text>
      </View>
    );
  }
  
  if (!conversation) {
    return (
      <View style={styles.errorContainer}>
        <Ionicons name="alert-circle-outline" size={64} color="#f87171" />
        <Text style={styles.errorText}>Conversation not found</Text>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Text style={styles.backButtonText}>Go Back</Text>
        </TouchableOpacity>
      </View>
    );
  }
  
  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : undefined}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 90 : 0}
    >
      {/* Recipient Info */}
      <View style={styles.recipientContainer}>
        <View style={styles.recipientInfo}>
          {conversation.recipientAvatar ? (
            <Image source={{ uri: conversation.recipientAvatar }} style={styles.recipientAvatar} />
          ) : (
            <View style={styles.recipientAvatarPlaceholder}>
              <Text style={styles.recipientAvatarText}>{recipientName.charAt(0)}</Text>
            </View>
          )}
          <Text style={styles.recipientName}>{recipientName}</Text>
        </View>
      </View>
      
      {/* Messages List */}
      <FlatList
        ref={flatListRef}
        data={getGroupedMessages()}
        renderItem={renderItem}
        keyExtractor={item => item.id}
        contentContainerStyle={styles.messagesContainer}
      />
      
      {/* Message Input */}
      <View style={styles.inputContainer}>
        <TextInput
          style={styles.textInput}
          placeholder="Type a message..."
          value={messageText}
          onChangeText={setMessageText}
          multiline
          maxLength={500}
        />
        <TouchableOpacity 
          style={[styles.sendButton, !messageText.trim() && styles.disabledSendButton]}
          onPress={handleSendMessage}
          disabled={!messageText.trim() || sending}
        >
          {sending ? (
            <ActivityIndicator size="small" color="white" />
          ) : (
            <Ionicons name="send" size={20} color="white" />
          )}
        </TouchableOpacity>
      </View>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    padding: 20,
  },
  errorText: {
    marginTop: 10,
    fontSize: 18,
    fontWeight: 'bold',
    color: '#666',
  },
  backButton: {
    marginTop: 20,
    paddingVertical: 10,
    paddingHorizontal: 20,
    backgroundColor: '#8b5cf6',
    borderRadius: 8,
  },
  backButtonText: {
    color: 'white',
    fontWeight: 'bold',
  },
  recipientContainer: {
    backgroundColor: 'white',
    paddingVertical: 10,
    paddingHorizontal: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  recipientInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  recipientAvatar: {
    width: 36,
    height: 36,
    borderRadius: 18,
    marginRight: 10,
  },
  recipientAvatarPlaceholder: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#8b5cf6',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 10,
  },
  recipientAvatarText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  recipientName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  messagesContainer: {
    padding: 15,
    paddingBottom: 20,
  },
  messageContainer: {
    marginBottom: 15,
    maxWidth: '80%',
  },
  userMessageContainer: {
    alignSelf: 'flex-end',
  },
  recipientMessageContainer: {
    alignSelf: 'flex-start',
  },
  messageBubble: {
    borderRadius: 16,
    paddingHorizontal: 15,
    paddingVertical: 10,
  },
  userMessageBubble: {
    backgroundColor: '#8b5cf6',
  },
  recipientMessageBubble: {
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: '#e5e7eb',
  },
  messageText: {
    fontSize: 16,
    lineHeight: 22,
  },
  userMessageText: {
    color: 'white',
  },
  recipientMessageText: {
    color: '#333',
  },
  messageFooter: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 5,
    paddingHorizontal: 5,
  },
  timestampText: {
    fontSize: 12,
    color: '#666',
    marginRight: 5,
  },
  messageStatus: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  dateSeparator: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: 15,
  },
  dateSeparatorLine: {
    flex: 1,
    height: 1,
    backgroundColor: '#e5e7eb',
  },
  dateSeparatorText: {
    fontSize: 12,
    color: '#666',
    marginHorizontal: 10,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    paddingHorizontal: 15,
    paddingVertical: 10,
    borderTopWidth: 1,
    borderTopColor: '#e5e7eb',
  },
  textInput: {
    flex: 1,
    backgroundColor: '#f3f4f6',
    borderRadius: 20,
    paddingHorizontal: 15,
    paddingVertical: 10,
    maxHeight: 100,
    fontSize: 16,
  },
  sendButton: {
    backgroundColor: '#8b5cf6',
    borderRadius: 20,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 10,
  },
  disabledSendButton: {
    backgroundColor: '#c4b5fd',
  },
});

export default ConversationScreen;