import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Alert, ActivityIndicator } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { MainStackParamList } from '../navigation/MainNavigator';
import MapView, { Marker } from 'react-native-maps';

type DeliveryDetailScreenRouteProp = RouteProp<MainStackParamList, 'DeliveryDetail'>;
type NavigationProp = NativeStackNavigationProp<MainStackParamList>;

// Mock data for initial development
const mockDeliveries = {
  'del-001': {
    id: 'del-001',
    customerName: 'Green Valley Farm',
    address: '1234 Farm Road, Greenville, CA',
    coordinates: {
      latitude: 37.7749,
      longitude: -122.4194,
    },
    time: '10:30 AM',
    status: 'In Progress',
    type: 'Delivery',
    items: [
      { id: 'item-001', name: 'Fertilizer (Organic)', quantity: 2, unit: 'pallets' },
      { id: 'item-002', name: 'Fertilizer (Chemical)', quantity: 1, unit: 'pallet' }
    ],
    notes: 'Call customer upon arrival. Gate code: 1234.',
    contactName: 'John Smith',
    contactPhone: '(*************',
    contactEmail: '<EMAIL>',
    deliveryInstructions: 'Deliver to the main warehouse. Ask for the farm manager if John is not available.',
    signatureRequired: true,
    photoRequired: true,
    history: [
      { timestamp: '2023-08-15 08:30 AM', status: 'Scheduled', notes: 'Delivery scheduled' },
      { timestamp: '2023-08-15 09:15 AM', status: 'In Transit', notes: 'Driver en route' },
      { timestamp: '2023-08-15 10:00 AM', status: 'In Progress', notes: 'Arrived at location' }
    ]
  },
  'del-002': {
    id: 'del-002',
    customerName: 'Sunrise Orchards',
    address: '567 Orchard Lane, Sunnydale, CA',
    coordinates: {
      latitude: 37.8049,
      longitude: -122.2711,
    },
    time: '1:15 PM',
    status: 'Scheduled',
    type: 'Delivery',
    items: [
      { id: 'item-003', name: 'Pesticide (Organic)', quantity: 2, unit: 'pallets' },
      { id: 'item-004', name: 'Gardening Tools', quantity: 1, unit: 'pallet' }
    ],
    notes: 'Gate code: 1234',
    contactName: 'Sarah Johnson',
    contactPhone: '(*************',
    contactEmail: '<EMAIL>',
    deliveryInstructions: 'Deliver to the storage shed behind the main building.',
    signatureRequired: true,
    photoRequired: false,
    history: [
      { timestamp: '2023-08-15 08:00 AM', status: 'Scheduled', notes: 'Delivery scheduled' }
    ]
  }
};

const DeliveryDetailScreen: React.FC = () => {
  const navigation = useNavigation<NavigationProp>();
  const route = useRoute<DeliveryDetailScreenRouteProp>();
  const { deliveryId } = route.params;
  
  const [delivery, setDelivery] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  
  useEffect(() => {
    // Simulate API call to fetch delivery details
    setTimeout(() => {
      const deliveryData = mockDeliveries[deliveryId];
      setDelivery(deliveryData);
      setLoading(false);
    }, 1000);
  }, [deliveryId]);
  
  const handleStartNavigation = () => {
    navigation.navigate('Navigation');
  };
  
  const handleCaptureSignature = () => {
    navigation.navigate('Signature', { deliveryId });
  };
  
  const handleCapturePhoto = () => {
    navigation.navigate('PhotoCapture', { deliveryId });
  };
  
  const handleCompleteDelivery = () => {
    Alert.alert(
      'Complete Delivery',
      'Are you sure you want to mark this delivery as complete?',
      [
        {
          text: 'Cancel',
          style: 'cancel'
        },
        {
          text: 'Complete',
          onPress: () => {
            // In a real app, this would update the delivery status via API
            Alert.alert('Success', 'Delivery marked as complete!');
            navigation.goBack();
          }
        }
      ]
    );
  };
  
  const handleContactCustomer = () => {
    Alert.alert(
      'Contact Customer',
      'Choose contact method:',
      [
        {
          text: 'Call',
          onPress: () => console.log('Call customer')
        },
        {
          text: 'Text',
          onPress: () => console.log('Text customer')
        },
        {
          text: 'Email',
          onPress: () => console.log('Email customer')
        },
        {
          text: 'Cancel',
          style: 'cancel'
        }
      ]
    );
  };
  
  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#8b5cf6" />
        <Text style={styles.loadingText}>Loading delivery details...</Text>
      </View>
    );
  }
  
  if (!delivery) {
    return (
      <View style={styles.errorContainer}>
        <Ionicons name="alert-circle-outline" size={64} color="#f87171" />
        <Text style={styles.errorText}>Delivery not found</Text>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Text style={styles.backButtonText}>Go Back</Text>
        </TouchableOpacity>
      </View>
    );
  }
  
  // Determine status color
  let statusColor = '#8b5cf6'; // Default purple
  if (delivery.status === 'Completed') {
    statusColor = '#10b981'; // Green
  } else if (delivery.status === 'In Progress') {
    statusColor = '#3b82f6'; // Blue
  }
  
  return (
    <ScrollView style={styles.container}>
      {/* Header Section */}
      <View style={styles.headerContainer}>
        <View style={styles.headerContent}>
          <Text style={styles.customerName}>{delivery.customerName}</Text>
          <View style={[styles.statusBadge, { backgroundColor: `${statusColor}20` }]}>
            <Text style={[styles.statusText, { color: statusColor }]}>{delivery.status}</Text>
          </View>
        </View>
        <Text style={styles.deliveryType}>{delivery.type} • {delivery.time}</Text>
      </View>
      
      {/* Map Section */}
      <View style={styles.mapContainer}>
        <MapView
          style={styles.map}
          initialRegion={{
            latitude: delivery.coordinates.latitude,
            longitude: delivery.coordinates.longitude,
            latitudeDelta: 0.01,
            longitudeDelta: 0.01,
          }}
        >
          <Marker
            coordinate={{
              latitude: delivery.coordinates.latitude,
              longitude: delivery.coordinates.longitude,
            }}
            title={delivery.customerName}
            description={delivery.address}
          />
        </MapView>
        <TouchableOpacity 
          style={styles.navigationButton}
          onPress={handleStartNavigation}
        >
          <Ionicons name="navigate" size={20} color="white" />
          <Text style={styles.navigationButtonText}>Start Navigation</Text>
        </TouchableOpacity>
      </View>
      
      {/* Address Section */}
      <View style={styles.sectionContainer}>
        <View style={styles.sectionHeader}>
          <Ionicons name="location" size={20} color="#8b5cf6" />
          <Text style={styles.sectionTitle}>Address</Text>
        </View>
        <Text style={styles.addressText}>{delivery.address}</Text>
      </View>
      
      {/* Items Section */}
      <View style={styles.sectionContainer}>
        <View style={styles.sectionHeader}>
          <Ionicons name="cube" size={20} color="#8b5cf6" />
          <Text style={styles.sectionTitle}>Items</Text>
        </View>
        {delivery.items.map((item: any) => (
          <View key={item.id} style={styles.itemContainer}>
            <Text style={styles.itemName}>{item.name}</Text>
            <Text style={styles.itemQuantity}>{item.quantity} {item.unit}</Text>
          </View>
        ))}
      </View>
      
      {/* Contact Section */}
      <View style={styles.sectionContainer}>
        <View style={styles.sectionHeader}>
          <Ionicons name="person" size={20} color="#8b5cf6" />
          <Text style={styles.sectionTitle}>Contact</Text>
        </View>
        <View style={styles.contactContainer}>
          <View style={styles.contactInfo}>
            <Text style={styles.contactName}>{delivery.contactName}</Text>
            <Text style={styles.contactDetail}>{delivery.contactPhone}</Text>
            <Text style={styles.contactDetail}>{delivery.contactEmail}</Text>
          </View>
          <TouchableOpacity 
            style={styles.contactButton}
            onPress={handleContactCustomer}
          >
            <Ionicons name="call" size={20} color="white" />
          </TouchableOpacity>
        </View>
      </View>
      
      {/* Notes Section */}
      <View style={styles.sectionContainer}>
        <View style={styles.sectionHeader}>
          <Ionicons name="document-text" size={20} color="#8b5cf6" />
          <Text style={styles.sectionTitle}>Notes & Instructions</Text>
        </View>
        <Text style={styles.notesText}>{delivery.notes}</Text>
        <Text style={styles.instructionsTitle}>Delivery Instructions:</Text>
        <Text style={styles.instructionsText}>{delivery.deliveryInstructions}</Text>
      </View>
      
      {/* Requirements Section */}
      <View style={styles.sectionContainer}>
        <View style={styles.sectionHeader}>
          <Ionicons name="checkmark-circle" size={20} color="#8b5cf6" />
          <Text style={styles.sectionTitle}>Requirements</Text>
        </View>
        <View style={styles.requirementsContainer}>
          <View style={styles.requirementItem}>
            <Ionicons 
              name={delivery.signatureRequired ? "checkmark-circle" : "close-circle"} 
              size={20} 
              color={delivery.signatureRequired ? "#10b981" : "#f87171"} 
            />
            <Text style={styles.requirementText}>Signature Required</Text>
          </View>
          <View style={styles.requirementItem}>
            <Ionicons 
              name={delivery.photoRequired ? "checkmark-circle" : "close-circle"} 
              size={20} 
              color={delivery.photoRequired ? "#10b981" : "#f87171"} 
            />
            <Text style={styles.requirementText}>Photo Required</Text>
          </View>
        </View>
      </View>
      
      {/* History Section */}
      <View style={styles.sectionContainer}>
        <View style={styles.sectionHeader}>
          <Ionicons name="time" size={20} color="#8b5cf6" />
          <Text style={styles.sectionTitle}>History</Text>
        </View>
        {delivery.history.map((event: any, index: number) => (
          <View key={index} style={styles.historyItem}>
            <View style={styles.historyTimeline}>
              <View style={styles.historyDot} />
              {index < delivery.history.length - 1 && <View style={styles.historyLine} />}
            </View>
            <View style={styles.historyContent}>
              <Text style={styles.historyStatus}>{event.status}</Text>
              <Text style={styles.historyTimestamp}>{event.timestamp}</Text>
              <Text style={styles.historyNotes}>{event.notes}</Text>
            </View>
          </View>
        ))}
      </View>
      
      {/* Action Buttons */}
      <View style={styles.actionContainer}>
        {delivery.signatureRequired && (
          <TouchableOpacity 
            style={[styles.actionButton, styles.secondaryButton]}
            onPress={handleCaptureSignature}
          >
            <Ionicons name="create" size={20} color="#8b5cf6" />
            <Text style={styles.secondaryButtonText}>Capture Signature</Text>
          </TouchableOpacity>
        )}
        
        {delivery.photoRequired && (
          <TouchableOpacity 
            style={[styles.actionButton, styles.secondaryButton]}
            onPress={handleCapturePhoto}
          >
            <Ionicons name="camera" size={20} color="#8b5cf6" />
            <Text style={styles.secondaryButtonText}>Capture Photo</Text>
          </TouchableOpacity>
        )}
        
        <TouchableOpacity 
          style={[styles.actionButton, styles.primaryButton]}
          onPress={handleCompleteDelivery}
        >
          <Ionicons name="checkmark-circle" size={20} color="white" />
          <Text style={styles.primaryButtonText}>Complete Delivery</Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    padding: 20,
  },
  errorText: {
    marginTop: 10,
    fontSize: 18,
    fontWeight: 'bold',
    color: '#666',
  },
  backButton: {
    marginTop: 20,
    paddingVertical: 10,
    paddingHorizontal: 20,
    backgroundColor: '#8b5cf6',
    borderRadius: 8,
  },
  backButtonText: {
    color: 'white',
    fontWeight: 'bold',
  },
  headerContainer: {
    backgroundColor: 'white',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  customerName: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  statusBadge: {
    borderRadius: 4,
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  statusText: {
    fontSize: 14,
    fontWeight: '500',
  },
  deliveryType: {
    fontSize: 14,
    color: '#666',
    marginTop: 5,
  },
  mapContainer: {
    height: 200,
    position: 'relative',
  },
  map: {
    ...StyleSheet.absoluteFillObject,
  },
  navigationButton: {
    position: 'absolute',
    bottom: 10,
    right: 10,
    backgroundColor: '#8b5cf6',
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
  },
  navigationButtonText: {
    color: 'white',
    fontWeight: '500',
    marginLeft: 5,
  },
  sectionContainer: {
    backgroundColor: 'white',
    padding: 15,
    marginTop: 10,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginLeft: 8,
  },
  addressText: {
    fontSize: 16,
    color: '#333',
  },
  itemContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  itemName: {
    fontSize: 14,
    color: '#333',
  },
  itemQuantity: {
    fontSize: 14,
    color: '#666',
  },
  contactContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  contactInfo: {
    flex: 1,
  },
  contactName: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
  },
  contactDetail: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  contactButton: {
    backgroundColor: '#8b5cf6',
    padding: 10,
    borderRadius: 8,
  },
  notesText: {
    fontSize: 14,
    color: '#333',
    marginBottom: 10,
  },
  instructionsTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
    marginTop: 5,
  },
  instructionsText: {
    fontSize: 14,
    color: '#333',
    marginTop: 2,
  },
  requirementsContainer: {
    marginTop: 5,
  },
  requirementItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  requirementText: {
    fontSize: 14,
    color: '#333',
    marginLeft: 8,
  },
  historyItem: {
    flexDirection: 'row',
    marginBottom: 15,
  },
  historyTimeline: {
    width: 20,
    alignItems: 'center',
  },
  historyDot: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: '#8b5cf6',
  },
  historyLine: {
    width: 2,
    height: '100%',
    backgroundColor: '#e5e7eb',
    marginTop: 5,
    marginBottom: -15,
  },
  historyContent: {
    flex: 1,
    marginLeft: 10,
  },
  historyStatus: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
  },
  historyTimestamp: {
    fontSize: 12,
    color: '#666',
    marginTop: 2,
  },
  historyNotes: {
    fontSize: 14,
    color: '#333',
    marginTop: 2,
  },
  actionContainer: {
    padding: 15,
    marginTop: 10,
    marginBottom: 20,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderRadius: 8,
    marginBottom: 10,
  },
  primaryButton: {
    backgroundColor: '#8b5cf6',
  },
  primaryButtonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 16,
    marginLeft: 8,
  },
  secondaryButton: {
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: '#8b5cf6',
  },
  secondaryButtonText: {
    color: '#8b5cf6',
    fontWeight: 'bold',
    fontSize: 16,
    marginLeft: 8,
  },
});

export default DeliveryDetailScreen;