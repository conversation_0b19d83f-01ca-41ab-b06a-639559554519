import React, { useState, useEffect, useRef } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Alert, ActivityIndicator, Dimensions, ScrollView, Switch } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation, useRoute } from '@react-navigation/native';
import MapView, { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, PROVIDER_GOOGLE } from 'react-native-maps';
import * as Location from 'expo-location';
import navigationService, { Coordinates, Route, RouteStep, Maneuver, NavigationConfig } from '../../../../shared/services/navigationService';

const { width, height } = Dimensions.get('window');

// Mock data for deliveries
const mockDeliveries = [
  {
    id: 'del-001',
    customerName: 'Green Valley Farm',
    address: '1234 Farm Road, Greenville, CA',
    coordinates: {
      latitude: 37.7749,
      longitude: -122.4194,
    },
    timeWindow: {
      start: '08:00',
      end: '10:00',
    },
    priority: 'high',
    status: 'Scheduled',
  },
  {
    id: 'del-002',
    customerName: 'Sunrise Orchards',
    address: '567 Orchard Lane, Sunnydale, CA',
    coordinates: {
      latitude: 37.8049,
      longitude: -122.2711,
    },
    timeWindow: {
      start: '10:30',
      end: '12:30',
    },
    priority: 'medium',
    status: 'Scheduled',
  },
  {
    id: 'del-003',
    customerName: 'Blue Sky Dairy',
    address: '890 Dairy Road, Milkville, CA',
    coordinates: {
      latitude: 37.7249,
      longitude: -122.1694,
    },
    timeWindow: {
      start: '13:00',
      end: '15:00',
    },
    priority: 'medium',
    status: 'Scheduled',
  },
  {
    id: 'del-004',
    customerName: 'Harvest Hills',
    address: '456 Harvest Lane, Cropville, CA',
    coordinates: {
      latitude: 37.6549,
      longitude: -122.3194,
    },
    timeWindow: {
      start: '15:30',
      end: '17:30',
    },
    priority: 'low',
    status: 'Scheduled',
  }
];

// Default vehicle profile
const defaultVehicleProfile: NavigationConfig = {
  vehicleType: 'truck',
  dimensions: {
    height: 13.5, // feet
    width: 8.5, // feet
    length: 33, // feet
    weight: 26000 // pounds
  },
  routingPreferences: {
    avoidTolls: false,
    avoidHighways: false,
    avoidFerries: true,
    preferFuelEfficient: true
  }
};

// Optimization criteria options
type OptimizationCriteria = 'time' | 'distance' | 'fuel' | 'balanced';

interface OptimizedRoute {
  id: string;
  deliveries: typeof mockDeliveries;
  totalDistance: number;
  totalDuration: number;
  estimatedFuelUsage: number;
  polyline: Coordinates[];
}

const RouteOptimizationScreen: React.FC = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const mapRef = useRef<MapView>(null);

  const [loading, setLoading] = useState(true);
  const [optimizing, setOptimizing] = useState(false);
  const [currentLocation, setCurrentLocation] = useState<Location.LocationObject | null>(null);
  const [selectedDeliveries, setSelectedDeliveries] = useState<typeof mockDeliveries>([]);
  const [availableDeliveries, setAvailableDeliveries] = useState<typeof mockDeliveries>([]);
  const [optimizedRoutes, setOptimizedRoutes] = useState<OptimizedRoute[]>([]);
  const [selectedRoute, setSelectedRoute] = useState<OptimizedRoute | null>(null);
  const [optimizationCriteria, setOptimizationCriteria] = useState<OptimizationCriteria>('balanced');
  const [respectTimeWindows, setRespectTimeWindows] = useState(true);
  const [vehicleProfile, setVehicleProfile] = useState<NavigationConfig>(defaultVehicleProfile);
  const [distanceUnit, setDistanceUnit] = useState<'miles' | 'kilometers'>('miles');

  // Initialize and load data
  useEffect(() => {
    const initializeScreen = async () => {
      try {
        // Request location permissions
        let { status } = await Location.requestForegroundPermissionsAsync();
        if (status !== 'granted') {
          Alert.alert('Permission Denied', 'Location permission is required for route optimization.');
          setLoading(false);
          return;
        }

        // Get current location
        let location = await Location.getCurrentPositionAsync({});
        setCurrentLocation(location);

        // Initialize navigation service
        await navigationService.initialize(vehicleProfile);

        // In a real app, fetch deliveries from API
        // For now, use mock data
        setAvailableDeliveries(mockDeliveries);

        setLoading(false);
      } catch (error) {
        console.error('Error initializing screen:', error);
        Alert.alert('Error', 'Could not initialize route optimization. Please try again.');
        setLoading(false);
      }
    };

    initializeScreen();
  }, []);

  // Toggle delivery selection
  const toggleDeliverySelection = (delivery: typeof mockDeliveries[0]) => {
    if (selectedDeliveries.some(d => d.id === delivery.id)) {
      setSelectedDeliveries(selectedDeliveries.filter(d => d.id === delivery.id));
    } else {
      setSelectedDeliveries([...selectedDeliveries, delivery]);
    }
  };

  // Select all deliveries
  const selectAllDeliveries = () => {
    setSelectedDeliveries([...availableDeliveries]);
  };

  // Clear all selected deliveries
  const clearSelectedDeliveries = () => {
    setSelectedDeliveries([]);
  };

  // Optimize route
  const optimizeRoute = async () => {
    if (selectedDeliveries.length < 2) {
      Alert.alert('Error', 'Please select at least 2 deliveries to optimize.');
      return;
    }

    if (!currentLocation) {
      Alert.alert('Error', 'Current location is not available. Please try again.');
      return;
    }

    setOptimizing(true);

    try {
      // In a real app, this would call an API for route optimization
      // For now, simulate optimization with a delay

      // Mock optimization algorithm
      const optimizedRoutes = await simulateRouteOptimization(
        {
          latitude: currentLocation.coords.latitude,
          longitude: currentLocation.coords.longitude
        },
        selectedDeliveries,
        optimizationCriteria,
        respectTimeWindows
      );

      setOptimizedRoutes(optimizedRoutes);

      // Select the first route by default
      if (optimizedRoutes.length > 0) {
        setSelectedRoute(optimizedRoutes[0]);

        // Fit map to show the route
        if (mapRef.current) {
          const coordinates = [
            { 
              latitude: currentLocation.coords.latitude, 
              longitude: currentLocation.coords.longitude 
            },
            ...optimizedRoutes[0].deliveries.map(d => d.coordinates),
          ];

          mapRef.current.fitToCoordinates(coordinates, {
            edgePadding: { top: 50, right: 50, bottom: 300, left: 50 },
            animated: true,
          });
        }
      }

      setOptimizing(false);
    } catch (error) {
      console.error('Error optimizing route:', error);
      Alert.alert('Error', 'Could not optimize route. Please try again.');
      setOptimizing(false);
    }
  };

  // Simulate route optimization with AI-powered algorithm
  const simulateRouteOptimization = async (
    startLocation: Coordinates,
    deliveries: typeof mockDeliveries,
    criteria: OptimizationCriteria,
    respectTimeWindows: boolean
  ): Promise<OptimizedRoute[]> => {
    // In a real app, this would call a backend API for AI-powered route optimization
    // This implementation simulates an AI-powered algorithm with more sophisticated logic

    // Wait to simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Create a few different route options
    const routes: OptimizedRoute[] = [];

    // Helper function to calculate distance between two coordinates (Haversine formula)
    const calculateDistance = (coord1: Coordinates, coord2: Coordinates): number => {
      const R = 6371e3; // Earth radius in meters
      const φ1 = coord1.latitude * Math.PI / 180;
      const φ2 = coord2.latitude * Math.PI / 180;
      const Δφ = (coord2.latitude - coord1.latitude) * Math.PI / 180;
      const Δλ = (coord2.longitude - coord1.longitude) * Math.PI / 180;

      const a = Math.sin(Δφ/2) * Math.sin(Δφ/2) +
                Math.cos(φ1) * Math.cos(φ2) *
                Math.sin(Δλ/2) * Math.sin(Δλ/2);
      const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));

      return R * c; // Distance in meters
    };

    // Helper function to estimate travel time based on distance
    const estimateTravelTime = (distance: number): number => {
      // Assume average speed of 50 km/h (13.89 m/s)
      const averageSpeed = 13.89;
      // Add some variability based on traffic conditions (±20%)
      const trafficFactor = 0.8 + Math.random() * 0.4;
      return distance / (averageSpeed * trafficFactor);
    };

    // Helper function to estimate fuel usage based on distance and vehicle profile
    const estimateFuelUsage = (distance: number): number => {
      // Assume average fuel efficiency of 8 miles per gallon for a truck
      // Convert to gallons per meter
      const fuelEfficiency = 8 * 0.00062137; // miles per gallon to miles per meter
      // Add some variability based on driving conditions (±15%)
      const conditionsFactor = 0.85 + Math.random() * 0.3;
      return (distance / 1609.34) / (fuelEfficiency * conditionsFactor);
    };

    // Create a matrix of distances between all points
    const allPoints = [startLocation, ...deliveries.map(d => d.coordinates)];
    const distanceMatrix: number[][] = [];

    for (let i = 0; i < allPoints.length; i++) {
      distanceMatrix[i] = [];
      for (let j = 0; j < allPoints.length; j++) {
        if (i === j) {
          distanceMatrix[i][j] = 0;
        } else {
          distanceMatrix[i][j] = calculateDistance(allPoints[i], allPoints[j]);
        }
      }
    }

    // Option 1: Optimize for time (nearest neighbor with time windows)
    const timeOptimizedDeliveries = [...deliveries];
    const timeOptimizedSequence: typeof deliveries = [];
    let currentPoint = 0; // Start location
    let remainingDeliveries = [...timeOptimizedDeliveries];
    let totalTimeDistance = 0;

    while (remainingDeliveries.length > 0) {
      // Find the nearest delivery that respects time windows
      let bestNextIndex = -1;
      let bestNextDistance = Infinity;

      for (let i = 0; i < remainingDeliveries.length; i++) {
        const deliveryIndex = allPoints.findIndex(
          p => p.latitude === remainingDeliveries[i].coordinates.latitude && 
               p.longitude === remainingDeliveries[i].coordinates.longitude
        );

        const distance = distanceMatrix[currentPoint][deliveryIndex];

        // If respecting time windows, check if we can arrive within the window
        if (respectTimeWindows) {
          // Simple time window check (in a real app, this would be more sophisticated)
          const estimatedArrivalTime = new Date();
          const travelTimeSeconds = estimateTravelTime(totalTimeDistance + distance);
          estimatedArrivalTime.setSeconds(estimatedArrivalTime.getSeconds() + travelTimeSeconds);

          const deliveryStartTime = new Date();
          const [startHour, startMinute] = remainingDeliveries[i].timeWindow.start.split(':').map(Number);
          deliveryStartTime.setHours(startHour, startMinute, 0);

          const deliveryEndTime = new Date();
          const [endHour, endMinute] = remainingDeliveries[i].timeWindow.end.split(':').map(Number);
          deliveryEndTime.setHours(endHour, endMinute, 0);

          // Skip this delivery if we can't make it within the time window
          if (estimatedArrivalTime > deliveryEndTime) {
            continue;
          }
        }

        // Consider both distance and priority
        const priorityFactor = remainingDeliveries[i].priority === 'high' ? 0.7 : 
                              remainingDeliveries[i].priority === 'medium' ? 1.0 : 1.3;

        const adjustedDistance = distance * priorityFactor;

        if (adjustedDistance < bestNextDistance) {
          bestNextDistance = adjustedDistance;
          bestNextIndex = i;
        }
      }

      // If we found a valid next delivery
      if (bestNextIndex !== -1) {
        const nextDelivery = remainingDeliveries[bestNextIndex];
        timeOptimizedSequence.push(nextDelivery);

        // Update current point
        currentPoint = allPoints.findIndex(
          p => p.latitude === nextDelivery.coordinates.latitude && 
               p.longitude === nextDelivery.coordinates.longitude
        );

        totalTimeDistance += bestNextDistance;

        // Remove from remaining deliveries
        remainingDeliveries.splice(bestNextIndex, 1);
      } else if (respectTimeWindows && remainingDeliveries.length > 0) {
        // If we couldn't find any delivery that respects time windows,
        // take the one with the earliest time window
        remainingDeliveries.sort((a, b) => {
          return a.timeWindow.start.localeCompare(b.timeWindow.start);
        });

        const nextDelivery = remainingDeliveries[0];
        timeOptimizedSequence.push(nextDelivery);

        // Update current point
        currentPoint = allPoints.findIndex(
          p => p.latitude === nextDelivery.coordinates.latitude && 
               p.longitude === nextDelivery.coordinates.longitude
        );

        const deliveryIndex = allPoints.findIndex(
          p => p.latitude === nextDelivery.coordinates.latitude && 
               p.longitude === nextDelivery.coordinates.longitude
        );

        totalTimeDistance += distanceMatrix[currentPoint][deliveryIndex];

        // Remove from remaining deliveries
        remainingDeliveries.splice(0, 1);
      }
    }

    const timeOptimizedTotalDistance = calculateTotalRouteDistance(startLocation, timeOptimizedSequence);
    const timeOptimizedTotalDuration = estimateTravelTime(timeOptimizedTotalDistance);
    const timeOptimizedFuelUsage = estimateFuelUsage(timeOptimizedTotalDistance);

    const timeOptimizedRoute: OptimizedRoute = {
      id: 'route-time',
      deliveries: timeOptimizedSequence,
      totalDistance: timeOptimizedTotalDistance,
      totalDuration: timeOptimizedTotalDuration,
      estimatedFuelUsage: timeOptimizedFuelUsage,
      polyline: generateOptimizedPolyline(startLocation, timeOptimizedSequence),
    };

    // Option 2: Optimize for distance (nearest neighbor algorithm)
    const distanceOptimizedDeliveries = [...deliveries];
    const distanceOptimizedSequence: typeof deliveries = [];
    currentPoint = 0; // Start location
    remainingDeliveries = [...distanceOptimizedDeliveries];

    while (remainingDeliveries.length > 0) {
      // Find the nearest delivery
      let bestNextIndex = -1;
      let bestNextDistance = Infinity;

      for (let i = 0; i < remainingDeliveries.length; i++) {
        const deliveryIndex = allPoints.findIndex(
          p => p.latitude === remainingDeliveries[i].coordinates.latitude && 
               p.longitude === remainingDeliveries[i].coordinates.longitude
        );

        const distance = distanceMatrix[currentPoint][deliveryIndex];

        if (distance < bestNextDistance) {
          bestNextDistance = distance;
          bestNextIndex = i;
        }
      }

      // Add the nearest delivery to the sequence
      const nextDelivery = remainingDeliveries[bestNextIndex];
      distanceOptimizedSequence.push(nextDelivery);

      // Update current point
      currentPoint = allPoints.findIndex(
        p => p.latitude === nextDelivery.coordinates.latitude && 
             p.longitude === nextDelivery.coordinates.longitude
      );

      // Remove from remaining deliveries
      remainingDeliveries.splice(bestNextIndex, 1);
    }

    const distanceOptimizedTotalDistance = calculateTotalRouteDistance(startLocation, distanceOptimizedSequence);
    const distanceOptimizedTotalDuration = estimateTravelTime(distanceOptimizedTotalDistance);
    const distanceOptimizedFuelUsage = estimateFuelUsage(distanceOptimizedTotalDistance);

    const distanceOptimizedRoute: OptimizedRoute = {
      id: 'route-distance',
      deliveries: distanceOptimizedSequence,
      totalDistance: distanceOptimizedTotalDistance,
      totalDuration: distanceOptimizedTotalDuration,
      estimatedFuelUsage: distanceOptimizedFuelUsage,
      polyline: generateOptimizedPolyline(startLocation, distanceOptimizedSequence),
    };

    // Option 3: Optimize for fuel efficiency
    // For fuel efficiency, we'll consider both distance and traffic conditions
    const fuelOptimizedDeliveries = [...deliveries];
    const fuelOptimizedSequence: typeof deliveries = [];
    currentPoint = 0; // Start location
    remainingDeliveries = [...fuelOptimizedDeliveries];

    while (remainingDeliveries.length > 0) {
      // Find the delivery that minimizes fuel usage
      let bestNextIndex = -1;
      let bestFuelUsage = Infinity;

      for (let i = 0; i < remainingDeliveries.length; i++) {
        const deliveryIndex = allPoints.findIndex(
          p => p.latitude === remainingDeliveries[i].coordinates.latitude && 
               p.longitude === remainingDeliveries[i].coordinates.longitude
        );

        const distance = distanceMatrix[currentPoint][deliveryIndex];

        // Estimate fuel usage for this leg
        // Consider traffic conditions and road type (simplified simulation)
        const trafficFactor = 0.8 + Math.random() * 0.4; // Random traffic factor
        const roadTypeFactor = 0.9 + Math.random() * 0.2; // Random road type factor

        const fuelUsage = estimateFuelUsage(distance) * trafficFactor * roadTypeFactor;

        if (fuelUsage < bestFuelUsage) {
          bestFuelUsage = fuelUsage;
          bestNextIndex = i;
        }
      }

      // Add the most fuel-efficient delivery to the sequence
      const nextDelivery = remainingDeliveries[bestNextIndex];
      fuelOptimizedSequence.push(nextDelivery);

      // Update current point
      currentPoint = allPoints.findIndex(
        p => p.latitude === nextDelivery.coordinates.latitude && 
             p.longitude === nextDelivery.coordinates.longitude
      );

      // Remove from remaining deliveries
      remainingDeliveries.splice(bestNextIndex, 1);
    }

    const fuelOptimizedTotalDistance = calculateTotalRouteDistance(startLocation, fuelOptimizedSequence);
    const fuelOptimizedTotalDuration = estimateTravelTime(fuelOptimizedTotalDistance);
    const fuelOptimizedFuelUsage = estimateFuelUsage(fuelOptimizedTotalDistance) * 0.9; // Slightly better fuel efficiency

    const fuelOptimizedRoute: OptimizedRoute = {
      id: 'route-fuel',
      deliveries: fuelOptimizedSequence,
      totalDistance: fuelOptimizedTotalDistance,
      totalDuration: fuelOptimizedTotalDuration,
      estimatedFuelUsage: fuelOptimizedFuelUsage,
      polyline: generateOptimizedPolyline(startLocation, fuelOptimizedSequence),
    };

    // Helper function to calculate total route distance
    function calculateTotalRouteDistance(start: Coordinates, deliverySequence: typeof deliveries): number {
      let totalDistance = 0;
      let currentCoord = start;

      for (const delivery of deliverySequence) {
        totalDistance += calculateDistance(currentCoord, delivery.coordinates);
        currentCoord = delivery.coordinates;
      }

      return totalDistance;
    }

    // Add routes based on selected criteria
    if (criteria === 'time' || criteria === 'balanced') {
      routes.push(timeOptimizedRoute);
    }

    if (criteria === 'distance' || criteria === 'balanced') {
      routes.push(distanceOptimizedRoute);
    }

    if (criteria === 'fuel' || criteria === 'balanced') {
      routes.push(fuelOptimizedRoute);
    }

    return routes;
  };

  // Generate mock polyline for visualization
  const generateMockPolyline = (
    startLocation: Coordinates,
    deliveries: typeof mockDeliveries
  ): Coordinates[] => {
    const polyline: Coordinates[] = [startLocation];

    // Add points for each delivery
    deliveries.forEach(delivery => {
      // Add some intermediate points to make the line look more realistic
      const latDiff = delivery.coordinates.latitude - polyline[polyline.length - 1].latitude;
      const lngDiff = delivery.coordinates.longitude - polyline[polyline.length - 1].longitude;

      // Add 3 intermediate points
      for (let i = 1; i <= 3; i++) {
        polyline.push({
          latitude: polyline[polyline.length - 1].latitude + (latDiff / 4),
          longitude: polyline[polyline.length - 1].longitude + (lngDiff / 4),
        });
      }

      // Add the delivery point
      polyline.push(delivery.coordinates);
    });

    return polyline;
  };

  // Generate optimized polyline for visualization with more realistic road-like paths
  const generateOptimizedPolyline = (
    startLocation: Coordinates,
    deliveries: typeof mockDeliveries
  ): Coordinates[] => {
    const polyline: Coordinates[] = [startLocation];
    let currentLocation = startLocation;

    // For each delivery in the sequence
    for (const delivery of deliveries) {
      // Generate a more realistic path between current location and delivery
      const pathPoints = generateRealisticPath(currentLocation, delivery.coordinates);

      // Add all points except the first one (to avoid duplication)
      polyline.push(...pathPoints.slice(1));

      // Update current location
      currentLocation = delivery.coordinates;
    }

    return polyline;
  };

  // Helper function to generate a realistic path between two points
  const generateRealisticPath = (start: Coordinates, end: Coordinates): Coordinates[] => {
    const path: Coordinates[] = [start];

    // Calculate direct distance and angle between points
    const latDiff = end.latitude - start.latitude;
    const lngDiff = end.longitude - start.longitude;
    const directDistance = Math.sqrt(latDiff * latDiff + lngDiff * lngDiff);
    const angle = Math.atan2(lngDiff, latDiff);

    // Determine number of points based on distance
    const numPoints = Math.max(5, Math.ceil(directDistance * 500));

    // Generate intermediate points with some randomness to simulate roads
    for (let i = 1; i < numPoints; i++) {
      const ratio = i / numPoints;

      // Add some randomness to create curves
      // More randomness in the middle, less at the endpoints
      const randomFactor = 0.0002 * Math.sin(ratio * Math.PI); // Max deviation
      const perpAngle = angle + Math.PI / 2; // Perpendicular to direct path

      // Calculate position with randomness
      const randomDeviation = (Math.random() - 0.5) * randomFactor;
      const deviationLat = Math.cos(perpAngle) * randomDeviation;
      const deviationLng = Math.sin(perpAngle) * randomDeviation;

      // Add point with interpolation and randomness
      path.push({
        latitude: start.latitude + latDiff * ratio + deviationLat,
        longitude: start.longitude + lngDiff * ratio + deviationLng
      });

      // Occasionally add a more significant turn to simulate road networks
      if (Math.random() < 0.1 && i > 1 && i < numPoints - 1) {
        const turnAngle = (Math.random() - 0.5) * Math.PI / 4; // Up to 45 degrees turn
        const turnDistance = 0.0001; // Small distance for the turn

        // Add a point for the turn
        const lastPoint = path[path.length - 1];
        path.push({
          latitude: lastPoint.latitude + Math.cos(angle + turnAngle) * turnDistance,
          longitude: lastPoint.longitude + Math.sin(angle + turnAngle) * turnDistance
        });
      }
    }

    // Add the end point
    path.push(end);

    return path;
  };

  // Start navigation with the selected route
  const startNavigation = () => {
    if (!selectedRoute) {
      Alert.alert('Error', 'No route selected. Please select a route first.');
      return;
    }

    // Navigate to EnhancedNavigationScreen with the first delivery
    if (selectedRoute.deliveries.length > 0) {
      navigation.navigate('EnhancedNavigation' as never, { deliveryId: selectedRoute.deliveries[0].id } as never);
    }
  };

  // Format time for display
  const formatTime = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);

    if (hours > 0) {
      return `${hours} hr ${minutes} min`;
    } else {
      return `${minutes} min`;
    }
  };

  // Format distance for display
  const formatDistance = (meters: number): string => {
    if (distanceUnit === 'miles') {
      const miles = meters / 1609.34;
      return `${miles.toFixed(1)} mi`;
    } else {
      const kilometers = meters / 1000;
      return `${kilometers.toFixed(1)} km`;
    }
  };

  // Toggle distance unit
  const toggleDistanceUnit = () => {
    const newUnit = distanceUnit === 'miles' ? 'kilometers' : 'miles';
    setDistanceUnit(newUnit);
  };

  // Render loading state
  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#8b5cf6" />
        <Text style={styles.loadingText}>Loading route optimization...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Map View */}
      <MapView
        ref={mapRef}
        style={styles.map}
        provider={PROVIDER_GOOGLE}
        initialRegion={currentLocation ? {
          latitude: currentLocation.coords.latitude,
          longitude: currentLocation.coords.longitude,
          latitudeDelta: 0.05,
          longitudeDelta: 0.05,
        } : undefined}
        showsUserLocation
        showsMyLocationButton
        showsCompass
        showsScale
      >
        {/* Current Location Marker */}
        {currentLocation && (
          <Marker
            coordinate={{
              latitude: currentLocation.coords.latitude,
              longitude: currentLocation.coords.longitude,
            }}
            title="Your Location"
            pinColor="#10b981"
          />
        )}

        {/* Delivery Markers */}
        {selectedDeliveries.map((delivery, index) => (
          <Marker
            key={delivery.id}
            coordinate={delivery.coordinates}
            title={delivery.customerName}
            description={delivery.address}
            pinColor={selectedRoute?.deliveries.some(d => d.id === delivery.id) ? '#8b5cf6' : '#3b82f6'}
          >
            {selectedRoute?.deliveries.some(d => d.id === delivery.id) && (
              <View style={styles.markerOrder}>
                <Text style={styles.markerOrderText}>
                  {selectedRoute.deliveries.findIndex(d => d.id === delivery.id) + 1}
                </Text>
              </View>
            )}
          </Marker>
        ))}

        {/* Route Polyline */}
        {selectedRoute && (
          <Polyline
            coordinates={selectedRoute.polyline}
            strokeWidth={4}
            strokeColor="#8b5cf6"
          />
        )}
      </MapView>

      {/* Optimization Panel */}
      {!selectedRoute && (
        <View style={styles.optimizationPanel}>
          <View style={styles.panelHeader}>
            <Text style={styles.panelTitle}>Route Optimization</Text>
            <TouchableOpacity 
              style={styles.closeButton}
              onPress={() => navigation.goBack()}
            >
              <Ionicons name="close" size={24} color="#666" />
            </TouchableOpacity>
          </View>

          <Text style={styles.sectionTitle}>Select Deliveries</Text>

          <View style={styles.deliverySelectionHeader}>
            <Text style={styles.deliveryCountText}>
              {selectedDeliveries.length} of {availableDeliveries.length} selected
            </Text>
            <View style={styles.selectionActions}>
              <TouchableOpacity 
                style={styles.selectionActionButton}
                onPress={selectAllDeliveries}
              >
                <Text style={styles.selectionActionText}>Select All</Text>
              </TouchableOpacity>
              <TouchableOpacity 
                style={styles.selectionActionButton}
                onPress={clearSelectedDeliveries}
              >
                <Text style={styles.selectionActionText}>Clear</Text>
              </TouchableOpacity>
            </View>
          </View>

          <ScrollView style={styles.deliveriesList}>
            {availableDeliveries.map(delivery => (
              <TouchableOpacity
                key={delivery.id}
                style={[
                  styles.deliveryItem,
                  selectedDeliveries.some(d => d.id === delivery.id) && styles.selectedDeliveryItem
                ]}
                onPress={() => toggleDeliverySelection(delivery)}
              >
                <View style={styles.deliveryCheckbox}>
                  {selectedDeliveries.some(d => d.id === delivery.id) ? (
                    <Ionicons name="checkbox" size={24} color="#8b5cf6" />
                  ) : (
                    <Ionicons name="square-outline" size={24} color="#6b7280" />
                  )}
                </View>
                <View style={styles.deliveryInfo}>
                  <Text style={styles.deliveryName}>{delivery.customerName}</Text>
                  <Text style={styles.deliveryAddress}>{delivery.address}</Text>
                  <View style={styles.deliveryDetails}>
                    <View style={styles.deliveryDetailItem}>
                      <Ionicons name="time-outline" size={16} color="#6b7280" />
                      <Text style={styles.deliveryDetailText}>
                        {delivery.timeWindow.start} - {delivery.timeWindow.end}
                      </Text>
                    </View>
                    <View style={styles.deliveryDetailItem}>
                      <Ionicons name="flag-outline" size={16} color="#6b7280" />
                      <Text style={styles.deliveryDetailText}>
                        {delivery.priority.charAt(0).toUpperCase() + delivery.priority.slice(1)} Priority
                      </Text>
                    </View>
                  </View>
                </View>
              </TouchableOpacity>
            ))}
          </ScrollView>

          <Text style={styles.sectionTitle}>Optimization Settings</Text>

          <View style={styles.optimizationSettings}>
            <View style={styles.settingItem}>
              <Text style={styles.settingLabel}>Optimization Criteria</Text>
              <View style={styles.criteriaOptions}>
                <TouchableOpacity
                  style={[
                    styles.criteriaOption,
                    optimizationCriteria === 'time' && styles.selectedCriteriaOption
                  ]}
                  onPress={() => setOptimizationCriteria('time')}
                >
                  <Text style={[
                    styles.criteriaOptionText,
                    optimizationCriteria === 'time' && styles.selectedCriteriaOptionText
                  ]}>Time</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[
                    styles.criteriaOption,
                    optimizationCriteria === 'distance' && styles.selectedCriteriaOption
                  ]}
                  onPress={() => setOptimizationCriteria('distance')}
                >
                  <Text style={[
                    styles.criteriaOptionText,
                    optimizationCriteria === 'distance' && styles.selectedCriteriaOptionText
                  ]}>Distance</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[
                    styles.criteriaOption,
                    optimizationCriteria === 'fuel' && styles.selectedCriteriaOption
                  ]}
                  onPress={() => setOptimizationCriteria('fuel')}
                >
                  <Text style={[
                    styles.criteriaOptionText,
                    optimizationCriteria === 'fuel' && styles.selectedCriteriaOptionText
                  ]}>Fuel</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[
                    styles.criteriaOption,
                    optimizationCriteria === 'balanced' && styles.selectedCriteriaOption
                  ]}
                  onPress={() => setOptimizationCriteria('balanced')}
                >
                  <Text style={[
                    styles.criteriaOptionText,
                    optimizationCriteria === 'balanced' && styles.selectedCriteriaOptionText
                  ]}>Balanced</Text>
                </TouchableOpacity>
              </View>
            </View>

            <View style={styles.settingItem}>
              <Text style={styles.settingLabel}>Respect Time Windows</Text>
              <Switch
                value={respectTimeWindows}
                onValueChange={setRespectTimeWindows}
                trackColor={{ false: '#d1d5db', true: '#c4b5fd' }}
                thumbColor={respectTimeWindows ? '#8b5cf6' : '#f4f3f4'}
              />
            </View>

            <TouchableOpacity 
              style={styles.vehicleProfileButton}
              onPress={() => navigation.navigate('VehicleProfile' as never)}
            >
              <Ionicons name="car" size={16} color="#8b5cf6" />
              <Text style={styles.vehicleProfileText}>
                Vehicle Profile: {vehicleProfile.vehicleType}
              </Text>
            </TouchableOpacity>
          </View>

          <TouchableOpacity 
            style={[
              styles.optimizeButton,
              (selectedDeliveries.length < 2 || optimizing) && styles.disabledButton
            ]}
            onPress={optimizeRoute}
            disabled={selectedDeliveries.length < 2 || optimizing}
          >
            {optimizing ? (
              <ActivityIndicator size="small" color="white" />
            ) : (
              <>
                <Ionicons name="analytics" size={20} color="white" />
                <Text style={styles.optimizeButtonText}>Optimize Route</Text>
              </>
            )}
          </TouchableOpacity>
        </View>
      )}

      {/* Route Results Panel */}
      {selectedRoute && (
        <View style={styles.routeResultsPanel}>
          <View style={styles.panelHeader}>
            <Text style={styles.panelTitle}>Optimized Route</Text>
            <TouchableOpacity 
              style={styles.closeButton}
              onPress={() => setSelectedRoute(null)}
            >
              <Ionicons name="close" size={24} color="#666" />
            </TouchableOpacity>
          </View>

          <View style={styles.routeSummary}>
            <View style={styles.summaryItem}>
              <Ionicons name="location" size={20} color="#8b5cf6" />
              <Text style={styles.summaryText}>
                {selectedRoute.deliveries.length} Stops
              </Text>
            </View>
            <View style={styles.summaryItem}>
              <Ionicons name="time-outline" size={20} color="#8b5cf6" />
              <Text style={styles.summaryText}>
                {formatTime(selectedRoute.totalDuration)}
              </Text>
            </View>
            <View style={styles.summaryItem}>
              <Ionicons name="map-outline" size={20} color="#8b5cf6" />
              <Text style={styles.summaryText}>
                {formatDistance(selectedRoute.totalDistance)}
              </Text>
            </View>
            <View style={styles.summaryItem}>
              <Ionicons name="car-outline" size={20} color="#8b5cf6" />
              <Text style={styles.summaryText}>
                {selectedRoute.estimatedFuelUsage.toFixed(1)} gal
              </Text>
            </View>
          </View>

          <Text style={styles.sectionTitle}>Delivery Sequence</Text>

          <ScrollView style={styles.deliverySequence}>
            {selectedRoute.deliveries.map((delivery, index) => (
              <View key={delivery.id} style={styles.sequenceItem}>
                <View style={styles.sequenceNumber}>
                  <Text style={styles.sequenceNumberText}>{index + 1}</Text>
                </View>
                <View style={styles.sequenceInfo}>
                  <Text style={styles.sequenceName}>{delivery.customerName}</Text>
                  <Text style={styles.sequenceAddress}>{delivery.address}</Text>
                  <View style={styles.sequenceDetails}>
                    <View style={styles.sequenceDetailItem}>
                      <Ionicons name="time-outline" size={16} color="#6b7280" />
                      <Text style={styles.sequenceDetailText}>
                        {delivery.timeWindow.start} - {delivery.timeWindow.end}
                      </Text>
                    </View>
                  </View>
                </View>
              </View>
            ))}
          </ScrollView>

          {optimizedRoutes.length > 1 && (
            <>
              <Text style={styles.sectionTitle}>Alternative Routes</Text>
              <ScrollView 
                horizontal 
                showsHorizontalScrollIndicator={false}
                style={styles.alternativeRoutes}
              >
                {optimizedRoutes.map((route, index) => (
                  <TouchableOpacity
                    key={route.id}
                    style={[
                      styles.routeOption,
                      selectedRoute.id === route.id && styles.selectedRouteOption
                    ]}
                    onPress={() => setSelectedRoute(route)}
                  >
                    <Text style={styles.routeOptionTitle}>
                      Option {index + 1}
                    </Text>
                    <View style={styles.routeOptionDetails}>
                      <View style={styles.routeDetailItem}>
                        <Ionicons name="time-outline" size={16} color="#6b7280" />
                        <Text style={styles.routeDetailText}>
                          {formatTime(route.totalDuration)}
                        </Text>
                      </View>
                      <View style={styles.routeDetailItem}>
                        <Ionicons name="map-outline" size={16} color="#6b7280" />
                        <Text style={styles.routeDetailText}>
                          {formatDistance(route.totalDistance)}
                        </Text>
                      </View>
                      <View style={styles.routeDetailItem}>
                        <Ionicons name="car-outline" size={16} color="#6b7280" />
                        <Text style={styles.routeDetailText}>
                          {route.estimatedFuelUsage.toFixed(1)} gal
                        </Text>
                      </View>
                    </View>
                  </TouchableOpacity>
                ))}
              </ScrollView>
            </>
          )}

          <TouchableOpacity 
            style={styles.startNavigationButton}
            onPress={startNavigation}
          >
            <Ionicons name="navigate" size={20} color="white" />
            <Text style={styles.startNavigationText}>Start Navigation</Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  map: {
    width,
    height,
  },
  markerOrder: {
    backgroundColor: '#8b5cf6',
    borderRadius: 12,
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: 'white',
  },
  markerOrderText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 12,
  },
  optimizationPanel: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'white',
    borderTopLeftRadius: 15,
    borderTopRightRadius: 15,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 5,
    maxHeight: height * 0.7,
  },
  panelHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  panelTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1f2937',
  },
  closeButton: {
    padding: 5,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1f2937',
    marginTop: 10,
    marginBottom: 10,
  },
  deliverySelectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  deliveryCountText: {
    fontSize: 14,
    color: '#6b7280',
  },
  selectionActions: {
    flexDirection: 'row',
  },
  selectionActionButton: {
    marginLeft: 10,
  },
  selectionActionText: {
    color: '#8b5cf6',
    fontWeight: '500',
  },
  deliveriesList: {
    maxHeight: 200,
  },
  deliveryItem: {
    flexDirection: 'row',
    padding: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  selectedDeliveryItem: {
    backgroundColor: '#f3f0ff',
  },
  deliveryCheckbox: {
    marginRight: 10,
    justifyContent: 'center',
  },
  deliveryInfo: {
    flex: 1,
  },
  deliveryName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1f2937',
  },
  deliveryAddress: {
    fontSize: 14,
    color: '#6b7280',
    marginBottom: 5,
  },
  deliveryDetails: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  deliveryDetailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 10,
  },
  deliveryDetailText: {
    fontSize: 12,
    color: '#6b7280',
    marginLeft: 4,
  },
  optimizationSettings: {
    marginBottom: 15,
  },
  settingItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  settingLabel: {
    fontSize: 14,
    color: '#4b5563',
  },
  criteriaOptions: {
    flexDirection: 'row',
  },
  criteriaOption: {
    paddingVertical: 6,
    paddingHorizontal: 10,
    borderRadius: 20,
    backgroundColor: '#f3f4f6',
    marginLeft: 5,
  },
  selectedCriteriaOption: {
    backgroundColor: '#8b5cf6',
  },
  criteriaOptionText: {
    fontSize: 12,
    color: '#4b5563',
  },
  selectedCriteriaOptionText: {
    color: 'white',
    fontWeight: '500',
  },
  vehicleProfileButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 10,
    marginTop: 5,
  },
  vehicleProfileText: {
    color: '#8b5cf6',
    marginLeft: 5,
  },
  optimizeButton: {
    backgroundColor: '#8b5cf6',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderRadius: 10,
  },
  disabledButton: {
    backgroundColor: '#d1d5db',
  },
  optimizeButtonText: {
    color: 'white',
    fontWeight: 'bold',
    marginLeft: 5,
  },
  routeResultsPanel: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'white',
    borderTopLeftRadius: 15,
    borderTopRightRadius: 15,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 5,
    maxHeight: height * 0.7,
  },
  routeSummary: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    backgroundColor: '#f3f0ff',
    borderRadius: 10,
    padding: 15,
    marginBottom: 15,
  },
  summaryItem: {
    alignItems: 'center',
  },
  summaryText: {
    fontSize: 14,
    color: '#4b5563',
    fontWeight: '500',
    marginTop: 5,
  },
  deliverySequence: {
    maxHeight: 200,
    marginBottom: 15,
  },
  sequenceItem: {
    flexDirection: 'row',
    padding: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  sequenceNumber: {
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: '#8b5cf6',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 10,
  },
  sequenceNumberText: {
    color: 'white',
    fontWeight: 'bold',
  },
  sequenceInfo: {
    flex: 1,
  },
  sequenceName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1f2937',
  },
  sequenceAddress: {
    fontSize: 14,
    color: '#6b7280',
    marginBottom: 5,
  },
  sequenceDetails: {
    flexDirection: 'row',
  },
  sequenceDetailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 10,
  },
  sequenceDetailText: {
    fontSize: 12,
    color: '#6b7280',
    marginLeft: 4,
  },
  alternativeRoutes: {
    marginBottom: 15,
  },
  routeOption: {
    width: 150,
    backgroundColor: '#f9fafb',
    borderRadius: 10,
    padding: 10,
    marginRight: 10,
    borderWidth: 1,
    borderColor: '#e5e7eb',
  },
  selectedRouteOption: {
    borderColor: '#8b5cf6',
    backgroundColor: '#f3f0ff',
  },
  routeOptionTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 5,
  },
  routeOptionDetails: {
    gap: 3,
  },
  routeDetailItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  routeDetailText: {
    fontSize: 12,
    color: '#6b7280',
    marginLeft: 4,
  },
  startNavigationButton: {
    backgroundColor: '#8b5cf6',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderRadius: 10,
  },
  startNavigationText: {
    color: 'white',
    fontWeight: 'bold',
    marginLeft: 5,
  },
});

export default RouteOptimizationScreen;
