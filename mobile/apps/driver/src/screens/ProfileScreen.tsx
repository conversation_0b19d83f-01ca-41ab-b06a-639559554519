import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Image, Switch, Alert, ActivityIndicator } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { MainStackParamList } from '../navigation/MainNavigator';
import { useAuth } from '../../../../shared/store/AuthProvider';
import { useNotificationStore } from '../../../../shared/store/notificationStore';

type NavigationProp = NativeStackNavigationProp<MainStackParamList>;

// Mock data for initial development
const mockProfile = {
  id: 'driver-001',
  firstName: 'John',
  lastName: 'Smith',
  email: '<EMAIL>',
  phone: '(*************',
  profileImage: 'https://randomuser.me/api/portraits/men/32.jpg',
  driverLicense: '**********',
  licenseExpiry: '2025-06-30',
  employeeId: 'EMP-1234',
  jobTitle: 'Delivery Driver',
  department: 'Logistics',
  hireDate: '2020-03-15',
  emergencyContact: {
    name: 'Jane Smith',
    relationship: 'Spouse',
    phone: '(*************'
  },
  preferredVehicle: 'Truck #103',
  stats: {
    deliveriesCompleted: 1247,
    totalMiles: 15680,
    onTimePercentage: 98.5,
    averageRating: 4.9
  }
};

const ProfileScreen: React.FC = () => {
  const navigation = useNavigation<NavigationProp>();
  const { logout } = useAuth();
  const [profile, setProfile] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  
  // Get notification settings from the store
  const { 
    isNotificationsEnabled, 
    notificationSettings, 
    toggleNotifications, 
    updateNotificationSetting 
  } = useNotificationStore();
  
  useEffect(() => {
    // Simulate API call to fetch profile
    setTimeout(() => {
      setProfile(mockProfile);
      setLoading(false);
    }, 1000);
  }, []);
  
  const handleLogout = () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        {
          text: 'Cancel',
          style: 'cancel'
        },
        {
          text: 'Logout',
          onPress: () => logout()
        }
      ]
    );
  };
  
  const handleSettingsPress = () => {
    navigation.navigate('Settings');
  };
  
  const handleHelpPress = () => {
    navigation.navigate('Help');
  };
  
  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#8b5cf6" />
        <Text style={styles.loadingText}>Loading profile...</Text>
      </View>
    );
  }
  
  return (
    <ScrollView style={styles.container}>
      {/* Profile Header */}
      <View style={styles.profileHeader}>
        <View style={styles.profileImageContainer}>
          {profile.profileImage ? (
            <Image 
              source={{ uri: profile.profileImage }} 
              style={styles.profileImage} 
            />
          ) : (
            <View style={styles.profileImagePlaceholder}>
              <Text style={styles.profileImagePlaceholderText}>
                {profile.firstName.charAt(0)}{profile.lastName.charAt(0)}
              </Text>
            </View>
          )}
        </View>
        <Text style={styles.profileName}>{profile.firstName} {profile.lastName}</Text>
        <Text style={styles.profileTitle}>{profile.jobTitle}</Text>
        <Text style={styles.profileId}>ID: {profile.employeeId}</Text>
      </View>
      
      {/* Stats Section */}
      <View style={styles.statsContainer}>
        <View style={styles.statItem}>
          <Text style={styles.statNumber}>{profile.stats.deliveriesCompleted}</Text>
          <Text style={styles.statLabel}>Deliveries</Text>
        </View>
        <View style={styles.statDivider} />
        <View style={styles.statItem}>
          <Text style={styles.statNumber}>{profile.stats.totalMiles.toLocaleString()}</Text>
          <Text style={styles.statLabel}>Miles</Text>
        </View>
        <View style={styles.statDivider} />
        <View style={styles.statItem}>
          <Text style={styles.statNumber}>{profile.stats.onTimePercentage}%</Text>
          <Text style={styles.statLabel}>On Time</Text>
        </View>
        <View style={styles.statDivider} />
        <View style={styles.statItem}>
          <Text style={styles.statNumber}>{profile.stats.averageRating}</Text>
          <Text style={styles.statLabel}>Rating</Text>
        </View>
      </View>
      
      {/* Contact Information */}
      <View style={styles.sectionContainer}>
        <View style={styles.sectionHeader}>
          <Ionicons name="person" size={20} color="#8b5cf6" />
          <Text style={styles.sectionTitle}>Contact Information</Text>
        </View>
        
        <View style={styles.infoItem}>
          <Ionicons name="mail-outline" size={20} color="#666" style={styles.infoIcon} />
          <View style={styles.infoContent}>
            <Text style={styles.infoLabel}>Email</Text>
            <Text style={styles.infoValue}>{profile.email}</Text>
          </View>
        </View>
        
        <View style={styles.infoItem}>
          <Ionicons name="call-outline" size={20} color="#666" style={styles.infoIcon} />
          <View style={styles.infoContent}>
            <Text style={styles.infoLabel}>Phone</Text>
            <Text style={styles.infoValue}>{profile.phone}</Text>
          </View>
        </View>
        
        <View style={styles.infoItem}>
          <Ionicons name="alert-circle-outline" size={20} color="#666" style={styles.infoIcon} />
          <View style={styles.infoContent}>
            <Text style={styles.infoLabel}>Emergency Contact</Text>
            <Text style={styles.infoValue}>{profile.emergencyContact.name} ({profile.emergencyContact.relationship})</Text>
            <Text style={styles.infoSubvalue}>{profile.emergencyContact.phone}</Text>
          </View>
        </View>
      </View>
      
      {/* Driver Information */}
      <View style={styles.sectionContainer}>
        <View style={styles.sectionHeader}>
          <Ionicons name="car" size={20} color="#8b5cf6" />
          <Text style={styles.sectionTitle}>Driver Information</Text>
        </View>
        
        <View style={styles.infoItem}>
          <Ionicons name="card-outline" size={20} color="#666" style={styles.infoIcon} />
          <View style={styles.infoContent}>
            <Text style={styles.infoLabel}>Driver License</Text>
            <Text style={styles.infoValue}>{profile.driverLicense}</Text>
            <Text style={styles.infoSubvalue}>Expires: {new Date(profile.licenseExpiry).toLocaleDateString()}</Text>
          </View>
        </View>
        
        <View style={styles.infoItem}>
          <Ionicons name="car-outline" size={20} color="#666" style={styles.infoIcon} />
          <View style={styles.infoContent}>
            <Text style={styles.infoLabel}>Preferred Vehicle</Text>
            <Text style={styles.infoValue}>{profile.preferredVehicle}</Text>
          </View>
        </View>
        
        <View style={styles.infoItem}>
          <Ionicons name="business-outline" size={20} color="#666" style={styles.infoIcon} />
          <View style={styles.infoContent}>
            <Text style={styles.infoLabel}>Department</Text>
            <Text style={styles.infoValue}>{profile.department}</Text>
          </View>
        </View>
        
        <View style={styles.infoItem}>
          <Ionicons name="calendar-outline" size={20} color="#666" style={styles.infoIcon} />
          <View style={styles.infoContent}>
            <Text style={styles.infoLabel}>Hire Date</Text>
            <Text style={styles.infoValue}>{new Date(profile.hireDate).toLocaleDateString()}</Text>
          </View>
        </View>
      </View>
      
      {/* Notification Settings */}
      <View style={styles.sectionContainer}>
        <View style={styles.sectionHeader}>
          <Ionicons name="notifications" size={20} color="#8b5cf6" />
          <Text style={styles.sectionTitle}>Notification Settings</Text>
        </View>
        
        <View style={styles.settingItem}>
          <View style={styles.settingContent}>
            <Text style={styles.settingLabel}>Enable Notifications</Text>
            <Text style={styles.settingDescription}>Receive push notifications</Text>
          </View>
          <Switch
            value={isNotificationsEnabled}
            onValueChange={toggleNotifications}
            trackColor={{ false: '#d1d5db', true: '#c4b5fd' }}
            thumbColor={isNotificationsEnabled ? '#8b5cf6' : '#f4f3f4'}
          />
        </View>
        
        {isNotificationsEnabled && (
          <>
            <View style={styles.settingItem}>
              <View style={styles.settingContent}>
                <Text style={styles.settingLabel}>New Deliveries</Text>
                <Text style={styles.settingDescription}>Notifications for new assigned deliveries</Text>
              </View>
              <Switch
                value={notificationSettings.newDeliveries}
                onValueChange={(value) => updateNotificationSetting('newDeliveries', value)}
                trackColor={{ false: '#d1d5db', true: '#c4b5fd' }}
                thumbColor={notificationSettings.newDeliveries ? '#8b5cf6' : '#f4f3f4'}
              />
            </View>
            
            <View style={styles.settingItem}>
              <View style={styles.settingContent}>
                <Text style={styles.settingLabel}>Delivery Updates</Text>
                <Text style={styles.settingDescription}>Notifications for changes to deliveries</Text>
              </View>
              <Switch
                value={notificationSettings.deliveryUpdates}
                onValueChange={(value) => updateNotificationSetting('deliveryUpdates', value)}
                trackColor={{ false: '#d1d5db', true: '#c4b5fd' }}
                thumbColor={notificationSettings.deliveryUpdates ? '#8b5cf6' : '#f4f3f4'}
              />
            </View>
            
            <View style={styles.settingItem}>
              <View style={styles.settingContent}>
                <Text style={styles.settingLabel}>Schedule Changes</Text>
                <Text style={styles.settingDescription}>Notifications for schedule changes</Text>
              </View>
              <Switch
                value={notificationSettings.scheduleChanges}
                onValueChange={(value) => updateNotificationSetting('scheduleChanges', value)}
                trackColor={{ false: '#d1d5db', true: '#c4b5fd' }}
                thumbColor={notificationSettings.scheduleChanges ? '#8b5cf6' : '#f4f3f4'}
              />
            </View>
            
            <View style={styles.settingItem}>
              <View style={styles.settingContent}>
                <Text style={styles.settingLabel}>Messages</Text>
                <Text style={styles.settingDescription}>Notifications for new messages</Text>
              </View>
              <Switch
                value={notificationSettings.messages}
                onValueChange={(value) => updateNotificationSetting('messages', value)}
                trackColor={{ false: '#d1d5db', true: '#c4b5fd' }}
                thumbColor={notificationSettings.messages ? '#8b5cf6' : '#f4f3f4'}
              />
            </View>
            
            <View style={styles.settingItem}>
              <View style={styles.settingContent}>
                <Text style={styles.settingLabel}>System Announcements</Text>
                <Text style={styles.settingDescription}>Important system-wide notifications</Text>
              </View>
              <Switch
                value={notificationSettings.systemAnnouncements}
                onValueChange={(value) => updateNotificationSetting('systemAnnouncements', value)}
                trackColor={{ false: '#d1d5db', true: '#c4b5fd' }}
                thumbColor={notificationSettings.systemAnnouncements ? '#8b5cf6' : '#f4f3f4'}
              />
            </View>
          </>
        )}
      </View>
      
      {/* Actions */}
      <View style={styles.actionsContainer}>
        <TouchableOpacity 
          style={styles.actionButton}
          onPress={handleSettingsPress}
        >
          <Ionicons name="settings-outline" size={20} color="#8b5cf6" />
          <Text style={styles.actionButtonText}>Settings</Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={styles.actionButton}
          onPress={handleHelpPress}
        >
          <Ionicons name="help-circle-outline" size={20} color="#8b5cf6" />
          <Text style={styles.actionButtonText}>Help & Support</Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={[styles.actionButton, styles.logoutButton]}
          onPress={handleLogout}
        >
          <Ionicons name="log-out-outline" size={20} color="#f87171" />
          <Text style={styles.logoutButtonText}>Logout</Text>
        </TouchableOpacity>
      </View>
      
      {/* App Version */}
      <View style={styles.versionContainer}>
        <Text style={styles.versionText}>NxtAcre Driver App v1.0.0</Text>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  profileHeader: {
    backgroundColor: '#8b5cf6',
    padding: 20,
    alignItems: 'center',
  },
  profileImageContainer: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: 'white',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 5,
  },
  profileImage: {
    width: 100,
    height: 100,
    borderRadius: 50,
  },
  profileImagePlaceholder: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: '#c4b5fd',
    justifyContent: 'center',
    alignItems: 'center',
  },
  profileImagePlaceholderText: {
    fontSize: 36,
    fontWeight: 'bold',
    color: 'white',
  },
  profileName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 5,
  },
  profileTitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.8)',
    marginBottom: 5,
  },
  profileId: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.6)',
  },
  statsContainer: {
    flexDirection: 'row',
    backgroundColor: 'white',
    borderRadius: 10,
    margin: 15,
    marginTop: -20,
    padding: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
  },
  statDivider: {
    width: 1,
    height: '80%',
    backgroundColor: '#e5e7eb',
  },
  statNumber: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#8b5cf6',
    marginBottom: 5,
  },
  statLabel: {
    fontSize: 12,
    color: '#666',
  },
  sectionContainer: {
    backgroundColor: 'white',
    borderRadius: 10,
    margin: 15,
    marginTop: 5,
    marginBottom: 5,
    padding: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 15,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginLeft: 8,
  },
  infoItem: {
    flexDirection: 'row',
    marginBottom: 15,
  },
  infoIcon: {
    marginTop: 2,
    marginRight: 10,
  },
  infoContent: {
    flex: 1,
  },
  infoLabel: {
    fontSize: 12,
    color: '#666',
    marginBottom: 2,
  },
  infoValue: {
    fontSize: 16,
    color: '#333',
  },
  infoSubvalue: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  settingItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  settingContent: {
    flex: 1,
    marginRight: 10,
  },
  settingLabel: {
    fontSize: 16,
    color: '#333',
    marginBottom: 2,
  },
  settingDescription: {
    fontSize: 12,
    color: '#666',
  },
  actionsContainer: {
    backgroundColor: 'white',
    borderRadius: 10,
    margin: 15,
    marginTop: 5,
    padding: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  actionButtonText: {
    fontSize: 16,
    color: '#333',
    marginLeft: 10,
  },
  logoutButton: {
    borderBottomWidth: 0,
  },
  logoutButtonText: {
    fontSize: 16,
    color: '#f87171',
    marginLeft: 10,
  },
  versionContainer: {
    alignItems: 'center',
    padding: 20,
  },
  versionText: {
    fontSize: 12,
    color: '#999',
  },
});

export default ProfileScreen;