import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Switch, Alert, ActivityIndicator } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { MainStackParamList } from '../navigation/MainNavigator';
import { useAuth } from '../../../../shared/store/AuthProvider';
import { useNotificationStore } from '../../../../shared/store/notificationStore';

type NavigationProp = NativeStackNavigationProp<MainStackParamList>;

// Mock data for initial development
const mockSettings = {
  appearance: {
    theme: 'light',
    fontSize: 'medium',
  },
  privacy: {
    locationTracking: true,
    shareAnalytics: true,
  },
  navigation: {
    preferredMapApp: 'in-app',
    voiceNavigation: true,
    avoidTolls: false,
    avoidHighways: false,
  },
  data: {
    autoSync: true,
    syncOnWifiOnly: false,
    syncFrequency: 'realtime',
  },
  language: 'english',
  units: {
    distance: 'miles',
    weight: 'pounds',
    temperature: 'fahrenheit',
  }
};

const SettingsScreen: React.FC = () => {
  const navigation = useNavigation<NavigationProp>();
  const { logout } = useAuth();
  const [settings, setSettings] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  
  // Get notification settings from the store
  const { 
    isNotificationsEnabled, 
    notificationSettings, 
    toggleNotifications, 
    updateNotificationSetting 
  } = useNotificationStore();
  
  // Simulate loading settings
  useEffect(() => {
    setTimeout(() => {
      setSettings(mockSettings);
      setLoading(false);
    }, 1000);
  }, []);
  
  // Handle setting toggle
  const handleToggleSetting = (section: string, setting: string) => {
    if (!settings) return;
    
    const updatedSettings = { ...settings };
    updatedSettings[section][setting] = !updatedSettings[section][setting];
    setSettings(updatedSettings);
    
    // In a real app, this would save the settings to a server or local storage
    console.log(`Updated ${section}.${setting} to ${updatedSettings[section][setting]}`);
  };
  
  // Handle option selection
  const handleSelectOption = (section: string, setting: string, options: string[]) => {
    if (!settings) return;
    
    Alert.alert(
      `Select ${setting}`,
      '',
      [
        ...options.map(option => ({
          text: option.charAt(0).toUpperCase() + option.slice(1),
          onPress: () => {
            const updatedSettings = { ...settings };
            updatedSettings[section][setting] = option;
            setSettings(updatedSettings);
            
            // In a real app, this would save the settings to a server or local storage
            console.log(`Updated ${section}.${setting} to ${option}`);
          },
          style: settings[section][setting] === option ? 'default' : 'default'
        })),
        {
          text: 'Cancel',
          style: 'cancel'
        }
      ]
    );
  };
  
  // Handle language selection
  const handleSelectLanguage = () => {
    if (!settings) return;
    
    const languages = ['english', 'spanish', 'french', 'german', 'chinese'];
    
    Alert.alert(
      'Select Language',
      '',
      [
        ...languages.map(language => ({
          text: language.charAt(0).toUpperCase() + language.slice(1),
          onPress: () => {
            const updatedSettings = { ...settings };
            updatedSettings.language = language;
            setSettings(updatedSettings);
            
            // In a real app, this would save the settings to a server or local storage
            console.log(`Updated language to ${language}`);
          },
          style: settings.language === language ? 'default' : 'default'
        })),
        {
          text: 'Cancel',
          style: 'cancel'
        }
      ]
    );
  };
  
  // Handle logout
  const handleLogout = () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        {
          text: 'Cancel',
          style: 'cancel'
        },
        {
          text: 'Logout',
          onPress: () => logout()
        }
      ]
    );
  };
  
  // Handle clear cache
  const handleClearCache = () => {
    Alert.alert(
      'Clear Cache',
      'Are you sure you want to clear the app cache? This will not delete any of your data.',
      [
        {
          text: 'Cancel',
          style: 'cancel'
        },
        {
          text: 'Clear',
          onPress: () => {
            // In a real app, this would clear the cache
            Alert.alert('Success', 'Cache cleared successfully');
          }
        }
      ]
    );
  };
  
  // Handle reset settings
  const handleResetSettings = () => {
    Alert.alert(
      'Reset Settings',
      'Are you sure you want to reset all settings to default? This cannot be undone.',
      [
        {
          text: 'Cancel',
          style: 'cancel'
        },
        {
          text: 'Reset',
          onPress: () => {
            // In a real app, this would reset settings to default
            setSettings(mockSettings);
            Alert.alert('Success', 'Settings reset to default');
          }
        }
      ]
    );
  };
  
  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#8b5cf6" />
        <Text style={styles.loadingText}>Loading settings...</Text>
      </View>
    );
  }
  
  return (
    <ScrollView style={styles.container}>
      {/* Appearance Section */}
      <View style={styles.sectionContainer}>
        <View style={styles.sectionHeader}>
          <Ionicons name="color-palette" size={20} color="#8b5cf6" />
          <Text style={styles.sectionTitle}>Appearance</Text>
        </View>
        
        <TouchableOpacity 
          style={styles.settingItem}
          onPress={() => handleSelectOption('appearance', 'theme', ['light', 'dark', 'system'])}
        >
          <View style={styles.settingContent}>
            <Text style={styles.settingLabel}>Theme</Text>
            <Text style={styles.settingDescription}>Change app appearance</Text>
          </View>
          <View style={styles.settingValue}>
            <Text style={styles.settingValueText}>
              {settings.appearance.theme.charAt(0).toUpperCase() + settings.appearance.theme.slice(1)}
            </Text>
            <Ionicons name="chevron-forward" size={20} color="#8b5cf6" />
          </View>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={styles.settingItem}
          onPress={() => handleSelectOption('appearance', 'fontSize', ['small', 'medium', 'large'])}
        >
          <View style={styles.settingContent}>
            <Text style={styles.settingLabel}>Font Size</Text>
            <Text style={styles.settingDescription}>Adjust text size</Text>
          </View>
          <View style={styles.settingValue}>
            <Text style={styles.settingValueText}>
              {settings.appearance.fontSize.charAt(0).toUpperCase() + settings.appearance.fontSize.slice(1)}
            </Text>
            <Ionicons name="chevron-forward" size={20} color="#8b5cf6" />
          </View>
        </TouchableOpacity>
      </View>
      
      {/* Notifications Section */}
      <View style={styles.sectionContainer}>
        <View style={styles.sectionHeader}>
          <Ionicons name="notifications" size={20} color="#8b5cf6" />
          <Text style={styles.sectionTitle}>Notifications</Text>
        </View>
        
        <View style={styles.settingItem}>
          <View style={styles.settingContent}>
            <Text style={styles.settingLabel}>Enable Notifications</Text>
            <Text style={styles.settingDescription}>Receive push notifications</Text>
          </View>
          <Switch
            value={isNotificationsEnabled}
            onValueChange={toggleNotifications}
            trackColor={{ false: '#d1d5db', true: '#c4b5fd' }}
            thumbColor={isNotificationsEnabled ? '#8b5cf6' : '#f4f3f4'}
          />
        </View>
        
        {isNotificationsEnabled && (
          <>
            <View style={styles.settingItem}>
              <View style={styles.settingContent}>
                <Text style={styles.settingLabel}>New Deliveries</Text>
                <Text style={styles.settingDescription}>Notifications for new assigned deliveries</Text>
              </View>
              <Switch
                value={notificationSettings.newDeliveries}
                onValueChange={(value) => updateNotificationSetting('newDeliveries', value)}
                trackColor={{ false: '#d1d5db', true: '#c4b5fd' }}
                thumbColor={notificationSettings.newDeliveries ? '#8b5cf6' : '#f4f3f4'}
              />
            </View>
            
            <View style={styles.settingItem}>
              <View style={styles.settingContent}>
                <Text style={styles.settingLabel}>Delivery Updates</Text>
                <Text style={styles.settingDescription}>Notifications for changes to deliveries</Text>
              </View>
              <Switch
                value={notificationSettings.deliveryUpdates}
                onValueChange={(value) => updateNotificationSetting('deliveryUpdates', value)}
                trackColor={{ false: '#d1d5db', true: '#c4b5fd' }}
                thumbColor={notificationSettings.deliveryUpdates ? '#8b5cf6' : '#f4f3f4'}
              />
            </View>
            
            <View style={styles.settingItem}>
              <View style={styles.settingContent}>
                <Text style={styles.settingLabel}>Schedule Changes</Text>
                <Text style={styles.settingDescription}>Notifications for schedule changes</Text>
              </View>
              <Switch
                value={notificationSettings.scheduleChanges}
                onValueChange={(value) => updateNotificationSetting('scheduleChanges', value)}
                trackColor={{ false: '#d1d5db', true: '#c4b5fd' }}
                thumbColor={notificationSettings.scheduleChanges ? '#8b5cf6' : '#f4f3f4'}
              />
            </View>
            
            <View style={styles.settingItem}>
              <View style={styles.settingContent}>
                <Text style={styles.settingLabel}>Messages</Text>
                <Text style={styles.settingDescription}>Notifications for new messages</Text>
              </View>
              <Switch
                value={notificationSettings.messages}
                onValueChange={(value) => updateNotificationSetting('messages', value)}
                trackColor={{ false: '#d1d5db', true: '#c4b5fd' }}
                thumbColor={notificationSettings.messages ? '#8b5cf6' : '#f4f3f4'}
              />
            </View>
          </>
        )}
      </View>
      
      {/* Privacy Section */}
      <View style={styles.sectionContainer}>
        <View style={styles.sectionHeader}>
          <Ionicons name="shield" size={20} color="#8b5cf6" />
          <Text style={styles.sectionTitle}>Privacy</Text>
        </View>
        
        <View style={styles.settingItem}>
          <View style={styles.settingContent}>
            <Text style={styles.settingLabel}>Location Tracking</Text>
            <Text style={styles.settingDescription}>Allow app to track your location</Text>
          </View>
          <Switch
            value={settings.privacy.locationTracking}
            onValueChange={() => handleToggleSetting('privacy', 'locationTracking')}
            trackColor={{ false: '#d1d5db', true: '#c4b5fd' }}
            thumbColor={settings.privacy.locationTracking ? '#8b5cf6' : '#f4f3f4'}
          />
        </View>
        
        <View style={styles.settingItem}>
          <View style={styles.settingContent}>
            <Text style={styles.settingLabel}>Share Analytics</Text>
            <Text style={styles.settingDescription}>Help improve the app by sharing usage data</Text>
          </View>
          <Switch
            value={settings.privacy.shareAnalytics}
            onValueChange={() => handleToggleSetting('privacy', 'shareAnalytics')}
            trackColor={{ false: '#d1d5db', true: '#c4b5fd' }}
            thumbColor={settings.privacy.shareAnalytics ? '#8b5cf6' : '#f4f3f4'}
          />
        </View>
      </View>
      
      {/* Navigation Section */}
      <View style={styles.sectionContainer}>
        <View style={styles.sectionHeader}>
          <Ionicons name="navigate" size={20} color="#8b5cf6" />
          <Text style={styles.sectionTitle}>Navigation</Text>
        </View>
        
        <TouchableOpacity 
          style={styles.settingItem}
          onPress={() => handleSelectOption('navigation', 'preferredMapApp', ['in-app', 'google-maps', 'apple-maps', 'waze'])}
        >
          <View style={styles.settingContent}>
            <Text style={styles.settingLabel}>Preferred Map App</Text>
            <Text style={styles.settingDescription}>Choose which map app to use for navigation</Text>
          </View>
          <View style={styles.settingValue}>
            <Text style={styles.settingValueText}>
              {settings.navigation.preferredMapApp === 'in-app' ? 'In-App' : 
               settings.navigation.preferredMapApp.split('-').map(word => 
                 word.charAt(0).toUpperCase() + word.slice(1)
               ).join(' ')}
            </Text>
            <Ionicons name="chevron-forward" size={20} color="#8b5cf6" />
          </View>
        </TouchableOpacity>
        
        <View style={styles.settingItem}>
          <View style={styles.settingContent}>
            <Text style={styles.settingLabel}>Voice Navigation</Text>
            <Text style={styles.settingDescription}>Enable voice instructions during navigation</Text>
          </View>
          <Switch
            value={settings.navigation.voiceNavigation}
            onValueChange={() => handleToggleSetting('navigation', 'voiceNavigation')}
            trackColor={{ false: '#d1d5db', true: '#c4b5fd' }}
            thumbColor={settings.navigation.voiceNavigation ? '#8b5cf6' : '#f4f3f4'}
          />
        </View>
        
        <View style={styles.settingItem}>
          <View style={styles.settingContent}>
            <Text style={styles.settingLabel}>Avoid Tolls</Text>
            <Text style={styles.settingDescription}>Prefer routes without toll roads</Text>
          </View>
          <Switch
            value={settings.navigation.avoidTolls}
            onValueChange={() => handleToggleSetting('navigation', 'avoidTolls')}
            trackColor={{ false: '#d1d5db', true: '#c4b5fd' }}
            thumbColor={settings.navigation.avoidTolls ? '#8b5cf6' : '#f4f3f4'}
          />
        </View>
        
        <View style={styles.settingItem}>
          <View style={styles.settingContent}>
            <Text style={styles.settingLabel}>Avoid Highways</Text>
            <Text style={styles.settingDescription}>Prefer routes without highways</Text>
          </View>
          <Switch
            value={settings.navigation.avoidHighways}
            onValueChange={() => handleToggleSetting('navigation', 'avoidHighways')}
            trackColor={{ false: '#d1d5db', true: '#c4b5fd' }}
            thumbColor={settings.navigation.avoidHighways ? '#8b5cf6' : '#f4f3f4'}
          />
        </View>
      </View>
      
      {/* Data & Sync Section */}
      <View style={styles.sectionContainer}>
        <View style={styles.sectionHeader}>
          <Ionicons name="sync" size={20} color="#8b5cf6" />
          <Text style={styles.sectionTitle}>Data & Sync</Text>
        </View>
        
        <View style={styles.settingItem}>
          <View style={styles.settingContent}>
            <Text style={styles.settingLabel}>Auto Sync</Text>
            <Text style={styles.settingDescription}>Automatically sync data with server</Text>
          </View>
          <Switch
            value={settings.data.autoSync}
            onValueChange={() => handleToggleSetting('data', 'autoSync')}
            trackColor={{ false: '#d1d5db', true: '#c4b5fd' }}
            thumbColor={settings.data.autoSync ? '#8b5cf6' : '#f4f3f4'}
          />
        </View>
        
        <View style={styles.settingItem}>
          <View style={styles.settingContent}>
            <Text style={styles.settingLabel}>Sync on Wi-Fi Only</Text>
            <Text style={styles.settingDescription}>Only sync when connected to Wi-Fi</Text>
          </View>
          <Switch
            value={settings.data.syncOnWifiOnly}
            onValueChange={() => handleToggleSetting('data', 'syncOnWifiOnly')}
            trackColor={{ false: '#d1d5db', true: '#c4b5fd' }}
            thumbColor={settings.data.syncOnWifiOnly ? '#8b5cf6' : '#f4f3f4'}
            disabled={!settings.data.autoSync}
          />
        </View>
        
        <TouchableOpacity 
          style={styles.settingItem}
          onPress={() => handleSelectOption('data', 'syncFrequency', ['realtime', 'hourly', 'daily'])}
          disabled={!settings.data.autoSync}
        >
          <View style={styles.settingContent}>
            <Text style={[styles.settingLabel, !settings.data.autoSync && styles.disabledText]}>Sync Frequency</Text>
            <Text style={[styles.settingDescription, !settings.data.autoSync && styles.disabledText]}>
              How often to sync with server
            </Text>
          </View>
          <View style={styles.settingValue}>
            <Text style={[styles.settingValueText, !settings.data.autoSync && styles.disabledText]}>
              {settings.data.syncFrequency.charAt(0).toUpperCase() + settings.data.syncFrequency.slice(1)}
            </Text>
            <Ionicons name="chevron-forward" size={20} color={settings.data.autoSync ? "#8b5cf6" : "#d1d5db"} />
          </View>
        </TouchableOpacity>
      </View>
      
      {/* Language & Units Section */}
      <View style={styles.sectionContainer}>
        <View style={styles.sectionHeader}>
          <Ionicons name="globe" size={20} color="#8b5cf6" />
          <Text style={styles.sectionTitle}>Language & Units</Text>
        </View>
        
        <TouchableOpacity 
          style={styles.settingItem}
          onPress={handleSelectLanguage}
        >
          <View style={styles.settingContent}>
            <Text style={styles.settingLabel}>Language</Text>
            <Text style={styles.settingDescription}>Set app language</Text>
          </View>
          <View style={styles.settingValue}>
            <Text style={styles.settingValueText}>
              {settings.language.charAt(0).toUpperCase() + settings.language.slice(1)}
            </Text>
            <Ionicons name="chevron-forward" size={20} color="#8b5cf6" />
          </View>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={styles.settingItem}
          onPress={() => handleSelectOption('units', 'distance', ['miles', 'kilometers'])}
        >
          <View style={styles.settingContent}>
            <Text style={styles.settingLabel}>Distance Units</Text>
            <Text style={styles.settingDescription}>Set preferred distance units</Text>
          </View>
          <View style={styles.settingValue}>
            <Text style={styles.settingValueText}>
              {settings.units.distance.charAt(0).toUpperCase() + settings.units.distance.slice(1)}
            </Text>
            <Ionicons name="chevron-forward" size={20} color="#8b5cf6" />
          </View>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={styles.settingItem}
          onPress={() => handleSelectOption('units', 'weight', ['pounds', 'kilograms'])}
        >
          <View style={styles.settingContent}>
            <Text style={styles.settingLabel}>Weight Units</Text>
            <Text style={styles.settingDescription}>Set preferred weight units</Text>
          </View>
          <View style={styles.settingValue}>
            <Text style={styles.settingValueText}>
              {settings.units.weight.charAt(0).toUpperCase() + settings.units.weight.slice(1)}
            </Text>
            <Ionicons name="chevron-forward" size={20} color="#8b5cf6" />
          </View>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={styles.settingItem}
          onPress={() => handleSelectOption('units', 'temperature', ['fahrenheit', 'celsius'])}
        >
          <View style={styles.settingContent}>
            <Text style={styles.settingLabel}>Temperature Units</Text>
            <Text style={styles.settingDescription}>Set preferred temperature units</Text>
          </View>
          <View style={styles.settingValue}>
            <Text style={styles.settingValueText}>
              {settings.units.temperature.charAt(0).toUpperCase() + settings.units.temperature.slice(1)}
            </Text>
            <Ionicons name="chevron-forward" size={20} color="#8b5cf6" />
          </View>
        </TouchableOpacity>
      </View>
      
      {/* Advanced Section */}
      <View style={styles.sectionContainer}>
        <View style={styles.sectionHeader}>
          <Ionicons name="settings" size={20} color="#8b5cf6" />
          <Text style={styles.sectionTitle}>Advanced</Text>
        </View>
        
        <TouchableOpacity 
          style={styles.settingItem}
          onPress={handleClearCache}
        >
          <View style={styles.settingContent}>
            <Text style={styles.settingLabel}>Clear Cache</Text>
            <Text style={styles.settingDescription}>Free up storage space</Text>
          </View>
          <Ionicons name="chevron-forward" size={20} color="#8b5cf6" />
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={styles.settingItem}
          onPress={handleResetSettings}
        >
          <View style={styles.settingContent}>
            <Text style={styles.settingLabel}>Reset Settings</Text>
            <Text style={styles.settingDescription}>Restore default settings</Text>
          </View>
          <Ionicons name="chevron-forward" size={20} color="#8b5cf6" />
        </TouchableOpacity>
      </View>
      
      {/* Logout Button */}
      <TouchableOpacity 
        style={styles.logoutButton}
        onPress={handleLogout}
      >
        <Ionicons name="log-out" size={20} color="white" />
        <Text style={styles.logoutButtonText}>Logout</Text>
      </TouchableOpacity>
      
      {/* App Version */}
      <View style={styles.versionContainer}>
        <Text style={styles.versionText}>NxtAcre Driver App v1.0.0</Text>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  sectionContainer: {
    backgroundColor: 'white',
    borderRadius: 10,
    margin: 15,
    marginTop: 10,
    marginBottom: 5,
    padding: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 15,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginLeft: 8,
  },
  settingItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  settingContent: {
    flex: 1,
    marginRight: 10,
  },
  settingLabel: {
    fontSize: 16,
    color: '#333',
    marginBottom: 2,
  },
  settingDescription: {
    fontSize: 12,
    color: '#666',
  },
  settingValue: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  settingValueText: {
    fontSize: 14,
    color: '#8b5cf6',
    marginRight: 5,
  },
  disabledText: {
    color: '#d1d5db',
  },
  logoutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#f87171',
    borderRadius: 8,
    paddingVertical: 15,
    margin: 15,
    marginTop: 10,
    marginBottom: 10,
  },
  logoutButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  versionContainer: {
    alignItems: 'center',
    padding: 20,
    paddingTop: 10,
  },
  versionText: {
    fontSize: 12,
    color: '#999',
  },
});

export default SettingsScreen;