import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, FlatList, TouchableOpacity, TextInput, Alert, ActivityIndicator } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { MainStackParamList } from '../navigation/MainNavigator';

type NavigationProp = NativeStackNavigationProp<MainStackParamList>;

// Mock data for initial development
const mockVehicles = [
  {
    id: 'veh-001',
    name: 'Truck #103',
    type: 'Delivery Truck',
    licensePlate: 'ABC-1234',
    status: 'Available',
    fuelLevel: 85,
    mileage: 45678,
    lastMaintenance: '2023-07-15',
    nextMaintenance: '2023-10-15',
    capacity: '2 tons',
    features: ['Refrigeration', 'GPS Tracking', 'Backup Camera']
  },
  {
    id: 'veh-002',
    name: '<PERSON> #205',
    type: 'Cargo Van',
    licensePlate: 'XYZ-5678',
    status: 'Available',
    fuelLevel: 72,
    mileage: 32456,
    lastMaintenance: '2023-08-01',
    nextMaintenance: '2023-11-01',
    capacity: '1 ton',
    features: ['GPS Tracking', 'Backup Camera']
  },
  {
    id: 'veh-003',
    name: 'Truck #107',
    type: 'Delivery Truck',
    licensePlate: 'DEF-9012',
    status: 'In Maintenance',
    fuelLevel: 45,
    mileage: 67890,
    lastMaintenance: '2023-08-20',
    nextMaintenance: '2023-08-25',
    capacity: '2.5 tons',
    features: ['Refrigeration', 'GPS Tracking', 'Backup Camera', 'Lift Gate']
  },
  {
    id: 'veh-004',
    name: 'Van #210',
    type: 'Cargo Van',
    licensePlate: 'GHI-3456',
    status: 'In Use',
    fuelLevel: 60,
    mileage: 28765,
    lastMaintenance: '2023-07-25',
    nextMaintenance: '2023-10-25',
    capacity: '1.2 tons',
    features: ['GPS Tracking', 'Backup Camera']
  },
  {
    id: 'veh-005',
    name: 'Truck #112',
    type: 'Delivery Truck',
    licensePlate: 'JKL-7890',
    status: 'Available',
    fuelLevel: 92,
    mileage: 12345,
    lastMaintenance: '2023-08-10',
    nextMaintenance: '2023-11-10',
    capacity: '3 tons',
    features: ['Refrigeration', 'GPS Tracking', 'Backup Camera', 'Lift Gate', 'Temperature Control']
  }
];

const VehicleSelectionScreen: React.FC = () => {
  const navigation = useNavigation<NavigationProp>();
  const [vehicles, setVehicles] = useState(mockVehicles);
  const [filteredVehicles, setFilteredVehicles] = useState(mockVehicles);
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(true);
  const [selectedVehicle, setSelectedVehicle] = useState<string | null>(null);
  const [activeFilter, setActiveFilter] = useState('all');
  
  // Simulate loading data
  useEffect(() => {
    setTimeout(() => {
      setLoading(false);
    }, 1000);
  }, []);
  
  // Filter vehicles based on search query and active filter
  useEffect(() => {
    setLoading(true);
    
    // Simulate API call delay
    setTimeout(() => {
      let filtered = vehicles;
      
      // Apply search filter
      if (searchQuery) {
        filtered = filtered.filter(vehicle => 
          vehicle.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          vehicle.type.toLowerCase().includes(searchQuery.toLowerCase()) ||
          vehicle.licensePlate.toLowerCase().includes(searchQuery.toLowerCase())
        );
      }
      
      // Apply status filter
      if (activeFilter !== 'all') {
        filtered = filtered.filter(vehicle => {
          if (activeFilter === 'available') {
            return vehicle.status === 'Available';
          } else if (activeFilter === 'in-use') {
            return vehicle.status === 'In Use';
          } else if (activeFilter === 'maintenance') {
            return vehicle.status === 'In Maintenance';
          }
          return true;
        });
      }
      
      setFilteredVehicles(filtered);
      setLoading(false);
    }, 500);
  }, [searchQuery, activeFilter, vehicles]);
  
  // Handle vehicle selection
  const handleVehicleSelect = (vehicleId: string) => {
    setSelectedVehicle(vehicleId);
  };
  
  // Handle confirm selection
  const handleConfirmSelection = () => {
    if (!selectedVehicle) {
      Alert.alert('Error', 'Please select a vehicle first.');
      return;
    }
    
    const vehicle = vehicles.find(v => v.id === selectedVehicle);
    
    if (vehicle?.status !== 'Available') {
      Alert.alert(
        'Warning',
        `Vehicle ${vehicle?.name} is currently ${vehicle?.status}. Are you sure you want to select it?`,
        [
          {
            text: 'Cancel',
            style: 'cancel'
          },
          {
            text: 'Confirm',
            onPress: () => {
              // In a real app, this would update the user's assigned vehicle
              Alert.alert('Success', `Vehicle ${vehicle?.name} has been assigned to you.`);
              navigation.goBack();
            }
          }
        ]
      );
    } else {
      // In a real app, this would update the user's assigned vehicle
      Alert.alert(
        'Success', 
        `Vehicle ${vehicle?.name} has been assigned to you.`,
        [
          {
            text: 'OK',
            onPress: () => navigation.goBack()
          }
        ]
      );
    }
  };
  
  // Render each vehicle item
  const renderVehicleItem = ({ item }: { item: typeof mockVehicles[0] }) => {
    // Determine status color
    let statusColor = '#8b5cf6'; // Default purple
    if (item.status === 'Available') {
      statusColor = '#10b981'; // Green
    } else if (item.status === 'In Use') {
      statusColor = '#3b82f6'; // Blue
    } else if (item.status === 'In Maintenance') {
      statusColor = '#f87171'; // Red
    }
    
    // Determine fuel level color
    let fuelColor = '#10b981'; // Green
    if (item.fuelLevel < 30) {
      fuelColor = '#f87171'; // Red
    } else if (item.fuelLevel < 70) {
      fuelColor = '#f59e0b'; // Yellow
    }
    
    const isSelected = selectedVehicle === item.id;
    
    return (
      <TouchableOpacity 
        style={[styles.vehicleCard, isSelected && styles.selectedVehicleCard]}
        onPress={() => handleVehicleSelect(item.id)}
      >
        <View style={styles.vehicleHeader}>
          <View style={styles.vehicleNameContainer}>
            <Text style={styles.vehicleName}>{item.name}</Text>
            <Text style={styles.vehicleType}>{item.type}</Text>
          </View>
          <View style={[styles.statusBadge, { backgroundColor: `${statusColor}20` }]}>
            <Text style={[styles.statusText, { color: statusColor }]}>{item.status}</Text>
          </View>
        </View>
        
        <View style={styles.vehicleDetails}>
          <View style={styles.detailItem}>
            <Ionicons name="car-outline" size={16} color="#666" />
            <Text style={styles.detailText}>{item.licensePlate}</Text>
          </View>
          
          <View style={styles.detailItem}>
            <Ionicons name="speedometer-outline" size={16} color="#666" />
            <Text style={styles.detailText}>{item.mileage.toLocaleString()} miles</Text>
          </View>
          
          <View style={styles.detailItem}>
            <Ionicons name="cube-outline" size={16} color="#666" />
            <Text style={styles.detailText}>Capacity: {item.capacity}</Text>
          </View>
        </View>
        
        <View style={styles.fuelContainer}>
          <Text style={styles.fuelText}>Fuel Level: {item.fuelLevel}%</Text>
          <View style={styles.fuelBarBackground}>
            <View 
              style={[
                styles.fuelBarFill, 
                { width: `${item.fuelLevel}%`, backgroundColor: fuelColor }
              ]} 
            />
          </View>
        </View>
        
        <View style={styles.featuresContainer}>
          {item.features.map((feature, index) => (
            <View key={index} style={styles.featureChip}>
              <Text style={styles.featureText}>{feature}</Text>
            </View>
          ))}
        </View>
        
        {isSelected && (
          <View style={styles.selectedIndicator}>
            <Ionicons name="checkmark-circle" size={24} color="#8b5cf6" />
          </View>
        )}
      </TouchableOpacity>
    );
  };
  
  return (
    <View style={styles.container}>
      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <Ionicons name="search" size={20} color="#666" style={styles.searchIcon} />
        <TextInput
          style={styles.searchInput}
          placeholder="Search vehicles..."
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
        {searchQuery ? (
          <TouchableOpacity onPress={() => setSearchQuery('')}>
            <Ionicons name="close-circle" size={20} color="#666" />
          </TouchableOpacity>
        ) : null}
      </View>
      
      {/* Filter Tabs */}
      <View style={styles.filterContainer}>
        <TouchableOpacity 
          style={[styles.filterTab, activeFilter === 'all' && styles.activeFilterTab]}
          onPress={() => setActiveFilter('all')}
        >
          <Text style={[styles.filterText, activeFilter === 'all' && styles.activeFilterText]}>All</Text>
        </TouchableOpacity>
        <TouchableOpacity 
          style={[styles.filterTab, activeFilter === 'available' && styles.activeFilterTab]}
          onPress={() => setActiveFilter('available')}
        >
          <Text style={[styles.filterText, activeFilter === 'available' && styles.activeFilterText]}>Available</Text>
        </TouchableOpacity>
        <TouchableOpacity 
          style={[styles.filterTab, activeFilter === 'in-use' && styles.activeFilterTab]}
          onPress={() => setActiveFilter('in-use')}
        >
          <Text style={[styles.filterText, activeFilter === 'in-use' && styles.activeFilterText]}>In Use</Text>
        </TouchableOpacity>
        <TouchableOpacity 
          style={[styles.filterTab, activeFilter === 'maintenance' && styles.activeFilterTab]}
          onPress={() => setActiveFilter('maintenance')}
        >
          <Text style={[styles.filterText, activeFilter === 'maintenance' && styles.activeFilterText]}>Maintenance</Text>
        </TouchableOpacity>
      </View>
      
      {/* Vehicles List */}
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#8b5cf6" />
        </View>
      ) : filteredVehicles.length > 0 ? (
        <FlatList
          data={filteredVehicles}
          renderItem={renderVehicleItem}
          keyExtractor={item => item.id}
          contentContainerStyle={styles.listContainer}
        />
      ) : (
        <View style={styles.emptyContainer}>
          <Ionicons name="car-outline" size={64} color="#ccc" />
          <Text style={styles.emptyText}>No vehicles found</Text>
          <Text style={styles.emptySubtext}>Try adjusting your search or filters</Text>
        </View>
      )}
      
      {/* Confirm Button */}
      <View style={styles.confirmContainer}>
        <TouchableOpacity 
          style={[styles.confirmButton, !selectedVehicle && styles.disabledButton]}
          onPress={handleConfirmSelection}
          disabled={!selectedVehicle}
        >
          <Text style={styles.confirmButtonText}>Confirm Selection</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    borderRadius: 8,
    margin: 15,
    paddingHorizontal: 15,
    paddingVertical: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  searchIcon: {
    marginRight: 10,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: '#333',
  },
  filterContainer: {
    flexDirection: 'row',
    marginHorizontal: 15,
    marginBottom: 10,
  },
  filterTab: {
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 20,
    marginRight: 10,
    backgroundColor: '#e5e7eb',
  },
  activeFilterTab: {
    backgroundColor: '#8b5cf6',
  },
  filterText: {
    fontSize: 14,
    color: '#666',
  },
  activeFilterText: {
    color: 'white',
    fontWeight: '500',
  },
  listContainer: {
    padding: 15,
    paddingTop: 5,
    paddingBottom: 80, // Space for the confirm button
  },
  vehicleCard: {
    backgroundColor: 'white',
    borderRadius: 10,
    padding: 15,
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
    position: 'relative',
  },
  selectedVehicleCard: {
    borderWidth: 2,
    borderColor: '#8b5cf6',
  },
  vehicleHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 10,
  },
  vehicleNameContainer: {
    flex: 1,
  },
  vehicleName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  vehicleType: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  statusBadge: {
    borderRadius: 4,
    paddingHorizontal: 8,
    paddingVertical: 2,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '500',
  },
  vehicleDetails: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 10,
  },
  detailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 15,
    marginBottom: 5,
  },
  detailText: {
    fontSize: 14,
    color: '#666',
    marginLeft: 5,
  },
  fuelContainer: {
    marginBottom: 10,
  },
  fuelText: {
    fontSize: 14,
    color: '#666',
    marginBottom: 5,
  },
  fuelBarBackground: {
    height: 8,
    backgroundColor: '#e5e7eb',
    borderRadius: 4,
    overflow: 'hidden',
  },
  fuelBarFill: {
    height: '100%',
    borderRadius: 4,
  },
  featuresContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  featureChip: {
    backgroundColor: '#f3f4f6',
    borderRadius: 15,
    paddingHorizontal: 10,
    paddingVertical: 5,
    marginRight: 8,
    marginBottom: 5,
  },
  featureText: {
    fontSize: 12,
    color: '#666',
  },
  selectedIndicator: {
    position: 'absolute',
    top: 10,
    right: 10,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#666',
    marginTop: 10,
  },
  emptySubtext: {
    fontSize: 14,
    color: '#999',
    marginTop: 5,
    textAlign: 'center',
  },
  confirmContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'white',
    padding: 15,
    borderTopWidth: 1,
    borderTopColor: '#e5e7eb',
  },
  confirmButton: {
    backgroundColor: '#8b5cf6',
    borderRadius: 8,
    paddingVertical: 15,
    alignItems: 'center',
  },
  disabledButton: {
    backgroundColor: '#c4b5fd',
  },
  confirmButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default VehicleSelectionScreen;