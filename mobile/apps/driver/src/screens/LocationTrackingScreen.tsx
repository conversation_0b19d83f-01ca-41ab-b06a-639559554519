import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Switch,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  ScrollView,
  Platform,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import MapView, { <PERSON>er, Circle, Polygon, PROVIDER_GOOGLE } from 'react-native-maps';
import * as Location from 'expo-location';
import * as TaskManager from 'expo-task-manager';
import * as BackgroundFetch from 'expo-background-fetch';
import { geolib } from '../utils/geolib';

// Define the background task name
const LOCATION_TRACKING = 'location-tracking';

// Mock delivery zones data
const mockDeliveryZones = [
  {
    id: 'zone-001',
    name: 'Green Valley Farm',
    type: 'circle',
    center: { latitude: 37.7749, longitude: -122.4194 },
    radius: 500, // meters
    color: 'rgba(16, 185, 129, 0.2)', // green with transparency
    strokeColor: '#10b981',
    deliveryId: 'del-001',
  },
  {
    id: 'zone-002',
    name: 'Sunrise Orchards',
    type: 'circle',
    center: { latitude: 37.8049, longitude: -122.2711 },
    radius: 300, // meters
    color: 'rgba(59, 130, 246, 0.2)', // blue with transparency
    strokeColor: '#3b82f6',
    deliveryId: 'del-002',
  },
  {
    id: 'zone-003',
    name: 'Blue Sky Dairy',
    type: 'polygon',
    coordinates: [
      { latitude: 37.7249, longitude: -122.1694 },
      { latitude: 37.7279, longitude: -122.1654 },
      { latitude: 37.7219, longitude: -122.1624 },
      { latitude: 37.7189, longitude: -122.1664 },
    ],
    color: 'rgba(139, 92, 246, 0.2)', // purple with transparency
    strokeColor: '#8b5cf6',
    deliveryId: 'del-003',
  },
];

// Mock function to simulate sending location to server
const sendLocationToServer = async (location) => {
  // In a real app, this would send the location to a server
  console.log('Sending location to server:', location);
  // Simulate API call
  await new Promise(resolve => setTimeout(resolve, 500));
  return true;
};

// Register the background task
TaskManager.defineTask(LOCATION_TRACKING, async ({ data, error }) => {
  if (error) {
    console.error('Error in background location task:', error);
    return;
  }
  if (data) {
    const { locations } = data;
    // Process the locations
    if (locations && locations.length > 0) {
      const location = locations[locations.length - 1];

      // Send location to server
      await sendLocationToServer(location);

      // Check for geofence events (this will be handled by the app when it's in the foreground)
      // In a real app, this would update a local storage or send events to the server
      const insideZones = [];
      for (const zone of mockDeliveryZones) {
        let isInside = false;

        if (zone.type === 'circle') {
          isInside = geolib.isPointInCircle(
            { latitude: location.coords.latitude, longitude: location.coords.longitude },
            zone.center,
            zone.radius
          );
        } else if (zone.type === 'polygon') {
          isInside = geolib.isPointInPolygon(
            { latitude: location.coords.latitude, longitude: location.coords.longitude },
            zone.coordinates
          );
        }

        if (isInside) {
          insideZones.push(zone.id);

          // In a real app, this would create a local notification
          console.log(`Inside delivery zone: ${zone.name}`);
        }
      }
    }
  }
});

// Register background fetch task
BackgroundFetch.registerTaskAsync(LOCATION_TRACKING, {
  minimumInterval: 60, // 1 minute
  stopOnTerminate: false,
  startOnBoot: true,
});

const LocationTrackingScreen = () => {
  const [isTracking, setIsTracking] = useState(false);
  const [location, setLocation] = useState(null);
  const [errorMsg, setErrorMsg] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [shareWithCustomers, setShareWithCustomers] = useState(true);
  const [trackingHistory, setTrackingHistory] = useState([]);
  const [batteryOptimization, setBatteryOptimization] = useState('balanced');
  const [showGeofences, setShowGeofences] = useState(true);
  const [activeZones, setActiveZones] = useState([]);
  const [recentEvents, setRecentEvents] = useState([]);
  const mapRef = useRef(null);

  // Check if a location is inside any delivery zones
  const checkDeliveryZones = (userLocation) => {
    if (!userLocation) return [];

    const insideZones = [];

    for (const zone of mockDeliveryZones) {
      let isInside = false;

      if (zone.type === 'circle') {
        isInside = geolib.isPointInCircle(
          { latitude: userLocation.coords.latitude, longitude: userLocation.coords.longitude },
          zone.center,
          zone.radius
        );
      } else if (zone.type === 'polygon') {
        isInside = geolib.isPointInPolygon(
          { latitude: userLocation.coords.latitude, longitude: userLocation.coords.longitude },
          zone.coordinates
        );
      }

      if (isInside) {
        insideZones.push(zone.id);
      }
    }

    return insideZones;
  };

  // Handle zone entry and exit events
  const handleZoneEvents = (newActiveZones) => {
    // Find zones that the driver just entered
    const enteredZones = newActiveZones.filter(zoneId => !activeZones.includes(zoneId));

    // Find zones that the driver just exited
    const exitedZones = activeZones.filter(zoneId => !newActiveZones.includes(zoneId));

    // Create events for zone entries
    const entryEvents = enteredZones.map(zoneId => {
      const zone = mockDeliveryZones.find(z => z.id === zoneId);
      return {
        id: `entry-${Date.now()}-${zoneId}`,
        type: 'entry',
        zoneName: zone.name,
        zoneId: zoneId,
        timestamp: new Date().toISOString(),
      };
    });

    // Create events for zone exits
    const exitEvents = exitedZones.map(zoneId => {
      const zone = mockDeliveryZones.find(z => z.id === zoneId);
      return {
        id: `exit-${Date.now()}-${zoneId}`,
        type: 'exit',
        zoneName: zone.name,
        zoneId: zoneId,
        timestamp: new Date().toISOString(),
      };
    });

    // Add new events to the recent events list
    if (entryEvents.length > 0 || exitEvents.length > 0) {
      setRecentEvents(prev => [...entryEvents, ...exitEvents, ...prev].slice(0, 10));
    }

    // Show notifications for entries and exits
    entryEvents.forEach(event => {
      Alert.alert(
        'Delivery Zone Entered',
        `You have arrived at ${event.zoneName}`,
        [{ text: 'OK' }]
      );
    });

    exitEvents.forEach(event => {
      Alert.alert(
        'Delivery Zone Exited',
        `You have departed from ${event.zoneName}`,
        [{ text: 'OK' }]
      );
    });

    // Update active zones
    setActiveZones(newActiveZones);
  };

  // Request location permissions and get initial location
  useEffect(() => {
    (async () => {
      try {
        setIsLoading(true);

        // Request foreground permissions
        let { status: foregroundStatus } = await Location.requestForegroundPermissionsAsync();
        if (foregroundStatus !== 'granted') {
          setErrorMsg('Permission to access location was denied');
          setIsLoading(false);
          return;
        }

        // Get current location
        let currentLocation = await Location.getCurrentPositionAsync({
          accuracy: Location.Accuracy.High,
        });
        setLocation(currentLocation);

        // Check if the location is inside any delivery zones
        const insideZones = checkDeliveryZones(currentLocation);
        setActiveZones(insideZones);

        // Check if tracking is already active
        const hasStartedTracking = await Location.hasStartedLocationUpdatesAsync(LOCATION_TRACKING);
        setIsTracking(hasStartedTracking);

        // Load tracking history (mock data for demo)
        loadTrackingHistory();
      } catch (error) {
        setErrorMsg(`Error initializing location: ${error.message}`);
        console.error('Location initialization error:', error);
      } finally {
        setIsLoading(false);
      }
    })();

    // Cleanup function
    return () => {
      // Nothing to clean up here as we want tracking to continue in background
    };
  }, []);

  // Load mock tracking history
  const loadTrackingHistory = () => {
    // In a real app, this would fetch from storage or API
    const mockHistory = [
      {
        id: '1',
        timestamp: new Date(Date.now() - 3600000).toISOString(), // 1 hour ago
        status: 'active',
        batteryLevel: '85%',
        sharedWith: ['Dispatch', 'Customer: John Doe'],
      },
      {
        id: '2',
        timestamp: new Date(Date.now() - 7200000).toISOString(), // 2 hours ago
        status: 'active',
        batteryLevel: '92%',
        sharedWith: ['Dispatch'],
      },
      {
        id: '3',
        timestamp: new Date(Date.now() - 10800000).toISOString(), // 3 hours ago
        status: 'started',
        batteryLevel: '98%',
        sharedWith: ['Dispatch', 'Customer: Jane Smith'],
      },
    ];
    setTrackingHistory(mockHistory);
  };

  // Location subscription for foreground tracking
  const [locationSubscription, setLocationSubscription] = useState(null);

  // Start location tracking
  const startLocationTracking = async () => {
    try {
      // Request background permissions
      const { status: backgroundStatus } = await Location.requestBackgroundPermissionsAsync();
      if (backgroundStatus !== 'granted') {
        Alert.alert(
          'Background Location Permission Required',
          'This app needs background location permission to track your location while the app is in the background.',
          [{ text: 'OK' }]
        );
        return;
      }

      // Configure background location tracking
      await Location.startLocationUpdatesAsync(LOCATION_TRACKING, {
        accuracy: getBatteryOptimizationAccuracy(),
        timeInterval: getBatteryOptimizationInterval(),
        distanceInterval: getBatteryOptimizationDistance(),
        foregroundService: {
          notificationTitle: 'Location Tracking Active',
          notificationBody: 'NxtAcre Driver App is tracking your location',
          notificationColor: '#8b5cf6',
        },
        activityType: Location.ActivityType.AutomotiveNavigation,
        showsBackgroundLocationIndicator: true,
      });

      // Set up foreground location subscription for more frequent updates when app is open
      const subscription = await Location.watchPositionAsync(
        {
          accuracy: getBatteryOptimizationAccuracy(),
          timeInterval: 3000, // 3 seconds
          distanceInterval: 10, // 10 meters
        },
        (location) => {
          // Update location state
          setLocation(location);

          // Check for geofence events
          const insideZones = checkDeliveryZones(location);
          handleZoneEvents(insideZones);

          // Center map on current location if tracking is active
          if (mapRef.current) {
            mapRef.current.animateToRegion({
              latitude: location.coords.latitude,
              longitude: location.coords.longitude,
              latitudeDelta: 0.01,
              longitudeDelta: 0.01,
            });
          }
        }
      );

      // Save subscription for cleanup
      setLocationSubscription(subscription);

      // Update state
      setIsTracking(true);

      // Add to history
      const newHistoryItem = {
        id: Date.now().toString(),
        timestamp: new Date().toISOString(),
        status: 'started',
        batteryLevel: '100%', // Mock battery level
        sharedWith: ['Dispatch', ...(shareWithCustomers ? ['All Customers'] : [])],
      };
      setTrackingHistory([newHistoryItem, ...trackingHistory]);

      // Show confirmation
      Alert.alert(
        'Location Tracking Started',
        'Your location is now being shared with dispatch' + 
        (shareWithCustomers ? ' and customers' : '') + 
        '. You can continue using other apps.',
        [{ text: 'OK' }]
      );
    } catch (error) {
      console.error('Error starting location tracking:', error);
      Alert.alert('Error', `Failed to start location tracking: ${error.message}`);
    }
  };

  // Stop location tracking
  const stopLocationTracking = async () => {
    try {
      // Stop background location updates
      await Location.stopLocationUpdatesAsync(LOCATION_TRACKING);

      // Remove foreground location subscription
      if (locationSubscription) {
        locationSubscription.remove();
        setLocationSubscription(null);
      }

      // Update state
      setIsTracking(false);

      // Clear active zones
      setActiveZones([]);

      // Add to history
      const newHistoryItem = {
        id: Date.now().toString(),
        timestamp: new Date().toISOString(),
        status: 'stopped',
        batteryLevel: '100%', // Mock battery level
        sharedWith: [],
      };
      setTrackingHistory([newHistoryItem, ...trackingHistory]);

      // Show confirmation
      Alert.alert(
        'Location Tracking Stopped',
        'Your location is no longer being shared.',
        [{ text: 'OK' }]
      );
    } catch (error) {
      console.error('Error stopping location tracking:', error);
      Alert.alert('Error', `Failed to stop location tracking: ${error.message}`);
    }
  };

  // Toggle location tracking
  const toggleLocationTracking = () => {
    if (isTracking) {
      stopLocationTracking();
    } else {
      startLocationTracking();
    }
  };

  // Get accuracy based on battery optimization setting
  const getBatteryOptimizationAccuracy = () => {
    switch (batteryOptimization) {
      case 'high-accuracy':
        return Location.Accuracy.Highest;
      case 'balanced':
        return Location.Accuracy.Balanced;
      case 'low-power':
        return Location.Accuracy.Low;
      default:
        return Location.Accuracy.Balanced;
    }
  };

  // Get time interval based on battery optimization setting
  const getBatteryOptimizationInterval = () => {
    switch (batteryOptimization) {
      case 'high-accuracy':
        return 10000; // 10 seconds
      case 'balanced':
        return 30000; // 30 seconds
      case 'low-power':
        return 60000; // 1 minute
      default:
        return 30000;
    }
  };

  // Get distance interval based on battery optimization setting
  const getBatteryOptimizationDistance = () => {
    switch (batteryOptimization) {
      case 'high-accuracy':
        return 10; // 10 meters
      case 'balanced':
        return 30; // 30 meters
      case 'low-power':
        return 100; // 100 meters
      default:
        return 30;
    }
  };

  // Set battery optimization mode
  const setBatteryOptimizationMode = (mode) => {
    setBatteryOptimization(mode);
    if (isTracking) {
      // Restart tracking with new settings
      stopLocationTracking().then(() => {
        startLocationTracking();
      });
    }
  };

  // Format timestamp
  const formatTimestamp = (timestamp) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#8b5cf6" />
        <Text style={styles.loadingText}>Initializing location services...</Text>
      </View>
    );
  }

  if (errorMsg) {
    return (
      <View style={styles.errorContainer}>
        <Ionicons name="alert-circle-outline" size={48} color="#ef4444" />
        <Text style={styles.errorText}>{errorMsg}</Text>
        <TouchableOpacity
          style={styles.retryButton}
          onPress={() => {
            setIsLoading(true);
            setErrorMsg(null);
            // Retry initialization
            Location.requestForegroundPermissionsAsync()
              .then(({ status }) => {
                if (status === 'granted') {
                  return Location.getCurrentPositionAsync({
                    accuracy: Location.Accuracy.High,
                  });
                } else {
                  throw new Error('Permission to access location was denied');
                }
              })
              .then((location) => {
                setLocation(location);
                setIsLoading(false);
              })
              .catch((error) => {
                setErrorMsg(`Error initializing location: ${error.message}`);
                setIsLoading(false);
              });
          }}
        >
          <Text style={styles.retryButtonText}>Retry</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container}>
      {/* Map View */}
      <View style={styles.mapContainer}>
        {location ? (
          <MapView
            ref={mapRef}
            style={styles.map}
            provider={PROVIDER_GOOGLE}
            initialRegion={{
              latitude: location.coords.latitude,
              longitude: location.coords.longitude,
              latitudeDelta: 0.01,
              longitudeDelta: 0.01,
            }}
          >
            {/* Delivery Zone Geofences */}
            {showGeofences && mockDeliveryZones.map(zone => {
              if (zone.type === 'circle') {
                return (
                  <Circle
                    key={zone.id}
                    center={zone.center}
                    radius={zone.radius}
                    fillColor={activeZones.includes(zone.id) ? 'rgba(16, 185, 129, 0.3)' : zone.color}
                    strokeColor={activeZones.includes(zone.id) ? '#10b981' : zone.strokeColor}
                    strokeWidth={2}
                  />
                );
              } else if (zone.type === 'polygon') {
                return (
                  <Polygon
                    key={zone.id}
                    coordinates={zone.coordinates}
                    fillColor={activeZones.includes(zone.id) ? 'rgba(16, 185, 129, 0.3)' : zone.color}
                    strokeColor={activeZones.includes(zone.id) ? '#10b981' : zone.strokeColor}
                    strokeWidth={2}
                  />
                );
              }
              return null;
            })}

            {/* Current Location Marker */}
            <Marker
              coordinate={{
                latitude: location.coords.latitude,
                longitude: location.coords.longitude,
              }}
              title="Your Location"
              description="This is your current location"
            >
              <View style={styles.markerContainer}>
                <View style={styles.markerInner}>
                  <Ionicons name="car" size={16} color="#ffffff" />
                </View>
              </View>
            </Marker>
          </MapView>
        ) : (
          <View style={styles.noMapContainer}>
            <Ionicons name="map-outline" size={48} color="#8b5cf6" />
            <Text style={styles.noMapText}>Map not available</Text>
          </View>
        )}

        {/* Map Controls */}
        {location && (
          <View style={styles.mapControls}>
            <TouchableOpacity
              style={styles.mapControlButton}
              onPress={() => setShowGeofences(!showGeofences)}
            >
              <Ionicons
                name={showGeofences ? "eye" : "eye-off"}
                size={20}
                color="#ffffff"
              />
              <Text style={styles.mapControlText}>
                {showGeofences ? "Hide Zones" : "Show Zones"}
              </Text>
            </TouchableOpacity>
          </View>
        )}
      </View>

      {/* Tracking Controls */}
      <View style={styles.controlsContainer}>
        <View style={styles.trackingStatusContainer}>
          <View style={styles.statusIndicator}>
            <View
              style={[
                styles.statusDot,
                { backgroundColor: isTracking ? '#10b981' : '#ef4444' },
              ]}
            />
            <Text style={styles.statusText}>
              {isTracking ? 'Tracking Active' : 'Tracking Inactive'}
            </Text>
          </View>
          <TouchableOpacity
            style={[
              styles.trackingButton,
              { backgroundColor: isTracking ? '#ef4444' : '#10b981' },
            ]}
            onPress={toggleLocationTracking}
          >
            <Text style={styles.trackingButtonText}>
              {isTracking ? 'Stop Tracking' : 'Start Tracking'}
            </Text>
          </TouchableOpacity>
        </View>

        <View style={styles.divider} />

        {/* Sharing Options */}
        <View style={styles.optionContainer}>
          <View style={styles.optionTextContainer}>
            <Text style={styles.optionTitle}>Share with Customers</Text>
            <Text style={styles.optionDescription}>
              Allow customers to see your real-time location
            </Text>
          </View>
          <Switch
            value={shareWithCustomers}
            onValueChange={setShareWithCustomers}
            trackColor={{ false: '#767577', true: '#8b5cf6' }}
            thumbColor={shareWithCustomers ? '#ede9fe' : '#f4f3f4'}
          />
        </View>

        {/* Battery Optimization */}
        <View style={styles.batteryOptimizationContainer}>
          <Text style={styles.sectionTitle}>Battery Optimization</Text>
          <View style={styles.batteryOptions}>
            <TouchableOpacity
              style={[
                styles.batteryOption,
                batteryOptimization === 'high-accuracy' && styles.selectedBatteryOption,
              ]}
              onPress={() => setBatteryOptimizationMode('high-accuracy')}
            >
              <Ionicons
                name="flash"
                size={24}
                color={batteryOptimization === 'high-accuracy' ? '#8b5cf6' : '#6b7280'}
              />
              <Text
                style={[
                  styles.batteryOptionText,
                  batteryOptimization === 'high-accuracy' && styles.selectedBatteryOptionText,
                ]}
              >
                High Accuracy
              </Text>
              <Text style={styles.batteryOptionDescription}>
                Most accurate, higher battery usage
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.batteryOption,
                batteryOptimization === 'balanced' && styles.selectedBatteryOption,
              ]}
              onPress={() => setBatteryOptimizationMode('balanced')}
            >
              <Ionicons
                name="battery-half"
                size={24}
                color={batteryOptimization === 'balanced' ? '#8b5cf6' : '#6b7280'}
              />
              <Text
                style={[
                  styles.batteryOptionText,
                  batteryOptimization === 'balanced' && styles.selectedBatteryOptionText,
                ]}
              >
                Balanced
              </Text>
              <Text style={styles.batteryOptionDescription}>
                Good accuracy, moderate battery usage
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.batteryOption,
                batteryOptimization === 'low-power' && styles.selectedBatteryOption,
              ]}
              onPress={() => setBatteryOptimizationMode('low-power')}
            >
              <Ionicons
                name="leaf"
                size={24}
                color={batteryOptimization === 'low-power' ? '#8b5cf6' : '#6b7280'}
              />
              <Text
                style={[
                  styles.batteryOptionText,
                  batteryOptimization === 'low-power' && styles.selectedBatteryOptionText,
                ]}
              >
                Low Power
              </Text>
              <Text style={styles.batteryOptionDescription}>
                Less accurate, lower battery usage
              </Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Active Delivery Zones */}
        <View style={styles.activeZonesContainer}>
          <Text style={styles.sectionTitle}>Active Delivery Zones</Text>
          {activeZones.length > 0 ? (
            activeZones.map((zoneId) => {
              const zone = mockDeliveryZones.find(z => z.id === zoneId);
              return (
                <View key={zoneId} style={styles.activeZoneItem}>
                  <View style={styles.activeZoneHeader}>
                    <Ionicons name="location" size={20} color="#10b981" />
                    <Text style={styles.activeZoneName}>{zone.name}</Text>
                  </View>
                  <Text style={styles.activeZoneDetail}>
                    You are currently inside this delivery zone
                  </Text>
                </View>
              );
            })
          ) : (
            <Text style={styles.noActiveZonesText}>
              You are not currently inside any delivery zones
            </Text>
          )}
        </View>

        {/* Recent Geofence Events */}
        <View style={styles.geofenceEventsContainer}>
          <Text style={styles.sectionTitle}>Recent Geofence Events</Text>
          {recentEvents.length > 0 ? (
            recentEvents.map((event) => (
              <View key={event.id} style={styles.geofenceEventItem}>
                <View style={styles.geofenceEventHeader}>
                  <Text style={styles.geofenceEventTime}>{formatTimestamp(event.timestamp)}</Text>
                  <View
                    style={[
                      styles.geofenceEventType,
                      {
                        backgroundColor: event.type === 'entry' ? '#10b981' : '#ef4444',
                      },
                    ]}
                  >
                    <Text style={styles.geofenceEventTypeText}>
                      {event.type === 'entry' ? 'Arrived' : 'Departed'}
                    </Text>
                  </View>
                </View>
                <Text style={styles.geofenceEventDetail}>
                  {event.type === 'entry' 
                    ? `Arrived at ${event.zoneName}` 
                    : `Departed from ${event.zoneName}`}
                </Text>
              </View>
            ))
          ) : (
            <Text style={styles.noEventsText}>No recent geofence events</Text>
          )}
        </View>

        {/* Tracking History */}
        <View style={styles.historyContainer}>
          <Text style={styles.sectionTitle}>Tracking History</Text>
          {trackingHistory.length > 0 ? (
            trackingHistory.map((item) => (
              <View key={item.id} style={styles.historyItem}>
                <View style={styles.historyItemHeader}>
                  <Text style={styles.historyItemTime}>{formatTimestamp(item.timestamp)}</Text>
                  <View
                    style={[
                      styles.historyItemStatus,
                      {
                        backgroundColor:
                          item.status === 'active' || item.status === 'started'
                            ? '#10b981'
                            : '#ef4444',
                      },
                    ]}
                  >
                    <Text style={styles.historyItemStatusText}>
                      {item.status === 'active'
                        ? 'Active'
                        : item.status === 'started'
                        ? 'Started'
                        : 'Stopped'}
                    </Text>
                  </View>
                </View>
                <Text style={styles.historyItemDetail}>Battery: {item.batteryLevel}</Text>
                {item.sharedWith && item.sharedWith.length > 0 && (
                  <Text style={styles.historyItemDetail}>
                    Shared with: {item.sharedWith.join(', ')}
                  </Text>
                )}
              </View>
            ))
          ) : (
            <Text style={styles.noHistoryText}>No tracking history available</Text>
          )}
        </View>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f3f4f6',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f3f4f6',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#6b7280',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f3f4f6',
    padding: 20,
  },
  errorText: {
    marginTop: 16,
    marginBottom: 24,
    fontSize: 16,
    color: '#6b7280',
    textAlign: 'center',
  },
  retryButton: {
    backgroundColor: '#8b5cf6',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  mapContainer: {
    height: 300,
    width: '100%',
    overflow: 'hidden',
  },
  map: {
    width: '100%',
    height: '100%',
  },
  noMapContainer: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#e5e7eb',
  },
  noMapText: {
    marginTop: 16,
    fontSize: 16,
    color: '#6b7280',
  },
  markerContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(139, 92, 246, 0.3)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  markerInner: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#8b5cf6',
    justifyContent: 'center',
    alignItems: 'center',
  },
  mapControls: {
    position: 'absolute',
    top: 10,
    right: 10,
    flexDirection: 'column',
    alignItems: 'flex-end',
  },
  mapControlButton: {
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    borderRadius: 8,
    padding: 8,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 8,
  },
  mapControlText: {
    color: '#ffffff',
    fontSize: 12,
    marginLeft: 4,
  },
  controlsContainer: {
    padding: 16,
  },
  trackingStatusContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  statusIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  statusText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#1f2937',
  },
  trackingButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  trackingButtonText: {
    color: '#ffffff',
    fontWeight: 'bold',
  },
  divider: {
    height: 1,
    backgroundColor: '#e5e7eb',
    marginVertical: 16,
  },
  optionContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  optionTextContainer: {
    flex: 1,
  },
  optionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#1f2937',
  },
  optionDescription: {
    fontSize: 14,
    color: '#6b7280',
    marginTop: 4,
  },
  batteryOptimizationContainer: {
    marginTop: 8,
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 12,
  },
  batteryOptions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  batteryOption: {
    flex: 1,
    padding: 12,
    borderRadius: 8,
    backgroundColor: '#ffffff',
    alignItems: 'center',
    marginHorizontal: 4,
    borderWidth: 1,
    borderColor: '#e5e7eb',
  },
  selectedBatteryOption: {
    borderColor: '#8b5cf6',
    backgroundColor: '#f5f3ff',
  },
  batteryOptionText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#1f2937',
    marginTop: 8,
    marginBottom: 4,
  },
  selectedBatteryOptionText: {
    color: '#8b5cf6',
  },
  batteryOptionDescription: {
    fontSize: 10,
    color: '#6b7280',
    textAlign: 'center',
  },
  activeZonesContainer: {
    marginTop: 8,
    marginBottom: 16,
  },
  activeZoneItem: {
    backgroundColor: '#f0fdf4', // Light green background
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: '#dcfce7',
  },
  activeZoneHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  activeZoneName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#166534', // Dark green text
    marginLeft: 8,
  },
  activeZoneDetail: {
    fontSize: 14,
    color: '#15803d', // Medium green text
  },
  noActiveZonesText: {
    fontSize: 14,
    color: '#6b7280',
    fontStyle: 'italic',
    textAlign: 'center',
    padding: 16,
  },
  geofenceEventsContainer: {
    marginTop: 8,
    marginBottom: 16,
  },
  geofenceEventItem: {
    backgroundColor: '#ffffff',
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: '#e5e7eb',
  },
  geofenceEventHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  geofenceEventTime: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#1f2937',
  },
  geofenceEventType: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  geofenceEventTypeText: {
    fontSize: 12,
    color: '#ffffff',
    fontWeight: 'bold',
  },
  geofenceEventDetail: {
    fontSize: 14,
    color: '#6b7280',
  },
  noEventsText: {
    fontSize: 14,
    color: '#6b7280',
    fontStyle: 'italic',
    textAlign: 'center',
    padding: 16,
  },
  historyContainer: {
    marginTop: 8,
  },
  historyItem: {
    backgroundColor: '#ffffff',
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: '#e5e7eb',
  },
  historyItemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  historyItemTime: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#1f2937',
  },
  historyItemStatus: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  historyItemStatusText: {
    fontSize: 12,
    color: '#ffffff',
    fontWeight: 'bold',
  },
  historyItemDetail: {
    fontSize: 14,
    color: '#6b7280',
    marginBottom: 4,
  },
  noHistoryText: {
    fontSize: 14,
    color: '#6b7280',
    fontStyle: 'italic',
    textAlign: 'center',
    padding: 16,
  },
});

export default LocationTrackingScreen;
