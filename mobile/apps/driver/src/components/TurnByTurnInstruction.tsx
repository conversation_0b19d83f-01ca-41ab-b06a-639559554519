import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import ManeuverIcon from './ManeuverIcon';
import { Maneuver } from '../../../../shared/services/navigationService';

interface TurnByTurnInstructionProps {
  maneuver: Maneuver;
  distanceUnit?: 'miles' | 'kilometers';
  isNext?: boolean;
}

/**
 * Component that displays a turn-by-turn instruction with maneuver icon, instruction text, and distance
 */
const TurnByTurnInstruction: React.FC<TurnByTurnInstructionProps> = ({ 
  maneuver, 
  distanceUnit = 'miles',
  isNext = false
}) => {
  // Format distance based on unit preference
  const formatDistance = (distanceInMeters: number): string => {
    if (distanceUnit === 'miles') {
      const miles = distanceInMeters / 1609.34;
      if (miles < 0.1) {
        return `${Math.round(miles * 5280)} ft`;
      } else if (miles < 1) {
        return `${miles.toFixed(1)} mi`;
      } else {
        return `${Math.round(miles)} mi`;
      }
    } else {
      const kilometers = distanceInMeters / 1000;
      if (kilometers < 1) {
        return `${Math.round(distanceInMeters)} m`;
      } else {
        return `${kilometers.toFixed(1)} km`;
      }
    }
  };

  return (
    <View style={[styles.container, isNext && styles.nextContainer]}>
      <View style={styles.iconContainer}>
        <ManeuverIcon 
          type={maneuver.type} 
          size={isNext ? 32 : 24} 
          color={isNext ? '#8b5cf6' : '#6b7280'} 
        />
      </View>
      <View style={styles.instructionContainer}>
        <Text style={[styles.instruction, isNext && styles.nextInstruction]}>
          {maneuver.instruction}
        </Text>
        {maneuver.street && (
          <Text style={styles.street}>{maneuver.street}</Text>
        )}
      </View>
      <View style={styles.distanceContainer}>
        <Text style={[styles.distance, isNext && styles.nextDistance]}>
          {formatDistance(maneuver.distance)}
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  nextContainer: {
    backgroundColor: '#f9fafb',
    borderLeftWidth: 4,
    borderLeftColor: '#8b5cf6',
  },
  iconContainer: {
    marginRight: 16,
  },
  instructionContainer: {
    flex: 1,
  },
  instruction: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1f2937',
  },
  nextInstruction: {
    fontSize: 18,
    fontWeight: '600',
  },
  street: {
    fontSize: 14,
    color: '#6b7280',
    marginTop: 2,
  },
  distanceContainer: {
    marginLeft: 8,
    alignItems: 'flex-end',
  },
  distance: {
    fontSize: 14,
    fontWeight: '500',
    color: '#6b7280',
  },
  nextDistance: {
    fontSize: 16,
    color: '#8b5cf6',
  },
});

export default TurnByTurnInstruction;