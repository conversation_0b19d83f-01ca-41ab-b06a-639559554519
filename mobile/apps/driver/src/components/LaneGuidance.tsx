import React from 'react';
import { View, StyleSheet } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Lane } from '../../../../shared/services/navigationService';

interface LaneGuidanceProps {
  lanes: Lane[];
}

/**
 * Component that displays lane guidance for complex intersections
 */
const LaneGuidance: React.FC<LaneGuidanceProps> = ({ lanes }) => {
  return (
    <View style={styles.container}>
      {lanes.map((lane, index) => (
        <LaneIndicator 
          key={index}
          directions={lane.directions}
          isRecommended={lane.recommended}
        />
      ))}
    </View>
  );
};

interface LaneIndicatorProps {
  directions: ('straight' | 'slight-left' | 'left' | 'sharp-left' | 'slight-right' | 'right' | 'sharp-right' | 'u-turn')[];
  isRecommended: boolean;
}

/**
 * Component that displays a single lane with direction indicators
 */
const LaneIndicator: React.FC<LaneIndicatorProps> = ({ directions, isRecommended }) => {
  // Helper function to render the appropriate arrow for each direction
  const renderArrow = (direction: string) => {
    // Map direction to icon name
    const getIconName = (direction: string) => {
      switch (direction) {
        case 'straight':
          return 'arrow-up';
        case 'slight-left':
          return 'arrow-up-left';
        case 'left':
          return 'arrow-left';
        case 'sharp-left':
          return 'arrow-down-left';
        case 'slight-right':
          return 'arrow-up-right';
        case 'right':
          return 'arrow-right';
        case 'sharp-right':
          return 'arrow-down-right';
        case 'u-turn':
          return 'arrow-u-left-top';
        default:
          return 'help-circle-outline';
      }
    };

    return (
      <View 
        key={direction} 
        style={[
          styles.arrow, 
          isRecommended ? styles.recommendedArrow : styles.regularArrow
        ]}
      >
        <Ionicons 
          name={getIconName(direction)} 
          size={24} 
          color={isRecommended ? '#8b5cf6' : '#9ca3af'} 
        />
      </View>
    );
  };

  return (
    <View style={[
      styles.laneIndicator, 
      isRecommended ? styles.recommendedLane : styles.regularLane
    ]}>
      {directions.map(direction => renderArrow(direction))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 12,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  laneIndicator: {
    width: 40,
    height: 60,
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 4,
    borderWidth: 2,
    borderRadius: 4,
  },
  regularLane: {
    borderColor: '#e5e7eb',
    backgroundColor: '#f9fafb',
  },
  recommendedLane: {
    borderColor: '#8b5cf6',
    backgroundColor: '#f3f0ff',
  },
  arrow: {
    width: 24,
    height: 24,
  },
  regularArrow: {
    opacity: 0.7,
  },
  recommendedArrow: {
    opacity: 1,
  },
});

export default LaneGuidance;
