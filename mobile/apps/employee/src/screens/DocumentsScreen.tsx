import React, { useState } from 'react';
import { View, Text, StyleSheet, FlatList, TouchableOpacity, TextInput, ScrollView } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { DocumentsScreenNavigationProp } from '../types/navigation';

interface Document {
  id: string;
  title: string;
  description: string;
  category: string;
  dateAdded: string;
  fileType: 'pdf' | 'doc' | 'image' | 'video';
  fileSize: string;
  isRequired: boolean;
  isRead: boolean;
}

const DocumentsScreen = () => {
  const navigation = useNavigation<DocumentsScreenNavigationProp>();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);

  // Mock data - would be fetched from API in a real implementation
  const mockDocuments: Document[] = [
    {
      id: '1',
      title: 'Employee Handbook',
      description: 'Complete guide to company policies and procedures',
      category: 'Policies',
      dateAdded: '2023-01-15',
      fileType: 'pdf',
      fileSize: '2.4 MB',
      isRequired: true,
      isRead: true,
    },
    {
      id: '2',
      title: 'Safety Guidelines',
      description: 'Important safety procedures for farm operations',
      category: 'Safety',
      dateAdded: '2023-02-20',
      fileType: 'pdf',
      fileSize: '1.8 MB',
      isRequired: true,
      isRead: false,
    },
    {
      id: '3',
      title: 'Equipment Operation Manual',
      description: 'Instructions for operating farm equipment safely',
      category: 'Equipment',
      dateAdded: '2023-03-10',
      fileType: 'pdf',
      fileSize: '5.2 MB',
      isRequired: false,
      isRead: false,
    },
    {
      id: '4',
      title: 'Crop Handling Procedures',
      description: 'Best practices for handling various crops',
      category: 'Procedures',
      dateAdded: '2023-04-05',
      fileType: 'doc',
      fileSize: '1.1 MB',
      isRequired: false,
      isRead: true,
    },
    {
      id: '5',
      title: 'Emergency Response Plan',
      description: 'Procedures to follow in case of emergency',
      category: 'Safety',
      dateAdded: '2023-05-12',
      fileType: 'pdf',
      fileSize: '3.0 MB',
      isRequired: true,
      isRead: true,
    },
    {
      id: '6',
      title: 'Chemical Handling Training',
      description: 'Video tutorial on safe chemical handling',
      category: 'Training',
      dateAdded: '2023-06-01',
      fileType: 'video',
      fileSize: '45.6 MB',
      isRequired: true,
      isRead: false,
    },
  ];

  // Get unique categories for filter
  const categories = Array.from(new Set(mockDocuments.map(doc => doc.category)));

  // Filter documents based on search query and category
  const filteredDocuments = mockDocuments.filter(doc => {
    const matchesSearch = doc.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         doc.description.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = selectedCategory ? doc.category === selectedCategory : true;
    return matchesSearch && matchesCategory;
  });

  const getFileTypeIcon = (fileType: string) => {
    switch (fileType) {
      case 'pdf':
        return 'document-text-outline';
      case 'doc':
        return 'document-outline';
      case 'image':
        return 'image-outline';
      case 'video':
        return 'videocam-outline';
      default:
        return 'document-outline';
    }
  };

  const handleDocumentPress = (document: Document) => {
    // In a real implementation, this would open the document
    console.log(`Opening document: ${document.title}`);

    // Mark as read if not already
    if (!document.isRead) {
      // In a real implementation, this would update the API
      console.log(`Marking document as read: ${document.title}`);
    }
  };

  const renderDocumentItem = ({ item }: { item: Document }) => (
    <TouchableOpacity 
      style={styles.documentItem}
      onPress={() => handleDocumentPress(item)}
    >
      <View style={styles.documentIconContainer}>
        <Ionicons 
          name={getFileTypeIcon(item.fileType)} 
          size={24} 
          color="#2196F3" 
        />
        {item.isRequired && !item.isRead && (
          <View style={styles.requiredBadge}>
            <Text style={styles.requiredBadgeText}>!</Text>
          </View>
        )}
      </View>

      <View style={styles.documentInfo}>
        <Text style={styles.documentTitle}>{item.title}</Text>
        <Text style={styles.documentDescription} numberOfLines={2}>{item.description}</Text>
        <View style={styles.documentMeta}>
          <Text style={styles.documentCategory}>{item.category}</Text>
          <Text style={styles.documentSize}>{item.fileSize}</Text>
        </View>
      </View>

      <Ionicons name="chevron-forward" size={20} color="#757575" />
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      <View style={styles.searchContainer}>
        <Ionicons name="search" size={20} color="#757575" style={styles.searchIcon} />
        <TextInput
          style={styles.searchInput}
          placeholder="Search documents..."
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
      </View>

      <View style={styles.categoriesContainer}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false} contentContainerStyle={styles.categoriesList}>
          <TouchableOpacity
            style={[styles.categoryButton, selectedCategory === null && styles.categoryButtonActive]}
            onPress={() => setSelectedCategory(null)}
          >
            <Text style={[styles.categoryButtonText, selectedCategory === null && styles.categoryButtonTextActive]}>All</Text>
          </TouchableOpacity>

          {categories.map(category => (
            <TouchableOpacity
              key={category}
              style={[styles.categoryButton, selectedCategory === category && styles.categoryButtonActive]}
              onPress={() => setSelectedCategory(category)}
            >
              <Text style={[styles.categoryButtonText, selectedCategory === category && styles.categoryButtonTextActive]}>{category}</Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>

      <View style={styles.requiredDocumentsContainer}>
        <Text style={styles.sectionTitle}>Required Documents</Text>
        {filteredDocuments.filter(doc => doc.isRequired && !doc.isRead).length > 0 ? (
          <FlatList
            data={filteredDocuments.filter(doc => doc.isRequired && !doc.isRead)}
            renderItem={renderDocumentItem}
            keyExtractor={item => item.id}
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.requiredDocumentsList}
          />
        ) : (
          <Text style={styles.emptyText}>No required documents to read</Text>
        )}
      </View>

      <Text style={[styles.sectionTitle, { marginTop: 20, marginLeft: 15 }]}>All Documents</Text>
      <FlatList
        data={filteredDocuments}
        renderItem={renderDocumentItem}
        keyExtractor={item => item.id}
        contentContainerStyle={styles.documentsList}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Ionicons name="document-text" size={50} color="#BDBDBD" />
            <Text style={styles.emptyText}>No documents found</Text>
          </View>
        }
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderRadius: 8,
    margin: 15,
    paddingHorizontal: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  searchIcon: {
    marginRight: 10,
  },
  searchInput: {
    flex: 1,
    height: 50,
    fontSize: 16,
  },
  categoriesContainer: {
    marginBottom: 15,
  },
  categoriesList: {
    paddingHorizontal: 15,
  },
  categoryButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    marginRight: 8,
    borderRadius: 20,
    backgroundColor: '#E0E0E0',
  },
  categoryButtonActive: {
    backgroundColor: '#4CAF50',
  },
  categoryButtonText: {
    color: '#757575',
    fontSize: 14,
  },
  categoryButtonTextActive: {
    color: '#fff',
  },
  requiredDocumentsContainer: {
    marginBottom: 10,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginLeft: 15,
    marginBottom: 10,
  },
  requiredDocumentsList: {
    paddingLeft: 15,
    paddingRight: 5,
  },
  documentsList: {
    padding: 15,
    paddingTop: 0,
  },
  documentItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 15,
    marginBottom: 15,
    marginRight: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
    width: 300,
  },
  documentIconContainer: {
    position: 'relative',
    marginRight: 15,
  },
  requiredBadge: {
    position: 'absolute',
    top: -5,
    right: -5,
    backgroundColor: '#F44336',
    borderRadius: 10,
    width: 16,
    height: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  requiredBadgeText: {
    color: '#fff',
    fontSize: 10,
    fontWeight: 'bold',
  },
  documentInfo: {
    flex: 1,
  },
  documentTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  documentDescription: {
    fontSize: 14,
    color: '#757575',
    marginBottom: 5,
  },
  documentMeta: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  documentCategory: {
    fontSize: 12,
    color: '#2196F3',
    fontWeight: '500',
  },
  documentSize: {
    fontSize: 12,
    color: '#757575',
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 50,
  },
  emptyText: {
    fontSize: 16,
    color: '#757575',
    marginTop: 10,
    textAlign: 'center',
    padding: 20,
  },
});

export default DocumentsScreen;
