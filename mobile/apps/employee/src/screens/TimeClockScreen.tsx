import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, FlatList, Alert } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as Location from 'expo-location';
import { useAuth } from '../../../../shared/services/auth/AuthContext';

interface TimeRecord {
  id: string;
  type: 'clock_in' | 'clock_out';
  timestamp: string;
  location?: {
    latitude: number;
    longitude: number;
    accuracy: number;
  };
  notes?: string;
}

const TimeClockScreen = () => {
  const { user } = useAuth();
  const [isClockedIn, setIsClockedIn] = useState(false);
  const [currentTime, setCurrentTime] = useState(new Date());
  const [timeRecords, setTimeRecords] = useState<TimeRecord[]>([]);
  const [locationPermission, setLocationPermission] = useState<boolean | null>(null);
  const [currentLocation, setCurrentLocation] = useState<Location.LocationObject | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  // Request location permissions on component mount
  useEffect(() => {
    (async () => {
      const { status } = await Location.requestForegroundPermissionsAsync();
      setLocationPermission(status === 'granted');
    })();
  }, []);

  // Update current time every second
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  // Check if user is already clocked in (would be fetched from API in real implementation)
  useEffect(() => {
    // Mock data - would be fetched from API in a real implementation
    const mockTimeRecords: TimeRecord[] = [
      {
        id: '1',
        type: 'clock_in',
        timestamp: '2023-06-21T08:30:00Z',
        location: {
          latitude: 37.7749,
          longitude: -122.4194,
          accuracy: 10,
        },
      },
      {
        id: '2',
        type: 'clock_out',
        timestamp: '2023-06-21T12:00:00Z',
        location: {
          latitude: 37.7749,
          longitude: -122.4194,
          accuracy: 10,
        },
      },
      {
        id: '3',
        type: 'clock_in',
        timestamp: '2023-06-21T13:00:00Z',
        location: {
          latitude: 37.7749,
          longitude: -122.4194,
          accuracy: 10,
        },
      },
      {
        id: '4',
        type: 'clock_out',
        timestamp: '2023-06-21T17:00:00Z',
        location: {
          latitude: 37.7749,
          longitude: -122.4194,
          accuracy: 10,
        },
      },
      {
        id: '5',
        type: 'clock_in',
        timestamp: '2023-06-22T08:30:00Z',
        location: {
          latitude: 37.7749,
          longitude: -122.4194,
          accuracy: 10,
        },
      },
    ];

    setTimeRecords(mockTimeRecords);

    // Check if the last record is a clock-in (no matching clock-out)
    const lastRecord = mockTimeRecords[mockTimeRecords.length - 1];
    if (lastRecord && lastRecord.type === 'clock_in') {
      setIsClockedIn(true);
    }
  }, []);

  const handleClockAction = async () => {
    if (!locationPermission) {
      Alert.alert(
        'Location Permission Required',
        'Please enable location services to clock in/out.',
        [{ text: 'OK' }]
      );
      return;
    }

    setIsLoading(true);

    try {
      // Get current location
      const location = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.High,
      });
      setCurrentLocation(location);

      // Create new time record
      const newRecord: TimeRecord = {
        id: Date.now().toString(),
        type: isClockedIn ? 'clock_out' : 'clock_in',
        timestamp: new Date().toISOString(),
        location: {
          latitude: location.coords.latitude,
          longitude: location.coords.longitude,
          accuracy: location.coords.accuracy,
        },
      };

      // Update state
      setTimeRecords([...timeRecords, newRecord]);
      setIsClockedIn(!isClockedIn);

      // In a real implementation, this would be sent to the API
      console.log('New time record:', newRecord);
    } catch (error) {
      Alert.alert(
        'Error',
        'Failed to get your location. Please try again.',
        [{ text: 'OK' }]
      );
    } finally {
      setIsLoading(false);
    }
  };

  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      weekday: 'short',
      month: 'short',
      day: 'numeric',
    });
  };

  const calculateHoursWorked = () => {
    let totalMilliseconds = 0;
    let clockInTime: Date | null = null;

    timeRecords.forEach(record => {
      const recordTime = new Date(record.timestamp);
      
      if (record.type === 'clock_in') {
        clockInTime = recordTime;
      } else if (record.type === 'clock_out' && clockInTime) {
        totalMilliseconds += recordTime.getTime() - clockInTime.getTime();
        clockInTime = null;
      }
    });

    // If still clocked in, add time until now
    if (clockInTime) {
      totalMilliseconds += new Date().getTime() - clockInTime.getTime();
    }

    // Convert to hours
    const hours = Math.floor(totalMilliseconds / (1000 * 60 * 60));
    const minutes = Math.floor((totalMilliseconds % (1000 * 60 * 60)) / (1000 * 60));
    
    return `${hours}h ${minutes}m`;
  };

  const groupRecordsByDate = () => {
    const grouped: { [date: string]: TimeRecord[] } = {};
    
    timeRecords.forEach(record => {
      const date = new Date(record.timestamp).toLocaleDateString();
      if (!grouped[date]) {
        grouped[date] = [];
      }
      grouped[date].push(record);
    });
    
    return Object.entries(grouped).map(([date, records]) => ({
      date,
      records,
    }));
  };

  const renderTimeRecordItem = ({ item }: { item: { date: string; records: TimeRecord[] } }) => (
    <View style={styles.dayContainer}>
      <Text style={styles.dateHeader}>{formatDate(item.date)}</Text>
      {item.records.map(record => (
        <View key={record.id} style={styles.recordItem}>
          <View style={[
            styles.recordTypeIndicator, 
            { backgroundColor: record.type === 'clock_in' ? '#4CAF50' : '#F44336' }
          ]} />
          <View style={styles.recordInfo}>
            <Text style={styles.recordType}>
              {record.type === 'clock_in' ? 'Clock In' : 'Clock Out'}
            </Text>
            <Text style={styles.recordTime}>{formatTime(record.timestamp)}</Text>
          </View>
          {record.location && (
            <TouchableOpacity style={styles.locationButton}>
              <Ionicons name="location-outline" size={20} color="#2196F3" />
            </TouchableOpacity>
          )}
        </View>
      ))}
    </View>
  );

  return (
    <View style={styles.container}>
      <View style={styles.clockCard}>
        <Text style={styles.currentTime}>
          {currentTime.toLocaleTimeString('en-US', {
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
          })}
        </Text>
        <Text style={styles.currentDate}>
          {currentTime.toLocaleDateString('en-US', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric',
          })}
        </Text>
        
        <TouchableOpacity
          style={[
            styles.clockButton,
            { backgroundColor: isClockedIn ? '#F44336' : '#4CAF50' },
            isLoading && styles.clockButtonDisabled,
          ]}
          onPress={handleClockAction}
          disabled={isLoading}
        >
          {isLoading ? (
            <Text style={styles.clockButtonText}>Loading...</Text>
          ) : (
            <>
              <Ionicons 
                name={isClockedIn ? 'exit-outline' : 'enter-outline'} 
                size={24} 
                color="#fff" 
              />
              <Text style={styles.clockButtonText}>
                {isClockedIn ? 'Clock Out' : 'Clock In'}
              </Text>
            </>
          )}
        </TouchableOpacity>
        
        {isClockedIn && (
          <View style={styles.statusContainer}>
            <Ionicons name="time" size={20} color="#4CAF50" />
            <Text style={styles.statusText}>
              You are currently clocked in
            </Text>
          </View>
        )}
      </View>
      
      <View style={styles.summaryContainer}>
        <View style={styles.summaryItem}>
          <Text style={styles.summaryLabel}>Today</Text>
          <Text style={styles.summaryValue}>
            {calculateHoursWorked()}
          </Text>
        </View>
        <View style={styles.summaryItem}>
          <Text style={styles.summaryLabel}>This Week</Text>
          <Text style={styles.summaryValue}>32h 15m</Text>
        </View>
      </View>
      
      <View style={styles.historyContainer}>
        <Text style={styles.historyTitle}>Time History</Text>
        <FlatList
          data={groupRecordsByDate()}
          renderItem={renderTimeRecordItem}
          keyExtractor={item => item.date}
          contentContainerStyle={styles.historyList}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  clockCard: {
    backgroundColor: '#fff',
    borderRadius: 10,
    margin: 15,
    padding: 20,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  currentTime: {
    fontSize: 36,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  currentDate: {
    fontSize: 16,
    color: '#757575',
    marginBottom: 20,
  },
  clockButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#4CAF50',
    borderRadius: 30,
    paddingVertical: 15,
    paddingHorizontal: 30,
    width: '100%',
  },
  clockButtonDisabled: {
    backgroundColor: '#BDBDBD',
  },
  clockButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
    marginLeft: 10,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 15,
  },
  statusText: {
    fontSize: 14,
    color: '#4CAF50',
    marginLeft: 5,
    fontWeight: '500',
  },
  summaryContainer: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    borderRadius: 10,
    margin: 15,
    marginTop: 0,
    padding: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  summaryItem: {
    flex: 1,
    alignItems: 'center',
  },
  summaryLabel: {
    fontSize: 14,
    color: '#757575',
  },
  summaryValue: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 5,
  },
  historyContainer: {
    flex: 1,
    margin: 15,
    marginTop: 0,
  },
  historyTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  historyList: {
    paddingBottom: 20,
  },
  dayContainer: {
    marginBottom: 15,
  },
  dateHeader: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 5,
    color: '#757575',
  },
  recordItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 15,
    marginBottom: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
  },
  recordTypeIndicator: {
    width: 4,
    height: 40,
    borderRadius: 2,
    marginRight: 15,
  },
  recordInfo: {
    flex: 1,
  },
  recordType: {
    fontSize: 16,
    fontWeight: '500',
  },
  recordTime: {
    fontSize: 14,
    color: '#757575',
    marginTop: 3,
  },
  locationButton: {
    padding: 5,
  },
});

export default TimeClockScreen;