import React from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { HomeScreenNavigationProp } from '../types/navigation';
import { useAuth } from '../../../../shared/services/auth/AuthContext';

const HomeScreen = () => {
  const navigation = useNavigation<HomeScreenNavigationProp>();
  const { user } = useAuth();

  // Mock data - would be fetched from API in a real implementation
  const taskCount = 5;
  const pendingTaskCount = 3;
  const clockedIn = true;
  const clockInTime = '08:30 AM';
  const recentDocuments = [
    { id: '1', title: 'Safety Guidelines', date: '2023-06-15' },
    { id: '2', title: 'Employee Handbook', date: '2023-05-20' },
  ];

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.greeting}>Hello, {user?.name || 'Employee'}</Text>
        <Text style={styles.date}>{new Date().toLocaleDateString('en-US', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' })}</Text>
      </View>

      <View style={styles.statusCard}>
        <View style={styles.statusItem}>
          <Ionicons name="time" size={24} color="#4CAF50" />
          <Text style={styles.statusLabel}>Status</Text>
          <Text style={[styles.statusValue, { color: clockedIn ? '#4CAF50' : '#F44336' }]}>
            {clockedIn ? 'Clocked In' : 'Clocked Out'}
          </Text>
          {clockedIn && <Text style={styles.statusDetail}>Since {clockInTime}</Text>}
        </View>

        <View style={styles.statusItem}>
          <Ionicons name="list" size={24} color="#2196F3" />
          <Text style={styles.statusLabel}>Tasks</Text>
          <Text style={styles.statusValue}>{taskCount} Total</Text>
          <Text style={styles.statusDetail}>{pendingTaskCount} Pending</Text>
        </View>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Quick Actions</Text>
        <View style={styles.quickActions}>
          <TouchableOpacity 
            style={styles.actionButton}
            onPress={() => navigation.navigate('TimeClock')}
          >
            <Ionicons name="time-outline" size={24} color="#fff" />
            <Text style={styles.actionButtonText}>Clock {clockedIn ? 'Out' : 'In'}</Text>
          </TouchableOpacity>

          <TouchableOpacity 
            style={styles.actionButton}
            onPress={() => navigation.navigate('Tasks')}
          >
            <Ionicons name="list-outline" size={24} color="#fff" />
            <Text style={styles.actionButtonText}>View Tasks</Text>
          </TouchableOpacity>
        </View>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Recent Documents</Text>
        {recentDocuments.map(doc => (
          <TouchableOpacity 
            key={doc.id}
            style={styles.documentItem}
            onPress={() => navigation.navigate('Documents')}
          >
            <Ionicons name="document-text-outline" size={24} color="#2196F3" />
            <View style={styles.documentInfo}>
              <Text style={styles.documentTitle}>{doc.title}</Text>
              <Text style={styles.documentDate}>Updated: {new Date(doc.date).toLocaleDateString()}</Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color="#757575" />
          </TouchableOpacity>
        ))}
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    padding: 20,
    backgroundColor: '#4CAF50',
  },
  greeting: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
  },
  date: {
    fontSize: 14,
    color: '#e0e0e0',
    marginTop: 5,
  },
  statusCard: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    borderRadius: 10,
    margin: 15,
    padding: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  statusItem: {
    flex: 1,
    alignItems: 'center',
    padding: 10,
  },
  statusLabel: {
    fontSize: 14,
    color: '#757575',
    marginTop: 5,
  },
  statusValue: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 5,
  },
  statusDetail: {
    fontSize: 12,
    color: '#757575',
    marginTop: 3,
  },
  section: {
    margin: 15,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 15,
  },
  quickActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#4CAF50',
    borderRadius: 8,
    padding: 15,
    flex: 0.48,
    justifyContent: 'center',
  },
  actionButtonText: {
    color: '#fff',
    fontWeight: 'bold',
    marginLeft: 10,
  },
  documentItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 15,
    marginBottom: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  documentInfo: {
    flex: 1,
    marginLeft: 15,
  },
  documentTitle: {
    fontSize: 16,
    fontWeight: '500',
  },
  documentDate: {
    fontSize: 12,
    color: '#757575',
    marginTop: 3,
  },
});

export default HomeScreen;