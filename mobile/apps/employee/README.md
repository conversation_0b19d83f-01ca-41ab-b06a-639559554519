# NxtAcre Employee App

## Overview

The NxtAcre Employee App is part of the NxtAcre Farm Management Platform, designed specifically for farm employees and seasonal workers. It provides a streamlined interface for managing daily tasks, tracking work hours, accessing important documents, and managing user profiles.

## Features

- **Task Management**: View assigned tasks, filter by status, search, and update task status
- **Time Clock**: Clock in and out with location tracking, view time history and summaries
- **Document Access**: Access training materials, safety information, and other important documents
- **User Profile**: View and manage personal information and app settings

## Screens

- **HomeScreen**: Dashboard with task count, clock-in status, and quick actions
- **TasksScreen**: List of tasks with search and filtering
- **TaskDetailScreen**: Detailed view of a task with status update options
- **TimeClockScreen**: Time tracking with clock in/out functionality
- **DocumentsScreen**: Access to training materials and documents
- **ProfileScreen**: User profile and app settings management

## Technical Implementation

The app is built using:
- React Native with Expo
- TypeScript for type safety
- AsyncStorage for local data persistence
- Expo Location for GPS tracking
- React Navigation for screen navigation
- Shared services for data management

## Project Structure

```
employee/
├── assets/             # Static assets for the app
├── src/                # Source code
│   ├── components/     # Reusable UI components
│   ├── navigation/     # Navigation configuration
│   │   └── AppNavigator.tsx  # Main navigation structure
│   ├── screens/        # Screen components
│   │   ├── HomeScreen.tsx
│   │   ├── TasksScreen.tsx
│   │   ├── TaskDetailScreen.tsx
│   │   ├── TimeClockScreen.tsx
│   │   ├── DocumentsScreen.tsx
│   │   └── ProfileScreen.tsx
│   └── types/          # TypeScript type definitions
│       └── navigation.ts  # Navigation types
├── App.tsx             # Main app component
├── app.json            # Expo configuration
├── DEVELOPMENT_SUMMARY.md  # Development progress summary
└── README.md           # This file
```

## Getting Started

### Prerequisites

- Node.js (v16 or higher)
- npm (v7 or higher)
- Expo CLI

### Installation

1. Clone the repository
2. Navigate to the project directory
3. Install dependencies:
   ```
   npm install
   ```
4. Start the development server:
   ```
   npm start
   ```
5. Use the Expo Go app on your mobile device to scan the QR code, or press 'a' to open in an Android emulator or 'i' to open in an iOS simulator

## Development Status

The app currently has all core features implemented, including:
- Task viewing and updates
- Time clock (clock in/out)
- Document access and categorization
- User profile management
- Basic settings management

Future enhancements planned:
- Offline support for critical operations
- Push notifications for new tasks and reminders
- Training material completion tracking
- Photo evidence for task completion

For more details on the development status and roadmap, see the [DEVELOPMENT_SUMMARY.md](./DEVELOPMENT_SUMMARY.md) file.

## Integration with Other Apps

The Employee App is part of the NxtAcre Farm Management Platform, which includes several other apps:
- Field Operations App
- Farm Manager App
- Inventory & Equipment App
- Financial Manager App
- Marketplace App
- Driver App
- Drive Tracker App

The apps share common code and services through the shared directory at the root of the mobile project.

## Contributing

When contributing to this app, please follow these guidelines:
1. Use the shared services and components when possible
2. Follow the established design patterns and code style
3. Write clean, maintainable code with appropriate comments
4. Update the DEVELOPMENT_SUMMARY.md file with any new features or changes
5. Test thoroughly on both Android and iOS devices

## License

This project is proprietary and confidential. Unauthorized copying, distribution, or use is strictly prohibited.