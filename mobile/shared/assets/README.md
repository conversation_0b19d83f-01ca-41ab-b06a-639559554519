# NxtAcre Mobile App Assets

This directory contains the shared assets for all NxtAcre mobile apps.

## Required Assets

The following assets should be placed in this directory:

1. `logo.png` - The NxtAcre logo used in the splash screen and throughout the apps
2. `icon.png` - The app icon (1024x1024 px)
3. `adaptive-icon.png` - The adaptive icon for Android (1024x1024 px)
4. `splash.png` - The splash screen image (2048x2048 px)
5. `favicon.png` - The favicon for web (48x48 px)

## Asset Creation Guidelines

### Icon Requirements
- `icon.png` should be 1024x1024 pixels
- Use the official NxtAcre logo
- Make sure the icon has no transparency
- Use a solid background color (#8b5cf6 for consistency)

### Splash Screen Requirements
- `splash.png` should be 2048x2048 pixels
- Center the NxtAcre logo in the image
- Use a solid background color (#8b5cf6 for consistency)
- Leave enough padding around the logo (at least 25% of the width/height)

### Adaptive Icon Requirements
- `adaptive-icon.png` should be 1024x1024 pixels
- The image should have the foreground content centered with padding
- The background color is set in the app.json file (#8b5cf6 for consistency)

## Usage in App.json

Update all app.json files to use these shared assets:

```json
{
  "expo": {
    "icon": "../../shared/assets/icon.png",
    "splash": {
      "image": "../../shared/assets/splash.png",
      "resizeMode": "contain",
      "backgroundColor": "#8b5cf6"
    },
    "android": {
      "adaptiveIcon": {
        "foregroundImage": "../../shared/assets/adaptive-icon.png",
        "backgroundColor": "#8b5cf6"
      }
    },
    "web": {
      "favicon": "../../shared/assets/favicon.png"
    }
  }
}
```

## Custom Splash Screen

The apps now use a custom splash screen component with an animated loading indicator. This component is defined in `mobile/shared/components/SplashScreen.tsx` and should be used in the main App.tsx file of each app.

Example usage:

```jsx
import React, { useState } from 'react';
import { SplashScreen } from '../../shared';
import MainApp from './MainApp';

export default function App() {
  const [isReady, setIsReady] = useState(false);

  if (!isReady) {
    return <SplashScreen onFinish={() => setIsReady(true)} />;
  }

  return <MainApp />;
}
```