// Components
export { default as SplashScreen } from './components/SplashScreen';
export { default as HelpTip } from './components/HelpTips';
export { default as UnifiedNotificationCenter } from './components/UnifiedNotificationCenter';
export { default as NotificationButton } from './components/NotificationButton';

// Utils
export * from './utils/Animations';
export * from './utils/SentryConfig';

// Services
export * from './services/notificationService';
export * from './services/unifiedNotificationService';

// Add a comment to explain how to use these shared components and utilities
/**
 * NxtAcre Shared Components and Utilities
 * 
 * This package provides shared components and utilities for NxtAcre mobile apps.
 * 
 * Usage:
 * 
 * 1. Import the components and utilities in your app:
 *    ```
 *    import { SplashScreen, HelpTip, useFadeIn, initSentry } from '@nxtacre/shared';
 *    ```
 * 
 * 2. Use the components in your app:
 *    ```
 *    // Custom splash screen with loading indicator
 *    const App = () => {
 *      const [isReady, setIsReady] = useState(false);
 *      
 *      if (!isReady) {
 *        return <SplashScreen onFinish={() => setIsReady(true)} />;
 *      }
 *      
 *      return <MainApp />;
 *    };
 *    
 *    // Help tip
 *    const MyScreen = () => {
 *      return (
 *        <View>
 *          <Text>Some content</Text>
 *          <HelpTip 
 *            title="Help" 
 *            content="This is a help tip that explains how to use this feature."
 *          />
 *        </View>
 *      );
 *    };
 *    
 *    // Animations
 *    const AnimatedComponent = () => {
 *      const { opacity, fadeIn } = useFadeIn();
 *      
 *      useEffect(() => {
 *        fadeIn();
 *      }, []);
 *      
 *      return (
 *        <Animated.View style={{ opacity }}>
 *          <Text>This text fades in</Text>
 *        </Animated.View>
 *      );
 *    };
 *    
 *    // Sentry integration
 *    const initApp = () => {
 *      initSentry('my-app', 'https://your-sentry-dsn');
 *    };
 *    ```
 */
