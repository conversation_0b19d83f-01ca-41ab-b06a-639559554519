import * as Sentry from 'sentry-expo';
import Constants from 'expo-constants';
import { Platform } from 'react-native';

/**
 * Initialize Sentry for error tracking and reporting
 * @param appName The name of the app (e.g., 'driver', 'farm-manager')
 * @param dsn The Sentry DSN (can be obtained from the Sentry dashboard)
 */
export const initSentry = (appName: string, dsn: string) => {
  Sentry.init({
    dsn: dsn,
    enableInExpoDevelopment: false,
    debug: __DEV__, // If true, Sentry will try to print out useful debugging information
    environment: __DEV__ ? 'development' : 'production',
    release: `${appName}@${Constants.expoConfig?.version}`,
    dist: Platform.OS === 'ios' 
      ? Constants.expoConfig?.ios?.buildNumber 
      : Constants.expoConfig?.android?.versionCode,
    tracesSampleRate: 1.0, // Capture 100% of transactions for performance monitoring
    integrations: [
      new Sentry.Native.ReactNativeTracing({
        routingInstrumentation: new Sentry.Native.ReactNavigationInstrumentation(),
      }),
    ],
  });

  // Set user-agent tag
  Sentry.Native.setTag('app.name', appName);
  Sentry.Native.setTag('app.version', Constants.expoConfig?.version || 'unknown');
  Sentry.Native.setTag('device.platform', Platform.OS);
  Sentry.Native.setTag('device.model', Constants.deviceName || 'unknown');

  // Add breadcrumb for app start
  Sentry.Native.addBreadcrumb({
    category: 'app.lifecycle',
    message: `${appName} app started`,
    level: 'info',
  });

  console.log(`Sentry initialized for ${appName}`);
};

/**
 * Set user information in Sentry
 * @param id User ID
 * @param email User email
 * @param username User username
 */
export const setUserContext = (id: string, email?: string, username?: string) => {
  Sentry.Native.setUser({
    id,
    email,
    username,
  });
};

/**
 * Clear user information from Sentry (e.g., on logout)
 */
export const clearUserContext = () => {
  Sentry.Native.setUser(null);
};

/**
 * Capture an exception and send it to Sentry
 * @param error The error to capture
 * @param context Additional context information
 */
export const captureException = (error: Error, context?: Record<string, any>) => {
  if (context) {
    Sentry.Native.setContext('additional', context);
  }
  Sentry.Native.captureException(error);
};

/**
 * Capture a message and send it to Sentry
 * @param message The message to capture
 * @param level The severity level
 */
export const captureMessage = (
  message: string, 
  level: 'fatal' | 'error' | 'warning' | 'info' | 'debug' = 'info'
) => {
  Sentry.Native.captureMessage(message, level);
};

/**
 * Start a new transaction for performance monitoring
 * @param name Transaction name
 * @param operation Operation type
 */
export const startTransaction = (name: string, operation: string) => {
  return Sentry.Native.startTransaction({
    name,
    op: operation,
  });
};

/**
 * Add a breadcrumb to the current scope
 * @param category Breadcrumb category
 * @param message Breadcrumb message
 * @param level Breadcrumb level
 * @param data Additional data
 */
export const addBreadcrumb = (
  category: string,
  message: string,
  level: 'fatal' | 'error' | 'warning' | 'info' | 'debug' = 'info',
  data?: Record<string, any>
) => {
  Sentry.Native.addBreadcrumb({
    category,
    message,
    level,
    data,
  });
};

/**
 * Set a tag for the current scope
 * @param key Tag key
 * @param value Tag value
 */
export const setTag = (key: string, value: string) => {
  Sentry.Native.setTag(key, value);
};

/**
 * Set extra context for the current scope
 * @param key Context key
 * @param value Context value
 */
export const setExtra = (key: string, value: any) => {
  Sentry.Native.setExtra(key, value);
};