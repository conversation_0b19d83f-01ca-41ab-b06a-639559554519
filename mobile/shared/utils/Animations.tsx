import React, { useRef } from 'react';
import { Animated, Easing, ViewStyle } from 'react-native';

// Fade in animation
export const useFadeIn = (duration = 500, delay = 0) => {
  const opacity = useRef(new Animated.Value(0)).current;
  
  const fadeIn = () => {
    Animated.timing(opacity, {
      toValue: 1,
      duration,
      delay,
      useNativeDriver: true,
      easing: Easing.ease,
    }).start();
  };
  
  const fadeOut = (callback?: () => void) => {
    Animated.timing(opacity, {
      toValue: 0,
      duration,
      useNativeDriver: true,
      easing: Easing.ease,
    }).start(callback);
  };
  
  return { opacity, fadeIn, fadeOut };
};

// Slide animation
export const useSlide = (
  direction: 'up' | 'down' | 'left' | 'right' = 'up',
  distance = 100,
  duration = 500,
  delay = 0
) => {
  const translateX = useRef(new Animated.Value(direction === 'left' ? distance : direction === 'right' ? -distance : 0)).current;
  const translateY = useRef(new Animated.Value(direction === 'up' ? distance : direction === 'down' ? -distance : 0)).current;
  
  const slide = () => {
    Animated.timing(direction === 'left' || direction === 'right' ? translateX : translateY, {
      toValue: 0,
      duration,
      delay,
      useNativeDriver: true,
      easing: Easing.out(Easing.cubic),
    }).start();
  };
  
  const slideBack = (callback?: () => void) => {
    Animated.timing(direction === 'left' || direction === 'right' ? translateX : translateY, {
      toValue: direction === 'left' ? distance : direction === 'right' ? -distance : direction === 'up' ? distance : -distance,
      duration,
      useNativeDriver: true,
      easing: Easing.in(Easing.cubic),
    }).start(callback);
  };
  
  const style = {
    transform: [
      { translateX },
      { translateY },
    ],
  };
  
  return { style, slide, slideBack };
};

// Scale animation
export const useScale = (
  startScale = 0.8,
  endScale = 1,
  duration = 500,
  delay = 0
) => {
  const scale = useRef(new Animated.Value(startScale)).current;
  
  const scaleUp = () => {
    Animated.timing(scale, {
      toValue: endScale,
      duration,
      delay,
      useNativeDriver: true,
      easing: Easing.out(Easing.back(1.5)),
    }).start();
  };
  
  const scaleDown = (callback?: () => void) => {
    Animated.timing(scale, {
      toValue: startScale,
      duration,
      useNativeDriver: true,
      easing: Easing.in(Easing.back(1.5)),
    }).start(callback);
  };
  
  const style = {
    transform: [{ scale }],
  };
  
  return { style, scaleUp, scaleDown };
};

// Pulse animation
export const usePulse = (
  minScale = 0.95,
  maxScale = 1.05,
  duration = 1000
) => {
  const scale = useRef(new Animated.Value(1)).current;
  
  const pulse = () => {
    Animated.loop(
      Animated.sequence([
        Animated.timing(scale, {
          toValue: maxScale,
          duration: duration / 2,
          useNativeDriver: true,
          easing: Easing.inOut(Easing.sine),
        }),
        Animated.timing(scale, {
          toValue: minScale,
          duration: duration / 2,
          useNativeDriver: true,
          easing: Easing.inOut(Easing.sine),
        }),
      ])
    ).start();
  };
  
  const stopPulse = () => {
    scale.stopAnimation();
    Animated.timing(scale, {
      toValue: 1,
      duration: 300,
      useNativeDriver: true,
    }).start();
  };
  
  const style = {
    transform: [{ scale }],
  };
  
  return { style, pulse, stopPulse };
};

// Animated button press
export const useButtonPress = (
  scaleAmount = 0.95,
  duration = 100
) => {
  const scale = useRef(new Animated.Value(1)).current;
  
  const onPressIn = () => {
    Animated.timing(scale, {
      toValue: scaleAmount,
      duration,
      useNativeDriver: true,
      easing: Easing.ease,
    }).start();
  };
  
  const onPressOut = () => {
    Animated.timing(scale, {
      toValue: 1,
      duration,
      useNativeDriver: true,
      easing: Easing.ease,
    }).start();
  };
  
  const style = {
    transform: [{ scale }],
  };
  
  return { style, onPressIn, onPressOut };
};

// Animated list item
interface AnimatedListItemProps {
  index: number;
  children: React.ReactNode;
  style?: ViewStyle;
}

export const AnimatedListItem: React.FC<AnimatedListItemProps> = ({ 
  index, 
  children, 
  style 
}) => {
  const opacity = useRef(new Animated.Value(0)).current;
  const translateY = useRef(new Animated.Value(50)).current;
  
  React.useEffect(() => {
    Animated.parallel([
      Animated.timing(opacity, {
        toValue: 1,
        duration: 400,
        delay: index * 100,
        useNativeDriver: true,
      }),
      Animated.timing(translateY, {
        toValue: 0,
        duration: 400,
        delay: index * 100,
        useNativeDriver: true,
        easing: Easing.out(Easing.cubic),
      }),
    ]).start();
  }, [opacity, translateY, index]);
  
  return (
    <Animated.View
      style={[
        {
          opacity,
          transform: [{ translateY }],
        },
        style,
      ]}
    >
      {children}
    </Animated.View>
  );
};