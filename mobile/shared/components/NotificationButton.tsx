import React, { useEffect, useState } from 'react';
import { TouchableOpacity, Text, StyleSheet, View, Modal } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as unifiedNotificationService from '../services/unifiedNotificationService';
import UnifiedNotificationCenter from './UnifiedNotificationCenter';
import { AppIdentifier, UnifiedNotification } from '../services/unifiedNotificationService';

interface NotificationButtonProps {
  appIdentifier: AppIdentifier;
  onNotificationPress?: (notification: UnifiedNotification) => void;
  color?: string;
  size?: number;
}

const NotificationButton: React.FC<NotificationButtonProps> = ({
  appIdentifier,
  onNotificationPress,
  color = '#333',
  size = 24,
}) => {
  const [unreadCount, setUnreadCount] = useState(0);
  const [modalVisible, setModalVisible] = useState(false);
  const [loading, setLoading] = useState(false);

  const loadUnreadCount = async () => {
    setLoading(true);
    try {
      const count = await unifiedNotificationService.getUnreadNotificationCount(appIdentifier);
      setUnreadCount(count);
    } catch (error) {
      console.error('Error loading unread count:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    // Load initial unread count
    loadUnreadCount();

    // Set up a timer to refresh the count periodically
    const intervalId = setInterval(loadUnreadCount, 30000); // Every 30 seconds

    // Clean up the interval when the component unmounts
    return () => clearInterval(intervalId);
  }, [appIdentifier]);

  // Refresh the count when the modal is closed
  const handleCloseModal = () => {
    setModalVisible(false);
    loadUnreadCount();
  };

  const handleNotificationPress = (notification: UnifiedNotification) => {
    // Close the modal
    setModalVisible(false);
    
    // Call the onNotificationPress callback if provided
    if (onNotificationPress) {
      onNotificationPress(notification);
    }
  };

  return (
    <>
      <TouchableOpacity
        style={styles.button}
        onPress={() => setModalVisible(true)}
        accessibilityLabel="Notifications"
        accessibilityHint="Opens the notification center"
      >
        <Ionicons name="notifications" size={size} color={color} />
        {unreadCount > 0 && (
          <View style={styles.badge}>
            <Text style={styles.badgeText}>
              {unreadCount > 99 ? '99+' : unreadCount}
            </Text>
          </View>
        )}
      </TouchableOpacity>

      <Modal
        animationType="slide"
        transparent={false}
        visible={modalVisible}
        onRequestClose={handleCloseModal}
      >
        <UnifiedNotificationCenter
          appIdentifier={appIdentifier}
          onNotificationPress={handleNotificationPress}
          onClose={handleCloseModal}
        />
      </Modal>
    </>
  );
};

const styles = StyleSheet.create({
  button: {
    padding: 8,
    position: 'relative',
  },
  badge: {
    position: 'absolute',
    top: 0,
    right: 0,
    backgroundColor: '#e74c3c',
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 4,
  },
  badgeText: {
    color: '#fff',
    fontSize: 10,
    fontWeight: 'bold',
  },
});

export default NotificationButton;