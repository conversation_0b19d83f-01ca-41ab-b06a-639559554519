import React, { useState } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  TouchableOpacity, 
  Modal, 
  ScrollView,
  Animated,
  Dimensions
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

interface HelpTipProps {
  title: string;
  content: string;
  position?: 'top' | 'bottom' | 'left' | 'right';
  iconSize?: number;
  iconColor?: string;
}

const HelpTip: React.FC<HelpTipProps> = ({
  title,
  content,
  position = 'bottom',
  iconSize = 24,
  iconColor = '#8b5cf6'
}) => {
  const [visible, setVisible] = useState(false);
  const [animation] = useState(new Animated.Value(0));
  
  const showTip = () => {
    setVisible(true);
    Animated.timing(animation, {
      toValue: 1,
      duration: 300,
      useNativeDriver: true,
    }).start();
  };
  
  const hideTip = () => {
    Animated.timing(animation, {
      toValue: 0,
      duration: 200,
      useNativeDriver: true,
    }).start(() => setVisible(false));
  };
  
  const getModalPosition = () => {
    const { width, height } = Dimensions.get('window');
    
    switch (position) {
      case 'top':
        return {
          top: height * 0.1,
          left: width * 0.1,
          right: width * 0.1,
        };
      case 'bottom':
        return {
          bottom: height * 0.1,
          left: width * 0.1,
          right: width * 0.1,
        };
      case 'left':
        return {
          top: height * 0.3,
          left: width * 0.1,
          width: width * 0.4,
        };
      case 'right':
        return {
          top: height * 0.3,
          right: width * 0.1,
          width: width * 0.4,
        };
      default:
        return {
          bottom: height * 0.1,
          left: width * 0.1,
          right: width * 0.1,
        };
    }
  };
  
  const animatedStyle = {
    opacity: animation,
    transform: [
      {
        scale: animation.interpolate({
          inputRange: [0, 1],
          outputRange: [0.8, 1],
        }),
      },
    ],
  };
  
  return (
    <View>
      <TouchableOpacity onPress={showTip} style={styles.helpIcon}>
        <Ionicons name="help-circle" size={iconSize} color={iconColor} />
      </TouchableOpacity>
      
      <Modal
        transparent
        visible={visible}
        animationType="none"
        onRequestClose={hideTip}
      >
        <TouchableOpacity 
          style={styles.modalOverlay} 
          activeOpacity={1} 
          onPress={hideTip}
        >
          <Animated.View 
            style={[
              styles.tipContainer,
              getModalPosition(),
              animatedStyle,
            ]}
          >
            <View style={styles.tipHeader}>
              <Text style={styles.tipTitle}>{title}</Text>
              <TouchableOpacity onPress={hideTip}>
                <Ionicons name="close" size={24} color="#000" />
              </TouchableOpacity>
            </View>
            
            <ScrollView style={styles.tipContent}>
              <Text style={styles.tipText}>{content}</Text>
            </ScrollView>
          </Animated.View>
        </TouchableOpacity>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  helpIcon: {
    padding: 5,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  tipContainer: {
    backgroundColor: 'white',
    borderRadius: 10,
    padding: 15,
    maxHeight: 300,
    position: 'absolute',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  tipHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
    paddingBottom: 10,
  },
  tipTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  tipContent: {
    maxHeight: 200,
  },
  tipText: {
    fontSize: 16,
    color: '#555',
    lineHeight: 22,
  },
});

export default HelpTip;