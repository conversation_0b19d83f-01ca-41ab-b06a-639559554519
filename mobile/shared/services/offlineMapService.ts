import { Platform } from 'react-native';
import * as FileSystem from 'expo-file-system';
import NetInfo from '@react-native-community/netinfo';
import { Coordinates, Route } from './navigationService';

// Types for offline map service
export interface MapRegion {
  id: string;
  name: string;
  coordinates: {
    latitude: number;
    longitude: number;
    latitudeDelta: number;
    longitudeDelta: number;
  };
  size: number; // Size in MB
  downloaded: boolean;
  lastUpdated: Date | null;
}

export interface OfflineMapConfig {
  maxCacheSize: number; // MB
  autoDownloadRadius: number; // km
  downloadHighResolution: boolean;
  downloadPOIs: boolean;
  keepUpdated: boolean;
  updateFrequency: number; // days
}

// Mock implementation for offline map service
class OfflineMapService {
  private config: OfflineMapConfig = {
    maxCacheSize: 1000, // 1GB
    autoDownloadRadius: 50, // 50km
    downloadHighResolution: true,
    downloadPOIs: true,
    keepUpdated: true,
    updateFrequency: 30, // 30 days
  };
  private downloadedMaps: MapRegion[] = [];
  private isConnected: boolean = true;
  private connectionType: string | null = 'wifi';
  private connectionQuality: 'poor' | 'fair' | 'good' | 'excellent' | 'unknown' = 'good';
  private downloadQueue: MapRegion[] = [];
  private isDownloading: boolean = false;
  private totalCacheSize: number = 0; // MB

  constructor() {
    // Initialize network monitoring
    this.initNetworkMonitoring();
    
    // Initialize with some mock downloaded maps
    this.downloadedMaps = [
      {
        id: 'region-1',
        name: 'San Francisco Bay Area',
        coordinates: {
          latitude: 37.7749,
          longitude: -122.4194,
          latitudeDelta: 0.5,
          longitudeDelta: 0.5,
        },
        size: 120, // 120MB
        downloaded: true,
        lastUpdated: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000), // 15 days ago
      },
      {
        id: 'region-2',
        name: 'Sacramento Area',
        coordinates: {
          latitude: 38.5816,
          longitude: -121.4944,
          latitudeDelta: 0.5,
          longitudeDelta: 0.5,
        },
        size: 85, // 85MB
        downloaded: true,
        lastUpdated: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000), // 5 days ago
      },
    ];
    
    this.totalCacheSize = this.downloadedMaps.reduce((total, map) => total + map.size, 0);
  }

  /**
   * Initialize network monitoring
   */
  private initNetworkMonitoring(): void {
    // Subscribe to network state updates
    NetInfo.addEventListener(state => {
      this.isConnected = state.isConnected ?? false;
      this.connectionType = state.type;
      
      // Determine connection quality based on type and details
      if (!this.isConnected) {
        this.connectionQuality = 'poor';
      } else if (state.type === 'wifi') {
        this.connectionQuality = 'excellent';
      } else if (state.type === 'cellular') {
        const cellularGen = state.details?.cellularGeneration;
        if (cellularGen === '4g' || cellularGen === '5g') {
          this.connectionQuality = 'good';
        } else if (cellularGen === '3g') {
          this.connectionQuality = 'fair';
        } else {
          this.connectionQuality = 'poor';
        }
      } else {
        this.connectionQuality = 'unknown';
      }
      
      console.log(`Network status: ${this.isConnected ? 'Connected' : 'Disconnected'}, Type: ${this.connectionType}, Quality: ${this.connectionQuality}`);
      
      // If connection is restored, process download queue
      if (this.isConnected && this.downloadQueue.length > 0 && !this.isDownloading) {
        this.processDownloadQueue();
      }
    });
  }

  /**
   * Get current network status
   */
  getNetworkStatus(): { isConnected: boolean; connectionType: string | null; connectionQuality: string } {
    return {
      isConnected: this.isConnected,
      connectionType: this.connectionType,
      connectionQuality: this.connectionQuality,
    };
  }

  /**
   * Check if a route is available offline
   */
  isRouteAvailableOffline(route: Route): boolean {
    if (!route || !route.polyline || route.polyline.length === 0) {
      return false;
    }
    
    // Check if all points in the route are covered by downloaded maps
    return route.polyline.every(point => this.isPointCovered(point));
  }

  /**
   * Check if a point is covered by downloaded maps
   */
  private isPointCovered(point: Coordinates): boolean {
    return this.downloadedMaps.some(map => {
      const { latitude, longitude, latitudeDelta, longitudeDelta } = map.coordinates;
      const halfLat = latitudeDelta / 2;
      const halfLng = longitudeDelta / 2;
      
      return (
        point.latitude >= latitude - halfLat &&
        point.latitude <= latitude + halfLat &&
        point.longitude >= longitude - halfLng &&
        point.longitude <= longitude + halfLng
      );
    });
  }

  /**
   * Download map for a region
   */
  downloadMap(region: Omit<MapRegion, 'downloaded' | 'lastUpdated' | 'size'>): Promise<boolean> {
    return new Promise((resolve, reject) => {
      // Check if already downloaded
      const existingMap = this.downloadedMaps.find(map => map.id === region.id);
      if (existingMap && existingMap.downloaded) {
        console.log(`Map for ${region.name} already downloaded`);
        resolve(true);
        return;
      }
      
      // Check network status
      if (!this.isConnected) {
        console.log(`Cannot download map for ${region.name}: No network connection`);
        
        // Add to download queue
        this.downloadQueue.push({
          ...region,
          size: Math.floor(Math.random() * 100) + 50, // Random size between 50-150MB
          downloaded: false,
          lastUpdated: null,
        });
        
        reject(new Error('No network connection'));
        return;
      }
      
      // Simulate download
      console.log(`Downloading map for ${region.name}...`);
      
      setTimeout(() => {
        const mapSize = Math.floor(Math.random() * 100) + 50; // Random size between 50-150MB
        
        // Check if we have enough space
        if (this.totalCacheSize + mapSize > this.config.maxCacheSize) {
          // Need to free up space
          this.freeUpCacheSpace(mapSize);
        }
        
        // Add to downloaded maps
        const newMap: MapRegion = {
          ...region,
          size: mapSize,
          downloaded: true,
          lastUpdated: new Date(),
        };
        
        this.downloadedMaps.push(newMap);
        this.totalCacheSize += mapSize;
        
        console.log(`Map for ${region.name} downloaded successfully (${mapSize}MB)`);
        resolve(true);
      }, 2000); // Simulate 2 second download
    });
  }

  /**
   * Free up cache space
   */
  private freeUpCacheSpace(requiredSpace: number): void {
    console.log(`Freeing up ${requiredSpace}MB of cache space...`);
    
    // Sort maps by last updated (oldest first)
    const sortedMaps = [...this.downloadedMaps].sort((a, b) => {
      if (!a.lastUpdated) return -1;
      if (!b.lastUpdated) return 1;
      return a.lastUpdated.getTime() - b.lastUpdated.getTime();
    });
    
    let freedSpace = 0;
    const mapsToRemove: string[] = [];
    
    // Remove maps until we have enough space
    for (const map of sortedMaps) {
      if (freedSpace >= requiredSpace) break;
      
      freedSpace += map.size;
      mapsToRemove.push(map.id);
      console.log(`Removing map: ${map.name} (${map.size}MB)`);
    }
    
    // Remove the maps
    this.downloadedMaps = this.downloadedMaps.filter(map => !mapsToRemove.includes(map.id));
    this.totalCacheSize -= freedSpace;
    
    console.log(`Freed up ${freedSpace}MB of cache space`);
  }

  /**
   * Process download queue
   */
  private processDownloadQueue(): void {
    if (this.downloadQueue.length === 0 || this.isDownloading) {
      return;
    }
    
    this.isDownloading = true;
    const nextMap = this.downloadQueue.shift();
    
    if (!nextMap) {
      this.isDownloading = false;
      return;
    }
    
    console.log(`Processing download queue: ${nextMap.name}`);
    
    this.downloadMap(nextMap)
      .then(() => {
        console.log(`Queue download complete: ${nextMap.name}`);
        this.isDownloading = false;
        
        // Process next in queue
        if (this.downloadQueue.length > 0) {
          this.processDownloadQueue();
        }
      })
      .catch(error => {
        console.error(`Failed to download map from queue: ${error.message}`);
        this.isDownloading = false;
        
        // Put back in queue if it was a temporary error
        if (error.message !== 'No network connection') {
          this.downloadQueue.unshift(nextMap);
        }
      });
  }

  /**
   * Get downloaded maps
   */
  getDownloadedMaps(): MapRegion[] {
    return this.downloadedMaps;
  }

  /**
   * Get download queue
   */
  getDownloadQueue(): MapRegion[] {
    return this.downloadQueue;
  }

  /**
   * Get total cache size
   */
  getTotalCacheSize(): number {
    return this.totalCacheSize;
  }

  /**
   * Update configuration
   */
  updateConfig(config: Partial<OfflineMapConfig>): void {
    this.config = { ...this.config, ...config };
    console.log('Updated offline map config:', this.config);
  }

  /**
   * Clear all cached maps
   */
  clearCache(): Promise<boolean> {
    return new Promise((resolve) => {
      console.log('Clearing map cache...');
      
      setTimeout(() => {
        this.downloadedMaps = [];
        this.totalCacheSize = 0;
        console.log('Map cache cleared');
        resolve(true);
      }, 500);
    });
  }

  /**
   * Check for map updates
   */
  checkForUpdates(): Promise<{ updatesAvailable: boolean; mapsToUpdate: MapRegion[] }> {
    return new Promise((resolve) => {
      if (!this.isConnected) {
        resolve({ updatesAvailable: false, mapsToUpdate: [] });
        return;
      }
      
      console.log('Checking for map updates...');
      
      setTimeout(() => {
        // Find maps that need updates (older than updateFrequency days)
        const now = new Date();
        const mapsToUpdate = this.downloadedMaps.filter(map => {
          if (!map.lastUpdated) return false;
          
          const daysSinceUpdate = (now.getTime() - map.lastUpdated.getTime()) / (1000 * 60 * 60 * 24);
          return daysSinceUpdate > this.config.updateFrequency;
        });
        
        resolve({
          updatesAvailable: mapsToUpdate.length > 0,
          mapsToUpdate,
        });
      }, 1000);
    });
  }

  /**
   * Get offline routing for a route
   */
  getOfflineRoute(origin: Coordinates, destination: Coordinates): Promise<Route | null> {
    return new Promise((resolve) => {
      console.log('Getting offline route...');
      
      // Check if both points are covered by offline maps
      if (!this.isPointCovered(origin) || !this.isPointCovered(destination)) {
        console.log('Origin or destination not covered by offline maps');
        resolve(null);
        return;
      }
      
      // Simulate offline routing
      setTimeout(() => {
        // Create a simple straight line route between origin and destination
        const route: Route = {
          id: 'offline-route',
          summary: {
            distance: this.calculateDistance(origin, destination),
            duration: this.calculateDuration(origin, destination),
            restrictions: {
              height: null,
              weight: null,
              width: null,
            },
            warnings: ['Using offline navigation - limited accuracy']
          },
          steps: [
            {
              maneuver: {
                type: 'straight',
                instruction: 'Head towards destination (offline mode)',
                street: 'Unknown Road',
                distance: this.calculateDistance(origin, destination),
                duration: this.calculateDuration(origin, destination),
                coordinates: origin
              },
              distance: this.calculateDistance(origin, destination),
              duration: this.calculateDuration(origin, destination),
              polyline: [origin, destination]
            }
          ],
          polyline: [origin, destination]
        };
        
        console.log('Offline route generated');
        resolve(route);
      }, 500);
    });
  }

  /**
   * Calculate distance between two points (in meters)
   */
  private calculateDistance(point1: Coordinates, point2: Coordinates): number {
    const R = 6371e3; // Earth radius in meters
    const φ1 = (point1.latitude * Math.PI) / 180;
    const φ2 = (point2.latitude * Math.PI) / 180;
    const Δφ = ((point2.latitude - point1.latitude) * Math.PI) / 180;
    const Δλ = ((point2.longitude - point1.longitude) * Math.PI) / 180;
    
    const a =
      Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
      Math.cos(φ1) * Math.cos(φ2) * Math.sin(Δλ / 2) * Math.sin(Δλ / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    const distance = R * c;
    
    return distance;
  }

  /**
   * Calculate duration between two points (in seconds)
   * Assumes average speed of 50 km/h
   */
  private calculateDuration(point1: Coordinates, point2: Coordinates): number {
    const distance = this.calculateDistance(point1, point2);
    const speedMps = (50 * 1000) / 3600; // 50 km/h in m/s
    return distance / speedMps;
  }
}

export default new OfflineMapService();