import { Platform } from 'react-native';
import voiceCommandService from './voiceCommandService';
import offlineMapService from './offlineMapService';

// Types for the navigation service
export interface VehicleDimensions {
  height: number; // feet
  width: number; // feet
  length: number; // feet
  weight: number; // pounds
}

export interface RoutingPreferences {
  avoidTolls: boolean;
  avoidHighways: boolean;
  avoidFerries: boolean;
  preferFuelEfficient: boolean;
}

export interface NavigationConfig {
  vehicleType: 'car' | 'truck' | 'van' | 'semi';
  dimensions: VehicleDimensions;
  routingPreferences: RoutingPreferences;
}

export interface Coordinates {
  latitude: number;
  longitude: number;
}

export interface RouteOptions {
  alternatives: boolean;
  optimizeFor: 'time' | 'distance' | 'fuelEfficiency';
}

export interface Maneuver {
  type: 'turn-right' | 'turn-left' | 'straight' | 'merge' | 'exit' | 'roundabout' | 'arrive';
  instruction: string;
  street: string;
  distance: number; // meters
  duration: number; // seconds
  coordinates: Coordinates;
}

export interface Lane {
  directions: ('straight' | 'slight-left' | 'left' | 'sharp-left' | 'slight-right' | 'right' | 'sharp-right' | 'u-turn')[];
  recommended: boolean;
}

export interface RouteStep {
  maneuver: Maneuver;
  lanes?: Lane[];
  speedLimit?: number; // mph
  distance: number; // meters
  duration: number; // seconds
  polyline: Coordinates[];
}

export interface Route {
  id: string;
  summary: {
    distance: number; // meters
    duration: number; // seconds
    fuelUsage?: number; // gallons
    tolls?: number; // dollars
    restrictions?: {
      height: number | null; // feet
      weight: number | null; // pounds
      width: number | null; // feet
    };
    warnings?: string[]; // Warnings about route restrictions
  };
  steps: RouteStep[];
  polyline: Coordinates[];
}

// Mock data for initial development
const mockRoutes: Route[] = [
  {
    id: 'route-1',
    summary: {
      distance: 8046.72, // 5 miles in meters
      duration: 600, // 10 minutes in seconds
      fuelUsage: 0.5, // gallons
      tolls: 0, // dollars
      restrictions: {
        height: null, // No height restrictions
        weight: null, // No weight restrictions
        width: null, // No width restrictions
      },
    },
    steps: [
      {
        maneuver: {
          type: 'straight',
          instruction: 'Head north on Farm Road',
          street: 'Farm Road',
          distance: 500,
          duration: 60,
          coordinates: { latitude: 37.7800, longitude: -122.4300 }
        },
        speedLimit: 35,
        distance: 500,
        duration: 60,
        polyline: [
          { latitude: 37.7800, longitude: -122.4300 },
          { latitude: 37.7850, longitude: -122.4300 }
        ]
      },
      {
        maneuver: {
          type: 'turn-right',
          instruction: 'Turn right onto Main Street',
          street: 'Main Street',
          distance: 1000,
          duration: 120,
          coordinates: { latitude: 37.7850, longitude: -122.4300 }
        },
        lanes: [
          { directions: ['straight', 'right'], recommended: false },
          { directions: ['right'], recommended: true }
        ],
        speedLimit: 40,
        distance: 1000,
        duration: 120,
        polyline: [
          { latitude: 37.7850, longitude: -122.4300 },
          { latitude: 37.7850, longitude: -122.4200 }
        ]
      },
      {
        maneuver: {
          type: 'turn-left',
          instruction: 'Turn left onto Highway 101',
          street: 'Highway 101',
          distance: 5000,
          duration: 300,
          coordinates: { latitude: 37.7850, longitude: -122.4200 }
        },
        lanes: [
          { directions: ['left'], recommended: true },
          { directions: ['straight'], recommended: false },
          { directions: ['straight', 'right'], recommended: false }
        ],
        speedLimit: 65,
        distance: 5000,
        duration: 300,
        polyline: [
          { latitude: 37.7850, longitude: -122.4200 },
          { latitude: 37.7800, longitude: -122.4150 },
          { latitude: 37.7750, longitude: -122.4100 }
        ]
      },
      {
        maneuver: {
          type: 'exit',
          instruction: 'Take exit 12 for Delivery Road',
          street: 'Delivery Road',
          distance: 1000,
          duration: 60,
          coordinates: { latitude: 37.7750, longitude: -122.4100 }
        },
        speedLimit: 35,
        distance: 1000,
        duration: 60,
        polyline: [
          { latitude: 37.7750, longitude: -122.4100 },
          { latitude: 37.7749, longitude: -122.4194 }
        ]
      },
      {
        maneuver: {
          type: 'arrive',
          instruction: 'Arrive at destination',
          street: 'Delivery Road',
          distance: 0,
          duration: 0,
          coordinates: { latitude: 37.7749, longitude: -122.4194 }
        },
        distance: 0,
        duration: 0,
        polyline: [
          { latitude: 37.7749, longitude: -122.4194 }
        ]
      }
    ],
    polyline: [
      { latitude: 37.7800, longitude: -122.4300 },
      { latitude: 37.7790, longitude: -122.4280 },
      { latitude: 37.7770, longitude: -122.4250 },
      { latitude: 37.7760, longitude: -122.4230 },
      { latitude: 37.7749, longitude: -122.4194 }
    ]
  },
  {
    id: 'route-2',
    summary: {
      distance: 9656.06, // 6 miles in meters
      duration: 720, // 12 minutes in seconds
      fuelUsage: 0.4, // gallons
      tolls: 0, // dollars
      restrictions: {
        height: 12.5, // 12.5 feet height restriction
        weight: 20000, // 20000 pounds weight restriction
        width: 8.0, // 8.0 feet width restriction
      },
    },
    steps: [
      // Similar structure to route-1, but with different values
      // This would be an alternative route
    ],
    polyline: [
      // Alternative route polyline
    ]
  }
];

// Mock vehicle profiles
const defaultVehicleProfiles = {
  car: {
    dimensions: {
      height: 5.5, // feet
      width: 6.5, // feet
      length: 16, // feet
      weight: 4000 // pounds
    }
  },
  van: {
    dimensions: {
      height: 8, // feet
      width: 7, // feet
      length: 20, // feet
      weight: 8000 // pounds
    }
  },
  truck: {
    dimensions: {
      height: 13.5, // feet
      width: 8.5, // feet
      length: 33, // feet
      weight: 26000 // pounds
    }
  },
  semi: {
    dimensions: {
      height: 13.5, // feet
      width: 8.5, // feet
      length: 53, // feet
      weight: 80000 // pounds
    }
  }
};

// Navigation service implementation
class NavigationService {
  private config: NavigationConfig | null = null;
  private initialized: boolean = false;
  private apiKey: string | null = null;
  private activeRoute: Route | null = null;
  private activeStep: number = 0;
  private voiceGuidanceEnabled: boolean = true;
  private distanceUnit: 'miles' | 'kilometers' = 'miles';
  private mapMode: 'day' | 'night' | 'auto' = 'auto';

  /**
   * Initialize the navigation service with vehicle profile and preferences
   */
  async initialize(config: NavigationConfig): Promise<boolean> {
    try {
      this.config = config;
      this.initialized = true;

      // In a real implementation, this would initialize the navigation SDK
      // and set up the vehicle profile with the actual navigation provider

      console.log('Navigation service initialized with config:', config);
      return true;
    } catch (error) {
      console.error('Failed to initialize navigation service:', error);
      return false;
    }
  }

  /**
   * Set the API key for the navigation service
   */
  setApiKey(apiKey: string): void {
    this.apiKey = apiKey;
  }

  /**
   * Get optimized routes between origin and destination
   */
  async getRoutes(
    origin: Coordinates,
    destination: Coordinates,
    options: RouteOptions = { alternatives: true, optimizeFor: 'time' }
  ): Promise<Route[]> {
    if (!this.initialized || !this.config) {
      throw new Error('Navigation service not initialized. Call initialize() first.');
    }

    // Check network status
    const networkStatus = offlineMapService.getNetworkStatus();
    const isOfflineMode = !networkStatus.isConnected || networkStatus.connectionQuality === 'poor';

    // If offline or poor connection, try to use offline routing
    if (isOfflineMode) {
      console.log('Using offline routing due to poor or no connectivity');

      try {
        // Try to get an offline route
        const offlineRoute = await offlineMapService.getOfflineRoute(origin, destination);

        if (offlineRoute) {
          console.log('Offline route found');
          return [offlineRoute];
        } else {
          console.log('No offline route available for the requested path');

          // If we're completely offline with no route, return empty array
          if (!networkStatus.isConnected) {
            return [];
          }

          // If we have poor connection but are still online, fall back to online routing
          console.log('Falling back to online routing with poor connection');
        }
      } catch (error) {
        console.error('Error getting offline route:', error);

        // If completely offline with error, return empty array
        if (!networkStatus.isConnected) {
          return [];
        }

        // Otherwise fall back to online routing
        console.log('Falling back to online routing after offline error');
      }
    }

    // In a real implementation, this would call the navigation API
    // to get optimized routes based on the vehicle profile and preferences

    // For now, filter mock data based on vehicle dimensions
    return new Promise((resolve) => {
      setTimeout(() => {
        // Get vehicle dimensions from config
        const { height, width, weight } = this.config.dimensions;

        // Filter routes based on restrictions
        const filteredRoutes = mockRoutes.filter(route => {
          const restrictions = route.summary.restrictions;

          // If no restrictions, the route is valid
          if (!restrictions) return true;

          // Check if vehicle exceeds any restrictions
          const heightExceeded = restrictions.height !== null && height > restrictions.height;
          const widthExceeded = restrictions.width !== null && width > restrictions.width;
          const weightExceeded = restrictions.weight !== null && weight > restrictions.weight;

          // Route is valid if no restrictions are exceeded
          return !heightExceeded && !widthExceeded && !weightExceeded;
        });

        // Add warnings for routes that are close to restrictions
        const routesWithWarnings = filteredRoutes.map(route => {
          const restrictions = route.summary.restrictions;
          if (!restrictions) return route;

          const warnings: string[] = [];

          // Check if vehicle is close to restrictions (within 10%)
          if (restrictions.height !== null && height > restrictions.height * 0.9) {
            warnings.push(`Height clearance limited (${restrictions.height} ft)`);
          }

          if (restrictions.width !== null && width > restrictions.width * 0.9) {
            warnings.push(`Width clearance limited (${restrictions.width} ft)`);
          }

          if (restrictions.weight !== null && weight > restrictions.weight * 0.9) {
            warnings.push(`Weight limit approaching (${restrictions.weight} lbs)`);
          }

          // Add connectivity warning if connection is poor
          if (networkStatus.connectionQuality === 'poor' || networkStatus.connectionQuality === 'fair') {
            warnings.push('Limited connectivity - navigation may be less accurate');
          }

          // Add warnings to route summary
          return {
            ...route,
            summary: {
              ...route.summary,
              warnings: warnings.length > 0 ? warnings : undefined
            }
          };
        });

        // Check if any routes are available offline and add that information
        const routesWithOfflineInfo = routesWithWarnings.map(route => {
          const isAvailableOffline = offlineMapService.isRouteAvailableOffline(route);

          return {
            ...route,
            summary: {
              ...route.summary,
              isAvailableOffline,
              warnings: [
                ...(route.summary.warnings || []),
                ...(isAvailableOffline ? [] : ['Route not available offline'])
              ]
            }
          };
        });

        resolve(routesWithOfflineInfo);
      }, 1000); // Simulate API delay
    });
  }

  /**
   * Start navigation along a selected route
   */
  startNavigation(routeId: string): boolean {
    const route = mockRoutes.find(r => r.id === routeId);
    if (!route) {
      console.error(`Route with id ${routeId} not found`);
      return false;
    }

    this.activeRoute = route;
    this.activeStep = 0;

    // In a real implementation, this would start the turn-by-turn navigation
    // with the selected route using the navigation SDK

    console.log(`Started navigation with route ${routeId}`);
    return true;
  }

  /**
   * Get the current navigation state
   */
  getNavigationState() {
    if (!this.activeRoute) {
      return null;
    }

    return {
      route: this.activeRoute,
      currentStep: this.activeStep,
      currentManeuver: this.activeRoute.steps[this.activeStep].maneuver,
      nextManeuver: this.activeStep < this.activeRoute.steps.length - 1 
        ? this.activeRoute.steps[this.activeStep + 1].maneuver 
        : null,
      remainingDistance: this.calculateRemainingDistance(),
      remainingDuration: this.calculateRemainingDuration(),
      eta: this.calculateETA()
    };
  }

  /**
   * Update the current navigation state based on location
   */
  updateNavigation(currentLocation: Coordinates): void {
    if (!this.activeRoute || this.activeStep >= this.activeRoute.steps.length) {
      return;
    }

    // In a real implementation, this would update the navigation state
    // based on the current location, checking if we've passed maneuvers
    // and updating the active step accordingly

    // For mock purposes, just advance to the next step occasionally
    if (Math.random() > 0.8) {
      this.activeStep = Math.min(this.activeStep + 1, this.activeRoute.steps.length - 1);
    }
  }

  /**
   * End the current navigation session
   */
  endNavigation(): void {
    this.activeRoute = null;
    this.activeStep = 0;

    // In a real implementation, this would stop the turn-by-turn navigation
    // and clean up any resources

    console.log('Navigation ended');
  }

  /**
   * Toggle voice guidance
   */
  toggleVoiceGuidance(enabled: boolean): void {
    this.voiceGuidanceEnabled = enabled;

    // Enable/disable voice commands based on voice guidance setting
    voiceCommandService.setEnabled(enabled);

    if (enabled) {
      // Initialize voice command service if it's being enabled
      this.initializeVoiceCommands();
    } else {
      // Stop listening for voice commands if it's being disabled
      voiceCommandService.stopListening();
    }

    console.log(`Voice guidance ${enabled ? 'enabled' : 'disabled'}`);
  }

  /**
   * Initialize voice commands for navigation
   */
  private initializeVoiceCommands(): void {
    // Initialize voice command service
    voiceCommandService.initialize({
      enabled: true,
      listenTimeout: 5000,
      confidenceThreshold: 0.7,
      language: 'en-US',
    });

    // Register navigation-related voice commands
    voiceCommandService.registerCommands([
      {
        command: 'start navigation',
        action: () => {
          if (this.activeRoute) {
            voiceCommandService.speakFeedback('Navigation already in progress');
          } else {
            voiceCommandService.speakFeedback('Please select a route first');
          }
        },
        aliases: ['begin navigation', 'navigate', 'start driving']
      },
      {
        command: 'end navigation',
        action: () => {
          if (this.activeRoute) {
            this.endNavigation();
            voiceCommandService.speakFeedback('Navigation ended');
          } else {
            voiceCommandService.speakFeedback('No active navigation to end');
          }
        },
        aliases: ['stop navigation', 'cancel navigation', 'exit navigation']
      },
      {
        command: 'next instruction',
        action: () => {
          if (this.activeRoute && this.activeStep < this.activeRoute.steps.length - 1) {
            this.activeStep++;
            const instruction = this.activeRoute.steps[this.activeStep].maneuver.instruction;
            voiceCommandService.speakInstruction(instruction);
          } else {
            voiceCommandService.speakFeedback('No more instructions available');
          }
        },
        aliases: ['next direction', 'what\'s next']
      },
      {
        command: 'repeat instruction',
        action: () => {
          if (this.activeRoute) {
            const instruction = this.activeRoute.steps[this.activeStep].maneuver.instruction;
            voiceCommandService.speakInstruction(instruction);
          } else {
            voiceCommandService.speakFeedback('No active navigation');
          }
        },
        aliases: ['repeat that', 'say again', 'what was that']
      },
      {
        command: 'how far',
        action: () => {
          if (this.activeRoute) {
            const remainingDistance = this.calculateRemainingDistance();
            const miles = (remainingDistance / 1609.34).toFixed(1);
            voiceCommandService.speakFeedback(`${miles} miles remaining to destination`);
          } else {
            voiceCommandService.speakFeedback('No active navigation');
          }
        },
        aliases: ['distance remaining', 'how much further', 'how far to destination']
      },
      {
        command: 'arrival time',
        action: () => {
          if (this.activeRoute) {
            const eta = this.calculateETA();
            const timeString = eta.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
            voiceCommandService.speakFeedback(`Estimated arrival time is ${timeString}`);
          } else {
            voiceCommandService.speakFeedback('No active navigation');
          }
        },
        aliases: ['when will I arrive', 'what time will I arrive', 'ETA']
      },
      {
        command: 'switch to miles',
        action: () => {
          this.setDistanceUnit('miles');
          voiceCommandService.speakFeedback('Switched to miles');
        },
        aliases: ['use miles', 'change to miles']
      },
      {
        command: 'switch to kilometers',
        action: () => {
          this.setDistanceUnit('kilometers');
          voiceCommandService.speakFeedback('Switched to kilometers');
        },
        aliases: ['use kilometers', 'change to kilometers']
      }
    ]);

    // Start listening for voice commands
    voiceCommandService.startListening();
  }

  /**
   * Set distance unit preference
   */
  setDistanceUnit(unit: 'miles' | 'kilometers'): void {
    this.distanceUnit = unit;

    // In a real implementation, this would update the distance unit
    // in the navigation SDK

    console.log(`Distance unit set to ${unit}`);
  }

  /**
   * Set map mode
   */
  setMapMode(mode: 'day' | 'night' | 'auto'): void {
    this.mapMode = mode;

    // In a real implementation, this would update the map mode
    // in the navigation SDK

    console.log(`Map mode set to ${mode}`);
  }

  /**
   * Get default vehicle profile
   */
  getDefaultVehicleProfile(type: 'car' | 'truck' | 'van' | 'semi'): VehicleDimensions {
    return defaultVehicleProfiles[type].dimensions;
  }

  /**
   * Helper method to calculate remaining distance
   */
  private calculateRemainingDistance(): number {
    if (!this.activeRoute || this.activeStep >= this.activeRoute.steps.length) {
      return 0;
    }

    let distance = 0;
    for (let i = this.activeStep; i < this.activeRoute.steps.length; i++) {
      distance += this.activeRoute.steps[i].distance;
    }
    return distance;
  }

  /**
   * Helper method to calculate remaining duration
   */
  private calculateRemainingDuration(): number {
    if (!this.activeRoute || this.activeStep >= this.activeRoute.steps.length) {
      return 0;
    }

    let duration = 0;
    for (let i = this.activeStep; i < this.activeRoute.steps.length; i++) {
      duration += this.activeRoute.steps[i].duration;
    }
    return duration;
  }

  /**
   * Helper method to calculate ETA
   */
  private calculateETA(): Date {
    const now = new Date();
    const remainingDuration = this.calculateRemainingDuration();
    return new Date(now.getTime() + remainingDuration * 1000);
  }
}

export default new NavigationService();
