import * as Notifications from 'expo-notifications';

/**
 * Add a listener for received notifications
 * @param callback Function to call when a notification is received
 * @returns Subscription that can be used to remove the listener
 */
export const addNotificationReceivedListener = (
  callback: (notification: Notifications.Notification) => void
): Notifications.Subscription => {
  return Notifications.addNotificationReceivedListener(callback);
};

/**
 * Add a listener for notification responses (when user taps a notification)
 * @param callback Function to call when a notification response is received
 * @returns Subscription that can be used to remove the listener
 */
export const addNotificationResponseReceivedListener = (
  callback: (response: Notifications.NotificationResponse) => void
): Notifications.Subscription => {
  return Notifications.addNotificationResponseReceivedListener(callback);
};

/**
 * Schedule a local notification
 * @param content Content of the notification
 * @param trigger When to show the notification
 * @returns Promise that resolves to the notification identifier
 */
export const scheduleNotification = async (
  content: Notifications.NotificationContentInput,
  trigger?: Notifications.NotificationTriggerInput
): Promise<string> => {
  return await Notifications.scheduleNotificationAsync({
    content,
    trigger,
  });
};

/**
 * Cancel a scheduled notification
 * @param identifier Identifier of the notification to cancel
 */
export const cancelNotification = async (identifier: string): Promise<void> => {
  await Notifications.cancelScheduledNotificationAsync(identifier);
};

/**
 * Cancel all scheduled notifications
 */
export const cancelAllNotifications = async (): Promise<void> => {
  await Notifications.cancelAllScheduledNotificationsAsync();
};

/**
 * Get all scheduled notifications
 * @returns Promise that resolves to an array of scheduled notifications
 */
export const getAllScheduledNotifications = async (): Promise<Notifications.NotificationRequest[]> => {
  return await Notifications.getAllScheduledNotificationsAsync();
};

/**
 * Dismiss all delivered notifications
 */
export const dismissAllNotifications = async (): Promise<void> => {
  await Notifications.dismissAllNotificationsAsync();
};

/**
 * Get all delivered notifications
 * @returns Promise that resolves to an array of delivered notifications
 */
export const getDeliveredNotifications = async (): Promise<Notifications.NotificationRequest[]> => {
  return await Notifications.getPresentedNotificationsAsync();
};

/**
 * Set notification badge count
 * @param count Number to display on the app icon badge
 */
export const setBadgeCount = async (count: number): Promise<void> => {
  await Notifications.setBadgeCountAsync(count);
};

/**
 * Get current notification badge count
 * @returns Promise that resolves to the current badge count
 */
export const getBadgeCount = async (): Promise<number> => {
  return await Notifications.getBadgeCountAsync();
};