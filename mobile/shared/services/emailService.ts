import * as Linking from 'expo-linking';

/**
 * Interface for email message
 */
export interface EmailMessage {
  to: string;
  subject: string;
  body: string;
  cc?: string;
  bcc?: string;
}

/**
 * Send an email
 * @param message Email message to send
 * @returns Promise that resolves to true if the email app was opened successfully
 */
export const sendEmail = async (message: EmailMessage): Promise<boolean> => {
  try {
    // Format the mailto URI
    let mailtoUri = `mailto:${message.to}?subject=${encodeURIComponent(message.subject)}&body=${encodeURIComponent(message.body)}`;
    
    // Add CC and BCC if provided
    if (message.cc) {
      mailtoUri += `&cc=${encodeURIComponent(message.cc)}`;
    }
    
    if (message.bcc) {
      mailtoUri += `&bcc=${encodeURIComponent(message.bcc)}`;
    }
    
    // Open the email app with the pre-filled message
    const supported = await Linking.canOpenURL(mailtoUri);
    
    if (supported) {
      await Linking.openURL(mailtoUri);
      return true;
    } else {
      console.error('Mailto URI not supported on this device');
      return false;
    }
  } catch (error) {
    console.error('Error sending email:', error);
    return false;
  }
};

/**
 * In a production app, this would be replaced with a call to a backend API
 * that would handle the actual sending of emails through a service like SendGrid or AWS SES.
 * 
 * For now, this is a mock implementation that logs the message and returns success.
 * 
 * @param message Email message to send
 * @returns Promise that resolves to true if the message was sent successfully
 */
export const sendEmailViaAPI = async (message: EmailMessage): Promise<boolean> => {
  try {
    // In a real app, this would make an API call to a backend service
    console.log(`[Email API] Sending email to ${message.to}`);
    console.log(`Subject: ${message.subject}`);
    console.log(`Body: ${message.body}`);
    
    if (message.cc) {
      console.log(`CC: ${message.cc}`);
    }
    
    if (message.bcc) {
      console.log(`BCC: ${message.bcc}`);
    }
    
    // Simulate API call success
    return true;
  } catch (error) {
    console.error('Error sending email via API:', error);
    return false;
  }
};