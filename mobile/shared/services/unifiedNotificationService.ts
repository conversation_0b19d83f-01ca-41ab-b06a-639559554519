import * as Notifications from 'expo-notifications';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as notificationService from './notificationService';

// Define notification types for different apps
export enum NotificationType {
  TASK_ASSIGNMENT = 'TASK_ASSIGNMENT',
  DUE_DATE_ALERT = 'DUE_DATE_ALERT',
  STATUS_CHANGE = 'STATUS_CHANGE',
  SYSTEM_ANNOUNCEMENT = 'SYSTEM_ANNOUNCEMENT',
  DELIVERY_UPDATE = 'DELIVERY_UPDATE',
  INVENTORY_ALERT = 'INVENTORY_ALERT',
  MAINTENANCE_REMINDER = 'MAINTENANCE_REMINDER',
  FINANCIAL_ALERT = 'FINANCIAL_ALERT',
  WEATHER_ALERT = 'WEATHER_ALERT',
  MARKETPLACE_UPDATE = 'MARKETPLACE_UPDATE',
  EMPLOYEE_UPDATE = 'EMPLOYEE_UPDATE',
  FIELD_ALERT = 'FIELD_ALERT',
}

// Define app identifiers
export enum AppIdentifier {
  FIELD_OPERATIONS = 'field-operations',
  FARM_MANAGER = 'farm-manager',
  INVENTORY_EQUIPMENT = 'inventory-equipment',
  FINANCIAL_MANAGER = 'financial-manager',
  EMPLOYEE = 'employee',
  MARKETPLACE = 'marketplace',
  DRIVER = 'driver',
  DRIVE_TRACKER = 'drive-tracker',
}

// Define notification data structure
export interface UnifiedNotification {
  id: string;
  type: NotificationType;
  title: string;
  body: string;
  data?: Record<string, any>;
  sourceApp: AppIdentifier;
  targetApp?: AppIdentifier;
  timestamp: number;
  read: boolean;
  actionable: boolean;
  action?: string;
  deepLink?: string;
  priority: 'high' | 'default' | 'low';
}

// Define notification storage
const NOTIFICATION_STORAGE_KEY = 'unifiedNotifications';
const MAX_NOTIFICATIONS = 100;

/**
 * Send a notification that can be received by any app
 * @param notification The notification to send
 * @returns Promise that resolves to the notification identifier
 */
export const sendUnifiedNotification = async (
  notification: Omit<UnifiedNotification, 'id' | 'timestamp' | 'read'>
): Promise<string> => {
  // Generate a unique ID
  const id = `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  
  // Create the full notification object
  const fullNotification: UnifiedNotification = {
    ...notification,
    id,
    timestamp: Date.now(),
    read: false,
  };
  
  // Store the notification
  await storeNotification(fullNotification);
  
  // Schedule a local notification if the app is the target or if no target is specified
  const notificationId = await notificationService.scheduleNotification({
    title: notification.title,
    body: notification.body,
    data: {
      ...notification.data,
      id,
      type: notification.type,
      sourceApp: notification.sourceApp,
      targetApp: notification.targetApp,
      actionable: notification.actionable,
      action: notification.action,
      deepLink: notification.deepLink,
    },
  });
  
  return id;
};

/**
 * Get all notifications for the current app
 * @param appIdentifier The app identifier to filter by (optional)
 * @returns Promise that resolves to an array of notifications
 */
export const getUnifiedNotifications = async (
  appIdentifier?: AppIdentifier
): Promise<UnifiedNotification[]> => {
  try {
    const storedNotifications = await AsyncStorage.getItem(NOTIFICATION_STORAGE_KEY);
    if (!storedNotifications) {
      return [];
    }
    
    const notifications: UnifiedNotification[] = JSON.parse(storedNotifications);
    
    // Filter by app identifier if provided
    if (appIdentifier) {
      return notifications.filter(
        (notification) => 
          notification.sourceApp === appIdentifier || 
          notification.targetApp === appIdentifier || 
          !notification.targetApp
      );
    }
    
    return notifications;
  } catch (error) {
    console.error('Error getting unified notifications:', error);
    return [];
  }
};

/**
 * Mark a notification as read
 * @param id The notification ID
 */
export const markNotificationAsRead = async (id: string): Promise<void> => {
  try {
    const notifications = await getUnifiedNotifications();
    const updatedNotifications = notifications.map((notification) => {
      if (notification.id === id) {
        return { ...notification, read: true };
      }
      return notification;
    });
    
    await AsyncStorage.setItem(
      NOTIFICATION_STORAGE_KEY,
      JSON.stringify(updatedNotifications)
    );
  } catch (error) {
    console.error('Error marking notification as read:', error);
  }
};

/**
 * Delete a notification
 * @param id The notification ID
 */
export const deleteNotification = async (id: string): Promise<void> => {
  try {
    const notifications = await getUnifiedNotifications();
    const updatedNotifications = notifications.filter(
      (notification) => notification.id !== id
    );
    
    await AsyncStorage.setItem(
      NOTIFICATION_STORAGE_KEY,
      JSON.stringify(updatedNotifications)
    );
  } catch (error) {
    console.error('Error deleting notification:', error);
  }
};

/**
 * Clear all notifications
 * @param appIdentifier The app identifier to filter by (optional)
 */
export const clearAllNotifications = async (
  appIdentifier?: AppIdentifier
): Promise<void> => {
  try {
    if (appIdentifier) {
      const notifications = await getUnifiedNotifications();
      const updatedNotifications = notifications.filter(
        (notification) => 
          notification.sourceApp !== appIdentifier && 
          notification.targetApp !== appIdentifier
      );
      
      await AsyncStorage.setItem(
        NOTIFICATION_STORAGE_KEY,
        JSON.stringify(updatedNotifications)
      );
    } else {
      await AsyncStorage.removeItem(NOTIFICATION_STORAGE_KEY);
    }
  } catch (error) {
    console.error('Error clearing notifications:', error);
  }
};

/**
 * Get unread notification count
 * @param appIdentifier The app identifier to filter by (optional)
 * @returns Promise that resolves to the unread count
 */
export const getUnreadNotificationCount = async (
  appIdentifier?: AppIdentifier
): Promise<number> => {
  const notifications = await getUnifiedNotifications(appIdentifier);
  return notifications.filter((notification) => !notification.read).length;
};

/**
 * Store a notification in AsyncStorage
 * @param notification The notification to store
 */
const storeNotification = async (
  notification: UnifiedNotification
): Promise<void> => {
  try {
    const notifications = await getUnifiedNotifications();
    
    // Add the new notification at the beginning
    const updatedNotifications = [notification, ...notifications];
    
    // Limit the number of stored notifications
    const limitedNotifications = updatedNotifications.slice(0, MAX_NOTIFICATIONS);
    
    await AsyncStorage.setItem(
      NOTIFICATION_STORAGE_KEY,
      JSON.stringify(limitedNotifications)
    );
  } catch (error) {
    console.error('Error storing notification:', error);
  }
};

/**
 * Initialize the unified notification system
 * Sets up listeners and handlers
 */
export const initializeUnifiedNotificationSystem = async (): Promise<void> => {
  // Set up notification received listener
  notificationService.addNotificationReceivedListener((notification) => {
    // Handle incoming notification
    console.log('Received notification:', notification);
    
    // Update badge count
    updateBadgeCount();
  });
  
  // Set up notification response listener
  notificationService.addNotificationResponseReceivedListener((response) => {
    // Handle notification response (when user taps notification)
    const data = response.notification.request.content.data as any;
    
    // Mark as read
    if (data.id) {
      markNotificationAsRead(data.id);
    }
    
    // Handle deep link if present
    if (data.deepLink) {
      // In a real implementation, this would use a navigation library
      console.log('Deep link to:', data.deepLink);
    }
    
    // Update badge count
    updateBadgeCount();
  });
  
  // Initial badge count update
  updateBadgeCount();
};

/**
 * Update the app badge count based on unread notifications
 */
const updateBadgeCount = async (): Promise<void> => {
  const unreadCount = await getUnreadNotificationCount();
  await notificationService.setBadgeCount(unreadCount);
};