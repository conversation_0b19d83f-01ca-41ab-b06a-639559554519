import navigationService from './navigationService';
import * as notificationService from './notificationService';
import * as smsService from './smsService';
import * as emailService from './emailService';

// Types for the ETA update service
export interface Customer {
  id: string;
  name: string;
  phone: string;
  email: string;
  notificationPreferences: {
    email: boolean;
    sms: boolean;
    push: boolean;
    notifyOnStart: boolean;
    notifyOnDelay: boolean;
    notifyOnApproaching: boolean; // When driver is X minutes away
    notifyOnArrival: boolean;
    approachingThreshold: number; // Minutes before arrival to notify
  };
}

export interface ETAUpdate {
  deliveryId: string;
  customerId: string;
  originalETA: Date;
  currentETA: Date;
  status: 'on-time' | 'delayed' | 'early';
  delayMinutes?: number;
  earlyMinutes?: number;
  notificationSent: boolean;
  notificationType?: 'start' | 'delay' | 'approaching' | 'arrival';
  sentAt?: Date;
}

// Mock data for initial development
const mockCustomers: Record<string, Customer> = {
  'cust-001': {
    id: 'cust-001',
    name: 'Green Valley Farm',
    phone: '+15551234567',
    email: '<EMAIL>',
    notificationPreferences: {
      email: true,
      sms: true,
      push: true,
      notifyOnStart: true,
      notifyOnDelay: true,
      notifyOnApproaching: true,
      notifyOnArrival: true,
      approachingThreshold: 15, // Notify when 15 minutes away
    },
  },
  'cust-002': {
    id: 'cust-002',
    name: 'Sunrise Orchards',
    phone: '+15559876543',
    email: '<EMAIL>',
    notificationPreferences: {
      email: true,
      sms: false,
      push: true,
      notifyOnStart: false,
      notifyOnDelay: true,
      notifyOnApproaching: true,
      notifyOnArrival: true,
      approachingThreshold: 10, // Notify when 10 minutes away
    },
  },
  'cust-003': {
    id: 'cust-003',
    name: 'Blue Sky Dairy',
    phone: '+15551112222',
    email: '<EMAIL>',
    notificationPreferences: {
      email: true,
      sms: true,
      push: false,
      notifyOnStart: true,
      notifyOnDelay: true,
      notifyOnApproaching: false,
      notifyOnArrival: true,
      approachingThreshold: 20, // Notify when 20 minutes away
    },
  },
};

// ETA update service implementation
class ETAUpdateService {
  private etaUpdates: Record<string, ETAUpdate> = {};
  private updateInterval: NodeJS.Timeout | null = null;
  private updateFrequency: number = 60000; // Update every minute
  private delayThreshold: number = 5; // Minutes of delay before notification
  private activeDeliveryId: string | null = null;
  private activeCustomerId: string | null = null;

  /**
   * Start ETA updates for a delivery
   * @param deliveryId ID of the delivery
   * @param customerId ID of the customer
   * @returns Promise that resolves to true if successful
   */
  async startETAUpdates(deliveryId: string, customerId: string): Promise<boolean> {
    try {
      // Check if customer exists
      if (!mockCustomers[customerId]) {
        console.error(`Customer with ID ${customerId} not found`);
        return false;
      }

      // Get initial ETA from navigation service
      const navigationState = navigationService.getNavigationState();
      if (!navigationState) {
        console.error('No active navigation session');
        return false;
      }

      const originalETA = navigationState.eta;

      // Create ETA update record
      this.etaUpdates[deliveryId] = {
        deliveryId,
        customerId,
        originalETA,
        currentETA: originalETA,
        status: 'on-time',
        notificationSent: false,
      };

      this.activeDeliveryId = deliveryId;
      this.activeCustomerId = customerId;

      // Send initial notification if customer prefers
      const customer = mockCustomers[customerId];
      if (customer.notificationPreferences.notifyOnStart) {
        await this.sendETANotification(deliveryId, 'start');
      }

      // Start update interval
      this.startUpdateInterval();

      console.log(`Started ETA updates for delivery ${deliveryId} to customer ${customerId}`);
      return true;
    } catch (error) {
      console.error('Failed to start ETA updates:', error);
      return false;
    }
  }

  /**
   * Stop ETA updates for a delivery
   * @param deliveryId ID of the delivery
   * @returns Promise that resolves to true if successful
   */
  async stopETAUpdates(deliveryId: string): Promise<boolean> {
    try {
      // Check if delivery exists
      if (!this.etaUpdates[deliveryId]) {
        console.error(`Delivery with ID ${deliveryId} not found`);
        return false;
      }

      // Clear update interval
      if (this.updateInterval) {
        clearInterval(this.updateInterval);
        this.updateInterval = null;
      }

      // Send arrival notification if customer prefers
      const customerId = this.etaUpdates[deliveryId].customerId;
      const customer = mockCustomers[customerId];
      if (customer.notificationPreferences.notifyOnArrival) {
        await this.sendETANotification(deliveryId, 'arrival');
      }

      // Clean up
      this.activeDeliveryId = null;
      this.activeCustomerId = null;

      console.log(`Stopped ETA updates for delivery ${deliveryId}`);
      return true;
    } catch (error) {
      console.error('Failed to stop ETA updates:', error);
      return false;
    }
  }

  /**
   * Get current ETA update for a delivery
   * @param deliveryId ID of the delivery
   * @returns ETA update object or null if not found
   */
  getETAUpdate(deliveryId: string): ETAUpdate | null {
    return this.etaUpdates[deliveryId] || null;
  }

  /**
   * Get customer information
   * @param customerId ID of the customer
   * @returns Customer object or null if not found
   */
  getCustomer(customerId: string): Customer | null {
    return mockCustomers[customerId] || null;
  }

  /**
   * Set update frequency
   * @param frequency Update frequency in milliseconds
   */
  setUpdateFrequency(frequency: number): void {
    this.updateFrequency = frequency;

    // Restart interval if active
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
      this.startUpdateInterval();
    }
  }

  /**
   * Set delay threshold for notifications
   * @param minutes Minutes of delay before notification
   */
  setDelayThreshold(minutes: number): void {
    this.delayThreshold = minutes;
  }

  /**
   * Start the update interval
   */
  private startUpdateInterval(): void {
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
    }

    this.updateInterval = setInterval(() => {
      this.updateETA();
    }, this.updateFrequency);
  }

  /**
   * Update ETA based on current navigation state
   */
  private updateETA(): void {
    if (!this.activeDeliveryId || !this.activeCustomerId) return;

    const navigationState = navigationService.getNavigationState();
    if (!navigationState) return;

    const deliveryId = this.activeDeliveryId;
    const customerId = this.activeCustomerId;
    const etaUpdate = this.etaUpdates[deliveryId];
    const customer = mockCustomers[customerId];

    if (!etaUpdate || !customer) return;

    const newETA = navigationState.eta;
    const originalETA = etaUpdate.originalETA;

    // Calculate time difference in minutes
    const diffMinutes = Math.round((newETA.getTime() - originalETA.getTime()) / 60000);

    // Update status
    let status: 'on-time' | 'delayed' | 'early' = 'on-time';
    if (diffMinutes >= this.delayThreshold) {
      status = 'delayed';
    } else if (diffMinutes <= -this.delayThreshold) {
      status = 'early';
    }

    // Update ETA record
    this.etaUpdates[deliveryId] = {
      ...etaUpdate,
      currentETA: newETA,
      status,
      delayMinutes: status === 'delayed' ? diffMinutes : undefined,
      earlyMinutes: status === 'early' ? -diffMinutes : undefined,
    };

    // Check if we need to send notifications
    this.checkNotifications(deliveryId);
  }

  /**
   * Check if notifications need to be sent
   * @param deliveryId ID of the delivery
   */
  private async checkNotifications(deliveryId: string): Promise<void> {
    const etaUpdate = this.etaUpdates[deliveryId];
    if (!etaUpdate) return;

    const customer = mockCustomers[etaUpdate.customerId];
    if (!customer) return;

    // Check for delay notification
    if (
      etaUpdate.status === 'delayed' &&
      customer.notificationPreferences.notifyOnDelay &&
      (!etaUpdate.notificationSent || etaUpdate.notificationType !== 'delay')
    ) {
      await this.sendETANotification(deliveryId, 'delay');
    }

    // Check for approaching notification
    if (customer.notificationPreferences.notifyOnApproaching) {
      const navigationState = navigationService.getNavigationState();
      if (!navigationState) return;

      // Calculate minutes until arrival
      const minutesUntilArrival = Math.round(navigationState.remainingDuration / 60);

      if (
        minutesUntilArrival <= customer.notificationPreferences.approachingThreshold &&
        (!etaUpdate.notificationSent || etaUpdate.notificationType !== 'approaching')
      ) {
        await this.sendETANotification(deliveryId, 'approaching');
      }
    }
  }

  /**
   * Send ETA notification to customer
   * @param deliveryId ID of the delivery
   * @param type Type of notification
   */
  private async sendETANotification(
    deliveryId: string,
    type: 'start' | 'delay' | 'approaching' | 'arrival'
  ): Promise<void> {
    const etaUpdate = this.etaUpdates[deliveryId];
    if (!etaUpdate) return;

    const customer = mockCustomers[etaUpdate.customerId];
    if (!customer) return;

    // Prepare notification content
    let title = '';
    let body = '';
    const data = {
      deliveryId,
      customerId: customer.id,
      type,
      eta: etaUpdate.currentETA.toISOString(),
    };

    switch (type) {
      case 'start':
        title = 'Delivery Started';
        body = `Your delivery from NxtAcre is on the way. Expected arrival: ${formatTime(etaUpdate.currentETA)}`;
        break;
      case 'delay':
        title = 'Delivery Delayed';
        body = `Your delivery is delayed by ${etaUpdate.delayMinutes} minutes. New ETA: ${formatTime(etaUpdate.currentETA)}`;
        break;
      case 'approaching':
        const navigationState = navigationService.getNavigationState();
        if (!navigationState) return;
        const minutesAway = Math.round(navigationState.remainingDuration / 60);
        title = 'Delivery Approaching';
        body = `Your delivery is ${minutesAway} minutes away. Expected arrival: ${formatTime(etaUpdate.currentETA)}`;
        break;
      case 'arrival':
        title = 'Delivery Arrived';
        body = 'Your delivery has arrived at the destination.';
        break;
    }

    // Send push notification if enabled
    if (customer.notificationPreferences.push) {
      try {
        await notificationService.scheduleNotification(
          {
            title,
            body,
            data,
          },
          null // Send immediately
        );
      } catch (error) {
        console.error('Failed to send push notification:', error);
      }
    }

    // Send SMS notification if enabled
    if (customer.notificationPreferences.sms) {
      try {
        await smsService.sendSMSViaAPI({
          to: customer.phone,
          body: `${title} - ${body}`
        });
        console.log(`SMS notification sent to ${customer.phone}`);
      } catch (error) {
        console.error('Failed to send SMS notification:', error);
      }
    }

    // Send email notification if enabled
    if (customer.notificationPreferences.email) {
      try {
        await emailService.sendEmailViaAPI({
          to: customer.email,
          subject: title,
          body: body
        });
        console.log(`Email notification sent to ${customer.email}`);
      } catch (error) {
        console.error('Failed to send email notification:', error);
      }
    }

    // Update notification status
    this.etaUpdates[deliveryId] = {
      ...etaUpdate,
      notificationSent: true,
      notificationType: type,
      sentAt: new Date(),
    };

    console.log(`Sent ${type} notification for delivery ${deliveryId} to customer ${customer.id}`);
  }
}

/**
 * Format date as time string
 * @param date Date to format
 * @returns Formatted time string (e.g., "3:45 PM")
 */
const formatTime = (date: Date): string => {
  return date.toLocaleTimeString([], { hour: 'numeric', minute: '2-digit' });
};

export default new ETAUpdateService();
