import { Platform } from 'react-native';
import * as Linking from 'expo-linking';

/**
 * Interface for SMS message
 */
export interface SMSMessage {
  to: string;
  body: string;
}

/**
 * Send an SMS message
 * @param message SMS message to send
 * @returns Promise that resolves to true if the SMS app was opened successfully
 */
export const sendSMS = async (message: SMSMessage): Promise<boolean> => {
  try {
    // Format the SMS URI based on the platform
    let smsUri = '';
    
    if (Platform.OS === 'ios') {
      // iOS uses the sms: scheme with & as separator
      smsUri = `sms:${message.to}&body=${encodeURIComponent(message.body)}`;
    } else {
      // Android uses the smsto: scheme with ? as separator
      smsUri = `smsto:${message.to}?body=${encodeURIComponent(message.body)}`;
    }
    
    // Open the SMS app with the pre-filled message
    const supported = await Linking.canOpenURL(smsUri);
    
    if (supported) {
      await Linking.openURL(smsUri);
      return true;
    } else {
      console.error('SMS URI not supported on this device');
      return false;
    }
  } catch (error) {
    console.error('Error sending SMS:', error);
    return false;
  }
};

/**
 * In a production app, this would be replaced with a call to a backend API
 * that would handle the actual sending of SMS messages through a service like Twilio.
 * 
 * For now, this is a mock implementation that logs the message and returns success.
 * 
 * @param message SMS message to send
 * @returns Promise that resolves to true if the message was sent successfully
 */
export const sendSMSViaAPI = async (message: SMSMessage): Promise<boolean> => {
  try {
    // In a real app, this would make an API call to a backend service
    console.log(`[SMS API] Sending SMS to ${message.to}: ${message.body}`);
    
    // Simulate API call success
    return true;
  } catch (error) {
    console.error('Error sending SMS via API:', error);
    return false;
  }
};