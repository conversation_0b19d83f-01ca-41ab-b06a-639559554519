import AsyncStorage from '@react-native-async-storage/async-storage';
import { Alert } from 'react-native';

// Define expense categories
export enum ExpenseCategory {
  FUEL = 'Fuel',
  MAINTENANCE = 'Maintenance',
  REPAIRS = 'Repairs',
  INSURANCE = 'Insurance',
  REGISTRATION = 'Registration',
  TOLLS = 'Tolls',
  PARKING = 'Parking',
  SUPPLIES = 'Supplies',
  MEALS = 'Meals',
  LODGING = 'Lodging',
  OFFICE = 'Office',
  UTILITIES = 'Utilities',
  PHONE = 'Phone',
  INTERNET = 'Internet',
  SOFTWARE = 'Software',
  HARDWARE = 'Hardware',
  PROFESSIONAL_SERVICES = 'Professional Services',
  TAXES = 'Taxes',
  OTHER = 'Other',
}

// Define expense interface
export interface Expense {
  id: string;
  amount: number;
  date: string;
  category: ExpenseCategory | string;
  description: string;
  vehicleId?: string;
  vehicle?: {
    id: string;
    name: string;
    make: string;
    model: string;
    year: number;
  };
  tripId?: string;
  trip?: {
    id: string;
    date: string;
    startLocation: string;
    endLocation: string;
    distance: number;
    category: 'business' | 'personal' | 'commute';
  };
  isTaxDeductible: boolean;
  receiptImage?: string;
  notes?: string;
  merchant?: string;
  createdAt: string;
  updatedAt: string;
  // AI-assisted categorization data
  aiProcessed?: boolean;
  confidenceLevels?: {
    amount?: number;
    date?: number;
    category?: number;
    merchant?: number;
  };
  alternativeCategories?: string[];
}

// Define receipt OCR result interface
export interface ReceiptOcrResult {
  amount?: number;
  date?: string;
  merchant?: string;
  category?: ExpenseCategory | string;
  items?: Array<{
    description: string;
    amount: number;
  }>;
  confidenceLevels: {
    amount: number;
    date: number;
    merchant: number;
    category: number;
  };
  alternativeCategories: Array<ExpenseCategory | string>;
}

/**
 * Get all expenses
 */
export const getAllExpenses = async (): Promise<Expense[]> => {
  try {
    const expensesJson = await AsyncStorage.getItem('expenses');
    if (expensesJson) {
      return JSON.parse(expensesJson);
    }
    return [];
  } catch (error) {
    console.error('Error getting expenses:', error);
    return [];
  }
};

/**
 * Get expense by ID
 */
export const getExpenseById = async (id: string): Promise<Expense | null> => {
  try {
    const expenses = await getAllExpenses();
    return expenses.find(expense => expense.id === id) || null;
  } catch (error) {
    console.error(`Error getting expense with ID ${id}:`, error);
    return null;
  }
};

/**
 * Save all expenses
 */
const saveAllExpenses = async (expenses: Expense[]): Promise<boolean> => {
  try {
    await AsyncStorage.setItem('expenses', JSON.stringify(expenses));
    return true;
  } catch (error) {
    console.error('Error saving expenses:', error);
    return false;
  }
};

/**
 * Create a new expense
 */
export const createExpense = async (expense: Omit<Expense, 'id' | 'createdAt' | 'updatedAt'>): Promise<Expense> => {
  try {
    const expenses = await getAllExpenses();
    
    const newExpense: Expense = {
      ...expense,
      id: Math.random().toString(36).substring(2, 15),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
    
    expenses.push(newExpense);
    await saveAllExpenses(expenses);
    
    return newExpense;
  } catch (error) {
    console.error('Error creating expense:', error);
    throw new Error(`Failed to create expense: ${error.message}`);
  }
};

/**
 * Update an existing expense
 */
export const updateExpense = async (id: string, updates: Partial<Expense>): Promise<Expense> => {
  try {
    const expenses = await getAllExpenses();
    const index = expenses.findIndex(expense => expense.id === id);
    
    if (index === -1) {
      throw new Error(`Expense with ID ${id} not found`);
    }
    
    const updatedExpense: Expense = {
      ...expenses[index],
      ...updates,
      updatedAt: new Date().toISOString(),
    };
    
    expenses[index] = updatedExpense;
    await saveAllExpenses(expenses);
    
    return updatedExpense;
  } catch (error) {
    console.error(`Error updating expense with ID ${id}:`, error);
    throw new Error(`Failed to update expense: ${error.message}`);
  }
};

/**
 * Delete an expense
 */
export const deleteExpense = async (id: string): Promise<boolean> => {
  try {
    const expenses = await getAllExpenses();
    const filteredExpenses = expenses.filter(expense => expense.id !== id);
    
    if (filteredExpenses.length === expenses.length) {
      throw new Error(`Expense with ID ${id} not found`);
    }
    
    await saveAllExpenses(filteredExpenses);
    return true;
  } catch (error) {
    console.error(`Error deleting expense with ID ${id}:`, error);
    return false;
  }
};

/**
 * Process receipt image with OCR and AI categorization
 */
export const processReceiptImage = async (imageUri: string): Promise<ReceiptOcrResult> => {
  try {
    // In a real implementation, this would:
    // 1. Upload the image to a server for OCR processing
    // 2. Process the OCR results with AI for categorization
    // 3. Return the structured data with confidence levels
    
    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // For demo purposes, return mock data with random confidence levels
    const mockCategories = Object.values(ExpenseCategory);
    const selectedCategory = mockCategories[Math.floor(Math.random() * mockCategories.length)];
    
    // Generate 2-3 alternative categories
    const alternativeCount = 2 + Math.floor(Math.random() * 2);
    const alternativeCategories: Array<ExpenseCategory | string> = [];
    
    while (alternativeCategories.length < alternativeCount) {
      const randomCategory = mockCategories[Math.floor(Math.random() * mockCategories.length)];
      if (randomCategory !== selectedCategory && !alternativeCategories.includes(randomCategory)) {
        alternativeCategories.push(randomCategory);
      }
    }
    
    // Generate random confidence levels between 0.6 and 0.99
    const getRandomConfidence = () => 0.6 + Math.random() * 0.39;
    
    return {
      amount: Math.round(Math.random() * 10000) / 100, // Random amount between 0 and 100
      date: new Date().toISOString().split('T')[0], // Today's date
      merchant: ['Costco Gas', 'Shell', 'Exxon', 'Walmart', 'Target', 'Amazon', 'Home Depot'][Math.floor(Math.random() * 7)],
      category: selectedCategory,
      confidenceLevels: {
        amount: getRandomConfidence(),
        date: getRandomConfidence(),
        merchant: getRandomConfidence(),
        category: getRandomConfidence(),
      },
      alternativeCategories,
    };
  } catch (error) {
    console.error('Error processing receipt image:', error);
    throw new Error(`Failed to process receipt: ${error.message}`);
  }
};

/**
 * Create expense from OCR result
 */
export const createExpenseFromOcrResult = async (
  ocrResult: ReceiptOcrResult,
  receiptImageUri: string,
  additionalData: {
    vehicleId?: string;
    tripId?: string;
    notes?: string;
    isTaxDeductible?: boolean;
  } = {}
): Promise<Expense> => {
  try {
    const newExpense: Omit<Expense, 'id' | 'createdAt' | 'updatedAt'> = {
      amount: ocrResult.amount || 0,
      date: ocrResult.date || new Date().toISOString().split('T')[0],
      category: ocrResult.category || ExpenseCategory.OTHER,
      description: `${ocrResult.merchant || 'Unknown'} purchase`,
      isTaxDeductible: additionalData.isTaxDeductible || false,
      receiptImage: receiptImageUri,
      merchant: ocrResult.merchant,
      notes: additionalData.notes,
      vehicleId: additionalData.vehicleId,
      tripId: additionalData.tripId,
      aiProcessed: true,
      confidenceLevels: ocrResult.confidenceLevels,
      alternativeCategories: ocrResult.alternativeCategories,
    };
    
    return await createExpense(newExpense);
  } catch (error) {
    console.error('Error creating expense from OCR result:', error);
    throw new Error(`Failed to create expense from OCR result: ${error.message}`);
  }
};

/**
 * Get tax optimization suggestions for expenses
 */
export const getTaxOptimizationSuggestions = async (expenses: Expense[]): Promise<{
  suggestions: string[];
  potentialSavings: number;
}> => {
  try {
    // In a real implementation, this would analyze expenses and provide tax optimization suggestions
    // For now, return mock suggestions
    
    // Count non-tax-deductible expenses that could potentially be deductible
    const potentialDeductibles = expenses.filter(
      expense => !expense.isTaxDeductible && 
      ['Fuel', 'Maintenance', 'Repairs', 'Office', 'Software', 'Professional Services'].includes(expense.category as string)
    );
    
    const potentialSavings = potentialDeductibles.reduce((sum, expense) => sum + expense.amount * 0.3, 0);
    
    const suggestions = [
      'Consider marking fuel expenses for business trips as tax deductible',
      'Vehicle maintenance expenses may be partially deductible for business use',
      'Professional service expenses related to your business are typically tax deductible',
      'Keep detailed records of all business-related travel expenses',
      'Consult with a tax professional to maximize your eligible deductions',
    ];
    
    return {
      suggestions: suggestions.slice(0, 2 + Math.floor(Math.random() * 3)), // Return 2-4 random suggestions
      potentialSavings: Math.round(potentialSavings * 100) / 100,
    };
  } catch (error) {
    console.error('Error getting tax optimization suggestions:', error);
    return {
      suggestions: [],
      potentialSavings: 0,
    };
  }
};

/**
 * Initialize with sample data if needed
 */
export const initializeWithSampleDataIfNeeded = async (): Promise<void> => {
  try {
    const expenses = await getAllExpenses();
    
    if (expenses.length === 0) {
      // Add sample expenses for demonstration
      const sampleExpenses: Omit<Expense, 'id' | 'createdAt' | 'updatedAt'>[] = [
        {
          amount: 45.67,
          date: '2023-06-20',
          category: ExpenseCategory.FUEL,
          description: 'Regular unleaded gas',
          vehicle: { id: '1', name: 'My Car', make: 'Toyota', model: 'Camry', year: 2020 },
          trip: { 
            id: '1', 
            date: '2023-06-20', 
            startLocation: 'Home', 
            endLocation: 'Office', 
            distance: 12.5,
            category: 'business'
          },
          isTaxDeductible: true,
          receiptImage: 'https://example.com/receipt.jpg',
          notes: 'Filled up at Costco',
          merchant: 'Costco Gas',
          aiProcessed: true,
          confidenceLevels: {
            amount: 0.95,
            date: 0.98,
            category: 0.85,
            merchant: 0.92,
          },
          alternativeCategories: ['Transportation', 'Vehicle Maintenance', 'Travel'],
        },
        {
          amount: 28.99,
          date: '2023-06-18',
          category: ExpenseCategory.MAINTENANCE,
          description: 'Oil change',
          vehicle: { id: '1', name: 'My Car', make: 'Toyota', model: 'Camry', year: 2020 },
          isTaxDeductible: false,
          merchant: 'Jiffy Lube',
          aiProcessed: true,
          confidenceLevels: {
            amount: 0.92,
            date: 0.95,
            category: 0.88,
            merchant: 0.90,
          },
          alternativeCategories: ['Repairs', 'Service', 'Automotive'],
        },
      ];
      
      for (const expense of sampleExpenses) {
        await createExpense(expense);
      }
    }
  } catch (error) {
    console.error('Error initializing sample data:', error);
  }
};