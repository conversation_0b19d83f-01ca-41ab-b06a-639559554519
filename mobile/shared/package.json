{"name": "nxtacre-shared", "version": "1.0.0", "description": "Shared components and utilities for NxtAcre mobile apps", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"@expo/vector-icons": "^13.0.0", "@react-native-async-storage/async-storage": "^2.2.0", "@react-native-community/netinfo": "^11.4.1", "expo-constants": "~14.0.2", "expo-notifications": "^0.31.3", "react": "*", "react-native": "*", "react-native-gesture-handler": "~2.9.0", "react-native-reanimated": "~2.14.4", "sentry-expo": "~6.0.0", "zustand": "^5.0.5"}, "peerDependencies": {"react": "*", "react-native": "*"}, "author": "NxtAcre", "license": "UNLICENSED", "private": true}