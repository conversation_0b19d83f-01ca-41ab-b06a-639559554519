# Unified Notification System

The Unified Notification System provides a centralized way to manage notifications across all NxtAcre mobile apps. It allows apps to send and receive notifications, display them in a consistent UI, and handle user interactions with notifications.

## Features

- Cross-app notifications with source and target app identifiers
- Notification types for different kinds of notifications
- Storage of notifications in AsyncStorage for persistence
- Badge count management based on unread notifications
- Deep linking support for navigation between apps
- Consistent UI for displaying notifications
- Notification button with badge count for app headers

## Components

### 1. NotificationButton

A button component that displays the current unread notification count and opens the notification center when pressed.

```jsx
import React from 'react';
import { View } from 'react-native';
import { NotificationButton, AppIdentifier } from '@nxtacre/shared';

const MyHeader = () => {
  const handleNotificationPress = (notification) => {
    // Handle notification press
    console.log('Notification pressed:', notification);
    
    // Navigate based on notification type or deep link
    if (notification.deepLink) {
      // Navigate to the deep link
    }
  };

  return (
    <View style={{ flexDirection: 'row', justifyContent: 'flex-end', padding: 10 }}>
      <NotificationButton 
        appIdentifier={AppIdentifier.FIELD_OPERATIONS} 
        onNotificationPress={handleNotificationPress}
      />
    </View>
  );
};

export default MyHeader;
```

### 2. UnifiedNotificationCenter

A component that displays a list of notifications with their title, body, source app, and timestamp.

```jsx
import React, { useState } from 'react';
import { View, Modal, Button } from 'react-native';
import { UnifiedNotificationCenter, AppIdentifier } from '@nxtacre/shared';

const MyApp = () => {
  const [modalVisible, setModalVisible] = useState(false);

  const handleNotificationPress = (notification) => {
    // Handle notification press
    console.log('Notification pressed:', notification);
    
    // Navigate based on notification type or deep link
    if (notification.deepLink) {
      // Navigate to the deep link
    }
    
    // Close the modal
    setModalVisible(false);
  };

  return (
    <View style={{ flex: 1 }}>
      <Button title="Open Notifications" onPress={() => setModalVisible(true)} />
      
      <Modal
        animationType="slide"
        transparent={false}
        visible={modalVisible}
        onRequestClose={() => setModalVisible(false)}
      >
        <UnifiedNotificationCenter
          appIdentifier={AppIdentifier.FIELD_OPERATIONS}
          onNotificationPress={handleNotificationPress}
          onClose={() => setModalVisible(false)}
        />
      </Modal>
    </View>
  );
};

export default MyApp;
```

## Services

### 1. Sending Notifications

```javascript
import { 
  sendUnifiedNotification, 
  NotificationType, 
  AppIdentifier 
} from '@nxtacre/shared';

// Send a notification
const sendTaskAssignmentNotification = async (taskId, taskTitle, assigneeId) => {
  await sendUnifiedNotification({
    type: NotificationType.TASK_ASSIGNMENT,
    title: 'New Task Assigned',
    body: `You have been assigned a new task: ${taskTitle}`,
    sourceApp: AppIdentifier.FARM_MANAGER,
    targetApp: AppIdentifier.EMPLOYEE,
    data: {
      taskId,
      assigneeId,
    },
    actionable: true,
    action: 'VIEW_TASK',
    deepLink: `nxtacre://employee/tasks/${taskId}`,
    priority: 'high',
  });
};
```

### 2. Initializing the Notification System

```javascript
import { initializeUnifiedNotificationSystem } from '@nxtacre/shared';

// Initialize the notification system in your app's entry point
const App = () => {
  useEffect(() => {
    // Initialize the unified notification system
    initializeUnifiedNotificationSystem();
  }, []);

  return (
    // Your app components
  );
};
```

### 3. Getting Notifications

```javascript
import { 
  getUnifiedNotifications, 
  AppIdentifier 
} from '@nxtacre/shared';

// Get all notifications for the current app
const loadNotifications = async () => {
  const notifications = await getUnifiedNotifications(AppIdentifier.FIELD_OPERATIONS);
  console.log('Notifications:', notifications);
  return notifications;
};
```

### 4. Managing Notifications

```javascript
import { 
  markNotificationAsRead, 
  deleteNotification, 
  clearAllNotifications,
  getUnreadNotificationCount,
  AppIdentifier 
} from '@nxtacre/shared';

// Mark a notification as read
const markAsRead = async (notificationId) => {
  await markNotificationAsRead(notificationId);
};

// Delete a notification
const deleteNotif = async (notificationId) => {
  await deleteNotification(notificationId);
};

// Clear all notifications for the current app
const clearNotifications = async () => {
  await clearAllNotifications(AppIdentifier.FIELD_OPERATIONS);
};

// Get unread notification count
const getUnreadCount = async () => {
  const count = await getUnreadNotificationCount(AppIdentifier.FIELD_OPERATIONS);
  console.log('Unread count:', count);
  return count;
};
```

## Notification Types

The system supports various notification types for different scenarios:

```javascript
export enum NotificationType {
  TASK_ASSIGNMENT = 'TASK_ASSIGNMENT',
  DUE_DATE_ALERT = 'DUE_DATE_ALERT',
  STATUS_CHANGE = 'STATUS_CHANGE',
  SYSTEM_ANNOUNCEMENT = 'SYSTEM_ANNOUNCEMENT',
  DELIVERY_UPDATE = 'DELIVERY_UPDATE',
  INVENTORY_ALERT = 'INVENTORY_ALERT',
  MAINTENANCE_REMINDER = 'MAINTENANCE_REMINDER',
  FINANCIAL_ALERT = 'FINANCIAL_ALERT',
  WEATHER_ALERT = 'WEATHER_ALERT',
  MARKETPLACE_UPDATE = 'MARKETPLACE_UPDATE',
  EMPLOYEE_UPDATE = 'EMPLOYEE_UPDATE',
  FIELD_ALERT = 'FIELD_ALERT',
}
```

## App Identifiers

Each app has a unique identifier:

```javascript
export enum AppIdentifier {
  FIELD_OPERATIONS = 'field-operations',
  FARM_MANAGER = 'farm-manager',
  INVENTORY_EQUIPMENT = 'inventory-equipment',
  FINANCIAL_MANAGER = 'financial-manager',
  EMPLOYEE = 'employee',
  MARKETPLACE = 'marketplace',
  DRIVER = 'driver',
  DRIVE_TRACKER = 'drive-tracker',
}
```

## Integration Steps

To integrate the Unified Notification System into your app:

1. Import the necessary components and services from `@nxtacre/shared`
2. Initialize the notification system in your app's entry point
3. Add the NotificationButton to your app's header
4. Implement handlers for notification presses
5. Use the notification services to send and manage notifications

## Best Practices

1. **Targeted Notifications**: When possible, specify a target app to avoid showing irrelevant notifications to users.
2. **Clear Titles and Bodies**: Make notification titles and bodies clear and concise.
3. **Deep Links**: Use deep links to navigate users to the relevant screen when they tap a notification.
4. **Priority**: Use the priority field appropriately - reserve 'high' for urgent notifications.
5. **Data Payload**: Include relevant data in the notification to allow proper handling when tapped.