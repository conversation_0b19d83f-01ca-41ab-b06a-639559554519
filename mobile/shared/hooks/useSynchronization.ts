import { useState, useEffect } from 'react';
import NetInfo from '@react-native-community/netinfo';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Define the return type for the hook
interface SynchronizationState {
  isInitialized: boolean;
  isOnline: boolean;
  lastSyncTime: Date | null;
  pendingChanges: number;
  syncInProgress: boolean;
  syncError: string | null;
  synchronize: () => Promise<boolean>;
}

/**
 * Hook for managing data synchronization between the app and the server
 * Handles offline mode, tracking pending changes, and synchronization
 */
const useSynchronization = (): SynchronizationState => {
  // State for tracking initialization status
  const [isInitialized, setIsInitialized] = useState(false);
  
  // State for tracking online status
  const [isOnline, setIsOnline] = useState(true);
  
  // State for tracking last successful sync time
  const [lastSyncTime, setLastSyncTime] = useState<Date | null>(null);
  
  // State for tracking number of pending changes
  const [pendingChanges, setPendingChanges] = useState(0);
  
  // State for tracking if sync is in progress
  const [syncInProgress, setSyncInProgress] = useState(false);
  
  // State for tracking sync errors
  const [syncError, setSyncError] = useState<string | null>(null);

  // Initialize the synchronization service
  useEffect(() => {
    const initialize = async () => {
      try {
        // Load last sync time from storage
        const storedLastSyncTime = await AsyncStorage.getItem('lastSyncTime');
        if (storedLastSyncTime) {
          setLastSyncTime(new Date(storedLastSyncTime));
        }

        // Load pending changes count from storage
        const storedPendingChanges = await AsyncStorage.getItem('pendingChanges');
        if (storedPendingChanges) {
          setPendingChanges(parseInt(storedPendingChanges, 10));
        }

        // Set up network status listener
        const unsubscribe = NetInfo.addEventListener(state => {
          setIsOnline(state.isConnected ?? false);
          
          // If we're coming back online and have pending changes, trigger sync
          if (state.isConnected && pendingChanges > 0) {
            synchronize();
          }
        });

        // Check initial network status
        const initialState = await NetInfo.fetch();
        setIsOnline(initialState.isConnected ?? false);

        // Mark as initialized
        setIsInitialized(true);

        // Return cleanup function
        return () => {
          unsubscribe();
        };
      } catch (error) {
        console.error('Error initializing synchronization service:', error);
        setSyncError('Failed to initialize synchronization service');
        // Still mark as initialized even if there was an error
        setIsInitialized(true);
      }
    };

    initialize();
  }, [pendingChanges]); // Re-run if pendingChanges changes

  /**
   * Synchronize data with the server
   * @returns Promise that resolves to true if sync was successful
   */
  const synchronize = async (): Promise<boolean> => {
    // If already syncing or offline, don't start another sync
    if (syncInProgress || !isOnline) {
      return false;
    }

    setSyncInProgress(true);
    setSyncError(null);

    try {
      // If no pending changes, just update last sync time
      if (pendingChanges === 0) {
        const now = new Date();
        setLastSyncTime(now);
        await AsyncStorage.setItem('lastSyncTime', now.toISOString());
        setSyncInProgress(false);
        return true;
      }

      // In a real implementation, this would:
      // 1. Get all pending changes from local storage
      // 2. Send them to the server
      // 3. Process any conflicts
      // 4. Update local storage with server changes
      
      // Simulate a successful sync
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Update last sync time
      const now = new Date();
      setLastSyncTime(now);
      await AsyncStorage.setItem('lastSyncTime', now.toISOString());
      
      // Clear pending changes
      setPendingChanges(0);
      await AsyncStorage.setItem('pendingChanges', '0');
      
      setSyncInProgress(false);
      return true;
    } catch (error) {
      console.error('Error during synchronization:', error);
      setSyncError('Failed to synchronize with server');
      setSyncInProgress(false);
      return false;
    }
  };

  return {
    isInitialized,
    isOnline,
    lastSyncTime,
    pendingChanges,
    syncInProgress,
    syncError,
    synchronize,
  };
};

export default useSynchronization;