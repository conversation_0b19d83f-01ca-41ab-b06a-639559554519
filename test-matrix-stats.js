import axios from 'axios';
import dotenv from 'dotenv';

dotenv.config({ path: './webapp/.env' });

const API_URL = process.env.API_URL || 'http://localhost:3002/api';
const JWT_TOKEN = process.env.TEST_JWT_TOKEN; // You'll need to set this with a valid admin token

async function testMatrixStats() {
  try {
    if (!JWT_TOKEN) {
      console.error('Please set TEST_JWT_TOKEN in your .env file with a valid admin JWT token');
      return;
    }

    console.log('Testing Matrix stats endpoint...');
    const response = await axios.get(`${API_URL}/admin/matrix/stats`, {
      headers: {
        Authorization: `Bearer ${JWT_TOKEN}`
      }
    });

    console.log('Response status:', response.status);
    console.log('Response data:', JSON.stringify(response.data, null, 2));
    console.log('Test completed successfully!');
  } catch (error) {
    console.error('Error testing Matrix stats endpoint:');
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', error.response.data);
    } else {
      console.error(error.message);
    }
  }
}

testMatrixStats();