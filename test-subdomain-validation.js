// Test script for subdomain validation
import { validateSubdomain } from './webapp/server/utils/subdomainUtils.js';

// Test cases
const testCases = [
  // Valid subdomains
  { subdomain: 'mycompany', expected: true, description: 'Valid company name' },
  { subdomain: 'acme-corp', expected: true, description: 'Valid company name with hyphen' },
  { subdomain: 'farm123', expected: true, description: 'Valid alphanumeric' },
  
  // Invalid subdomains - format issues
  { subdomain: 'ab', expected: false, description: 'Too short' },
  { subdomain: 'invalid@domain', expected: false, description: 'Contains invalid characters' },
  
  // Reserved words and sensitive routes
  { subdomain: 'admin', expected: false, description: 'Reserved: admin' },
  { subdomain: 'www', expected: false, description: 'Reserved: www' },
  { subdomain: 'login', expected: false, description: 'Sensitive: login' },
  { subdomain: 'auth', expected: false, description: 'Sensitive: auth' },
  { subdomain: 'pass', expected: false, description: 'Sensitive: pass' },
  { subdomain: 'sso', expected: false, description: 'Sensitive: sso' },
  { subdomain: 'mail', expected: false, description: 'Sensitive: mail' },
  { subdomain: 'password', expected: false, description: 'Sensitive: password' },
  { subdomain: 'security', expected: false, description: 'Sensitive: security' },
  { subdomain: 'billing', expected: false, description: 'Sensitive: billing' },
  { subdomain: 'payment', expected: false, description: 'Sensitive: payment' },
  { subdomain: 'dashboard', expected: false, description: 'Sensitive: dashboard' },
  { subdomain: 'register', expected: false, description: 'Sensitive: register' },
];

// Run tests
console.log('Testing subdomain validation...\n');
let passCount = 0;
let failCount = 0;

for (const test of testCases) {
  const result = validateSubdomain(test.subdomain);
  const passed = result.isValid === test.expected;
  
  if (passed) {
    passCount++;
    console.log(`✅ PASS: "${test.subdomain}" - ${test.description}`);
  } else {
    failCount++;
    console.log(`❌ FAIL: "${test.subdomain}" - ${test.description}`);
    console.log(`   Expected: ${test.expected}, Got: ${result.isValid}`);
    console.log(`   Message: ${result.message}`);
  }
}

console.log(`\nTest Results: ${passCount} passed, ${failCount} failed`);