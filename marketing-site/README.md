# nxtAcre Marketing Website

This is the marketing website for the nxtAcre Farm Management Platform. It's a standalone website separate from the main application, designed to showcase the platform's features and benefits to potential customers.

## Structure

The marketing website follows a simple structure:

```
marketing-site/
├── assets/
│   ├── images/       # Images used throughout the site
│   └── fonts/        # Custom fonts (if any)
├── css/
│   └── styles.css    # Custom CSS styles
├── js/
│   └── main.js       # JavaScript functionality
├── index.html        # Home page
├── features.html     # Features page
├── pricing.html      # Pricing page
├── contact.html      # Contact page
└── about.html        # About page (to be implemented)
```

## Technologies Used

- HTML5
- CSS3 with custom styles
- JavaScript (ES6+)
- Bootstrap 5 for responsive design
- AOS (Animate On Scroll) for animations
- Bootstrap Icons for iconography
- Google Maps for location embedding

## Running Locally

To run the marketing website locally:

1. Navigate to the marketing-site directory:
   ```
   cd marketing-site
   ```

2. Install dependencies:
   ```
   npm install
   ```

3. Start the development server:
   ```
   npm start
   ```

4. Open your browser and navigate to `http://localhost:8080`

## Deployment

The marketing website is designed to be deployed as a static site. Here are the steps to deploy it:

1. Ensure all image assets are optimized for web
2. Update any environment-specific URLs (like app.nxtacre.com) to match the production environment
3. Deploy the entire `marketing-site` directory to your web server or static site hosting service

### Deployment Options

- **Web Server**: Upload the files to your web server via FTP or SFTP
- **AWS S3**: Host as a static website on Amazon S3
- **Netlify**: Connect your repository to Netlify for automatic deployments
- **Vercel**: Deploy using Vercel for static site hosting
- **GitHub Pages**: Host directly from your GitHub repository

## Customization

### Changing Colors

The color scheme is defined using CSS variables in `css/styles.css`. To change the color scheme, modify the following variables:

```css
:root {
    --primary-color: #4CAF50;
    --secondary-color: #2E7D32;
    --accent-color: #FFC107;
    --text-color: #333333;
    --light-color: #F8F9FA;
    --dark-color: #212529;
    --gray-color: #6C757D;
}
```

### Adding New Pages

To add a new page:

1. Create a new HTML file in the root directory
2. Copy the header and footer structure from an existing page
3. Update the navigation to highlight the new page when active
4. Add your content in the main section
5. Link to the new page from the navigation and footer

### Updating Images

Replace the placeholder images in the `assets/images` directory with your own images. Make sure to maintain the same filenames or update the references in the HTML files.

## Links to Main Application

The marketing website includes links to the main nxtAcre application for:

- Login: `https://app.nxtacre.com/login`
- Registration: `https://app.nxtacre.com/register`

These links are present in the navigation bar and various call-to-action buttons throughout the site. Update these URLs if your application domain changes.

## Maintenance

Regular maintenance tasks:

1. Keep dependencies updated
2. Check for broken links
3. Update content as the product evolves
4. Optimize images for performance
5. Test across different browsers and devices

## License

This project is proprietary and confidential. Unauthorized copying, distribution, or use is strictly prohibited.