const express = require('express');
const path = require('path');
const fs = require('fs');
const app = express();
const port = process.env.PORT || 8080;

// Set EJS as the view engine
app.set('view engine', 'ejs');
app.set('views', path.join(__dirname, 'views'));

// Serve static files (CSS, JS, images)
app.use(express.static(path.join(__dirname)));

// Create a directory for views if it doesn't exist
const viewsDir = path.join(__dirname, 'views');
if (!fs.existsSync(viewsDir)) {
  fs.mkdirSync(viewsDir);
}

// Create a directory for page templates if it doesn't exist
const pagesDir = path.join(__dirname, 'views', 'pages');
if (!fs.existsSync(pagesDir)) {
  fs.mkdirSync(pagesDir);
}

// Helper function to extract content between tags
function extractContent(htmlContent, startTag, endTag) {
  const startIndex = htmlContent.indexOf(startTag) + startTag.length;
  const endIndex = htmlContent.indexOf(endTag);
  if (startIndex === -1 || endIndex === -1) {
    return '';
  }
  return htmlContent.substring(startIndex, endIndex).trim();
}

// Helper function to get page metadata
function getPageMetadata(htmlContent) {
  const titleMatch = htmlContent.match(/<title>(.*?)<\/title>/);
  const descriptionMatch = htmlContent.match(/<meta name="description" content="(.*?)">/);

  return {
    title: titleMatch ? titleMatch[1].replace(' - nxtAcre Farm Management Platform', '') : 'nxtAcre',
    description: descriptionMatch ? descriptionMatch[1] : 'nxtAcre Farm Management Platform'
  };
}

// Route handler for URLs without .html extension
app.get('/:page', (req, res) => {
  const page = req.params.page;
  const htmlFile = path.join(__dirname, `${page}.html`);

  // Check if the HTML file exists
  if (fs.existsSync(htmlFile)) {
    // Read the HTML file
    const htmlContent = fs.readFileSync(htmlFile, 'utf8');

    // Extract metadata
    const metadata = getPageMetadata(htmlContent);

    // Extract content between body tags (excluding header and footer)
    const bodyStartTag = '<body>';
    const bodyEndTag = '</body>';
    let bodyContent = extractContent(htmlContent, bodyStartTag, bodyEndTag);

    // Remove navigation section
    const navStartTag = '<!-- Navigation -->';
    const navEndTag = '</nav>';
    const navSection = bodyContent.substring(
      bodyContent.indexOf(navStartTag),
      bodyContent.indexOf(navEndTag) + navEndTag.length
    );
    bodyContent = bodyContent.replace(navSection, '');

    // Remove footer section
    const footerStartTag = '<!-- Footer -->';
    const footerEndTag = '</footer>';
    const footerSection = bodyContent.substring(
      bodyContent.indexOf(footerStartTag),
      bodyContent.indexOf(footerEndTag) + footerEndTag.length
    );
    bodyContent = bodyContent.replace(footerSection, '');

    // Remove script tags at the end
    const scriptsStartTag = '<!-- Bootstrap Icons -->';
    if (bodyContent.includes(scriptsStartTag)) {
      bodyContent = bodyContent.substring(0, bodyContent.indexOf(scriptsStartTag));
    }

    // Render the page with the extracted content
    res.render('page-template', {
      title: metadata.title,
      description: metadata.description,
      content: bodyContent,
      currentPage: page
    });
  } else {
    res.status(404).send('Page not found');
  }
});

// Route for the home page
app.get('/', (req, res) => {
  const htmlFile = path.join(__dirname, 'index.html');

  // Read the HTML file
  const htmlContent = fs.readFileSync(htmlFile, 'utf8');

  // Extract metadata
  const metadata = getPageMetadata(htmlContent);

  // Extract content between body tags (excluding header and footer)
  const bodyStartTag = '<body>';
  const bodyEndTag = '</body>';
  let bodyContent = extractContent(htmlContent, bodyStartTag, bodyEndTag);

  // Remove navigation section
  const navStartTag = '<!-- Navigation -->';
  const navEndTag = '</nav>';
  const navSection = bodyContent.substring(
    bodyContent.indexOf(navStartTag),
    bodyContent.indexOf(navEndTag) + navEndTag.length
  );
  bodyContent = bodyContent.replace(navSection, '');

  // Remove footer section
  const footerStartTag = '<!-- Footer -->';
  const footerEndTag = '</footer>';
  const footerSection = bodyContent.substring(
    bodyContent.indexOf(footerStartTag),
    bodyContent.indexOf(footerEndTag) + footerEndTag.length
  );
  bodyContent = bodyContent.replace(footerSection, '');

  // Remove script tags at the end
  const scriptsStartTag = '<!-- Bootstrap Icons -->';
  if (bodyContent.includes(scriptsStartTag)) {
    bodyContent = bodyContent.substring(0, bodyContent.indexOf(scriptsStartTag));
  }

  // Render the page with the extracted content
  res.render('page-template', {
    title: metadata.title,
    description: metadata.description,
    content: bodyContent,
    currentPage: 'home'
  });
});

// Start the server
app.listen(port, () => {
  console.log(`Server running at http://localhost:${port}`);
});
