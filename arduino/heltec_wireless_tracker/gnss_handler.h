/*
 * GNSS Handler for Heltec Wireless Tracker (ESP32S3)
 * 
 * This file implements the functionality for receiving and processing
 * Global Navigation Satellite System (GNSS) data.
 */

#ifndef GNSS_HANDLER_H
#define GNSS_HANDLER_H

#include <Arduino.h>
#include <TinyGPS++.h>
#include "config.h"

// Structure to hold GNSS data
struct GNSSData {
  double latitude;
  double longitude;
  double altitude;
  double speed;
  double course;
  int satellites;
  double hdop;
  String timestamp;
};

// Main GNSS handler class
class GNSSHandler {
private:
  TinyGPSPlus gps;
  HardwareSerial gnssSerial;
  GNSSData data;
  unsigned long lastUpdateTime;
  bool hasValidFix;
  
public:
  GNSSHandler() : 
    gnssSerial(1),  // Use UART1 for GNSS
    lastUpdateTime(0),
    hasValidFix(false) {
    // Initialize data structure
    data.latitude = 0.0;
    data.longitude = 0.0;
    data.altitude = 0.0;
    data.speed = 0.0;
    data.course = 0.0;
    data.satellites = 0;
    data.hdop = 99.99;
    data.timestamp = "";
  }
  
  // Initialize GNSS
  void init() {
    // Start GNSS serial communication
    gnssSerial.begin(GNSS_BAUD_RATE, SERIAL_8N1, GNSS_RX_PIN, GNSS_TX_PIN);
    
    if (DEBUG_ENABLED) {
      DEBUG_SERIAL.println("GNSS initialized");
    }
  }
  
  // Update GNSS data - call this in the main loop
  // Returns true if new data is available
  bool update() {
    // Read data from GNSS module
    while (gnssSerial.available() > 0) {
      gps.encode(gnssSerial.read());
    }
    
    unsigned long currentTime = millis();
    
    // Check if it's time to update the data
    if (currentTime - lastUpdateTime >= GNSS_UPDATE_INTERVAL) {
      lastUpdateTime = currentTime;
      
      // Check if we have a valid fix
      if (gps.location.isValid() && gps.date.isValid() && gps.time.isValid()) {
        // Update data structure
        data.latitude = gps.location.lat();
        data.longitude = gps.location.lng();
        data.altitude = gps.altitude.meters();
        data.speed = gps.speed.kmph();
        data.course = gps.course.deg();
        data.satellites = gps.satellites.value();
        data.hdop = gps.hdop.hdop();
        
        // Format timestamp: YYYY-MM-DDTHH:MM:SSZ
        char timestamp[24];
        sprintf(timestamp, "%04d-%02d-%02dT%02d:%02d:%02dZ",
                gps.date.year(),
                gps.date.month(),
                gps.date.day(),
                gps.time.hour(),
                gps.time.minute(),
                gps.time.second());
        data.timestamp = String(timestamp);
        
        hasValidFix = true;
        
        if (DEBUG_ENABLED) {
          DEBUG_SERIAL.print("GNSS fix: ");
          DEBUG_SERIAL.print(data.latitude, 6);
          DEBUG_SERIAL.print(", ");
          DEBUG_SERIAL.print(data.longitude, 6);
          DEBUG_SERIAL.print(" (");
          DEBUG_SERIAL.print(data.satellites);
          DEBUG_SERIAL.print(" satellites, HDOP: ");
          DEBUG_SERIAL.print(data.hdop);
          DEBUG_SERIAL.println(")");
        }
        
        return true;
      } else {
        hasValidFix = false;
        
        if (DEBUG_ENABLED) {
          DEBUG_SERIAL.println("Waiting for GNSS fix...");
        }
      }
    }
    
    return false;
  }
  
  // Get the current GNSS data
  GNSSData getData() {
    return data;
  }
  
  // Check if we have a valid fix
  bool hasValidData() {
    return hasValidFix;
  }
  
  // Get the number of satellites
  int getSatellites() {
    return data.satellites;
  }
  
  // Get the current latitude
  double getLatitude() {
    return data.latitude;
  }
  
  // Get the current longitude
  double getLongitude() {
    return data.longitude;
  }
  
  // Get the current altitude
  double getAltitude() {
    return data.altitude;
  }
  
  // Get the current speed in km/h
  double getSpeed() {
    return data.speed;
  }
  
  // Get the current course in degrees
  double getCourse() {
    return data.course;
  }
  
  // Get the current horizontal dilution of precision
  double getHDOP() {
    return data.hdop;
  }
  
  // Get the current timestamp
  String getTimestamp() {
    return data.timestamp;
  }
};

#endif // GNSS_HANDLER_H