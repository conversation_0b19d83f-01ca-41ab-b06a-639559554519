# Heltec Wireless Tracker (ESP32S3)

This module implements a wireless tracker using the Heltec ESP32S3 board with BLE and LoRaWAN capabilities for the NxtAcre Farm Management Platform.

## Features

- **BLE Communication**: Connects to the NxtAcre mobile app to send GNSS data and receive commands
- **LoRaWAN Communication**: Enables device-to-device communication between wireless trackers
- **GNSS Data Handling**: Processes location data from a GNSS module
- **Message Forwarding**: Forwards messages from LoRaWAN to BLE, enabling communication between farmers

## Hardware Requirements

- [Heltec WiFi LoRa 32 (V3)](https://heltec.org/project/wifi-lora-32-v3/) or compatible ESP32S3 board with LoRa
- GNSS/GPS module (e.g., NEO-6M, NEO-7M, or similar)
- Antenna for LoRa
- Antenna for GNSS
- USB cable for programming and power

## Wiring

Connect the GNSS module to the ESP32S3 as follows:

| GNSS Module | ESP32S3 |
|-------------|---------|
| VCC         | 3.3V    |
| GND         | GND     |
| TX          | GPIO22  |
| RX          | GPIO23  |

The LoRa module is integrated into the Heltec board, so no additional wiring is needed.

## Installation

1. Install the Arduino IDE (version 1.8.19 or later)
2. Add ESP32 board support to Arduino IDE:
   - Open Arduino IDE
   - Go to File > Preferences
   - Add `https://raw.githubusercontent.com/espressif/arduino-esp32/gh-pages/package_esp32_index.json` to the "Additional Board Manager URLs" field
   - Go to Tools > Board > Boards Manager
   - Search for "esp32" and install the latest version
3. Install required libraries:
   - Go to Sketch > Include Library > Manage Libraries
   - Install the following libraries:
     - TinyGPS++ by Mikal Hart
     - RadioLib by Jan Gromeš
     - ESP32 BLE Arduino by Neil Kolban
4. Clone or download this repository
5. Open the `heltec_wireless_tracker.ino` file in Arduino IDE
6. Select the appropriate board (Heltec WiFi LoRa 32 V3) from Tools > Board
7. Connect your ESP32S3 board to your computer
8. Click the Upload button to compile and upload the code

## Configuration

You can customize the behavior of the wireless tracker by modifying the `config.h` file:

- **Hardware Pins**: Adjust pin assignments if your hardware setup differs
- **BLE Configuration**: Change the device name and UUIDs if needed
- **LoRaWAN Configuration**: Adjust frequency, bandwidth, and other radio parameters
- **GNSS Configuration**: Modify baud rate and update interval
- **Timing Parameters**: Adjust advertising and transmission intervals
- **Debug Settings**: Enable or disable debug output

## Usage

### Mobile App Communication

The wireless tracker communicates with the NxtAcre mobile app via BLE. The app can:

1. Receive GNSS data from the tracker
2. Send messages to other trackers via LoRaWAN
3. Receive messages from other trackers

### Device-to-Device Communication

Trackers can communicate with each other using LoRaWAN. Messages sent from one tracker are automatically forwarded to the connected mobile app.

### Message Format

- **GNSS Data**: `GNSS:<latitude>,<longitude>,<altitude>,<speed>,<course>,<satellites>,<hdop>,<timestamp>`
- **LoRaWAN to BLE**: `FROM_LORAWAN:<message>|RSSI:<rssi>`
- **BLE to LoRaWAN**: `FWD:<message>`

## Troubleshooting

- **No BLE Connection**: Ensure the mobile app is scanning for the correct device name
- **No LoRaWAN Communication**: Check antenna connections and ensure trackers are within range
- **No GNSS Data**: Verify wiring and ensure the GNSS module has a clear view of the sky
- **Compilation Errors**: Make sure all required libraries are installed

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Contributing

Contributions to improve the wireless tracker module are welcome. Please feel free to submit a pull request.