/*
 * Configuration file for Heltec Wireless Tracker (ESP32S3)
 * 
 * This file contains all configurable parameters for the wireless tracker.
 */

#ifndef CONFIG_H
#define CONFIG_H

// Hardware pins
#define LED_PIN 25  // Built-in LED pin on Heltec ESP32S3
#define LORA_CS_PIN 18
#define LORA_RST_PIN 14
#define LORA_DIO0_PIN 26
#define LORA_DIO1_PIN 35
#define LORA_DIO2_PIN 34
#define GNSS_RX_PIN 22  // GPS module TX connects to this pin
#define GNSS_TX_PIN 23  // GPS module RX connects to this pin

// BLE Configuration
#define BLE_DEVICE_NAME "NxtAcre-Tracker"
#define SERVICE_UUID "4fafc201-1fb5-459e-8fcc-c5c9c331914b"
#define CHARACTERISTIC_UUID "beb5483e-36e1-4688-b7f5-ea07361b26a8"

// LoRaWAN Configuration
#define LORAWAN_FREQUENCY 915000000  // US frequency band (915 MHz)
#define LORAWAN_BANDWIDTH 125000     // 125 kHz
#define LORAWAN_SPREADING_FACTOR 7   // SF7
#define LORAWAN_CODING_RATE 5        // 4/5
#define LORAWAN_SYNC_WORD 0x34       // Private network
#define LORAWAN_TX_POWER 20          // 20 dBm (100 mW)
#define LORAWAN_PREAMBLE_LENGTH 8    // 8 symbols

// GNSS Configuration
#define GNSS_BAUD_RATE 9600          // Default baud rate for most GPS modules
#define GNSS_UPDATE_INTERVAL 5000    // Update GNSS data every 5 seconds

// Message buffer sizes
#define MAX_MESSAGE_SIZE 256         // Maximum size of messages
#define MAX_QUEUE_SIZE 10            // Maximum number of queued messages

// Timing parameters
#define BLE_ADVERTISING_INTERVAL 100 // Advertising interval in ms
#define BLE_ADVERTISING_REFRESH 60000 // Refresh advertising every 60 seconds
#define LORAWAN_TX_INTERVAL 30000    // Minimum time between LoRaWAN transmissions (30 seconds)

// Debug settings
#define DEBUG_ENABLED true           // Enable debug output
#define DEBUG_SERIAL Serial          // Serial port for debug output

#endif // CONFIG_H
