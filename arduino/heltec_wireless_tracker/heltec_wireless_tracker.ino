/*
 * Heltec Wireless Tracker (ESP32S3)
 *
 * This sketch implements a wireless tracker using the Heltec ESP32S3 board
 * with BLE and LoRaWAN capabilities. It enables:
 * 1. BLE communication with a mobile app
 * 2. LoRaWAN communication with other wireless trackers
 * 3. GNSS data transmission to the mobile app
 * 4. Message forwarding from LoRaWAN to BLE
 *
 * Created for NxtAcre Farm Management Platform
 */

// Include necessary libraries
#include <Arduino.h>
// Use the official ESP32 BLE libraries
#include <BLEDevice.h>
#include <BLEServer.h>
#include <BLEUtils.h>
#include <BLE2902.h>
#include <RadioLib.h>
#include <TinyGPS++.h>
#include "config.h"
#include "ble_handler.h"
#include "lorawan_handler.h"
#include "gnss_handler.h"

// Global variables
BLEHandler bleHandler;
LoRaWANHandler lorawanHandler;
GNSSHandler gnssHandler;

// Setup function - runs once at startup
void setup() {
  // Initialize serial communication
  Serial.begin(115200);
  delay(1000);
  Serial.println("Heltec Wireless Tracker starting...");

  // Initialize the LED pin as an output
  pinMode(LED_PIN, OUTPUT);

  // Initialize BLE
  bleHandler.init();
  Serial.println("BLE initialized");

  // Initialize LoRaWAN
  lorawanHandler.init();
  Serial.println("LoRaWAN initialized");

  // Initialize GNSS
  gnssHandler.init();
  Serial.println("GNSS initialized");

  // Set up message handlers
  bleHandler.setMessageCallback([](const String &message) {
    // Handle messages from BLE (mobile app)
    Serial.println("BLE message received: " + message);

    // Forward messages to other trackers via LoRaWAN if needed
    if (message.startsWith("FWD:")) {
      String forwardMessage = message.substring(4);
      lorawanHandler.sendMessage(forwardMessage);
    }
  });

  lorawanHandler.setMessageCallback([](const String &message, int rssi) {
    // Handle messages from LoRaWAN (other trackers)
    Serial.println("LoRaWAN message received: " + message + " (RSSI: " + String(rssi) + ")");

    // Forward messages to mobile app via BLE
    String forwardMessage = "FROM_LORAWAN:" + message + "|RSSI:" + String(rssi);
    bleHandler.sendMessage(forwardMessage);
  });

  Serial.println("Setup complete");
}

// Loop function - runs repeatedly
void loop() {
  // Process BLE events
  bleHandler.update();

  // Process LoRaWAN events
  lorawanHandler.update();

  // Update GNSS data
  if (gnssHandler.update()) {
    // If new GNSS data is available, send it via BLE
    GNSSData data = gnssHandler.getData();
    String gnssMessage = "GNSS:" +
                         String(data.latitude, 6) + "," +
                         String(data.longitude, 6) + "," +
                         String(data.altitude) + "," +
                         String(data.speed) + "," +
                         String(data.course) + "," +
                         String(data.satellites) + "," +
                         String(data.hdop) + "," +
                         data.timestamp;

    bleHandler.sendMessage(gnssMessage);
    
    // Visual indicator for GNSS update
    digitalWrite(LED_PIN, HIGH);
    delay(100);
    digitalWrite(LED_PIN, LOW);
  }
  
  // Small delay to prevent CPU hogging
  delay(10);
}