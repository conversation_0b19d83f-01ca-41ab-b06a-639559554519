/*
 * NxtAcre Weather Station - WiFi Handler
 * 
 * This file contains functions for handling WiFi connections and sending data to the server.
 */

#ifndef WIFI_HANDLER_H
#define WIFI_HANDLER_H

#include <WiFi.h>
#include <HTTPClient.h>
#include <ArduinoJson.h>
#include "config.h"
#include "sensor_handler.h"

// WiFi connection status
bool wifiConnected = false;
int wifiRetryCount = 0;
unsigned long lastWifiRetryTime = 0;

// Initialize WiFi connection
void initializeWiFi() {
  Serial.println("Initializing WiFi...");
  
  // Set WiFi mode
  WiFi.mode(WIFI_STA);
  
  // Start WiFi connection
  WiFi.begin(WIFI_SSID, WIFI_PASSWORD);
  
  // Wait for connection (with timeout)
  unsigned long startTime = millis();
  while (WiFi.status() != WL_CONNECTED && millis() - startTime < 10000) {
    delay(500);
    Serial.print(".");
  }
  
  if (WiFi.status() == WL_CONNECTED) {
    wifiConnected = true;
    Serial.println("\nWiFi connected!");
    Serial.print("IP address: ");
    Serial.println(WiFi.localIP());
  } else {
    wifiConnected = false;
    Serial.println("\nWiFi connection failed. Will retry later.");
  }
}

// Handle WiFi connection (reconnect if necessary)
void handleWiFi() {
  if (WiFi.status() != WL_CONNECTED) {
    if (wifiConnected) {
      // WiFi was connected but is now disconnected
      wifiConnected = false;
      Serial.println("WiFi connection lost!");
    }
    
    // Try to reconnect after delay
    unsigned long currentMillis = millis();
    if (currentMillis - lastWifiRetryTime >= WIFI_RETRY_DELAY) {
      lastWifiRetryTime = currentMillis;
      
      if (wifiRetryCount < WIFI_MAX_RETRIES || WIFI_MAX_RETRIES == 0) {
        Serial.print("Attempting to reconnect to WiFi... (Attempt ");
        Serial.print(++wifiRetryCount);
        Serial.println(")");
        
        WiFi.disconnect();
        delay(100);
        WiFi.begin(WIFI_SSID, WIFI_PASSWORD);
        
        // Wait briefly for connection
        unsigned long startTime = millis();
        while (WiFi.status() != WL_CONNECTED && millis() - startTime < 5000) {
          delay(100);
          Serial.print(".");
        }
        
        if (WiFi.status() == WL_CONNECTED) {
          wifiConnected = true;
          wifiRetryCount = 0;
          Serial.println("\nWiFi reconnected!");
          Serial.print("IP address: ");
          Serial.println(WiFi.localIP());
        } else {
          Serial.println("\nWiFi reconnection failed. Will retry later.");
        }
      } else {
        Serial.println("Maximum WiFi retry attempts reached. Restarting device...");
        ESP.restart();
      }
    }
  } else if (!wifiConnected) {
    // WiFi is now connected
    wifiConnected = true;
    wifiRetryCount = 0;
    Serial.println("WiFi connected!");
    Serial.print("IP address: ");
    Serial.println(WiFi.localIP());
  }
}

// Send weather data to the server
void sendDataToServer(WeatherData* data) {
  if (WiFi.status() != WL_CONNECTED) {
    Serial.println("Cannot send data: WiFi not connected");
    return;
  }
  
  Serial.println("Sending data to server...");
  
  HTTPClient http;
  http.begin(SERVER_URL);
  http.addHeader("Content-Type", "application/json");
  
  // Create JSON document
  DynamicJsonDocument jsonDoc(1024);
  jsonDoc["apiKey"] = API_KEY;
  jsonDoc["deviceIdentifier"] = DEVICE_IDENTIFIER;
  
  // Add weather data
  JsonObject iotData = jsonDoc.createNestedObject("iotData");
  iotData["timestamp"] = millis(); // In a real implementation, use a real timestamp
  iotData["temperature"] = data->temperature;
  iotData["humidity"] = data->humidity;
  iotData["pressure"] = data->pressure;
  
  // Add custom fields for weather station specific data
  JsonObject customFields = iotData.createNestedObject("customFields");
  customFields["rainfall"] = data->rainfall;
  customFields["windSpeed"] = data->windSpeed;
  customFields["windDirection"] = data->windDirection;
  customFields["solarVoltage"] = data->solarVoltage;
  
  // Add device status
  iotData["batteryLevel"] = data->batteryLevel;
  
  // Serialize JSON to string
  String jsonString;
  serializeJson(jsonDoc, jsonString);
  
  if (DEBUG_ENABLED) {
    Serial.println("JSON payload:");
    Serial.println(jsonString);
  }
  
  // Send POST request
  int httpResponseCode = http.POST(jsonString);
  
  if (httpResponseCode > 0) {
    String response = http.getString();
    Serial.print("HTTP Response code: ");
    Serial.println(httpResponseCode);
    
    if (DEBUG_ENABLED) {
      Serial.println("Response:");
      Serial.println(response);
    }
  } else {
    Serial.print("Error sending HTTP request. Error code: ");
    Serial.println(httpResponseCode);
  }
  
  http.end();
}

#endif // WIFI_HANDLER_H