# NxtAcre Desktop Application

A native desktop client for the NxtAcre Farm Management Platform built with Electron.js, React, TypeScript, and Vite.

## Features

- Offline access to critical farm management data
- System-level notifications for important events
- Enhanced performance for resource-intensive operations
- Desktop-specific user experience
- Data synchronization across platforms

### Implemented Features

- **Authentication**
  - Secure login and session management
  - Remember me functionality
  - Automatic session refresh

- **Data Synchronization**
  - Sync data between local and remote databases
  - Offline data access
  - Background synchronization when connection is restored
  - Sync status indicators

- **Offline Data Storage**
  - Local database using IndexedDB
  - Store and retrieve data for farms, tasks, equipment, inventory, finances, weather, and messages
  - Sync metadata management

- **Desktop Integration**
  - System tray integration
  - Window management
  - Desktop notifications

## Prerequisites

- Node.js (v16 or higher)
- npm (v7 or higher)

## Installation

1. Clone the repository (if you haven't already)
2. Navigate to the desktop directory:
   ```
   cd desktop
   ```
3. Install dependencies:
   ```
   npm install
   ```

## Development

To start the development server:

```
npm run electron:dev
```

This will start both the Vite development server for the React application and the Electron process.

## Building

To build the application for production:

```
npm run electron:build
```

This will create a production-ready build of the application in the `dist` directory.

## Packaging

To package the application for distribution:

```
npm run electron:package
```

To create platform-specific installers:

```
npm run electron:make
```

## Project Structure

```
desktop/
├── electron/                # Electron main process code
│   ├── main.ts              # Main entry point
│   ├── preload.ts           # Preload script for secure IPC
│   └── utils/               # Utility functions for the main process
├── src/                     # React application code (renderer process)
│   ├── assets/              # Static assets
│   ├── components/          # Reusable React components
│   ├── context/             # React context providers
│   ├── hooks/               # Custom React hooks
│   ├── pages/               # Page components
│   │   └── LoginPage.tsx    # Login page component
│   ├── services/            # API and service integrations
│   │   ├── authService.ts   # Authentication service
│   │   ├── dbService.ts     # Local database service
│   │   └── syncService.ts   # Data synchronization service
│   ├── types/               # TypeScript type definitions
│   │   └── user.ts          # User type definitions
│   ├── utils/               # Utility functions
│   ├── App.tsx              # Main application component
│   └── main.tsx             # Renderer entry point
├── shared/                  # Code shared between main and renderer processes
│   ├── constants/           # Shared constants
│   ├── types/               # Shared type definitions
│   └── utils/               # Shared utility functions
├── index.html               # HTML entry point
├── package.json             # Project dependencies and scripts
├── tsconfig.json            # TypeScript configuration
├── vite.config.ts           # Vite configuration
└── tailwind.config.js       # Tailwind CSS configuration
```

## Testing

### Authentication Testing

1. Launch the application using `npm run electron:dev`
2. You should see the login page
3. Enter the demo credentials:
   - Username: demo
   - Password: password
4. Check the "Remember me" checkbox if you want to stay logged in
5. Click the "Sign in" button
6. Verify that you are logged in and can see the main application
7. Test the logout functionality by clicking the "Logout" button in the header

### Data Synchronization Testing

1. Log in to the application
2. Click the "Sync Data" button in the header
3. Verify that the sync status indicator updates
4. Disconnect from the internet (turn off Wi-Fi or unplug ethernet)
5. Verify that the "Offline Mode" indicator appears in the header
6. Reconnect to the internet
7. Verify that the "Offline Mode" indicator disappears
8. Click the "Sync Data" button again to test synchronization after being offline

### System Tray Testing

1. Launch the application
2. Click the "Minimize to Tray" button in the header
3. Verify that the application window closes but the app continues running in the system tray
4. Click the tray icon to show the application window again
5. Right-click the tray icon to see the context menu
6. Test the "Open NxtAcre" and "Quit" options in the context menu

## Development Guidelines

- Use TypeScript for all new code
- Follow the existing code style and patterns
- Use Tailwind CSS for styling
- Keep components modular and reusable
- Use React hooks for state management
- Implement proper error handling
- Write meaningful comments

## License

This project is proprietary and confidential. Unauthorized copying, distribution, or use is strictly prohibited.
