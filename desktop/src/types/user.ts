/**
 * User interface representing a user in the system
 */
export interface User {
  /**
   * Unique identifier for the user
   */
  id: number;
  
  /**
   * Username for login
   */
  username: string;
  
  /**
   * User's email address
   */
  email: string;
  
  /**
   * User's first name
   */
  firstName: string;
  
  /**
   * User's last name
   */
  lastName: string;
  
  /**
   * User's role in the system (e.g., 'admin', 'user', 'manager')
   */
  role: string;
  
  /**
   * Optional profile image URL
   */
  profileImage?: string;
  
  /**
   * Optional last login timestamp
   */
  lastLogin?: Date;
  
  /**
   * Optional user preferences
   */
  preferences?: UserPreferences;
}

/**
 * User preferences interface
 */
export interface UserPreferences {
  /**
   * Theme preference ('light', 'dark', 'system')
   */
  theme?: 'light' | 'dark' | 'system';
  
  /**
   * Language preference (e.g., 'en', 'es', 'fr')
   */
  language?: string;
  
  /**
   * Notification preferences
   */
  notifications?: {
    /**
     * Whether to enable desktop notifications
     */
    desktop?: boolean;
    
    /**
     * Whether to enable sound for notifications
     */
    sound?: boolean;
    
    /**
     * Types of notifications to receive
     */
    types?: {
      tasks?: boolean;
      messages?: boolean;
      system?: boolean;
      weather?: boolean;
    };
  };
  
  /**
   * Default farm ID to load on startup
   */
  defaultFarmId?: number;
}