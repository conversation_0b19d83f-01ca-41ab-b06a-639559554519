/**
 * Database service for handling local data storage
 * This service provides methods for storing and retrieving data from IndexedDB
 */
class DbService {
  private static instance: DbService;
  private db: IDBDatabase | null = null;
  private readonly DB_NAME = 'nxtacre_local_db';
  private readonly DB_VERSION = 1;
  private isInitialized = false;
  private initPromise: Promise<boolean> | null = null;

  private constructor() {
    // Initialize the database
    this.initPromise = this.initDb();
  }

  /**
   * Get the singleton instance of DbService
   */
  public static getInstance(): DbService {
    if (!DbService.instance) {
      DbService.instance = new DbService();
    }
    return DbService.instance;
  }

  /**
   * Initialize the database
   * @returns Promise resolving to a boolean indicating success
   */
  private async initDb(): Promise<boolean> {
    if (this.isInitialized) {
      return true;
    }

    return new Promise<boolean>((resolve, reject) => {
      const request = indexedDB.open(this.DB_NAME, this.DB_VERSION);

      request.onerror = (event) => {
        console.error('Failed to open database:', event);
        reject(new Error('Failed to open database'));
      };

      request.onsuccess = (event) => {
        this.db = (event.target as IDBOpenDBRequest).result;
        this.isInitialized = true;
        resolve(true);
      };

      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;

        // Create object stores for each entity type
        if (!db.objectStoreNames.contains('farms')) {
          db.createObjectStore('farms', { keyPath: 'id' });
        }

        if (!db.objectStoreNames.contains('tasks')) {
          db.createObjectStore('tasks', { keyPath: 'id' });
        }

        if (!db.objectStoreNames.contains('equipment')) {
          db.createObjectStore('equipment', { keyPath: 'id' });
        }

        if (!db.objectStoreNames.contains('inventory')) {
          db.createObjectStore('inventory', { keyPath: 'id' });
        }

        if (!db.objectStoreNames.contains('finances')) {
          db.createObjectStore('finances', { keyPath: 'id' });
        }

        if (!db.objectStoreNames.contains('weather')) {
          db.createObjectStore('weather', { keyPath: 'id' });
        }

        if (!db.objectStoreNames.contains('messages')) {
          db.createObjectStore('messages', { keyPath: 'id' });
        }

        if (!db.objectStoreNames.contains('syncMetadata')) {
          db.createObjectStore('syncMetadata', { keyPath: 'key' });
        }
      };
    });
  }

  /**
   * Ensure the database is initialized
   * @returns Promise resolving when the database is initialized
   */
  private async ensureInitialized(): Promise<void> {
    if (!this.isInitialized) {
      if (!this.initPromise) {
        this.initPromise = this.initDb();
      }
      await this.initPromise;
    }
  }

  /**
   * Get all items from a store
   * @param storeName The name of the store
   * @returns Promise resolving to an array of items
   */
  public async getAll<T>(storeName: string): Promise<T[]> {
    await this.ensureInitialized();

    return new Promise<T[]>((resolve, reject) => {
      if (!this.db) {
        reject(new Error('Database not initialized'));
        return;
      }

      const transaction = this.db.transaction(storeName, 'readonly');
      const store = transaction.objectStore(storeName);
      const request = store.getAll();

      request.onerror = (event) => {
        console.error(`Failed to get all items from ${storeName}:`, event);
        reject(new Error(`Failed to get all items from ${storeName}`));
      };

      request.onsuccess = (event) => {
        resolve((event.target as IDBRequest<T[]>).result);
      };
    });
  }

  /**
   * Get an item by ID
   * @param storeName The name of the store
   * @param id The ID of the item
   * @returns Promise resolving to the item or null if not found
   */
  public async getById<T>(storeName: string, id: number | string): Promise<T | null> {
    await this.ensureInitialized();

    return new Promise<T | null>((resolve, reject) => {
      if (!this.db) {
        reject(new Error('Database not initialized'));
        return;
      }

      const transaction = this.db.transaction(storeName, 'readonly');
      const store = transaction.objectStore(storeName);
      const request = store.get(id);

      request.onerror = (event) => {
        console.error(`Failed to get item from ${storeName}:`, event);
        reject(new Error(`Failed to get item from ${storeName}`));
      };

      request.onsuccess = (event) => {
        resolve((event.target as IDBRequest<T>).result || null);
      };
    });
  }

  /**
   * Add or update an item
   * @param storeName The name of the store
   * @param item The item to add or update
   * @returns Promise resolving to the ID of the item
   */
  public async put<T extends { id: number | string }>(storeName: string, item: T): Promise<number | string> {
    await this.ensureInitialized();

    return new Promise<number | string>((resolve, reject) => {
      if (!this.db) {
        reject(new Error('Database not initialized'));
        return;
      }

      const transaction = this.db.transaction(storeName, 'readwrite');
      const store = transaction.objectStore(storeName);
      const request = store.put(item);

      request.onerror = (event) => {
        console.error(`Failed to put item in ${storeName}:`, event);
        reject(new Error(`Failed to put item in ${storeName}`));
      };

      request.onsuccess = (event) => {
        resolve(item.id);
      };
    });
  }

  /**
   * Delete an item by ID
   * @param storeName The name of the store
   * @param id The ID of the item
   * @returns Promise resolving when the item is deleted
   */
  public async delete(storeName: string, id: number | string): Promise<void> {
    await this.ensureInitialized();

    return new Promise<void>((resolve, reject) => {
      if (!this.db) {
        reject(new Error('Database not initialized'));
        return;
      }

      const transaction = this.db.transaction(storeName, 'readwrite');
      const store = transaction.objectStore(storeName);
      const request = store.delete(id);

      request.onerror = (event) => {
        console.error(`Failed to delete item from ${storeName}:`, event);
        reject(new Error(`Failed to delete item from ${storeName}`));
      };

      request.onsuccess = () => {
        resolve();
      };
    });
  }

  /**
   * Clear all items from a store
   * @param storeName The name of the store
   * @returns Promise resolving when the store is cleared
   */
  public async clear(storeName: string): Promise<void> {
    await this.ensureInitialized();

    return new Promise<void>((resolve, reject) => {
      if (!this.db) {
        reject(new Error('Database not initialized'));
        return;
      }

      const transaction = this.db.transaction(storeName, 'readwrite');
      const store = transaction.objectStore(storeName);
      const request = store.clear();

      request.onerror = (event) => {
        console.error(`Failed to clear ${storeName}:`, event);
        reject(new Error(`Failed to clear ${storeName}`));
      };

      request.onsuccess = () => {
        resolve();
      };
    });
  }

  /**
   * Get the count of items in a store
   * @param storeName The name of the store
   * @returns Promise resolving to the count
   */
  public async count(storeName: string): Promise<number> {
    await this.ensureInitialized();

    return new Promise<number>((resolve, reject) => {
      if (!this.db) {
        reject(new Error('Database not initialized'));
        return;
      }

      const transaction = this.db.transaction(storeName, 'readonly');
      const store = transaction.objectStore(storeName);
      const request = store.count();

      request.onerror = (event) => {
        console.error(`Failed to count items in ${storeName}:`, event);
        reject(new Error(`Failed to count items in ${storeName}`));
      };

      request.onsuccess = (event) => {
        resolve((event.target as IDBRequest<number>).result);
      };
    });
  }

  /**
   * Set sync metadata
   * @param key The metadata key
   * @param value The metadata value
   * @returns Promise resolving when the metadata is set
   */
  public async setSyncMetadata(key: string, value: any): Promise<void> {
    await this.put('syncMetadata', { key, value });
  }

  /**
   * Get sync metadata
   * @param key The metadata key
   * @returns Promise resolving to the metadata value or null if not found
   */
  public async getSyncMetadata(key: string): Promise<any> {
    const metadata = await this.getById<{ key: string; value: any }>('syncMetadata', key);
    return metadata ? metadata.value : null;
  }
}

export default DbService.getInstance();