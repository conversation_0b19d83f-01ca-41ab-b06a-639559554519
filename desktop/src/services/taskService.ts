import { Task, TaskStatus, TaskPriority, TaskFilter, CreateTaskRequest, UpdateTaskRequest } from '../types/task';
import syncService from './syncService';

// Mock data for offline development
const mockTasks: Task[] = [
  {
    id: 1,
    title: 'Inspect North Field Irrigation',
    description: 'Check the irrigation system in the north field for any leaks or blockages.',
    status: TaskStatus.TODO,
    priority: TaskPriority.HIGH,
    dueDate: new Date(new Date().getTime() + 86400000), // Tomorrow
    assignedTo: 1,
    assignedToName: '<PERSON>e',
    farmId: 1,
    fieldId: 1,
    createdAt: new Date(),
    updatedAt: new Date(),
    completedAt: null
  },
  {
    id: 2,
    title: 'Order Fertilizer',
    description: 'Order 500kg of organic fertilizer for the upcoming planting season.',
    status: TaskStatus.IN_PROGRESS,
    priority: TaskPriority.MEDIUM,
    dueDate: new Date(new Date().getTime() + 172800000), // Day after tomorrow
    assignedTo: 2,
    assignedToName: '<PERSON>',
    farmId: 1,
    createdAt: new Date(),
    updatedAt: new Date(),
    completedAt: null
  },
  {
    id: 3,
    title: 'Repair Tractor',
    description: 'Schedule maintenance for the John Deere tractor.',
    status: TaskStatus.COMPLETED,
    priority: TaskPriority.URGENT,
    dueDate: new Date(new Date().getTime() - 86400000), // Yesterday
    assignedTo: 1,
    assignedToName: 'John Doe',
    farmId: 1,
    equipmentId: 1,
    createdAt: new Date(new Date().getTime() - 172800000), // 2 days ago
    updatedAt: new Date(new Date().getTime() - 86400000), // Yesterday
    completedAt: new Date(new Date().getTime() - 86400000) // Yesterday
  }
];

// Local storage key for tasks
const TASKS_STORAGE_KEY = 'nxtacre_tasks';

// Helper function to get tasks from local storage
const getTasksFromStorage = (): Task[] => {
  const tasksJson = localStorage.getItem(TASKS_STORAGE_KEY);
  if (tasksJson) {
    try {
      // Parse dates from JSON
      const tasks = JSON.parse(tasksJson, (key, value) => {
        const dateKeys = ['dueDate', 'createdAt', 'updatedAt', 'completedAt'];
        if (dateKeys.includes(key) && value) {
          return new Date(value);
        }
        return value;
      });
      return tasks;
    } catch (error) {
      console.error('Error parsing tasks from local storage:', error);
      return [...mockTasks];
    }
  }
  return [...mockTasks];
};

// Helper function to save tasks to local storage
const saveTasksToStorage = (tasks: Task[]): void => {
  localStorage.setItem(TASKS_STORAGE_KEY, JSON.stringify(tasks));
};

// Task service methods
const taskService = {
  // Get all tasks with optional filtering
  getTasks: async (filter?: TaskFilter): Promise<Task[]> => {
    try {
      // In a real app, this would make an API call if online
      // For now, we'll use local storage/mock data
      const isOffline = await window.electronAPI.getOfflineStatus();
      
      if (!isOffline) {
        // TODO: Implement API call when online
        // For now, just use local storage
      }
      
      let tasks = getTasksFromStorage();
      
      // Apply filters if provided
      if (filter) {
        if (filter.status !== undefined) {
          tasks = tasks.filter(task => task.status === filter.status);
        }
        if (filter.priority !== undefined) {
          tasks = tasks.filter(task => task.priority === filter.priority);
        }
        if (filter.assignedTo !== undefined) {
          tasks = tasks.filter(task => task.assignedTo === filter.assignedTo);
        }
        if (filter.farmId !== undefined) {
          tasks = tasks.filter(task => task.farmId === filter.farmId);
        }
        if (filter.fieldId !== undefined) {
          tasks = tasks.filter(task => task.fieldId === filter.fieldId);
        }
        if (filter.equipmentId !== undefined) {
          tasks = tasks.filter(task => task.equipmentId === filter.equipmentId);
        }
        if (filter.startDate !== undefined) {
          tasks = tasks.filter(task => task.dueDate && task.dueDate >= filter.startDate!);
        }
        if (filter.endDate !== undefined) {
          tasks = tasks.filter(task => task.dueDate && task.dueDate <= filter.endDate!);
        }
        if (filter.searchTerm !== undefined && filter.searchTerm.trim() !== '') {
          const searchTerm = filter.searchTerm.toLowerCase();
          tasks = tasks.filter(task => 
            task.title.toLowerCase().includes(searchTerm) || 
            task.description.toLowerCase().includes(searchTerm)
          );
        }
      }
      
      return tasks;
    } catch (error) {
      console.error('Error getting tasks:', error);
      throw error;
    }
  },
  
  // Get a single task by ID
  getTaskById: async (taskId: number): Promise<Task | null> => {
    try {
      const isOffline = await window.electronAPI.getOfflineStatus();
      
      if (!isOffline) {
        // TODO: Implement API call when online
      }
      
      const tasks = getTasksFromStorage();
      const task = tasks.find(t => t.id === taskId);
      
      return task || null;
    } catch (error) {
      console.error(`Error getting task with ID ${taskId}:`, error);
      throw error;
    }
  },
  
  // Create a new task
  createTask: async (taskData: CreateTaskRequest): Promise<Task> => {
    try {
      const isOffline = await window.electronAPI.getOfflineStatus();
      
      if (!isOffline) {
        // TODO: Implement API call when online
        // For now, just use local storage
      }
      
      const tasks = getTasksFromStorage();
      
      // Generate a new ID (in a real app, the server would do this)
      const newId = tasks.length > 0 ? Math.max(...tasks.map(t => t.id)) + 1 : 1;
      
      const now = new Date();
      const newTask: Task = {
        id: newId,
        ...taskData,
        createdAt: now,
        updatedAt: now,
        completedAt: null
      };
      
      tasks.push(newTask);
      saveTasksToStorage(tasks);
      
      // Add to sync queue if offline
      if (isOffline) {
        syncService.addToSyncQueue('tasks', 'create', newTask);
      }
      
      return newTask;
    } catch (error) {
      console.error('Error creating task:', error);
      throw error;
    }
  },
  
  // Update an existing task
  updateTask: async (taskData: UpdateTaskRequest): Promise<Task> => {
    try {
      const isOffline = await window.electronAPI.getOfflineStatus();
      
      if (!isOffline) {
        // TODO: Implement API call when online
      }
      
      const tasks = getTasksFromStorage();
      const taskIndex = tasks.findIndex(t => t.id === taskData.id);
      
      if (taskIndex === -1) {
        throw new Error(`Task with ID ${taskData.id} not found`);
      }
      
      const updatedTask: Task = {
        ...tasks[taskIndex],
        ...taskData,
        updatedAt: new Date()
      };
      
      tasks[taskIndex] = updatedTask;
      saveTasksToStorage(tasks);
      
      // Add to sync queue if offline
      if (isOffline) {
        syncService.addToSyncQueue('tasks', 'update', updatedTask);
      }
      
      return updatedTask;
    } catch (error) {
      console.error(`Error updating task with ID ${taskData.id}:`, error);
      throw error;
    }
  },
  
  // Delete a task
  deleteTask: async (taskId: number): Promise<boolean> => {
    try {
      const isOffline = await window.electronAPI.getOfflineStatus();
      
      if (!isOffline) {
        // TODO: Implement API call when online
      }
      
      const tasks = getTasksFromStorage();
      const taskIndex = tasks.findIndex(t => t.id === taskId);
      
      if (taskIndex === -1) {
        throw new Error(`Task with ID ${taskId} not found`);
      }
      
      const deletedTask = tasks[taskIndex];
      tasks.splice(taskIndex, 1);
      saveTasksToStorage(tasks);
      
      // Add to sync queue if offline
      if (isOffline) {
        syncService.addToSyncQueue('tasks', 'delete', { id: taskId });
      }
      
      return true;
    } catch (error) {
      console.error(`Error deleting task with ID ${taskId}:`, error);
      throw error;
    }
  }
};

export default taskService;