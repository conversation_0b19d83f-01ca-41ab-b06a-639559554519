import React, { useState, useEffect, useRef } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import dbService from '../services/dbService';
import syncService from '../services/syncService';

interface Field {
  id: number;
  farmId: number;
  name: string;
  size: number;
  sizeUnit: 'acres' | 'hectares';
  cropType: string;
  status: 'active' | 'fallow' | 'planned';
  coordinates: {
    lat: number;
    lng: number;
  }[];
  createdAt: string;
  updatedAt: string;
}

interface Farm {
  id: number;
  name: string;
  location: string;
}

// Mock map component since we can't use actual Google Maps in this environment
const MockMap: React.FC<{
  field: Field;
  onCoordinatesChange: (coordinates: { lat: number; lng: number }[]) => void;
}> = ({ field, onCoordinatesChange }) => {
  const [isEditing, setIsEditing] = useState(false);
  
  // In a real implementation, this would be replaced with actual map interactions
  const handleAddPoint = () => {
    // Add a mock point near the existing points or at a default location
    const newCoordinates = [...field.coordinates];
    
    if (newCoordinates.length === 0) {
      // Default location if no points exist
      newCoordinates.push({ lat: 37.7749, lng: -122.4194 });
    } else {
      // Add a point slightly offset from the last point
      const lastPoint = newCoordinates[newCoordinates.length - 1];
      newCoordinates.push({
        lat: lastPoint.lat + 0.001 * (Math.random() - 0.5),
        lng: lastPoint.lng + 0.001 * (Math.random() - 0.5)
      });
    }
    
    onCoordinatesChange(newCoordinates);
  };
  
  const handleRemoveLastPoint = () => {
    if (field.coordinates.length > 0) {
      const newCoordinates = [...field.coordinates];
      newCoordinates.pop();
      onCoordinatesChange(newCoordinates);
    }
  };
  
  const handleClearPoints = () => {
    onCoordinatesChange([]);
  };
  
  return (
    <div className="bg-gray-200 dark:bg-gray-700 rounded-lg p-4 h-96 flex flex-col">
      <div className="flex justify-between mb-4">
        <h3 className="text-lg font-semibold">Field Map</h3>
        <div>
          <button
            onClick={() => setIsEditing(!isEditing)}
            className={`px-3 py-1 rounded mr-2 ${
              isEditing 
                ? 'bg-yellow-500 hover:bg-yellow-600 text-white' 
                : 'bg-blue-500 hover:bg-blue-600 text-white'
            }`}
          >
            {isEditing ? 'Finish Editing' : 'Edit Boundaries'}
          </button>
        </div>
      </div>
      
      {isEditing ? (
        <div className="flex-1 flex flex-col">
          <div className="flex-1 bg-white dark:bg-gray-800 rounded-lg p-4 mb-4 overflow-auto">
            <div className="text-sm mb-2 font-medium">Current Coordinates:</div>
            {field.coordinates.length === 0 ? (
              <p className="text-gray-500 dark:text-gray-400 italic">No coordinates defined yet.</p>
            ) : (
              <table className="w-full text-sm">
                <thead>
                  <tr>
                    <th className="text-left py-2">Point</th>
                    <th className="text-left py-2">Latitude</th>
                    <th className="text-left py-2">Longitude</th>
                  </tr>
                </thead>
                <tbody>
                  {field.coordinates.map((coord, index) => (
                    <tr key={index} className="border-t border-gray-200 dark:border-gray-700">
                      <td className="py-2">{index + 1}</td>
                      <td className="py-2">{coord.lat.toFixed(6)}</td>
                      <td className="py-2">{coord.lng.toFixed(6)}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            )}
          </div>
          
          <div className="flex space-x-2">
            <button
              onClick={handleAddPoint}
              className="bg-green-600 hover:bg-green-500 text-white px-4 py-2 rounded flex-1"
            >
              Add Point
            </button>
            <button
              onClick={handleRemoveLastPoint}
              className="bg-red-600 hover:bg-red-500 text-white px-4 py-2 rounded flex-1"
              disabled={field.coordinates.length === 0}
            >
              Remove Last Point
            </button>
            <button
              onClick={handleClearPoints}
              className="bg-gray-600 hover:bg-gray-500 text-white px-4 py-2 rounded flex-1"
              disabled={field.coordinates.length === 0}
            >
              Clear All
            </button>
          </div>
        </div>
      ) : (
        <div className="flex-1 flex flex-col items-center justify-center bg-white dark:bg-gray-800 rounded-lg">
          {field.coordinates.length === 0 ? (
            <div className="text-center">
              <p className="text-gray-500 dark:text-gray-400 mb-4">No field boundaries defined yet.</p>
              <button
                onClick={() => setIsEditing(true)}
                className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded"
              >
                Define Field Boundaries
              </button>
            </div>
          ) : (
            <div className="text-center">
              <div className="w-full h-64 bg-gray-300 dark:bg-gray-600 rounded-lg mb-4 relative">
                <div className="absolute inset-0 flex items-center justify-center">
                  <p className="text-gray-600 dark:text-gray-300">
                    Map visualization would appear here
                  </p>
                </div>
                {/* This is where the actual map would be rendered */}
                <div className="absolute bottom-2 right-2 bg-white dark:bg-gray-700 px-2 py-1 rounded text-xs">
                  {field.coordinates.length} points defined
                </div>
              </div>
              <p className="text-gray-500 dark:text-gray-400">
                Field area: Approximately {field.size} {field.sizeUnit}
              </p>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

const FieldMapPage: React.FC = () => {
  const { farmId, fieldId } = useParams<{ farmId: string; fieldId: string }>();
  const navigate = useNavigate();
  const [farm, setFarm] = useState<Farm | null>(null);
  const [field, setField] = useState<Field | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [isSaving, setIsSaving] = useState<boolean>(false);
  const [hasChanges, setHasChanges] = useState<boolean>(false);
  
  useEffect(() => {
    const loadData = async () => {
      if (!farmId || !fieldId) {
        setError('Farm ID and Field ID are required');
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        setError(null);
        
        // Get farm from local database
        const farmData = await dbService.getInstance().getById<Farm>('farms', parseInt(farmId, 10));
        
        if (!farmData) {
          setError('Farm not found');
          setIsLoading(false);
          return;
        }
        
        setFarm(farmData);
        
        // Get field from local database
        const fieldData = await dbService.getInstance().getById<Field>('fields', parseInt(fieldId, 10));
        
        if (!fieldData) {
          setError('Field not found');
          setIsLoading(false);
          return;
        }
        
        setField(fieldData);
      } catch (err) {
        console.error('Failed to load data:', err);
        setError('Failed to load field data');
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, [farmId, fieldId]);

  const handleCoordinatesChange = (coordinates: { lat: number; lng: number }[]) => {
    if (field) {
      setField({
        ...field,
        coordinates
      });
      setHasChanges(true);
    }
  };

  const handleSave = async () => {
    if (!field) {
      return;
    }
    
    try {
      setIsSaving(true);
      setError(null);
      
      const updatedField: Field = {
        ...field,
        updatedAt: new Date().toISOString()
      };
      
      // Save to local database
      await dbService.getInstance().put<Field>('fields', updatedField);
      
      // Add to sync queue
      syncService.getInstance().addToSyncQueue({
        type: 'update',
        entity: 'fields',
        id: updatedField.id,
        data: updatedField
      });
      
      setField(updatedField);
      setHasChanges(false);
    } catch (err) {
      console.error('Failed to save field:', err);
      setError('Failed to save field boundaries');
    } finally {
      setIsSaving(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-700 mx-auto"></div>
          <p className="mt-4 text-gray-600 dark:text-gray-400">Loading field map...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6">
        <div className="bg-red-100 dark:bg-red-900 text-red-700 dark:text-red-200 p-4 rounded-md mb-6">
          <p>{error}</p>
        </div>
        <button
          onClick={() => navigate(-1)}
          className="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded"
        >
          Go Back
        </button>
      </div>
    );
  }

  if (!field || !farm) {
    return (
      <div className="p-6">
        <div className="bg-yellow-100 dark:bg-yellow-900 text-yellow-700 dark:text-yellow-200 p-4 rounded-md mb-6">
          <p>Field or farm not found</p>
        </div>
        <button
          onClick={() => navigate(-1)}
          className="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded"
        >
          Go Back
        </button>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold">Field Map</h1>
          <p className="text-gray-600 dark:text-gray-400">
            Farm: {farm.name} | Field: {field.name}
          </p>
        </div>
        <div className="flex space-x-2">
          {hasChanges && (
            <button
              onClick={handleSave}
              className="bg-green-600 hover:bg-green-500 text-white px-4 py-2 rounded"
              disabled={isSaving}
            >
              {isSaving ? 'Saving...' : 'Save Changes'}
            </button>
          )}
          <button
            onClick={() => navigate(`/farms/${farmId}/fields`)}
            className="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded"
          >
            Back to Fields
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2">
          <MockMap field={field} onCoordinatesChange={handleCoordinatesChange} />
        </div>
        
        <div>
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold mb-4">Field Information</h2>
            <div className="space-y-3">
              <div>
                <span className="text-gray-500 dark:text-gray-400">Name:</span>
                <span className="ml-2 font-medium">{field.name}</span>
              </div>
              <div>
                <span className="text-gray-500 dark:text-gray-400">Size:</span>
                <span className="ml-2">{field.size} {field.sizeUnit}</span>
              </div>
              <div>
                <span className="text-gray-500 dark:text-gray-400">Crop Type:</span>
                <span className="ml-2">{field.cropType || 'Not specified'}</span>
              </div>
              <div>
                <span className="text-gray-500 dark:text-gray-400">Status:</span>
                <span className={`ml-2 px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                  ${field.status === 'active' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' : 
                    field.status === 'fallow' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' : 
                    'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'}`}
                >
                  {field.status.charAt(0).toUpperCase() + field.status.slice(1)}
                </span>
              </div>
              <div>
                <span className="text-gray-500 dark:text-gray-400">Created:</span>
                <span className="ml-2">{new Date(field.createdAt).toLocaleDateString()}</span>
              </div>
              <div>
                <span className="text-gray-500 dark:text-gray-400">Last Updated:</span>
                <span className="ml-2">{new Date(field.updatedAt).toLocaleDateString()}</span>
              </div>
            </div>
            
            <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
              <h3 className="text-lg font-semibold mb-2">Actions</h3>
              <div className="space-y-2">
                <button
                  onClick={() => navigate(`/farms/${farmId}/fields/${fieldId}/edit`)}
                  className="w-full bg-blue-600 hover:bg-blue-500 text-white px-4 py-2 rounded"
                >
                  Edit Field Details
                </button>
                <button
                  onClick={() => navigate(`/farms/${farmId}/fields/${fieldId}/tasks`)}
                  className="w-full bg-purple-600 hover:bg-purple-500 text-white px-4 py-2 rounded"
                >
                  View Field Tasks
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FieldMapPage;