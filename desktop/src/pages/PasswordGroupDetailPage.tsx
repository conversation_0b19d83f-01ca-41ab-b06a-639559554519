import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { <PERSON>aLock, FaPlus, FaEdit, FaTrash, FaEye, FaEyeSlash, FaCopy, FaArrowLeft } from 'react-icons/fa';
import authService from '../services/authService';
import { User } from '../types/user';

// Define types for password groups and passwords
interface PasswordGroup {
  id: string;
  farm_id: string;
  name: string;
  description?: string;
  created_by: string;
  created_at: string;
  updated_at: string;
}

interface Password {
  id: string;
  group_id: string;
  name: string;
  username: string;
  password: string;
  url?: string;
  notes?: string;
  has_2fa: boolean;
  totp_secret?: string;
  encryption_key_id: string;
  encryption_iv: string;
  created_by: string;
  created_at: string;
  updated_at: string;
}

interface Permission {
  id: string;
  group_id: string;
  role_id?: string;
  user_id?: string;
  permission_type: 'view' | 'edit' | 'manage';
  created_at: string;
  updated_at: string;
}

const PasswordGroupDetailPage: React.FC = () => {
  const { groupId } = useParams<{ groupId: string }>();
  const navigate = useNavigate();
  
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [group, setGroup] = useState<PasswordGroup | null>(null);
  const [passwords, setPasswords] = useState<Password[]>([]);
  const [userPermission, setUserPermission] = useState<'view' | 'edit' | 'manage'>('view');
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [isOffline, setIsOffline] = useState(false);
  const [showPasswordMap, setShowPasswordMap] = useState<Record<string, boolean>>({});
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedPassword, setSelectedPassword] = useState<Password | null>(null);
  const [newPassword, setNewPassword] = useState<Partial<Password>>({
    name: '',
    username: '',
    password: '',
    url: '',
    notes: '',
    has_2fa: false,
    totp_secret: '',
  });

  // Check offline status and get current user
  useEffect(() => {
    window.electronAPI.getOfflineStatus().then(status => {
      setIsOffline(status);
    }).catch(err => {
      console.error('Failed to get offline status:', err);
    });

    // Get current user
    const user = authService.getCurrentUser();
    setCurrentUser(user);
  }, []);

  // Fetch group details and passwords
  useEffect(() => {
    if (!groupId) return;

    setLoading(true);
    setError(null);

    // First try to get from local storage
    const storedGroup = localStorage.getItem(`password_group_${groupId}`);
    const storedPasswords = localStorage.getItem(`passwords_${groupId}`);
    const storedPermission = localStorage.getItem(`permission_${groupId}`);
    
    if (storedGroup && storedPasswords && storedPermission) {
      setGroup(JSON.parse(storedGroup));
      setPasswords(JSON.parse(storedPasswords));
      setUserPermission(JSON.parse(storedPermission) as 'view' | 'edit' | 'manage');
      setLoading(false);
    }

    // If we're online, also fetch from server to get the latest
    if (!isOffline) {
      // In a real implementation, this would be a call to the API
      // For now, we'll simulate it with a timeout
      setTimeout(() => {
        // This is where we would make the actual API call
        // For now, we'll just use some dummy data if we don't have stored data
        if (!storedGroup || !storedPasswords) {
          const dummyGroup: PasswordGroup = {
            id: groupId,
            farm_id: '1',
            name: 'Farm Accounts',
            description: 'Login credentials for farm-related services',
            created_by: currentUser?.id || '',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          };

          const dummyPasswords: Password[] = [
            {
              id: '1',
              group_id: groupId,
              name: 'Farm Website',
              username: '<EMAIL>',
              password: 'encrypted_password_1',
              url: 'https://farm-website.example.com',
              notes: 'Main farm website admin account',
              has_2fa: true,
              totp_secret: 'encrypted_totp_secret_1',
              encryption_key_id: 'key1',
              encryption_iv: 'iv1',
              created_by: currentUser?.id || '',
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString(),
            },
            {
              id: '2',
              group_id: groupId,
              name: 'Supplier Portal',
              username: 'farm_account',
              password: 'encrypted_password_2',
              url: 'https://supplier.example.com',
              notes: 'Account for ordering supplies',
              has_2fa: false,
              encryption_key_id: 'key2',
              encryption_iv: 'iv2',
              created_by: currentUser?.id || '',
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString(),
            }
          ];

          // Determine user permission (in a real app, this would come from the server)
          const dummyPermission: 'view' | 'edit' | 'manage' = 'manage';

          setGroup(dummyGroup);
          setPasswords(dummyPasswords);
          setUserPermission(dummyPermission);
          
          // Store in local storage for offline access
          localStorage.setItem(`password_group_${groupId}`, JSON.stringify(dummyGroup));
          localStorage.setItem(`passwords_${groupId}`, JSON.stringify(dummyPasswords));
          localStorage.setItem(`permission_${groupId}`, JSON.stringify(dummyPermission));
        }
        
        setLoading(false);
      }, 1000);
    }
  }, [groupId, currentUser, isOffline]);

  // Initialize password visibility map
  useEffect(() => {
    const initialMap: Record<string, boolean> = {};
    passwords.forEach(password => {
      initialMap[password.id] = false;
    });
    setShowPasswordMap(initialMap);
  }, [passwords]);

  // Function to toggle password visibility
  const togglePasswordVisibility = (passwordId: string) => {
    setShowPasswordMap(prev => ({
      ...prev,
      [passwordId]: !prev[passwordId]
    }));
  };

  // Function to copy text to clipboard
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text).then(() => {
      window.electronAPI.showNotification('Copied', 'Text copied to clipboard');
    }).catch(err => {
      console.error('Failed to copy text:', err);
      window.electronAPI.showNotification('Copy Failed', 'Failed to copy text to clipboard');
    });
  };

  // Function to handle adding a new password
  const handleAddPassword = () => {
    if (userPermission === 'view') {
      window.electronAPI.showNotification('Permission Denied', 'You do not have permission to add passwords');
      return;
    }

    // In a real implementation, this would be a call to the API
    // For now, we'll simulate it with a local update
    const newPasswordEntry: Password = {
      id: `new_${Date.now()}`,
      group_id: groupId || '',
      name: newPassword.name || '',
      username: newPassword.username || '',
      password: newPassword.password || '',
      url: newPassword.url || '',
      notes: newPassword.notes || '',
      has_2fa: newPassword.has_2fa || false,
      totp_secret: newPassword.totp_secret || '',
      encryption_key_id: `key_${Date.now()}`,
      encryption_iv: `iv_${Date.now()}`,
      created_by: currentUser?.id || '',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };

    const updatedPasswords = [...passwords, newPasswordEntry];
    setPasswords(updatedPasswords);
    
    // Update local storage
    localStorage.setItem(`passwords_${groupId}`, JSON.stringify(updatedPasswords));
    
    // Reset form and close modal
    setNewPassword({
      name: '',
      username: '',
      password: '',
      url: '',
      notes: '',
      has_2fa: false,
      totp_secret: '',
    });
    setShowAddModal(false);
    
    // Show notification
    window.electronAPI.showNotification('Password Added', 'The password has been added successfully');
  };

  // Function to handle editing a password
  const handleEditPassword = () => {
    if (userPermission === 'view') {
      window.electronAPI.showNotification('Permission Denied', 'You do not have permission to edit passwords');
      return;
    }

    if (!selectedPassword) return;

    // In a real implementation, this would be a call to the API
    // For now, we'll simulate it with a local update
    const updatedPasswords = passwords.map(p => 
      p.id === selectedPassword.id ? { ...selectedPassword } : p
    );
    
    setPasswords(updatedPasswords);
    
    // Update local storage
    localStorage.setItem(`passwords_${groupId}`, JSON.stringify(updatedPasswords));
    
    // Reset selected password and close modal
    setSelectedPassword(null);
    setShowEditModal(false);
    
    // Show notification
    window.electronAPI.showNotification('Password Updated', 'The password has been updated successfully');
  };

  // Function to handle deleting a password
  const handleDeletePassword = () => {
    if (userPermission !== 'manage') {
      window.electronAPI.showNotification('Permission Denied', 'You do not have permission to delete passwords');
      return;
    }

    if (!selectedPassword) return;

    // In a real implementation, this would be a call to the API
    // For now, we'll simulate it with a local update
    const updatedPasswords = passwords.filter(p => p.id !== selectedPassword.id);
    
    setPasswords(updatedPasswords);
    
    // Update local storage
    localStorage.setItem(`passwords_${groupId}`, JSON.stringify(updatedPasswords));
    
    // Reset selected password and close modal
    setSelectedPassword(null);
    setShowDeleteModal(false);
    
    // Show notification
    window.electronAPI.showNotification('Password Deleted', 'The password has been deleted successfully');
  };

  // Render add password modal
  const renderAddPasswordModal = () => {
    if (!showAddModal) return null;

    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 w-full max-w-md">
          <h2 className="text-xl font-semibold mb-4">Add New Password</h2>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Name</label>
              <input
                type="text"
                value={newPassword.name || ''}
                onChange={(e) => setNewPassword({...newPassword, name: e.target.value})}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                placeholder="e.g., Farm Website"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Username</label>
              <input
                type="text"
                value={newPassword.username || ''}
                onChange={(e) => setNewPassword({...newPassword, username: e.target.value})}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                placeholder="e.g., <EMAIL>"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Password</label>
              <input
                type="password"
                value={newPassword.password || ''}
                onChange={(e) => setNewPassword({...newPassword, password: e.target.value})}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                placeholder="Enter password"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">URL (Optional)</label>
              <input
                type="text"
                value={newPassword.url || ''}
                onChange={(e) => setNewPassword({...newPassword, url: e.target.value})}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                placeholder="e.g., https://example.com"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Notes (Optional)</label>
              <textarea
                value={newPassword.notes || ''}
                onChange={(e) => setNewPassword({...newPassword, notes: e.target.value})}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                placeholder="Additional information"
                rows={3}
              />
            </div>
            
            <div className="flex items-center">
              <input
                type="checkbox"
                id="has2fa"
                checked={newPassword.has_2fa || false}
                onChange={(e) => setNewPassword({...newPassword, has_2fa: e.target.checked})}
                className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
              />
              <label htmlFor="has2fa" className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                Has 2FA
              </label>
            </div>
            
            {newPassword.has_2fa && (
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">TOTP Secret</label>
                <input
                  type="text"
                  value={newPassword.totp_secret || ''}
                  onChange={(e) => setNewPassword({...newPassword, totp_secret: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  placeholder="Enter TOTP secret"
                />
              </div>
            )}
          </div>
          
          <div className="flex justify-end space-x-2 mt-6">
            <button
              onClick={() => setShowAddModal(false)}
              className="px-4 py-2 bg-gray-300 dark:bg-gray-600 text-gray-800 dark:text-white rounded hover:bg-gray-400 dark:hover:bg-gray-500"
            >
              Cancel
            </button>
            <button
              onClick={handleAddPassword}
              className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-500"
              disabled={!newPassword.name || !newPassword.username || !newPassword.password}
            >
              Add Password
            </button>
          </div>
        </div>
      </div>
    );
  };

  // Render edit password modal
  const renderEditPasswordModal = () => {
    if (!showEditModal || !selectedPassword) return null;

    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 w-full max-w-md">
          <h2 className="text-xl font-semibold mb-4">Edit Password</h2>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Name</label>
              <input
                type="text"
                value={selectedPassword.name}
                onChange={(e) => setSelectedPassword({...selectedPassword, name: e.target.value})}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Username</label>
              <input
                type="text"
                value={selectedPassword.username}
                onChange={(e) => setSelectedPassword({...selectedPassword, username: e.target.value})}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Password</label>
              <input
                type={showPasswordMap[selectedPassword.id] ? 'text' : 'password'}
                value={selectedPassword.password}
                onChange={(e) => setSelectedPassword({...selectedPassword, password: e.target.value})}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              />
              <button
                onClick={() => togglePasswordVisibility(selectedPassword.id)}
                className="mt-1 text-sm text-green-600 hover:text-green-500"
              >
                {showPasswordMap[selectedPassword.id] ? 'Hide Password' : 'Show Password'}
              </button>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">URL (Optional)</label>
              <input
                type="text"
                value={selectedPassword.url || ''}
                onChange={(e) => setSelectedPassword({...selectedPassword, url: e.target.value})}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Notes (Optional)</label>
              <textarea
                value={selectedPassword.notes || ''}
                onChange={(e) => setSelectedPassword({...selectedPassword, notes: e.target.value})}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                rows={3}
              />
            </div>
            
            <div className="flex items-center">
              <input
                type="checkbox"
                id="editHas2fa"
                checked={selectedPassword.has_2fa}
                onChange={(e) => setSelectedPassword({...selectedPassword, has_2fa: e.target.checked})}
                className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
              />
              <label htmlFor="editHas2fa" className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                Has 2FA
              </label>
            </div>
            
            {selectedPassword.has_2fa && (
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">TOTP Secret</label>
                <input
                  type="text"
                  value={selectedPassword.totp_secret || ''}
                  onChange={(e) => setSelectedPassword({...selectedPassword, totp_secret: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                />
              </div>
            )}
          </div>
          
          <div className="flex justify-end space-x-2 mt-6">
            <button
              onClick={() => {
                setSelectedPassword(null);
                setShowEditModal(false);
              }}
              className="px-4 py-2 bg-gray-300 dark:bg-gray-600 text-gray-800 dark:text-white rounded hover:bg-gray-400 dark:hover:bg-gray-500"
            >
              Cancel
            </button>
            <button
              onClick={handleEditPassword}
              className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-500"
              disabled={!selectedPassword.name || !selectedPassword.username || !selectedPassword.password}
            >
              Save Changes
            </button>
          </div>
        </div>
      </div>
    );
  };

  // Render delete password confirmation modal
  const renderDeletePasswordModal = () => {
    if (!showDeleteModal || !selectedPassword) return null;

    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 w-full max-w-md">
          <h2 className="text-xl font-semibold mb-4">Delete Password</h2>
          <p className="text-gray-600 dark:text-gray-400 mb-6">
            Are you sure you want to delete the password for "{selectedPassword.name}"? This action cannot be undone.
          </p>
          
          <div className="flex justify-end space-x-2">
            <button
              onClick={() => {
                setSelectedPassword(null);
                setShowDeleteModal(false);
              }}
              className="px-4 py-2 bg-gray-300 dark:bg-gray-600 text-gray-800 dark:text-white rounded hover:bg-gray-400 dark:hover:bg-gray-500"
            >
              Cancel
            </button>
            <button
              onClick={handleDeletePassword}
              className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-500"
            >
              Delete
            </button>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="p-6">
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center">
          <button
            onClick={() => navigate('/password-manager')}
            className="mr-4 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200"
          >
            <FaArrowLeft className="text-xl" />
          </button>
          <div>
            <h1 className="text-2xl font-bold flex items-center">
              <FaLock className="mr-2 text-green-600" /> {group?.name || 'Password Group'}
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              {group?.description || 'No description'}
            </p>
          </div>
        </div>
        <div>
          {userPermission !== 'view' && (
            <button
              onClick={() => setShowAddModal(true)}
              className="flex items-center px-4 py-2 bg-green-600 text-white rounded hover:bg-green-500"
            >
              <FaPlus className="mr-2" /> Add Password
            </button>
          )}
        </div>
      </div>

      {/* Permission indicator */}
      <div className="mb-6">
        <span className={`inline-block px-3 py-1 rounded-full text-sm font-medium ${
          userPermission === 'manage' ? 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100' :
          userPermission === 'edit' ? 'bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100' :
          'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-100'
        }`}>
          Your permission: {userPermission.charAt(0).toUpperCase() + userPermission.slice(1)}
        </span>
      </div>

      {/* Error message */}
      {error && (
        <div className="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6">
          <p>{error}</p>
        </div>
      )}

      {/* Passwords list */}
      {loading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-green-600"></div>
        </div>
      ) : passwords.length === 0 ? (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-8 text-center">
          <div className="text-gray-500 dark:text-gray-400 mb-4">
            <FaLock className="mx-auto text-5xl mb-4" />
            <h3 className="text-xl font-semibold mb-2">No Passwords</h3>
            <p>This group doesn't have any passwords yet.</p>
          </div>
          {userPermission !== 'view' && (
            <button
              onClick={() => setShowAddModal(true)}
              className="mt-4 px-4 py-2 bg-green-600 text-white rounded hover:bg-green-500 flex items-center mx-auto"
            >
              <FaPlus className="mr-2" /> Add Password
            </button>
          )}
        </div>
      ) : (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Name
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Username
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Password
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  URL
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  2FA
                </th>
                <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {passwords.map((password) => (
                <tr key={password.id}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900 dark:text-white">{password.name}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-500 dark:text-gray-400 flex items-center">
                      {password.username}
                      <button 
                        onClick={() => copyToClipboard(password.username)}
                        className="ml-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                        title="Copy username"
                      >
                        <FaCopy size={14} />
                      </button>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-500 dark:text-gray-400 flex items-center">
                      {showPasswordMap[password.id] ? password.password : '••••••••'}
                      <button 
                        onClick={() => togglePasswordVisibility(password.id)}
                        className="ml-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                        title={showPasswordMap[password.id] ? "Hide password" : "Show password"}
                      >
                        {showPasswordMap[password.id] ? <FaEyeSlash size={14} /> : <FaEye size={14} />}
                      </button>
                      <button 
                        onClick={() => copyToClipboard(password.password)}
                        className="ml-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                        title="Copy password"
                      >
                        <FaCopy size={14} />
                      </button>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-500 dark:text-gray-400">
                      {password.url ? (
                        <a 
                          href={password.url} 
                          target="_blank" 
                          rel="noopener noreferrer"
                          className="text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300"
                        >
                          {password.url}
                        </a>
                      ) : (
                        <span className="text-gray-400">-</span>
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-500 dark:text-gray-400">
                      {password.has_2fa ? (
                        <span className="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100">
                          Yes
                        </span>
                      ) : (
                        <span className="px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">
                          No
                        </span>
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex justify-end space-x-2">
                      {userPermission !== 'view' && (
                        <button
                          onClick={() => {
                            setSelectedPassword(password);
                            setShowEditModal(true);
                          }}
                          className="text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300"
                          title="Edit"
                        >
                          <FaEdit />
                        </button>
                      )}
                      {userPermission === 'manage' && (
                        <button
                          onClick={() => {
                            setSelectedPassword(password);
                            setShowDeleteModal(true);
                          }}
                          className="text-red-600 hover:text-red-500 dark:text-red-400 dark:hover:text-red-300"
                          title="Delete"
                        >
                          <FaTrash />
                        </button>
                      )}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}

      {/* Modals */}
      {renderAddPasswordModal()}
      {renderEditPasswordModal()}
      {renderDeletePasswordModal()}
    </div>
  );
};

export default PasswordGroupDetailPage;