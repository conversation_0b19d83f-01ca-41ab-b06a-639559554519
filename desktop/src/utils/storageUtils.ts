/**
 * Storage utilities for the desktop app
 * Provides consistent storage interface for authentication data
 */

// Set a value in localStorage
export const setStorageItem = (key: string, value: string): void => {
  try {
    localStorage.setItem(key, value);
  } catch (error) {
    console.error('Error setting storage item:', error);
  }
};

// Get a value from localStorage
export const getStorageItem = (key: string): string | null => {
  try {
    return localStorage.getItem(key);
  } catch (error) {
    console.error('Error getting storage item:', error);
    return null;
  }
};

// Remove a value from localStorage
export const removeStorageItem = (key: string): void => {
  try {
    localStorage.removeItem(key);
  } catch (error) {
    console.error('Error removing storage item:', error);
  }
};

// Set a JSON value in storage
export const setStorageJSON = (key: string, value: any): void => {
  try {
    const jsonValue = JSON.stringify(value);
    setStorageItem(key, jsonValue);
  } catch (error) {
    console.error('Error setting JSON in storage:', error);
  }
};

// Get a JSON value from storage
export const getStorageJSON = <T>(key: string): T | null => {
  try {
    const value = getStorageItem(key);
    if (value === null) {
      return null;
    }
    return JSON.parse(value) as T;
  } catch (error) {
    console.error('Error getting JSON from storage:', error);
    return null;
  }
};

// Convenience function to get the authentication token
export const getAuthToken = (): string | null => {
  // For desktop app, we use 'auth_token' as the primary key
  let token = getStorageItem('auth_token');
  
  // Fall back to 'token' for backward compatibility
  if (!token) {
    token = getStorageItem('token');
  }
  
  return token;
};

// Convenience function to set the authentication token
export const setAuthToken = (token: string): void => {
  setStorageItem('auth_token', token);
  // Also set the old key for backward compatibility
  setStorageItem('token', token);
};

// Convenience function to get the user object
export const getUser = <T>(): T | null => {
  return getStorageJSON<T>('user');
};

// Convenience function to set the user object
export const setUser = (user: any): void => {
  setStorageJSON('user', user);
};

// Convenience function to get the refresh token
export const getRefreshToken = (): string | null => {
  // Try 'refresh_token' first, then fall back to 'refreshToken'
  let token = getStorageItem('refresh_token');
  if (!token) {
    token = getStorageItem('refreshToken');
  }
  return token;
};

// Convenience function to set the refresh token
export const setRefreshToken = (token: string): void => {
  setStorageItem('refresh_token', token);
  // Also set the old key for backward compatibility
  setStorageItem('refreshToken', token);
};

// Clear all authentication-related storage items
export const clearAuthStorage = (): void => {
  // List of all authentication-related keys
  const authKeys = [
    'token',
    'auth_token',
    'refresh_token',
    'refreshToken',
    'user',
    'lastActivity'
  ];
  
  // Remove each key
  authKeys.forEach(key => {
    removeStorageItem(key);
  });
};
