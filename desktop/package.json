{"name": "nxtacre-desktop", "version": "0.1.0", "description": "NxtAcre Farm Management Platform Desktop Application", "main": "dist/electron/main.js", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "electron:dev": "concurrently \"vite\" \"electron-forge start\"", "electron:build": "vite build && electron-builder", "electron:package": "electron-forge package", "electron:make": "electron-forge make"}, "keywords": ["electron", "react", "typescript", "vite", "farm-management"], "author": "NxtAcre Team", "license": "UNLICENSED", "private": true, "devDependencies": {"@electron-forge/cli": "^6.4.2", "@electron-forge/maker-deb": "^6.4.2", "@electron-forge/maker-rpm": "^6.4.2", "@electron-forge/maker-squirrel": "^6.4.2", "@electron-forge/maker-zip": "^6.4.2", "@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-react": "^4.0.3", "autoprefixer": "^10.4.14", "concurrently": "^8.2.1", "electron": "^26.2.1", "electron-builder": "^24.6.4", "eslint": "^8.45.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "postcss": "^8.4.27", "tailwindcss": "^3.3.3", "typescript": "^5.0.2", "vite": "^4.4.5", "vite-plugin-electron": "^0.14.0"}, "dependencies": {"electron-squirrel-startup": "^1.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-icons": "^4.10.1", "react-router-dom": "^6.14.2"}}