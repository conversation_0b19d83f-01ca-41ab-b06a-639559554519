import { contextBridge, ipc<PERSON>enderer } from 'electron';

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  // App info
  getAppVersion: () => ipcRenderer.invoke('app:get-version'),
  
  // Farm management
  getFarms: () => ipcRenderer.invoke('farms:get-all'),
  
  // System notifications
  showNotification: (title: string, body: string) => {
    new Notification(title, { body });
  },
  
  // System tray
  minimizeToTray: () => ipcRenderer.invoke('app:minimize-to-tray'),
  
  // File system operations
  openFile: () => ipcRenderer.invoke('dialog:open-file'),
  saveFile: (content: string) => ipcRenderer.invoke('dialog:save-file', content),
  
  // Data synchronization
  syncData: () => ipcRenderer.invoke('data:sync'),
  getOfflineStatus: () => ipcRenderer.invoke('app:get-offline-status'),
  
  // Window management
  minimizeWindow: () => ipc<PERSON>enderer.invoke('window:minimize'),
  maximizeWindow: () => ipcRenderer.invoke('window:maximize'),
  closeWindow: () => ipcRenderer.invoke('window:close'),
});

// Log when preload script has loaded
console.log('Preload script loaded');