import express from 'express';
import { authenticate } from '../middleware/authMiddleware.js';
import {
  getAllSignableDocuments,
  getSignableDocumentById,
  createSignableDocument,
  updateSignableDocument,
  deleteSignableDocument
} from '../controllers/documentSigningController.js';
import {
  addSigners,
  removeSigner,
  sendDocument,
  getDocumentForSigning,
  downloadDocumentForSigning,
  signDocument,
  declineDocument,
  getDocumentBlockchainVerification,
  verifyDocumentWithBlockchain,
  signDocumentWithCertificate,
  verifyDocumentDigitalSignature
} from '../controllers/documentSigningControllerPart2.js';
import {
  getAllTemplates,
  getTemplateById,
  createTemplate,
  saveAsTemplate,
  createDocumentFromTemplate,
  createDefaultTemplates
} from '../controllers/documentTemplateController.js';
import {
  createFormBuiltDocument,
  getFormBuiltDocument,
  updateFormBuiltDocument,
  saveFormBuiltDocumentAsTemplate,
  createDocumentFromFormBuiltTemplate
} from '../controllers/formBuilderController.js';

const router = express.Router();

// Signable document routes (authenticated)
router.get('/farm/:farmId/signable-documents', authenticate, getAllSignableDocuments);
router.get('/signable-documents/:id', authenticate, getSignableDocumentById);
router.post('/farm/:farmId/signable-documents', authenticate, createSignableDocument);
router.put('/signable-documents/:id', authenticate, updateSignableDocument);
router.delete('/signable-documents/:id', authenticate, deleteSignableDocument);

// Signer management routes (authenticated)
router.post('/signable-documents/:id/signers', authenticate, addSigners);
router.delete('/signable-documents/:id/signers/:signerId', authenticate, removeSigner);

// Document sending route (authenticated)
router.post('/signable-documents/:id/send', authenticate, sendDocument);

// Public signing routes (no authentication required)
router.get('/sign/:documentId/:signerId', getDocumentForSigning);
router.get('/sign/:documentId/:signerId/download', downloadDocumentForSigning);
router.post('/sign/:documentId/:signerId', signDocument);
router.post('/sign/:documentId/:signerId/decline', declineDocument);

// Blockchain verification routes (authenticated)
router.get('/signable-documents/:id/blockchain', authenticate, getDocumentBlockchainVerification);
router.get('/signable-documents/:id/verify', authenticate, verifyDocumentWithBlockchain);

// Digital certificate signing routes
router.post('/sign/:documentId/:signerId/certificate', signDocumentWithCertificate);
router.get('/signable-documents/:id/verify-digital-signature', authenticate, verifyDocumentDigitalSignature);

// Template routes (authenticated)
router.get('/farm/:farmId/templates', authenticate, getAllTemplates);
router.get('/templates/:id', authenticate, getTemplateById);
router.get('/documents/signing/template/:id', authenticate, getTemplateById); // Added to match error URL pattern
router.post('/farm/:farmId/templates', authenticate, createTemplate);
router.post('/signable-documents/:id/save-as-template', authenticate, saveAsTemplate);
router.post('/templates/:templateId/create-document', authenticate, createDocumentFromTemplate);
router.post('/farm/:farmId/create-default-templates', authenticate, createDefaultTemplates);

// Form builder routes (authenticated)
router.post('/farm/:farmId/form-built-documents', authenticate, createFormBuiltDocument);
router.get('/form-built-documents/:id', authenticate, getFormBuiltDocument);
router.put('/form-built-documents/:id', authenticate, updateFormBuiltDocument);
router.post('/form-built-documents/:id/save-as-template', authenticate, saveFormBuiltDocumentAsTemplate);
router.post('/form-built-templates/:templateId/create-document', authenticate, createDocumentFromFormBuiltTemplate);

export default router;
