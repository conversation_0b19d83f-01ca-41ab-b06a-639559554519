import express from 'express';
import {
  getCustomerDeliveries,
  getCustomerDeliveryById,
  requestDelivery
} from '../controllers/customerDeliveryController.js';
import { authenticateCustomer } from '../middleware/customerAuthMiddleware.js';

const router = express.Router();

// All routes require customer authentication
router.use(authenticateCustomer);

// Get all deliveries for the authenticated customer
router.get('/', getCustomerDeliveries);

// Get a single delivery by ID for the authenticated customer
router.get('/:deliveryId', getCustomerDeliveryById);

// Request a delivery
router.post('/request', requestDelivery);

export default router;