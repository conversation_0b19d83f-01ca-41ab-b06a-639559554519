import express from 'express';
import {
  createInvoiceDispute,
  getInvoiceDisputes,
  getDispute,
  addDisputeMessage,
  resolveDispute,
  escalateDispute,
  getDisputeMessages
} from '../controllers/invoiceDisputeController.js';
import { authenticateToken } from '../middleware/auth.js';
import { farmAccessMiddleware } from '../middleware/farmAccess.js';
import InvoicePermissionService from '../services/invoicePermissionService.js';

const router = express.Router();

// Apply authentication and farm access middleware to all routes
router.use(authenticateToken);
router.use(farmAccessMiddleware);

// Create a new dispute for an invoice
router.post('/invoices/:invoiceId/disputes', 
  InvoicePermissionService.requireInvoicePermission('dispute'),
  createInvoiceDispute
);

// Get all disputes for an invoice
router.get('/invoices/:invoiceId/disputes',
  InvoicePermissionService.requireInvoicePermission('view'),
  getInvoiceDisputes
);

// Get a specific dispute
router.get('/disputes/:disputeId', getDispute);

// Add a message to a dispute
router.post('/disputes/:disputeId/messages', addDisputeMessage);

// Get messages for a dispute
router.get('/disputes/:disputeId/messages', getDisputeMessages);

// Resolve a dispute
router.patch('/disputes/:disputeId/resolve', resolveDispute);

// Escalate a dispute
router.patch('/disputes/:disputeId/escalate', escalateDispute);

export default router;
