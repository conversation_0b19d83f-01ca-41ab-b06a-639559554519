import express from 'express';
import {
  getAllVets,
  getGlobalVets,
  getFarmVets,
  getVetById,
  createVet,
  updateVet,
  deleteVet,
  searchVets
} from '../controllers/vetController.js';
import { authenticate, isApprovedBusinessOwner } from '../middleware/authMiddleware.js';

const router = express.Router();

// Get all vets (global and farm-specific)
router.get('/', authenticate, getAllVets);

// Get all global vets (managed by admin)
router.get('/global', authenticate, getGlobalVets);

// Get all vets for a specific farm (including global vets)
router.get('/farm/:farmId', authenticate, getFarmVets);

// Search vets by name, specialization, or location
router.get('/search', authenticate, searchVets);

// Get a single vet by ID
router.get('/:vetId', authenticate, getVetById);

// Create a new vet
router.post('/', authenticate, createVet);

// Update a vet
router.put('/:vetId', authenticate, isApprovedBusinessOwner, updateVet);

// Delete a vet
router.delete('/:vetId', authenticate, isApprovedBusinessOwner, deleteVet);

export default router;
