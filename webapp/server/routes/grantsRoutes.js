import express from 'express';
import { authenticate } from '../middleware/index.js';
import * as grantsController from '../controllers/grantsController.js';
import { Grant } from '../models/index.js';

const router = express.Router();

// Apply authentication middleware to all routes
router.use(authenticate);

// Route to get all grants from the database
router.get('/', async (req, res) => {
  try {
    const grants = await Grant.findAll({
      order: [['created_at', 'DESC']]
    });

    // Format the grants to match the expected format in the frontend
    const formattedGrants = grants.map(grant => ({
      id: grant.id,
      title: grant.title,
      description: grant.description || '',
      agency: grant.agency || '',
      opportunityNumber: grant.opportunity_number || '',
      category: grant.category || 'agriculture',
      eligibility: grant.eligibility || '',
      fundingAmount: grant.funding_amount || '',
      closeDate: grant.close_date || new Date().toISOString(),
      url: grant.url || '',
      source: grant.source || '',
      createdAt: grant.created_at || new Date().toISOString()
    }));

    res.json(formattedGrants);
  } catch (error) {
    console.error('Error fetching grants from database:', error);
    res.status(500).json({ error: 'Failed to fetch grants from database' });
  }
});

// Routes for external grants APIs
router.get('/grants-gov', grantsController.fetchGrantsGovGrants);
router.get('/usda', grantsController.fetchUSDAGrants);
router.get('/farmers-gov', grantsController.fetchFarmersGovGrants);
router.get('/fsa', grantsController.fetchFSAGrants);
router.get('/rural-development', grantsController.fetchRuralDevelopmentGrants);
router.get('/nrcs', grantsController.fetchNRCSGrants);
router.get('/nifa', grantsController.fetchNIFAGrants);
router.get('/rma', grantsController.fetchRMAPrograms);
router.get('/ams', grantsController.fetchAMSGrants);
router.get('/data-gov', grantsController.fetchDataGovGrants);
router.get('/search', grantsController.searchGrants);
router.get('/:source/:id', grantsController.getGrantDetails);

export default router;
