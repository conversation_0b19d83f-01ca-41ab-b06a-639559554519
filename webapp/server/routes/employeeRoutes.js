import express from 'express';
import {
  getEmployees,
  getEmployeeById,
  createEmployee,
  updateEmployee,
  deleteEmployee,
  getEmployeesByFarm,
  searchEmployees
} from '../controllers/employeeController.js';
import { authenticate } from '../middleware/authMiddleware.js';

const router = express.Router();

// Get all employees with optional filtering
router.get('/', getEmployees);

// Search employees
router.get('/search', searchEmployees);

// Get employees by farm ID
router.get('/farm/:farmId', getEmployeesByFarm);

// Get employee by ID
router.get('/:employeeId', getEmployeeById);

// Create a new employee
router.post('/', createEmployee);

// Update an employee
router.put('/:employeeId', updateEmployee);

// Delete an employee
router.delete('/:employeeId', deleteEmployee);

export default router;