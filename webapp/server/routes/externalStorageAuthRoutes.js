import express from 'express';
import { authenticate } from '../middleware/index.js';
import {
  getGoogleDriveAuthUrl,
  handleGoogleDriveCallback,
  getDropboxAuthUrl,
  handleDropboxCallback,
  getUserConnections,
  disconnectExternalStorage
} from '../controllers/externalStorageAuthController.js';

const router = express.Router();

// Apply authentication middleware to protected routes
router.get('/google-drive/auth-url', authenticate, getGoogleDriveAuthUrl);
router.get('/dropbox/auth-url', authenticate, getDropboxAuthUrl);
router.get('/user/:userId/tenant/:tenantId/connections', authenticate, getUserConnections);
router.get('/user/:userId/farm/:farmId/connections', authenticate, getUserConnections);
router.delete('/connections/:connectionId', authenticate, disconnectExternalStorage);

// Public callback routes (authentication happens in the controller)
router.get('/google-drive/callback', handleGoogleDriveCallback);
router.get('/dropbox/callback', handleDropboxCallback);

export default router;
