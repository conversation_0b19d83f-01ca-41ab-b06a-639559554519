import express from 'express';
import {
  getFarmLivestock,
  getLivestockById,
  createLivestock,
  updateLivestock,
  deleteLivestock
} from '../controllers/livestockController.js';
import { authenticate } from '../middleware/authMiddleware.js';

const router = express.Router();

// Get all livestock for a farm
router.get('/farm/:farmId', authenticate, getFarmLivestock);

// Get a single livestock item by ID
router.get('/:livestockId', authenticate, getLivestockById);

// Create a new livestock item
router.post('/', authenticate, createLivestock);

// Update a livestock item
router.put('/:livestockId', authenticate, updateLivestock);

// Delete a livestock item
router.delete('/:livestockId', authenticate, deleteLivestock);

export default router;
