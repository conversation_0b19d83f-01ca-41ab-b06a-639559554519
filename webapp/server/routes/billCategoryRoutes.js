import express from 'express';
import {
  getFarmBillCategories,
  getBillCategoryById,
  createBillCategory,
  updateBillCategory,
  deleteBillCategory,
  getCategoryUsageStats
} from '../controllers/billCategoryController.js';
import { authenticate } from '../middleware/authMiddleware.js';

const router = express.Router();

// Get all bill categories for a farm
router.get('/farm/:farmId', authenticate, getFarmBillCategories);

// Get bill category usage statistics for a farm
router.get('/farm/:farmId/stats', authenticate, getCategoryUsageStats);

// Get a single bill category by ID
router.get('/:categoryId', authenticate, getBillCategoryById);

// Create a new bill category
router.post('/', authenticate, createBillCategory);

// Update a bill category
router.put('/:categoryId', authenticate, updateBillCategory);

// Delete a bill category
router.delete('/:categoryId', authenticate, deleteBillCategory);

export default router;