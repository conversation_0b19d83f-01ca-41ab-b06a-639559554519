import express from 'express';
import {
  getCropRotationPlans,
  getCropRotationPlanById,
  createCropRotationPlan,
  generateCropRotationPlan,
  deleteCropRotationPlan
} from '../controllers/cropRotationController.js';
import { authenticate } from '../middleware/authMiddleware.js';

const router = express.Router();

// Apply authentication middleware to all routes
router.use(authenticate);

// Get all crop rotation plans for a farm
router.get('/', getCropRotationPlans);

// Get a single crop rotation plan by ID
router.get('/:planId', getCropRotationPlanById);

// Create a new crop rotation plan
router.post('/', createCropRotationPlan);

// Generate a crop rotation plan using AI
router.post('/generate', generateCropRotationPlan);

// Delete a crop rotation plan
router.delete('/:planId', deleteCropRotationPlan);

export default router;