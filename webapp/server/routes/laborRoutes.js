import express from 'express';
import { check } from 'express-validator';
import * as laborController from '../controllers/laborController.js';
import { authenticateToken } from '../middleware/authMiddleware.js';

const router = express.Router();

// Apply authentication middleware to all routes
router.use(authenticateToken);

// Seasonal Worker Management Routes
router.post(
  '/seasonal-workers',
  [
    check('farmId', 'Farm ID is required').not().isEmpty(),
    check('firstName', 'First name is required').not().isEmpty(),
    check('lastName', 'Last name is required').not().isEmpty(),
    check('contactInfo', 'Contact information is required').not().isEmpty(),
    check('startDate', 'Start date is required').not().isEmpty(),
    check('position', 'Position is required').not().isEmpty(),
    check('status', 'Status is required').isIn(['active', 'inactive', 'pending']),
    check('documentationStatus', 'Documentation status is required').isIn(['complete', 'incomplete', 'pending']),
  ],
  laborController.createSeasonalWorker
);

router.get('/seasonal-workers', laborController.getSeasonalWorkers);
router.get('/seasonal-workers/:id', laborController.getSeasonalWorker);

router.put(
  '/seasonal-workers/:id',
  [
    check('firstName', 'First name is required').not().isEmpty(),
    check('lastName', 'Last name is required').not().isEmpty(),
    check('contactInfo', 'Contact information is required').not().isEmpty(),
    check('startDate', 'Start date is required').not().isEmpty(),
    check('position', 'Position is required').not().isEmpty(),
    check('status', 'Status is required').isIn(['active', 'inactive', 'pending']),
    check('documentationStatus', 'Documentation status is required').isIn(['complete', 'incomplete', 'pending']),
  ],
  laborController.updateSeasonalWorker
);

router.delete('/seasonal-workers/:id', laborController.deleteSeasonalWorker);

// Labor Cost Analysis Routes
router.post(
  '/cost-analysis',
  [
    check('farmId', 'Farm ID is required').not().isEmpty(),
    check('analysisDate', 'Analysis date is required').not().isEmpty(),
    check('period', 'Period is required').not().isEmpty(),
    check('laborHours', 'Labor hours must be a number').isNumeric(),
    check('laborCost', 'Labor cost must be a number').isNumeric(),
    check('productivity', 'Productivity must be a number').isNumeric(),
  ],
  laborController.createLaborCostAnalysis
);

router.get('/cost-analysis', laborController.getLaborCostAnalyses);
router.get('/cost-analysis/:id', laborController.getLaborCostAnalysis);

router.put(
  '/cost-analysis/:id',
  [
    check('analysisDate', 'Analysis date is required').not().isEmpty(),
    check('period', 'Period is required').not().isEmpty(),
    check('laborHours', 'Labor hours must be a number').isNumeric(),
    check('laborCost', 'Labor cost must be a number').isNumeric(),
    check('productivity', 'Productivity must be a number').isNumeric(),
  ],
  laborController.updateLaborCostAnalysis
);

router.delete('/cost-analysis/:id', laborController.deleteLaborCostAnalysis);

// Compliance Tracking Routes
router.post(
  '/compliance',
  [
    check('farmId', 'Farm ID is required').not().isEmpty(),
    check('recordType', 'Record type is required').not().isEmpty(),
    check('recordDate', 'Record date is required').not().isEmpty(),
    check('status', 'Status is required').isIn(['compliant', 'non-compliant', 'pending']),
    check('details', 'Details are required').not().isEmpty(),
  ],
  laborController.createComplianceRecord
);

router.get('/compliance', laborController.getComplianceRecords);
router.get('/compliance/:id', laborController.getComplianceRecord);

router.put(
  '/compliance/:id',
  [
    check('recordType', 'Record type is required').not().isEmpty(),
    check('recordDate', 'Record date is required').not().isEmpty(),
    check('status', 'Status is required').isIn(['compliant', 'non-compliant', 'pending']),
    check('details', 'Details are required').not().isEmpty(),
  ],
  laborController.updateComplianceRecord
);

router.delete('/compliance/:id', laborController.deleteComplianceRecord);

// Worker Certification Tracking Routes
router.post(
  '/certifications',
  [
    check('farmId', 'Farm ID is required').not().isEmpty(),
    check('workerId', 'Worker ID is required').not().isEmpty(),
    check('certificationType', 'Certification type is required').not().isEmpty(),
    check('issueDate', 'Issue date is required').not().isEmpty(),
    check('expirationDate', 'Expiration date is required').not().isEmpty(),
    check('status', 'Status is required').isIn(['active', 'expired', 'pending']),
  ],
  laborController.createWorkerCertification
);

router.get('/certifications', laborController.getWorkerCertifications);
router.get('/certifications/:id', laborController.getWorkerCertification);

router.put(
  '/certifications/:id',
  [
    check('certificationType', 'Certification type is required').not().isEmpty(),
    check('issueDate', 'Issue date is required').not().isEmpty(),
    check('expirationDate', 'Expiration date is required').not().isEmpty(),
    check('status', 'Status is required').isIn(['active', 'expired', 'pending']),
  ],
  laborController.updateWorkerCertification
);

router.delete('/certifications/:id', laborController.deleteWorkerCertification);

export default router;
