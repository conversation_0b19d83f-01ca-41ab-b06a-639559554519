import express from 'express';
import { authenticate } from '../middleware/authMiddleware.js';
import {
  getAllDocuments,
  getDocumentById,
  uploadDocument,
  downloadDocument,
  updateDocument,
  deleteDocument,
  searchDocuments,
  getDocumentContent,
  getStorageQuotaInfo
} from '../controllers/documentController.js';
import {
  getAllFolders,
  getFolderById,
  createFolder,
  updateFolder,
  deleteFolder,
  getFolderHierarchy
} from '../controllers/folderController.js';
import {
  listGoogleDriveFilesController,
  listDropboxFilesController,
  linkGoogleDriveFile,
  linkDropboxFile,
  importExternalFileController,
  bulkImportExternalFilesController,
  getUserIntegrations
} from '../controllers/externalStorageController.js';
import {
  getEntityPermissions,
  setEntityPermission,
  deleteEntityPermission,
  checkEntityPermission
} from '../controllers/documentPermissionController.js';
import {
  createShareLink,
  getDocumentShares,
  deleteShareLink,
  accessSharedDocument,
  downloadSharedDocument
} from '../controllers/documentShareController.js';

const router = express.Router();

// Document routes
router.get('/farm/:farmId/documents', authenticate, getAllDocuments); // Changed from tenant to farm
router.get('/documents/:id', authenticate, getDocumentById);
router.post('/farm/:farmId/documents', authenticate, uploadDocument); // Changed from tenant to farm
router.post('/farm/:farmId/upload', authenticate, uploadDocument); // New route for file manager upload
router.post('/documents/upload', authenticate, uploadDocument); // Direct upload route for client compatibility
router.get('/documents/:id/download', authenticate, downloadDocument);
router.get('/documents/:id/thumbnail', authenticate, downloadDocument); // New route for thumbnails
router.get('/documents/:id/content', authenticate, getDocumentContent); // New route for text content
router.put('/documents/:id', authenticate, updateDocument);
router.put('/documents/farm/:farmId/documents/:id', authenticate, updateDocument); // Added to match potential error URL pattern
router.delete('/documents/:id', authenticate, deleteDocument);
router.get('/farm/:farmId/search', authenticate, searchDocuments); // Changed from tenant to farm
router.get('/farm/:farmId/storage-quota', authenticate, getStorageQuotaInfo); // New route for storage quota information

// Folder routes
router.get('/farm/:farmId/folders', authenticate, getAllFolders); // Changed from tenant to farm
router.get('/folders/:id', authenticate, getFolderById);
router.post('/farm/:farmId/folders', authenticate, createFolder); // Changed from tenant to farm
router.put('/folders/:id', authenticate, updateFolder);
router.put('/documents/folders/:id', authenticate, updateFolder); // Added to match client URL pattern
router.put('/documents/farm/:farmId/folders/:id', authenticate, updateFolder); // Added to match error URL pattern
router.delete('/folders/:id', authenticate, deleteFolder);
router.get('/farm/:farmId/folder-hierarchy', authenticate, getFolderHierarchy); // Changed from tenant to farm

// Permission routes
router.get('/:entityType/:entityId/permissions', authenticate, getEntityPermissions);
router.post('/:entityType/:entityId/permissions', authenticate, setEntityPermission);
router.delete('/permissions/:permissionId', authenticate, deleteEntityPermission);
router.get('/:entityType/:entityId/check-permission', authenticate, checkEntityPermission);

// Share routes
router.post('/documents/:documentId/share', authenticate, createShareLink);
router.get('/documents/:documentId/shares', authenticate, getDocumentShares);
router.delete('/shares/:shareId', authenticate, deleteShareLink);
router.get('/share/:token', accessSharedDocument); // Public route, no authentication required
router.get('/share/:token/download', downloadSharedDocument); // Public route, no authentication required

// External storage routes
router.get('/farm/:farmId/google-drive/files', authenticate, listGoogleDriveFilesController); // Changed from tenant to farm
router.get('/farm/:farmId/dropbox/files', authenticate, listDropboxFilesController); // Changed from tenant to farm
router.post('/farm/:farmId/google-drive/link', authenticate, linkGoogleDriveFile); // Changed from tenant to farm
router.post('/farm/:farmId/dropbox/link', authenticate, linkDropboxFile); // Changed from tenant to farm
router.post('/farm/:farmId/documents/:documentId/import', authenticate, importExternalFileController); // Changed from tenant to farm
router.post('/farm/:farmId/documents/bulk-import', authenticate, bulkImportExternalFilesController); // Changed from tenant to farm
router.get('/farm/:farmId/integrations', authenticate, getUserIntegrations); // Changed from tenant to farm

export default router;
