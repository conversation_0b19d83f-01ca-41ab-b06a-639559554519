import express from 'express';
import { 
  getAllScriptExecutions,
  getScriptExecution,
  executeScript,
  scanForScripts,
  checkScriptExecutionStatus
} from '../controllers/scriptExecutionController.js';
import { authenticate, isGlobalAdmin } from '../middleware/authMiddleware.js';

const router = express.Router();

// Apply authentication and global admin middleware to all routes
router.use(authenticate);
router.use(isGlobalAdmin);

// Get all script executions
router.get('/', getAllScriptExecutions);

// Get a specific script execution by ID
router.get('/:scriptId', getScriptExecution);

// Execute a script
router.post('/:scriptId/execute', executeScript);

// Scan for new scripts in the scripts directory
router.post('/scan', scanForScripts);

// Check script execution status
router.get('/status/check', checkScriptExecutionStatus);

export default router;