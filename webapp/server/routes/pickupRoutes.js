import express from 'express';
import {
  createPickup,
  getPickups,
  getPickupById,
  updatePickup,
  deletePickup,
  updatePickupStatus
} from '../controllers/pickupController.js';
import { authenticate } from '../middleware/authMiddleware.js';

const router = express.Router();

// All routes require authentication
router.post('/', authenticate, createPickup);
router.get('/', authenticate, getPickups);
router.get('/:pickupId', authenticate, getPickupById);
router.put('/:pickupId', authenticate, updatePickup);
router.delete('/:pickupId', authenticate, deletePickup);
router.patch('/:pickupId/status', authenticate, updatePickupStatus);

export default router;