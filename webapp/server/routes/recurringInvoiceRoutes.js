import express from 'express';
import { 
  createRecurringInvoice, 
  getRecurringInvoice, 
  getFarmRecurringInvoices, 
  updateRecurringInvoice, 
  deleteRecurringInvoice,
  generateRecurringInvoices
} from '../controllers/recurringInvoiceController.js';
import { authenticateToken } from '../middleware/authMiddleware.js';

const router = express.Router();

// Apply authentication middleware to all routes
router.use(authenticateToken);

// Create a recurring invoice
router.post('/', createRecurringInvoice);

// Get a recurring invoice by ID
router.get('/:recurringInvoiceId', getRecurringInvoice);

// Get all recurring invoices for a farm
router.get('/farm/:farmId', getFarmRecurringInvoices);

// Update a recurring invoice
router.put('/:recurringInvoiceId', updateRecurringInvoice);

// Delete a recurring invoice
router.delete('/:recurringInvoiceId', deleteRecurringInvoice);

// Generate invoices for all due recurring invoices
router.post('/generate', generateRecurringInvoices);

export default router;