import express from 'express';
import { 
  getMaintenanceSchedules, 
  getMaintenanceSchedule, 
  createMaintenanceSchedule, 
  updateMaintenanceSchedule, 
  deleteMaintenanceSchedule, 
  createMaintenanceLog, 
  getMaintenanceLogs, 
  getMaintenanceAnalytics 
} from '../controllers/maintenanceSchedulingController.js';
import { authenticateToken } from '../middleware/authMiddleware.js';
import { checkFarmAccess } from '../middleware/farmAccessMiddleware.js';

const router = express.Router();

// Apply authentication middleware to all routes
router.use(authenticateToken);

// Get maintenance schedules for a farm
router.get('/farms/:farmId/maintenance-schedules', checkFarmAccess, getMaintenanceSchedules);

// Get a specific maintenance schedule
router.get('/maintenance-schedules/:scheduleId', checkFarmAccess, getMaintenanceSchedule);

// Create a new maintenance schedule
router.post('/farms/:farmId/maintenance-schedules', checkFarmAccess, createMaintenanceSchedule);

// Update a maintenance schedule
router.put('/maintenance-schedules/:scheduleId', checkFarmAccess, updateMaintenanceSchedule);

// Delete a maintenance schedule
router.delete('/maintenance-schedules/:scheduleId', checkFarmAccess, deleteMaintenanceSchedule);

// Create a maintenance log entry
router.post('/maintenance-schedules/:scheduleId/logs', checkFarmAccess, createMaintenanceLog);

// Get maintenance logs for a schedule
router.get('/maintenance-schedules/:scheduleId/logs', checkFarmAccess, getMaintenanceLogs);

// Get maintenance analytics for a farm
router.get('/farms/:farmId/maintenance-analytics', checkFarmAccess, getMaintenanceAnalytics);

export default router;