import express from 'express';
import { 
  checkDatabaseHealth,
  getMigrationFiles,
  fixSchemaIssue,
  fixAllSchemaIssues
} from '../controllers/databaseHealthController.js';
import { authenticate, isGlobalAdmin } from '../middleware/authMiddleware.js';

const router = express.Router();

// Apply authentication and global admin middleware to all routes
router.use(authenticate);
router.use(isGlobalAdmin);

// Check database health
router.get('/check', checkDatabaseHealth);

// Get all migration files
router.get('/migrations', getMigrationFiles);

// Fix a specific schema issue
router.post('/fix-issue', fixSchemaIssue);

// Fix all schema issues
router.post('/fix-all', fixAllSchemaIssues);

export default router;
