import express from 'express';
import {
  createDriverSchedule,
  getDriverSchedules,
  getDriverScheduleById,
  updateDriverSchedule,
  deleteDriverSchedule,
  getDriverSchedulesByDriverId
} from '../controllers/driverScheduleController.js';
import { authenticate } from '../middleware/authMiddleware.js';

const router = express.Router();

// All routes require authentication
router.post('/', authenticate, createDriverSchedule);
router.get('/', authenticate, getDriverSchedules);
router.get('/driver/:driverId', authenticate, getDriverSchedulesByDriverId);
router.get('/:scheduleId', authenticate, getDriverScheduleById);
router.put('/:scheduleId', authenticate, updateDriverSchedule);
router.delete('/:scheduleId', authenticate, deleteDriverSchedule);

export default router;
