import express from 'express';
import {
  createSupportTicket,
  getSupportTickets,
  getSupportTicketById,
  updateSupportTicket,
  deleteSupportTicket,
  addComment,
  deleteComment,
  uploadAttachment,
  getAttachments,
  downloadAttachment,
  deleteAttachment
} from '../controllers/supportTicketController.js';
import { authenticate } from '../middleware/authMiddleware.js';
import { uploadSupportTicketFile, handleFileUploadErrors } from '../middleware/fileUploadMiddleware.js';

const router = express.Router();

// All routes require authentication
router.post('/', authenticate, createSupportTicket);
router.get('/', authenticate, getSupportTickets);
router.get('/:ticketId', authenticate, getSupportTicketById);
router.put('/:ticketId', authenticate, updateSupportTicket);
router.delete('/:ticketId', authenticate, deleteSupportTicket);

// Comment routes
router.post('/:ticketId/comments', authenticate, addComment);
router.delete('/comments/:commentId', authenticate, deleteComment);

// Attachment routes
router.post('/:ticketId/attachments', authenticate, uploadSupportTicketFile.single('file'), handleFileUploadErrors, uploadAttachment);
router.get('/:ticketId/attachments', authenticate, getAttachments);
router.get('/attachments/:attachmentId/download', authenticate, downloadAttachment);
router.delete('/attachments/:attachmentId', authenticate, deleteAttachment);

export default router;
