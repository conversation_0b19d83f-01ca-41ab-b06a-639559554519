import express from 'express';
import { 
  getFarmCropTypes, 
  getCropTypeById, 
  createCropType, 
  updateCropType, 
  deleteCropType 
} from '../controllers/cropTypeController.js';
import { authenticate, authorize } from '../middleware/authMiddleware.js';

const router = express.Router();

// Get all crop types for a farm
router.get('/farm/:farmId', 
  authenticate, 
  authorize([], 'crop_types', 'view'),
  getFarmCropTypes
);

// Get a single crop type by ID
router.get('/:cropTypeId', 
  authenticate, 
  authorize([], 'crop_types', 'view'),
  getCropTypeById
);

// Create a new crop type
router.post('/', 
  authenticate, 
  authorize([], 'crop_types', 'create'),
  createCropType
);

// Update a crop type
router.put('/:cropTypeId', 
  authenticate, 
  authorize([], 'crop_types', 'edit'),
  updateCropType
);

// Delete a crop type
router.delete('/:cropTypeId', 
  authenticate, 
  authorize([], 'crop_types', 'delete'),
  deleteCropType
);

export default router;
