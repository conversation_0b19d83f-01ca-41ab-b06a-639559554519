import express from 'express';
import { authenticate } from '../middleware/authMiddleware.js';
import * as inventoryCategoryController from '../controllers/inventoryCategoryController.js';
import * as inventoryItemController from '../controllers/inventoryItemController.js';
import * as inventoryTransactionController from '../controllers/inventoryTransactionController.js';

const router = express.Router();

// Apply authentication middleware to all inventory routes
router.use(authenticate);

// Inventory Category Routes
router.get('/categories', inventoryCategoryController.getInventoryCategories);
router.get('/categories/:id', inventoryCategoryController.getInventoryCategory);
router.post('/categories', inventoryCategoryController.createInventoryCategory);
router.put('/categories/:id', inventoryCategoryController.updateInventoryCategory);
router.delete('/categories/:id', inventoryCategoryController.deleteInventoryCategory);

// Inventory Item Routes
router.get('/', inventoryItemController.getInventoryItems);
router.get('/:id', inventoryItemController.getInventoryItem);
router.get('/barcode/:barcode', inventoryItemController.getInventoryItemByBarcode);
router.get('/qrcode/:qrCode', inventoryItemController.getInventoryItemByQRCode);
router.post('/', inventoryItemController.createInventoryItem);
router.put('/:id', inventoryItemController.updateInventoryItem);
router.delete('/:id', inventoryItemController.deleteInventoryItem);
router.post('/:id/generate-qrcode', inventoryItemController.generateQRCode);

// Inventory Transaction Routes
router.get('/transactions', inventoryTransactionController.getInventoryTransactions);
router.get('/transactions/:id', inventoryTransactionController.getInventoryTransaction);
router.post('/transactions', inventoryTransactionController.createInventoryTransaction);
router.put('/transactions/:id', inventoryTransactionController.updateInventoryTransaction);
router.delete('/transactions/:id', inventoryTransactionController.deleteInventoryTransaction);

export default router;