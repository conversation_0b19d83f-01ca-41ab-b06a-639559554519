import express from 'express';
import {
  getProductInventory,
  getInventoryProducts,
  linkProductToInventory,
  updateProductInventoryLink,
  deleteProductInventoryLink
} from '../controllers/productInventoryController.js';

const router = express.Router();

// Get all inventory items linked to a product
router.get('/product/:productId', getProductInventory);

// Get all products linked to an inventory item
router.get('/inventory/:inventoryItemId', getInventoryProducts);

// Link a product to an inventory item
router.post('/', linkProductToInventory);

// Update a product-inventory link
router.put('/:id', updateProductInventoryLink);

// Delete a product-inventory link
router.delete('/:id', deleteProductInventoryLink);

export default router;