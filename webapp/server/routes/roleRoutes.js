import express from 'express';
import {
  getGlobalRoles,
  getFarmR<PERSON><PERSON>,
  getRoleById,
  createGlobalRole,
  createFarmRole,
  updateRole,
  deleteRole
} from '../controllers/roleController.js';
import { authenticate } from '../middleware/authMiddleware.js';

const router = express.Router();

// Get all global roles (for global admins)
router.get('/global', authenticate, getGlobalRoles);

// Get all farm roles
router.get('/farm/:farmId', authenticate, getFarmRoles);

// Get a role by ID
router.get('/:id', authenticate, getRoleById);

// Create a new global role (for global admins)
router.post('/global', authenticate, createGlobalRole);

// Create a new farm role
router.post('/farm/:farmId', authenticate, createFarmRole);

// Update a role
router.put('/:id', authenticate, updateRole);

// Delete a role
router.delete('/:id', authenticate, deleteRole);

export default router;