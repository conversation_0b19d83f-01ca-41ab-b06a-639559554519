import express from 'express';
import { body } from 'express-validator';
import { authenticate } from '../middleware/index.js';
import { 
  generateCropRotationAnalysisController,
  getLatestCropRotationAnalysisController,
  applyCropRotationAnalysisController,
  getAllCropRotationAnalysesController,
  runCropRotationAnalysisForFarm,
  runCropRotationAnalysisForAllFarms,
  generateHarvestScheduleAnalysisController,
  getLatestHarvestScheduleAnalysisController,
  applyHarvestScheduleAnalysisController,
  getAllHarvestScheduleAnalysesController,
  runHarvestScheduleAnalysisForFarm,
  runHarvestScheduleAnalysisForAllFarms,
  generateSoilHealthAnalysisController,
  getLatestSoilHealthAnalysisController,
  applySoilHealthAnalysisController,
  getAllSoilHealthAnalysesController,
  runSoilHealthAnalysisForFarm,
  runSoilHealthAnalysisForAllFarms,
  generateFieldHealthAnalysisController,
  getLatestFieldHealthAnalysisController,
  applyFieldHealthAnalysisController,
  getAllFieldHealthAnalysesController,
  runFieldHealthAnalysisForFarm,
  runFieldHealthAnalysisForAllFarms,
  generateHerdHealthAnalysisController,
  getLatestHerdHealthAnalysisController,
  applyHerdHealthAnalysisController,
  getAllHerdHealthAnalysesController,
  runHerdHealthAnalysisForFarm,
  runHerdHealthAnalysisForAllFarms
} from '../controllers/aiAnalysisController.js';

const router = express.Router();

// Apply authentication middleware to all routes
router.use(authenticate);

/**
 * @route POST /api/ai-analysis/crop-rotation
 * @desc Generate a crop rotation analysis for a field or farm
 * @access Private
 */
router.post(
  '/crop-rotation',
  [
    body('farmId').notEmpty().withMessage('Farm ID is required'),
    body('fieldId').optional(),
    body('cropData').optional()
  ],
  generateCropRotationAnalysisController
);

/**
 * @route GET /api/ai-analysis/crop-rotation/:farmId
 * @desc Get the latest crop rotation analysis for a farm or field
 * @access Private
 */
router.get('/crop-rotation/:farmId', getLatestCropRotationAnalysisController);

/**
 * @route GET /api/ai-analysis/crop-rotation/:farmId/all
 * @desc Get all crop rotation analyses for a farm
 * @access Private
 */
router.get('/crop-rotation/:farmId/all', getAllCropRotationAnalysesController);

/**
 * @route PUT /api/ai-analysis/crop-rotation/:id
 * @desc Apply a crop rotation analysis to farm planning
 * @access Private
 */
router.put(
  '/crop-rotation/:id',
  [
    body('isApplied').isBoolean().optional()
  ],
  applyCropRotationAnalysisController
);

/**
 * @route POST /api/ai-analysis/crop-rotation/:farmId/run
 * @desc Run crop rotation analysis for all fields in a farm
 * @access Private
 */
router.post('/crop-rotation/:farmId/run', runCropRotationAnalysisForFarm);

/**
 * @route POST /api/ai-analysis/crop-rotation/run-all
 * @desc Run crop rotation analysis for all farms
 * @access Private
 */
router.post('/crop-rotation/run-all', runCropRotationAnalysisForAllFarms);

/**
 * @route POST /api/ai-analysis/harvest-schedule
 * @desc Generate a harvest schedule analysis for a field or farm
 * @access Private
 */
router.post(
  '/harvest-schedule',
  [
    body('farmId').notEmpty().withMessage('Farm ID is required'),
    body('fieldId').optional(),
    body('cropId').optional(),
    body('harvestData').optional()
  ],
  generateHarvestScheduleAnalysisController
);

/**
 * @route GET /api/ai-analysis/harvest-schedule/:farmId
 * @desc Get the latest harvest schedule analysis for a farm or field
 * @access Private
 */
router.get('/harvest-schedule/:farmId', getLatestHarvestScheduleAnalysisController);

/**
 * @route GET /api/ai-analysis/harvest-schedule/:farmId/all
 * @desc Get all harvest schedule analyses for a farm
 * @access Private
 */
router.get('/harvest-schedule/:farmId/all', getAllHarvestScheduleAnalysesController);

/**
 * @route PUT /api/ai-analysis/harvest-schedule/:id
 * @desc Apply a harvest schedule analysis to farm planning
 * @access Private
 */
router.put(
  '/harvest-schedule/:id',
  [
    body('isApplied').isBoolean().optional()
  ],
  applyHarvestScheduleAnalysisController
);

/**
 * @route POST /api/ai-analysis/harvest-schedule/:farmId/run
 * @desc Run harvest schedule analysis for all fields in a farm
 * @access Private
 */
router.post('/harvest-schedule/:farmId/run', runHarvestScheduleAnalysisForFarm);

/**
 * @route POST /api/ai-analysis/harvest-schedule/run-all
 * @desc Run harvest schedule analysis for all farms
 * @access Private
 */
router.post('/harvest-schedule/run-all', runHarvestScheduleAnalysisForAllFarms);

/**
 * @route POST /api/ai-analysis/soil-health
 * @desc Generate a soil health analysis for a field or farm
 * @access Private
 */
router.post(
  '/soil-health',
  [
    body('farmId').notEmpty().withMessage('Farm ID is required'),
    body('fieldId').optional(),
    body('soilData').optional()
  ],
  generateSoilHealthAnalysisController
);

/**
 * @route GET /api/ai-analysis/soil-health/:farmId
 * @desc Get the latest soil health analysis for a farm or field
 * @access Private
 */
router.get('/soil-health/:farmId', getLatestSoilHealthAnalysisController);

/**
 * @route GET /api/ai-analysis/soil-health/:farmId/all
 * @desc Get all soil health analyses for a farm
 * @access Private
 */
router.get('/soil-health/:farmId/all', getAllSoilHealthAnalysesController);

/**
 * @route PUT /api/ai-analysis/soil-health/:id
 * @desc Apply a soil health analysis to farm planning
 * @access Private
 */
router.put(
  '/soil-health/:id',
  [
    body('isApplied').isBoolean().optional()
  ],
  applySoilHealthAnalysisController
);

/**
 * @route POST /api/ai-analysis/soil-health/:farmId/run
 * @desc Run soil health analysis for all fields in a farm
 * @access Private
 */
router.post('/soil-health/:farmId/run', runSoilHealthAnalysisForFarm);

/**
 * @route POST /api/ai-analysis/soil-health/run-all
 * @desc Run soil health analysis for all farms
 * @access Private
 */
router.post('/soil-health/run-all', runSoilHealthAnalysisForAllFarms);

/**
 * @route POST /api/ai-analysis/field-health
 * @desc Generate a field health analysis for a field or farm
 * @access Private
 */
router.post(
  '/field-health',
  [
    body('farmId').notEmpty().withMessage('Farm ID is required'),
    body('fieldId').optional(),
    body('cropId').optional(),
    body('fieldData').optional()
  ],
  generateFieldHealthAnalysisController
);

/**
 * @route GET /api/ai-analysis/field-health/:farmId
 * @desc Get the latest field health analysis for a farm or field
 * @access Private
 */
router.get('/field-health/:farmId', getLatestFieldHealthAnalysisController);

/**
 * @route GET /api/ai-analysis/field-health/:farmId/all
 * @desc Get all field health analyses for a farm
 * @access Private
 */
router.get('/field-health/:farmId/all', getAllFieldHealthAnalysesController);

/**
 * @route PUT /api/ai-analysis/field-health/:id
 * @desc Apply a field health analysis to farm planning
 * @access Private
 */
router.put(
  '/field-health/:id',
  [
    body('isApplied').isBoolean().optional()
  ],
  applyFieldHealthAnalysisController
);

/**
 * @route POST /api/ai-analysis/field-health/:farmId/run
 * @desc Run field health analysis for all fields in a farm
 * @access Private
 */
router.post('/field-health/:farmId/run', runFieldHealthAnalysisForFarm);

/**
 * @route POST /api/ai-analysis/field-health/run-all
 * @desc Run field health analysis for all farms
 * @access Private
 */
router.post('/field-health/run-all', runFieldHealthAnalysisForAllFarms);

/**
 * @route POST /api/ai-analysis/herd-health
 * @desc Generate a herd health analysis for a livestock group or farm
 * @access Private
 */
router.post(
  '/herd-health',
  [
    body('farmId').notEmpty().withMessage('Farm ID is required'),
    body('livestockGroupId').optional(),
    body('herdData').optional()
  ],
  generateHerdHealthAnalysisController
);

/**
 * @route GET /api/ai-analysis/herd-health/:farmId
 * @desc Get the latest herd health analysis for a farm or livestock group
 * @access Private
 */
router.get('/herd-health/:farmId', getLatestHerdHealthAnalysisController);

/**
 * @route GET /api/ai-analysis/herd-health/:farmId/all
 * @desc Get all herd health analyses for a farm
 * @access Private
 */
router.get('/herd-health/:farmId/all', getAllHerdHealthAnalysesController);

/**
 * @route PUT /api/ai-analysis/herd-health/:id
 * @desc Apply a herd health analysis to farm planning
 * @access Private
 */
router.put(
  '/herd-health/:id',
  [
    body('isApplied').isBoolean().optional()
  ],
  applyHerdHealthAnalysisController
);

/**
 * @route POST /api/ai-analysis/herd-health/:farmId/run
 * @desc Run herd health analysis for all livestock groups in a farm
 * @access Private
 */
router.post('/herd-health/:farmId/run', runHerdHealthAnalysisForFarm);

/**
 * @route POST /api/ai-analysis/herd-health/run-all
 * @desc Run herd health analysis for all farms
 * @access Private
 */
router.post('/herd-health/run-all', runHerdHealthAnalysisForAllFarms);

export default router;
