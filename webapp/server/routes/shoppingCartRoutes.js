import express from 'express';
import {
  addItemToCart,
  getCurrentCart,
  getAllCarts,
  updateCartItem,
  removeCartItem,
  saveCart,
  checkout
} from '../controllers/shoppingCartController.js';
import { optionalAuthenticate, authenticate } from '../middleware/authMiddleware.js';

const router = express.Router();

// The shopping cart can be used by both authenticated and unauthenticated users
// For some operations like saving a cart, authentication is required

// Add item to cart
router.post('/items', optionalAuthenticate, addItemToCart);

// Get current cart
router.get('/', optionalAuthenticate, getCurrentCart);

// Get all carts for a user (requires authentication)
router.get('/all', authenticate, getAllCarts);

// Update cart item quantity
router.put('/items/:itemId', optionalAuthenticate, updateCartItem);

// Remove item from cart
router.delete('/items/:itemId', optionalAuthenticate, removeCartItem);

// Save cart for later (requires authentication)
router.post('/:cartId/save', authenticate, saveCart);

// Submit purchase request (checkout)
router.post('/checkout', optionalAuthenticate, checkout);

export default router;
