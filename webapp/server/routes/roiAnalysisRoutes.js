import express from 'express';
import { 
  getCropROI, 
  getFieldROI, 
  getEquipmentROI, 
  getROIHistory, 
  getCombinedROI 
} from '../controllers/roiAnalysisController.js';
import { authenticateToken } from '../middleware/authMiddleware.js';
import { checkFarmAccess } from '../middleware/farmAccessMiddleware.js';

const router = express.Router();

// Apply authentication middleware to all routes
router.use(authenticateToken);

// Get ROI data for crops
router.get('/farms/:farmId/roi/crops', checkFarmAccess, getCropROI);

// Get ROI data for fields
router.get('/farms/:farmId/roi/fields', checkFarmAccess, getFieldROI);

// Get ROI data for equipment
router.get('/farms/:farmId/roi/equipment', checkFarmAccess, getEquipmentROI);

// Get ROI history data
router.get('/farms/:farmId/roi/history', checkFarmAccess, getROIHistory);

// Get combined ROI data
router.get('/farms/:farmId/roi/combined', checkFarmAccess, getCombinedROI);

export default router;