import express from 'express';
import {
  // Help Guides
  createHelpGuide,
  getHelpGuides,
  getHelpGuide,
  updateHelpGuide,
  deleteHelpGuide,

  // Help Tips
  createHelpTip,
  getHelpTips,
  getHelpTip,
  updateHelpTip,
  deleteHelpTip,
  dismissHelpTip,
  getDismissedHelpTips,
  resetDismissedHelpTips,


} from '../controllers/helpController.js';
import { authenticate } from '../middleware/index.js';

const router = express.Router();

// Help Guides routes
// Some routes are public to allow access without authentication
router.get('/guides', getHelpGuides);
router.get('/guides/:idOrSlug', getHelpGuide);

// Admin-only routes (require authentication)
router.post('/guides', authenticate, createHelpGuide);
router.put('/guides/:guideId', authenticate, updateHelpGuide);
router.delete('/guides/:guideId', authenticate, deleteHelpGuide);

// Help Tips routes
// Public route to get tips for a page
router.get('/tips', getHelpTips);

// User-specific routes (require authentication)
// Note: Order matters - put specific routes before parameterized routes
router.get('/tips/dismissed', authenticate, getDismissedHelpTips);
router.post('/tips/dismissed/reset', authenticate, resetDismissedHelpTips);

// Routes with parameters should come after specific routes
router.get('/tips/:tipId', getHelpTip);
router.post('/tips/:tipId/dismiss', authenticate, dismissHelpTip);

// Admin-only routes (require authentication)
router.post('/tips', authenticate, createHelpTip);
router.put('/tips/:tipId', authenticate, updateHelpTip);
router.delete('/tips/:tipId', authenticate, deleteHelpTip);



export default router;
