import express from 'express';
import {
  getFarmCrops,
  getCropById,
  createCrop,
  updateCrop,
  deleteCrop,
  getCropActivities,
  createCropActivity,
  updateCropActivity,
  deleteCropActivity,
  getCropsByFarmId
} from '../controllers/cropController.js';
import { authenticate } from '../middleware/authMiddleware.js';

const router = express.Router();

// Crop routes
router.get('/', authenticate, getCropsByFarmId);
router.get('/farm/:farmId', authenticate, getFarmCrops);

router.get('/:cropId', authenticate, getCropById);

router.post('/', authenticate, createCrop);

router.put('/:cropId', authenticate, updateCrop);

router.delete('/:cropId', authenticate, deleteCrop);

// Crop activity routes
router.get('/:cropId/activities', authenticate, getCropActivities);

router.post('/:cropId/activities', authenticate, createCropActivity);

router.put('/activities/:activityId', authenticate, updateCropActivity);

router.delete('/activities/:activityId', authenticate, deleteCropActivity);

export default router;
