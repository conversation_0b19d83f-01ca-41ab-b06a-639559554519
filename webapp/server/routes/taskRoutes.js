import express from 'express';
import { 
  getTasks, 
  getTaskById, 
  createTask, 
  updateTask, 
  deleteTask, 
  completeTask 
} from '../controllers/taskController.js';
import { authenticate } from '../middleware/authMiddleware.js';
import { checkFarmAccess } from '../middleware/farmAccessMiddleware.js';

const router = express.Router();

// Get all tasks for a specific farm (with filtering and pagination)
router.get('/farms/:farmId', authenticate, getTasks);

// Get a single task by ID
router.get('/:taskId', authenticate, getTaskById);

// Create a new task
router.post('/', authenticate, createTask);

// Update an existing task
router.put('/:taskId', authenticate, updateTask);

// Delete a task
router.delete('/:taskId', authenticate, deleteTask);

// Complete a task
router.post('/:taskId/complete', authenticate, completeTask);

// Keep the old route for backward compatibility (will be deprecated)
router.get('/', authenticate, getTasks);

export default router;
