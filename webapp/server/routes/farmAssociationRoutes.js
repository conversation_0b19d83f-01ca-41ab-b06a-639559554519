import express from 'express';
import { authenticate } from '../middleware/index.js';
import {
  createFarmAssociation,
  getFarmAssociations,
  getPendingAssociations,
  updateFarmAssociation,
  removeFarmAssociation,
  checkFarmAssociation,
  updateAssociationType
} from '../controllers/farmAssociationController.js';

const router = express.Router();

// Apply authentication middleware to all routes
router.use(authenticate);

// Create a new farm association
router.post('/', createFarmAssociation);

// Get all associations for a farm (both initiated and received)
router.get('/farm/:farmId', getFarmAssociations);

// Get all pending associations for a farm
router.get('/farm/:farmId/pending', getPendingAssociations);

// Update a farm association status
router.put('/:associationId', updateFarmAssociation);

// Remove a farm association
router.delete('/:associationId', removeFarmAssociation);

// Check if two farms are associated
router.get('/check/:farmId1/:farmId2', checkFarmAssociation);

// Update a farm association type (requires approval)
router.put('/:associationId/type', updateAssociationType);

export default router;
