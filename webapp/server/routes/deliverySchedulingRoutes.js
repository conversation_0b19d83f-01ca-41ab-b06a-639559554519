import express from 'express';
import {
  getFarmDeliverySchedules,
  createDeliverySchedule,
  updateDeliverySchedule,
  deleteDeliverySchedule,
  getDeliverySlots,
  createDeliverySlot,
  updateDeliverySlot,
  deleteDeliverySlot,
  getFarmDeliveryRoutes,
  createDeliveryRoute,
  updateDeliveryRoute,
  deleteDeliveryRoute,
  updateRouteAssignments,
  trackDriverLocation,
  getDriverLocationTracking,
  updateAssignmentStatus,
  getAvailableDeliverySlots
} from '../controllers/deliverySchedulingController.js';
import { authenticate, isAdmin } from '../middleware/authMiddleware.js';

const router = express.Router();

// Farm delivery schedules routes
router.get('/farms/:farmId/schedules', authenticate, getFarmDeliverySchedules);
router.post('/farms/:farmId/schedules', authenticate, createDeliverySchedule);
router.put('/schedules/:scheduleId', authenticate, updateDeliverySchedule);
router.delete('/schedules/:scheduleId', authenticate, deleteDeliverySchedule);

// Delivery slots routes
router.get('/schedules/:scheduleId/slots', authenticate, getDeliverySlots);
router.post('/schedules/:scheduleId/slots', authenticate, createDeliverySlot);
router.put('/slots/:slotId', authenticate, updateDeliverySlot);
router.delete('/slots/:slotId', authenticate, deleteDeliverySlot);

// Delivery routes management
router.get('/farms/:farmId/routes', authenticate, getFarmDeliveryRoutes);
router.post('/farms/:farmId/routes', authenticate, createDeliveryRoute);
router.put('/routes/:routeId', authenticate, updateDeliveryRoute);
router.delete('/routes/:routeId', authenticate, deleteDeliveryRoute);
router.put('/routes/:routeId/assignments', authenticate, updateRouteAssignments);

// Driver location tracking
router.post('/routes/:routeId/track', authenticate, trackDriverLocation);
router.get('/routes/:routeId/tracking', authenticate, getDriverLocationTracking);

// Delivery assignment status updates
router.put('/assignments/:assignmentId/status', authenticate, updateAssignmentStatus);

// Customer-facing routes
router.get('/marketplace/farms/:farmId/delivery-slots', getAvailableDeliverySlots);

export default router;