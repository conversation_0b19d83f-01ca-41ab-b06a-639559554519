import express from 'express';
import {
  getAuthorizationUrl,
  handleCallback,
  getConnection,
  disconnectQuickBooks,
  syncAccounts,
  importData,
  updateItem
} from '../controllers/quickbooksController.js';
import { authenticate } from '../middleware/authMiddleware.js';

const router = express.Router();

// QuickBooks OAuth routes
// Get authorization URL for QuickBooks OAuth
router.get('/auth', authenticate, getAuthorizationUrl);

// Handle OAuth callback from QuickBooks
router.get('/callback', handleCallback);

// QuickBooks connection routes
// Get QuickBooks connection for a farm
router.get('/connection/:farmId', authenticate, getConnection);

// Disconnect QuickBooks
router.delete('/connection/:connectionId', authenticate, disconnectQuickBooks);

// QuickBooks data routes
// Sync accounts from QuickBooks
router.get('/sync/accounts/:connectionId', authenticate, syncAccounts);

// Import QuickBooks data
router.get('/import/:connectionId/:dataType', authenticate, importData);

// Update an item in QuickBooks
router.post('/update/:connectionId/:dataType/:itemId', authenticate, updateItem);

export default router;
