import express from 'express';
import {
  getFieldSoilData,
  getFarmSoilData,
  getSoilSampleData
} from '../controllers/soilDataController.js';

const router = express.Router();

// Get soil data for a field
router.get('/field/:fieldId', getFieldSoilData);

// Get soil data for a farm
router.get('/farm/:farmId', getFarmSoilData);

// Get soil data for a specific soil sample
router.get('/sample/:sampleId', getSoilSampleData);

export default router;