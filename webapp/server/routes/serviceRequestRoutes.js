import express from 'express';
import { authenticate } from '../middleware/authMiddleware.js';
import * as serviceRequestController from '../controllers/serviceRequestController.js';

const router = express.Router();

// Apply authentication middleware to all service request routes
router.use(authenticate);

// Service Request Routes
router.get('/', serviceRequestController.getServiceRequests);
router.get('/:id', serviceRequestController.getServiceRequest);
router.post('/', serviceRequestController.createServiceRequest);
router.put('/:id', serviceRequestController.updateServiceRequest);
router.delete('/:id', serviceRequestController.deleteServiceRequest);

// Special service request operations
router.patch('/:id/status', serviceRequestController.updateServiceRequestStatus);
router.post('/:id/rate', serviceRequestController.rateServiceRequest);

export default router;