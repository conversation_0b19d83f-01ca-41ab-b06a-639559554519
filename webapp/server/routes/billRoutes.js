import express from 'express';
import {
  getFarmBills,
  getBillById,
  createBill,
  updateBill,
  deleteBill,
  addBillPayment,
  linkTransaction,
  unlinkTransaction,
  uploadAttachment,
  deleteAttachment,
  getBillStatistics
} from '../controllers/billController.js';
import { authenticate } from '../middleware/authMiddleware.js';
import multer from 'multer';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';

const router = express.Router();

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, 'uploads/temp');
  },
  filename: function (req, file, cb) {
    const uniqueFilename = `${uuidv4()}${path.extname(file.originalname)}`;
    cb(null, uniqueFilename);
  }
});

const upload = multer({ 
  storage: storage,
  limits: {
    fileSize: 10 * 1024 * 1024 // 10MB limit
  }
});

// Get all bills for a farm
router.get('/farm/:farmId', authenticate, getFarmBills);

// Get bill statistics for a farm
router.get('/farm/:farmId/statistics', authenticate, getBillStatistics);

// Get a single bill by ID
router.get('/:billId', authenticate, getBillById);

// Create a new bill
router.post('/', authenticate, createBill);

// Update a bill
router.put('/:billId', authenticate, updateBill);

// Delete a bill
router.delete('/:billId', authenticate, deleteBill);

// Add a payment to a bill
router.post('/:billId/payments', authenticate, addBillPayment);

// Link a transaction to a bill
router.post('/:billId/transactions', authenticate, linkTransaction);

// Unlink a transaction from a bill
router.delete('/:billId/transactions/:linkId', authenticate, unlinkTransaction);

// Upload an attachment to a bill
router.post('/:billId/attachments', authenticate, upload.single('file'), uploadAttachment);

// Delete an attachment from a bill
router.delete('/:billId/attachments/:attachmentId', authenticate, deleteAttachment);

export default router;