import express from 'express';
import {
  getReports,
  getReportById,
  createReport,
  updateReport,
  deleteReport,
  generateReportData,
  getReportDataByType
} from '../controllers/reportController.js';
import { authenticate } from '../middleware/authMiddleware.js';

const router = express.Router();

// Apply authentication middleware to all routes
router.use(authenticate);

// Get all reports for a farm
router.get('/', getReports);

// Get a single report by ID
router.get('/:reportId', getReportById);

// Create a new report
router.post('/', createReport);

// Update an existing report
router.put('/:reportId', updateReport);

// Delete a report
router.delete('/:reportId', deleteReport);

// Generate report data based on report type and parameters
router.get('/:reportId/data', generateReportData);

// Get report data by type (without requiring a saved report)
router.get('/type/:reportType', getReportDataByType);

export default router;