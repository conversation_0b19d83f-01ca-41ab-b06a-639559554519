import express from 'express';
import axios from 'axios';
import dotenv from 'dotenv';
import { 
  getFarm<PERSON><PERSON><PERSON>, 
  getField<PERSON>eat<PERSON>, 
  getFarmForecast, 
  getFieldForecast, 
  getFarmHourlyForecast, 
  getFieldHourlyForecast,
  getFieldAllWeatherData,
  getFarmAllWeatherData
} from '../controllers/weatherController.js';

dotenv.config();

const router = express.Router();

// National Weather Service API base URL
const NWS_API_BASE = 'https://api.weather.gov';

// Map NWS forecast icons to our icon codes (similar to OpenWeatherMap)
const mapNWSIconToCode = (iconUrl) => {
  if (!iconUrl) return '01d'; // default to clear day

  // Extract the icon name from the URL
  const iconName = iconUrl.split('/').pop().replace(/\?.*$/, '');

  // Check if it's day or night
  const isDay = !iconName.includes('night');
  const suffix = isDay ? 'd' : 'n';

  // Map NWS icons to codes similar to OpenWeatherMap
  if (iconName.includes('skc') || iconName.includes('few')) return `01${suffix}`; // clear
  if (iconName.includes('sct')) return `02${suffix}`; // partly cloudy
  if (iconName.includes('bkn')) return `03${suffix}`; // mostly cloudy
  if (iconName.includes('ovc')) return `04${suffix}`; // overcast
  if (iconName.includes('rain') || iconName.includes('showers')) return `09${suffix}`; // rain
  if (iconName.includes('tsra')) return `11${suffix}`; // thunderstorm
  if (iconName.includes('snow')) return `13${suffix}`; // snow
  if (iconName.includes('fog') || iconName.includes('haze')) return `50${suffix}`; // fog

  return `01${suffix}`; // default to clear
};

// Map NWS forecast descriptions to conditions (similar to OpenWeatherMap)
const mapNWSDescriptionToCondition = (description) => {
  if (!description) return 'Clear';

  const desc = description.toLowerCase();

  if (desc.includes('sunny') || desc.includes('clear')) return 'Clear';
  if (desc.includes('cloud')) return 'Clouds';
  if (desc.includes('rain') || desc.includes('shower')) return 'Rain';
  if (desc.includes('thunderstorm') || desc.includes('tstorm')) return 'Thunderstorm';
  if (desc.includes('snow') || desc.includes('flurries')) return 'Snow';
  if (desc.includes('fog') || desc.includes('mist') || desc.includes('haze')) return 'Mist';

  return 'Clear'; // default
};

// Enhanced weather endpoints
// Get weather data for a farm
// Optional query parameter: provider (nws, tomorrow, open-meteo)
router.get('/farm/:farmId', getFarmWeather);

// Get weather data for a field
// Optional query parameter: provider (nws, tomorrow, open-meteo)
router.get('/field/:fieldId', getFieldWeather);

// Get detailed forecast for a farm
// Optional query parameters: days (default: 7), provider (nws, tomorrow, open-meteo)
router.get('/forecast/farm/:farmId', getFarmForecast);

// Get detailed forecast for a field
// Optional query parameters: days (default: 7), provider (nws, tomorrow, open-meteo)
router.get('/forecast/field/:fieldId', getFieldForecast);

// Get hourly forecast for a farm
// Optional query parameters: hours (default: 48), provider (nws, tomorrow, open-meteo)
router.get('/hourly/farm/:farmId', getFarmHourlyForecast);

// Get hourly forecast for a field
// Optional query parameters: hours (default: 48), provider (nws, tomorrow, open-meteo)
router.get('/hourly/field/:fieldId', getFieldHourlyForecast);

// Get all weather data for a field in a single request
// Optional query parameters: days (default: 7), hours (default: 48), 
// startDate, endDate, interval (default: weekly), provider (nws, tomorrow, open-meteo)
router.get('/all/field/:fieldId', getFieldAllWeatherData);

// Get all weather data for a farm in a single request
// Optional query parameters: days (default: 7), hours (default: 48), 
// startDate, endDate, interval (default: weekly), provider (nws, tomorrow, open-meteo)
router.get('/all/farm/:farmId', getFarmAllWeatherData);

// Legacy proxy endpoint for weather data (kept for backward compatibility)
router.get('/proxy', async (req, res) => {
  try {
    const { lat, lon, units } = req.query;

    // Validate required parameters
    if (!lat || !lon) {
      return res.status(400).json({ error: 'Missing required parameters: lat and lon' });
    }

    console.log(`Fetching weather data for lat=${lat}, lon=${lon}, units=${units || 'imperial'}`);

    // Step 1: Get the grid point for the location
    const pointsUrl = `${NWS_API_BASE}/points/${lat},${lon}`;
    console.log(`Points URL: ${pointsUrl}`);

    const pointsResponse = await axios.get(pointsUrl, {
      headers: {
        'User-Agent': '(NxtAcre Farm Management, <EMAIL>)',
        'Accept': 'application/geo+json',
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    });

    const { gridId, gridX, gridY } = pointsResponse.data.properties;

    // Step 2: Get the forecast for the grid point
    const forecastUrl = `${NWS_API_BASE}/gridpoints/${gridId}/${gridX},${gridY}/forecast`;
    console.log(`Forecast URL: ${forecastUrl}`);

    const forecastResponse = await axios.get(forecastUrl, {
      headers: {
        'User-Agent': '(NxtAcre Farm Management, <EMAIL>)',
        'Accept': 'application/geo+json',
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    });

    // Step 3: Get the hourly forecast for current conditions
    const hourlyForecastUrl = `${NWS_API_BASE}/gridpoints/${gridId}/${gridX},${gridY}/forecast/hourly`;
    console.log(`Hourly Forecast URL: ${hourlyForecastUrl}`);

    const hourlyForecastResponse = await axios.get(hourlyForecastUrl, {
      headers: {
        'User-Agent': '(NxtAcre Farm Management, <EMAIL>)',
        'Accept': 'application/geo+json',
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    });

    // Process current weather data (from the first hourly forecast period)
    const currentPeriod = hourlyForecastResponse.data.properties.periods[0];

    // Convert temperature if needed
    let currentTemp = currentPeriod.temperature;
    if (units === 'metric' && currentPeriod.temperatureUnit === 'F') {
      currentTemp = Math.round((currentTemp - 32) * 5 / 9);
    }

    const current = {
      temp: Math.round(currentTemp),
      condition: mapNWSDescriptionToCondition(currentPeriod.shortForecast),
      icon: mapNWSIconToCode(currentPeriod.icon),
    };

    // Process hourly forecast data
    const hourlyPeriods = hourlyForecastResponse.data.properties.periods;
    const hourlyForecast = hourlyPeriods.slice(0, 24).map(period => {
      // Convert temperature if needed
      let temp = period.temperature;
      if (units === 'metric' && period.temperatureUnit === 'F') {
        temp = Math.round((temp - 32) * 5 / 9);
      }

      return {
        time: period.startTime,
        temp: Math.round(temp),
        condition: mapNWSDescriptionToCondition(period.shortForecast),
        icon: mapNWSIconToCode(period.icon),
        wind_speed: period.windSpeed,
        wind_direction: period.windDirection,
        precipitation_chance: period.probabilityOfPrecipitation?.value || 0,
        humidity: period.relativeHumidity?.value || 0
      };
    });

    // Process forecast data
    const dailyPeriods = forecastResponse.data.properties.periods;
    const dailyForecasts = {};

    // NWS API returns separate periods for day and night
    // We need to combine them to get high/low temps for each day
    dailyPeriods.forEach((period) => {
      const date = new Date(period.startTime);
      const day = date.toLocaleDateString('en-US', { weekday: 'short' });
      const isDaytime = period.isDaytime;

      if (!dailyForecasts[day]) {
        dailyForecasts[day] = { 
          high: isDaytime ? period.temperature : -Infinity,
          low: !isDaytime ? period.temperature : Infinity,
          condition: isDaytime ? mapNWSDescriptionToCondition(period.shortForecast) : '',
          icon: isDaytime ? mapNWSIconToCode(period.icon) : '',
          date: date
        };
      } else {
        if (isDaytime) {
          dailyForecasts[day].high = period.temperature;
          dailyForecasts[day].condition = mapNWSDescriptionToCondition(period.shortForecast);
          dailyForecasts[day].icon = mapNWSIconToCode(period.icon);
        } else {
          dailyForecasts[day].low = period.temperature;
        }
      }
    });

    // Convert to array and limit to 3 days
    const forecast = Object.entries(dailyForecasts)
      .sort((a, b) => a[1].date - b[1].date) // Sort by date
      .map(([day, data]) => {
        // Convert temperatures if needed
        let high = data.high;
        let low = data.low;

        if (units === 'metric' && dailyPeriods[0].temperatureUnit === 'F') {
          high = Math.round((high - 32) * 5 / 9);
          low = Math.round((low - 32) * 5 / 9);
        }

        return {
          day: day === new Date().toLocaleDateString('en-US', { weekday: 'short' }) ? 'Today' : day,
          high: Math.round(high),
          low: Math.round(low),
          condition: data.condition,
          icon: data.icon,
        };
      })
      .slice(0, 3);

    console.log('Weather data processed successfully');

    // Set cache-control headers on the response to prevent client-side caching
    res.set({
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      'Pragma': 'no-cache',
      'Expires': '0'
    });

    res.json({ current, forecast, hourly: hourlyForecast });
  } catch (error) {
    console.error('Weather proxy error:', error.message);
    if (error.response) {
      console.error('National Weather Service API response:', {
        status: error.response.status,
        data: error.response.data
      });
    }
    res.status(500).json({ 
      error: 'Failed to fetch weather data',
      message: error.message,
      details: process.env.NODE_ENV === 'development' ? error.toString() : undefined
    });
  }
});

export default router;
