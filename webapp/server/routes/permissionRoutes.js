import express from 'express';
import {
  checkPermission,
  getUserViewableFeatures,
  getDefaultRolePermissions,
  getFarmRolePermissions,
  setFarmRolePermissions
} from '../controllers/permissionController.js';
import { authenticate } from '../middleware/authMiddleware.js';

const router = express.Router();

// Check if a user has permission for a feature
router.get('/check', checkPermission);

// Get all features that a user has permission to view
router.get('/user/:userId/farm/:farmId/viewable', authenticate, getUserViewableFeatures);

// Get default permissions for a role
router.get('/defaults/:roleId', authenticate, getDefaultRolePermissions);

// Get permissions for a role in a farm
router.get('/farm/:farmId/role/:roleId', authenticate, getFarmRolePermissions);

// Set permissions for a role in a farm
router.post('/farm/:farmId/role/:roleId', authenticate, setFarmRolePermissions);

export default router;
