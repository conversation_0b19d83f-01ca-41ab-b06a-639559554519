import express from 'express';
import {
  getUsers,
  getUserById,
  updateUser,
  deleteUser,
  approveBusinessUser,
  getPendingApprovalUsers,
  updateCurrentUser,
  getCurrentUser,
  markOnboardingCompleted
} from '../controllers/userController.js';
import { getUserFarmById } from '../controllers/userFarmController.js';
import { authenticate, authenticateWithToken } from '../middleware/authMiddleware.js';

const router = express.Router();

// Public routes (none for users API - all routes should be protected)

// Protected routes (require authentication)
router.get('/me', authenticate, getCurrentUser);
router.patch('/me', authenticate, updateCurrentUser);
router.post('/mark-onboarding-completed', authenticate, markOnboardingCompleted);
// Special case for matrix chat user list - allow access with token authentication
router.get('/', (req, res, next) => {
  // Check if this is a request for the matrix chat user list
  if (req.query.matrix_chat === 'true') {
    // Use token authentication for matrix chat requests
    return authenticateWithToken(req, res, next);
  }
  // Use regular authentication for all other requests
  return authenticate(req, res, next);
}, getUsers);
router.get('/pending-approval', authenticate, getPendingApprovalUsers);
router.get('/:userId', authenticate, getUserById);
router.get('/:userId/details', authenticate, getUserById);
router.put('/:userId', authenticate, updateUser);
router.delete('/:userId', authenticate, deleteUser);
router.post('/:userId/approve', authenticate, approveBusinessUser);

// Get a specific UserFarm by ID
router.get('/farm/:id', authenticate, getUserFarmById);

export default router;
