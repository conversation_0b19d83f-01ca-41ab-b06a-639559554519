import express from 'express';
import {
  getExpenses,
  getExpenseById,
  createExpense,
  updateExpense,
  deleteExpense,
  getExpensesByEmployee,
  reviewExpense,
  getExpenseSummary
} from '../controllers/expenseController.js';
import { authenticate } from '../middleware/authMiddleware.js';

const router = express.Router();

// Get all expenses with optional filtering
router.get('/', getExpenses);

// Get expense summary
router.get('/summary', getExpenseSummary);

// Get expenses by employee ID
router.get('/employee/:employeeId', getExpensesByEmployee);

// Get expense by ID
router.get('/:expenseId', getExpenseById);

// Create a new expense
router.post('/', createExpense);

// Update an expense
router.put('/:expenseId', updateExpense);

// Delete an expense
router.delete('/:expenseId', deleteExpense);

// Review an expense (approve, deny, or reimburse)
router.put('/:expenseId/review', reviewExpense);

export default router;