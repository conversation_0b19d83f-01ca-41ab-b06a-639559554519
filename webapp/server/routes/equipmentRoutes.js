import express from 'express';
import {
  getAllEquipment,
  getFarmEquipment,
  getEquipmentById,
  getEquipmentByType,
  getEquipmentByStatus,
  createEquipment,
  updateEquipment,
  deleteEquipment
} from '../controllers/equipmentController.js';
import { authenticate } from '../middleware/authMiddleware.js';

const router = express.Router();

// Get all equipment (across all farms)
router.get('/', authenticate, getAllEquipment);

// Get equipment by type
router.get('/type/:type', authenticate, getEquipmentByType);

// Get equipment by status
router.get('/status/:status', authenticate, getEquipmentByStatus);

// Get all equipment for a farm
router.get('/farm/:farmId', authenticate, getFarmEquipment);

// Get a single equipment item by ID
router.get('/:equipmentId', authenticate, getEquipmentById);

// Create a new equipment item
router.post('/', authenticate, createEquipment);

// Update an equipment item
router.put('/:equipmentId', authenticate, updateEquipment);

// Delete an equipment item
router.delete('/:equipmentId', authenticate, deleteEquipment);

export default router;
