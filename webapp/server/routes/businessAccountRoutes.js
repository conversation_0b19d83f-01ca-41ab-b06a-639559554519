import express from 'express';
import {
  getBusinessAccount,
  updateBusinessAccount,
  createBusinessAccount
} from '../controllers/businessAccountController.js';
import { authenticate } from '../middleware/authMiddleware.js';

const router = express.Router();

// Get business account information
router.get('/', authenticate, getBusinessAccount);

// Update business account information
router.put('/:id', authenticate, updateBusinessAccount);

// Create business account
router.post('/', authenticate, createBusinessAccount);

export default router;