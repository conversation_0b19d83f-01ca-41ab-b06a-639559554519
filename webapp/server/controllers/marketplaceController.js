import { sequelize } from '../config/database.js';
import Product from '../models/Product.js';
import ProductImage from '../models/ProductImage.js';
import Farm from '../models/Farm.js';
import { Op } from 'sequelize';

// Get all products visible in the marketplace
export const getMarketplaceProducts = async (req, res) => {
  try {
    const { 
      search, 
      category, 
      farmId, 
      minPrice, 
      maxPrice, 
      page = 1, 
      limit = 20 
    } = req.query;

    // Build the where clause
    const where = {
      is_marketplace_visible: true,
      is_active: true
    };

    // Add search filter
    if (search) {
      where[Op.or] = [
        { name: { [Op.iLike]: `%${search}%` } },
        { description: { [Op.iLike]: `%${search}%` } },
        { marketplace_description: { [Op.iLike]: `%${search}%` } }
      ];
    }

    // Add category filter
    if (category) {
      where.marketplace_category = category;
    }

    // Add farm filter
    if (farmId) {
      where.farm_id = farmId;
    }

    // Add price range filter
    if (minPrice !== undefined) {
      where.price = { ...where.price, [Op.gte]: minPrice };
    }

    if (maxPrice !== undefined) {
      where.price = { ...where.price, [Op.lte]: maxPrice };
    }

    // Calculate pagination
    const offset = (page - 1) * limit;

    // Get products with their primary image and farm info
    const { count, rows: products } = await Product.findAndCountAll({
      where,
      include: [
        {
          model: ProductImage,
          as: 'images',
          where: { is_primary: true },
          required: false,
          limit: 1
        },
        {
          model: Farm,
          as: 'productFarm',
          attributes: ['id', 'name', 'logo_url', 'subdomain']
        }
      ],
      order: [['name', 'ASC']],
      limit,
      offset
    });

    // Calculate total pages
    const totalPages = Math.ceil(count / limit);

    res.status(200).json({
      products,
      pagination: {
        total: count,
        page: parseInt(page),
        limit: parseInt(limit),
        totalPages
      }
    });
  } catch (error) {
    console.error('Error fetching marketplace products:', error);
    res.status(500).json({ 
      message: "Failed to fetch marketplace products",
      errorType: "ServerError",
      errorCode: "500",
      context: {
        url: req.originalUrl,
        method: req.method
      }
    });
  }
};

// Get all products for a specific farm in the marketplace
export const getFarmMarketplaceProducts = async (req, res) => {
  try {
    const { farmId } = req.params;
    const { 
      search, 
      category, 
      minPrice, 
      maxPrice, 
      page = 1, 
      limit = 20 
    } = req.query;

    // Check if farm exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ 
        message: "Farm not found",
        errorType: "NotFoundError",
        errorCode: "404",
        context: {
          url: req.originalUrl,
          method: req.method
        }
      });
    }

    // Build the where clause
    const where = {
      farm_id: farmId,
      is_marketplace_visible: true,
      is_active: true
    };

    // Add search filter
    if (search) {
      where[Op.or] = [
        { name: { [Op.iLike]: `%${search}%` } },
        { description: { [Op.iLike]: `%${search}%` } },
        { marketplace_description: { [Op.iLike]: `%${search}%` } }
      ];
    }

    // Add category filter
    if (category) {
      where.marketplace_category = category;
    }

    // Add price range filter
    if (minPrice !== undefined) {
      where.price = { ...where.price, [Op.gte]: minPrice };
    }

    if (maxPrice !== undefined) {
      where.price = { ...where.price, [Op.lte]: maxPrice };
    }

    // Calculate pagination
    const offset = (page - 1) * limit;

    // Get products with their primary image
    const { count, rows: products } = await Product.findAndCountAll({
      where,
      include: [
        {
          model: ProductImage,
          as: 'images',
          where: { is_primary: true },
          required: false,
          limit: 1
        }
      ],
      order: [['name', 'ASC']],
      limit,
      offset
    });

    // Calculate total pages
    const totalPages = Math.ceil(count / limit);

    res.status(200).json({
      farm,
      products,
      pagination: {
        total: count,
        page: parseInt(page),
        limit: parseInt(limit),
        totalPages
      }
    });
  } catch (error) {
    console.error('Error fetching farm marketplace products:', error);
    res.status(500).json({ 
      message: "Failed to fetch farm marketplace products",
      errorType: "ServerError",
      errorCode: "500",
      context: {
        url: req.originalUrl,
        method: req.method
      }
    });
  }
};

// Get product categories for marketplace filtering
export const getMarketplaceCategories = async (req, res) => {
  try {
    // Get distinct categories from products that are marketplace visible
    const categories = await Product.findAll({
      attributes: [
        [sequelize.fn('DISTINCT', sequelize.col('marketplace_category')), 'category']
      ],
      where: {
        is_marketplace_visible: true,
        is_active: true,
        marketplace_category: {
          [Op.not]: null
        }
      },
      raw: true
    });

    // Extract category values
    const categoryList = categories
      .map(item => item.category)
      .filter(Boolean)
      .sort();

    res.status(200).json(categoryList);
  } catch (error) {
    console.error('Error fetching marketplace categories:', error);
    res.status(500).json({ 
      message: "Failed to fetch marketplace categories",
      errorType: "ServerError",
      errorCode: "500",
      context: {
        url: req.originalUrl,
        method: req.method
      }
    });
  }
};

// Get a single product from the marketplace
export const getMarketplaceProduct = async (req, res) => {
  try {
    const { productId } = req.params;

    const product = await Product.findOne({
      where: {
        id: productId,
        is_marketplace_visible: true,
        is_active: true
      },
      include: [
        {
          model: ProductImage,
          as: 'images',
          order: [['display_order', 'ASC']]
        },
        {
          model: Farm,
          as: 'productFarm',
          attributes: ['id', 'name', 'logo_url', 'subdomain', 'description']
        }
      ]
    });

    if (!product) {
      return res.status(404).json({ 
        message: "Product not found or not available in marketplace",
        errorType: "NotFoundError",
        errorCode: "404",
        context: {
          url: req.originalUrl,
          method: req.method
        }
      });
    }

    res.status(200).json(product);
  } catch (error) {
    console.error('Error fetching marketplace product:', error);
    res.status(500).json({ 
      message: "Failed to fetch marketplace product",
      errorType: "ServerError",
      errorCode: "500",
      context: {
        url: req.originalUrl,
        method: req.method
      }
    });
  }
};