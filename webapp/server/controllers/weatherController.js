import Weather from '../models/Weather.js';
import Farm from '../models/Farm.js';
import Field from '../models/Field.js';
import Alert from '../models/Alert.js';
import { sequelize } from '../config/database.js';
import { fetchWeatherAlerts } from './weatherAlertsController.js';
import { processHistoricalData, analyzeHistoricalData } from './historicalWeatherController.js';
import { 
  fetchWeatherData, 
  fetchDetailedForecast, 
  fetchHourlyForecast 
} from '../services/weatherServices.js';

// Get weather data for a farm
export const getFarmWeather = async (req, res) => {
  try {
    const { farmId } = req.params;
    const { provider } = req.query; // Optional preferred provider

    // Find farm to ensure it exists and get its location
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Get farm location data
    const farmLocation = farm.location_data;
    if (!farmLocation || !farmLocation.latitude || !farmLocation.longitude) {
      return res.status(400).json({ error: 'Farm location data is missing or invalid' });
    }

    // Fetch weather data from the preferred provider or fallback to the default order
    const weatherData = await fetchWeatherData(farmLocation.latitude, farmLocation.longitude, provider);

    // Store weather data in database
    await storeWeatherData(weatherData, farmId, null, farmLocation.latitude, farmLocation.longitude);

    return res.status(200).json(weatherData);
  } catch (error) {
    console.error('Error getting farm weather:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get weather data for a field
export const getFieldWeather = async (req, res) => {
  try {
    const { fieldId } = req.params;
    const { provider } = req.query; // Optional preferred provider

    // Find field to ensure it exists and get its location
    const field = await Field.findByPk(fieldId);
    if (!field) {
      return res.status(404).json({ error: 'Field not found' });
    }

    // Get field location data (center point)
    const fieldLocation = field.location_data;
    if (!fieldLocation || !fieldLocation.center || !fieldLocation.center.latitude || !fieldLocation.center.longitude) {
      return res.status(400).json({ error: 'Field location data is missing or invalid' });
    }

    // Fetch weather data from the preferred provider or fallback to the default order
    const weatherData = await fetchWeatherData(fieldLocation.center.latitude, fieldLocation.center.longitude, provider);

    // Store weather data in database
    await storeWeatherData(weatherData, field.farm_id, fieldId, fieldLocation.center.latitude, fieldLocation.center.longitude);

    return res.status(200).json(weatherData);
  } catch (error) {
    console.error('Error getting field weather:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get detailed weather forecast for a farm
export const getFarmForecast = async (req, res) => {
  try {
    const { farmId } = req.params;
    const { days = 7, provider } = req.query; // Default to 7 days, optional provider

    // Find farm to ensure it exists and get its location
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Get farm location data
    const farmLocation = farm.location_data;
    if (!farmLocation || !farmLocation.latitude || !farmLocation.longitude) {
      return res.status(400).json({ error: 'Farm location data is missing or invalid' });
    }

    // Fetch detailed forecast from the preferred provider or fallback to the default order
    const forecastData = await fetchDetailedForecast(farmLocation.latitude, farmLocation.longitude, parseInt(days), provider);

    return res.status(200).json(forecastData);
  } catch (error) {
    console.error('Error getting farm forecast:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get detailed weather forecast for a field
export const getFieldForecast = async (req, res) => {
  try {
    const { fieldId } = req.params;
    const { days = 7, provider } = req.query; // Default to 7 days, optional provider

    // Find field to ensure it exists and get its location
    const field = await Field.findByPk(fieldId);
    if (!field) {
      return res.status(404).json({ error: 'Field not found' });
    }

    // Get field location data (center point)
    const fieldLocation = field.location_data;
    if (!fieldLocation || !fieldLocation.center || !fieldLocation.center.latitude || !fieldLocation.center.longitude) {
      return res.status(400).json({ error: 'Field location data is missing or invalid' });
    }

    // Fetch detailed forecast from the preferred provider or fallback to the default order
    const forecastData = await fetchDetailedForecast(fieldLocation.center.latitude, fieldLocation.center.longitude, parseInt(days), provider);

    return res.status(200).json(forecastData);
  } catch (error) {
    console.error('Error getting field forecast:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get hourly forecast for a farm
export const getFarmHourlyForecast = async (req, res) => {
  try {
    const { farmId } = req.params;
    const { hours = 48, provider } = req.query; // Default to 48 hours, optional provider

    // Find farm to ensure it exists and get its location
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Get farm location data
    const farmLocation = farm.location_data;
    if (!farmLocation || !farmLocation.latitude || !farmLocation.longitude) {
      return res.status(400).json({ error: 'Farm location data is missing or invalid' });
    }

    // Fetch hourly forecast from the preferred provider or fallback to the default order
    const hourlyForecast = await fetchHourlyForecast(farmLocation.latitude, farmLocation.longitude, parseInt(hours), provider);

    return res.status(200).json(hourlyForecast);
  } catch (error) {
    console.error('Error getting farm hourly forecast:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get hourly forecast for a field
export const getFieldHourlyForecast = async (req, res) => {
  try {
    const { fieldId } = req.params;
    const { hours = 48, provider } = req.query; // Default to 48 hours, optional provider

    // Find field to ensure it exists and get its location
    const field = await Field.findByPk(fieldId);
    if (!field) {
      return res.status(404).json({ error: 'Field not found' });
    }

    // Get field location data (center point)
    const fieldLocation = field.location_data;
    if (!fieldLocation || !fieldLocation.center || !fieldLocation.center.latitude || !fieldLocation.center.longitude) {
      return res.status(400).json({ error: 'Field location data is missing or invalid' });
    }

    // Fetch hourly forecast from the preferred provider or fallback to the default order
    const hourlyForecast = await fetchHourlyForecast(fieldLocation.center.latitude, fieldLocation.center.longitude, parseInt(hours), provider);

    return res.status(200).json(hourlyForecast);
  } catch (error) {
    console.error('Error getting field hourly forecast:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Note: The fetchWeatherData, fetchDetailedForecast, and fetchHourlyForecast functions
// are now imported from ../services/weatherServices.js

// Get all weather data for a field in a single request
export const getFieldAllWeatherData = async (req, res) => {
  try {
    const { fieldId } = req.params;
    const { days = 7, hours = 48, startDate, endDate, interval = 'weekly', provider } = req.query;

    // Find field to ensure it exists and get its location
    const field = await Field.findByPk(fieldId);
    if (!field) {
      return res.status(404).json({ error: 'Field not found' });
    }

    // Get field location data (center point)
    const fieldLocation = field.location_data;
    if (!fieldLocation || !fieldLocation.center || !fieldLocation.center.latitude || !fieldLocation.center.longitude) {
      return res.status(400).json({ error: 'Field location data is missing or invalid' });
    }

    // Fetch all weather data in parallel
    const [weatherData, forecastData, hourlyForecast, alertsData, historicalData] = await Promise.all([
      fetchWeatherData(fieldLocation.center.latitude, fieldLocation.center.longitude, provider),
      fetchDetailedForecast(fieldLocation.center.latitude, fieldLocation.center.longitude, parseInt(days), provider),
      fetchHourlyForecast(fieldLocation.center.latitude, fieldLocation.center.longitude, parseInt(hours), provider),
      fetchWeatherAlerts(fieldLocation.center.latitude, fieldLocation.center.longitude),
      fetchHistoricalWeatherData(fieldId, startDate, endDate, interval)
    ]);

    // Store weather data in database
    await storeWeatherData(weatherData, field.farm_id, fieldId, fieldLocation.center.latitude, fieldLocation.center.longitude);

    // Combine all data into a single response
    return res.status(200).json({
      current: weatherData.current,
      forecast: weatherData.forecast,
      detailed_forecast: forecastData.forecast,
      hourly: hourlyForecast.hourly,
      alerts: alertsData.alerts,
      provider: weatherData.provider,
      historical: {
        data: historicalData.data,
        analysis: historicalData.analysis
      }
    });
  } catch (error) {
    console.error('Error getting all field weather data:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get all weather data for a farm in a single request
export const getFarmAllWeatherData = async (req, res) => {
  try {
    const { farmId } = req.params;
    const { days = 7, hours = 48, startDate, endDate, interval = 'weekly', provider } = req.query;

    // Find farm to ensure it exists and get its location
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Get farm location data
    const farmLocation = farm.location_data;
    if (!farmLocation || !farmLocation.latitude || !farmLocation.longitude) {
      return res.status(400).json({ error: 'Farm location data is missing or invalid' });
    }

    // Fetch all weather data in parallel
    const [weatherData, forecastData, hourlyForecast, alertsData, historicalData] = await Promise.all([
      fetchWeatherData(farmLocation.latitude, farmLocation.longitude, provider),
      fetchDetailedForecast(farmLocation.latitude, farmLocation.longitude, parseInt(days), provider),
      fetchHourlyForecast(farmLocation.latitude, farmLocation.longitude, parseInt(hours), provider),
      fetchWeatherAlerts(farmLocation.latitude, farmLocation.longitude),
      fetchHistoricalWeatherData(null, startDate, endDate, interval, farmId)
    ]);

    // Store weather data in database
    await storeWeatherData(weatherData, farmId, null, farmLocation.latitude, farmLocation.longitude);

    // Combine all data into a single response
    return res.status(200).json({
      current: weatherData.current,
      forecast: weatherData.forecast,
      detailed_forecast: forecastData.forecast,
      hourly: hourlyForecast.hourly,
      alerts: alertsData.alerts,
      provider: weatherData.provider,
      historical: {
        data: historicalData.data,
        analysis: historicalData.analysis
      }
    });
  } catch (error) {
    console.error('Error getting all farm weather data:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Helper function to fetch historical weather data
const fetchHistoricalWeatherData = async (fieldId, startDate, endDate, interval, farmId = null) => {
  try {
    // Build query conditions
    const where = {};

    if (fieldId) {
      where.field_id = fieldId;
    } else if (farmId) {
      where.farm_id = farmId;
      where.field_id = null; // Only get farm-level data
    } else {
      throw new Error('Either fieldId or farmId must be provided');
    }

    // Add date range if provided
    if (startDate || endDate) {
      where.timestamp = {};

      if (startDate) {
        where.timestamp[Op.gte] = new Date(startDate);
      }

      if (endDate) {
        const endDateTime = new Date(endDate);
        endDateTime.setHours(23, 59, 59, 999);
        where.timestamp[Op.lte] = endDateTime;
      }
    }

    // Get historical weather data
    const historicalData = await Weather.findAll({
      where,
      order: [['timestamp', 'ASC']]
    });

    // Process data based on interval
    const processedData = processHistoricalData(historicalData, interval);

    // Analyze the data
    const analysis = analyzeHistoricalData(processedData);

    return {
      data: processedData,
      analysis
    };
  } catch (error) {
    console.error('Error fetching historical weather data:', error);
    throw new Error(`Failed to fetch historical weather data: ${error.message}`);
  }
};

// Helper function to store weather data in database
const storeWeatherData = async (weatherData, farmId, fieldId, latitude, longitude) => {
  const transaction = await sequelize.transaction();

  try {
    // Store current weather
    await Weather.create({
      farm_id: farmId,
      field_id: fieldId,
      latitude,
      longitude,
      timestamp: new Date(weatherData.current.timestamp),
      temperature: weatherData.current.temp,
      feels_like: weatherData.current.feels_like,
      humidity: weatherData.current.humidity,
      wind_speed: parseFloat(weatherData.current.wind_speed),
      wind_direction: weatherData.current.wind_direction,
      precipitation: 0, // NWS doesn't provide current precipitation
      precipitation_chance: weatherData.current.precipitation_chance,
      condition: weatherData.current.condition,
      icon: weatherData.current.icon,
      forecast_type: 'current'
    }, { transaction });

    // Store hourly forecast
    for (let i = 0; i < weatherData.hourly.length; i++) {
      const hour = weatherData.hourly[i];
      await Weather.create({
        farm_id: farmId,
        field_id: fieldId,
        latitude,
        longitude,
        timestamp: new Date(hour.time),
        temperature: hour.temp,
        feels_like: hour.temp, // NWS doesn't provide feels_like
        humidity: hour.humidity,
        wind_speed: parseFloat(hour.wind_speed),
        wind_direction: hour.wind_direction,
        precipitation: 0, // NWS doesn't provide precipitation amount
        precipitation_chance: hour.precipitation_chance,
        condition: hour.condition,
        icon: hour.icon,
        forecast_type: 'hourly',
        forecast_hour: i
      }, { transaction });
    }

    // Store daily forecast
    for (let i = 0; i < weatherData.forecast.length; i++) {
      const day = weatherData.forecast[i];
      await Weather.create({
        farm_id: farmId,
        field_id: fieldId,
        latitude,
        longitude,
        timestamp: new Date(day.date),
        temperature: day.high, // Store high temperature
        feels_like: day.high, // NWS doesn't provide feels_like
        humidity: day.humidity,
        wind_speed: parseFloat(day.wind_speed),
        wind_direction: day.wind_direction,
        precipitation: 0, // NWS doesn't provide precipitation amount
        precipitation_chance: day.precipitation_chance,
        condition: day.condition,
        icon: day.icon,
        forecast_type: 'daily',
        forecast_day: i
      }, { transaction });
    }

    await transaction.commit();
  } catch (error) {
    await transaction.rollback();
    console.error('Error storing weather data:', error);
    throw new Error(`Failed to store weather data: ${error.message}`);
  }
};
