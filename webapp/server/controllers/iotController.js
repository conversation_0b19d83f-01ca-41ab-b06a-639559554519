import IoTDevice from '../models/IoTDevice.js';
import IoTData from '../models/IoTData.js';
import Farm from '../models/Farm.js';
import { sequelize } from '../config/database.js';
import { Op } from 'sequelize';
import { encryptCameraCredentials, decryptCameraCredentials } from '../utils/encryption.js';

// Get all IoT devices for a farm
export const getFarmIoTDevices = async (req, res) => {
  try {
    const { farmId } = req.params;

    // Find farm to ensure it exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Get all IoT devices for the farm
    const devices = await IoTDevice.findAll({
      where: { farm_id: farmId },
      order: [['name', 'ASC']]
    });

    // Decrypt camera credentials for frontend use
    devices.forEach(device => {
      if (device.device_type === 'camera' && device.configuration) {
        device.configuration = decryptCameraCredentials(device.configuration);
      }
    });

    return res.status(200).json({ devices });
  } catch (error) {
    console.error('Error getting farm IoT devices:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get a specific IoT device
export const getIoTDevice = async (req, res) => {
  try {
    const { deviceId } = req.params;

    // Find the device
    const device = await IoTDevice.findByPk(deviceId);
    if (!device) {
      return res.status(404).json({ error: 'IoT device not found' });
    }

    // If this is a camera device, decrypt the credentials
    if (device.device_type === 'camera' && device.configuration) {
      device.configuration = decryptCameraCredentials(device.configuration);
    }

    return res.status(200).json({ device });
  } catch (error) {
    console.error('Error getting IoT device:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Create a new IoT device
export const createIoTDevice = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const {
      farmId,
      name,
      deviceType,
      manufacturer,
      model,
      serialNumber,
      firmwareVersion,
      locationDescription,
      latitude,
      longitude,
      status,
      configuration
    } = req.body;

    // Validate required fields
    if (!farmId) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Farm ID is required' });
    }

    if (!name) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Device name is required' });
    }

    if (!deviceType) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Device type is required' });
    }

    // Find farm to ensure it exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Process configuration for camera devices
    let processedConfig = configuration;
    if (deviceType === 'camera' && configuration) {
      processedConfig = encryptCameraCredentials(configuration);
    }

    // Create the IoT device
    const device = await IoTDevice.create({
      farm_id: farmId,
      name,
      device_type: deviceType,
      manufacturer,
      model,
      serial_number: serialNumber,
      firmware_version: firmwareVersion,
      location_description: locationDescription,
      latitude,
      longitude,
      status: status || 'active',
      configuration: processedConfig
    }, { transaction });

    await transaction.commit();

    return res.status(201).json({
      message: 'IoT device created successfully',
      device
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error creating IoT device:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Update an IoT device
export const updateIoTDevice = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { deviceId } = req.params;
    const {
      name,
      deviceType,
      manufacturer,
      model,
      serialNumber,
      firmwareVersion,
      locationDescription,
      latitude,
      longitude,
      status,
      configuration
    } = req.body;

    // Find the device
    const device = await IoTDevice.findByPk(deviceId);
    if (!device) {
      await transaction.rollback();
      return res.status(404).json({ error: 'IoT device not found' });
    }

    // Process configuration for camera devices
    let processedConfig = configuration;
    if (device.device_type === 'camera' || deviceType === 'camera') {
      if (configuration !== undefined) {
        // For Amcrest cameras, ensure all required fields are preserved
        if (configuration.cameraType === 'amcrest') {
          // If we have existing configuration and it's an Amcrest camera
          if (device.configuration && device.configuration.cameraType === 'amcrest') {
            // Merge the new configuration with the existing one to preserve all fields
            processedConfig = {
              ...device.configuration,
              ...configuration,
              // If password is not provided in the new config, keep the existing one
              password: configuration.password || device.configuration.password
            };
          }
        }
        // Encrypt the camera credentials
        processedConfig = encryptCameraCredentials(processedConfig);
      } else if (device.configuration) {
        // Keep existing configuration
        processedConfig = device.configuration;
      }
    }

    // Update the device
    await device.update({
      name: name || device.name,
      device_type: deviceType || device.device_type,
      manufacturer: manufacturer !== undefined ? manufacturer : device.manufacturer,
      model: model !== undefined ? model : device.model,
      serial_number: serialNumber !== undefined ? serialNumber : device.serial_number,
      firmware_version: firmwareVersion !== undefined ? firmwareVersion : device.firmware_version,
      location_description: locationDescription !== undefined ? locationDescription : device.location_description,
      latitude: latitude !== undefined ? latitude : device.latitude,
      longitude: longitude !== undefined ? longitude : device.longitude,
      status: status || device.status,
      configuration: processedConfig !== undefined ? processedConfig : device.configuration
    }, { transaction });

    await transaction.commit();

    return res.status(200).json({
      message: 'IoT device updated successfully',
      device
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error updating IoT device:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Delete an IoT device
export const deleteIoTDevice = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { deviceId } = req.params;

    // Find the device
    const device = await IoTDevice.findByPk(deviceId);
    if (!device) {
      await transaction.rollback();
      return res.status(404).json({ error: 'IoT device not found' });
    }

    // Delete the device
    await device.destroy({ transaction });

    await transaction.commit();

    return res.status(200).json({
      message: 'IoT device deleted successfully'
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error deleting IoT device:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get IoT data for a specific device
export const getDeviceIoTData = async (req, res) => {
  try {
    const { deviceId } = req.params;
    const { startDate, endDate, limit = 100, offset = 0 } = req.query;

    // Find device to ensure it exists
    const device = await IoTDevice.findByPk(deviceId);
    if (!device) {
      return res.status(404).json({ error: 'IoT device not found' });
    }

    // Build query conditions
    const whereConditions = { device_id: deviceId };

    // Add date range filter if provided
    if (startDate && endDate) {
      whereConditions.timestamp = {
        [Op.between]: [new Date(startDate), new Date(endDate)]
      };
    } else if (startDate) {
      whereConditions.timestamp = {
        [Op.gte]: new Date(startDate)
      };
    } else if (endDate) {
      whereConditions.timestamp = {
        [Op.lte]: new Date(endDate)
      };
    }

    // Get IoT data with pagination
    const iotData = await IoTData.findAndCountAll({
      where: whereConditions,
      order: [['timestamp', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

    return res.status(200).json({
      iotData: iotData.rows,
      total: iotData.count,
      limit: parseInt(limit),
      offset: parseInt(offset)
    });
  } catch (error) {
    console.error('Error getting device IoT data:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get the latest IoT data for a device
export const getLatestIoTData = async (req, res) => {
  try {
    const { deviceId } = req.params;

    // Find device to ensure it exists
    const device = await IoTDevice.findByPk(deviceId);
    if (!device) {
      return res.status(404).json({ error: 'IoT device not found' });
    }

    // Get the latest IoT data
    const latestData = await IoTData.findOne({
      where: { device_id: deviceId },
      order: [['timestamp', 'DESC']]
    });

    if (!latestData) {
      return res.status(404).json({ error: 'No IoT data found for this device' });
    }

    return res.status(200).json({ iotData: latestData });
  } catch (error) {
    console.error('Error getting latest IoT data:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Create new IoT data record
export const createIoTData = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const {
      deviceId,
      timestamp,
      latitude,
      longitude,
      altitude,
      temperature,
      humidity,
      pressure,
      soilMoisture,
      soilTemperature,
      lightLevel,
      batteryLevel,
      signalStrength,
      status,
      rawData,
      customFields
    } = req.body;

    // Validate required fields
    if (!deviceId) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Device ID is required' });
    }

    // Find device to ensure it exists
    const device = await IoTDevice.findByPk(deviceId);
    if (!device) {
      await transaction.rollback();
      return res.status(404).json({ error: 'IoT device not found' });
    }

    // Create IoT data record
    const iotData = await IoTData.create({
      device_id: deviceId,
      timestamp: timestamp || new Date(),
      latitude,
      longitude,
      altitude,
      temperature,
      humidity,
      pressure,
      soil_moisture: soilMoisture,
      soil_temperature: soilTemperature,
      light_level: lightLevel,
      battery_level: batteryLevel,
      signal_strength: signalStrength,
      status,
      raw_data: rawData,
      custom_fields: customFields
    }, { transaction });

    // Update the device's last communication timestamp and status
    await device.update({
      last_communication: timestamp || new Date(),
      battery_level: batteryLevel !== undefined ? batteryLevel : device.battery_level,
      signal_strength: signalStrength !== undefined ? signalStrength : device.signal_strength
    }, { transaction });

    await transaction.commit();

    return res.status(201).json({
      message: 'IoT data created successfully',
      iotData
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error creating IoT data:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Receive IoT data from external systems
export const receiveExternalIoTData = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const {
      apiKey,
      deviceIdentifier,
      iotData
    } = req.body;

    // Validate API key (in a real system, this would be more secure)
    if (!apiKey || apiKey !== process.env.IOT_API_KEY) {
      await transaction.rollback();
      return res.status(401).json({ error: 'Invalid API key' });
    }

    // Validate required fields
    if (!deviceIdentifier || !iotData) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Device identifier and IoT data are required' });
    }

    // Find device by identifier (could be serial number, name, etc.)
    const device = await IoTDevice.findOne({
      where: {
        [Op.or]: [
          { id: deviceIdentifier },
          { name: deviceIdentifier },
          { serial_number: deviceIdentifier }
        ]
      }
    });

    if (!device) {
      await transaction.rollback();
      return res.status(404).json({ error: 'IoT device not found' });
    }

    // Process and store the IoT data
    const createdIoTData = await IoTData.create({
      device_id: device.id,
      timestamp: iotData.timestamp || new Date(),
      latitude: iotData.latitude,
      longitude: iotData.longitude,
      altitude: iotData.altitude,
      temperature: iotData.temperature,
      humidity: iotData.humidity,
      pressure: iotData.pressure,
      soil_moisture: iotData.soilMoisture,
      soil_temperature: iotData.soilTemperature,
      light_level: iotData.lightLevel,
      battery_level: iotData.batteryLevel,
      signal_strength: iotData.signalStrength,
      status: iotData.status,
      raw_data: iotData.rawData || iotData,
      custom_fields: iotData.customFields
    }, { transaction });

    // Update the device's last communication timestamp and status
    await device.update({
      last_communication: iotData.timestamp || new Date(),
      battery_level: iotData.batteryLevel !== undefined ? iotData.batteryLevel : device.battery_level,
      signal_strength: iotData.signalStrength !== undefined ? iotData.signalStrength : device.signal_strength
    }, { transaction });

    await transaction.commit();

    return res.status(201).json({
      message: 'IoT data received and processed successfully',
      iotDataId: createdIoTData.id
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error processing external IoT data:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get aggregated IoT data for reporting
export const getAggregatedIoTData = async (req, res) => {
  try {
    const { deviceId } = req.params;
    const { startDate, endDate, interval = 'day', metric } = req.query;
    const schema = process.env.DB_SCHEMA || 'site';

    // Find device to ensure it exists
    const device = await IoTDevice.findByPk(deviceId);
    if (!device) {
      return res.status(404).json({ error: 'IoT device not found' });
    }

    // Validate dates
    if (!startDate || !endDate) {
      return res.status(400).json({ error: 'Start date and end date are required' });
    }

    // Validate metric
    if (!metric) {
      return res.status(400).json({ error: 'Metric is required (e.g., temperature, humidity)' });
    }

    // Define time interval for aggregation
    let timeFormat;
    switch (interval) {
      case 'hour':
        timeFormat = 'YYYY-MM-DD HH24';
        break;
      case 'day':
        timeFormat = 'YYYY-MM-DD';
        break;
      case 'week':
        timeFormat = 'YYYY-WW';
        break;
      case 'month':
        timeFormat = 'YYYY-MM';
        break;
      default:
        timeFormat = 'YYYY-MM-DD';
    }

    // Map metric to column name
    let columnName;
    switch (metric) {
      case 'temperature':
        columnName = 'temperature';
        break;
      case 'humidity':
        columnName = 'humidity';
        break;
      case 'pressure':
        columnName = 'pressure';
        break;
      case 'soilMoisture':
        columnName = 'soil_moisture';
        break;
      case 'soilTemperature':
        columnName = 'soil_temperature';
        break;
      case 'lightLevel':
        columnName = 'light_level';
        break;
      case 'batteryLevel':
        columnName = 'battery_level';
        break;
      case 'signalStrength':
        columnName = 'signal_strength';
        break;
      default:
        return res.status(400).json({ error: 'Invalid metric' });
    }

    // Get aggregated data
    const aggregatedData = await sequelize.query(`
      SELECT 
        TO_CHAR(timestamp, '${timeFormat}') as time_period,
        AVG(${columnName}) as avg_value,
        MIN(${columnName}) as min_value,
        MAX(${columnName}) as max_value,
        COUNT(*) as data_points
      FROM ${schema}.iot_data
      WHERE device_id = :deviceId
        AND timestamp BETWEEN :startDate AND :endDate
        AND ${columnName} IS NOT NULL
      GROUP BY time_period
      ORDER BY time_period
    `, {
      replacements: { 
        deviceId, 
        startDate: new Date(startDate), 
        endDate: new Date(endDate),
        schema: process.env.DB_SCHEMA || 'site'
      },
      type: sequelize.QueryTypes.SELECT
    });

    return res.status(200).json({ 
      metric,
      aggregatedData 
    });
  } catch (error) {
    console.error('Error getting aggregated IoT data:', error);
    return res.status(500).json({ error: error.message });
  }
};
