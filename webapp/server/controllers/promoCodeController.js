import PromoCode from '../models/PromoCode.js';
import SubscriptionPlan from '../models/SubscriptionPlan.js';
import User from '../models/User.js';
import Farm from '../models/Farm.js';
import { sequelize } from '../config/database.js';
import { Op } from 'sequelize';

// Get all promo codes (admin only)
export const getAllPromoCodes = async (req, res) => {
  try {
    const promoCodes = await PromoCode.findAll({
      include: [
        {
          model: SubscriptionPlan,
          as: 'subscriptionPlan',
          attributes: ['id', 'name']
        },
        {
          model: User,
          as: 'user',
          attributes: ['id', 'email', 'first_name', 'last_name']
        },
        {
          model: Farm,
          as: 'farm',
          attributes: ['id', 'name']
        }
      ],
      order: [['created_at', 'DESC']]
    });

    return res.status(200).json({ promoCodes });
  } catch (error) {
    console.error('Error getting promo codes:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get a single promo code by ID (admin only)
export const getPromoCodeById = async (req, res) => {
  try {
    const { promoCodeId } = req.params;

    const promoCode = await PromoCode.findByPk(promoCodeId, {
      include: [
        {
          model: SubscriptionPlan,
          as: 'subscriptionPlan',
          attributes: ['id', 'name']
        },
        {
          model: User,
          as: 'user',
          attributes: ['id', 'email', 'first_name', 'last_name']
        },
        {
          model: Farm,
          as: 'farm',
          attributes: ['id', 'name']
        }
      ]
    });

    if (!promoCode) {
      return res.status(404).json({ error: 'Promo code not found' });
    }

    return res.status(200).json({ promoCode });
  } catch (error) {
    console.error('Error getting promo code:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Create a new promo code (admin only)
export const createPromoCode = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { 
      code, 
      description, 
      subscription_plan_id, 
      valid_from, 
      valid_to, 
      max_uses, 
      user_id, 
      farm_id, 
      discount_percent, 
      discount_amount, 
      discount_months, 
      applies_to_monthly, 
      applies_to_yearly, 
      is_active 
    } = req.body;

    // Validate required fields
    if (!code) {
      await transaction.rollback();
      return res.status(400).json({ 
        error: 'Code is required' 
      });
    }

    // Check if code already exists
    const existingCode = await PromoCode.findOne({
      where: { code },
      transaction
    });

    if (existingCode) {
      await transaction.rollback();
      return res.status(400).json({ 
        error: 'Promo code already exists' 
      });
    }

    // Validate that either discount_percent or discount_amount is provided, but not both
    if ((!discount_percent && !discount_amount) || (discount_percent && discount_amount)) {
      await transaction.rollback();
      return res.status(400).json({ 
        error: 'Either discount_percent or discount_amount must be provided, but not both' 
      });
    }

    // If subscription_plan_id is provided, check if it exists
    if (subscription_plan_id) {
      const plan = await SubscriptionPlan.findByPk(subscription_plan_id, { transaction });
      if (!plan) {
        await transaction.rollback();
        return res.status(404).json({ error: 'Subscription plan not found' });
      }
    }

    // If user_id is provided, check if it exists
    if (user_id) {
      const user = await User.findByPk(user_id, { transaction });
      if (!user) {
        await transaction.rollback();
        return res.status(404).json({ error: 'User not found' });
      }
    }

    // If farm_id is provided, check if it exists
    if (farm_id) {
      const farm = await Farm.findByPk(farm_id, { transaction });
      if (!farm) {
        await transaction.rollback();
        return res.status(404).json({ error: 'Farm not found' });
      }
    }

    // Create promo code
    const promoCode = await PromoCode.create({
      code,
      description,
      subscription_plan_id,
      valid_from: valid_from || new Date(),
      valid_to,
      max_uses,
      current_uses: 0,
      user_id,
      farm_id,
      discount_percent,
      discount_amount,
      discount_months,
      applies_to_monthly: applies_to_monthly !== undefined ? applies_to_monthly : true,
      applies_to_yearly: applies_to_yearly !== undefined ? applies_to_yearly : true,
      is_active: is_active !== undefined ? is_active : true
    }, { transaction });

    await transaction.commit();

    return res.status(201).json({ 
      message: 'Promo code created successfully',
      promoCode 
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error creating promo code:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Update a promo code (admin only)
export const updatePromoCode = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { promoCodeId } = req.params;
    const { 
      code, 
      description, 
      subscription_plan_id, 
      valid_from, 
      valid_to, 
      max_uses, 
      user_id, 
      farm_id, 
      discount_percent, 
      discount_amount, 
      discount_months, 
      applies_to_monthly, 
      applies_to_yearly, 
      is_active 
    } = req.body;

    // Find promo code to ensure it exists
    const promoCode = await PromoCode.findByPk(promoCodeId, { transaction });
    if (!promoCode) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Promo code not found' });
    }

    // If code is being changed, check if the new code already exists
    if (code && code !== promoCode.code) {
      const existingCode = await PromoCode.findOne({
        where: { code },
        transaction
      });

      if (existingCode) {
        await transaction.rollback();
        return res.status(400).json({ 
          error: 'Promo code already exists' 
        });
      }
    }

    // Validate that either discount_percent or discount_amount is provided, but not both
    if (
      (discount_percent !== undefined && discount_amount !== undefined) &&
      (discount_percent !== null && discount_amount !== null)
    ) {
      await transaction.rollback();
      return res.status(400).json({ 
        error: 'Either discount_percent or discount_amount must be provided, but not both' 
      });
    }

    // If subscription_plan_id is provided, check if it exists
    if (subscription_plan_id) {
      const plan = await SubscriptionPlan.findByPk(subscription_plan_id, { transaction });
      if (!plan) {
        await transaction.rollback();
        return res.status(404).json({ error: 'Subscription plan not found' });
      }
    }

    // If user_id is provided, check if it exists
    if (user_id) {
      const user = await User.findByPk(user_id, { transaction });
      if (!user) {
        await transaction.rollback();
        return res.status(404).json({ error: 'User not found' });
      }
    }

    // If farm_id is provided, check if it exists
    if (farm_id) {
      const farm = await Farm.findByPk(farm_id, { transaction });
      if (!farm) {
        await transaction.rollback();
        return res.status(404).json({ error: 'Farm not found' });
      }
    }

    // Update promo code
    await promoCode.update({
      code: code || promoCode.code,
      description: description !== undefined ? description : promoCode.description,
      subscription_plan_id: subscription_plan_id !== undefined ? subscription_plan_id : promoCode.subscription_plan_id,
      valid_from: valid_from || promoCode.valid_from,
      valid_to: valid_to !== undefined ? valid_to : promoCode.valid_to,
      max_uses: max_uses !== undefined ? max_uses : promoCode.max_uses,
      user_id: user_id !== undefined ? user_id : promoCode.user_id,
      farm_id: farm_id !== undefined ? farm_id : promoCode.farm_id,
      discount_percent: discount_percent !== undefined ? discount_percent : promoCode.discount_percent,
      discount_amount: discount_amount !== undefined ? discount_amount : promoCode.discount_amount,
      discount_months: discount_months !== undefined ? discount_months : promoCode.discount_months,
      applies_to_monthly: applies_to_monthly !== undefined ? applies_to_monthly : promoCode.applies_to_monthly,
      applies_to_yearly: applies_to_yearly !== undefined ? applies_to_yearly : promoCode.applies_to_yearly,
      is_active: is_active !== undefined ? is_active : promoCode.is_active
    }, { transaction });

    await transaction.commit();

    return res.status(200).json({ 
      message: 'Promo code updated successfully',
      promoCode 
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error updating promo code:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Delete a promo code (admin only)
export const deletePromoCode = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { promoCodeId } = req.params;

    // Find promo code to ensure it exists
    const promoCode = await PromoCode.findByPk(promoCodeId);
    if (!promoCode) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Promo code not found' });
    }

    // Delete promo code
    await promoCode.destroy({ transaction });

    await transaction.commit();

    return res.status(200).json({ 
      message: 'Promo code deleted successfully' 
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error deleting promo code:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Validate a promo code (for users applying a code)
export const validatePromoCode = async (req, res) => {
  try {
    const { code, subscriptionPlanId, userId, farmId, billingCycle } = req.body;

    if (!code) {
      return res.status(400).json({ error: 'Promo code is required' });
    }

    // Find the promo code
    const promoCode = await PromoCode.findOne({
      where: { 
        code,
        is_active: true,
        [Op.or]: [
          { valid_to: null },
          { valid_to: { [Op.gte]: new Date() } }
        ],
        valid_from: { [Op.lte]: new Date() }
      },
      include: [
        {
          model: SubscriptionPlan,
          as: 'subscriptionPlan',
          attributes: ['id', 'name']
        }
      ]
    });

    if (!promoCode) {
      return res.status(404).json({ error: 'Invalid or expired promo code' });
    }

    // Check if the promo code has reached its maximum uses
    if (promoCode.max_uses !== null && promoCode.current_uses >= promoCode.max_uses) {
      return res.status(400).json({ error: 'Promo code has reached its maximum number of uses' });
    }

    // Check if the promo code is specific to a subscription plan
    if (promoCode.subscription_plan_id && subscriptionPlanId && promoCode.subscription_plan_id !== subscriptionPlanId) {
      return res.status(400).json({ error: 'Promo code is not valid for this subscription plan' });
    }

    // Check if the promo code is specific to a user
    if (promoCode.user_id && userId && promoCode.user_id !== userId) {
      return res.status(400).json({ error: 'Promo code is not valid for this user' });
    }

    // Check if the promo code is specific to a farm
    if (promoCode.farm_id && farmId && promoCode.farm_id !== farmId) {
      return res.status(400).json({ error: 'Promo code is not valid for this farm' });
    }

    // Check if the promo code applies to the selected billing cycle
    if (billingCycle) {
      if (billingCycle === 'monthly' && !promoCode.applies_to_monthly) {
        return res.status(400).json({ error: 'Promo code is not valid for monthly billing' });
      }
      if (billingCycle === 'yearly' && !promoCode.applies_to_yearly) {
        return res.status(400).json({ error: 'Promo code is not valid for yearly billing' });
      }
    }

    // Promo code is valid
    return res.status(200).json({ 
      message: 'Promo code is valid',
      promoCode: {
        id: promoCode.id,
        code: promoCode.code,
        description: promoCode.description,
        discount_percent: promoCode.discount_percent,
        discount_amount: promoCode.discount_amount,
        discount_months: promoCode.discount_months,
        subscription_plan: promoCode.subscriptionPlan
      }
    });
  } catch (error) {
    console.error('Error validating promo code:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Apply a promo code (increment usage counter)
export const applyPromoCode = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { code } = req.body;

    if (!code) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Promo code is required' });
    }

    // Find the promo code
    const promoCode = await PromoCode.findOne({
      where: { 
        code,
        is_active: true,
        [Op.or]: [
          { valid_to: null },
          { valid_to: { [Op.gte]: new Date() } }
        ],
        valid_from: { [Op.lte]: new Date() }
      },
      transaction
    });

    if (!promoCode) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Invalid or expired promo code' });
    }

    // Check if the promo code has reached its maximum uses
    if (promoCode.max_uses !== null && promoCode.current_uses >= promoCode.max_uses) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Promo code has reached its maximum number of uses' });
    }

    // Increment the usage counter
    await promoCode.update({
      current_uses: promoCode.current_uses + 1
    }, { transaction });

    await transaction.commit();

    return res.status(200).json({ 
      message: 'Promo code applied successfully',
      promoCode: {
        id: promoCode.id,
        code: promoCode.code,
        current_uses: promoCode.current_uses + 1
      }
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error applying promo code:', error);
    return res.status(500).json({ error: error.message });
  }
};