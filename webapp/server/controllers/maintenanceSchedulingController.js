import { Op } from 'sequelize';
import Farm from '../models/Farm.js';
import Equipment from '../models/Equipment.js';
import MaintenanceSchedule from '../models/MaintenanceSchedule.js';
import MaintenanceLog from '../models/MaintenanceLog.js';

/**
 * Get maintenance schedules for a farm
 */
export const getMaintenanceSchedules = async (req, res) => {
  try {
    const { farmId } = req.params;
    const { 
      equipmentId, 
      status, 
      upcoming = false,
      overdue = false,
      sort = 'next_date', 
      order = 'asc',
      limit = 100,
      offset = 0
    } = req.query;

    // Validate farm exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Build query conditions
    const whereConditions = {
      farm_id: farmId
    };

    if (equipmentId) {
      whereConditions.equipment_id = equipmentId;
    }

    if (status) {
      whereConditions.status = status;
    }

    // Filter for upcoming maintenance (due in the next 30 days)
    if (upcoming === 'true' || upcoming === true) {
      const today = new Date();
      const thirtyDaysFromNow = new Date();
      thirtyDaysFromNow.setDate(today.getDate() + 30);
      
      whereConditions.next_date = {
        [Op.between]: [today, thirtyDaysFromNow]
      };
    }

    // Filter for overdue maintenance
    if (overdue === 'true' || overdue === true) {
      const today = new Date();
      
      whereConditions.next_date = {
        [Op.lt]: today
      };
      whereConditions.status = {
        [Op.ne]: 'completed'
      };
    }

    // Determine sort field and order
    const sortField = sort === 'next_date' ? 'next_date' : 
                      sort === 'last_date' ? 'last_date' : 
                      sort === 'interval' ? 'interval_value' : 'next_date';
    
    const sortOrder = order.toLowerCase() === 'desc' ? 'DESC' : 'ASC';

    // Get maintenance schedules
    const schedules = await MaintenanceSchedule.findAndCountAll({
      where: whereConditions,
      include: [
        { model: Equipment, as: 'equipment' }
      ],
      order: [[sortField, sortOrder]],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

    // Format the schedules
    const formattedSchedules = schedules.rows.map(schedule => ({
      id: schedule.id,
      name: schedule.name,
      description: schedule.description,
      equipmentId: schedule.equipment_id,
      equipment: schedule.equipment ? {
        id: schedule.equipment.id,
        name: `${schedule.equipment.make} ${schedule.equipment.model}`,
        type: schedule.equipment.type
      } : null,
      maintenanceType: schedule.maintenance_type,
      intervalValue: schedule.interval_value,
      intervalUnit: schedule.interval_unit,
      lastDate: schedule.last_date,
      nextDate: schedule.next_date,
      status: schedule.status,
      notes: schedule.notes,
      createdAt: schedule.created_at,
      updatedAt: schedule.updated_at
    }));

    return res.status(200).json({
      maintenanceSchedules: formattedSchedules,
      total: schedules.count,
      limit: parseInt(limit),
      offset: parseInt(offset)
    });
  } catch (error) {
    console.error('Error getting maintenance schedules:', error);
    return res.status(500).json({ error: 'Failed to get maintenance schedules' });
  }
};

/**
 * Get a specific maintenance schedule
 */
export const getMaintenanceSchedule = async (req, res) => {
  try {
    const { scheduleId } = req.params;

    // Find the maintenance schedule
    const schedule = await MaintenanceSchedule.findByPk(scheduleId, {
      include: [
        { model: Equipment, as: 'equipment' }
      ]
    });

    if (!schedule) {
      return res.status(404).json({ error: 'Maintenance schedule not found' });
    }

    // Get maintenance logs for this schedule
    const logs = await MaintenanceLog.findAll({
      where: {
        schedule_id: scheduleId
      },
      order: [['date', 'DESC']]
    });

    // Format the schedule
    const formattedSchedule = {
      id: schedule.id,
      name: schedule.name,
      description: schedule.description,
      equipmentId: schedule.equipment_id,
      equipment: schedule.equipment ? {
        id: schedule.equipment.id,
        name: `${schedule.equipment.make} ${schedule.equipment.model}`,
        type: schedule.equipment.type
      } : null,
      maintenanceType: schedule.maintenance_type,
      intervalValue: schedule.interval_value,
      intervalUnit: schedule.interval_unit,
      lastDate: schedule.last_date,
      nextDate: schedule.next_date,
      status: schedule.status,
      notes: schedule.notes,
      createdAt: schedule.created_at,
      updatedAt: schedule.updated_at,
      maintenanceLogs: logs.map(log => ({
        id: log.id,
        date: log.date,
        performedBy: log.performed_by,
        cost: log.cost,
        notes: log.notes,
        status: log.status,
        createdAt: log.created_at,
        updatedAt: log.updated_at
      }))
    };

    return res.status(200).json({
      maintenanceSchedule: formattedSchedule
    });
  } catch (error) {
    console.error('Error getting maintenance schedule:', error);
    return res.status(500).json({ error: 'Failed to get maintenance schedule' });
  }
};

/**
 * Create a new maintenance schedule
 */
export const createMaintenanceSchedule = async (req, res) => {
  try {
    const { farmId } = req.params;
    const { 
      name, 
      description, 
      equipmentId, 
      maintenanceType, 
      intervalValue, 
      intervalUnit, 
      nextDate, 
      notes 
    } = req.body;

    // Validate required fields
    if (!name || !equipmentId || !maintenanceType || !intervalValue || !intervalUnit || !nextDate) {
      return res.status(400).json({ 
        error: 'Name, equipment ID, maintenance type, interval value, interval unit, and next date are required' 
      });
    }

    // Validate farm exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Validate equipment exists
    const equipment = await Equipment.findByPk(equipmentId);
    if (!equipment) {
      return res.status(404).json({ error: 'Equipment not found' });
    }

    // Create the maintenance schedule
    const schedule = await MaintenanceSchedule.create({
      farm_id: farmId,
      name,
      description: description || null,
      equipment_id: equipmentId,
      maintenance_type: maintenanceType,
      interval_value: intervalValue,
      interval_unit: intervalUnit,
      next_date: nextDate,
      status: 'scheduled',
      notes: notes || null
    });

    return res.status(201).json({
      maintenanceSchedule: {
        id: schedule.id,
        name: schedule.name,
        description: schedule.description,
        equipmentId: schedule.equipment_id,
        maintenanceType: schedule.maintenance_type,
        intervalValue: schedule.interval_value,
        intervalUnit: schedule.interval_unit,
        nextDate: schedule.next_date,
        status: schedule.status,
        notes: schedule.notes,
        createdAt: schedule.created_at,
        updatedAt: schedule.updated_at
      }
    });
  } catch (error) {
    console.error('Error creating maintenance schedule:', error);
    return res.status(500).json({ error: 'Failed to create maintenance schedule' });
  }
};

/**
 * Update a maintenance schedule
 */
export const updateMaintenanceSchedule = async (req, res) => {
  try {
    const { scheduleId } = req.params;
    const { 
      name, 
      description, 
      equipmentId, 
      maintenanceType, 
      intervalValue, 
      intervalUnit, 
      nextDate, 
      status,
      notes 
    } = req.body;

    // Find the maintenance schedule
    const schedule = await MaintenanceSchedule.findByPk(scheduleId);
    if (!schedule) {
      return res.status(404).json({ error: 'Maintenance schedule not found' });
    }

    // Validate equipment if provided
    if (equipmentId) {
      const equipment = await Equipment.findByPk(equipmentId);
      if (!equipment) {
        return res.status(404).json({ error: 'Equipment not found' });
      }
    }

    // Update the maintenance schedule
    if (name) schedule.name = name;
    if (description !== undefined) schedule.description = description;
    if (equipmentId) schedule.equipment_id = equipmentId;
    if (maintenanceType) schedule.maintenance_type = maintenanceType;
    if (intervalValue) schedule.interval_value = intervalValue;
    if (intervalUnit) schedule.interval_unit = intervalUnit;
    if (nextDate) schedule.next_date = nextDate;
    if (status) schedule.status = status;
    if (notes !== undefined) schedule.notes = notes;

    await schedule.save();

    return res.status(200).json({
      maintenanceSchedule: {
        id: schedule.id,
        name: schedule.name,
        description: schedule.description,
        equipmentId: schedule.equipment_id,
        maintenanceType: schedule.maintenance_type,
        intervalValue: schedule.interval_value,
        intervalUnit: schedule.interval_unit,
        lastDate: schedule.last_date,
        nextDate: schedule.next_date,
        status: schedule.status,
        notes: schedule.notes,
        createdAt: schedule.created_at,
        updatedAt: schedule.updated_at
      }
    });
  } catch (error) {
    console.error('Error updating maintenance schedule:', error);
    return res.status(500).json({ error: 'Failed to update maintenance schedule' });
  }
};

/**
 * Delete a maintenance schedule
 */
export const deleteMaintenanceSchedule = async (req, res) => {
  try {
    const { scheduleId } = req.params;

    // Find the maintenance schedule
    const schedule = await MaintenanceSchedule.findByPk(scheduleId);
    if (!schedule) {
      return res.status(404).json({ error: 'Maintenance schedule not found' });
    }

    // Delete the maintenance schedule
    await schedule.destroy();

    return res.status(200).json({
      message: 'Maintenance schedule deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting maintenance schedule:', error);
    return res.status(500).json({ error: 'Failed to delete maintenance schedule' });
  }
};

/**
 * Create a maintenance log entry
 */
export const createMaintenanceLog = async (req, res) => {
  try {
    const { scheduleId } = req.params;
    const { date, performedBy, cost, notes, status } = req.body;

    // Validate required fields
    if (!date || !performedBy) {
      return res.status(400).json({ error: 'Date and performed by are required' });
    }

    // Find the maintenance schedule
    const schedule = await MaintenanceSchedule.findByPk(scheduleId);
    if (!schedule) {
      return res.status(404).json({ error: 'Maintenance schedule not found' });
    }

    // Create the maintenance log
    const log = await MaintenanceLog.create({
      schedule_id: scheduleId,
      date,
      performed_by: performedBy,
      cost: cost || 0,
      notes: notes || null,
      status: status || 'completed'
    });

    // Update the maintenance schedule
    schedule.last_date = date;
    
    // Calculate next maintenance date based on interval
    const nextDate = new Date(date);
    if (schedule.interval_unit === 'days') {
      nextDate.setDate(nextDate.getDate() + schedule.interval_value);
    } else if (schedule.interval_unit === 'weeks') {
      nextDate.setDate(nextDate.getDate() + (schedule.interval_value * 7));
    } else if (schedule.interval_unit === 'months') {
      nextDate.setMonth(nextDate.getMonth() + schedule.interval_value);
    } else if (schedule.interval_unit === 'years') {
      nextDate.setFullYear(nextDate.getFullYear() + schedule.interval_value);
    } else if (schedule.interval_unit === 'hours') {
      // For hour-based maintenance, we'll convert to days for simplicity
      const hoursInDays = schedule.interval_value / 24;
      nextDate.setDate(nextDate.getDate() + hoursInDays);
    } else if (schedule.interval_unit === 'miles' || schedule.interval_unit === 'kilometers') {
      // For mileage-based maintenance, we need to estimate based on average usage
      // This is a simplified approach - in a real app, this would be more sophisticated
      const milesPerDay = 50; // Assume 50 miles per day average
      const daysUntilNextMaintenance = schedule.interval_value / milesPerDay;
      nextDate.setDate(nextDate.getDate() + daysUntilNextMaintenance);
    }
    
    schedule.next_date = nextDate;
    schedule.status = 'scheduled';
    
    await schedule.save();

    return res.status(201).json({
      maintenanceLog: {
        id: log.id,
        scheduleId: log.schedule_id,
        date: log.date,
        performedBy: log.performed_by,
        cost: log.cost,
        notes: log.notes,
        status: log.status,
        createdAt: log.created_at,
        updatedAt: log.updated_at
      },
      maintenanceSchedule: {
        id: schedule.id,
        lastDate: schedule.last_date,
        nextDate: schedule.next_date,
        status: schedule.status
      }
    });
  } catch (error) {
    console.error('Error creating maintenance log:', error);
    return res.status(500).json({ error: 'Failed to create maintenance log' });
  }
};

/**
 * Get maintenance logs for a schedule
 */
export const getMaintenanceLogs = async (req, res) => {
  try {
    const { scheduleId } = req.params;
    const { limit = 100, offset = 0 } = req.query;

    // Find the maintenance schedule
    const schedule = await MaintenanceSchedule.findByPk(scheduleId);
    if (!schedule) {
      return res.status(404).json({ error: 'Maintenance schedule not found' });
    }

    // Get maintenance logs
    const logs = await MaintenanceLog.findAndCountAll({
      where: {
        schedule_id: scheduleId
      },
      order: [['date', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

    // Format the logs
    const formattedLogs = logs.rows.map(log => ({
      id: log.id,
      scheduleId: log.schedule_id,
      date: log.date,
      performedBy: log.performed_by,
      cost: log.cost,
      notes: log.notes,
      status: log.status,
      createdAt: log.created_at,
      updatedAt: log.updated_at
    }));

    return res.status(200).json({
      maintenanceLogs: formattedLogs,
      total: logs.count,
      limit: parseInt(limit),
      offset: parseInt(offset)
    });
  } catch (error) {
    console.error('Error getting maintenance logs:', error);
    return res.status(500).json({ error: 'Failed to get maintenance logs' });
  }
};

/**
 * Get maintenance analytics for a farm
 */
export const getMaintenanceAnalytics = async (req, res) => {
  try {
    const { farmId } = req.params;

    // Validate farm exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Get all maintenance schedules
    const schedules = await MaintenanceSchedule.findAll({
      where: {
        farm_id: farmId
      },
      include: [
        { model: Equipment, as: 'equipment' }
      ]
    });

    // Get all maintenance logs
    const logs = await MaintenanceLog.findAll({
      where: {
        schedule_id: {
          [Op.in]: schedules.map(schedule => schedule.id)
        }
      }
    });

    // Calculate analytics
    const today = new Date();
    
    // Count schedules by status
    const scheduleCounts = {
      total: schedules.length,
      scheduled: schedules.filter(s => s.status === 'scheduled').length,
      overdue: schedules.filter(s => s.status === 'scheduled' && new Date(s.next_date) < today).length,
      completed: schedules.filter(s => s.status === 'completed').length,
      upcoming: schedules.filter(s => {
        const nextDate = new Date(s.next_date);
        const thirtyDaysFromNow = new Date();
        thirtyDaysFromNow.setDate(today.getDate() + 30);
        return s.status === 'scheduled' && nextDate >= today && nextDate <= thirtyDaysFromNow;
      }).length
    };
    
    // Calculate total maintenance cost
    const totalCost = logs.reduce((sum, log) => sum + (log.cost || 0), 0);
    
    // Group maintenance by equipment type
    const maintenanceByEquipmentType = {};
    
    schedules.forEach(schedule => {
      if (schedule.equipment) {
        const equipmentType = schedule.equipment.type || 'unknown';
        
        if (!maintenanceByEquipmentType[equipmentType]) {
          maintenanceByEquipmentType[equipmentType] = {
            type: equipmentType,
            count: 0,
            cost: 0
          };
        }
        
        maintenanceByEquipmentType[equipmentType].count += 1;
        
        // Add costs from logs for this schedule
        const scheduleLogs = logs.filter(log => log.schedule_id === schedule.id);
        maintenanceByEquipmentType[equipmentType].cost += scheduleLogs.reduce((sum, log) => sum + (log.cost || 0), 0);
      }
    });
    
    // Convert to array and sort by count
    const equipmentTypeAnalytics = Object.values(maintenanceByEquipmentType).sort((a, b) => b.count - a.count);
    
    // Group maintenance by type
    const maintenanceByType = {};
    
    schedules.forEach(schedule => {
      const maintenanceType = schedule.maintenance_type || 'unknown';
      
      if (!maintenanceByType[maintenanceType]) {
        maintenanceByType[maintenanceType] = {
          type: maintenanceType,
          count: 0,
          cost: 0
        };
      }
      
      maintenanceByType[maintenanceType].count += 1;
      
      // Add costs from logs for this schedule
      const scheduleLogs = logs.filter(log => log.schedule_id === schedule.id);
      maintenanceByType[maintenanceType].cost += scheduleLogs.reduce((sum, log) => sum + (log.cost || 0), 0);
    });
    
    // Convert to array and sort by count
    const maintenanceTypeAnalytics = Object.values(maintenanceByType).sort((a, b) => b.count - a.count);

    return res.status(200).json({
      analytics: {
        scheduleCounts,
        totalCost,
        equipmentTypeAnalytics,
        maintenanceTypeAnalytics
      }
    });
  } catch (error) {
    console.error('Error getting maintenance analytics:', error);
    return res.status(500).json({ error: 'Failed to get maintenance analytics' });
  }
};