import InventoryTransaction from '../models/InventoryTransaction.js';
import InventoryItem from '../models/InventoryItem.js';
import User from '../models/User.js';
import { Op } from 'sequelize';
import { sequelize } from '../config/database.js';

// Get all transactions for an inventory item
export const getInventoryTransactions = async (req, res) => {
  try {
    const { itemId, userId, type, startDate, endDate } = req.query;
    
    const whereClause = {};
    
    if (itemId) {
      whereClause.inventory_item_id = itemId;
    }
    
    if (userId) {
      whereClause.user_id = userId;
    }
    
    if (type) {
      whereClause.transaction_type = type;
    }
    
    if (startDate && endDate) {
      whereClause.transaction_date = {
        [Op.between]: [new Date(startDate), new Date(endDate)]
      };
    } else if (startDate) {
      whereClause.transaction_date = {
        [Op.gte]: new Date(startDate)
      };
    } else if (endDate) {
      whereClause.transaction_date = {
        [Op.lte]: new Date(endDate)
      };
    }

    const transactions = await InventoryTransaction.findAll({
      where: whereClause,
      include: [
        {
          model: InventoryItem,
          attributes: ['id', 'name', 'barcode', 'qr_code']
        },
        {
          model: User,
          attributes: ['id', 'first_name', 'last_name', 'email']
        }
      ],
      order: [['transaction_date', 'DESC']]
    });

    res.status(200).json(transactions);
  } catch (error) {
    console.error('Error fetching inventory transactions:', error);
    res.status(500).json({ message: 'Failed to fetch inventory transactions', error: error.message });
  }
};

// Get a single transaction by ID
export const getInventoryTransaction = async (req, res) => {
  try {
    const { id } = req.params;
    
    const transaction = await InventoryTransaction.findByPk(id, {
      include: [
        {
          model: InventoryItem,
          attributes: ['id', 'name', 'barcode', 'qr_code']
        },
        {
          model: User,
          attributes: ['id', 'first_name', 'last_name', 'email']
        }
      ]
    });
    
    if (!transaction) {
      return res.status(404).json({ message: 'Inventory transaction not found' });
    }
    
    res.status(200).json(transaction);
  } catch (error) {
    console.error('Error fetching inventory transaction:', error);
    res.status(500).json({ message: 'Failed to fetch inventory transaction', error: error.message });
  }
};

// Create a new inventory transaction
export const createInventoryTransaction = async (req, res) => {
  const transaction = await sequelize.transaction();
  
  try {
    const { 
      inventory_item_id, 
      user_id, 
      transaction_type, 
      quantity, 
      unit_price, 
      total_price, 
      reference, 
      notes, 
      transaction_date 
    } = req.body;
    
    if (!inventory_item_id || !user_id || !transaction_type || quantity === undefined) {
      await transaction.rollback();
      return res.status(400).json({ message: 'Inventory item ID, user ID, transaction type, and quantity are required' });
    }
    
    // Find the inventory item
    const item = await InventoryItem.findByPk(inventory_item_id, { transaction });
    
    if (!item) {
      await transaction.rollback();
      return res.status(404).json({ message: 'Inventory item not found' });
    }
    
    // Create the transaction
    const newTransaction = await InventoryTransaction.create({
      inventory_item_id,
      user_id,
      transaction_type,
      quantity,
      unit_price,
      total_price: total_price || (unit_price ? unit_price * quantity : null),
      reference,
      notes,
      transaction_date: transaction_date || new Date()
    }, { transaction });
    
    // Update the inventory item quantity based on the transaction type
    let newQuantity = parseFloat(item.quantity);
    
    switch (transaction_type) {
      case 'purchase':
        newQuantity += parseFloat(quantity);
        break;
      case 'usage':
        newQuantity -= parseFloat(quantity);
        break;
      case 'adjustment':
        newQuantity = parseFloat(quantity); // Direct set for adjustment
        break;
      case 'transfer':
        // For transfers, we'd typically have two transactions (out from one item, in to another)
        newQuantity -= parseFloat(quantity);
        break;
    }
    
    // Ensure quantity doesn't go below zero
    newQuantity = Math.max(0, newQuantity);
    
    // Update the item quantity
    await item.update({
      quantity: newQuantity,
      updated_at: new Date()
    }, { transaction });
    
    await transaction.commit();
    
    // Fetch the complete transaction with associations
    const completeTransaction = await InventoryTransaction.findByPk(newTransaction.id, {
      include: [
        {
          model: InventoryItem,
          attributes: ['id', 'name', 'barcode', 'qr_code', 'quantity']
        },
        {
          model: User,
          attributes: ['id', 'first_name', 'last_name', 'email']
        }
      ]
    });
    
    res.status(201).json(completeTransaction);
  } catch (error) {
    await transaction.rollback();
    console.error('Error creating inventory transaction:', error);
    res.status(500).json({ message: 'Failed to create inventory transaction', error: error.message });
  }
};

// Update an inventory transaction
export const updateInventoryTransaction = async (req, res) => {
  const transaction = await sequelize.transaction();
  
  try {
    const { id } = req.params;
    const { 
      transaction_type, 
      quantity, 
      unit_price, 
      total_price, 
      reference, 
      notes, 
      transaction_date 
    } = req.body;
    
    // Find the transaction
    const inventoryTransaction = await InventoryTransaction.findByPk(id, { transaction });
    
    if (!inventoryTransaction) {
      await transaction.rollback();
      return res.status(404).json({ message: 'Inventory transaction not found' });
    }
    
    // Find the inventory item
    const item = await InventoryItem.findByPk(inventoryTransaction.inventory_item_id, { transaction });
    
    if (!item) {
      await transaction.rollback();
      return res.status(404).json({ message: 'Inventory item not found' });
    }
    
    // Calculate the quantity difference
    const oldQuantity = parseFloat(inventoryTransaction.quantity);
    const newQuantity = quantity !== undefined ? parseFloat(quantity) : oldQuantity;
    const quantityDifference = newQuantity - oldQuantity;
    
    // Update the transaction
    await inventoryTransaction.update({
      transaction_type: transaction_type || inventoryTransaction.transaction_type,
      quantity: newQuantity,
      unit_price: unit_price !== undefined ? unit_price : inventoryTransaction.unit_price,
      total_price: total_price !== undefined ? total_price : (unit_price !== undefined ? unit_price * newQuantity : inventoryTransaction.total_price),
      reference: reference !== undefined ? reference : inventoryTransaction.reference,
      notes: notes !== undefined ? notes : inventoryTransaction.notes,
      transaction_date: transaction_date ? new Date(transaction_date) : inventoryTransaction.transaction_date,
      updated_at: new Date()
    }, { transaction });
    
    // Update the inventory item quantity based on the transaction type and quantity difference
    let updatedItemQuantity = parseFloat(item.quantity);
    
    switch (transaction_type || inventoryTransaction.transaction_type) {
      case 'purchase':
        updatedItemQuantity += quantityDifference;
        break;
      case 'usage':
        updatedItemQuantity -= quantityDifference;
        break;
      case 'adjustment':
        // For adjustments, we need to recalculate based on the new quantity
        if (quantity !== undefined) {
          // Revert the old adjustment
          if (inventoryTransaction.transaction_type === 'adjustment') {
            // No need to do anything, as we're setting a new value
          } else {
            // Handle case where transaction type changed to adjustment
            updatedItemQuantity = parseFloat(quantity);
          }
        }
        break;
      case 'transfer':
        updatedItemQuantity -= quantityDifference;
        break;
    }
    
    // Ensure quantity doesn't go below zero
    updatedItemQuantity = Math.max(0, updatedItemQuantity);
    
    // Update the item quantity
    await item.update({
      quantity: updatedItemQuantity,
      updated_at: new Date()
    }, { transaction });
    
    await transaction.commit();
    
    // Fetch the complete updated transaction with associations
    const completeTransaction = await InventoryTransaction.findByPk(id, {
      include: [
        {
          model: InventoryItem,
          attributes: ['id', 'name', 'barcode', 'qr_code', 'quantity']
        },
        {
          model: User,
          attributes: ['id', 'first_name', 'last_name', 'email']
        }
      ]
    });
    
    res.status(200).json(completeTransaction);
  } catch (error) {
    await transaction.rollback();
    console.error('Error updating inventory transaction:', error);
    res.status(500).json({ message: 'Failed to update inventory transaction', error: error.message });
  }
};

// Delete an inventory transaction
export const deleteInventoryTransaction = async (req, res) => {
  const transaction = await sequelize.transaction();
  
  try {
    const { id } = req.params;
    
    // Find the transaction
    const inventoryTransaction = await InventoryTransaction.findByPk(id, { transaction });
    
    if (!inventoryTransaction) {
      await transaction.rollback();
      return res.status(404).json({ message: 'Inventory transaction not found' });
    }
    
    // Find the inventory item
    const item = await InventoryItem.findByPk(inventoryTransaction.inventory_item_id, { transaction });
    
    if (!item) {
      await transaction.rollback();
      return res.status(404).json({ message: 'Inventory item not found' });
    }
    
    // Update the inventory item quantity based on the transaction type
    let updatedItemQuantity = parseFloat(item.quantity);
    const transactionQuantity = parseFloat(inventoryTransaction.quantity);
    
    switch (inventoryTransaction.transaction_type) {
      case 'purchase':
        updatedItemQuantity -= transactionQuantity;
        break;
      case 'usage':
        updatedItemQuantity += transactionQuantity;
        break;
      case 'adjustment':
        // For adjustments, we'd need to know the previous quantity before the adjustment
        // This is complex and might require fetching previous transactions
        // For simplicity, we'll just leave the quantity as is when deleting an adjustment
        break;
      case 'transfer':
        updatedItemQuantity += transactionQuantity;
        break;
    }
    
    // Ensure quantity doesn't go below zero
    updatedItemQuantity = Math.max(0, updatedItemQuantity);
    
    // Update the item quantity
    await item.update({
      quantity: updatedItemQuantity,
      updated_at: new Date()
    }, { transaction });
    
    // Delete the transaction
    await inventoryTransaction.destroy({ transaction });
    
    await transaction.commit();
    
    res.status(200).json({ message: 'Inventory transaction deleted successfully' });
  } catch (error) {
    await transaction.rollback();
    console.error('Error deleting inventory transaction:', error);
    res.status(500).json({ message: 'Failed to delete inventory transaction', error: error.message });
  }
};