import UserFarm from '../models/UserFarm.js';
import RolePermission from '../models/RolePermission.js';
import User from '../models/User.js';
import Role from '../models/Role.js';

/**
 * Check if a user has permission for a feature
 * @route GET /api/permissions/check
 * @param {string} farmId - The farm ID
 * @param {string} userId - The user ID
 * @param {string} feature - The feature to check
 * @param {string} permission - The permission to check (view, create, edit, delete)
 * @returns {boolean} hasPermission - Whether the user has the permission
 */
export const checkPermission = async (req, res) => {
  try {
    const { farmId, userId, feature, permission } = req.query;

    // Validate required parameters
    if (!farmId || !userId || !feature || !permission) {
      // Set cache control headers to prevent caching
      res.setHeader('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate');
      res.setHeader('Pragma', 'no-cache');
      res.setHeader('Expires', '0');
      res.setHeader('Surrogate-Control', 'no-store');
      return res.status(400).json({ 
        error: 'Missing required parameters',
        hasPermission: false
      });
    }

    // Check if user is a global admin
    const user = await User.findByPk(userId);
    if (user && user.is_global_admin) {
      // Set cache control headers to prevent caching
      res.setHeader('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate');
      res.setHeader('Pragma', 'no-cache');
      res.setHeader('Expires', '0');
      res.setHeader('Surrogate-Control', 'no-store');
      return res.status(200).json({ hasPermission: true });
    }

    // Check if user has access to the farm
    const userFarm = await UserFarm.findOne({
      where: {
        user_id: userId,
        farm_id: farmId
      }
    });

    if (!userFarm) {
      // Set cache control headers to prevent caching
      res.setHeader('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate');
      res.setHeader('Pragma', 'no-cache');
      res.setHeader('Expires', '0');
      res.setHeader('Surrogate-Control', 'no-store');
      return res.status(200).json({ hasPermission: false });
    }

    // Check if user has custom permissions in the UserFarm model
    if (userFarm.permissions && 
        userFarm.permissions[feature] && 
        userFarm.permissions[feature][permission] !== undefined) {
      // Set cache control headers to prevent caching
      res.setHeader('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate');
      res.setHeader('Pragma', 'no-cache');
      res.setHeader('Expires', '0');
      res.setHeader('Surrogate-Control', 'no-store');
      return res.status(200).json({ 
        hasPermission: userFarm.permissions[feature][permission]
      });
    }

    // If not, check the RolePermission model for default permissions for this role
    const rolePermission = await RolePermission.findOne({
      where: {
        farm_id: farmId,
        role_id: userFarm.role_id,
        feature: feature
      }
    });

    if (!rolePermission) {
      // Set cache control headers to prevent caching
      res.setHeader('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate');
      res.setHeader('Pragma', 'no-cache');
      res.setHeader('Expires', '0');
      res.setHeader('Surrogate-Control', 'no-store');
      return res.status(200).json({ hasPermission: false });
    }

    // Return the appropriate permission value
    let hasPermission = false;
    switch (permission) {
      case 'view':
        hasPermission = rolePermission.can_view;
        break;
      case 'create':
        hasPermission = rolePermission.can_create;
        break;
      case 'edit':
        hasPermission = rolePermission.can_edit;
        break;
      case 'delete':
        hasPermission = rolePermission.can_delete;
        break;
      case 'approve':
        hasPermission = rolePermission.can_approve;
        break;
      case 'reject':
        hasPermission = rolePermission.can_reject;
        break;
      case 'assign':
        hasPermission = rolePermission.can_assign;
        break;
      case 'export':
        hasPermission = rolePermission.can_export;
        break;
      case 'import':
        hasPermission = rolePermission.can_import;
        break;
      case 'manage_settings':
        hasPermission = rolePermission.can_manage_settings;
        break;
      case 'generate_reports':
        hasPermission = rolePermission.can_generate_reports;
        break;
      case 'view_sensitive':
        hasPermission = rolePermission.can_view_sensitive;
        break;
      default:
        hasPermission = false;
    }

    // Set cache control headers to prevent caching
    res.setHeader('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate');
    res.setHeader('Pragma', 'no-cache');
    res.setHeader('Expires', '0');
    res.setHeader('Surrogate-Control', 'no-store');
    return res.status(200).json({ hasPermission });
  } catch (error) {
    console.error('Error checking permission:', error);
    // Set cache control headers to prevent caching
    res.setHeader('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate');
    res.setHeader('Pragma', 'no-cache');
    res.setHeader('Expires', '0');
    res.setHeader('Surrogate-Control', 'no-store');
    return res.status(500).json({ 
      error: 'Server error checking permission',
      hasPermission: false
    });
  }
};

/**
 * Get all features that a user has permission to view
 * @route GET /api/permissions/user/:userId/farm/:farmId/viewable
 * @param {string} userId - The user ID
 * @param {string} farmId - The farm ID
 * @returns {Array} features - List of features the user can view
 */
export const getUserViewableFeatures = async (req, res) => {
  try {
    const { userId, farmId } = req.params;

    // Validate required parameters
    if (!userId || !farmId) {
      return res.status(400).json({ 
        error: 'Missing required parameters',
        features: []
      });
    }

    // Check if user is a global admin
    const user = await User.findByPk(userId);
    if (user && user.is_global_admin) {
      // Global admins can view all features
      // Get all available features from the system
      const allPermissions = await RolePermission.findAll({
        attributes: ['feature'],
        group: ['feature']
      });

      const allFeatures = allPermissions.map(permission => permission.feature);
      return res.status(200).json({ features: allFeatures });
    }

    // Check if user has access to the farm
    const userFarm = await UserFarm.findOne({
      where: {
        user_id: userId,
        farm_id: farmId
      }
    });

    if (!userFarm) {
      return res.status(200).json({ features: [] });
    }

    // Get all role permissions for this role
    const rolePermissions = await RolePermission.findAll({
      where: {
        farm_id: farmId,
        role_id: userFarm.role_id
      }
    });

    // Filter for features that the user can view
    const viewableFeatures = rolePermissions
      .filter(permission => permission.can_view)
      .map(permission => permission.feature);

    // Check for custom permissions in the UserFarm model
    if (userFarm.permissions) {
      Object.entries(userFarm.permissions).forEach(([feature, permissions]) => {
        if (permissions.view && !viewableFeatures.includes(feature)) {
          viewableFeatures.push(feature);
        } else if (permissions.view === false && viewableFeatures.includes(feature)) {
          // Remove feature if custom permission explicitly denies view access
          const index = viewableFeatures.indexOf(feature);
          if (index !== -1) {
            viewableFeatures.splice(index, 1);
          }
        }
      });
    }

    return res.status(200).json({ features: viewableFeatures });
  } catch (error) {
    console.error('Error getting viewable features:', error);
    return res.status(500).json({ 
      error: 'Server error getting viewable features',
      features: []
    });
  }
};

/**
 * Get default permissions for a role
 * @route GET /api/permissions/defaults/:roleId
 * @param {string} roleId - The role ID
 * @returns {Array} permissions - List of default permissions for the role
 */
export const getDefaultRolePermissions = async (req, res) => {
  try {
    const { roleId } = req.params;

    // Validate required parameters
    if (!roleId) {
      return res.status(400).json({ 
        error: 'Missing required parameters'
      });
    }

    // Get the role
    const role = await Role.findByPk(roleId);
    if (!role) {
      return res.status(404).json({ 
        error: 'Role not found'
      });
    }

    // Get default permissions for this role (system-wide defaults)
    const defaultPermissions = await RolePermission.findAll({
      where: {
        role_id: roleId,
        farm_id: null // System-wide defaults have null farm_id
      }
    });

    return res.status(200).json(defaultPermissions);
  } catch (error) {
    console.error('Error getting default permissions:', error);
    return res.status(500).json({ 
      error: 'Server error getting default permissions'
    });
  }
};

/**
 * Get permissions for a role in a farm
 * @route GET /api/permissions/farm/:farmId/role/:roleId
 * @param {string} farmId - The farm ID
 * @param {string} roleId - The role ID
 * @returns {Array} permissions - List of permissions for the role in the farm
 */
export const getFarmRolePermissions = async (req, res) => {
  try {
    const { farmId, roleId } = req.params;

    // Validate required parameters
    if (!farmId || !roleId) {
      return res.status(400).json({ 
        error: 'Missing required parameters'
      });
    }

    // Convert "null" string to actual null value for global roles
    const actualFarmId = farmId === "null" ? null : farmId;

    // Get the role
    const role = await Role.findOne({
      where: {
        id: roleId,
        farm_id: actualFarmId
      }
    });

    if (!role) {
      console.log(`Role ${roleId} not found for farm ${farmId}, falling back to default permissions`);

      // Try to find the role by name instead of ID
      const roleName = roleId; // In the API, roleId is actually the role name (farm_owner, farm_employee, etc.)

      // Find or create the role
      const [createdRole, created] = await Role.findOrCreate({
        where: {
          name: roleName,
          farm_id: actualFarmId
        },
        defaults: {
          name: roleName,
          farm_id: actualFarmId,
          description: `${roleName} role for farm ${actualFarmId}`
        }
      });

      if (created) {
        console.log(`Created new role ${roleName} for farm ${farmId}`);
      }

      // Get permissions for this role in this farm
      const farmPermissions = await RolePermission.findAll({
        where: {
          farm_id: actualFarmId,
          role_id: createdRole.id
        }
      });

      // If no permissions exist, return an empty array
      if (farmPermissions.length === 0) {
        console.log(`No permissions found for role ${roleName} in farm ${farmId}, returning empty array`);
        return res.status(200).json([]);
      }

      return res.status(200).json(farmPermissions);
    }

    // Get permissions for this role in this farm
    const farmPermissions = await RolePermission.findAll({
      where: {
        farm_id: actualFarmId,
        role_id: roleId
      }
    });

    // If no permissions exist, return an empty array instead of an error
    if (farmPermissions.length === 0) {
      console.log(`No permissions found for role ${roleId} in farm ${farmId}, returning empty array`);
    }

    return res.status(200).json(farmPermissions);
  } catch (error) {
    console.error('Error getting farm role permissions:', error);
    return res.status(500).json({ 
      error: 'Server error getting farm role permissions'
    });
  }
};

/**
 * Set permissions for a role in a farm
 * @route POST /api/permissions/farm/:farmId/role/:roleId
 * @param {string} farmId - The farm ID
 * @param {string} roleId - The role ID
 * @param {string} feature - The feature to set permissions for
 * @param {boolean} view - Whether the role can view the feature
 * @param {boolean} create - Whether the role can create in the feature
 * @param {boolean} edit - Whether the role can edit in the feature
 * @param {boolean} delete - Whether the role can delete in the feature
 * @param {boolean} approve - Whether the role can approve items in the feature
 * @param {boolean} reject - Whether the role can reject items in the feature
 * @param {boolean} assign - Whether the role can assign items to users in the feature
 * @param {boolean} export - Whether the role can export data from the feature
 * @param {boolean} import - Whether the role can import data into the feature
 * @param {boolean} manage_settings - Whether the role can manage settings for the feature
 * @param {boolean} generate_reports - Whether the role can generate reports for the feature
 * @param {boolean} view_sensitive - Whether the role can view sensitive information in the feature
 * @returns {Object} permission - The updated permission
 */
export const setFarmRolePermissions = async (req, res) => {
  try {
    const { farmId, roleId } = req.params;
    const { 
      feature, 
      view, 
      create, 
      edit, 
      delete: canDelete,
      approve,
      reject,
      assign,
      export: canExport,
      import: canImport,
      manage_settings: canManageSettings,
      generate_reports: canGenerateReports,
      view_sensitive: canViewSensitive
    } = req.body;

    // Validate required parameters
    if (!farmId || !roleId || !feature) {
      return res.status(400).json({ 
        error: 'Missing required parameters'
      });
    }

    // Convert "null" string to actual null value for global roles
    const actualFarmId = farmId === "null" ? null : farmId;

    // Get the role
    const role = await Role.findOne({
      where: {
        id: roleId,
        farm_id: actualFarmId
      }
    });

    if (!role) {
      return res.status(404).json({ 
        error: 'Role not found for this farm'
      });
    }

    // Find or create the permission
    let [permission, created] = await RolePermission.findOrCreate({
      where: {
        farm_id: actualFarmId,
        role_id: roleId,
        feature: feature
      },
      defaults: {
        can_view: view !== undefined ? view : false,
        can_create: create !== undefined ? create : false,
        can_edit: edit !== undefined ? edit : false,
        can_delete: canDelete !== undefined ? canDelete : false,
        can_approve: approve !== undefined ? approve : false,
        can_reject: reject !== undefined ? reject : false,
        can_assign: assign !== undefined ? assign : false,
        can_export: canExport !== undefined ? canExport : false,
        can_import: canImport !== undefined ? canImport : false,
        can_manage_settings: canManageSettings !== undefined ? canManageSettings : false,
        can_generate_reports: canGenerateReports !== undefined ? canGenerateReports : false,
        can_view_sensitive: canViewSensitive !== undefined ? canViewSensitive : false
      }
    });

    // If the permission already existed, update it
    if (!created) {
      permission.can_view = view !== undefined ? view : permission.can_view;
      permission.can_create = create !== undefined ? create : permission.can_create;
      permission.can_edit = edit !== undefined ? edit : permission.can_edit;
      permission.can_delete = canDelete !== undefined ? canDelete : permission.can_delete;
      permission.can_approve = approve !== undefined ? approve : permission.can_approve;
      permission.can_reject = reject !== undefined ? reject : permission.can_reject;
      permission.can_assign = assign !== undefined ? assign : permission.can_assign;
      permission.can_export = canExport !== undefined ? canExport : permission.can_export;
      permission.can_import = canImport !== undefined ? canImport : permission.can_import;
      permission.can_manage_settings = canManageSettings !== undefined ? canManageSettings : permission.can_manage_settings;
      permission.can_generate_reports = canGenerateReports !== undefined ? canGenerateReports : permission.can_generate_reports;
      permission.can_view_sensitive = canViewSensitive !== undefined ? canViewSensitive : permission.can_view_sensitive;
      await permission.save();
    }

    return res.status(200).json(permission);
  } catch (error) {
    console.error('Error setting farm role permissions:', error);
    return res.status(500).json({ 
      error: 'Server error setting farm role permissions'
    });
  }
};
