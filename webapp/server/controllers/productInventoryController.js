import ProductInventory from '../models/ProductInventory.js';
import Product from '../models/Product.js';
import InventoryItem from '../models/InventoryItem.js';
import InventoryTransaction from '../models/InventoryTransaction.js';
import { sequelize } from '../config/database.js';

/**
 * Get all product-inventory relationships for a product
 */
export const getProductInventory = async (req, res) => {
  try {
    const { productId } = req.params;

    const productInventory = await ProductInventory.findAll({
      where: { product_id: productId },
      include: [
        {
          model: InventoryItem,
          attributes: ['id', 'name', 'description', 'quantity', 'unit']
        }
      ]
    });

    res.status(200).json(productInventory);
  } catch (error) {
    console.error('Error fetching product inventory:', error);
    res.status(500).json({ error: 'Failed to fetch product inventory' });
  }
};

/**
 * Get all products linked to an inventory item
 */
export const getInventoryProducts = async (req, res) => {
  try {
    const { inventoryItemId } = req.params;

    const productInventory = await ProductInventory.findAll({
      where: { inventory_item_id: inventoryItemId },
      include: [
        {
          model: Product,
          attributes: ['id', 'name', 'description', 'price', 'unit']
        }
      ]
    });

    res.status(200).json(productInventory);
  } catch (error) {
    console.error('Error fetching inventory products:', error);
    res.status(500).json({ error: 'Failed to fetch inventory products' });
  }
};

/**
 * Link a product to an inventory item
 */
export const linkProductToInventory = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { product_id, inventory_item_id, quantity_per_unit } = req.body;

    // Check if product exists
    const product = await Product.findByPk(product_id);
    if (!product) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Product not found' });
    }

    // Check if inventory item exists
    const inventoryItem = await InventoryItem.findByPk(inventory_item_id);
    if (!inventoryItem) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Inventory item not found' });
    }

    // Check if the link already exists
    const existingLink = await ProductInventory.findOne({
      where: {
        product_id,
        inventory_item_id
      }
    });

    if (existingLink) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Product is already linked to this inventory item' });
    }

    // Create the link
    const productInventory = await ProductInventory.create({
      product_id,
      inventory_item_id,
      quantity_per_unit: quantity_per_unit || 1.0,
      is_active: true
    }, { transaction });

    await transaction.commit();

    // Return the created link with related data
    const result = await ProductInventory.findByPk(productInventory.id, {
      include: [
        {
          model: Product,
          attributes: ['id', 'name', 'description', 'price', 'unit']
        },
        {
          model: InventoryItem,
          attributes: ['id', 'name', 'description', 'quantity', 'unit']
        }
      ]
    });

    res.status(201).json(result);
  } catch (error) {
    await transaction.rollback();
    console.error('Error linking product to inventory:', error);
    res.status(500).json({ error: 'Failed to link product to inventory' });
  }
};

/**
 * Update a product-inventory link
 */
export const updateProductInventoryLink = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { id } = req.params;
    const { quantity_per_unit, is_active } = req.body;

    // Check if the link exists
    const productInventory = await ProductInventory.findByPk(id);
    if (!productInventory) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Product-inventory link not found' });
    }

    // Update the link
    await productInventory.update({
      quantity_per_unit: quantity_per_unit !== undefined ? quantity_per_unit : productInventory.quantity_per_unit,
      is_active: is_active !== undefined ? is_active : productInventory.is_active
    }, { transaction });

    await transaction.commit();

    // Return the updated link with related data
    const result = await ProductInventory.findByPk(id, {
      include: [
        {
          model: Product,
          attributes: ['id', 'name', 'description', 'price', 'unit']
        },
        {
          model: InventoryItem,
          attributes: ['id', 'name', 'description', 'quantity', 'unit']
        }
      ]
    });

    res.status(200).json(result);
  } catch (error) {
    await transaction.rollback();
    console.error('Error updating product-inventory link:', error);
    res.status(500).json({ error: 'Failed to update product-inventory link' });
  }
};

/**
 * Delete a product-inventory link
 */
export const deleteProductInventoryLink = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { id } = req.params;

    // Check if the link exists
    const productInventory = await ProductInventory.findByPk(id);
    if (!productInventory) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Product-inventory link not found' });
    }

    // Delete the link
    await productInventory.destroy({ transaction });

    await transaction.commit();

    res.status(200).json({ message: 'Product-inventory link deleted successfully' });
  } catch (error) {
    await transaction.rollback();
    console.error('Error deleting product-inventory link:', error);
    res.status(500).json({ error: 'Failed to delete product-inventory link' });
  }
};

/**
 * Update inventory when a product is sold
 * This function is meant to be called from the invoice controller
 * @returns {Object} result - Result of the operation
 * @returns {boolean} result.success - Whether the operation was successful
 * @returns {string} result.message - Message describing the result
 * @returns {Array} result.insufficientItems - Array of inventory items with insufficient quantity (if any)
 */
export const updateInventoryOnProductSale = async (invoiceItem, userId, transaction) => {
  try {
    if (!invoiceItem.product_id) {
      console.log('No product associated with this invoice item, skipping inventory update');
      return { success: true, message: 'No product associated with this invoice item' };
    }

    // Find all inventory items linked to this product
    const productInventoryLinks = await ProductInventory.findAll({
      where: {
        product_id: invoiceItem.product_id,
        is_active: true
      },
      transaction
    });

    if (productInventoryLinks.length === 0) {
      console.log('No inventory items linked to this product, skipping inventory update');
      return { success: true, message: 'No inventory items linked to this product' };
    }

    // First, validate that there's enough inventory for all linked items
    const insufficientItems = [];
    for (const link of productInventoryLinks) {
      // Calculate how much inventory to deduct
      const deductQuantity = parseFloat(invoiceItem.quantity) * parseFloat(link.quantity_per_unit);

      // Get the inventory item
      const inventoryItem = await InventoryItem.findByPk(link.inventory_item_id, { transaction });

      if (!inventoryItem) {
        console.error(`Inventory item ${link.inventory_item_id} not found`);
        continue;
      }

      // Check if there's enough inventory
      if (parseFloat(inventoryItem.quantity) < deductQuantity) {
        insufficientItems.push({
          id: inventoryItem.id,
          name: inventoryItem.name,
          available: parseFloat(inventoryItem.quantity),
          required: deductQuantity,
          unit: inventoryItem.unit
        });
      }
    }

    // If there are insufficient items, return an error
    if (insufficientItems.length > 0) {
      console.warn('Insufficient inventory for product sale', { insufficientItems });
      return {
        success: false,
        message: 'Insufficient inventory for product sale',
        insufficientItems
      };
    }

    // If validation passes, update each linked inventory item
    for (const link of productInventoryLinks) {
      // Calculate how much inventory to deduct
      const deductQuantity = parseFloat(invoiceItem.quantity) * parseFloat(link.quantity_per_unit);

      // Get the inventory item
      const inventoryItem = await InventoryItem.findByPk(link.inventory_item_id, { transaction });

      if (!inventoryItem) {
        console.error(`Inventory item ${link.inventory_item_id} not found`);
        continue;
      }

      // Update inventory quantity
      const newQuantity = parseFloat(inventoryItem.quantity) - deductQuantity;
      await inventoryItem.update({ quantity: newQuantity }, { transaction });

      // Create inventory transaction record
      await InventoryTransaction.create({
        inventory_item_id: link.inventory_item_id,
        user_id: userId,
        transaction_type: 'usage',
        quantity: -deductQuantity,
        unit_price: invoiceItem.unit_price,
        total_price: parseFloat(invoiceItem.unit_price) * deductQuantity,
        reference: `Invoice Item: ${invoiceItem.id}`,
        notes: `Deducted from inventory due to product sale`,
        transaction_date: new Date()
      }, { transaction });

      console.log(`Updated inventory for item ${inventoryItem.name}, new quantity: ${newQuantity}`);
    }

    return { success: true, message: 'Inventory updated successfully' };
  } catch (error) {
    console.error('Error updating inventory on product sale:', error);
    throw error;
  }
};
