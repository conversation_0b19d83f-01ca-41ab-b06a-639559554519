import Customer from '../models/Customer.js';
import CustomerAddress from '../models/CustomerAddress.js';
import Farm from '../models/Farm.js';
import PurchaseRequest from '../models/PurchaseRequest.js';
import { sequelize } from '../config/database.js';
import jwt from 'jsonwebtoken';
import bcrypt from 'bcryptjs';
import crypto from 'crypto';
import nodemailer from 'nodemailer';
import dotenv from 'dotenv';
import { setRefreshTokenCookie } from '../utils/cookieUtils.js';

dotenv.config();

// JWT configuration
const JWT_SECRET = process.env.JWT_SECRET || 'your_jwt_secret_key';
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '15m';
const JWT_REFRESH_SECRET = process.env.JWT_REFRESH_SECRET || 'your_refresh_token_secret';
const JWT_REFRESH_EXPIRES_IN = process.env.JWT_REFRESH_EXPIRES_IN || '7d';

// Email configuration
const transporter = nodemailer.createTransport({
  host: process.env.EMAIL_HOST,
  port: process.env.EMAIL_PORT,
  secure: process.env.EMAIL_PORT === '465',
  auth: {
    user: process.env.EMAIL_USER,
    pass: process.env.EMAIL_PASSWORD,
  },
});

// Helper function to generate tokens
const generateTokens = async (customerId, farmId) => {
  // Generate access token with shorter expiration
  const token = jwt.sign({ id: customerId, type: 'marketplace_customer', farmId }, JWT_SECRET, { expiresIn: JWT_EXPIRES_IN });

  // Generate refresh token
  const refreshToken = jwt.sign({ id: customerId, type: 'marketplace_customer', farmId }, JWT_REFRESH_SECRET, { expiresIn: JWT_REFRESH_EXPIRES_IN });

  return { token, refreshToken };
};

// Register a new marketplace customer
export const registerCustomer = async (req, res) => {
  let transaction;

  try {
    transaction = await sequelize.transaction();

    const { name, email, password, phone, farmId } = req.body;

    // Validate required fields
    if (!name || !email || !password) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Name, email, and password are required' });
    }

    // If farmId is provided, check if customer already exists with this email for this farm
    // Otherwise, check if customer exists with this email for any farm
    let existingCustomer;
    if (farmId) {
      existingCustomer = await Customer.findOne({
        where: { email, farm_id: farmId },
        transaction
      });
    } else {
      existingCustomer = await Customer.findOne({
        where: { email, origin: 'marketplace' },
        transaction
      });
    }

    if (existingCustomer) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Customer with this email already exists' });
    }

    // If farmId is provided, check if farm exists
    let farm = null;
    if (farmId) {
      farm = await Farm.findByPk(farmId, { transaction });
      if (!farm) {
        await transaction.rollback();
        return res.status(404).json({ error: 'Farm not found' });
      }
    }

    // Generate email verification token
    const emailVerificationToken = crypto.randomBytes(32).toString('hex');
    const emailVerificationExpires = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours

    // Create new customer
    const customer = await Customer.create({
      name,
      email,
      phone,
      password_hash: password, // Will be hashed by model hooks
      farm_id: farmId || null, // Allow null farm_id for global store registrations
      origin: 'marketplace',
      email_verification_token: emailVerificationToken,
      email_verification_expires: emailVerificationExpires,
      portal_access: true,
      email_verified: false
    }, { transaction });

    // Send verification email
    const verificationUrl = `${process.env.FRONTEND_URL}/marketplace/verify-email/${emailVerificationToken}`;

    await transporter.sendMail({
      from: process.env.EMAIL_FROM || '<EMAIL>',
      to: email,
      subject: 'Verify Your Email Address',
      html: `
        <h1>Email Verification</h1>
        <p>Hello ${name},</p>
        <p>Thank you for registering with NxtAcre Marketplace. Please verify your email address by clicking the link below:</p>
        <a href="${verificationUrl}">Verify Email</a>
        <p>This link will expire in 24 hours.</p>
        <p>If you did not request this, please ignore this email.</p>
      `
    });

    await transaction.commit();

    return res.status(201).json({
      message: 'Customer registered successfully. Please check your email to verify your account.',
      customerId: customer.id
    });
  } catch (error) {
    if (transaction) await transaction.rollback();
    console.error('Marketplace customer registration error:', error);
    return res.status(500).json({ error: 'Registration failed. Please try again later.' });
  }
};

// Login as a marketplace customer
export const loginCustomer = async (req, res) => {
  try {
    const { email, password, farmId } = req.body;

    // Find customer by email and farm ID if provided, otherwise just by email
    let whereClause = { 
      email,
      portal_access: true,
      origin: 'marketplace'
    };

    // Only add farm_id to the where clause if it's provided
    if (farmId) {
      whereClause.farm_id = farmId;
    }

    const customer = await Customer.findOne({
      where: whereClause
    });

    if (!customer) {
      return res.status(401).json({ error: 'Invalid credentials or no marketplace access' });
    }

    // Validate password
    const isPasswordValid = await customer.validatePassword(password);
    if (!isPasswordValid) {
      return res.status(401).json({ error: 'Invalid credentials' });
    }

    // Check if email is verified
    if (!customer.email_verified) {
      return res.status(403).json({ error: 'Email not verified. Please verify your email before logging in.' });
    }

    // Get farm information if customer has a farm_id
    let farm = null;
    if (customer.farm_id) {
      farm = await Farm.findByPk(customer.farm_id);
      if (!farm) {
        return res.status(404).json({ error: 'Farm not found' });
      }
    }

    // Generate tokens
    const { token, refreshToken } = await generateTokens(customer.id, customer.farm_id || null);

    // Set refresh token as HTTP-only cookie
    setRefreshTokenCookie(res, refreshToken, {
      maxAge: 7 * 24 * 60 * 60 * 1000 // 7 days
    });

    // Return customer information and token
    return res.status(200).json({
      token,
      customer: {
        id: customer.id,
        name: customer.name,
        email: customer.email,
        farm: farm ? {
          id: farm.id,
          name: farm.name
        } : null
      }
    });
  } catch (error) {
    console.error('Marketplace customer login error:', error);
    return res.status(500).json({ error: 'An error occurred during login. Please try again later.' });
  }
};

// Get customer profile
export const getCustomerProfile = async (req, res) => {
  try {
    const customerId = req.user.id;

    const customer = await Customer.findByPk(customerId, {
      attributes: ['id', 'name', 'email', 'phone', 'farm_id', 'origin', 'global_customer_id'],
      include: [
        {
          model: CustomerAddress,
          as: 'addresses',
          attributes: ['id', 'farm_id', 'farm_alias', 'address', 'city', 'state', 'zip_code', 'country', 
                      'delivery_instructions', 'access_code', 'contact_name', 'contact_phone', 'is_default']
        }
      ]
    });

    if (!customer) {
      return res.status(404).json({ error: 'Customer not found' });
    }

    return res.status(200).json({ customer });
  } catch (error) {
    console.error('Error getting customer profile:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Update customer profile
export const updateCustomerProfile = async (req, res) => {
  try {
    const customerId = req.user.id;
    const { name, phone } = req.body;

    const customer = await Customer.findByPk(customerId);
    if (!customer) {
      return res.status(404).json({ error: 'Customer not found' });
    }

    // Update customer
    await customer.update({
      name: name || customer.name,
      phone: phone !== undefined ? phone : customer.phone
    });

    return res.status(200).json({ 
      message: 'Profile updated successfully',
      customer: {
        id: customer.id,
        name: customer.name,
        email: customer.email,
        phone: customer.phone
      }
    });
  } catch (error) {
    console.error('Error updating customer profile:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Add a new customer address
export const addCustomerAddress = async (req, res) => {
  try {
    const customerId = req.user.id;
    const { 
      farmId, 
      farmAlias, 
      address, 
      city, 
      state, 
      zipCode, 
      country, 
      deliveryInstructions, 
      accessCode, 
      contactName, 
      contactPhone,
      isDefault
    } = req.body;

    // Validate required fields
    if (!farmId || !address || !city || !state || !zipCode) {
      return res.status(400).json({ error: 'Farm ID, address, city, state, and zip code are required' });
    }

    // Check if customer exists
    const customer = await Customer.findByPk(customerId);
    if (!customer) {
      return res.status(404).json({ error: 'Customer not found' });
    }

    // Check if farm exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // If this is the default address, unset any existing default
    if (isDefault) {
      await CustomerAddress.update(
        { is_default: false },
        { where: { customer_id: customerId } }
      );
    }

    // Create new address
    const customerAddress = await CustomerAddress.create({
      customer_id: customerId,
      farm_id: farmId,
      farm_alias: farmAlias,
      address,
      city,
      state,
      zip_code: zipCode,
      country: country || 'USA',
      delivery_instructions: deliveryInstructions,
      access_code: accessCode,
      contact_name: contactName,
      contact_phone: contactPhone,
      is_default: isDefault || false
    });

    return res.status(201).json({ 
      message: 'Address added successfully',
      address: customerAddress
    });
  } catch (error) {
    console.error('Error adding customer address:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Update a customer address
export const updateCustomerAddress = async (req, res) => {
  try {
    const customerId = req.user.id;
    const { addressId } = req.params;
    const { 
      farmAlias, 
      address, 
      city, 
      state, 
      zipCode, 
      country, 
      deliveryInstructions, 
      accessCode, 
      contactName, 
      contactPhone,
      isDefault
    } = req.body;

    // Find address to ensure it exists and belongs to the customer
    const customerAddress = await CustomerAddress.findOne({
      where: { 
        id: addressId,
        customer_id: customerId
      }
    });

    if (!customerAddress) {
      return res.status(404).json({ error: 'Address not found or does not belong to the customer' });
    }

    // If this is being set as the default address, unset any existing default
    if (isDefault && !customerAddress.is_default) {
      await CustomerAddress.update(
        { is_default: false },
        { where: { customer_id: customerId } }
      );
    }

    // Update address
    await customerAddress.update({
      farm_alias: farmAlias !== undefined ? farmAlias : customerAddress.farm_alias,
      address: address || customerAddress.address,
      city: city || customerAddress.city,
      state: state || customerAddress.state,
      zip_code: zipCode || customerAddress.zip_code,
      country: country || customerAddress.country,
      delivery_instructions: deliveryInstructions !== undefined ? deliveryInstructions : customerAddress.delivery_instructions,
      access_code: accessCode !== undefined ? accessCode : customerAddress.access_code,
      contact_name: contactName !== undefined ? contactName : customerAddress.contact_name,
      contact_phone: contactPhone !== undefined ? contactPhone : customerAddress.contact_phone,
      is_default: isDefault !== undefined ? isDefault : customerAddress.is_default
    });

    return res.status(200).json({ 
      message: 'Address updated successfully',
      address: customerAddress
    });
  } catch (error) {
    console.error('Error updating customer address:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Delete a customer address
export const deleteCustomerAddress = async (req, res) => {
  try {
    const customerId = req.user.id;
    const { addressId } = req.params;

    // Find address to ensure it exists and belongs to the customer
    const customerAddress = await CustomerAddress.findOne({
      where: { 
        id: addressId,
        customer_id: customerId
      }
    });

    if (!customerAddress) {
      return res.status(404).json({ error: 'Address not found or does not belong to the customer' });
    }

    // Delete address
    await customerAddress.destroy();

    // If this was the default address, set another address as default if available
    if (customerAddress.is_default) {
      const anotherAddress = await CustomerAddress.findOne({
        where: { customer_id: customerId }
      });

      if (anotherAddress) {
        await anotherAddress.update({ is_default: true });
      }
    }

    return res.status(200).json({ 
      message: 'Address deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting customer address:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get customer order history
export const getCustomerOrders = async (req, res) => {
  try {
    const customerId = req.user.id;

    const orders = await PurchaseRequest.findAll({
      where: { customer_id: customerId },
      include: [
        {
          model: Farm,
          as: 'farm',
          attributes: ['id', 'name']
        }
      ],
      order: [['created_at', 'DESC']]
    });

    return res.status(200).json({ orders });
  } catch (error) {
    console.error('Error getting customer orders:', error);
    return res.status(500).json({ error: error.message });
  }
};
