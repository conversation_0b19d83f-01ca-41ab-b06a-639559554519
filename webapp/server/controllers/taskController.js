import Task from '../models/Task.js';
import { Op } from 'sequelize';
import User from '../models/User.js';
import Farm from '../models/Farm.js';

// Get all tasks with optional filtering and pagination
export const getTasks = async (req, res) => {
  try {
    const { 
      farmId: queryFarmId, 
      status, 
      priority, 
      assignedTo, 
      taskType,
      search,
      page = 1, 
      limit = 10,
      sortBy = 'due_date',
      sortOrder = 'ASC'
    } = req.query;

    // Get farmId from URL params or query params
    const farmId = req.params.farmId || queryFarmId;

    // Build where clause for filtering
    const whereClause = {};

    // Farm ID is required
    if (!farmId) {
      return res.status(400).json({ 
        success: false, 
        message: 'Farm ID is required' 
      });
    }

    whereClause.farm_id = farmId;

    // Add optional filters
    if (status) whereClause.status = status;
    if (priority) whereClause.priority = priority;
    if (assignedTo) whereClause.assigned_to = assignedTo;
    if (taskType) whereClause.task_type = taskType;

    // Add search functionality
    if (search) {
      whereClause[Op.or] = [
        { title: { [Op.iLike]: `%${search}%` } },
        { description: { [Op.iLike]: `%${search}%` } }
      ];
    }

    // Calculate pagination
    const offset = (page - 1) * limit;

    // Determine sort order
    const order = [[sortBy, sortOrder]];

    // Get tasks with pagination
    const tasks = await Task.findAll({
      where: whereClause,
      include: [
        {
          model: User,
          as: 'assignee',
          attributes: ['id', 'first_name', 'last_name', 'email']
        },
        {
          model: User,
          as: 'assigner',
          attributes: ['id', 'first_name', 'last_name', 'email']
        },
        {
          model: User,
          as: 'completer',
          attributes: ['id', 'first_name', 'last_name', 'email']
        },
        {
          model: Farm,
          attributes: ['id', 'name']
        }
      ],
      order,
      limit: parseInt(limit),
      offset
    });

    // Get total count for pagination
    const totalCount = await Task.count({ where: whereClause });

    res.status(200).json({
      success: true,
      tasks: tasks.map(task => ({
        id: task.id,
        title: task.title,
        description: task.description,
        taskType: task.task_type,
        priority: task.priority,
        status: task.status,
        dueDate: task.due_date,
        assignedTo: task.assignee ? {
          id: task.assignee.id,
          firstName: task.assignee.first_name,
          lastName: task.assignee.last_name,
          email: task.assignee.email
        } : null,
        assignedBy: task.assigner ? {
          id: task.assigner.id,
          firstName: task.assigner.first_name,
          lastName: task.assigner.last_name,
          email: task.assigner.email
        } : null,
        completedDate: task.completed_date,
        completedBy: task.completer ? {
          id: task.completer.id,
          firstName: task.completer.first_name,
          lastName: task.completer.last_name,
          email: task.completer.email
        } : null,
        notes: task.notes,
        farm: {
          id: task.Farm.id,
          name: task.Farm.name
        },
        createdAt: task.created_at,
        updatedAt: task.updated_at
      })),
      pagination: {
        total: totalCount,
        page: parseInt(page),
        limit: parseInt(limit),
        totalPages: Math.ceil(totalCount / limit)
      }
    });
  } catch (error) {
    console.error('Error getting tasks:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Failed to get tasks', 
      error: error.message 
    });
  }
};

// Get a single task by ID
export const getTaskById = async (req, res) => {
  try {
    const { taskId } = req.params;

    const task = await Task.findByPk(taskId, {
      include: [
        {
          model: User,
          as: 'assignee',
          attributes: ['id', 'first_name', 'last_name', 'email']
        },
        {
          model: User,
          as: 'assigner',
          attributes: ['id', 'first_name', 'last_name', 'email']
        },
        {
          model: User,
          as: 'completer',
          attributes: ['id', 'first_name', 'last_name', 'email']
        },
        {
          model: Farm,
          attributes: ['id', 'name']
        }
      ]
    });

    if (!task) {
      return res.status(404).json({ 
        success: false, 
        message: 'Task not found' 
      });
    }

    res.status(200).json({
      success: true,
      task: {
        id: task.id,
        title: task.title,
        description: task.description,
        taskType: task.task_type,
        priority: task.priority,
        status: task.status,
        dueDate: task.due_date,
        assignedTo: task.assignee ? {
          id: task.assignee.id,
          firstName: task.assignee.first_name,
          lastName: task.assignee.last_name,
          email: task.assignee.email
        } : null,
        assignedBy: task.assigner ? {
          id: task.assigner.id,
          firstName: task.assigner.first_name,
          lastName: task.assigner.last_name,
          email: task.assigner.email
        } : null,
        completedDate: task.completed_date,
        completedBy: task.completer ? {
          id: task.completer.id,
          firstName: task.completer.first_name,
          lastName: task.completer.last_name,
          email: task.completer.email
        } : null,
        notes: task.notes,
        farm: {
          id: task.Farm.id,
          name: task.Farm.name
        },
        createdAt: task.created_at,
        updatedAt: task.updated_at
      }
    });
  } catch (error) {
    console.error('Error getting task by ID:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Failed to get task', 
      error: error.message 
    });
  }
};

// Create a new task
export const createTask = async (req, res) => {
  try {
    const { 
      farmId, 
      title, 
      description, 
      taskType, 
      priority, 
      status, 
      dueDate, 
      assignedTo, 
      assignedBy, 
      notes 
    } = req.body;

    // Validate required fields
    if (!farmId || !title) {
      return res.status(400).json({ 
        success: false, 
        message: 'Farm ID and title are required' 
      });
    }

    // Create the task
    const task = await Task.create({
      farm_id: farmId,
      title,
      description,
      task_type: taskType,
      priority: priority || 'medium',
      status: status || 'pending',
      due_date: dueDate,
      assigned_to: assignedTo,
      assigned_by: assignedBy || req.user.id, // Default to current user
      notes
    });

    // Fetch the created task with associations
    const createdTask = await Task.findByPk(task.id, {
      include: [
        {
          model: User,
          as: 'assignee',
          attributes: ['id', 'first_name', 'last_name', 'email']
        },
        {
          model: User,
          as: 'assigner',
          attributes: ['id', 'first_name', 'last_name', 'email']
        },
        {
          model: Farm,
          attributes: ['id', 'name']
        }
      ]
    });

    res.status(201).json({
      success: true,
      message: 'Task created successfully',
      task: {
        id: createdTask.id,
        title: createdTask.title,
        description: createdTask.description,
        taskType: createdTask.task_type,
        priority: createdTask.priority,
        status: createdTask.status,
        dueDate: createdTask.due_date,
        assignedTo: createdTask.assignee ? {
          id: createdTask.assignee.id,
          firstName: createdTask.assignee.first_name,
          lastName: createdTask.assignee.last_name,
          email: createdTask.assignee.email
        } : null,
        assignedBy: createdTask.assigner ? {
          id: createdTask.assigner.id,
          firstName: createdTask.assigner.first_name,
          lastName: createdTask.assigner.last_name,
          email: createdTask.assigner.email
        } : null,
        notes: createdTask.notes,
        farm: {
          id: createdTask.Farm.id,
          name: createdTask.Farm.name
        },
        createdAt: createdTask.created_at,
        updatedAt: createdTask.updated_at
      }
    });
  } catch (error) {
    console.error('Error creating task:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Failed to create task', 
      error: error.message 
    });
  }
};

// Update an existing task
export const updateTask = async (req, res) => {
  try {
    const { taskId } = req.params;
    const { 
      title, 
      description, 
      taskType, 
      priority, 
      status, 
      dueDate, 
      assignedTo, 
      notes 
    } = req.body;

    // Find the task
    const task = await Task.findByPk(taskId);

    if (!task) {
      return res.status(404).json({ 
        success: false, 
        message: 'Task not found' 
      });
    }

    // Update the task
    await task.update({
      title: title !== undefined ? title : task.title,
      description: description !== undefined ? description : task.description,
      task_type: taskType !== undefined ? taskType : task.task_type,
      priority: priority !== undefined ? priority : task.priority,
      status: status !== undefined ? status : task.status,
      due_date: dueDate !== undefined ? dueDate : task.due_date,
      assigned_to: assignedTo !== undefined ? assignedTo : task.assigned_to,
      notes: notes !== undefined ? notes : task.notes
    });

    // If status is changed to 'completed', update completed_date and completed_by
    if (status === 'completed' && task.status !== 'completed') {
      await task.update({
        completed_date: new Date(),
        completed_by: req.user.id
      });
    }

    // Fetch the updated task with associations
    const updatedTask = await Task.findByPk(taskId, {
      include: [
        {
          model: User,
          as: 'assignee',
          attributes: ['id', 'first_name', 'last_name', 'email']
        },
        {
          model: User,
          as: 'assigner',
          attributes: ['id', 'first_name', 'last_name', 'email']
        },
        {
          model: User,
          as: 'completer',
          attributes: ['id', 'first_name', 'last_name', 'email']
        },
        {
          model: Farm,
          attributes: ['id', 'name']
        }
      ]
    });

    res.status(200).json({
      success: true,
      message: 'Task updated successfully',
      task: {
        id: updatedTask.id,
        title: updatedTask.title,
        description: updatedTask.description,
        taskType: updatedTask.task_type,
        priority: updatedTask.priority,
        status: updatedTask.status,
        dueDate: updatedTask.due_date,
        assignedTo: updatedTask.assignee ? {
          id: updatedTask.assignee.id,
          firstName: updatedTask.assignee.first_name,
          lastName: updatedTask.assignee.last_name,
          email: updatedTask.assignee.email
        } : null,
        assignedBy: updatedTask.assigner ? {
          id: updatedTask.assigner.id,
          firstName: updatedTask.assigner.first_name,
          lastName: updatedTask.assigner.last_name,
          email: updatedTask.assigner.email
        } : null,
        completedDate: updatedTask.completed_date,
        completedBy: updatedTask.completer ? {
          id: updatedTask.completer.id,
          firstName: updatedTask.completer.first_name,
          lastName: updatedTask.completer.last_name,
          email: updatedTask.completer.email
        } : null,
        notes: updatedTask.notes,
        farm: {
          id: updatedTask.Farm.id,
          name: updatedTask.Farm.name
        },
        createdAt: updatedTask.created_at,
        updatedAt: updatedTask.updated_at
      }
    });
  } catch (error) {
    console.error('Error updating task:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Failed to update task', 
      error: error.message 
    });
  }
};

// Delete a task
export const deleteTask = async (req, res) => {
  try {
    const { taskId } = req.params;

    // Find the task
    const task = await Task.findByPk(taskId);

    if (!task) {
      return res.status(404).json({ 
        success: false, 
        message: 'Task not found' 
      });
    }

    // Delete the task
    await task.destroy();

    res.status(200).json({
      success: true,
      message: 'Task deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting task:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Failed to delete task', 
      error: error.message 
    });
  }
};

// Complete a task
export const completeTask = async (req, res) => {
  try {
    const { taskId } = req.params;
    const { notes } = req.body;

    // Find the task
    const task = await Task.findByPk(taskId);

    if (!task) {
      return res.status(404).json({ 
        success: false, 
        message: 'Task not found' 
      });
    }

    // Update the task to completed
    await task.update({
      status: 'completed',
      completed_date: new Date(),
      completed_by: req.user.id,
      notes: notes ? (task.notes ? `${task.notes}\n\nCompletion notes: ${notes}` : `Completion notes: ${notes}`) : task.notes
    });

    // Fetch the updated task with associations
    const updatedTask = await Task.findByPk(taskId, {
      include: [
        {
          model: User,
          as: 'assignee',
          attributes: ['id', 'first_name', 'last_name', 'email']
        },
        {
          model: User,
          as: 'assigner',
          attributes: ['id', 'first_name', 'last_name', 'email']
        },
        {
          model: User,
          as: 'completer',
          attributes: ['id', 'first_name', 'last_name', 'email']
        },
        {
          model: Farm,
          attributes: ['id', 'name']
        }
      ]
    });

    res.status(200).json({
      success: true,
      message: 'Task completed successfully',
      task: {
        id: updatedTask.id,
        title: updatedTask.title,
        description: updatedTask.description,
        taskType: updatedTask.task_type,
        priority: updatedTask.priority,
        status: updatedTask.status,
        dueDate: updatedTask.due_date,
        assignedTo: updatedTask.assignee ? {
          id: updatedTask.assignee.id,
          firstName: updatedTask.assignee.first_name,
          lastName: updatedTask.assignee.last_name,
          email: updatedTask.assignee.email
        } : null,
        assignedBy: updatedTask.assigner ? {
          id: updatedTask.assigner.id,
          firstName: updatedTask.assigner.first_name,
          lastName: updatedTask.assigner.last_name,
          email: updatedTask.assigner.email
        } : null,
        completedDate: updatedTask.completed_date,
        completedBy: updatedTask.completer ? {
          id: updatedTask.completer.id,
          firstName: updatedTask.completer.first_name,
          lastName: updatedTask.completer.last_name,
          email: updatedTask.completer.email
        } : null,
        notes: updatedTask.notes,
        farm: {
          id: updatedTask.Farm.id,
          name: updatedTask.Farm.name
        },
        createdAt: updatedTask.created_at,
        updatedAt: updatedTask.updated_at
      }
    });
  } catch (error) {
    console.error('Error completing task:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Failed to complete task', 
      error: error.message 
    });
  }
};
