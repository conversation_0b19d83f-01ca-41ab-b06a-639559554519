import FarmFeatureToggles from '../models/FarmFeatureToggles.js';
import Farm from '../models/Farm.js';

/**
 * Get feature toggles for a farm
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const getFarmFeatureToggles = async (req, res) => {
  try {
    const { farmId } = req.params;

    // Check if farm exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Get or create feature toggles for the farm
    let featureToggles = await FarmFeatureToggles.findOne({
      where: { farm_id: farmId }
    });

    if (!featureToggles) {
      // Create default feature toggles if they don't exist
      featureToggles = await FarmFeatureToggles.create({
        farm_id: farmId,
        marketplace_enabled: false,
        customer_orders_enabled: false,
        delivery_scheduling_enabled: false,
        driver_tracking_enabled: false,
        customer_messaging_enabled: false
      });
    }

    return res.status(200).json(featureToggles);
  } catch (error) {
    console.error('Error getting farm feature toggles:', error);
    return res.status(500).json({ error: 'Failed to get farm feature toggles' });
  }
};

/**
 * Update feature toggles for a farm
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const updateFarmFeatureToggles = async (req, res) => {
  try {
    const { farmId } = req.params;
    const {
      marketplace_enabled,
      customer_orders_enabled,
      delivery_scheduling_enabled,
      driver_tracking_enabled,
      customer_messaging_enabled
    } = req.body;

    // Check if farm exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Get or create feature toggles for the farm
    let featureToggles = await FarmFeatureToggles.findOne({
      where: { farm_id: farmId }
    });

    if (!featureToggles) {
      // Create feature toggles if they don't exist
      featureToggles = await FarmFeatureToggles.create({
        farm_id: farmId,
        marketplace_enabled: marketplace_enabled || false,
        customer_orders_enabled: customer_orders_enabled || false,
        delivery_scheduling_enabled: delivery_scheduling_enabled || false,
        driver_tracking_enabled: driver_tracking_enabled || false,
        customer_messaging_enabled: customer_messaging_enabled || false
      });
    } else {
      // Update existing feature toggles
      await featureToggles.update({
        marketplace_enabled: marketplace_enabled !== undefined ? marketplace_enabled : featureToggles.marketplace_enabled,
        customer_orders_enabled: customer_orders_enabled !== undefined ? customer_orders_enabled : featureToggles.customer_orders_enabled,
        delivery_scheduling_enabled: delivery_scheduling_enabled !== undefined ? delivery_scheduling_enabled : featureToggles.delivery_scheduling_enabled,
        driver_tracking_enabled: driver_tracking_enabled !== undefined ? driver_tracking_enabled : featureToggles.driver_tracking_enabled,
        customer_messaging_enabled: customer_messaging_enabled !== undefined ? customer_messaging_enabled : featureToggles.customer_messaging_enabled
      });
    }

    return res.status(200).json(featureToggles);
  } catch (error) {
    console.error('Error updating farm feature toggles:', error);
    return res.status(500).json({ error: 'Failed to update farm feature toggles' });
  }
};
