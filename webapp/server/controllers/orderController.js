import Order from '../models/Order.js';
import OrderItem from '../models/OrderItem.js';
import Supplier from '../models/Supplier.js';
import InventoryItem from '../models/InventoryItem.js';
import { sequelize } from '../config/database.js';

// Get all orders for a farm
export const getOrders = async (req, res) => {
  try {
    const { farmId, status } = req.query;

    if (!farmId) {
      return res.status(400).json({ message: 'Farm ID is required' });
    }

    const whereClause = { farm_id: farmId };
    
    // Filter by status if provided
    if (status) {
      whereClause.status = status;
    }

    const orders = await Order.findAll({
      where: whereClause,
      include: [
        {
          model: Supplier,
          attributes: ['id', 'name', 'contact_name', 'email', 'phone']
        }
      ],
      order: [['order_date', 'DESC']]
    });

    res.status(200).json(orders);
  } catch (error) {
    console.error('Error fetching orders:', error);
    res.status(500).json({ message: 'Failed to fetch orders', error: error.message });
  }
};

// Get a single order by ID with its items
export const getOrder = async (req, res) => {
  try {
    const { id } = req.params;

    const order = await Order.findByPk(id, {
      include: [
        {
          model: Supplier,
          attributes: ['id', 'name', 'contact_name', 'email', 'phone']
        },
        {
          model: OrderItem,
          include: [
            {
              model: InventoryItem,
              attributes: ['id', 'name', 'description', 'unit']
            }
          ]
        }
      ]
    });

    if (!order) {
      return res.status(404).json({ message: 'Order not found' });
    }

    res.status(200).json(order);
  } catch (error) {
    console.error('Error fetching order:', error);
    res.status(500).json({ message: 'Failed to fetch order', error: error.message });
  }
};

// Create a new order with items
export const createOrder = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const {
      farm_id,
      supplier_id,
      user_id,
      order_number,
      status,
      order_date,
      expected_delivery_date,
      shipping_address,
      notes,
      items
    } = req.body;

    if (!farm_id || !supplier_id || !user_id) {
      await transaction.rollback();
      return res.status(400).json({ message: 'Farm ID, Supplier ID, and User ID are required' });
    }

    if (!items || !Array.isArray(items) || items.length === 0) {
      await transaction.rollback();
      return res.status(400).json({ message: 'At least one order item is required' });
    }

    // Calculate order totals
    let subtotal = 0;
    let tax = 0;
    let shipping = req.body.shipping || 0;

    // Create the order
    const newOrder = await Order.create({
      farm_id,
      supplier_id,
      user_id,
      order_number,
      status: status || 'draft',
      order_date: order_date || new Date(),
      expected_delivery_date,
      shipping_address,
      notes,
      subtotal: 0, // Will update after adding items
      tax: 0, // Will update after adding items
      shipping,
      total: 0 // Will update after adding items
    }, { transaction });

    // Create order items
    for (const item of items) {
      const itemSubtotal = parseFloat(item.quantity) * parseFloat(item.unit_price);
      const itemTaxAmount = item.tax_rate ? (itemSubtotal * parseFloat(item.tax_rate) / 100) : 0;
      
      subtotal += itemSubtotal;
      tax += itemTaxAmount;

      await OrderItem.create({
        order_id: newOrder.id,
        inventory_item_id: item.inventory_item_id,
        name: item.name,
        description: item.description,
        quantity: item.quantity,
        unit: item.unit,
        unit_price: item.unit_price,
        subtotal: itemSubtotal,
        tax_rate: item.tax_rate,
        tax_amount: itemTaxAmount,
        total: itemSubtotal + itemTaxAmount,
        notes: item.notes
      }, { transaction });
    }

    // Update order totals
    const total = subtotal + tax + parseFloat(shipping);
    
    await newOrder.update({
      subtotal,
      tax,
      total
    }, { transaction });

    await transaction.commit();

    // Fetch the complete order with items to return
    const completeOrder = await Order.findByPk(newOrder.id, {
      include: [
        {
          model: Supplier,
          attributes: ['id', 'name', 'contact_name', 'email', 'phone']
        },
        {
          model: OrderItem,
          include: [
            {
              model: InventoryItem,
              attributes: ['id', 'name', 'description', 'unit']
            }
          ]
        }
      ]
    });

    res.status(201).json(completeOrder);
  } catch (error) {
    await transaction.rollback();
    console.error('Error creating order:', error);
    res.status(500).json({ message: 'Failed to create order', error: error.message });
  }
};

// Update an order
export const updateOrder = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { id } = req.params;
    const {
      supplier_id,
      order_number,
      status,
      expected_delivery_date,
      actual_delivery_date,
      shipping,
      payment_status,
      payment_method,
      shipping_address,
      notes,
      items
    } = req.body;

    const order = await Order.findByPk(id, { transaction });

    if (!order) {
      await transaction.rollback();
      return res.status(404).json({ message: 'Order not found' });
    }

    // Update order fields
    if (supplier_id !== undefined) order.supplier_id = supplier_id;
    if (order_number !== undefined) order.order_number = order_number;
    if (status !== undefined) order.status = status;
    if (expected_delivery_date !== undefined) order.expected_delivery_date = expected_delivery_date;
    if (actual_delivery_date !== undefined) order.actual_delivery_date = actual_delivery_date;
    if (payment_status !== undefined) order.payment_status = payment_status;
    if (payment_method !== undefined) order.payment_method = payment_method;
    if (shipping_address !== undefined) order.shipping_address = shipping_address;
    if (notes !== undefined) order.notes = notes;
    if (shipping !== undefined) order.shipping = shipping;

    // If items are provided, update them
    if (items && Array.isArray(items)) {
      // Delete existing items
      await OrderItem.destroy({
        where: { order_id: id },
        transaction
      });

      // Calculate new totals
      let subtotal = 0;
      let tax = 0;

      // Create new items
      for (const item of items) {
        const itemSubtotal = parseFloat(item.quantity) * parseFloat(item.unit_price);
        const itemTaxAmount = item.tax_rate ? (itemSubtotal * parseFloat(item.tax_rate) / 100) : 0;
        
        subtotal += itemSubtotal;
        tax += itemTaxAmount;

        await OrderItem.create({
          order_id: order.id,
          inventory_item_id: item.inventory_item_id,
          name: item.name,
          description: item.description,
          quantity: item.quantity,
          unit: item.unit,
          unit_price: item.unit_price,
          subtotal: itemSubtotal,
          tax_rate: item.tax_rate,
          tax_amount: itemTaxAmount,
          total: itemSubtotal + itemTaxAmount,
          notes: item.notes
        }, { transaction });
      }

      // Update order totals
      order.subtotal = subtotal;
      order.tax = tax;
      order.total = subtotal + tax + parseFloat(order.shipping);
    }

    await order.save({ transaction });
    await transaction.commit();

    // Fetch the complete updated order with items
    const updatedOrder = await Order.findByPk(id, {
      include: [
        {
          model: Supplier,
          attributes: ['id', 'name', 'contact_name', 'email', 'phone']
        },
        {
          model: OrderItem,
          include: [
            {
              model: InventoryItem,
              attributes: ['id', 'name', 'description', 'unit']
            }
          ]
        }
      ]
    });

    res.status(200).json(updatedOrder);
  } catch (error) {
    await transaction.rollback();
    console.error('Error updating order:', error);
    res.status(500).json({ message: 'Failed to update order', error: error.message });
  }
};

// Delete an order
export const deleteOrder = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { id } = req.params;

    const order = await Order.findByPk(id, { transaction });

    if (!order) {
      await transaction.rollback();
      return res.status(404).json({ message: 'Order not found' });
    }

    // Delete order items first (should cascade, but being explicit)
    await OrderItem.destroy({
      where: { order_id: id },
      transaction
    });

    // Delete the order
    await order.destroy({ transaction });
    await transaction.commit();

    res.status(200).json({ message: 'Order deleted successfully' });
  } catch (error) {
    await transaction.rollback();
    console.error('Error deleting order:', error);
    res.status(500).json({ message: 'Failed to delete order', error: error.message });
  }
};

// Update order status
export const updateOrderStatus = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { id } = req.params;
    const { status } = req.body;

    if (!status) {
      await transaction.rollback();
      return res.status(400).json({ message: 'Status is required' });
    }

    const order = await Order.findByPk(id, { transaction });

    if (!order) {
      await transaction.rollback();
      return res.status(404).json({ message: 'Order not found' });
    }

    // Update status
    order.status = status;
    
    // If status is 'delivered', set actual_delivery_date
    if (status === 'delivered' && !order.actual_delivery_date) {
      order.actual_delivery_date = new Date();
    }

    await order.save({ transaction });
    await transaction.commit();

    res.status(200).json(order);
  } catch (error) {
    await transaction.rollback();
    console.error('Error updating order status:', error);
    res.status(500).json({ message: 'Failed to update order status', error: error.message });
  }
};

// Receive order (update inventory)
export const receiveOrder = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { id } = req.params;
    const { items, partial } = req.body;

    const order = await Order.findByPk(id, {
      include: [{ model: OrderItem }],
      transaction
    });

    if (!order) {
      await transaction.rollback();
      return res.status(404).json({ message: 'Order not found' });
    }

    // Validate items if partial receiving
    if (partial && (!items || !Array.isArray(items))) {
      await transaction.rollback();
      return res.status(400).json({ message: 'Items array is required for partial receiving' });
    }

    // Process all items or just the specified ones
    const itemsToProcess = partial ? items : order.OrderItems;

    for (const item of itemsToProcess) {
      const orderItem = partial 
        ? order.OrderItems.find(oi => oi.id === item.id)
        : item;
      
      if (!orderItem) {
        continue; // Skip if item not found
      }

      const receivedQuantity = partial ? parseFloat(item.received_quantity) : parseFloat(orderItem.quantity);
      
      // If item is linked to inventory, update inventory quantity
      if (orderItem.inventory_item_id) {
        const inventoryItem = await InventoryItem.findByPk(orderItem.inventory_item_id, { transaction });
        
        if (inventoryItem) {
          inventoryItem.quantity = parseFloat(inventoryItem.quantity) + receivedQuantity;
          await inventoryItem.save({ transaction });
        }
      }
    }

    // Update order status if not partial
    if (!partial) {
      order.status = 'delivered';
      order.actual_delivery_date = new Date();
    }

    await order.save({ transaction });
    await transaction.commit();

    res.status(200).json({ 
      message: partial ? 'Order partially received' : 'Order fully received',
      order_id: order.id
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error receiving order:', error);
    res.status(500).json({ message: 'Failed to receive order', error: error.message });
  }
};