import fs from 'fs';
import path from 'path';
import { promisify } from 'util';
import { exec } from 'child_process';
import { sequelize } from '../config/database.js';
import { Op } from 'sequelize';
import DatabaseMigration from '../models/DatabaseMigration.js';
import User from '../models/User.js';

const execPromise = promisify(exec);

/**
 * Get all database migrations
 * Supports filtering by status: all, pending, applied
 */
export const getAllMigrations = async (req, res) => {
  try {
    const { filter } = req.query;

    // Define where clause based on filter
    let whereClause = {};

    if (filter === 'pending') {
      whereClause = {
        applied: false,
        status: { [Op.ne]: 'skipped' }
      };
    } else if (filter === 'applied') {
      whereClause = { applied: true };
    }

    const migrations = await DatabaseMigration.findAll({
      where: whereClause,
      include: [
        {
          model: User,
          as: 'appliedBy',
          attributes: ['id', 'first_name', 'last_name', 'email'],
          required: false
        }
      ],
      order: [['order', 'ASC']]
    });

    return res.status(200).json({
      success: true,
      migrations
    });
  } catch (error) {
    console.error('Error fetching database migrations:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to fetch database migrations'
    });
  }
};

/**
 * Get a specific database migration by ID
 */
export const getMigration = async (req, res) => {
  try {
    const { migrationId } = req.params;

    const migration = await DatabaseMigration.findByPk(migrationId, {
      include: [
        {
          model: User,
          as: 'appliedBy',
          attributes: ['id', 'first_name', 'last_name', 'email'],
          required: false
        }
      ]
    });

    if (!migration) {
      return res.status(404).json({
        success: false,
        error: 'Database migration not found'
      });
    }

    return res.status(200).json({
      success: true,
      migration
    });
  } catch (error) {
    console.error('Error fetching database migration:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to fetch database migration'
    });
  }
};

/**
 * Apply a database migration
 */
export const applyMigration = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { migrationId } = req.params;

    const migration = await DatabaseMigration.findByPk(migrationId);

    if (!migration) {
      return res.status(404).json({
        success: false,
        error: 'Database migration not found'
      });
    }

    if (migration.applied) {
      return res.status(400).json({
        success: false,
        error: 'Migration has already been applied'
      });
    }

    if (migration.status === 'skipped') {
      return res.status(400).json({
        success: false,
        error: 'Migration has been skipped and cannot be applied'
      });
    }

    // We no longer enforce strict order, only dependencies

    // Check if all dependencies have been applied
    if (migration.dependencies && migration.dependencies.length > 0) {
      const dependencyMigrations = await DatabaseMigration.findAll({
        where: {
          name: {
            [Op.in]: migration.dependencies
          }
        }
      });

      // Check if all dependencies were found
      if (dependencyMigrations.length !== migration.dependencies.length) {
        const foundDependencies = dependencyMigrations.map(dep => dep.name);
        const missingDependencies = migration.dependencies.filter(dep => !foundDependencies.includes(dep));

        return res.status(400).json({
          success: false,
          error: `Missing dependencies: ${missingDependencies.join(', ')}. Please scan for migrations to find these dependencies.`
        });
      }

      // Check if all dependencies have been applied
      const unappliedDependencies = dependencyMigrations.filter(dep => !dep.applied && dep.status !== 'skipped');

      if (unappliedDependencies.length > 0) {
        const unappliedNames = unappliedDependencies.map(dep => dep.name).join(', ');

        return res.status(400).json({
          success: false,
          error: `Dependency issue: This migration depends on other migrations that haven't been applied yet. Please apply these dependencies first: ${unappliedNames}`
        });
      }
    }

    // Update migration status to in_progress
    await migration.update({
      status: 'in_progress'
    }, { transaction });

    // Read the SQL file
    const filePath = migration.file_path;

    // Check if file exists before reading
    if (!fs.existsSync(filePath)) {
      await transaction.rollback();
      return res.status(400).json({
        success: false,
        error: `Migration file does not exist: ${filePath}`
      });
    }

    const sql = fs.readFileSync(filePath, 'utf8');

    // Set the db_schema PostgreSQL variable from the environment variable with proper namespace
    const schema = process.env.DB_SCHEMA || 'site';
    await sequelize.query(`SET LOCAL app.db_schema = '${schema}';`, { transaction });

    // Execute the SQL
    await sequelize.query(sql, { transaction });

    // Update migration status to completed
    await migration.update({
      applied: true,
      applied_at: new Date(),
      applied_by: req.user.id,
      status: 'completed'
    }, { transaction });

    await transaction.commit();

    return res.status(200).json({
      success: true,
      message: 'Migration applied successfully',
      migration
    });
  } catch (error) {
    await transaction.rollback();

    console.error('Error applying database migration:', error);

    // Check if the error message indicates the item was already done
    const alreadyDonePatterns = [
      /already exists/i,
      /duplicate key/i
    ];

    const isAlreadyDone = alreadyDonePatterns.some(pattern => pattern.test(error.message));

    // Update migration status based on the error type
    if (req.params.migrationId) {
      try {
        const migration = await DatabaseMigration.findByPk(req.params.migrationId);
        if (migration) {
          if (isAlreadyDone) {
            // Mark as completed if the error indicates the item was already done
            await migration.update({
              applied: true,
              applied_at: new Date(),
              applied_by: req.user.id,
              status: 'completed',
              error_message: `Automatically marked as completed. Original error: ${error.message}`
            });

            return res.status(200).json({
              success: true,
              message: 'Migration marked as completed (already done)',
              migration
            });
          } else {
            // Mark as failed for other errors
            await migration.update({
              status: 'failed',
              error_message: error.message
            });
          }
        }
      } catch (updateError) {
        console.error('Error updating migration status:', updateError);
      }
    }

    return res.status(500).json({
      success: false,
      error: 'Failed to apply database migration',
      details: error.message
    });
  }
};

/**
 * Recursively find all SQL files in a directory and its subdirectories
 * @param {string} dir - Directory to search
 * @param {Array} fileList - Array to store found files
 * @returns {Array} - Array of objects with name and path of SQL files
 */
const findSqlFiles = (dir, fileList = []) => {
  try {
    // Check if directory exists
    if (!fs.existsSync(dir)) {
      console.error(`Directory does not exist: ${dir}`);
      return fileList;
    }

    console.log(`Scanning directory: ${dir}`);
    const files = fs.readdirSync(dir);

    files.forEach(file => {
      try {
        const filePath = path.join(dir, file);
        const stat = fs.statSync(filePath);

        if (stat.isDirectory()) {
          // Recursively search subdirectories and make sure to update the fileList
          console.log(`Found subdirectory: ${filePath}`);
          findSqlFiles(filePath, fileList);
        } else if (file.endsWith('.sql')) {
          // Check if file exists before adding to the list
          if (fs.existsSync(filePath)) {
            console.log(`Found SQL file: ${filePath}`);
            fileList.push({
              name: file,
              filePath: filePath
            });
          } else {
            console.error(`SQL file does not exist: ${filePath}`);
          }
        }
      } catch (fileError) {
        console.error(`Error processing file ${file} in directory ${dir}:`, fileError);
        // Continue with next file
      }
    });

    return fileList;
  } catch (dirError) {
    console.error(`Error reading directory ${dir}:`, dirError);
    return fileList;
  }
};

/**
 * Scan for new migrations in the db directory and its subdirectories
 */
export const scanForMigrations = async (req, res) => {
  try {
    const dbDir = path.join(process.cwd(), 'server', 'db');

    // Check if the db directory exists
    if (!fs.existsSync(dbDir)) {
      console.error(`Database directory does not exist: ${dbDir}`);
      return res.status(500).json({
        success: false,
        error: 'Failed to scan for migrations',
        details: `Database directory does not exist: ${dbDir}`
      });
    }

    console.log(`Scanning for SQL files in: ${dbDir}`);

    // Get all SQL files in the db directory and its subdirectories
    const sqlFiles = findSqlFiles(dbDir);

    if (!sqlFiles || sqlFiles.length === 0) {
      console.log('No SQL files found in the database directory');
    } else {
      console.log(`Found ${sqlFiles.length} SQL files`);
    }

    // Get existing migrations
    const existingMigrations = await DatabaseMigration.findAll();
    const existingPaths = existingMigrations.map(m => m.file_path);

    // Find new migrations
    const newMigrations = [];
    const existingNames = existingMigrations.map(m => m.name);

    for (const sqlFile of sqlFiles) {
      try {
        const { name, filePath } = sqlFile;

        // Skip if already in database by path
        if (existingPaths.includes(filePath)) {
          continue;
        }

        // Skip if a migration with the same name (without extension) already exists
        const nameWithoutExtension = name.replace(/\.sql$/, '');
        if (existingNames.includes(name) || existingNames.includes(nameWithoutExtension)) {
          console.log(`Skipping duplicate migration: ${name}`);
          continue;
        }

        // Check if file exists before reading
        if (!fs.existsSync(filePath)) {
          console.error(`SQL file does not exist: ${filePath}`);
          continue;
        }

        // Read the first few lines to extract description
        const fileContent = fs.readFileSync(filePath, 'utf8');
        const lines = fileContent.split('\n').slice(0, 10);
        let description = '';

        // Look for comment lines that might contain a description or dependencies
        let dependencies = [];
        for (const line of lines) {
          const trimmedLine = line.trim();
          if (trimmedLine.startsWith('--') || trimmedLine.startsWith('/*')) {
            const commentText = trimmedLine.replace(/^--\s*|^\/\*|\*\/$/g, '').trim();

            // Check for Migration description
            if (commentText.startsWith('Migration:')) {
              const migrationDesc = commentText.replace(/^Migration:\s*/, '').trim();
              if (migrationDesc) {
                description = migrationDesc;
              }
            }
            // Check for dependencies
            else if (commentText.startsWith('Depends on:')) {
              const dependsOn = commentText.replace(/^Depends on:\s*/, '').trim();
              if (dependsOn) {
                dependencies = dependsOn.split(',').map(dep => dep.trim());
              }
            }
            // Add other comments to description if needed
            else if (commentText && !commentText.startsWith('Migration') && description.length < 100) {
              description += (description ? ' ' : '') + commentText;
            }
          }
        }

        // Create new migration record
        const newMigration = await DatabaseMigration.create({
          name: name,
          description: description || `SQL migration: ${name}`,
          file_path: filePath,
          order: existingMigrations.length + newMigrations.length + 1,
          dependencies: dependencies
        });

        newMigrations.push(newMigration);
      } catch (fileError) {
        console.error(`Error processing SQL file:`, fileError);
        // Continue with next file
      }
    }

    return res.status(200).json({
      success: true,
      message: `Found ${newMigrations.length} new migrations`,
      newMigrations
    });
  } catch (error) {
    console.error('Error scanning for migrations:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to scan for migrations',
      details: error.message
    });
  }
};

/**
 * Skip a database migration
 */
export const skipMigration = async (req, res) => {
  try {
    const { migrationId } = req.params;

    const migration = await DatabaseMigration.findByPk(migrationId);

    if (!migration) {
      return res.status(404).json({
        success: false,
        error: 'Database migration not found'
      });
    }

    if (migration.applied) {
      return res.status(400).json({
        success: false,
        error: 'Migration has already been applied'
      });
    }

    if (migration.status === 'skipped') {
      return res.status(400).json({
        success: false,
        error: 'Migration has already been skipped'
      });
    }

    // We no longer enforce strict order, only dependencies

    // Check if any other migrations depend on this one
    // Use literal SQL with proper type casting to avoid text[] vs varchar[] mismatch
    const dependentMigrations = await DatabaseMigration.findAll({
      where: sequelize.literal(`dependencies @> ARRAY['${migration.name}']::text[]`)
    });

    if (dependentMigrations.length > 0) {
      const dependentNames = dependentMigrations.map(dep => dep.name).join(', ');

      return res.status(400).json({
        success: false,
        error: `Cannot skip this migration because other migrations depend on it: ${dependentNames}`
      });
    }

    // Update migration to mark it as skipped
    await migration.update({
      applied_at: new Date(),
      applied_by: req.user.id,
      status: 'skipped'
    });

    return res.status(200).json({
      success: true,
      message: 'Migration skipped successfully',
      migration
    });
  } catch (error) {
    console.error('Error skipping database migration:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to skip database migration',
      details: error.message
    });
  }
};

/**
 * Check if all migrations have been applied
 */
export const checkMigrationStatus = async (req, res) => {
  try {
    const pendingMigrations = await DatabaseMigration.count({
      where: {
        applied: false,
        status: { [Op.ne]: 'skipped' }
      }
    });

    const allMigrations = await DatabaseMigration.count();
    const appliedMigrations = await DatabaseMigration.count({
      where: { applied: true }
    });
    const skippedMigrations = await DatabaseMigration.count({
      where: { status: 'skipped' }
    });

    return res.status(200).json({
      success: true,
      pendingMigrations,
      allMigrations,
      appliedMigrations,
      skippedMigrations,
      allApplied: pendingMigrations === 0
    });
  } catch (error) {
    console.error('Error checking migration status:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to check migration status'
    });
  }
};

/**
 * Retry a skipped migration
 */
export const retrySkippedMigration = async (req, res) => {
  try {
    const { migrationId } = req.params;

    const migration = await DatabaseMigration.findByPk(migrationId);

    if (!migration) {
      return res.status(404).json({
        success: false,
        error: 'Database migration not found'
      });
    }

    if (migration.applied) {
      return res.status(400).json({
        success: false,
        error: 'Migration has already been applied'
      });
    }

    if (migration.status !== 'skipped') {
      return res.status(400).json({
        success: false,
        error: 'Only skipped migrations can be retried'
      });
    }

    // We no longer enforce strict order, only dependencies

    // Check if all dependencies have been applied
    if (migration.dependencies && migration.dependencies.length > 0) {
      const dependencyMigrations = await DatabaseMigration.findAll({
        where: {
          name: {
            [Op.in]: migration.dependencies
          }
        }
      });

      // Check if all dependencies were found
      if (dependencyMigrations.length !== migration.dependencies.length) {
        const foundDependencies = dependencyMigrations.map(dep => dep.name);
        const missingDependencies = migration.dependencies.filter(dep => !foundDependencies.includes(dep));

        return res.status(400).json({
          success: false,
          error: `Missing dependencies: ${missingDependencies.join(', ')}. Please scan for migrations to find these dependencies.`
        });
      }

      // Check if all dependencies have been applied
      const unappliedDependencies = dependencyMigrations.filter(dep => !dep.applied && dep.status !== 'skipped');

      if (unappliedDependencies.length > 0) {
        const unappliedNames = unappliedDependencies.map(dep => dep.name).join(', ');

        return res.status(400).json({
          success: false,
          error: `Dependency issue: This migration depends on other migrations that haven't been applied yet. Please apply these dependencies first: ${unappliedNames}`
        });
      }
    }

    // Update migration to mark it as pending again
    await migration.update({
      applied_at: null,
      applied_by: null,
      status: 'pending'
    });

    return res.status(200).json({
      success: true,
      message: 'Migration reset to pending status successfully',
      migration
    });
  } catch (error) {
    console.error('Error retrying skipped migration:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to retry skipped migration',
      details: error.message
    });
  }
};
