import FarmFieldCollaboration from '../models/FarmFieldCollaboration.js';
import Field from '../models/Field.js';
import Farm from '../models/Farm.js';
import { sequelize } from '../config/database.js';
import { Op } from 'sequelize';

// Create a new field collaboration
export const createFieldCollaboration = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { field_id, owner_farm_id, collaborator_farm_id, permission_level } = req.body;

    // Validate required fields
    if (!field_id) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Field ID is required' });
    }

    if (!owner_farm_id) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Owner farm ID is required' });
    }

    if (!collaborator_farm_id) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Collaborator farm ID is required' });
    }

    // Check if field exists
    const field = await Field.findByPk(field_id);
    if (!field) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Field not found' });
    }

    // Check if field belongs to owner farm
    if (field.farm_id !== owner_farm_id) {
      await transaction.rollback();
      return res.status(403).json({ error: 'Field does not belong to the specified owner farm' });
    }

    // Check if owner farm exists
    const ownerFarm = await Farm.findByPk(owner_farm_id);
    if (!ownerFarm) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Owner farm not found' });
    }

    // Check if collaborator farm exists
    const collaboratorFarm = await Farm.findByPk(collaborator_farm_id);
    if (!collaboratorFarm) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Collaborator farm not found' });
    }

    // Check if collaboration already exists
    const existingCollaboration = await FarmFieldCollaboration.findOne({
      where: {
        field_id,
        collaborator_farm_id
      }
    });

    if (existingCollaboration) {
      await transaction.rollback();
      return res.status(409).json({ error: 'Collaboration already exists for this field and farm' });
    }

    // Create the collaboration
    const collaboration = await FarmFieldCollaboration.create({
      field_id,
      owner_farm_id,
      collaborator_farm_id,
      permission_level: permission_level || 'read'
    }, { transaction });

    await transaction.commit();
    return res.status(201).json(collaboration);
  } catch (error) {
    await transaction.rollback();
    console.error('Error creating field collaboration:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get all collaborations for a field
export const getFieldCollaborations = async (req, res) => {
  try {
    const { fieldId } = req.params;

    // Check if field exists
    const field = await Field.findByPk(fieldId);
    if (!field) {
      return res.status(404).json({ error: 'Field not found' });
    }

    // Get all collaborations for the field
    const collaborations = await FarmFieldCollaboration.findAll({
      where: { field_id: fieldId },
      include: [
        {
          model: Farm,
          as: 'collaboratorFarm',
          attributes: ['id', 'name']
        }
      ]
    });

    return res.status(200).json(collaborations);
  } catch (error) {
    console.error('Error getting field collaborations:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get all fields a farm is collaborating on (fields shared with this farm)
export const getCollaboratingFields = async (req, res) => {
  try {
    const { farmId } = req.params;

    // Check if farm exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Get all fields the farm is collaborating on
    const collaborations = await FarmFieldCollaboration.findAll({
      where: { collaborator_farm_id: farmId },
      include: [
        {
          model: Field,
          as: 'field'
        },
        {
          model: Farm,
          as: 'ownerFarm',
          attributes: ['id', 'name']
        }
      ]
    });

    return res.status(200).json(collaborations);
  } catch (error) {
    console.error('Error getting collaborating fields:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get all fields a farm is sharing with others
export const getSharedFields = async (req, res) => {
  try {
    const { farmId } = req.params;

    // Check if farm exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Get all fields the farm is sharing
    const collaborations = await FarmFieldCollaboration.findAll({
      where: { owner_farm_id: farmId },
      include: [
        {
          model: Field,
          as: 'field'
        },
        {
          model: Farm,
          as: 'collaboratorFarm',
          attributes: ['id', 'name']
        }
      ]
    });

    return res.status(200).json(collaborations);
  } catch (error) {
    console.error('Error getting shared fields:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Update a collaboration's permission level
export const updateFieldCollaboration = async (req, res) => {
  try {
    const { collaborationId } = req.params;
    const { permission_level } = req.body;

    // Validate permission level
    if (!['read', 'write', 'admin'].includes(permission_level)) {
      return res.status(400).json({ error: 'Invalid permission level. Must be read, write, or admin' });
    }

    // Find the collaboration
    const collaboration = await FarmFieldCollaboration.findByPk(collaborationId);
    if (!collaboration) {
      return res.status(404).json({ error: 'Collaboration not found' });
    }

    // Update the collaboration
    collaboration.permission_level = permission_level;
    await collaboration.save();

    return res.status(200).json(collaboration);
  } catch (error) {
    console.error('Error updating field collaboration:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Remove a collaboration
export const removeFieldCollaboration = async (req, res) => {
  try {
    const { collaborationId } = req.params;

    // Find the collaboration
    const collaboration = await FarmFieldCollaboration.findByPk(collaborationId);
    if (!collaboration) {
      return res.status(404).json({ error: 'Collaboration not found' });
    }

    // Delete the collaboration
    await collaboration.destroy();

    return res.status(204).send();
  } catch (error) {
    console.error('Error removing field collaboration:', error);
    return res.status(500).json({ error: error.message });
  }
};