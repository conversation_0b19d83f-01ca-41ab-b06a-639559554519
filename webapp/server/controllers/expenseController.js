import Expense from '../models/Expense.js';
import Employee from '../models/Employee.js';
import User from '../models/User.js';
import { Op } from 'sequelize';

// Get all expenses with optional filtering
export const getExpenses = async (req, res) => {
  try {
    const { employee_id, status, category, start_date, end_date } = req.query;
    
    // Build filter conditions
    const whereConditions = {};
    
    if (employee_id) {
      whereConditions.employee_id = employee_id;
    }
    
    if (status && status !== 'all') {
      whereConditions.status = status;
    }
    
    if (category && category !== 'all') {
      whereConditions.category = category;
    }
    
    // Filter by expense date range
    if (start_date || end_date) {
      whereConditions.expense_date = {};
      
      if (start_date) {
        whereConditions.expense_date[Op.gte] = new Date(start_date);
      }
      
      if (end_date) {
        const endDateTime = new Date(end_date);
        endDateTime.setHours(23, 59, 59, 999);
        whereConditions.expense_date[Op.lte] = endDateTime;
      }
    }
    
    // Find expenses with filters
    const expenses = await Expense.findAll({
      where: whereConditions,
      include: [
        {
          model: Employee,
          attributes: ['id', 'first_name', 'last_name'],
        },
        {
          model: User,
          as: 'reviewer',
          attributes: ['id', 'first_name', 'last_name'],
        }
      ],
      order: [['expense_date', 'DESC']]
    });
    
    res.status(200).json(expenses);
  } catch (error) {
    console.error('Error fetching expenses:', error);
    res.status(500).json({ message: 'Failed to fetch expenses', error: error.message });
  }
};

// Get expense by ID
export const getExpenseById = async (req, res) => {
  try {
    const { expenseId } = req.params;
    
    const expense = await Expense.findByPk(expenseId, {
      include: [
        {
          model: Employee,
          attributes: ['id', 'first_name', 'last_name'],
        },
        {
          model: User,
          as: 'reviewer',
          attributes: ['id', 'first_name', 'last_name'],
        }
      ]
    });
    
    if (!expense) {
      return res.status(404).json({ message: 'Expense not found' });
    }
    
    res.status(200).json(expense);
  } catch (error) {
    console.error('Error fetching expense:', error);
    res.status(500).json({ message: 'Failed to fetch expense', error: error.message });
  }
};

// Create a new expense
export const createExpense = async (req, res) => {
  try {
    const expenseData = req.body;
    
    // Validate required fields
    if (!expenseData.employee_id || !expenseData.expense_date || !expenseData.category || 
        !expenseData.amount || !expenseData.description) {
      return res.status(400).json({ 
        message: 'Employee ID, expense date, category, amount, and description are required' 
      });
    }
    
    // Create the expense
    const newExpense = await Expense.create(expenseData);
    
    // Fetch the created expense with associations
    const expense = await Expense.findByPk(newExpense.id, {
      include: [
        {
          model: Employee,
          attributes: ['id', 'first_name', 'last_name'],
        }
      ]
    });
    
    res.status(201).json(expense);
  } catch (error) {
    console.error('Error creating expense:', error);
    res.status(500).json({ message: 'Failed to create expense', error: error.message });
  }
};

// Update an expense
export const updateExpense = async (req, res) => {
  try {
    const { expenseId } = req.params;
    const expenseData = req.body;
    
    // Find the expense
    const expense = await Expense.findByPk(expenseId);
    
    if (!expense) {
      return res.status(404).json({ message: 'Expense not found' });
    }
    
    // Update the expense
    await expense.update(expenseData);
    
    // Fetch the updated expense with associations
    const updatedExpense = await Expense.findByPk(expenseId, {
      include: [
        {
          model: Employee,
          attributes: ['id', 'first_name', 'last_name'],
        },
        {
          model: User,
          as: 'reviewer',
          attributes: ['id', 'first_name', 'last_name'],
        }
      ]
    });
    
    res.status(200).json(updatedExpense);
  } catch (error) {
    console.error('Error updating expense:', error);
    res.status(500).json({ message: 'Failed to update expense', error: error.message });
  }
};

// Delete an expense
export const deleteExpense = async (req, res) => {
  try {
    const { expenseId } = req.params;
    
    // Find the expense
    const expense = await Expense.findByPk(expenseId);
    
    if (!expense) {
      return res.status(404).json({ message: 'Expense not found' });
    }
    
    // Delete the expense
    await expense.destroy();
    
    res.status(200).json({ message: 'Expense deleted successfully' });
  } catch (error) {
    console.error('Error deleting expense:', error);
    res.status(500).json({ message: 'Failed to delete expense', error: error.message });
  }
};

// Get expenses by employee ID
export const getExpensesByEmployee = async (req, res) => {
  try {
    const { employeeId } = req.params;
    const { status } = req.query;
    
    // Build filter conditions
    const whereConditions = {
      employee_id: employeeId
    };
    
    if (status && status !== 'all') {
      whereConditions.status = status;
    }
    
    const expenses = await Expense.findAll({
      where: whereConditions,
      order: [['expense_date', 'DESC']]
    });
    
    res.status(200).json(expenses);
  } catch (error) {
    console.error('Error fetching employee expenses:', error);
    res.status(500).json({ message: 'Failed to fetch employee expenses', error: error.message });
  }
};

// Review an expense (approve, deny, or reimburse)
export const reviewExpense = async (req, res) => {
  try {
    const { expenseId } = req.params;
    const { status, reviewed_by, review_notes, reimbursed_date, reimbursed_amount } = req.body;
    
    // Validate required fields
    if (!status || !reviewed_by) {
      return res.status(400).json({ message: 'Status and reviewer ID are required' });
    }
    
    // Find the expense
    const expense = await Expense.findByPk(expenseId);
    
    if (!expense) {
      return res.status(404).json({ message: 'Expense not found' });
    }
    
    // Update the expense
    const updateData = {
      status,
      reviewed_by,
      reviewed_at: new Date(),
      review_notes: review_notes || expense.review_notes
    };
    
    // Add reimbursement details if status is 'reimbursed'
    if (status === 'reimbursed') {
      if (!reimbursed_date) {
        return res.status(400).json({ message: 'Reimbursement date is required for reimbursed status' });
      }
      
      updateData.reimbursed_date = reimbursed_date;
      updateData.reimbursed_amount = reimbursed_amount || expense.amount;
    }
    
    await expense.update(updateData);
    
    // Fetch the updated expense with associations
    const updatedExpense = await Expense.findByPk(expenseId, {
      include: [
        {
          model: Employee,
          attributes: ['id', 'first_name', 'last_name'],
        },
        {
          model: User,
          as: 'reviewer',
          attributes: ['id', 'first_name', 'last_name'],
        }
      ]
    });
    
    res.status(200).json(updatedExpense);
  } catch (error) {
    console.error('Error reviewing expense:', error);
    res.status(500).json({ message: 'Failed to review expense', error: error.message });
  }
};

// Get expense summary by employee
export const getExpenseSummary = async (req, res) => {
  try {
    const { employee_id, year, month } = req.query;
    
    // Build filter conditions
    const whereConditions = {};
    
    if (employee_id) {
      whereConditions.employee_id = employee_id;
    }
    
    // Filter by year and/or month
    if (year || month) {
      whereConditions.expense_date = {};
      
      if (year && month) {
        // Filter by specific year and month
        const startDate = new Date(parseInt(year), parseInt(month) - 1, 1);
        const endDate = new Date(parseInt(year), parseInt(month), 0, 23, 59, 59, 999);
        
        whereConditions.expense_date[Op.between] = [startDate, endDate];
      } else if (year) {
        // Filter by year only
        const startDate = new Date(parseInt(year), 0, 1);
        const endDate = new Date(parseInt(year), 11, 31, 23, 59, 59, 999);
        
        whereConditions.expense_date[Op.between] = [startDate, endDate];
      } else if (month) {
        // Filter by month only (current year)
        const currentYear = new Date().getFullYear();
        const startDate = new Date(currentYear, parseInt(month) - 1, 1);
        const endDate = new Date(currentYear, parseInt(month), 0, 23, 59, 59, 999);
        
        whereConditions.expense_date[Op.between] = [startDate, endDate];
      }
    }
    
    // Find expenses with filters
    const expenses = await Expense.findAll({
      where: whereConditions,
      include: [
        {
          model: Employee,
          attributes: ['id', 'first_name', 'last_name'],
        }
      ]
    });
    
    // Calculate summary
    const summary = {
      total_amount: 0,
      total_reimbursed: 0,
      total_pending: 0,
      total_approved: 0,
      total_denied: 0,
      by_category: {},
      by_status: {
        pending: 0,
        approved: 0,
        denied: 0,
        reimbursed: 0
      },
      by_payment_method: {}
    };
    
    expenses.forEach(expense => {
      const amount = parseFloat(expense.amount) || 0;
      summary.total_amount += amount;
      
      // Summarize by status
      if (expense.status === 'reimbursed') {
        const reimbursedAmount = parseFloat(expense.reimbursed_amount) || amount;
        summary.total_reimbursed += reimbursedAmount;
      } else if (expense.status === 'pending') {
        summary.total_pending += amount;
      } else if (expense.status === 'approved') {
        summary.total_approved += amount;
      } else if (expense.status === 'denied') {
        summary.total_denied += amount;
      }
      
      summary.by_status[expense.status] = (summary.by_status[expense.status] || 0) + amount;
      
      // Summarize by category
      if (expense.category) {
        if (!summary.by_category[expense.category]) {
          summary.by_category[expense.category] = 0;
        }
        summary.by_category[expense.category] += amount;
      }
      
      // Summarize by payment method
      if (expense.payment_method) {
        if (!summary.by_payment_method[expense.payment_method]) {
          summary.by_payment_method[expense.payment_method] = 0;
        }
        summary.by_payment_method[expense.payment_method] += amount;
      }
    });
    
    res.status(200).json(summary);
  } catch (error) {
    console.error('Error generating expense summary:', error);
    res.status(500).json({ message: 'Failed to generate expense summary', error: error.message });
  }
};