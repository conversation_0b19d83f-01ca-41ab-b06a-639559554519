import MarketplaceListing from '../models/MarketplaceListing.js';
import Farm from '../models/Farm.js';
import User from '../models/User.js';
import Product from '../models/Product.js';
import { sequelize } from '../config/database.js';

// Get all marketplace listings (for global admin or public marketplace)
export const getAllListings = async (req, res) => {
  try {
    const { category, subcategory, listingType, minPrice, maxPrice, location, searchTerm, visibility } = req.query;

    // Build the where clause
    const whereClause = {};
    
    // Filter by visibility (default to public)
    whereClause.visibility = visibility || 'public';
    
    // Filter by status (only show active listings)
    whereClause.status = 'active';
    whereClause.is_active = true;
    
    // Apply additional filters if provided
    if (category) {
      whereClause.category = category;
    }
    
    if (subcategory) {
      whereClause.subcategory = subcategory;
    }
    
    if (listingType) {
      whereClause.listing_type = listingType;
    }
    
    if (location) {
      whereClause.location = { [sequelize.Op.iLike]: `%${location}%` };
    }
    
    // Price range filter
    if (minPrice || maxPrice) {
      whereClause.price = {};
      if (minPrice) {
        whereClause.price[sequelize.Op.gte] = parseFloat(minPrice);
      }
      if (maxPrice) {
        whereClause.price[sequelize.Op.lte] = parseFloat(maxPrice);
      }
    }
    
    // Search term filter (search in title and description)
    if (searchTerm) {
      whereClause[sequelize.Op.or] = [
        { title: { [sequelize.Op.iLike]: `%${searchTerm}%` } },
        { description: { [sequelize.Op.iLike]: `%${searchTerm}%` } }
      ];
    }

    const listings = await MarketplaceListing.findAll({
      where: whereClause,
      include: [
        {
          model: Farm,
          as: 'marketplaceListingFarm',
          attributes: ['id', 'name']
        },
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'first_name', 'last_name', 'email']
        },
        {
          model: Product,
          as: 'marketplaceListingProduct',
          attributes: ['id', 'name', 'type']
        }
      ],
      order: [
        ['is_featured', 'DESC'],
        ['created_at', 'DESC']
      ]
    });

    res.status(200).json(listings);
  } catch (error) {
    console.error('Error fetching marketplace listings:', error);
    res.status(500).json({ message: 'Failed to fetch marketplace listings', error: error.message });
  }
};

// Get all marketplace listings for a farm
export const getFarmListings = async (req, res) => {
  try {
    const { farmId } = req.query;

    if (!farmId) {
      return res.status(400).json({ message: 'Farm ID is required' });
    }

    const listings = await MarketplaceListing.findAll({
      where: {
        farm_id: farmId
      },
      include: [
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'first_name', 'last_name', 'email']
        },
        {
          model: Product,
          as: 'marketplaceListingProduct',
          attributes: ['id', 'name', 'type']
        }
      ],
      order: [
        ['is_featured', 'DESC'],
        ['created_at', 'DESC']
      ]
    });

    res.status(200).json(listings);
  } catch (error) {
    console.error('Error fetching farm marketplace listings:', error);
    res.status(500).json({ message: 'Failed to fetch farm marketplace listings', error: error.message });
  }
};

// Get a single marketplace listing by ID
export const getListing = async (req, res) => {
  try {
    const { id } = req.params;

    const listing = await MarketplaceListing.findByPk(id, {
      include: [
        {
          model: Farm,
          as: 'marketplaceListingFarm',
          attributes: ['id', 'name']
        },
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'first_name', 'last_name', 'email']
        },
        {
          model: Product,
          as: 'marketplaceListingProduct',
          attributes: ['id', 'name', 'description', 'type', 'price', 'unit']
        }
      ]
    });

    if (!listing) {
      return res.status(404).json({ message: 'Marketplace listing not found' });
    }

    // Increment view count
    await MarketplaceListing.update(
      { views: listing.views + 1 },
      { where: { id } }
    );

    // Return the listing with updated view count
    listing.views += 1;

    res.status(200).json(listing);
  } catch (error) {
    console.error('Error fetching marketplace listing:', error);
    res.status(500).json({ message: 'Failed to fetch marketplace listing', error: error.message });
  }
};

// Create a new marketplace listing
export const createListing = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const {
      farm_id,
      created_by,
      product_id,
      title,
      description,
      listing_type,
      category,
      subcategory,
      price,
      price_type,
      currency,
      quantity,
      unit,
      location,
      latitude,
      longitude,
      images,
      tags,
      status,
      expiration_date,
      visibility,
      contact_info,
      shipping_options,
      payment_options,
      is_featured,
      is_active
    } = req.body;

    if (!farm_id || !created_by || !title || !listing_type || !category) {
      await transaction.rollback();
      return res.status(400).json({ message: 'Farm ID, created by, title, listing type, and category are required' });
    }

    const newListing = await MarketplaceListing.create({
      farm_id,
      created_by,
      product_id,
      title,
      description,
      listing_type,
      category,
      subcategory,
      price,
      price_type,
      currency: currency || 'USD',
      quantity,
      unit,
      location,
      latitude,
      longitude,
      images,
      tags,
      status: status || 'active',
      expiration_date,
      visibility: visibility || 'public',
      contact_info,
      shipping_options,
      payment_options,
      views: 0,
      favorites: 0,
      is_featured: is_featured !== undefined ? is_featured : false,
      is_active: is_active !== undefined ? is_active : true
    }, { transaction });

    await transaction.commit();

    // Return the created listing with related data
    const result = await MarketplaceListing.findByPk(newListing.id, {
      include: [
        {
          model: Farm,
          as: 'marketplaceListingFarm',
          attributes: ['id', 'name']
        },
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'first_name', 'last_name', 'email']
        },
        {
          model: Product,
          as: 'marketplaceListingProduct',
          attributes: ['id', 'name', 'description', 'type', 'price', 'unit']
        }
      ]
    });

    res.status(201).json(result);
  } catch (error) {
    await transaction.rollback();
    console.error('Error creating marketplace listing:', error);
    res.status(500).json({ message: 'Failed to create marketplace listing', error: error.message });
  }
};

// Update a marketplace listing
export const updateListing = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { id } = req.params;
    const {
      product_id,
      title,
      description,
      listing_type,
      category,
      subcategory,
      price,
      price_type,
      currency,
      quantity,
      unit,
      location,
      latitude,
      longitude,
      images,
      tags,
      status,
      expiration_date,
      visibility,
      contact_info,
      shipping_options,
      payment_options,
      is_featured,
      is_active
    } = req.body;

    const listing = await MarketplaceListing.findByPk(id, { transaction });

    if (!listing) {
      await transaction.rollback();
      return res.status(404).json({ message: 'Marketplace listing not found' });
    }

    // Update listing fields
    if (product_id !== undefined) listing.product_id = product_id;
    if (title !== undefined) listing.title = title;
    if (description !== undefined) listing.description = description;
    if (listing_type !== undefined) listing.listing_type = listing_type;
    if (category !== undefined) listing.category = category;
    if (subcategory !== undefined) listing.subcategory = subcategory;
    if (price !== undefined) listing.price = price;
    if (price_type !== undefined) listing.price_type = price_type;
    if (currency !== undefined) listing.currency = currency;
    if (quantity !== undefined) listing.quantity = quantity;
    if (unit !== undefined) listing.unit = unit;
    if (location !== undefined) listing.location = location;
    if (latitude !== undefined) listing.latitude = latitude;
    if (longitude !== undefined) listing.longitude = longitude;
    if (images !== undefined) listing.images = images;
    if (tags !== undefined) listing.tags = tags;
    if (status !== undefined) listing.status = status;
    if (expiration_date !== undefined) listing.expiration_date = expiration_date;
    if (visibility !== undefined) listing.visibility = visibility;
    if (contact_info !== undefined) listing.contact_info = contact_info;
    if (shipping_options !== undefined) listing.shipping_options = shipping_options;
    if (payment_options !== undefined) listing.payment_options = payment_options;
    if (is_featured !== undefined) listing.is_featured = is_featured;
    if (is_active !== undefined) listing.is_active = is_active;

    await listing.save({ transaction });
    await transaction.commit();

    // Return the updated listing with related data
    const result = await MarketplaceListing.findByPk(id, {
      include: [
        {
          model: Farm,
          as: 'marketplaceListingFarm',
          attributes: ['id', 'name']
        },
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'first_name', 'last_name', 'email']
        },
        {
          model: Product,
          as: 'marketplaceListingProduct',
          attributes: ['id', 'name', 'description', 'type', 'price', 'unit']
        }
      ]
    });

    res.status(200).json(result);
  } catch (error) {
    await transaction.rollback();
    console.error('Error updating marketplace listing:', error);
    res.status(500).json({ message: 'Failed to update marketplace listing', error: error.message });
  }
};

// Delete a marketplace listing
export const deleteListing = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { id } = req.params;

    const listing = await MarketplaceListing.findByPk(id, { transaction });

    if (!listing) {
      await transaction.rollback();
      return res.status(404).json({ message: 'Marketplace listing not found' });
    }

    await listing.destroy({ transaction });
    await transaction.commit();

    res.status(200).json({ message: 'Marketplace listing deleted successfully' });
  } catch (error) {
    await transaction.rollback();
    console.error('Error deleting marketplace listing:', error);
    res.status(500).json({ message: 'Failed to delete marketplace listing', error: error.message });
  }
};

// Toggle favorite status for a listing
export const toggleFavorite = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { id } = req.params;
    const { userId, isFavorite } = req.body;

    if (!userId) {
      await transaction.rollback();
      return res.status(400).json({ message: 'User ID is required' });
    }

    const listing = await MarketplaceListing.findByPk(id, { transaction });

    if (!listing) {
      await transaction.rollback();
      return res.status(404).json({ message: 'Marketplace listing not found' });
    }

    // Update favorites count
    if (isFavorite) {
      listing.favorites += 1;
    } else {
      listing.favorites = Math.max(0, listing.favorites - 1);
    }

    await listing.save({ transaction });

    // In a real implementation, you would also update a user_favorites table
    // to track which users have favorited which listings

    await transaction.commit();

    res.status(200).json({
      message: isFavorite ? 'Listing added to favorites' : 'Listing removed from favorites',
      favorites: listing.favorites
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error toggling favorite status:', error);
    res.status(500).json({ message: 'Failed to toggle favorite status', error: error.message });
  }
};

// Get marketplace categories and subcategories
export const getCategories = async (req, res) => {
  try {
    // In a real implementation, this would be fetched from a database table
    // For now, we'll return a static list of categories
    const categories = [
      {
        id: 'crops',
        name: 'Crops',
        subcategories: [
          { id: 'grains', name: 'Grains' },
          { id: 'fruits', name: 'Fruits' },
          { id: 'vegetables', name: 'Vegetables' },
          { id: 'nuts', name: 'Nuts' },
          { id: 'seeds', name: 'Seeds' }
        ]
      },
      {
        id: 'livestock',
        name: 'Livestock',
        subcategories: [
          { id: 'cattle', name: 'Cattle' },
          { id: 'poultry', name: 'Poultry' },
          { id: 'swine', name: 'Swine' },
          { id: 'sheep', name: 'Sheep' },
          { id: 'goats', name: 'Goats' }
        ]
      },
      {
        id: 'equipment',
        name: 'Equipment',
        subcategories: [
          { id: 'tractors', name: 'Tractors' },
          { id: 'harvesters', name: 'Harvesters' },
          { id: 'implements', name: 'Implements' },
          { id: 'irrigation', name: 'Irrigation' },
          { id: 'tools', name: 'Tools' }
        ]
      },
      {
        id: 'services',
        name: 'Services',
        subcategories: [
          { id: 'custom-farming', name: 'Custom Farming' },
          { id: 'consulting', name: 'Consulting' },
          { id: 'transportation', name: 'Transportation' },
          { id: 'veterinary', name: 'Veterinary' },
          { id: 'maintenance', name: 'Maintenance' }
        ]
      },
      {
        id: 'land',
        name: 'Land',
        subcategories: [
          { id: 'farmland', name: 'Farmland' },
          { id: 'pasture', name: 'Pasture' },
          { id: 'orchard', name: 'Orchard' },
          { id: 'vineyard', name: 'Vineyard' },
          { id: 'woodland', name: 'Woodland' }
        ]
      }
    ];

    res.status(200).json(categories);
  } catch (error) {
    console.error('Error fetching marketplace categories:', error);
    res.status(500).json({ message: 'Failed to fetch marketplace categories', error: error.message });
  }
};