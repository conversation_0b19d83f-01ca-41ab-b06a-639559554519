import PasswordGroup from '../models/PasswordGroup.js';
import PasswordGroupPermission from '../models/PasswordGroupPermission.js';
import Password from '../models/Password.js';
import { Op } from 'sequelize';
import { sequelize } from '../config/database.js';

/**
 * Controller for managing password groups
 */
class PasswordGroupController {
  /**
   * Get all password groups for a farm that the user has access to
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async getPasswordGroups(req, res) {
    try {
      const { farmId } = req.params;
      const userId = req.user.id;
      
      // Check if user has access to the farm
      const userFarm = await req.user.getUserFarms({
        where: { farm_id: farmId }
      });
      
      if (!userFarm || userFarm.length === 0) {
        return res.status(403).json({
          success: false,
          message: 'You do not have access to this farm'
        });
      }
      
      // Get user's roles for this farm
      const userRoles = userFarm.map(uf => uf.role_id);
      
      // Find password groups the user has access to through roles or direct permissions
      const passwordGroups = await PasswordGroup.findAll({
        where: { farm_id: farmId },
        include: [
          {
            model: PasswordGroupPermission,
            as: 'permissions',
            required: true,
            where: {
              [Op.or]: [
                { user_id: userId },
                { role_id: { [Op.in]: userRoles } }
              ]
            }
          }
        ]
      });
      
      return res.status(200).json({
        success: true,
        data: passwordGroups
      });
    } catch (error) {
      console.error('Error getting password groups:', error);
      return res.status(500).json({
        success: false,
        message: 'An error occurred while getting password groups',
        error: error.message
      });
    }
  }
  
  /**
   * Get a specific password group by ID
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async getPasswordGroup(req, res) {
    try {
      const { id } = req.params;
      const userId = req.user.id;
      
      // Find the password group
      const passwordGroup = await PasswordGroup.findByPk(id, {
        include: [
          {
            model: PasswordGroupPermission,
            as: 'permissions'
          }
        ]
      });
      
      if (!passwordGroup) {
        return res.status(404).json({
          success: false,
          message: 'Password group not found'
        });
      }
      
      // Check if user has access to the farm
      const userFarm = await req.user.getUserFarms({
        where: { farm_id: passwordGroup.farm_id }
      });
      
      if (!userFarm || userFarm.length === 0) {
        return res.status(403).json({
          success: false,
          message: 'You do not have access to this farm'
        });
      }
      
      // Get user's roles for this farm
      const userRoles = userFarm.map(uf => uf.role_id);
      
      // Check if user has permission to access this group
      const hasPermission = await PasswordGroupPermission.findOne({
        where: {
          group_id: id,
          [Op.or]: [
            { user_id: userId },
            { role_id: { [Op.in]: userRoles } }
          ]
        }
      });
      
      if (!hasPermission) {
        return res.status(403).json({
          success: false,
          message: 'You do not have permission to access this password group'
        });
      }
      
      return res.status(200).json({
        success: true,
        data: passwordGroup
      });
    } catch (error) {
      console.error('Error getting password group:', error);
      return res.status(500).json({
        success: false,
        message: 'An error occurred while getting the password group',
        error: error.message
      });
    }
  }
  
  /**
   * Create a new password group
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async createPasswordGroup(req, res) {
    const transaction = await sequelize.transaction();
    
    try {
      const { farmId } = req.params;
      const { name, description } = req.body;
      const userId = req.user.id;
      
      // Check if user has access to the farm
      const userFarm = await req.user.getUserFarms({
        where: { farm_id: farmId }
      });
      
      if (!userFarm || userFarm.length === 0) {
        await transaction.rollback();
        return res.status(403).json({
          success: false,
          message: 'You do not have access to this farm'
        });
      }
      
      // Create the password group
      const passwordGroup = await PasswordGroup.create({
        farm_id: farmId,
        name,
        description,
        created_by: userId
      }, { transaction });
      
      // Create a permission for the creator (manage permission)
      await PasswordGroupPermission.create({
        group_id: passwordGroup.id,
        user_id: userId,
        permission_type: 'manage'
      }, { transaction });
      
      await transaction.commit();
      
      return res.status(201).json({
        success: true,
        data: passwordGroup,
        message: 'Password group created successfully'
      });
    } catch (error) {
      await transaction.rollback();
      console.error('Error creating password group:', error);
      return res.status(500).json({
        success: false,
        message: 'An error occurred while creating the password group',
        error: error.message
      });
    }
  }
  
  /**
   * Update a password group
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async updatePasswordGroup(req, res) {
    try {
      const { id } = req.params;
      const { name, description } = req.body;
      const userId = req.user.id;
      
      // Find the password group
      const passwordGroup = await PasswordGroup.findByPk(id);
      
      if (!passwordGroup) {
        return res.status(404).json({
          success: false,
          message: 'Password group not found'
        });
      }
      
      // Check if user has access to the farm
      const userFarm = await req.user.getUserFarms({
        where: { farm_id: passwordGroup.farm_id }
      });
      
      if (!userFarm || userFarm.length === 0) {
        return res.status(403).json({
          success: false,
          message: 'You do not have access to this farm'
        });
      }
      
      // Check if user has manage permission for this group
      const hasManagePermission = await PasswordGroupPermission.findOne({
        where: {
          group_id: id,
          user_id: userId,
          permission_type: 'manage'
        }
      });
      
      if (!hasManagePermission) {
        return res.status(403).json({
          success: false,
          message: 'You do not have permission to update this password group'
        });
      }
      
      // Update the password group
      await passwordGroup.update({
        name: name || passwordGroup.name,
        description: description !== undefined ? description : passwordGroup.description
      });
      
      return res.status(200).json({
        success: true,
        data: passwordGroup,
        message: 'Password group updated successfully'
      });
    } catch (error) {
      console.error('Error updating password group:', error);
      return res.status(500).json({
        success: false,
        message: 'An error occurred while updating the password group',
        error: error.message
      });
    }
  }
  
  /**
   * Delete a password group
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async deletePasswordGroup(req, res) {
    const transaction = await sequelize.transaction();
    
    try {
      const { id } = req.params;
      const userId = req.user.id;
      
      // Find the password group
      const passwordGroup = await PasswordGroup.findByPk(id);
      
      if (!passwordGroup) {
        await transaction.rollback();
        return res.status(404).json({
          success: false,
          message: 'Password group not found'
        });
      }
      
      // Check if user has access to the farm
      const userFarm = await req.user.getUserFarms({
        where: { farm_id: passwordGroup.farm_id }
      });
      
      if (!userFarm || userFarm.length === 0) {
        await transaction.rollback();
        return res.status(403).json({
          success: false,
          message: 'You do not have access to this farm'
        });
      }
      
      // Check if user has manage permission for this group
      const hasManagePermission = await PasswordGroupPermission.findOne({
        where: {
          group_id: id,
          user_id: userId,
          permission_type: 'manage'
        }
      });
      
      if (!hasManagePermission) {
        await transaction.rollback();
        return res.status(403).json({
          success: false,
          message: 'You do not have permission to delete this password group'
        });
      }
      
      // Delete all passwords in this group
      await Password.destroy({
        where: { group_id: id },
        transaction
      });
      
      // Delete all permissions for this group
      await PasswordGroupPermission.destroy({
        where: { group_id: id },
        transaction
      });
      
      // Delete the password group
      await passwordGroup.destroy({ transaction });
      
      await transaction.commit();
      
      return res.status(200).json({
        success: true,
        message: 'Password group deleted successfully'
      });
    } catch (error) {
      await transaction.rollback();
      console.error('Error deleting password group:', error);
      return res.status(500).json({
        success: false,
        message: 'An error occurred while deleting the password group',
        error: error.message
      });
    }
  }
}

export default new PasswordGroupController();