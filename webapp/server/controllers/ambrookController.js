import axios from 'axios';
import dotenv from 'dotenv';
import NodeCache from 'node-cache';
import AmbrookGrant from '../models/AmbrookGrant.js';
import AmbrookLoan from '../models/AmbrookLoan.js';
import AmbrookReport from '../models/AmbrookReport.js';

dotenv.config();

// Ambrook API configuration
const AMBROOK_API_URL = process.env.AMBROOK_API_URL || 'https://api.ambrook.com/v1';
const AMBROOK_API_KEY = process.env.AMBROOK_API_KEY;

// Configure Ambrook API client
const ambrookClient = axios.create({
  baseURL: AMBROOK_API_URL,
  headers: {
    'Authorization': `Bearer ${AMBROOK_API_KEY}`,
    'Content-Type': 'application/json'
  }
});

// Initialize cache with standard TTL of 1 hour (3600 seconds) and check period of 120 seconds
const cache = new NodeCache({ stdTTL: 3600, checkperiod: 120 });

/**
 * Get available grants from Ambrook
 */
export const getAvailableGrants = async (req, res) => {
  try {
    // Create a cache key
    const cacheKey = 'ambrook_grants';

    // Check if data exists in cache
    const cachedData = cache.get(cacheKey);
    if (cachedData) {
      console.log('Using cached Ambrook grants data');
      return res.status(200).json({ grants: cachedData });
    }

    // If not in cache, check if data exists in the database
    let grants = await AmbrookGrant.findAll({
      where: { status: 'open' },
      order: [['deadline', 'ASC']]
    });

    if (grants && grants.length > 0) {
      console.log('Using Ambrook grants data from database');
      const grantsData = grants.map(grant => grant.toJSON());

      // Store in cache
      cache.set(cacheKey, grantsData);

      return res.status(200).json({ grants: grantsData });
    }

    // If not in database, try to fetch from the Ambrook API
    if (AMBROOK_API_KEY) {
      try {
        console.log('Fetching grants from Ambrook API');
        const response = await ambrookClient.get('/grants', {
          params: { status: 'open' }
        });

        if (response.data && response.data.grants) {
          // Store the successful API response in the database
          for (const grant of response.data.grants) {
            await AmbrookGrant.upsert({
              id: grant.id,
              name: grant.name,
              description: grant.description,
              amount: grant.amount,
              deadline: grant.deadline,
              eligibility: grant.eligibility,
              status: grant.status,
              category: grant.category,
              url: grant.url
            });
          }

          // Store in cache
          cache.set(cacheKey, response.data.grants);

          return res.status(200).json({ grants: response.data.grants });
        }
      } catch (apiError) {
        console.error('Error fetching from Ambrook API:', apiError);
        // Continue to fallback data if API call fails
      }
    }

    // If API call fails or no API key, return nothing to the user
    console.log('No Ambrook grants data available');
    return res.status(404).json({ 
      error: 'No grants available', 
      message: 'Could not retrieve grants data from the Ambrook API'
    });
  } catch (error) {
    console.error('Error getting Ambrook grants:', error);
    return res.status(500).json({ error: 'Failed to fetch grants from Ambrook' });
  }
};

/**
 * Get available loans from Ambrook
 */
export const getAvailableLoans = async (req, res) => {
  try {
    // Create a cache key
    const cacheKey = 'ambrook_loans';

    // Check if data exists in cache
    const cachedData = cache.get(cacheKey);
    if (cachedData) {
      console.log('Using cached Ambrook loans data');
      return res.status(200).json({ loans: cachedData });
    }

    // If not in cache, check if data exists in the database
    let loans = await AmbrookLoan.findAll({
      where: { status: 'available' },
      order: [['name', 'ASC']]
    });

    if (loans && loans.length > 0) {
      console.log('Using Ambrook loans data from database');
      const loansData = loans.map(loan => loan.toJSON());

      // Store in cache
      cache.set(cacheKey, loansData);

      return res.status(200).json({ loans: loansData });
    }

    // If not in database, try to fetch from the Ambrook API
    if (AMBROOK_API_KEY) {
      try {
        console.log('Fetching loans from Ambrook API');
        const response = await ambrookClient.get('/loans', {
          params: { status: 'available' }
        });

        if (response.data && response.data.loans) {
          // Store the successful API response in the database
          for (const loan of response.data.loans) {
            await AmbrookLoan.upsert({
              id: loan.id,
              name: loan.name,
              description: loan.description,
              provider: loan.provider,
              interestRate: loan.interestRate,
              term: loan.term,
              eligibility: loan.eligibility,
              status: loan.status,
              category: loan.category,
              url: loan.url
            });
          }

          // Store in cache
          cache.set(cacheKey, response.data.loans);

          return res.status(200).json({ loans: response.data.loans });
        }
      } catch (apiError) {
        console.error('Error fetching from Ambrook API:', apiError);
        // Continue to fallback data if API call fails
      }
    }

    // If API call fails or no API key, return nothing to the user
    console.log('No Ambrook loans data available');
    return res.status(404).json({ 
      error: 'No loans available', 
      message: 'Could not retrieve loans data from the Ambrook API'
    });
  } catch (error) {
    console.error('Error getting Ambrook loans:', error);
    return res.status(500).json({ error: 'Failed to fetch loans from Ambrook' });
  }
};

/**
 * Get financial reports from Ambrook
 */
export const getFinancialReports = async (req, res) => {
  try {
    // Create a cache key
    const cacheKey = 'ambrook_reports';

    // Check if data exists in cache
    const cachedData = cache.get(cacheKey);
    if (cachedData) {
      console.log('Using cached Ambrook reports data');
      return res.status(200).json({ reports: cachedData });
    }

    // If not in cache, check if data exists in the database
    let reports = await AmbrookReport.findAll({
      order: [['date', 'DESC']]
    });

    if (reports && reports.length > 0) {
      console.log('Using Ambrook reports data from database');
      const reportsData = reports.map(report => report.toJSON());

      // Store in cache
      cache.set(cacheKey, reportsData);

      return res.status(200).json({ reports: reportsData });
    }

    // If not in database, try to fetch from the Ambrook API
    if (AMBROOK_API_KEY) {
      try {
        console.log('Fetching reports from Ambrook API');
        const response = await ambrookClient.get('/reports');

        if (response.data && response.data.reports) {
          // Store the successful API response in the database
          for (const report of response.data.reports) {
            await AmbrookReport.upsert({
              id: report.id,
              name: report.name,
              description: report.description,
              date: report.date,
              type: report.type,
              url: report.url
            });
          }

          // Store in cache
          cache.set(cacheKey, response.data.reports);

          return res.status(200).json({ reports: response.data.reports });
        }
      } catch (apiError) {
        console.error('Error fetching from Ambrook API:', apiError);
        // Continue to fallback data if API call fails
      }
    }

    // If API call fails or no API key, return nothing to the user
    console.log('No Ambrook financial reports data available');
    return res.status(404).json({ 
      error: 'No financial reports available', 
      message: 'Could not retrieve financial reports data from the Ambrook API'
    });
  } catch (error) {
    console.error('Error getting Ambrook financial reports:', error);
    return res.status(500).json({ error: 'Failed to fetch financial reports from Ambrook' });
  }
};

/**
 * Apply for a grant through Ambrook
 */
export const applyForGrant = async (req, res) => {
  try {
    const { grantId } = req.params;
    const applicationData = req.body;

    // Validate input
    if (!grantId) {
      return res.status(400).json({ error: 'Grant ID is required' });
    }

    if (!applicationData) {
      return res.status(400).json({ error: 'Application data is required' });
    }

    // Check if the grant exists in our database
    const grant = await AmbrookGrant.findByPk(grantId);
    if (!grant) {
      return res.status(404).json({ error: 'Grant not found' });
    }

    // If we have an API key, try to submit the application to the Ambrook API
    if (AMBROOK_API_KEY) {
      try {
        console.log('Submitting grant application to Ambrook API');
        const response = await ambrookClient.post(`/grants/${grantId}/apply`, applicationData);

        if (response.data) {
          return res.status(200).json(response.data);
        }
      } catch (apiError) {
        console.error('Error submitting to Ambrook API:', apiError);
        // Continue to fallback if API call fails
      }
    }

    // If API call fails or no API key, return nothing to the user
    console.log('Could not submit grant application to Ambrook API');
    return res.status(404).json({ 
      error: 'Grant application failed', 
      message: 'Could not submit grant application to the Ambrook API'
    });
  } catch (error) {
    console.error('Error applying for Ambrook grant:', error);
    return res.status(500).json({ error: 'Failed to apply for grant through Ambrook' });
  }
};

/**
 * Apply for a loan through Ambrook
 */
export const applyForLoan = async (req, res) => {
  try {
    const { loanId } = req.params;
    const applicationData = req.body;

    // Validate input
    if (!loanId) {
      return res.status(400).json({ error: 'Loan ID is required' });
    }

    if (!applicationData) {
      return res.status(400).json({ error: 'Application data is required' });
    }

    // Check if the loan exists in our database
    const loan = await AmbrookLoan.findByPk(loanId);
    if (!loan) {
      return res.status(404).json({ error: 'Loan not found' });
    }

    // If we have an API key, try to submit the application to the Ambrook API
    if (AMBROOK_API_KEY) {
      try {
        console.log('Submitting loan application to Ambrook API');
        const response = await ambrookClient.post(`/loans/${loanId}/apply`, applicationData);

        if (response.data) {
          return res.status(200).json(response.data);
        }
      } catch (apiError) {
        console.error('Error submitting to Ambrook API:', apiError);
        // Continue to fallback if API call fails
      }
    }

    // If API call fails or no API key, return nothing to the user
    console.log('Could not submit loan application to Ambrook API');
    return res.status(404).json({ 
      error: 'Loan application failed', 
      message: 'Could not submit loan application to the Ambrook API'
    });
  } catch (error) {
    console.error('Error applying for Ambrook loan:', error);
    return res.status(500).json({ error: 'Failed to apply for loan through Ambrook' });
  }
};

/**
 * Sync farm financial data with Ambrook
 */
export const syncFarmData = async (req, res) => {
  try {
    const { farmId } = req.body;

    // Validate input
    if (!farmId) {
      return res.status(400).json({ error: 'Farm ID is required' });
    }

    // If we have an API key, try to sync data with the Ambrook API
    if (AMBROOK_API_KEY) {
      try {
        console.log('Syncing farm data with Ambrook API');
        const response = await ambrookClient.post('/farms/sync', { farmId });

        if (response.data) {
          // Clear cache for this farm's data to ensure fresh data on next request
          cache.del('ambrook_grants');
          cache.del('ambrook_loans');
          cache.del('ambrook_reports');

          return res.status(200).json(response.data);
        }
      } catch (apiError) {
        console.error('Error syncing with Ambrook API:', apiError);
        // Continue to fallback if API call fails
      }
    }

    // If API call fails or no API key, return nothing to the user
    console.log('Could not sync farm data with Ambrook API');
    return res.status(404).json({ 
      error: 'Farm data sync failed', 
      message: 'Could not sync farm data with the Ambrook API'
    });
  } catch (error) {
    console.error('Error syncing farm data with Ambrook:', error);
    return res.status(500).json({ error: 'Failed to sync farm data with Ambrook' });
  }
};
