import Role from '../models/Role.js';
import RolePermission from '../models/RolePermission.js';
import User from '../models/User.js';
import { sequelize } from '../config/database.js';

/**
 * Get all global roles (for global admins)
 * @route GET /api/roles/global
 * @returns {Array} roles - List of global roles
 */
export const getGlobalRoles = async (req, res) => {
  try {
    // Check if user is a global admin
    const userId = req.user.id;
    const user = await User.findByPk(userId);

    if (!user || !user.is_global_admin) {
      return res.status(403).json({ 
        error: 'Unauthorized. Only global admins can access global roles.'
      });
    }

    const roles = await Role.findAll({
      where: {
        farm_id: null
      },
      include: [
        {
          model: RolePermission,
          as: 'permissions'
        }
      ],
      order: [['name', 'ASC']]
    });

    return res.status(200).json(roles);
  } catch (error) {
    console.error('Error getting global roles:', error);
    return res.status(500).json({ 
      error: 'Server error getting global roles'
    });
  }
};

/**
 * Get all farm roles
 * @route GET /api/roles/farm/:farmId
 * @param {string} farmId - The farm ID
 * @returns {Array} roles - List of farm roles
 */
export const getFarmRoles = async (req, res) => {
  try {
    const { farmId } = req.params;

    // Validate required parameters
    if (!farmId) {
      return res.status(400).json({ 
        error: 'Missing required parameters'
      });
    }

    const roles = await Role.findAll({
      where: {
        farm_id: farmId
      },
      include: [
        {
          model: RolePermission,
          as: 'permissions'
        }
      ],
      order: [['name', 'ASC']]
    });

    return res.status(200).json(roles);
  } catch (error) {
    console.error('Error getting farm roles:', error);
    return res.status(500).json({ 
      error: 'Server error getting farm roles'
    });
  }
};

/**
 * Get a role by ID
 * @route GET /api/roles/:id
 * @param {string} id - The role ID
 * @returns {Object} role - The role
 */
export const getRoleById = async (req, res) => {
  try {
    const { id } = req.params;

    // Validate required parameters
    if (!id) {
      return res.status(400).json({ 
        error: 'Missing required parameters'
      });
    }

    const role = await Role.findByPk(id, {
      include: [
        {
          model: RolePermission,
          as: 'permissions'
        }
      ]
    });

    if (!role) {
      return res.status(404).json({ 
        error: 'Role not found'
      });
    }

    return res.status(200).json(role);
  } catch (error) {
    console.error('Error getting role:', error);
    return res.status(500).json({ 
      error: 'Server error getting role'
    });
  }
};

/**
 * Create a new global role (for global admins)
 * @route POST /api/roles/global
 * @param {string} name - The role name
 * @param {string} description - The role description
 * @returns {Object} role - The created role
 */
export const createGlobalRole = async (req, res) => {
  try {
    const { name, description } = req.body;

    // Check if user is a global admin
    const userId = req.user.id;
    const user = await User.findByPk(userId);

    if (!user || !user.is_global_admin) {
      return res.status(403).json({ 
        error: 'Unauthorized. Only global admins can create global roles.'
      });
    }

    // Validate required parameters
    if (!name) {
      return res.status(400).json({ 
        error: 'Missing required parameters'
      });
    }

    // Check if role with this name already exists
    const existingRole = await Role.findOne({
      where: {
        farm_id: null,
        name
      }
    });

    if (existingRole) {
      return res.status(400).json({ 
        error: 'A global role with this name already exists'
      });
    }

    // Create the role
    const role = await Role.create({
      farm_id: null,
      name,
      description,
      is_system_role: false
    });

    return res.status(201).json(role);
  } catch (error) {
    console.error('Error creating global role:', error);
    return res.status(500).json({ 
      error: 'Server error creating global role'
    });
  }
};

/**
 * Create a new farm role
 * @route POST /api/roles/farm/:farmId
 * @param {string} farmId - The farm ID
 * @param {string} name - The role name
 * @param {string} description - The role description
 * @returns {Object} role - The created role
 */
export const createFarmRole = async (req, res) => {
  try {
    const { farmId } = req.params;
    const { name, description } = req.body;

    // Validate required parameters
    if (!farmId || !name) {
      return res.status(400).json({ 
        error: 'Missing required parameters'
      });
    }

    // Check if role with this name already exists for this farm
    const existingRole = await Role.findOne({
      where: {
        farm_id: farmId,
        name
      }
    });

    if (existingRole) {
      return res.status(400).json({ 
        error: 'A role with this name already exists for this farm'
      });
    }

    // Create the role
    const role = await Role.create({
      farm_id: farmId,
      name,
      description,
      is_system_role: false
    });

    return res.status(201).json(role);
  } catch (error) {
    console.error('Error creating farm role:', error);
    return res.status(500).json({ 
      error: 'Server error creating farm role'
    });
  }
};

/**
 * Update a role
 * @route PUT /api/roles/:id
 * @param {string} id - The role ID
 * @param {string} name - The role name
 * @param {string} description - The role description
 * @returns {Object} role - The updated role
 */
export const updateRole = async (req, res) => {
  try {
    const { id } = req.params;
    const { name, description } = req.body;

    // Validate required parameters
    if (!id || !name) {
      return res.status(400).json({ 
        error: 'Missing required parameters'
      });
    }

    // Find the role
    const role = await Role.findByPk(id);

    if (!role) {
      return res.status(404).json({ 
        error: 'Role not found'
      });
    }

    // Check if this is a system role
    if (role.is_system_role) {
      return res.status(403).json({ 
        error: 'System roles cannot be modified'
      });
    }

    // Check if user has permission to modify this role
    if (role.farm_id === null) {
      // Global role - only global admins can modify
      const userId = req.user.id;
      const user = await User.findByPk(userId);

      if (!user || !user.is_global_admin) {
        return res.status(403).json({ 
          error: 'Unauthorized. Only global admins can modify global roles.'
        });
      }
    } else {
      // Farm role - check if user has admin access to the farm
      // This would typically be handled by a middleware that checks farm access
    }

    // Check if another role with this name already exists
    const existingRole = await Role.findOne({
      where: {
        id: { [sequelize.Op.ne]: id }, // Not this role
        farm_id: role.farm_id,
        name
      }
    });

    if (existingRole) {
      return res.status(400).json({ 
        error: role.farm_id ? 
          'A role with this name already exists for this farm' : 
          'A global role with this name already exists'
      });
    }

    // Update the role
    role.name = name;
    role.description = description;
    await role.save();

    return res.status(200).json(role);
  } catch (error) {
    console.error('Error updating role:', error);
    return res.status(500).json({ 
      error: 'Server error updating role'
    });
  }
};

/**
 * Delete a role
 * @route DELETE /api/roles/:id
 * @param {string} id - The role ID
 * @returns {Object} message - Success message
 */
export const deleteRole = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { id } = req.params;

    // Validate required parameters
    if (!id) {
      await transaction.rollback();
      return res.status(400).json({ 
        error: 'Missing required parameters'
      });
    }

    // Find the role
    const role = await Role.findByPk(id);

    if (!role) {
      await transaction.rollback();
      return res.status(404).json({ 
        error: 'Role not found'
      });
    }

    // Check if this is a system role
    if (role.is_system_role) {
      await transaction.rollback();
      return res.status(403).json({ 
        error: 'System roles cannot be deleted'
      });
    }

    // Check if user has permission to delete this role
    if (role.farm_id === null) {
      // Global role - only global admins can delete
      const userId = req.user.id;
      const user = await User.findByPk(userId);

      if (!user || !user.is_global_admin) {
        await transaction.rollback();
        return res.status(403).json({ 
          error: 'Unauthorized. Only global admins can delete global roles.'
        });
      }
    } else {
      // Farm role - check if user has admin access to the farm
      // This would typically be handled by a middleware that checks farm access
    }

    // Delete all permissions for this role
    await RolePermission.destroy({
      where: {
        role_id: id
      },
      transaction
    });

    // Delete the role
    await role.destroy({ transaction });

    await transaction.commit();
    return res.status(200).json({ 
      message: 'Role deleted successfully'
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error deleting role:', error);
    return res.status(500).json({ 
      error: 'Server error deleting role'
    });
  }
};
