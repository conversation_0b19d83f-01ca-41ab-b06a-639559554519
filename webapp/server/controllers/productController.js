import Product from '../models/Product.js';
import Crop from '../models/Crop.js';
import Livestock from '../models/Livestock.js';
import Equipment from '../models/Equipment.js';
import { sequelize } from '../config/database.js';

// Get all products for a farm using query parameter
export const getFarmProductsByQuery = async (req, res) => {
  try {
    const { farmId } = req.query;

    if (!farmId) {
      return res.status(400).json({ 
        message: "Farm ID is required",
        errorType: "ValidationError",
        errorCode: "400",
        context: {
          url: req.originalUrl,
          method: req.method
        }
      });
    }

    // Set the schema options to include farmId
    const products = await Product.findAll({
      where: { farm_id: farmId },
      order: [['name', 'ASC']],
      // Pass farmId to ensure correct schema is used
      schemaOptions: { farmId }
    });

    res.status(200).json(products);
  } catch (error) {
    console.error('Error fetching farm products by query:', error);
    res.status(500).json({ 
      message: "Failed to fetch products",
      errorType: "ServerError",
      errorCode: "500",
      context: {
        url: req.originalUrl,
        method: req.method,
        farmId: req.query.farmId
      }
    });
  }
};

// Get all products for a farm
export const getFarmProducts = async (req, res) => {
  try {
    const { farmId } = req.params;

    if (!farmId) {
      return res.status(400).json({ 
        message: "Farm ID is required",
        errorType: "ValidationError",
        errorCode: "400",
        context: {
          url: req.originalUrl,
          method: req.method
        }
      });
    }

    // Set the schema options to include farmId
    const products = await Product.findAll({
      where: { farm_id: farmId },
      order: [['name', 'ASC']],
      // Pass farmId to ensure correct schema is used
      schemaOptions: { farmId }
    });

    res.status(200).json(products);
  } catch (error) {
    console.error('Error fetching farm products:', error);
    res.status(500).json({ 
      message: "Failed to fetch products",
      errorType: "ServerError",
      errorCode: "500",
      context: {
        url: req.originalUrl,
        method: req.method,
        farmId: req.params.farmId
      }
    });
  }
};

// Get a single product by ID
export const getProductById = async (req, res) => {
  try {
    const { productId } = req.params;

    // Get the product with the farm_id to use for schema
    const product = await Product.findByPk(productId);

    if (!product) {
      return res.status(404).json({ 
        message: "Product not found",
        errorType: "NotFoundError",
        errorCode: "404",
        context: {
          url: req.originalUrl,
          method: req.method,
          productId
        }
      });
    }

    // Now fetch the product again with the correct schema
    const productWithSchema = await Product.findByPk(productId, {
      schemaOptions: { farmId: product.farm_id }
    });

    res.status(200).json(productWithSchema);
  } catch (error) {
    console.error('Error fetching product:', error);
    res.status(500).json({ 
      message: "Failed to fetch product",
      errorType: "ServerError",
      errorCode: "500",
      context: {
        url: req.originalUrl,
        method: req.method,
        productId: req.params.productId
      }
    });
  }
};

// Create a new product
export const createProduct = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { 
      farm_id, 
      name, 
      description, 
      sku, 
      price, 
      cost, 
      unit, 
      category, 
      type, 
      source_id,
      is_active,
      is_tax_exempt
    } = req.body;

    // Validate required fields
    if (!farm_id || !name || !price) {
      await transaction.rollback();
      return res.status(400).json({ 
        message: "Missing required fields",
        errorType: "ValidationError",
        errorCode: "400",
        context: {
          url: req.originalUrl,
          method: req.method,
          missingFields: !farm_id ? 'farm_id' : !name ? 'name' : 'price'
        }
      });
    }

    // Validate source_id if type is specified
    if (type && type !== 'other' && source_id) {
      let sourceExists = false;

      if (type === 'crop') {
        const crop = await Crop.findByPk(source_id, {
          schemaOptions: { farmId: farm_id }
        });
        sourceExists = !!crop;
      } else if (type === 'livestock') {
        const livestock = await Livestock.findByPk(source_id, {
          schemaOptions: { farmId: farm_id }
        });
        sourceExists = !!livestock;
      } else if (type === 'equipment') {
        const equipment = await Equipment.findByPk(source_id, {
          schemaOptions: { farmId: farm_id }
        });
        sourceExists = !!equipment;
      }

      if (!sourceExists) {
        await transaction.rollback();
        return res.status(400).json({ 
          message: `${type} with ID ${source_id} not found`,
          errorType: "ValidationError",
          errorCode: "400",
          context: {
            url: req.originalUrl,
            method: req.method,
            type,
            source_id
          }
        });
      }
    }

    const product = await Product.create({
      farm_id,
      name,
      description,
      sku,
      price,
      cost,
      unit,
      category,
      type: type || 'other',
      source_id,
      is_active: is_active !== undefined ? is_active : true,
      is_tax_exempt: is_tax_exempt !== undefined ? is_tax_exempt : false
    }, { 
      transaction,
      schemaOptions: { farmId: farm_id }
    });

    await transaction.commit();
    res.status(201).json(product);
  } catch (error) {
    await transaction.rollback();
    console.error('Error creating product:', error);
    res.status(500).json({ 
      message: "Failed to create product",
      errorType: "ServerError",
      errorCode: "500",
      context: {
        url: req.originalUrl,
        method: req.method,
        farm_id: req.body.farm_id
      }
    });
  }
};

// Update a product
export const updateProduct = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { productId } = req.params;
    const { 
      name, 
      description, 
      sku, 
      price, 
      cost, 
      unit, 
      category, 
      type, 
      source_id,
      is_active,
      is_tax_exempt
    } = req.body;

    // Get the product with the farm_id to use for schema
    const product = await Product.findByPk(productId);

    if (!product) {
      await transaction.rollback();
      return res.status(404).json({ 
        message: "Product not found",
        errorType: "NotFoundError",
        errorCode: "404",
        context: {
          url: req.originalUrl,
          method: req.method,
          productId
        }
      });
    }

    // Validate source_id if type is specified
    if (type && type !== 'other' && source_id) {
      let sourceExists = false;

      if (type === 'crop') {
        const crop = await Crop.findByPk(source_id, {
          schemaOptions: { farmId: product.farm_id }
        });
        sourceExists = !!crop;
      } else if (type === 'livestock') {
        const livestock = await Livestock.findByPk(source_id, {
          schemaOptions: { farmId: product.farm_id }
        });
        sourceExists = !!livestock;
      } else if (type === 'equipment') {
        const equipment = await Equipment.findByPk(source_id, {
          schemaOptions: { farmId: product.farm_id }
        });
        sourceExists = !!equipment;
      }

      if (!sourceExists) {
        await transaction.rollback();
        return res.status(400).json({ 
          message: `${type} with ID ${source_id} not found`,
          errorType: "ValidationError",
          errorCode: "400",
          context: {
            url: req.originalUrl,
            method: req.method,
            type,
            source_id
          }
        });
      }
    }

    await product.update({
      name: name || product.name,
      description: description !== undefined ? description : product.description,
      sku: sku !== undefined ? sku : product.sku,
      price: price !== undefined ? price : product.price,
      cost: cost !== undefined ? cost : product.cost,
      unit: unit !== undefined ? unit : product.unit,
      category: category !== undefined ? category : product.category,
      type: type || product.type,
      source_id: source_id !== undefined ? source_id : product.source_id,
      is_active: is_active !== undefined ? is_active : product.is_active,
      is_tax_exempt: is_tax_exempt !== undefined ? is_tax_exempt : product.is_tax_exempt
    }, { 
      transaction,
      schemaOptions: { farmId: product.farm_id }
    });

    await transaction.commit();
    res.status(200).json(product);
  } catch (error) {
    await transaction.rollback();
    console.error('Error updating product:', error);
    res.status(500).json({ 
      message: "Failed to update product",
      errorType: "ServerError",
      errorCode: "500",
      context: {
        url: req.originalUrl,
        method: req.method,
        productId: req.params.productId
      }
    });
  }
};

// Delete a product
export const deleteProduct = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { productId } = req.params;

    // Get the product with the farm_id to use for schema
    const product = await Product.findByPk(productId);

    if (!product) {
      await transaction.rollback();
      return res.status(404).json({ 
        message: "Product not found",
        errorType: "NotFoundError",
        errorCode: "404",
        context: {
          url: req.originalUrl,
          method: req.method,
          productId
        }
      });
    }

    // Now destroy the product with the correct schema
    await product.destroy({ 
      transaction,
      schemaOptions: { farmId: product.farm_id }
    });

    await transaction.commit();
    res.status(200).json({ message: 'Product deleted successfully' });
  } catch (error) {
    await transaction.rollback();
    console.error('Error deleting product:', error);
    res.status(500).json({ 
      message: "Failed to delete product",
      errorType: "ServerError",
      errorCode: "500",
      context: {
        url: req.originalUrl,
        method: req.method,
        productId: req.params.productId
      }
    });
  }
};

// Create a product from a crop
export const createProductFromCrop = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { cropId } = req.params;
    const { price, cost, unit, is_active, is_tax_exempt } = req.body;

    // Find the crop with the correct schema
    const crop = await Crop.findByPk(cropId);

    if (!crop) {
      await transaction.rollback();
      return res.status(404).json({ 
        message: "Crop not found",
        errorType: "NotFoundError",
        errorCode: "404",
        context: {
          url: req.originalUrl,
          method: req.method,
          cropId
        }
      });
    }

    // Create a product based on the crop with the correct schema
    const product = await Product.create({
      farm_id: crop.farm_id,
      name: crop.name,
      description: `${crop.name} ${crop.variety ? `- ${crop.variety}` : ''} (${crop.year || 'Unknown Year'})`,
      price: price || 0,
      cost: cost,
      unit: unit,
      category: 'Crops',
      type: 'crop',
      source_id: crop.id,
      is_active: is_active !== undefined ? is_active : true,
      is_tax_exempt: is_tax_exempt !== undefined ? is_tax_exempt : false
    }, { 
      transaction,
      schemaOptions: { farmId: crop.farm_id }
    });

    await transaction.commit();
    res.status(201).json(product);
  } catch (error) {
    await transaction.rollback();
    console.error('Error creating product from crop:', error);
    res.status(500).json({ 
      message: "Failed to create product from crop",
      errorType: "ServerError",
      errorCode: "500",
      context: {
        url: req.originalUrl,
        method: req.method,
        cropId: req.params.cropId
      }
    });
  }
};

// Create a product from livestock
export const createProductFromLivestock = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { livestockId } = req.params;
    const { price, cost, unit, is_active, is_tax_exempt } = req.body;

    // Find the livestock with the correct schema
    const livestock = await Livestock.findByPk(livestockId);

    if (!livestock) {
      await transaction.rollback();
      return res.status(404).json({ 
        message: "Livestock not found",
        errorType: "NotFoundError",
        errorCode: "404",
        context: {
          url: req.originalUrl,
          method: req.method,
          livestockId
        }
      });
    }

    // Create a product based on the livestock with the correct schema
    const product = await Product.create({
      farm_id: livestock.farm_id,
      name: `${livestock.type} ${livestock.breed ? `- ${livestock.breed}` : ''}`,
      description: `${livestock.type} ${livestock.breed ? `- ${livestock.breed}` : ''} (Quantity: ${livestock.quantity})`,
      price: price || 0,
      cost: cost || livestock.acquisition_cost,
      unit: unit,
      category: 'Livestock',
      type: 'livestock',
      source_id: livestock.id,
      is_active: is_active !== undefined ? is_active : true,
      is_tax_exempt: is_tax_exempt !== undefined ? is_tax_exempt : false
    }, { 
      transaction,
      schemaOptions: { farmId: livestock.farm_id }
    });

    await transaction.commit();
    res.status(201).json(product);
  } catch (error) {
    await transaction.rollback();
    console.error('Error creating product from livestock:', error);
    res.status(500).json({ 
      message: "Failed to create product from livestock",
      errorType: "ServerError",
      errorCode: "500",
      context: {
        url: req.originalUrl,
        method: req.method,
        livestockId: req.params.livestockId
      }
    });
  }
};

// Create a product from equipment
export const createProductFromEquipment = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { equipmentId } = req.params;
    const { price, cost, unit, is_active, is_tax_exempt } = req.body;

    // Find the equipment with the correct schema
    const equipment = await Equipment.findByPk(equipmentId);

    if (!equipment) {
      await transaction.rollback();
      return res.status(404).json({ 
        message: "Equipment not found",
        errorType: "NotFoundError",
        errorCode: "404",
        context: {
          url: req.originalUrl,
          method: req.method,
          equipmentId
        }
      });
    }

    // Create a product based on the equipment with the correct schema
    const product = await Product.create({
      farm_id: equipment.farm_id,
      name: equipment.name,
      description: `${equipment.name} ${equipment.manufacturer ? `- ${equipment.manufacturer}` : ''} ${equipment.model ? `(${equipment.model})` : ''}`,
      price: price || 0,
      cost: cost || equipment.purchase_cost,
      unit: unit,
      category: 'Equipment',
      type: 'equipment',
      source_id: equipment.id,
      is_active: is_active !== undefined ? is_active : true,
      is_tax_exempt: is_tax_exempt !== undefined ? is_tax_exempt : false
    }, { 
      transaction,
      schemaOptions: { farmId: equipment.farm_id }
    });

    await transaction.commit();
    res.status(201).json(product);
  } catch (error) {
    await transaction.rollback();
    console.error('Error creating product from equipment:', error);
    res.status(500).json({ 
      message: "Failed to create product from equipment",
      errorType: "ServerError",
      errorCode: "500",
      context: {
        url: req.originalUrl,
        method: req.method,
        equipmentId: req.params.equipmentId
      }
    });
  }
};
