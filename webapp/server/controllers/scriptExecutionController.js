import fs from 'fs';
import path from 'path';
import { promisify } from 'util';
import { exec } from 'child_process';
import { sequelize } from '../config/database.js';
import { Op } from 'sequelize';
import ScriptExecution from '../models/ScriptExecution.js';
import User from '../models/User.js';

const execPromise = promisify(exec);

/**
 * Get all script executions
 * Supports filtering by status: all, pending, executed
 */
export const getAllScriptExecutions = async (req, res) => {
  try {
    const { filter } = req.query;

    // Define where clause based on filter
    let whereClause = {};

    if (filter === 'pending') {
      whereClause = { 
        executed: false
      };
    } else if (filter === 'executed') {
      whereClause = { executed: true };
    }

    const scriptExecutions = await ScriptExecution.findAll({
      where: whereClause,
      include: [
        { model: User, as: 'executedBy', attributes: ['id', 'first_name', 'last_name', 'email'] }
      ],
      order: [['created_at', 'DESC']]
    });

    return res.status(200).json({
      success: true,
      scriptExecutions
    });
  } catch (error) {
    console.error('Error fetching script executions:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to fetch script executions'
    });
  }
};

/**
 * Get a specific script execution by ID
 */
export const getScriptExecution = async (req, res) => {
  try {
    const { scriptId } = req.params;

    const scriptExecution = await ScriptExecution.findByPk(scriptId, {
      include: [
        { model: User, as: 'executedBy', attributes: ['id', 'first_name', 'last_name', 'email'] }
      ]
    });

    if (!scriptExecution) {
      return res.status(404).json({
        success: false,
        error: 'Script execution not found'
      });
    }

    return res.status(200).json({
      success: true,
      scriptExecution
    });
  } catch (error) {
    console.error('Error fetching script execution:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to fetch script execution'
    });
  }
};

/**
 * Recursively find all JavaScript files in a directory
 * @param {string} dir - Directory to search
 * @param {Array} fileList - Array to store found files
 * @returns {Array} - Array of objects with name and path of JavaScript files
 */
const findJsFiles = (dir, fileList = []) => {
  try {
    // Check if directory exists
    if (!fs.existsSync(dir)) {
      console.error(`Directory does not exist: ${dir}`);
      return fileList;
    }

    console.log(`Scanning directory: ${dir}`);
    const files = fs.readdirSync(dir);

    files.forEach(file => {
      try {
        const filePath = path.join(dir, file);
        const stat = fs.statSync(filePath);

        if (stat.isDirectory()) {
          // Recursively search subdirectories and make sure to update the fileList
          console.log(`Found subdirectory: ${filePath}`);
          findJsFiles(filePath, fileList);
        } else if (file.endsWith('.js')) {
          // Add JavaScript files to the list
          console.log(`Found JavaScript file: ${filePath}`);
          fileList.push({
            name: file,
            filePath: filePath
          });
        }
      } catch (fileError) {
        console.error(`Error processing file ${file} in directory ${dir}:`, fileError);
        // Continue with next file
      }
    });

    return fileList;
  } catch (dirError) {
    console.error(`Error reading directory ${dir}:`, dirError);
    return fileList;
  }
};

/**
 * Scan for scripts in the server/scripts directory
 */
export const scanForScripts = async (req, res) => {
  try {
    const scriptsDir = path.join(process.cwd(), 'server', 'scripts');

    // Check if the scripts directory exists
    if (!fs.existsSync(scriptsDir)) {
      console.error(`Scripts directory does not exist: ${scriptsDir}`);
      return res.status(500).json({
        success: false,
        error: 'Failed to scan for scripts',
        details: `Scripts directory does not exist: ${scriptsDir}`
      });
    }

    console.log(`Scanning for JavaScript files in: ${scriptsDir}`);

    // Get all JavaScript files in the scripts directory
    const jsFiles = findJsFiles(scriptsDir);

    if (!jsFiles || jsFiles.length === 0) {
      console.log('No JavaScript files found in the scripts directory');
    } else {
      console.log(`Found ${jsFiles.length} JavaScript files`);
    }

    // Get existing script executions
    const existingScriptExecutions = await ScriptExecution.findAll();
    const existingPaths = existingScriptExecutions.map(s => s.file_path);
    const existingNames = existingScriptExecutions.map(s => s.name);

    // Find new scripts
    const newScripts = [];

    for (const jsFile of jsFiles) {
      try {
        const { name, filePath } = jsFile;

        // Skip if file path already in database
        if (existingPaths.includes(filePath)) {
          continue;
        }

        // Skip if name already in database (prevents duplicate entries for migrations)
        if (existingNames.includes(name)) {
          console.log(`Skipping ${name} as a script with this name already exists`);
          continue;
        }

        // Check if file exists before reading
        if (!fs.existsSync(filePath)) {
          console.error(`JavaScript file does not exist: ${filePath}`);
          continue;
        }

        // Read the first few lines to extract description
        const fileContent = fs.readFileSync(filePath, 'utf8');
        const lines = fileContent.split('\n').slice(0, 10);
        let description = '';

        // Look for comment lines that might contain a description
        for (const line of lines) {
          const trimmedLine = line.trim();
          if (trimmedLine.startsWith('//') || trimmedLine.startsWith('/*')) {
            const commentText = trimmedLine.replace(/^\/\/\s*|^\/\*|\*\/$/g, '').trim();
            if (commentText && description.length < 100) {
              description += (description ? ' ' : '') + commentText;
            }
          }
        }

        // Create new script execution record
        const newScriptExecution = await ScriptExecution.create({
          name: name,
          description: description || `JavaScript script: ${name}`,
          file_path: filePath,
          status: 'pending'
        });

        newScripts.push(newScriptExecution);
      } catch (fileError) {
        console.error(`Error processing JavaScript file:`, fileError);
        // Continue with next file
      }
    }

    return res.status(200).json({
      success: true,
      message: `Found ${newScripts.length} new scripts`,
      newScripts
    });
  } catch (error) {
    console.error('Error scanning for scripts:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to scan for scripts',
      details: error.message
    });
  }
};

/**
 * Execute a script
 */
export const executeScript = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { scriptId } = req.params;

    const scriptExecution = await ScriptExecution.findByPk(scriptId);

    if (!scriptExecution) {
      return res.status(404).json({
        success: false,
        error: 'Script execution not found'
      });
    }

    // Check if this script has already been executed by name
    const existingExecution = await ScriptExecution.findOne({
      where: {
        name: scriptExecution.name,
        executed: true,
        id: { [Op.ne]: scriptId } // Not the current script
      }
    });

    if (existingExecution) {
      console.log(`Script with name ${scriptExecution.name} has already been executed. Updating existing record.`);

      // Update the existing record with the current execution details
      await existingExecution.update({
        status: 'in_progress'
      }, { transaction });

      // Execute the script
      const filePath = scriptExecution.file_path;
      const command = `node ${filePath}`;

      console.log(`Executing script: ${command}`);
      const { stdout, stderr } = await execPromise(command);

      // Update the existing record with the execution results
      await existingExecution.update({
        executed_at: new Date(),
        executed_by: req.user.id,
        status: 'completed',
        output: stdout,
        error_message: stderr || null
      }, { transaction });

      // Delete the duplicate record
      await scriptExecution.destroy({ transaction });

      await transaction.commit();

      return res.status(200).json({
        success: true,
        message: 'Script executed successfully (updated existing record)',
        scriptExecution: existingExecution
      });
    }

    // If no existing execution found, proceed with normal execution
    // Update script execution status to in_progress
    await scriptExecution.update({
      status: 'in_progress'
    }, { transaction });

    // Execute the script
    const filePath = scriptExecution.file_path;
    const command = `node ${filePath}`;

    console.log(`Executing script: ${command}`);
    const { stdout, stderr } = await execPromise(command);

    // Update script execution status to completed
    await scriptExecution.update({
      executed: true,
      executed_at: new Date(),
      executed_by: req.user.id,
      status: 'completed',
      output: stdout,
      error_message: stderr || null
    }, { transaction });

    await transaction.commit();

    return res.status(200).json({
      success: true,
      message: 'Script executed successfully',
      scriptExecution
    });
  } catch (error) {
    await transaction.rollback();

    console.error('Error executing script:', error);

    // Update script execution status to failed
    if (req.params.scriptId) {
      try {
        const scriptExecution = await ScriptExecution.findByPk(req.params.scriptId);
        if (scriptExecution) {
          await scriptExecution.update({
            status: 'failed',
            error_message: error.message
          });
        }
      } catch (updateError) {
        console.error('Error updating script execution status:', updateError);
      }
    }

    return res.status(500).json({
      success: false,
      error: 'Failed to execute script',
      details: error.message
    });
  }
};

/**
 * Check script execution status
 */
export const checkScriptExecutionStatus = async (req, res) => {
  try {
    const pendingScripts = await ScriptExecution.count({
      where: { executed: false }
    });

    const allScripts = await ScriptExecution.count();
    const executedScripts = await ScriptExecution.count({
      where: { executed: true }
    });

    return res.status(200).json({
      success: true,
      pendingScripts,
      allScripts,
      executedScripts,
      allExecuted: pendingScripts === 0
    });
  } catch (error) {
    console.error('Error checking script execution status:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to check script execution status'
    });
  }
};
