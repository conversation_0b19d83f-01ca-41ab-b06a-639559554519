import Farm from '../models/Farm.js';
import Stripe from 'stripe';
import dotenv from 'dotenv';

dotenv.config();

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY);

/**
 * Check the sync status between farms and Stripe customers
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const checkSyncStatus = async (req, res) => {
  try {
    // Get all farms
    const farms = await Farm.findAll();

    // Initialize statuses object
    const statuses = {};

    // Check each farm's sync status with Stripe
    for (const farm of farms) {
      let inSync = true;
      let message = 'Farm is in sync with Stripe';

      // If farm doesn't have a Stripe customer ID, it's out of sync
      if (!farm.stripe_customer_id) {
        inSync = false;
        message = 'Farm does not have a Stripe customer ID';
      } else {
        try {
          // Try to retrieve the customer from Stripe
          const customer = await stripe.customers.retrieve(farm.stripe_customer_id);

          // If customer is deleted or doesn't exist, farm is out of sync
          if (customer.deleted) {
            inSync = false;
            message = 'Stripe customer has been deleted';
          }

          // Check if billing email matches
          if (farm.billing_email && customer.email !== farm.billing_email) {
            inSync = false;
            message = 'Billing email does not match Stripe customer email';
          }
        } catch (error) {
          // If customer doesn't exist in Stripe, farm is out of sync
          inSync = false;
          message = `Error retrieving Stripe customer: ${error.message}`;
        }
      }

      // Add status to statuses object
      statuses[farm.id] = {
        farmId: farm.id,
        inSync,
        message
      };
    }

    // Return statuses
    res.json({ statuses });
  } catch (error) {
    console.error('Error checking sync status:', error);
    res.status(500).json({ error: 'Failed to check sync status' });
  }
};

/**
 * Sync a specific farm with Stripe
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const syncFarm = async (req, res) => {
  try {
    const { farmId } = req.params;

    // Get the farm
    const farm = await Farm.findByPk(farmId);

    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    let customer;

    // If farm already has a Stripe customer ID, try to retrieve it
    if (farm.stripe_customer_id) {
      try {
        customer = await stripe.customers.retrieve(farm.stripe_customer_id);

        // If customer is deleted, create a new one
        if (customer.deleted) {
          customer = await createStripeCustomer(farm);
        } else {
          // Update the customer with farm's current information
          customer = await stripe.customers.update(farm.stripe_customer_id, {
            name: farm.name,
            email: farm.billing_email || undefined,
            address: farm.billing_address ? {
              line1: farm.billing_address,
              city: farm.billing_city,
              state: farm.billing_state,
              postal_code: farm.billing_zip_code,
              country: farm.billing_country
            } : undefined,
            metadata: {
              farm_id: farm.id
            }
          });
        }
      } catch (error) {
        // If customer doesn't exist in Stripe, create a new one
        customer = await createStripeCustomer(farm);
      }
    } else {
      // If farm doesn't have a Stripe customer ID, create a new one
      customer = await createStripeCustomer(farm);
    }

    // Update the farm with the Stripe customer ID
    await farm.update({ stripe_customer_id: customer.id });

    // Return success status
    res.json({
      status: {
        farmId: farm.id,
        inSync: true,
        message: 'Farm successfully synced with Stripe'
      }
    });
  } catch (error) {
    console.error('Error syncing farm:', error);
    res.status(500).json({ error: 'Failed to sync farm with Stripe' });
  }
};

/**
 * Sync all farms with Stripe
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const syncAllFarms = async (req, res) => {
  try {
    // Get all farms
    const farms = await Farm.findAll();

    // Initialize statuses object
    const statuses = {};

    // Sync each farm with Stripe
    for (const farm of farms) {
      try {
        let customer;

        // If farm already has a Stripe customer ID, try to retrieve it
        if (farm.stripe_customer_id) {
          try {
            customer = await stripe.customers.retrieve(farm.stripe_customer_id);

            // If customer is deleted, create a new one
            if (customer.deleted) {
              customer = await createStripeCustomer(farm);
            } else {
              // Update the customer with farm's current information
              customer = await stripe.customers.update(farm.stripe_customer_id, {
                name: farm.name,
                email: farm.billing_email || undefined,
                address: farm.billing_address ? {
                  line1: farm.billing_address,
                  city: farm.billing_city,
                  state: farm.billing_state,
                  postal_code: farm.billing_zip_code,
                  country: farm.billing_country
                } : undefined,
                metadata: {
                  farm_id: farm.id
                }
              });
            }
          } catch (error) {
            // If customer doesn't exist in Stripe, create a new one
            customer = await createStripeCustomer(farm);
          }
        } else {
          // If farm doesn't have a Stripe customer ID, create a new one
          customer = await createStripeCustomer(farm);
        }

        // Update the farm with the Stripe customer ID
        await farm.update({ stripe_customer_id: customer.id });

        // Add success status
        statuses[farm.id] = {
          farmId: farm.id,
          inSync: true,
          message: 'Farm successfully synced with Stripe'
        };
      } catch (error) {
        console.error(`Error syncing farm ${farm.id}:`, error);

        // Add error status
        statuses[farm.id] = {
          farmId: farm.id,
          inSync: false,
          message: `Error syncing farm: ${error.message}`
        };
      }
    }

    // Return statuses
    res.json({ statuses });
  } catch (error) {
    console.error('Error syncing all farms:', error);
    res.status(500).json({ error: 'Failed to sync farms with Stripe' });
  }
};

/**
 * Helper function to create a Stripe customer for a farm
 * @param {Object} farm - Farm model instance
 * @returns {Promise<Object>} - Stripe customer object
 */
const createStripeCustomer = async (farm) => {
  const customerData = {
    name: farm.name,
    metadata: {
      farm_id: farm.id
    }
  };

  // Add email if available
  if (farm.billing_email) {
    customerData.email = farm.billing_email;
  }

  // Add address if available
  if (farm.billing_address) {
    customerData.address = {
      line1: farm.billing_address,
      city: farm.billing_city,
      state: farm.billing_state,
      postal_code: farm.billing_zip_code,
      country: farm.billing_country
    };
  }

  return await stripe.customers.create(customerData);
};
