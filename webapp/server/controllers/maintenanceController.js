import MaintenanceSchedule from '../models/MaintenanceSchedule.js';
import MaintenanceLog from '../models/MaintenanceLog.js';
import Equipment from '../models/Equipment.js';
import { sequelize } from '../config/database.js';
import { Op } from 'sequelize';

// Maintenance Schedule Controllers

// Get all maintenance schedules (across all equipment)
export const getAllMaintenanceSchedules = async (req, res) => {
  try {
    // Get query parameters for filtering
    const { page = 1, limit = 20 } = req.query;
    const offset = (page - 1) * limit;

    // Get all maintenance schedules with pagination
    const { count, rows: maintenanceSchedules } = await MaintenanceSchedule.findAndCountAll({
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['next_due_date', 'ASC']],
      include: [
        {
          model: Equipment,
          attributes: ['id', 'name', 'type', 'manufacturer', 'model']
        }
      ]
    });

    return res.status(200).json({ 
      maintenanceSchedules,
      totalItems: count,
      totalPages: Math.ceil(count / limit),
      currentPage: parseInt(page)
    });
  } catch (error) {
    console.error('Error getting all maintenance schedules:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get maintenance schedules by maintenance type
export const getMaintenanceSchedulesByType = async (req, res) => {
  try {
    const { maintenanceType } = req.params;

    // Get all maintenance schedules of the specified type
    const maintenanceSchedules = await MaintenanceSchedule.findAll({
      where: { maintenance_type: maintenanceType },
      order: [['next_due_date', 'ASC']],
      include: [
        {
          model: Equipment,
          attributes: ['id', 'name', 'type', 'manufacturer', 'model']
        }
      ]
    });

    return res.status(200).json({ maintenanceSchedules });
  } catch (error) {
    console.error('Error getting maintenance schedules by type:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get overdue maintenance schedules
export const getOverdueMaintenanceSchedules = async (req, res) => {
  try {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // Get all maintenance schedules that are overdue
    const maintenanceSchedules = await MaintenanceSchedule.findAll({
      where: {
        next_due_date: {
          [Op.lt]: today
        }
      },
      order: [['next_due_date', 'ASC']],
      include: [
        {
          model: Equipment,
          attributes: ['id', 'name', 'type', 'manufacturer', 'model']
        }
      ]
    });

    return res.status(200).json({ maintenanceSchedules });
  } catch (error) {
    console.error('Error getting overdue maintenance schedules:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get all maintenance schedules for a piece of equipment
export const getEquipmentMaintenanceSchedules = async (req, res) => {
  try {
    const { equipmentId } = req.params;

    // Find equipment to ensure it exists
    const equipment = await Equipment.findByPk(equipmentId);
    if (!equipment) {
      return res.status(404).json({ error: 'Equipment not found' });
    }

    // Get all maintenance schedules for the equipment
    const maintenanceSchedules = await MaintenanceSchedule.findAll({
      where: { equipment_id: equipmentId },
      order: [['maintenance_type', 'ASC']]
    });

    return res.status(200).json({ maintenanceSchedules });
  } catch (error) {
    console.error('Error getting equipment maintenance schedules:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get a single maintenance schedule by ID
export const getMaintenanceScheduleById = async (req, res) => {
  try {
    const { scheduleId } = req.params;

    const maintenanceSchedule = await MaintenanceSchedule.findByPk(scheduleId);

    if (!maintenanceSchedule) {
      return res.status(404).json({ error: 'Maintenance schedule not found' });
    }

    return res.status(200).json({ maintenanceSchedule });
  } catch (error) {
    console.error('Error getting maintenance schedule:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Create a new maintenance schedule
export const createMaintenanceSchedule = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { 
      equipmentId, 
      maintenanceType, 
      frequencyType, 
      frequencyValue, 
      lastPerformedDate, 
      lastPerformedHours, 
      nextDueDate, 
      nextDueHours, 
      alertThreshold, 
      notes 
    } = req.body;

    // Validate required fields
    if (!equipmentId) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Equipment ID is required' });
    }

    if (!maintenanceType) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Maintenance type is required' });
    }

    if (!frequencyType) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Frequency type is required' });
    }

    if (!frequencyValue) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Frequency value is required' });
    }

    // Find equipment to ensure it exists
    const equipment = await Equipment.findByPk(equipmentId);
    if (!equipment) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Equipment not found' });
    }

    // Create maintenance schedule
    const maintenanceSchedule = await MaintenanceSchedule.create({
      equipment_id: equipmentId,
      maintenance_type: maintenanceType,
      frequency_type: frequencyType,
      frequency_value: frequencyValue,
      last_performed_date: lastPerformedDate,
      last_performed_hours: lastPerformedHours,
      next_due_date: nextDueDate,
      next_due_hours: nextDueHours,
      alert_threshold: alertThreshold,
      notes
    }, { transaction });

    await transaction.commit();

    return res.status(201).json({ 
      message: 'Maintenance schedule created successfully',
      maintenanceSchedule 
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error creating maintenance schedule:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Update a maintenance schedule
export const updateMaintenanceSchedule = async (req, res) => {
  try {
    const { scheduleId } = req.params;
    const { 
      maintenanceType, 
      frequencyType, 
      frequencyValue, 
      lastPerformedDate, 
      lastPerformedHours, 
      nextDueDate, 
      nextDueHours, 
      alertThreshold, 
      notes 
    } = req.body;

    // Find maintenance schedule to ensure it exists
    const maintenanceSchedule = await MaintenanceSchedule.findByPk(scheduleId);
    if (!maintenanceSchedule) {
      return res.status(404).json({ error: 'Maintenance schedule not found' });
    }

    // Update maintenance schedule
    await maintenanceSchedule.update({
      maintenance_type: maintenanceType || maintenanceSchedule.maintenance_type,
      frequency_type: frequencyType || maintenanceSchedule.frequency_type,
      frequency_value: frequencyValue !== undefined ? frequencyValue : maintenanceSchedule.frequency_value,
      last_performed_date: lastPerformedDate !== undefined ? lastPerformedDate : maintenanceSchedule.last_performed_date,
      last_performed_hours: lastPerformedHours !== undefined ? lastPerformedHours : maintenanceSchedule.last_performed_hours,
      next_due_date: nextDueDate !== undefined ? nextDueDate : maintenanceSchedule.next_due_date,
      next_due_hours: nextDueHours !== undefined ? nextDueHours : maintenanceSchedule.next_due_hours,
      alert_threshold: alertThreshold !== undefined ? alertThreshold : maintenanceSchedule.alert_threshold,
      notes: notes !== undefined ? notes : maintenanceSchedule.notes
    });

    return res.status(200).json({ 
      message: 'Maintenance schedule updated successfully',
      maintenanceSchedule 
    });
  } catch (error) {
    console.error('Error updating maintenance schedule:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Delete a maintenance schedule
export const deleteMaintenanceSchedule = async (req, res) => {
  try {
    const { scheduleId } = req.params;

    // Find maintenance schedule to ensure it exists
    const maintenanceSchedule = await MaintenanceSchedule.findByPk(scheduleId);
    if (!maintenanceSchedule) {
      return res.status(404).json({ error: 'Maintenance schedule not found' });
    }

    // Delete maintenance schedule
    await maintenanceSchedule.destroy();

    return res.status(200).json({ 
      message: 'Maintenance schedule deleted successfully' 
    });
  } catch (error) {
    console.error('Error deleting maintenance schedule:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Maintenance Log Controllers

// Get all maintenance logs for a piece of equipment
export const getEquipmentMaintenanceLogs = async (req, res) => {
  try {
    const { equipmentId } = req.params;

    // Find equipment to ensure it exists
    const equipment = await Equipment.findByPk(equipmentId);
    if (!equipment) {
      return res.status(404).json({ error: 'Equipment not found' });
    }

    // Get all maintenance logs for the equipment
    const maintenanceLogs = await MaintenanceLog.findAll({
      where: { equipment_id: equipmentId },
      order: [['service_date', 'DESC']]
    });

    return res.status(200).json({ maintenanceLogs });
  } catch (error) {
    console.error('Error getting equipment maintenance logs:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get a single maintenance log by ID
export const getMaintenanceLogById = async (req, res) => {
  try {
    const { logId } = req.params;

    const maintenanceLog = await MaintenanceLog.findByPk(logId);

    if (!maintenanceLog) {
      return res.status(404).json({ error: 'Maintenance log not found' });
    }

    return res.status(200).json({ maintenanceLog });
  } catch (error) {
    console.error('Error getting maintenance log:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Create a new maintenance log
export const createMaintenanceLog = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { 
      equipmentId, 
      maintenanceScheduleId, 
      serviceDate, 
      serviceHours, 
      serviceType, 
      description, 
      performedBy, 
      partsUsed, 
      laborHours, 
      laborCost, 
      partsCost, 
      totalCost, 
      notes 
    } = req.body;

    // Validate required fields
    if (!equipmentId) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Equipment ID is required' });
    }

    if (!serviceDate) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Service date is required' });
    }

    if (!serviceType) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Service type is required' });
    }

    if (!description) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Description is required' });
    }

    // Find equipment to ensure it exists
    const equipment = await Equipment.findByPk(equipmentId);
    if (!equipment) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Equipment not found' });
    }

    // If maintenance schedule ID is provided, ensure it exists
    if (maintenanceScheduleId) {
      const maintenanceSchedule = await MaintenanceSchedule.findByPk(maintenanceScheduleId);
      if (!maintenanceSchedule) {
        await transaction.rollback();
        return res.status(404).json({ error: 'Maintenance schedule not found' });
      }
    }

    // Create maintenance log
    const maintenanceLog = await MaintenanceLog.create({
      equipment_id: equipmentId,
      maintenance_schedule_id: maintenanceScheduleId,
      service_date: serviceDate,
      service_hours: serviceHours,
      service_type: serviceType,
      description,
      performed_by: performedBy,
      parts_used: partsUsed,
      labor_hours: laborHours,
      labor_cost: laborCost,
      parts_cost: partsCost,
      total_cost: totalCost,
      notes
    }, { transaction });

    // If this is for a scheduled maintenance, update the schedule's last performed date and hours
    if (maintenanceScheduleId) {
      const maintenanceSchedule = await MaintenanceSchedule.findByPk(maintenanceScheduleId);

      // Calculate next due date based on frequency
      let nextDueDate = null;
      if (maintenanceSchedule.frequency_type === 'days') {
        const date = new Date(serviceDate);
        date.setDate(date.getDate() + maintenanceSchedule.frequency_value);
        nextDueDate = date.toISOString().split('T')[0];
      } else if (maintenanceSchedule.frequency_type === 'months') {
        const date = new Date(serviceDate);
        date.setMonth(date.getMonth() + maintenanceSchedule.frequency_value);
        nextDueDate = date.toISOString().split('T')[0];
      } else if (maintenanceSchedule.frequency_type === 'years') {
        const date = new Date(serviceDate);
        date.setFullYear(date.getFullYear() + maintenanceSchedule.frequency_value);
        nextDueDate = date.toISOString().split('T')[0];
      }

      // Calculate next due hours based on frequency
      let nextDueHours = null;
      if (maintenanceSchedule.frequency_type === 'hours' && serviceHours !== null && serviceHours !== undefined) {
        nextDueHours = parseFloat(serviceHours) + parseFloat(maintenanceSchedule.frequency_value);
      }

      await maintenanceSchedule.update({
        last_performed_date: serviceDate,
        last_performed_hours: serviceHours,
        next_due_date: nextDueDate,
        next_due_hours: nextDueHours
      }, { transaction });
    }

    await transaction.commit();

    return res.status(201).json({ 
      message: 'Maintenance log created successfully',
      maintenanceLog 
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error creating maintenance log:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Update a maintenance log
export const updateMaintenanceLog = async (req, res) => {
  try {
    const { logId } = req.params;
    const { 
      serviceDate, 
      serviceHours, 
      serviceType, 
      description, 
      performedBy, 
      partsUsed, 
      laborHours, 
      laborCost, 
      partsCost, 
      totalCost, 
      notes 
    } = req.body;

    // Find maintenance log to ensure it exists
    const maintenanceLog = await MaintenanceLog.findByPk(logId);
    if (!maintenanceLog) {
      return res.status(404).json({ error: 'Maintenance log not found' });
    }

    // Update maintenance log
    await maintenanceLog.update({
      service_date: serviceDate || maintenanceLog.service_date,
      service_hours: serviceHours !== undefined ? serviceHours : maintenanceLog.service_hours,
      service_type: serviceType || maintenanceLog.service_type,
      description: description || maintenanceLog.description,
      performed_by: performedBy !== undefined ? performedBy : maintenanceLog.performed_by,
      parts_used: partsUsed !== undefined ? partsUsed : maintenanceLog.parts_used,
      labor_hours: laborHours !== undefined ? laborHours : maintenanceLog.labor_hours,
      labor_cost: laborCost !== undefined ? laborCost : maintenanceLog.labor_cost,
      parts_cost: partsCost !== undefined ? partsCost : maintenanceLog.parts_cost,
      total_cost: totalCost !== undefined ? totalCost : maintenanceLog.total_cost,
      notes: notes !== undefined ? notes : maintenanceLog.notes
    });

    return res.status(200).json({ 
      message: 'Maintenance log updated successfully',
      maintenanceLog 
    });
  } catch (error) {
    console.error('Error updating maintenance log:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Delete a maintenance log
export const deleteMaintenanceLog = async (req, res) => {
  try {
    const { logId } = req.params;

    // Find maintenance log to ensure it exists
    const maintenanceLog = await MaintenanceLog.findByPk(logId);
    if (!maintenanceLog) {
      return res.status(404).json({ error: 'Maintenance log not found' });
    }

    // Delete maintenance log
    await maintenanceLog.destroy();

    return res.status(200).json({ 
      message: 'Maintenance log deleted successfully' 
    });
  } catch (error) {
    console.error('Error deleting maintenance log:', error);
    return res.status(500).json({ error: error.message });
  }
};
