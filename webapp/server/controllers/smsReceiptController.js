import { sequelize } from '../config/database.js';
import Receipt from '../models/Receipt.js';
import User from '../models/User.js';
import Farm from '../models/Farm.js';
import UserFarm from '../models/UserFarm.js';
import { validatePhoneNumber, sendSMS, getMediaContent } from '../services/twilioService.js';
import {
  validateFileType,
  generateStoragePath,
  saveFile,
  updateStorageUsage
} from '../utils/fileUtils.js';
import path from 'path';

// Store conversation state for users
const userConversations = new Map();

/**
 * Process an SMS receipt
 * This endpoint will be called by the Twilio webhook when a user sends an SMS
 */
export const processSmsReceipt = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    // Extract data from Twilio webhook
    const { From, Body, NumMedia, MessageSid } = req.body;
    
    // Get media URLs if available
    const mediaUrls = [];
    for (let i = 0; i < parseInt(NumMedia || 0); i++) {
      const mediaUrl = req.body[`MediaUrl${i}`];
      if (mediaUrl) {
        mediaUrls.push(mediaUrl);
      }
    }

    console.log(`Received SMS from ${From} with ${NumMedia} media items`);

    // Validate phone number
    const phoneNumber = validatePhoneNumber(From);
    if (!phoneNumber) {
      await transaction.rollback();
      return res.status(400).send('<Response><Message>Invalid phone number format.</Message></Response>');
    }

    // Find user by phone number
    const user = await User.findOne({
      where: { phone_number: phoneNumber }
    });

    if (!user) {
      await transaction.rollback();
      return res.status(404).send(
        '<Response><Message>Your phone number is not registered with any account. Please register in the app first.</Message></Response>'
      );
    }

    // Get user's farms
    const userFarms = await UserFarm.findAll({
      where: { user_id: user.id },
      include: [{ model: Farm, as: 'farm' }]
    });

    if (userFarms.length === 0) {
      await transaction.rollback();
      return res.status(404).send(
        '<Response><Message>You do not have access to any farms. Please contact your administrator.</Message></Response>'
      );
    }

    // Check if user is in an active conversation
    let conversation = userConversations.get(phoneNumber);
    
    // If no conversation exists and there's media, start a new one
    if (!conversation && mediaUrls.length > 0) {
      // If user has access to only one farm, use that
      if (userFarms.length === 1) {
        const farm = userFarms[0].farm;
        conversation = {
          state: 'CATEGORY',
          farmId: farm.id,
          mediaUrls,
          messageSid: MessageSid,
          timestamp: new Date()
        };
        userConversations.set(phoneNumber, conversation);
        
        await transaction.commit();
        return res.status(200).send(
          '<Response><Message>Thanks for your receipt! Please reply with a category for this receipt (e.g., Supplies, Fuel, Equipment, Office, etc.)</Message></Response>'
        );
      } else {
        // User has access to multiple farms, ask which one to use
        const farmOptions = userFarms.map((uf, index) => 
          `${index + 1}. ${uf.farm.name}`
        ).join('\n');
        
        conversation = {
          state: 'FARM_SELECTION',
          farms: userFarms.map(uf => ({ 
            id: uf.farm.id, 
            name: uf.farm.name 
          })),
          mediaUrls,
          messageSid: MessageSid,
          timestamp: new Date()
        };
        userConversations.set(phoneNumber, conversation);
        
        await transaction.commit();
        return res.status(200).send(
          `<Response><Message>Thanks for your receipt! Please select which farm this receipt is for:\n${farmOptions}\nReply with the number.</Message></Response>`
        );
      }
    } 
    // Handle ongoing conversation
    else if (conversation) {
      // Check for conversation timeout (30 minutes)
      const now = new Date();
      const timeDiff = now - conversation.timestamp;
      if (timeDiff > 30 * 60 * 1000) { // 30 minutes in milliseconds
        userConversations.delete(phoneNumber);
        await transaction.rollback();
        return res.status(200).send(
          '<Response><Message>Your session has expired. Please send your receipt again.</Message></Response>'
        );
      }
      
      // Update timestamp
      conversation.timestamp = now;
      
      // Handle farm selection state
      if (conversation.state === 'FARM_SELECTION') {
        const selection = parseInt(Body.trim());
        
        if (isNaN(selection) || selection < 1 || selection > conversation.farms.length) {
          await transaction.rollback();
          return res.status(200).send(
            '<Response><Message>Invalid selection. Please enter a number from the list.</Message></Response>'
          );
        }
        
        const selectedFarm = conversation.farms[selection - 1];
        conversation.farmId = selectedFarm.id;
        conversation.state = 'CATEGORY';
        
        userConversations.set(phoneNumber, conversation);
        
        await transaction.commit();
        return res.status(200).send(
          `<Response><Message>Selected farm: ${selectedFarm.name}. Please reply with a category for this receipt (e.g., Supplies, Fuel, Equipment, Office, etc.)</Message></Response>`
        );
      }
      
      // Handle category state
      else if (conversation.state === 'CATEGORY') {
        const category = Body.trim();
        
        // Process the receipt with the provided category
        try {
          // Process each media item as a receipt
          for (const mediaUrl of conversation.mediaUrls) {
            // Download the media content
            const mediaContent = await getMediaContent(mediaUrl);
            
            // Generate a filename
            const filename = `sms_receipt_${Date.now()}_${path.basename(mediaUrl)}`;
            
            // Validate file type
            const fileTypeValidation = await validateFileType(filename, mediaContent);
            if (!fileTypeValidation.valid) {
              console.error(`Invalid file type: ${fileTypeValidation.reason}`);
              continue; // Skip this file
            }
            
            // Generate storage path
            const storagePath = generateStoragePath(conversation.farmId, user.id, filename);
            
            // Save file to disk
            await saveFile(mediaContent, storagePath);
            
            // Create receipt data
            const receiptData = {
              description: `SMS Receipt - Category: ${category}`,
              status: 'pending',
              farm_id: conversation.farmId,
              tenant_id: conversation.farmId, // Using farm_id as tenant_id
              uploaded_by: user.id,
              file_path: storagePath,
              file_size: mediaContent.length,
              file_type: path.extname(filename).substring(1) || 'unknown',
              mime_type: fileTypeValidation.detectedType || 'image/jpeg',
              receipt_date: new Date()
            };
            
            // Create receipt record
            await Receipt.create(receiptData, { transaction });
            
            // Update storage usage
            await updateStorageUsage(conversation.farmId, mediaContent.length, false, 1);
          }
          
          // Clear the conversation
          userConversations.delete(phoneNumber);
          
          await transaction.commit();
          return res.status(200).send(
            '<Response><Message>Receipt uploaded successfully! It will be processed shortly.</Message></Response>'
          );
        } catch (error) {
          console.error('Error processing receipt:', error);
          await transaction.rollback();
          return res.status(500).send(
            '<Response><Message>There was an error processing your receipt. Please try again later.</Message></Response>'
          );
        }
      }
    } 
    // No active conversation and no media
    else {
      await transaction.rollback();
      return res.status(200).send(
        '<Response><Message>Please send a photo of your receipt to upload it.</Message></Response>'
      );
    }
  } catch (error) {
    await transaction.rollback();
    console.error('Error processing SMS receipt:', error);
    return res.status(500).send(
      '<Response><Message>An error occurred. Please try again later.</Message></Response>'
    );
  }
};

/**
 * Clear a user's conversation state
 * @param {string} phoneNumber - User's phone number
 */
export const clearConversation = (phoneNumber) => {
  const formattedNumber = validatePhoneNumber(phoneNumber);
  if (formattedNumber) {
    userConversations.delete(formattedNumber);
    return true;
  }
  return false;
};

/**
 * Get all active conversations (for debugging)
 */
export const getActiveConversations = () => {
  return Array.from(userConversations.entries()).map(([phoneNumber, conversation]) => ({
    phoneNumber,
    state: conversation.state,
    timestamp: conversation.timestamp
  }));
};