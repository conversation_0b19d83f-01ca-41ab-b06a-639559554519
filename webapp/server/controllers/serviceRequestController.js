import ServiceRequest from '../models/ServiceRequest.js';
import ServiceProvider from '../models/ServiceProvider.js';
import { sequelize } from '../config/database.js';

// Get all service requests for a farm
export const getServiceRequests = async (req, res) => {
  try {
    const { farmId, status, serviceProviderId } = req.query;

    if (!farmId) {
      return res.status(400).json({ message: 'Farm ID is required' });
    }

    const whereClause = { farm_id: farmId };
    
    // Filter by status if provided
    if (status) {
      whereClause.status = status;
    }
    
    // Filter by service provider if provided
    if (serviceProviderId) {
      whereClause.service_provider_id = serviceProviderId;
    }

    const serviceRequests = await ServiceRequest.findAll({
      where: whereClause,
      include: [
        {
          model: ServiceProvider,
          attributes: ['id', 'name', 'service_type', 'contact_name', 'email', 'phone']
        }
      ],
      order: [['created_at', 'DESC']]
    });

    res.status(200).json(serviceRequests);
  } catch (error) {
    console.error('Error fetching service requests:', error);
    res.status(500).json({ message: 'Failed to fetch service requests', error: error.message });
  }
};

// Get a single service request by ID
export const getServiceRequest = async (req, res) => {
  try {
    const { id } = req.params;

    const serviceRequest = await ServiceRequest.findByPk(id, {
      include: [
        {
          model: ServiceProvider,
          attributes: ['id', 'name', 'service_type', 'contact_name', 'email', 'phone', 'address', 'website']
        }
      ]
    });

    if (!serviceRequest) {
      return res.status(404).json({ message: 'Service request not found' });
    }

    res.status(200).json(serviceRequest);
  } catch (error) {
    console.error('Error fetching service request:', error);
    res.status(500).json({ message: 'Failed to fetch service request', error: error.message });
  }
};

// Create a new service request
export const createServiceRequest = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const {
      farm_id,
      service_provider_id,
      user_id,
      title,
      description,
      service_type,
      status,
      priority,
      location,
      requested_date,
      estimated_cost,
      notes
    } = req.body;

    if (!farm_id || !service_provider_id || !user_id || !title || !description) {
      await transaction.rollback();
      return res.status(400).json({ message: 'Farm ID, Service Provider ID, User ID, title, and description are required' });
    }

    const newServiceRequest = await ServiceRequest.create({
      farm_id,
      service_provider_id,
      user_id,
      title,
      description,
      service_type,
      status: status || 'draft',
      priority: priority || 'medium',
      location,
      requested_date,
      estimated_cost,
      notes,
      payment_status: 'unpaid'
    }, { transaction });

    await transaction.commit();

    // Fetch the complete service request with provider to return
    const completeServiceRequest = await ServiceRequest.findByPk(newServiceRequest.id, {
      include: [
        {
          model: ServiceProvider,
          attributes: ['id', 'name', 'service_type', 'contact_name', 'email', 'phone']
        }
      ]
    });

    res.status(201).json(completeServiceRequest);
  } catch (error) {
    await transaction.rollback();
    console.error('Error creating service request:', error);
    res.status(500).json({ message: 'Failed to create service request', error: error.message });
  }
};

// Update a service request
export const updateServiceRequest = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { id } = req.params;
    const {
      title,
      description,
      service_type,
      status,
      priority,
      location,
      requested_date,
      scheduled_date,
      completion_date,
      estimated_cost,
      actual_cost,
      payment_status,
      notes,
      provider_notes
    } = req.body;

    const serviceRequest = await ServiceRequest.findByPk(id, { transaction });

    if (!serviceRequest) {
      await transaction.rollback();
      return res.status(404).json({ message: 'Service request not found' });
    }

    // Update service request fields
    if (title !== undefined) serviceRequest.title = title;
    if (description !== undefined) serviceRequest.description = description;
    if (service_type !== undefined) serviceRequest.service_type = service_type;
    if (status !== undefined) serviceRequest.status = status;
    if (priority !== undefined) serviceRequest.priority = priority;
    if (location !== undefined) serviceRequest.location = location;
    if (requested_date !== undefined) serviceRequest.requested_date = requested_date;
    if (scheduled_date !== undefined) serviceRequest.scheduled_date = scheduled_date;
    if (completion_date !== undefined) serviceRequest.completion_date = completion_date;
    if (estimated_cost !== undefined) serviceRequest.estimated_cost = estimated_cost;
    if (actual_cost !== undefined) serviceRequest.actual_cost = actual_cost;
    if (payment_status !== undefined) serviceRequest.payment_status = payment_status;
    if (notes !== undefined) serviceRequest.notes = notes;
    if (provider_notes !== undefined) serviceRequest.provider_notes = provider_notes;

    await serviceRequest.save({ transaction });
    await transaction.commit();

    // Fetch the updated service request with provider to return
    const updatedServiceRequest = await ServiceRequest.findByPk(id, {
      include: [
        {
          model: ServiceProvider,
          attributes: ['id', 'name', 'service_type', 'contact_name', 'email', 'phone']
        }
      ]
    });

    res.status(200).json(updatedServiceRequest);
  } catch (error) {
    await transaction.rollback();
    console.error('Error updating service request:', error);
    res.status(500).json({ message: 'Failed to update service request', error: error.message });
  }
};

// Delete a service request
export const deleteServiceRequest = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { id } = req.params;

    const serviceRequest = await ServiceRequest.findByPk(id, { transaction });

    if (!serviceRequest) {
      await transaction.rollback();
      return res.status(404).json({ message: 'Service request not found' });
    }

    await serviceRequest.destroy({ transaction });
    await transaction.commit();

    res.status(200).json({ message: 'Service request deleted successfully' });
  } catch (error) {
    await transaction.rollback();
    console.error('Error deleting service request:', error);
    res.status(500).json({ message: 'Failed to delete service request', error: error.message });
  }
};

// Update service request status
export const updateServiceRequestStatus = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { id } = req.params;
    const { status, provider_notes } = req.body;

    if (!status) {
      await transaction.rollback();
      return res.status(400).json({ message: 'Status is required' });
    }

    const serviceRequest = await ServiceRequest.findByPk(id, { transaction });

    if (!serviceRequest) {
      await transaction.rollback();
      return res.status(404).json({ message: 'Service request not found' });
    }

    // Update status
    serviceRequest.status = status;
    
    // Update provider notes if provided
    if (provider_notes !== undefined) {
      serviceRequest.provider_notes = provider_notes;
    }
    
    // Update dates based on status
    if (status === 'scheduled' && !serviceRequest.scheduled_date) {
      serviceRequest.scheduled_date = new Date();
    } else if (status === 'completed' && !serviceRequest.completion_date) {
      serviceRequest.completion_date = new Date();
    }

    await serviceRequest.save({ transaction });
    await transaction.commit();

    res.status(200).json(serviceRequest);
  } catch (error) {
    await transaction.rollback();
    console.error('Error updating service request status:', error);
    res.status(500).json({ message: 'Failed to update service request status', error: error.message });
  }
};

// Rate and review a service request
export const rateServiceRequest = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { id } = req.params;
    const { rating, review } = req.body;

    if (!rating || rating < 1 || rating > 5) {
      await transaction.rollback();
      return res.status(400).json({ message: 'Rating is required and must be between 1 and 5' });
    }

    const serviceRequest = await ServiceRequest.findByPk(id, { transaction });

    if (!serviceRequest) {
      await transaction.rollback();
      return res.status(404).json({ message: 'Service request not found' });
    }

    // Update rating and review
    serviceRequest.rating = rating;
    serviceRequest.review = review;

    await serviceRequest.save({ transaction });

    // Update the service provider's average rating
    // In a real application, you would calculate the average rating from all service requests
    // For simplicity, we're just updating the service provider with this rating
    const serviceProvider = await ServiceProvider.findByPk(serviceRequest.service_provider_id, { transaction });
    if (serviceProvider) {
      // Simple average calculation (this is a simplification)
      if (!serviceProvider.rating) {
        serviceProvider.rating = rating;
      } else {
        // In a real app, you'd calculate a proper average based on all ratings
        serviceProvider.rating = (serviceProvider.rating + rating) / 2;
      }
      await serviceProvider.save({ transaction });
    }

    await transaction.commit();

    res.status(200).json({ message: 'Service request rated successfully', rating, review });
  } catch (error) {
    await transaction.rollback();
    console.error('Error rating service request:', error);
    res.status(500).json({ message: 'Failed to rate service request', error: error.message });
  }
};