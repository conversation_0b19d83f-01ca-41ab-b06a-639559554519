import Alert from '../models/Alert.js';
import AlertRule from '../models/AlertRule.js';
import Farm from '../models/Farm.js';
import User from '../models/User.js';
import Equipment from '../models/Equipment.js';
import EquipmentTelematics from '../models/EquipmentTelematics.js';
import InventoryItem from '../models/InventoryItem.js';
import Weather from '../models/Weather.js';
import Transaction from '../models/Transaction.js';
import NodeCache from 'node-cache';
import { Op } from 'sequelize';
import { sequelize } from '../config/database.js';
import axios from 'axios';
import dotenv from 'dotenv';

dotenv.config();

// Initialize cache with standard TTL of 5 minutes and check period of 60 seconds
const cache = new NodeCache({ stdTTL: 300, checkperiod: 60 });

/**
 * Get alerts for a farm
 */
export const getAlerts = async (req, res) => {
  try {
    const { farmId } = req.query;
    const limit = parseInt(req.query.limit) || 10;
    const offset = parseInt(req.query.offset) || 0;
    const type = req.query.type;
    const read = req.query.read !== undefined ? req.query.read === 'true' : undefined;

    if (!farmId) {
      return res.status(400).json({ error: 'Farm ID is required' });
    }

    // Build query conditions
    const where = { farm_id: farmId };
    if (type) where.type = type;
    if (read !== undefined) where.read = read;

    // Get alerts with pagination
    const alerts = await Alert.findAndCountAll({
      where,
      limit,
      offset,
      order: [['created_at', 'DESC']],
      include: [
        {
          model: Farm,
          attributes: ['id', 'name']
        },
        {
          model: User,
          attributes: ['id', 'first_name', 'last_name', 'email']
        }
      ]
    });

    return res.status(200).json({
      alerts: alerts.rows,
      total: alerts.count,
      limit,
      offset
    });
  } catch (error) {
    console.error('Error getting alerts:', error);
    return res.status(500).json({ error: 'Failed to fetch alerts' });
  }
};

/**
 * Create a new alert
 */
export const createAlert = async (req, res) => {
  try {
    const {
      title,
      message,
      type,
      source,
      farmId,
      userId,
      relatedEntityType,
      relatedEntityId,
      actionUrl
    } = req.body;

    // Validate required fields
    if (!title || !message || !farmId) {
      return res.status(400).json({ error: 'Title, message, and farmId are required' });
    }

    // Create the alert
    const alert = await Alert.create({
      title,
      message,
      type: type || 'info',
      source: source || 'system',
      farm_id: farmId,
      user_id: userId,
      related_entity_type: relatedEntityType,
      related_entity_id: relatedEntityId,
      action_url: actionUrl,
      read: false
    });

    return res.status(201).json({ alert });
  } catch (error) {
    console.error('Error creating alert:', error);
    return res.status(500).json({ error: 'Failed to create alert' });
  }
};

/**
 * Mark an alert as read
 */
export const markAlertAsRead = async (req, res) => {
  try {
    const { alertId } = req.params;

    if (!alertId) {
      return res.status(400).json({ error: 'Alert ID is required' });
    }

    // Find the alert
    const alert = await Alert.findByPk(alertId);

    if (!alert) {
      return res.status(404).json({ error: 'Alert not found' });
    }

    // Update the alert
    alert.read = true;
    await alert.save();

    return res.status(200).json({ message: 'Alert marked as read' });
  } catch (error) {
    console.error('Error marking alert as read:', error);
    return res.status(500).json({ error: 'Failed to mark alert as read' });
  }
};

/**
 * Mark all alerts as read for a farm
 */
export const markAllAlertsAsRead = async (req, res) => {
  try {
    const { farmId } = req.query;

    if (!farmId) {
      return res.status(400).json({ error: 'Farm ID is required' });
    }

    // Update all unread alerts for the farm
    await Alert.update(
      { read: true },
      { where: { farm_id: farmId, read: false } }
    );

    return res.status(200).json({ message: 'All alerts marked as read' });
  } catch (error) {
    console.error('Error marking all alerts as read:', error);
    return res.status(500).json({ error: 'Failed to mark all alerts as read' });
  }
};

/**
 * Get alert rules for a farm
 */
export const getAlertRules = async (req, res) => {
  try {
    const { farmId } = req.query;

    if (!farmId) {
      return res.status(400).json({ error: 'Farm ID is required' });
    }

    // Get alert rules
    const rules = await AlertRule.findAll({
      where: { farm_id: farmId },
      order: [['created_at', 'DESC']],
      include: [
        {
          model: Farm,
          attributes: ['id', 'name']
        }
      ]
    });

    return res.status(200).json({ rules });
  } catch (error) {
    console.error('Error getting alert rules:', error);
    return res.status(500).json({ error: 'Failed to fetch alert rules' });
  }
};

/**
 * Create a new alert rule
 */
export const createAlertRule = async (req, res) => {
  try {
    const { name, description, enabled, conditions, actions, farmId } = req.body;

    console.log('Creating alert rule with data:', { name, description, enabled, conditions, actions, farmId });

    // Validate required fields
    if (!name) {
      return res.status(400).json({ error: 'Name is required' });
    }

    if (!farmId) {
      return res.status(400).json({ error: 'FarmId is required' });
    }

    if (!conditions) {
      return res.status(400).json({ error: 'Conditions are required' });
    }

    if (!actions) {
      return res.status(400).json({ error: 'Actions are required' });
    }

    // Validate conditions and actions
    if (!Array.isArray(conditions) || conditions.length === 0) {
      return res.status(400).json({ error: 'Conditions must be a non-empty array' });
    }

    if (!Array.isArray(actions) || actions.length === 0) {
      return res.status(400).json({ error: 'Actions must be a non-empty array' });
    }

    // Create the alert rule
    const rule = await AlertRule.create({
      name,
      description,
      enabled: enabled !== undefined ? enabled : true,
      conditions,
      actions,
      farm_id: farmId
    });

    return res.status(201).json({ rule });
  } catch (error) {
    console.error('Error creating alert rule:', error);
    return res.status(500).json({ error: 'Failed to create alert rule' });
  }
};

/**
 * Update an alert rule
 */
export const updateAlertRule = async (req, res) => {
  try {
    const { ruleId } = req.params;
    const { name, description, enabled, conditions, actions } = req.body;

    if (!ruleId) {
      return res.status(400).json({ error: 'Rule ID is required' });
    }

    // Find the rule
    const rule = await AlertRule.findByPk(ruleId);

    if (!rule) {
      return res.status(404).json({ error: 'Alert rule not found' });
    }

    // Update the rule
    if (name !== undefined) rule.name = name;
    if (description !== undefined) rule.description = description;
    if (enabled !== undefined) rule.enabled = enabled;
    if (conditions !== undefined) {
      if (!Array.isArray(conditions) || conditions.length === 0) {
        return res.status(400).json({ error: 'Conditions must be a non-empty array' });
      }
      rule.conditions = conditions;
    }
    if (actions !== undefined) {
      if (!Array.isArray(actions) || actions.length === 0) {
        return res.status(400).json({ error: 'Actions must be a non-empty array' });
      }
      rule.actions = actions;
    }

    await rule.save();

    return res.status(200).json({ rule });
  } catch (error) {
    console.error('Error updating alert rule:', error);
    return res.status(500).json({ error: 'Failed to update alert rule' });
  }
};

/**
 * Delete an alert rule
 */
export const deleteAlertRule = async (req, res) => {
  try {
    const { ruleId } = req.params;

    if (!ruleId) {
      return res.status(400).json({ error: 'Rule ID is required' });
    }

    // Find the rule
    const rule = await AlertRule.findByPk(ruleId);

    if (!rule) {
      return res.status(404).json({ error: 'Alert rule not found' });
    }

    // Delete the rule
    await rule.destroy();

    return res.status(200).json({ message: 'Alert rule deleted successfully' });
  } catch (error) {
    console.error('Error deleting alert rule:', error);
    return res.status(500).json({ error: 'Failed to delete alert rule' });
  }
};

/**
 * Test an alert rule
 */
export const testAlertRule = async (req, res) => {
  try {
    const { name, conditions, actions, farmId } = req.body;

    // Validate required fields
    if (!conditions || !actions || !farmId) {
      return res.status(400).json({ error: 'Conditions, actions, and farmId are required' });
    }

    // Validate conditions and actions
    if (!Array.isArray(conditions) || conditions.length === 0) {
      return res.status(400).json({ error: 'Conditions must be a non-empty array' });
    }

    if (!Array.isArray(actions) || actions.length === 0) {
      return res.status(400).json({ error: 'Actions must be a non-empty array' });
    }

    // Evaluate conditions against real data
    const conditionResults = await evaluateConditions(conditions, farmId);
    const allConditionsMet = conditionResults.every(result => result.met);

    // Prepare detailed results for the response
    const detailedResults = conditionResults.map(result => ({
      condition: result.condition,
      met: result.met,
      value: result.value,
      message: result.message
    }));

    return res.status(200).json({
      message: allConditionsMet
        ? 'Alert rule would trigger based on current conditions'
        : 'Alert rule would not trigger based on current conditions',
      triggered: allConditionsMet,
      conditionResults: detailedResults,
      actions: actions.map(action => ({
        type: action.type,
        wouldExecute: allConditionsMet
      }))
    });
  } catch (error) {
    console.error('Error testing alert rule:', error);
    return res.status(500).json({ error: 'Failed to test alert rule' });
  }
};

/**
 * Process alert rules for a farm
 * This would typically be called by a scheduled job
 */
export const processAlertRules = async (req, res) => {
  try {
    const { farmId } = req.body;

    if (!farmId) {
      return res.status(400).json({ error: 'Farm ID is required' });
    }

    // Get enabled alert rules for the farm
    const rules = await AlertRule.findAll({
      where: { farm_id: farmId, enabled: true }
    });

    // Process each rule
    const results = [];
    for (const rule of rules) {
      console.log(`Processing alert rule: ${rule.name}`);

      // Evaluate conditions
      const conditionResults = await evaluateConditions(rule.conditions, farmId);
      const allConditionsMet = conditionResults.every(result => result.met);

      // If all conditions are met, execute actions
      if (allConditionsMet) {
        const actionResults = await executeActions(rule.actions, {
          farmId,
          ruleName: rule.name,
          ruleId: rule.id,
          conditionResults
        });

        results.push({
          ruleId: rule.id,
          ruleName: rule.name,
          processed: true,
          triggered: true,
          conditionResults,
          actionResults
        });
      } else {
        results.push({
          ruleId: rule.id,
          ruleName: rule.name,
          processed: true,
          triggered: false,
          conditionResults
        });
      }
    }

    return res.status(200).json({
      message: `Processed ${rules.length} alert rules`,
      results
    });
  } catch (error) {
    console.error('Error processing alert rules:', error);
    return res.status(500).json({ error: 'Failed to process alert rules' });
  }
};

/**
 * Evaluate conditions for an alert rule
 * @param {Array} conditions - Array of condition objects
 * @param {string} farmId - ID of the farm
 * @returns {Promise<Array>} - Array of condition results
 */
const evaluateConditions = async (conditions, farmId) => {
  const results = [];

  for (const condition of conditions) {
    let met = false;
    let value = null;
    let message = '';

    try {
      switch (condition.type) {
        case 'threshold':
          // Evaluate threshold condition
          const { parameter, operator, value: thresholdValue, entityType } = condition;

          // Get the current value based on parameter and entityType
          const currentValue = await getParameterValue(parameter, entityType, farmId);
          value = currentValue;

          // Compare with the threshold
          met = compareValues(currentValue, operator, thresholdValue);
          message = met 
            ? `${parameter} (${currentValue}) ${operator} ${thresholdValue}`
            : `${parameter} (${currentValue}) is not ${operator} ${thresholdValue}`;
          break;

        case 'schedule':
          // Evaluate schedule condition
          const { parameter: scheduleType, value: scheduleValue } = condition;

          // Check if current time matches the schedule
          met = checkSchedule(scheduleType, scheduleValue);
          message = met
            ? `Current time matches schedule: ${scheduleType} ${scheduleValue}`
            : `Current time does not match schedule: ${scheduleType} ${scheduleValue}`;
          break;

        case 'status_change':
          // Evaluate status change condition
          const { parameter: statusParam, value: statusValue, entityType: statusEntityType } = condition;

          // Check if status has changed
          const statusChanged = await checkStatusChange(statusParam, statusValue, statusEntityType, farmId);
          met = statusChanged;
          message = met
            ? `Status of ${statusParam} changed to ${statusValue}`
            : `Status of ${statusParam} has not changed to ${statusValue}`;
          break;

        case 'weather':
          // Evaluate weather condition
          const { parameter: weatherParam, operator: weatherOp, value: weatherValue } = condition;

          // Get weather data
          const weatherData = await getWeatherData(farmId);
          const weatherParamValue = weatherData[weatherParam];
          value = weatherParamValue;

          // Compare with the threshold
          met = compareValues(weatherParamValue, weatherOp, weatherValue);
          message = met
            ? `Weather ${weatherParam} (${weatherParamValue}) ${weatherOp} ${weatherValue}`
            : `Weather ${weatherParam} (${weatherParamValue}) is not ${weatherOp} ${weatherValue}`;
          break;

        case 'inventory_level':
          // Evaluate inventory level condition
          const { parameter: inventoryParam, operator: inventoryOp, value: inventoryValue } = condition;

          // Get inventory level
          const inventoryLevel = await getInventoryLevel(inventoryParam, farmId);
          value = inventoryLevel;

          // Compare with the threshold
          met = compareValues(inventoryLevel, inventoryOp, inventoryValue);
          message = met
            ? `Inventory level for ${inventoryParam} (${inventoryLevel}) ${inventoryOp} ${inventoryValue}`
            : `Inventory level for ${inventoryParam} (${inventoryLevel}) is not ${inventoryOp} ${inventoryValue}`;
          break;

        default:
          message = `Unknown condition type: ${condition.type}`;
      }
    } catch (error) {
      console.error(`Error evaluating condition: ${error.message}`);
      message = `Error evaluating condition: ${error.message}`;
    }

    results.push({
      condition,
      met,
      value,
      message
    });
  }

  return results;
};

/**
 * Execute actions for an alert rule
 * @param {Array} actions - Array of action objects
 * @param {Object} context - Context for the actions
 * @returns {Promise<Array>} - Array of action results
 */
const executeActions = async (actions, context) => {
  const { farmId, ruleName, ruleId, conditionResults } = context;
  const results = [];

  for (const action of actions) {
    let success = false;
    let message = '';

    try {
      switch (action.type) {
        case 'notification':
          // Create an in-app notification
          const notificationTitle = action.template 
            ? replaceTemplateVariables(action.template, context) 
            : `Alert: ${ruleName}`;

          const notificationMessage = generateAlertMessage(conditionResults);

          // Create the alert
          const alert = await Alert.create({
            title: notificationTitle,
            message: notificationMessage,
            type: action.severity || 'info',
            source: 'system',
            farm_id: farmId,
            user_id: action.recipients ? action.recipients[0] : null, // If specific user, otherwise farm-wide
            related_entity_type: 'alert_rule',
            related_entity_id: ruleId,
            action_url: action.actionUrl || null
          });

          success = true;
          message = `Created notification alert: ${alert.id}`;
          break;

        case 'email':
          // Send email notification
          // This would integrate with an email service
          success = true;
          message = `Email notification would be sent to ${action.recipients.join(', ')}`;
          break;

        case 'sms':
          // Send SMS notification
          // This would integrate with an SMS service
          success = true;
          message = `SMS notification would be sent to ${action.recipients.join(', ')}`;
          break;

        case 'task_creation':
          // Create a task
          if (action.taskDetails) {
            // This would create a task in the task management system
            const taskTitle = action.taskDetails.title 
              ? replaceTemplateVariables(action.taskDetails.title, context)
              : `Task from alert: ${ruleName}`;

            const taskDescription = action.taskDetails.description
              ? replaceTemplateVariables(action.taskDetails.description, context)
              : generateAlertMessage(conditionResults);

            // Here you would create the task in your task system
            success = true;
            message = `Task would be created: ${taskTitle}`;
          } else {
            success = false;
            message = 'Task details not provided';
          }
          break;

        default:
          message = `Unknown action type: ${action.type}`;
      }
    } catch (error) {
      console.error(`Error executing action: ${error.message}`);
      message = `Error executing action: ${error.message}`;
    }

    results.push({
      action,
      success,
      message
    });
  }

  return results;
};

/**
 * Get the current value of a parameter
 * @param {string} parameter - Parameter name
 * @param {string} entityType - Type of entity
 * @param {string} farmId - ID of the farm
 * @returns {Promise<any>} - Current value
 */
const getParameterValue = async (parameter, entityType, farmId) => {
  try {
    // Create a cache key
    const cacheKey = `param_value_${entityType}_${parameter}_${farmId}`;

    // Check if data exists in cache
    const cachedValue = cache.get(cacheKey);
    if (cachedValue !== undefined) {
      console.log(`Using cached value for ${entityType}.${parameter}`);
      return cachedValue;
    }

    let value;

    switch (entityType) {
      case 'equipment':
        if (parameter === 'count') {
          // Get count of equipment
          value = await Equipment.count({ where: { farm_id: farmId } });
        } else if (parameter === 'maintenance_due') {
          // Get count of equipment due for maintenance
          value = await Equipment.count({ 
            where: { 
              farm_id: farmId,
              next_maintenance_date: { [Op.lte]: new Date() }
            } 
          });
        } else if (parameter.startsWith('telematics_')) {
          // Get latest telematics data for specific parameter
          const telematicsParam = parameter.replace('telematics_', '');
          const latestTelematics = await EquipmentTelematics.findOne({
            where: { farm_id: farmId },
            order: [['timestamp', 'DESC']]
          });

          value = latestTelematics ? latestTelematics[telematicsParam] : 0;
        } else {
          // Try to get the parameter directly from the equipment
          const equipment = await Equipment.findOne({
            where: { farm_id: farmId, id: parameter },
            order: [['updated_at', 'DESC']]
          });

          value = equipment ? equipment[parameter] || 0 : 0;
        }
        break;

      case 'inventory':
        if (parameter === 'count') {
          // Get count of inventory items
          value = await InventoryItem.count({ where: { farm_id: farmId } });
        } else if (parameter === 'low_stock_count') {
          // Get count of low stock items
          value = await InventoryItem.count({ 
            where: { 
              farm_id: farmId,
              quantity: { [Op.lt]: sequelize.col('reorder_point') }
            } 
          });
        } else {
          // Get quantity of specific inventory item
          const inventoryItem = await InventoryItem.findOne({
            where: { farm_id: farmId, id: parameter },
            order: [['updated_at', 'DESC']]
          });

          value = inventoryItem ? inventoryItem.quantity : 0;
        }
        break;

      case 'weather':
        // Get latest weather data for the farm
        const latestWeather = await Weather.findOne({
          where: { 
            latitude: { [Op.between]: [parseFloat(farmId) - 0.1, parseFloat(farmId) + 0.1] },
            longitude: { [Op.between]: [parseFloat(farmId) - 0.1, parseFloat(farmId) + 0.1] }
          },
          order: [['timestamp', 'DESC']]
        });

        if (latestWeather) {
          value = latestWeather[parameter] || 0;
        } else {
          // If no weather data in database, try to get from weather API
          const weatherData = await getWeatherData(farmId);
          value = weatherData[parameter] || 0;
        }
        break;

      case 'financial':
        if (parameter === 'transaction_count') {
          // Get count of transactions
          value = await Transaction.count({ where: { farm_id: farmId } });
        } else if (parameter === 'total_expenses') {
          // Get sum of expenses
          const expenses = await Transaction.sum('amount', { 
            where: { 
              farm_id: farmId,
              transaction_type: 'withdrawal'
            } 
          });

          value = expenses || 0;
        } else if (parameter === 'total_income') {
          // Get sum of income
          const income = await Transaction.sum('amount', { 
            where: { 
              farm_id: farmId,
              transaction_type: 'deposit'
            } 
          });

          value = income || 0;
        } else {
          // Try to get the parameter directly from transactions
          const transaction = await Transaction.findOne({
            where: { farm_id: farmId, id: parameter },
            order: [['transaction_date', 'DESC']]
          });

          value = transaction ? transaction.amount : 0;
        }
        break;

      default:
        console.warn(`Unknown entity type: ${entityType}`);
        value = 0;
    }

    // Store in cache
    cache.set(cacheKey, value);

    return value;
  } catch (error) {
    console.error(`Error getting parameter value for ${entityType}.${parameter}:`, error);
    // Return a default value in case of error
    return 0;
  }
};

/**
 * Compare two values using the specified operator
 * @param {any} value1 - First value
 * @param {string} operator - Operator
 * @param {any} value2 - Second value
 * @returns {boolean} - Result of comparison
 */
const compareValues = (value1, operator, value2) => {
  switch (operator) {
    case '>':
      return value1 > value2;
    case '<':
      return value1 < value2;
    case '=':
      return value1 == value2;
    case '>=':
      return value1 >= value2;
    case '<=':
      return value1 <= value2;
    case 'contains':
      return String(value1).includes(String(value2));
    case 'not_contains':
      return !String(value1).includes(String(value2));
    default:
      return false;
  }
};

/**
 * Check if current time matches the schedule
 * @param {string} scheduleType - Type of schedule
 * @param {string} scheduleValue - Schedule value
 * @returns {boolean} - Whether the schedule matches
 */
const checkSchedule = (scheduleType, scheduleValue) => {
  const now = new Date();

  switch (scheduleType) {
    case 'daily':
      // scheduleValue format: "HH:MM"
      const [hour, minute] = scheduleValue.split(':').map(Number);
      return now.getHours() === hour && now.getMinutes() === minute;

    case 'weekly':
      // scheduleValue format: "DAY,HH:MM" (DAY is 0-6, 0 = Sunday)
      const [day, time] = scheduleValue.split(',');
      const [weeklyHour, weeklyMinute] = time.split(':').map(Number);
      return now.getDay() === Number(day) && 
             now.getHours() === weeklyHour && 
             now.getMinutes() === weeklyMinute;

    case 'monthly':
      // scheduleValue format: "DATE,HH:MM" (DATE is 1-31)
      const [date, monthlyTime] = scheduleValue.split(',');
      const [monthlyHour, monthlyMinute] = monthlyTime.split(':').map(Number);
      return now.getDate() === Number(date) && 
             now.getHours() === monthlyHour && 
             now.getMinutes() === monthlyMinute;

    default:
      return false;
  }
};

/**
 * Check if status has changed
 * @param {string} parameter - Parameter name
 * @param {string} value - Expected value
 * @param {string} entityType - Type of entity
 * @param {string} farmId - ID of the farm
 * @returns {Promise<boolean>} - Whether the status has changed
 */
const checkStatusChange = async (parameter, value, entityType, farmId) => {
  try {
    // Create a cache key
    const cacheKey = `status_change_${entityType}_${parameter}_${value}_${farmId}`;

    // Check if result exists in cache
    const cachedResult = cache.get(cacheKey);
    if (cachedResult !== undefined) {
      console.log(`Using cached status change result for ${entityType}.${parameter}`);
      return cachedResult;
    }

    let result = false;

    switch (entityType) {
      case 'equipment':
        // Check if equipment status has changed
        const equipment = await Equipment.findOne({
          where: { farm_id: farmId, id: parameter }
        });

        if (equipment) {
          // Check if status matches expected value
          result = equipment.status === value;

          // Check if status was updated recently (within last 24 hours)
          const updatedAt = new Date(equipment.updated_at);
          const yesterday = new Date();
          yesterday.setDate(yesterday.getDate() - 1);

          result = result && updatedAt > yesterday;
        }
        break;

      case 'inventory':
        // Check if inventory status has changed
        const inventoryItem = await InventoryItem.findOne({
          where: { farm_id: farmId, id: parameter }
        });

        if (inventoryItem) {
          // Check if quantity is below reorder point (low stock)
          if (value === 'low_stock') {
            result = inventoryItem.quantity < inventoryItem.reorder_point;
          } else if (value === 'out_of_stock') {
            result = inventoryItem.quantity <= 0;
          } else if (value === 'in_stock') {
            result = inventoryItem.quantity > 0;
          } else {
            result = inventoryItem.status === value;
          }

          // Check if status was updated recently (within last 24 hours)
          const updatedAt = new Date(inventoryItem.updated_at);
          const yesterday = new Date();
          yesterday.setDate(yesterday.getDate() - 1);

          result = result && updatedAt > yesterday;
        }
        break;

      case 'weather':
        // Check if weather condition has changed
        const latestWeather = await Weather.findOne({
          where: { 
            latitude: { [Op.between]: [parseFloat(farmId) - 0.1, parseFloat(farmId) + 0.1] },
            longitude: { [Op.between]: [parseFloat(farmId) - 0.1, parseFloat(farmId) + 0.1] }
          },
          order: [['timestamp', 'DESC']]
        });

        if (latestWeather) {
          // Check if condition matches expected value
          result = latestWeather.conditions === value;

          // Check if condition was updated recently (within last 6 hours)
          const timestamp = new Date(latestWeather.timestamp);
          const sixHoursAgo = new Date();
          sixHoursAgo.setHours(sixHoursAgo.getHours() - 6);

          result = result && timestamp > sixHoursAgo;
        } else {
          // If no weather data in database, try to get from weather API
          const weatherData = await getWeatherData(farmId);
          result = weatherData.conditions === value;
        }
        break;

      case 'financial':
        // Check if transaction status has changed
        if (parameter === 'balance') {
          // Check if balance is above or below threshold
          const totalIncome = await Transaction.sum('amount', { 
            where: { 
              farm_id: farmId,
              transaction_type: 'deposit'
            } 
          }) || 0;

          const totalExpenses = await Transaction.sum('amount', { 
            where: { 
              farm_id: farmId,
              transaction_type: 'withdrawal'
            } 
          }) || 0;

          const balance = totalIncome - totalExpenses;

          // Parse value as a threshold (e.g., "below_1000" or "above_5000")
          if (value.startsWith('below_')) {
            const threshold = parseFloat(value.replace('below_', ''));
            result = balance < threshold;
          } else if (value.startsWith('above_')) {
            const threshold = parseFloat(value.replace('above_', ''));
            result = balance > threshold;
          }

          // Check if there was a recent transaction (within last 24 hours)
          const latestTransaction = await Transaction.findOne({
            where: { farm_id: farmId },
            order: [['transaction_date', 'DESC']]
          });

          if (latestTransaction) {
            const transactionDate = new Date(latestTransaction.transaction_date);
            const yesterday = new Date();
            yesterday.setDate(yesterday.getDate() - 1);

            result = result && transactionDate > yesterday;
          }
        } else {
          // Check specific transaction status
          const transaction = await Transaction.findOne({
            where: { farm_id: farmId, id: parameter }
          });

          if (transaction) {
            result = transaction.status === value;

            // Check if status was updated recently (within last 24 hours)
            const updatedAt = new Date(transaction.updated_at);
            const yesterday = new Date();
            yesterday.setDate(yesterday.getDate() - 1);

            result = result && updatedAt > yesterday;
          }
        }
        break;

      default:
        console.warn(`Unknown entity type: ${entityType}`);
        result = false;
    }

    // Store in cache (with shorter TTL for status changes - 1 minute)
    cache.set(cacheKey, result, 60);

    return result;
  } catch (error) {
    console.error(`Error checking status change for ${entityType}.${parameter}:`, error);
    // Return false in case of error
    return false;
  }
};

/**
 * Get weather data for a farm
 * @param {string} farmId - ID of the farm
 * @returns {Promise<Object>} - Weather data
 */
const getWeatherData = async (farmId) => {
  try {
    // Create a cache key
    const cacheKey = `weather_data_${farmId}`;

    // Check if data exists in cache
    const cachedData = cache.get(cacheKey);
    if (cachedData) {
      console.log(`Using cached weather data for farm ${farmId}`);
      return cachedData;
    }

    // Try to get farm location
    const farm = await Farm.findByPk(farmId);
    if (!farm || !farm.location_data) {
      console.warn(`Farm ${farmId} has no location data`);
      return getDefaultWeatherData();
    }

    const latitude = farm.location_data.latitude || farm.location_data.center?.latitude;
    const longitude = farm.location_data.longitude || farm.location_data.center?.longitude;

    if (!latitude || !longitude) {
      console.warn(`Farm ${farmId} has invalid location data`);
      return getDefaultWeatherData();
    }

    // First, try to get weather data from our database
    const latestWeather = await Weather.findOne({
      where: { 
        latitude: { [Op.between]: [parseFloat(latitude) - 0.1, parseFloat(latitude) + 0.1] },
        longitude: { [Op.between]: [parseFloat(longitude) - 0.1, parseFloat(longitude) + 0.1] }
      },
      order: [['timestamp', 'DESC']]
    });

    if (latestWeather) {
      console.log(`Found weather data in database for location near ${latitude},${longitude}`);

      const weatherData = {
        temperature: latestWeather.temperature,
        humidity: latestWeather.humidity,
        precipitation: latestWeather.precipitation,
        windSpeed: latestWeather.wind_speed,
        windDirection: latestWeather.wind_direction,
        conditions: latestWeather.conditions,
        timestamp: latestWeather.timestamp
      };

      // Store in cache
      cache.set(cacheKey, weatherData);

      return weatherData;
    }

    // If no data in database, try to fetch from NWS API
    try {
      console.log(`Fetching weather data from NWS API for ${latitude},${longitude}`);

      // Step 1: Get the grid point for the location
      const pointsUrl = `https://api.weather.gov/points/${latitude},${longitude}`;
      const pointsResponse = await axios.get(pointsUrl, {
        headers: {
          'User-Agent': '(NxtAcre Farm Management, <EMAIL>)',
          'Accept': 'application/geo+json'
        }
      });

      const { gridId, gridX, gridY } = pointsResponse.data.properties;

      // Step 2: Get the forecast for the grid point
      const forecastUrl = `https://api.weather.gov/gridpoints/${gridId}/${gridX},${gridY}/forecast/hourly`;
      const forecastResponse = await axios.get(forecastUrl, {
        headers: {
          'User-Agent': '(NxtAcre Farm Management, <EMAIL>)',
          'Accept': 'application/geo+json'
        }
      });

      // Get the current hour's forecast
      const currentForecast = forecastResponse.data.properties.periods[0];

      // Extract weather data
      const weatherData = {
        temperature: currentForecast.temperature,
        humidity: currentForecast.relativeHumidity?.value || 0,
        precipitation: currentForecast.probabilityOfPrecipitation?.value || 0,
        windSpeed: parseInt(currentForecast.windSpeed.replace(/[^0-9]/g, '')),
        windDirection: currentForecast.windDirection,
        conditions: currentForecast.shortForecast,
        timestamp: new Date().toISOString()
      };

      // Store in database for future use
      try {
        await Weather.create({
          latitude,
          longitude,
          timestamp: new Date(),
          temperature: weatherData.temperature,
          precipitation: weatherData.precipitation,
          humidity: weatherData.humidity,
          wind_speed: weatherData.windSpeed,
          wind_direction: weatherData.windDirection,
          conditions: weatherData.conditions,
          source: 'NWS API'
        });
        console.log(`Stored weather data from NWS API in database`);
      } catch (dbError) {
        console.error('Error storing weather data in database:', dbError);
      }

      // Store in cache
      cache.set(cacheKey, weatherData);

      return weatherData;
    } catch (nwsError) {
      console.error('Error fetching from NWS API:', nwsError);

      // Try OpenWeatherMap API as fallback
      try {
        const OPENWEATHER_API_KEY = process.env.OPENWEATHER_API_KEY;

        if (!OPENWEATHER_API_KEY) {
          console.warn('OPENWEATHER_API_KEY is not set');
          return getDefaultWeatherData();
        }

        console.log(`Fetching weather data from OpenWeatherMap API for ${latitude},${longitude}`);

        const url = `https://api.openweathermap.org/data/2.5/weather?lat=${latitude}&lon=${longitude}&appid=${OPENWEATHER_API_KEY}&units=imperial`;
        const response = await axios.get(url);

        if (response.data) {
          const weatherData = {
            temperature: response.data.main.temp,
            humidity: response.data.main.humidity,
            precipitation: response.data.rain ? response.data.rain['1h'] || 0 : 0,
            windSpeed: response.data.wind.speed,
            windDirection: getWindDirection(response.data.wind.deg),
            conditions: response.data.weather[0].description,
            timestamp: new Date().toISOString()
          };

          // Store in database for future use
          try {
            await Weather.create({
              latitude,
              longitude,
              timestamp: new Date(),
              temperature: weatherData.temperature,
              precipitation: weatherData.precipitation,
              humidity: weatherData.humidity,
              wind_speed: weatherData.windSpeed,
              wind_direction: weatherData.windDirection,
              conditions: weatherData.conditions,
              source: 'OpenWeatherMap API'
            });
            console.log(`Stored weather data from OpenWeatherMap API in database`);
          } catch (dbError) {
            console.error('Error storing weather data in database:', dbError);
          }

          // Store in cache
          cache.set(cacheKey, weatherData);

          return weatherData;
        }
      } catch (owmError) {
        console.error('Error fetching from OpenWeatherMap API:', owmError);
      }
    }

    // If all API calls fail, return default data
    return getDefaultWeatherData();
  } catch (error) {
    console.error('Error getting weather data:', error);
    return getDefaultWeatherData();
  }
};

/**
 * Get default weather data
 * @returns {Object} - Default weather data
 */
const getDefaultWeatherData = () => {
  return {
    temperature: 70,
    humidity: 50,
    precipitation: 0,
    windSpeed: 5,
    windDirection: 'N',
    conditions: 'Clear',
    timestamp: new Date().toISOString()
  };
};

/**
 * Convert wind degrees to direction
 * @param {number} degrees - Wind direction in degrees
 * @returns {string} - Wind direction as cardinal direction
 */
const getWindDirection = (degrees) => {
  const directions = ['N', 'NNE', 'NE', 'ENE', 'E', 'ESE', 'SE', 'SSE', 'S', 'SSW', 'SW', 'WSW', 'W', 'WNW', 'NW', 'NNW'];
  const index = Math.round(degrees / 22.5) % 16;
  return directions[index];
};

/**
 * Get inventory level for a product
 * @param {string} productId - ID of the product
 * @param {string} farmId - ID of the farm
 * @returns {Promise<number>} - Inventory level
 */
const getInventoryLevel = async (productId, farmId) => {
  try {
    // Create a cache key
    const cacheKey = `inventory_level_${productId}_${farmId}`;

    // Check if data exists in cache
    const cachedLevel = cache.get(cacheKey);
    if (cachedLevel !== undefined) {
      console.log(`Using cached inventory level for product ${productId}`);
      return cachedLevel;
    }

    // Get inventory item from database
    const inventoryItem = await InventoryItem.findOne({
      where: { 
        id: productId,
        farm_id: farmId
      }
    });

    if (inventoryItem) {
      console.log(`Found inventory item ${productId} in database`);

      // Store in cache
      cache.set(cacheKey, inventoryItem.quantity);

      return inventoryItem.quantity;
    }

    // If product ID is a category, get sum of all items in that category
    if (productId.startsWith('category_')) {
      const categoryId = productId.replace('category_', '');

      const items = await InventoryItem.findAll({
        where: { 
          category_id: categoryId,
          farm_id: farmId
        }
      });

      if (items && items.length > 0) {
        console.log(`Found ${items.length} inventory items in category ${categoryId}`);

        const totalQuantity = items.reduce((sum, item) => sum + item.quantity, 0);

        // Store in cache
        cache.set(cacheKey, totalQuantity);

        return totalQuantity;
      }
    }

    console.warn(`Inventory item ${productId} not found for farm ${farmId}`);
    return 0;
  } catch (error) {
    console.error(`Error getting inventory level for product ${productId}:`, error);
    return 0;
  }
};

/**
 * Replace template variables in a string
 * @param {string} template - Template string
 * @param {Object} context - Context object
 * @returns {string} - String with variables replaced
 */
const replaceTemplateVariables = (template, context) => {
  let result = template;

  // Replace variables in the format {{variableName}}
  const variableRegex = /\{\{([^}]+)\}\}/g;
  result = result.replace(variableRegex, (match, variableName) => {
    const parts = variableName.split('.');
    let value = context;

    for (const part of parts) {
      if (value && value[part] !== undefined) {
        value = value[part];
      } else {
        return match; // Keep the original if variable not found
      }
    }

    return String(value);
  });

  return result;
};

/**
 * Generate a message from condition results
 * @param {Array} conditionResults - Array of condition results
 * @returns {string} - Generated message
 */
const generateAlertMessage = (conditionResults) => {
  return conditionResults
    .filter(result => result.met)
    .map(result => result.message)
    .join('\n');
};
