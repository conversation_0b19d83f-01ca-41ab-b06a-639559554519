import Integration from '../models/Integration.js';
import { Op } from 'sequelize';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Get all integrations
export const getIntegrations = async (req, res) => {
  try {
    const { farmId } = req.query;

    const whereClause = {};

    if (farmId) {
      whereClause[Op.or] = [
        { farm_id: farmId },
        { farm_id: null }
      ];
    }

    const integrations = await Integration.findAll({
      where: whereClause,
      order: [['name', 'ASC']]
    });

    res.status(200).json(integrations);
  } catch (error) {
    console.error('Error fetching integrations:', error);
    res.status(500).json({ message: 'Failed to fetch integrations', error: error.message });
  }
};

// Get a single integration by ID
export const getIntegration = async (req, res) => {
  try {
    const { id } = req.params;

    const integration = await Integration.findByPk(id);

    if (!integration) {
      return res.status(404).json({ message: 'Integration not found' });
    }

    // Get the plugin folder name from the integration name
    const pluginFolderName = integration.name.toLowerCase().replace(/\s+/g, '-');
    const pluginsDir = path.join(__dirname, '../../plugins');
    const pluginDir = path.join(pluginsDir, pluginFolderName);

    // Check if plugin directory exists
    if (fs.existsSync(pluginDir)) {
      // Try to read settings schema
      const schemaPath = path.join(pluginDir, 'settings_schema.json');

      if (fs.existsSync(schemaPath)) {
        try {
          const settingsSchema = JSON.parse(fs.readFileSync(schemaPath, 'utf8'));

          // Return integration with settings schema
          return res.status(200).json({
            ...integration.toJSON(),
            settings_schema: settingsSchema
          });
        } catch (schemaError) {
          console.error(`Error reading settings schema for integration ${id}:`, schemaError);
        }
      }
    }

    // If we couldn't find or read the schema, return the integration without it
    res.status(200).json(integration);
  } catch (error) {
    console.error('Error fetching integration:', error);
    res.status(500).json({ message: 'Failed to fetch integration', error: error.message });
  }
};

// Create a new integration
export const createIntegration = async (req, res) => {
  try {
    const { name, description, version, author, entry_point, icon, farm_id, settings, is_global } = req.body;

    if (!name || !version || !author || !entry_point) {
      return res.status(400).json({ message: 'Name, version, author, and entry_point are required' });
    }

    const integration = await Integration.create({
      name,
      description,
      version,
      author,
      entry_point,
      icon,
      farm_id,
      settings: settings || {},
      is_global: is_global || false,
      enabled: false
    });

    res.status(201).json(integration);
  } catch (error) {
    console.error('Error creating integration:', error);
    res.status(500).json({ message: 'Failed to create integration', error: error.message });
  }
};

// Update an integration
export const updateIntegration = async (req, res) => {
  try {
    const { id } = req.params;
    const { name, description, version, author, entry_point, icon, farm_id, settings, enabled, is_global } = req.body;

    const integration = await Integration.findByPk(id);

    if (!integration) {
      return res.status(404).json({ message: 'Integration not found' });
    }

    await integration.update({
      name: name || integration.name,
      description: description !== undefined ? description : integration.description,
      version: version || integration.version,
      author: author || integration.author,
      entry_point: entry_point || integration.entry_point,
      icon: icon !== undefined ? icon : integration.icon,
      farm_id: farm_id !== undefined ? farm_id : integration.farm_id,
      settings: settings || integration.settings,
      enabled: enabled !== undefined ? enabled : integration.enabled,
      is_global: is_global !== undefined ? is_global : integration.is_global
    });

    res.status(200).json(integration);
  } catch (error) {
    console.error('Error updating integration:', error);
    res.status(500).json({ message: 'Failed to update integration', error: error.message });
  }
};

// Delete an integration
export const deleteIntegration = async (req, res) => {
  try {
    const { id } = req.params;

    const integration = await Integration.findByPk(id);

    if (!integration) {
      return res.status(404).json({ message: 'Integration not found' });
    }

    await integration.destroy();

    res.status(200).json({ message: 'Integration deleted successfully' });
  } catch (error) {
    console.error('Error deleting integration:', error);
    res.status(500).json({ message: 'Failed to delete integration', error: error.message });
  }
};

// Enable or disable an integration
export const toggleIntegration = async (req, res) => {
  try {
    const { id } = req.params;
    const { enabled } = req.body;

    if (enabled === undefined) {
      return res.status(400).json({ message: 'Enabled status is required' });
    }

    const integration = await Integration.findByPk(id);

    if (!integration) {
      return res.status(404).json({ message: 'Integration not found' });
    }

    await integration.update({ enabled });

    res.status(200).json(integration);
  } catch (error) {
    console.error('Error toggling integration:', error);
    res.status(500).json({ message: 'Failed to toggle integration', error: error.message });
  }
};

// Update integration settings
export const updateIntegrationSettings = async (req, res) => {
  try {
    const { id } = req.params;
    const { settings } = req.body;

    if (!settings) {
      return res.status(400).json({ message: 'Settings are required' });
    }

    const integration = await Integration.findByPk(id);

    if (!integration) {
      return res.status(404).json({ message: 'Integration not found' });
    }

    await integration.update({ settings });

    res.status(200).json(integration);
  } catch (error) {
    console.error('Error updating integration settings:', error);
    res.status(500).json({ message: 'Failed to update integration settings', error: error.message });
  }
};

// Get available integration plugins
export const getAvailablePlugins = async (req, res) => {
  try {
    const pluginsDir = path.join(__dirname, '../../plugins');

    // Check if plugins directory exists
    if (!fs.existsSync(pluginsDir)) {
      return res.status(200).json([]);
    }

    // Read plugins directory
    const pluginFolders = fs.readdirSync(pluginsDir, { withFileTypes: true })
      .filter(dirent => dirent.isDirectory())
      .map(dirent => dirent.name);

    const availablePlugins = [];

    // Read plugin metadata from each plugin folder
    for (const pluginFolder of pluginFolders) {
      const metadataPath = path.join(pluginsDir, pluginFolder, 'metadata.json');

      if (fs.existsSync(metadataPath)) {
        try {
          const metadata = JSON.parse(fs.readFileSync(metadataPath, 'utf8'));
          availablePlugins.push({
            ...metadata,
            folder: pluginFolder
          });
        } catch (error) {
          console.error(`Error reading metadata for plugin ${pluginFolder}:`, error);
        }
      }
    }

    res.status(200).json(availablePlugins);
  } catch (error) {
    console.error('Error fetching available plugins:', error);
    res.status(500).json({ message: 'Failed to fetch available plugins', error: error.message });
  }
};

// Install a plugin
export const installPlugin = async (req, res) => {
  try {
    const { folder } = req.body;

    if (!folder) {
      return res.status(400).json({ message: 'Plugin folder is required' });
    }

    const pluginsDir = path.join(__dirname, '../../plugins');
    const pluginDir = path.join(pluginsDir, folder);

    // Check if plugin directory exists
    if (!fs.existsSync(pluginDir)) {
      return res.status(404).json({ message: 'Plugin not found' });
    }

    // Read plugin metadata
    const metadataPath = path.join(pluginDir, 'metadata.json');

    if (!fs.existsSync(metadataPath)) {
      return res.status(400).json({ message: 'Plugin metadata not found' });
    }

    const metadata = JSON.parse(fs.readFileSync(metadataPath, 'utf8'));

    // Check if plugin is already installed
    const existingIntegration = await Integration.findOne({
      where: {
        name: metadata.name,
        version: metadata.version
      }
    });

    if (existingIntegration) {
      return res.status(400).json({ message: 'Plugin is already installed' });
    }

    // Create integration record
    const integration = await Integration.create({
      name: metadata.name,
      description: metadata.description,
      version: metadata.version,
      author: metadata.author,
      entry_point: metadata.entry_point,
      icon: metadata.icon,
      settings: metadata.default_settings || {},
      is_global: metadata.is_global || false,
      enabled: false
    });

    res.status(201).json(integration);
  } catch (error) {
    console.error('Error installing plugin:', error);
    res.status(500).json({ message: 'Failed to install plugin', error: error.message });
  }
};
