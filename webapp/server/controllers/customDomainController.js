import Farm from '../models/Farm.js';
import SubscriptionPlan from '../models/SubscriptionPlan.js';
import digitalOceanService from '../services/digitalOceanService.js';
import { Op } from 'sequelize';

/**
 * Check if a farm has the custom domain feature enabled
 * @param {string} farmId - The ID of the farm
 * @returns {boolean} - Whether the farm has the custom domain feature enabled
 */
const hasCustomDomainFeature = async (farmId) => {
  try {
    const farm = await Farm.findByPk(farmId, {
      include: [{
        model: SubscriptionPlan,
        attributes: ['features']
      }]
    });

    if (!farm || !farm.SubscriptionPlan) {
      return false;
    }

    const features = farm.SubscriptionPlan.features;
    return features && features.custom_domain_enabled === true;
  } catch (error) {
    console.error('Error checking custom domain feature:', error);
    return false;
  }
};

/**
 * Set up a custom domain for a farm
 * @param {Object} req - The request object
 * @param {Object} res - The response object
 */
export const setupCustomDomain = async (req, res) => {
  try {
    const { farmId } = req.params;
    const { customDomain } = req.body;

    // Check if the farm exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Check if the farm has the custom domain feature
    const hasFeature = await hasCustomDomainFeature(farmId);
    if (!hasFeature) {
      return res.status(403).json({ 
        error: 'Custom domain feature not available with current subscription plan',
        upgradeRequired: true
      });
    }

    // Check if the domain is already in use
    const existingFarm = await Farm.findOne({
      where: {
        custom_domain: customDomain,
        id: { [Op.ne]: farmId }
      }
    });

    if (existingFarm) {
      return res.status(400).json({ error: 'This domain is already in use by another farm' });
    }

    // Initialize the Digital Ocean service
    const initialized = await digitalOceanService.initialize();
    if (!initialized) {
      return res.status(500).json({ error: 'Failed to initialize Digital Ocean service' });
    }

    // Set up the custom domain
    const result = await digitalOceanService.setupCustomDomain(customDomain, farm.subdomain, farmId);
    
    if (!result.success) {
      return res.status(400).json({ error: result.message });
    }

    // Update the farm with the custom domain
    await farm.update({
      custom_domain: customDomain,
      custom_domain_verified: false
    });

    return res.status(200).json({
      message: 'Custom domain set up successfully',
      domain: customDomain,
      verified: false
    });
  } catch (error) {
    console.error('Error setting up custom domain:', error);
    return res.status(500).json({ error: 'Failed to set up custom domain' });
  }
};

/**
 * Verify a custom domain for a farm
 * @param {Object} req - The request object
 * @param {Object} res - The response object
 */
export const verifyCustomDomain = async (req, res) => {
  try {
    const { farmId } = req.params;

    // Check if the farm exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Check if the farm has a custom domain
    if (!farm.custom_domain) {
      return res.status(400).json({ error: 'Farm does not have a custom domain' });
    }

    // Initialize the Digital Ocean service
    const initialized = await digitalOceanService.initialize();
    if (!initialized) {
      return res.status(500).json({ error: 'Failed to initialize Digital Ocean service' });
    }

    // Verify the custom domain
    const result = await digitalOceanService.verifyCustomDomain(farm.custom_domain, farmId);
    
    if (!result.success) {
      return res.status(400).json({ error: result.message });
    }

    // Update the farm with the verified status
    await farm.update({
      custom_domain_verified: result.verified
    });

    return res.status(200).json({
      message: 'Custom domain verified successfully',
      domain: farm.custom_domain,
      verified: result.verified
    });
  } catch (error) {
    console.error('Error verifying custom domain:', error);
    return res.status(500).json({ error: 'Failed to verify custom domain' });
  }
};

/**
 * Remove a custom domain from a farm
 * @param {Object} req - The request object
 * @param {Object} res - The response object
 */
export const removeCustomDomain = async (req, res) => {
  try {
    const { farmId } = req.params;

    // Check if the farm exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Check if the farm has a custom domain
    if (!farm.custom_domain) {
      return res.status(400).json({ error: 'Farm does not have a custom domain' });
    }

    // Update the farm to remove the custom domain
    await farm.update({
      custom_domain: null,
      custom_domain_verified: false
    });

    return res.status(200).json({
      message: 'Custom domain removed successfully'
    });
  } catch (error) {
    console.error('Error removing custom domain:', error);
    return res.status(500).json({ error: 'Failed to remove custom domain' });
  }
};

/**
 * Get the custom domain for a farm
 * @param {Object} req - The request object
 * @param {Object} res - The response object
 */
export const getCustomDomain = async (req, res) => {
  try {
    const { farmId } = req.params;

    // Check if the farm exists
    const farm = await Farm.findByPk(farmId, {
      include: [{
        model: SubscriptionPlan,
        attributes: ['features']
      }]
    });

    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Check if the farm has the custom domain feature
    const hasFeature = farm.SubscriptionPlan && 
                       farm.SubscriptionPlan.features && 
                       farm.SubscriptionPlan.features.custom_domain_enabled === true;

    return res.status(200).json({
      customDomain: farm.custom_domain,
      verified: farm.custom_domain_verified,
      featureEnabled: hasFeature
    });
  } catch (error) {
    console.error('Error getting custom domain:', error);
    return res.status(500).json({ error: 'Failed to get custom domain' });
  }
};