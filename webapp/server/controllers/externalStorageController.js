import { sequelize } from '../config/database.js';
import Document from '../models/Document.js';
import DocumentFolder from '../models/DocumentFolder.js';
import Integration from '../models/Integration.js';
import UserFarm from '../models/UserFarm.js';

// Helper function to check if a user has access to a farm
const checkUserFarmAccess = async (userId, farmId) => {
  if (!userId || !farmId) return false;

  const userFarm = await UserFarm.findOne({
    where: {
      user_id: userId,
      farm_id: farmId
    }
  });

  return !!userFarm;
};

import {
  listGoogleDriveFiles,
  listDropboxFiles,
  getGoogleDriveFileMetadata,
  getDropboxFileMetadata,
  linkExternalFile,
  importExternalFile,
  bulkImportExternalFiles
} from '../utils/externalStorageUtils.js';

/**
 * List files from Google Drive
 */
export const listGoogleDriveFilesController = async (req, res) => {
  try {
    const { farmId } = req.params; // Changed from tenantId to farmId
    const { folderId = 'root', pageToken, pageSize = 100 } = req.query;

    // Validate farm access
    const hasAccess = await checkUserFarmAccess(req.user.id, farmId);
    if (!hasAccess && !req.user.is_global_admin) {
      return res.status(403).json({ error: 'You do not have access to this farm' });
    }

    // Check if user has Google Drive integration
    const integration = await Integration.findOne({
      where: {
        user_id: req.user.id,
        farm_id: farmId, // Changed from tenant_id to farm_id
        provider: 'google_drive',
        is_active: true
      }
    });

    if (!integration) {
      return res.status(400).json({ error: 'Google Drive integration not found or inactive' });
    }

    // List files
    const result = await listGoogleDriveFiles(req.user.id, farmId, folderId, pageSize, pageToken); // Changed from tenantId to farmId

    if (!result.success) {
      return res.status(500).json({ error: result.error });
    }

    return res.status(200).json({
      files: result.files,
      nextPageToken: result.nextPageToken
    });
  } catch (error) {
    console.error('Error listing Google Drive files:', error);
    return res.status(500).json({ error: error.message });
  }
};

/**
 * List files from Dropbox
 */
export const listDropboxFilesController = async (req, res) => {
  try {
    const { farmId } = req.params; // Changed from tenantId to farmId
    const { path = '', cursor, limit = 100 } = req.query;

    // Validate farm access
    const hasAccess = await checkUserFarmAccess(req.user.id, farmId);
    if (!hasAccess && !req.user.is_global_admin) {
      return res.status(403).json({ error: 'You do not have access to this farm' });
    }

    // Check if user has Dropbox integration
    const integration = await Integration.findOne({
      where: {
        user_id: req.user.id,
        farm_id: farmId, // Changed from tenant_id to farm_id
        provider: 'dropbox',
        is_active: true
      }
    });

    if (!integration) {
      return res.status(400).json({ error: 'Dropbox integration not found or inactive' });
    }

    // List files
    const result = await listDropboxFiles(req.user.id, farmId, path, limit, cursor); // Changed from tenantId to farmId

    if (!result.success) {
      return res.status(500).json({ error: result.error });
    }

    return res.status(200).json({
      files: result.files,
      cursor: result.cursor,
      hasMore: result.hasMore
    });
  } catch (error) {
    console.error('Error listing Dropbox files:', error);
    return res.status(500).json({ error: error.message });
  }
};

/**
 * Link a Google Drive file to the document system
 */
export const linkGoogleDriveFile = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { farmId } = req.params; // Changed from tenantId to farmId
    const { fileId, folderId, description } = req.body;

    // Validate farm access
    const hasAccess = await checkUserFarmAccess(req.user.id, farmId);
    if (!hasAccess && !req.user.is_global_admin) {
      await transaction.rollback();
      return res.status(403).json({ error: 'You do not have access to this farm' });
    }

    // Check if folder exists if provided
    if (folderId) {
      const folder = await DocumentFolder.findOne({
        where: {
          id: folderId,
          farm_id: farmId // Changed from tenant_id to farm_id
        }
      });

      if (!folder) {
        await transaction.rollback();
        return res.status(404).json({ error: 'Folder not found' });
      }
    }

    // Get file metadata
    const metadataResult = await getGoogleDriveFileMetadata(req.user.id, farmId, fileId); // Changed from tenantId to farmId

    if (!metadataResult.success) {
      await transaction.rollback();
      return res.status(500).json({ error: metadataResult.error });
    }

    // Link file
    const linkResult = await linkExternalFile(
      req.user.id,
      farmId, // Changed from tenantId to farmId
      'google_drive',
      fileId,
      null, // No path for Google Drive
      {
        name: metadataResult.file.name,
        description: description || '',
        size: metadataResult.file.size,
        mimeType: metadataResult.file.mimeType
      },
      folderId
    );

    if (!linkResult.success) {
      await transaction.rollback();
      return res.status(500).json({ error: linkResult.error });
    }

    await transaction.commit();

    return res.status(201).json({
      message: 'Google Drive file linked successfully',
      document: linkResult.document
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error linking Google Drive file:', error);
    return res.status(500).json({ error: error.message });
  }
};

/**
 * Link a Dropbox file to the document system
 */
export const linkDropboxFile = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { farmId } = req.params; // Changed from tenantId to farmId
    const { path, folderId, description } = req.body;

    // Validate farm access
    const hasAccess = await checkUserFarmAccess(req.user.id, farmId);
    if (!hasAccess && !req.user.is_global_admin) {
      await transaction.rollback();
      return res.status(403).json({ error: 'You do not have access to this farm' });
    }

    // Check if folder exists if provided
    if (folderId) {
      const folder = await DocumentFolder.findOne({
        where: {
          id: folderId,
          farm_id: farmId // Changed from tenant_id to farm_id
        }
      });

      if (!folder) {
        await transaction.rollback();
        return res.status(404).json({ error: 'Folder not found' });
      }
    }

    // Get file metadata
    const metadataResult = await getDropboxFileMetadata(req.user.id, tenantId, path);

    if (!metadataResult.success) {
      await transaction.rollback();
      return res.status(500).json({ error: metadataResult.error });
    }

    // Link file
    const linkResult = await linkExternalFile(
      req.user.id,
      tenantId,
      'dropbox',
      metadataResult.file.id,
      path,
      {
        name: metadataResult.file.name,
        description: description || '',
        size: metadataResult.file.size,
        mimeType: metadataResult.file.mimeType
      },
      folderId
    );

    if (!linkResult.success) {
      await transaction.rollback();
      return res.status(500).json({ error: linkResult.error });
    }

    await transaction.commit();

    return res.status(201).json({
      message: 'Dropbox file linked successfully',
      document: linkResult.document
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error linking Dropbox file:', error);
    return res.status(500).json({ error: error.message });
  }
};

/**
 * Import an external file to the local system
 */
export const importExternalFileController = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { farmId, documentId } = req.params; // Changed from tenantId to farmId

    // Validate farm access
    const hasAccess = await checkUserFarmAccess(req.user.id, farmId);
    if (!hasAccess && !req.user.is_global_admin) {
      await transaction.rollback();
      return res.status(403).json({ error: 'You do not have access to this farm' });
    }

    // Import file
    const importResult = await importExternalFile(req.user.id, farmId, documentId);

    if (!importResult.success) {
      await transaction.rollback();
      return res.status(500).json({ error: importResult.error });
    }

    await transaction.commit();

    return res.status(200).json({
      message: 'External file imported successfully',
      document: importResult.document
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error importing external file:', error);
    return res.status(500).json({ error: error.message });
  }
};

/**
 * Bulk import external files to the local system
 */
export const bulkImportExternalFilesController = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { farmId } = req.params; // Changed from tenantId to farmId
    const { documentIds } = req.body;

    if (!documentIds || !Array.isArray(documentIds) || documentIds.length === 0) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Document IDs array is required' });
    }

    // Validate farm access
    const hasAccess = await checkUserFarmAccess(req.user.id, farmId);
    if (!hasAccess && !req.user.is_global_admin) {
      await transaction.rollback();
      return res.status(403).json({ error: 'You do not have access to this farm' });
    }

    // Verify all documents exist and belong to the farm
    const documents = await Document.findAll({
      where: {
        id: documentIds,
        farm_id: farmId,
        is_external: true
      }
    });

    if (documents.length !== documentIds.length) {
      await transaction.rollback();
      return res.status(400).json({ 
        error: 'One or more documents not found, do not belong to the farm, or are not external' 
      });
    }

    // Import files
    const importResult = await bulkImportExternalFiles(req.user.id, farmId, documentIds);

    if (!importResult.success) {
      await transaction.rollback();
      return res.status(500).json({ 
        error: importResult.error,
        results: importResult.results
      });
    }

    await transaction.commit();

    return res.status(200).json({
      message: 'External files imported successfully',
      results: importResult.results
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error bulk importing external files:', error);
    return res.status(500).json({ error: error.message });
  }
};

/**
 * Get external service integrations for a user
 */
export const getUserIntegrations = async (req, res) => {
  try {
    const { farmId } = req.params; // Changed from tenantId to farmId

    // Validate farm access
    const hasAccess = await checkUserFarmAccess(req.user.id, farmId);
    if (!hasAccess && !req.user.is_global_admin) {
      return res.status(403).json({ error: 'You do not have access to this farm' });
    }

    // Get integrations
    const integrations = await Integration.findAll({
      where: {
        user_id: req.user.id,
        farm_id: farmId, // Changed from tenant_id to farm_id
        is_active: true
      },
      attributes: ['id', 'provider', 'created_at', 'updated_at']
    });

    return res.status(200).json(integrations);
  } catch (error) {
    console.error('Error getting user integrations:', error);
    return res.status(500).json({ error: error.message });
  }
};
