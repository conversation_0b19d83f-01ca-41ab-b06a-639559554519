import MarketContract from '../models/MarketContract.js';
import Farm from '../models/Farm.js';
import User from '../models/User.js';
import Supplier from '../models/Supplier.js';
import { sequelize } from '../config/database.js';

// Get all contracts (for global admin)
export const getAllContracts = async (req, res) => {
  try {
    const contracts = await MarketContract.findAll({
      include: [
        {
          model: Farm,
          as: 'marketContractFarm',
          attributes: ['id', 'name']
        },
        {
          model: Supplier,
          as: 'marketContractSupplier',
          attributes: ['id', 'name']
        },
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'first_name', 'last_name', 'email']
        }
      ],
      order: [['created_at', 'DESC']]
    });

    res.status(200).json(contracts);
  } catch (error) {
    console.error('Error fetching all contracts:', error);
    res.status(500).json({ message: 'Failed to fetch contracts', error: error.message });
  }
};

// Get all contracts for a farm
export const getContracts = async (req, res) => {
  try {
    const { farmId } = req.query;

    // If farmId is not provided, fall back to getting all contracts
    if (!farmId) {
      return getAllContracts(req, res);
    }

    const contracts = await MarketContract.findAll({
      where: {
        farm_id: farmId
      },
      include: [
        {
          model: Supplier,
          as: 'marketContractSupplier',
          attributes: ['id', 'name']
        },
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'first_name', 'last_name', 'email']
        }
      ],
      order: [['created_at', 'DESC']]
    });

    res.status(200).json(contracts);
  } catch (error) {
    console.error('Error fetching contracts:', error);
    res.status(500).json({ message: 'Failed to fetch contracts', error: error.message });
  }
};

// Get a single contract by ID
export const getContract = async (req, res) => {
  try {
    const { id } = req.params;

    const contract = await MarketContract.findByPk(id, {
      include: [
        {
          model: Farm,
          as: 'marketContractFarm',
          attributes: ['id', 'name']
        },
        {
          model: Supplier,
          as: 'marketContractSupplier',
          attributes: ['id', 'name', 'contact_name', 'email', 'phone']
        },
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'first_name', 'last_name', 'email']
        }
      ]
    });

    if (!contract) {
      return res.status(404).json({ message: 'Contract not found' });
    }

    res.status(200).json(contract);
  } catch (error) {
    console.error('Error fetching contract:', error);
    res.status(500).json({ message: 'Failed to fetch contract', error: error.message });
  }
};

// Create a new contract
export const createContract = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const {
      farm_id,
      supplier_id,
      created_by,
      title,
      description,
      contract_type,
      status,
      start_date,
      end_date,
      value,
      currency,
      payment_terms,
      delivery_terms,
      products,
      attachments,
      notes,
      is_active
    } = req.body;

    if (!farm_id || !created_by || !title || !contract_type) {
      await transaction.rollback();
      return res.status(400).json({ message: 'Farm ID, created by, title, and contract type are required' });
    }

    const newContract = await MarketContract.create({
      farm_id,
      supplier_id,
      created_by,
      title,
      description,
      contract_type,
      status: status || 'draft',
      start_date,
      end_date,
      value,
      currency: currency || 'USD',
      payment_terms,
      delivery_terms,
      products,
      attachments,
      notes,
      is_active: is_active !== undefined ? is_active : true
    }, { transaction });

    await transaction.commit();

    // Return the created contract with related data
    const result = await MarketContract.findByPk(newContract.id, {
      include: [
        {
          model: Farm,
          as: 'marketContractFarm',
          attributes: ['id', 'name']
        },
        {
          model: Supplier,
          as: 'marketContractSupplier',
          attributes: ['id', 'name', 'contact_name', 'email', 'phone']
        },
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'first_name', 'last_name', 'email']
        }
      ]
    });

    res.status(201).json(result);
  } catch (error) {
    await transaction.rollback();
    console.error('Error creating contract:', error);
    res.status(500).json({ message: 'Failed to create contract', error: error.message });
  }
};

// Update a contract
export const updateContract = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { id } = req.params;
    const {
      supplier_id,
      title,
      description,
      contract_type,
      status,
      start_date,
      end_date,
      value,
      currency,
      payment_terms,
      delivery_terms,
      products,
      attachments,
      notes,
      is_active
    } = req.body;

    const contract = await MarketContract.findByPk(id, { transaction });

    if (!contract) {
      await transaction.rollback();
      return res.status(404).json({ message: 'Contract not found' });
    }

    // Update contract fields
    if (supplier_id !== undefined) contract.supplier_id = supplier_id;
    if (title !== undefined) contract.title = title;
    if (description !== undefined) contract.description = description;
    if (contract_type !== undefined) contract.contract_type = contract_type;
    if (status !== undefined) contract.status = status;
    if (start_date !== undefined) contract.start_date = start_date;
    if (end_date !== undefined) contract.end_date = end_date;
    if (value !== undefined) contract.value = value;
    if (currency !== undefined) contract.currency = currency;
    if (payment_terms !== undefined) contract.payment_terms = payment_terms;
    if (delivery_terms !== undefined) contract.delivery_terms = delivery_terms;
    if (products !== undefined) contract.products = products;
    if (attachments !== undefined) contract.attachments = attachments;
    if (notes !== undefined) contract.notes = notes;
    if (is_active !== undefined) contract.is_active = is_active;

    await contract.save({ transaction });
    await transaction.commit();

    // Return the updated contract with related data
    const result = await MarketContract.findByPk(id, {
      include: [
        {
          model: Farm,
          as: 'marketContractFarm',
          attributes: ['id', 'name']
        },
        {
          model: Supplier,
          as: 'marketContractSupplier',
          attributes: ['id', 'name', 'contact_name', 'email', 'phone']
        },
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'first_name', 'last_name', 'email']
        }
      ]
    });

    res.status(200).json(result);
  } catch (error) {
    await transaction.rollback();
    console.error('Error updating contract:', error);
    res.status(500).json({ message: 'Failed to update contract', error: error.message });
  }
};

// Delete a contract
export const deleteContract = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { id } = req.params;

    const contract = await MarketContract.findByPk(id, { transaction });

    if (!contract) {
      await transaction.rollback();
      return res.status(404).json({ message: 'Contract not found' });
    }

    await contract.destroy({ transaction });
    await transaction.commit();

    res.status(200).json({ message: 'Contract deleted successfully' });
  } catch (error) {
    await transaction.rollback();
    console.error('Error deleting contract:', error);
    res.status(500).json({ message: 'Failed to delete contract', error: error.message });
  }
};