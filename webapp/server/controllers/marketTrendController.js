import MarketTrend from '../models/MarketTrend.js';
import Farm from '../models/Farm.js';
import Product from '../models/Product.js';
import { sequelize, Op } from '../config/database.js';

// Get all market trends (for global admin)
export const getAllMarketTrends = async (req, res) => {
  try {
    const marketTrends = await MarketTrend.findAll({
      include: [
        {
          model: Farm,
          as: 'marketTrendFarm',
          attributes: ['id', 'name']
        },
        {
          model: Product,
          as: 'marketTrendProduct',
          attributes: ['id', 'name', 'type']
        }
      ],
      order: [['trend_date', 'DESC']]
    });

    res.status(200).json(marketTrends);
  } catch (error) {
    console.error('Error fetching all market trends:', error);
    res.status(500).json({ message: 'Failed to fetch market trends', error: error.message });
  }
};

// Get all market trends for a farm
export const getMarketTrends = async (req, res) => {
  try {
    const { farmId, productCategory, startDate, endDate } = req.query;

    // Build the where clause
    const whereClause = {};

    if (farmId) {
      whereClause.farm_id = farmId;
    }

    if (productCategory) {
      whereClause.product_category = productCategory;
    }

    // Add date range filter if provided
    if (startDate || endDate) {
      whereClause.trend_date = {};
      if (startDate) {
        whereClause.trend_date[Op.gte] = new Date(startDate);
      }
      if (endDate) {
        whereClause.trend_date[Op.lte] = new Date(endDate);
      }
    }

    const marketTrends = await MarketTrend.findAll({
      where: whereClause,
      include: [
        {
          model: Product,
          as: 'marketTrendProduct',
          attributes: ['id', 'name', 'type']
        }
      ],
      order: [['trend_date', 'DESC']]
    });

    res.status(200).json(marketTrends);
  } catch (error) {
    console.error('Error fetching market trends:', error);
    res.status(500).json({ message: 'Failed to fetch market trends', error: error.message });
  }
};

// Get a single market trend by ID
export const getMarketTrend = async (req, res) => {
  try {
    const { id } = req.params;

    const marketTrend = await MarketTrend.findByPk(id, {
      include: [
        {
          model: Farm,
          as: 'marketTrendFarm',
          attributes: ['id', 'name']
        },
        {
          model: Product,
          as: 'marketTrendProduct',
          attributes: ['id', 'name', 'description', 'type', 'price', 'unit']
        }
      ]
    });

    if (!marketTrend) {
      return res.status(404).json({ message: 'Market trend not found' });
    }

    res.status(200).json(marketTrend);
  } catch (error) {
    console.error('Error fetching market trend:', error);
    res.status(500).json({ message: 'Failed to fetch market trend', error: error.message });
  }
};

// Get market trends for a specific product or category
export const getProductMarketTrends = async (req, res) => {
  try {
    const { productId, productCategory, startDate, endDate } = req.query;

    if (!productId && !productCategory) {
      return res.status(400).json({ message: 'Product ID or product category is required' });
    }

    // Build the where clause
    const whereClause = {};

    if (productId) {
      whereClause.product_id = productId;
    }

    if (productCategory) {
      whereClause.product_category = productCategory;
    }

    // Add date range filter if provided
    if (startDate || endDate) {
      whereClause.trend_date = {};
      if (startDate) {
        whereClause.trend_date[Op.gte] = new Date(startDate);
      }
      if (endDate) {
        whereClause.trend_date[Op.lte] = new Date(endDate);
      }
    }

    const marketTrends = await MarketTrend.findAll({
      where: whereClause,
      order: [['trend_date', 'ASC']] // Order by date ascending for time series data
    });

    // Format the data for time series visualization
    const formattedData = marketTrends.map(trend => ({
      id: trend.id,
      date: trend.trend_date,
      price: parseFloat(trend.price),
      currency: trend.currency,
      unit: trend.unit,
      product_name: trend.product_name,
      product_category: trend.product_category,
      market_location: trend.market_location,
      source: trend.source,
      price_change: trend.price_change ? parseFloat(trend.price_change) : null,
      price_change_percentage: trend.price_change_percentage ? parseFloat(trend.price_change_percentage) : null
    }));

    res.status(200).json({
      trends: formattedData,
      meta: {
        count: formattedData.length,
        product_id: productId,
        product_category: productCategory,
        start_date: startDate,
        end_date: endDate
      }
    });
  } catch (error) {
    console.error('Error fetching product market trends:', error);
    res.status(500).json({ message: 'Failed to fetch product market trends', error: error.message });
  }
};

// Create a new market trend
export const createMarketTrend = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const {
      farm_id,
      product_id,
      product_category,
      product_name,
      trend_date,
      price,
      currency,
      unit,
      source,
      market_location,
      volume,
      price_change,
      price_change_percentage,
      forecast,
      notes,
      is_active
    } = req.body;

    if (!product_category || !product_name || !price) {
      await transaction.rollback();
      return res.status(400).json({ message: 'Product category, product name, and price are required' });
    }

    const newMarketTrend = await MarketTrend.create({
      farm_id,
      product_id,
      product_category,
      product_name,
      trend_date: trend_date || new Date(),
      price,
      currency: currency || 'USD',
      unit,
      source,
      market_location,
      volume,
      price_change,
      price_change_percentage,
      forecast,
      notes,
      is_active: is_active !== undefined ? is_active : true
    }, { transaction });

    await transaction.commit();

    // Return the created market trend with related data
    const result = await MarketTrend.findByPk(newMarketTrend.id, {
      include: [
        {
          model: Farm,
          as: 'marketTrendFarm',
          attributes: ['id', 'name']
        },
        {
          model: Product,
          as: 'marketTrendProduct',
          attributes: ['id', 'name', 'description', 'type', 'price', 'unit']
        }
      ]
    });

    res.status(201).json(result);
  } catch (error) {
    await transaction.rollback();
    console.error('Error creating market trend:', error);
    res.status(500).json({ message: 'Failed to create market trend', error: error.message });
  }
};

// Update a market trend
export const updateMarketTrend = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { id } = req.params;
    const {
      farm_id,
      product_id,
      product_category,
      product_name,
      trend_date,
      price,
      currency,
      unit,
      source,
      market_location,
      volume,
      price_change,
      price_change_percentage,
      forecast,
      notes,
      is_active
    } = req.body;

    const marketTrend = await MarketTrend.findByPk(id, { transaction });

    if (!marketTrend) {
      await transaction.rollback();
      return res.status(404).json({ message: 'Market trend not found' });
    }

    // Update market trend fields
    if (farm_id !== undefined) marketTrend.farm_id = farm_id;
    if (product_id !== undefined) marketTrend.product_id = product_id;
    if (product_category !== undefined) marketTrend.product_category = product_category;
    if (product_name !== undefined) marketTrend.product_name = product_name;
    if (trend_date !== undefined) marketTrend.trend_date = trend_date;
    if (price !== undefined) marketTrend.price = price;
    if (currency !== undefined) marketTrend.currency = currency;
    if (unit !== undefined) marketTrend.unit = unit;
    if (source !== undefined) marketTrend.source = source;
    if (market_location !== undefined) marketTrend.market_location = market_location;
    if (volume !== undefined) marketTrend.volume = volume;
    if (price_change !== undefined) marketTrend.price_change = price_change;
    if (price_change_percentage !== undefined) marketTrend.price_change_percentage = price_change_percentage;
    if (forecast !== undefined) marketTrend.forecast = forecast;
    if (notes !== undefined) marketTrend.notes = notes;
    if (is_active !== undefined) marketTrend.is_active = is_active;

    await marketTrend.save({ transaction });
    await transaction.commit();

    // Return the updated market trend with related data
    const result = await MarketTrend.findByPk(id, {
      include: [
        {
          model: Farm,
          as: 'marketTrendFarm',
          attributes: ['id', 'name']
        },
        {
          model: Product,
          as: 'marketTrendProduct',
          attributes: ['id', 'name', 'description', 'type', 'price', 'unit']
        }
      ]
    });

    res.status(200).json(result);
  } catch (error) {
    await transaction.rollback();
    console.error('Error updating market trend:', error);
    res.status(500).json({ message: 'Failed to update market trend', error: error.message });
  }
};

// Delete a market trend
export const deleteMarketTrend = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { id } = req.params;

    const marketTrend = await MarketTrend.findByPk(id, { transaction });

    if (!marketTrend) {
      await transaction.rollback();
      return res.status(404).json({ message: 'Market trend not found' });
    }

    await marketTrend.destroy({ transaction });
    await transaction.commit();

    res.status(200).json({ message: 'Market trend deleted successfully' });
  } catch (error) {
    await transaction.rollback();
    console.error('Error deleting market trend:', error);
    res.status(500).json({ message: 'Failed to delete market trend', error: error.message });
  }
};

// Get market trend forecast
export const getMarketTrendForecast = async (req, res) => {
  try {
    const { productId, productCategory } = req.query;

    if (!productId && !productCategory) {
      return res.status(400).json({ message: 'Product ID or product category is required' });
    }

    // Build the where clause
    const whereClause = {};

    if (productId) {
      whereClause.product_id = productId;
    }

    if (productCategory) {
      whereClause.product_category = productCategory;
    }

    // Only get trends with forecast data
    whereClause.forecast = { [Op.ne]: null };

    const marketTrends = await MarketTrend.findAll({
      where: whereClause,
      order: [['trend_date', 'DESC']],
      limit: 1 // Get the most recent forecast
    });

    if (marketTrends.length === 0) {
      return res.status(404).json({ message: 'No forecast data found for the specified product' });
    }

    const latestTrend = marketTrends[0];

    res.status(200).json({
      id: latestTrend.id,
      product_id: latestTrend.product_id,
      product_name: latestTrend.product_name,
      product_category: latestTrend.product_category,
      trend_date: latestTrend.trend_date,
      current_price: parseFloat(latestTrend.price),
      currency: latestTrend.currency,
      unit: latestTrend.unit,
      forecast: latestTrend.forecast,
      source: latestTrend.source,
      market_location: latestTrend.market_location
    });
  } catch (error) {
    console.error('Error fetching market trend forecast:', error);
    res.status(500).json({ message: 'Failed to fetch market trend forecast', error: error.message });
  }
};
