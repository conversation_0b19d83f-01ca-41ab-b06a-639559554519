import { validationResult } from 'express-validator';
import { sequelize } from '../config/database.js';
import { handleServerError } from '../utils/errorHandlers.js';
import { 
  generateCropRotationAnalysis, 
  getLatestCropRotationAnalysis, 
  applyCropRotationAnalysis, 
  getAllCropRotationAnalyses,
  generateHarvestScheduleAnalysis,
  getLatestHarvestScheduleAnalysis,
  applyHarvestScheduleAnalysis,
  getAllHarvestScheduleAnalyses,
  generateSoilHealthAnalysis,
  getLatestSoilHealthAnalysis,
  applySoilHealthAnalysis,
  getAllSoilHealthAnalyses,
  generateFieldHealthAnalysis,
  getLatestFieldHealthAnalysis,
  applyFieldHealthAnalysis,
  getAllFieldHealthAnalyses,
  generateHerdHealthAnalysis,
  getLatestHerdHealthAnalysis,
  applyHerdHealthAnalysis,
  getAllHerdHealthAnalyses
} from '../services/aiAnalysisService.js';

/**
 * Generate a crop rotation analysis for a field or farm
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const generateCropRotationAnalysisController = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { farmId, fieldId, cropData } = req.body;

    if (!farmId) {
      return res.status(400).json({ error: 'Farm ID is required' });
    }

    const analysis = await generateCropRotationAnalysis(farmId, fieldId || null, cropData || {});
    return res.status(201).json({ analysis });
  } catch (error) {
    return handleServerError(res, error);
  }
};

/**
 * Get the latest crop rotation analysis for a field or farm
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const getLatestCropRotationAnalysisController = async (req, res) => {
  try {
    const { farmId } = req.params;
    const { fieldId } = req.query;

    if (!farmId) {
      return res.status(400).json({ error: 'Farm ID is required' });
    }

    const analysis = await getLatestCropRotationAnalysis(farmId, fieldId || null);
    return res.status(200).json({ analysis });
  } catch (error) {
    return handleServerError(res, error);
  }
};

/**
 * Apply a crop rotation analysis to farm planning
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const applyCropRotationAnalysisController = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { id } = req.params;
    const { isApplied } = req.body;

    if (!id) {
      return res.status(400).json({ error: 'Analysis ID is required' });
    }

    const analysis = await applyCropRotationAnalysis(id, isApplied !== false);
    return res.status(200).json({ analysis });
  } catch (error) {
    return handleServerError(res, error);
  }
};

/**
 * Get all crop rotation analyses for a farm
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const getAllCropRotationAnalysesController = async (req, res) => {
  try {
    const { farmId } = req.params;

    if (!farmId) {
      return res.status(400).json({ error: 'Farm ID is required' });
    }

    try {
      const analyses = await getAllCropRotationAnalyses(farmId);
      return res.status(200).json({ analyses });
    } catch (error) {
      // Check if the error is about farm not found
      if (error.message.includes('Farm with ID') && error.message.includes('not found')) {
        return res.status(404).json({ error: error.message });
      }
      // Re-throw for general error handling
      throw error;
    }
  } catch (error) {
    return handleServerError(res, error);
  }
};

/**
 * Run crop rotation analysis for all fields in a farm
 * This is used by the cron job to regularly update analyses
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const runCropRotationAnalysisForFarm = async (req, res) => {
  try {
    const { farmId } = req.params;

    if (!farmId) {
      return res.status(400).json({ error: 'Farm ID is required' });
    }

    // Get all fields for the farm
    const fields = await sequelize.query(
      'SELECT id FROM fields WHERE farm_id = $1',
      { 
        replacements: [farmId],
        type: sequelize.QueryTypes.SELECT
      }
    );

    // Generate analysis for each field
    const results = [];
    for (const field of fields) {
      try {
        const analysis = await generateCropRotationAnalysis(farmId, field.id);
        results.push({
          fieldId: field.id,
          success: true,
          analysisId: analysis.id
        });
      } catch (error) {
        console.error(`Error generating analysis for field ${field.id}:`, error);
        results.push({
          fieldId: field.id,
          success: false,
          error: error.message
        });
      }
    }

    // Also generate a farm-wide analysis
    try {
      const farmAnalysis = await generateCropRotationAnalysis(farmId);
      results.push({
        farmWide: true,
        success: true,
        analysisId: farmAnalysis.id
      });
    } catch (error) {
      console.error(`Error generating farm-wide analysis:`, error);
      results.push({
        farmWide: true,
        success: false,
        error: error.message
      });
    }

    return res.status(200).json({ 
      message: 'Crop rotation analysis completed',
      results 
    });
  } catch (error) {
    return handleServerError(res, error);
  }
};

/**
 * Run crop rotation analysis for all farms
 * This is used by the cron job to regularly update analyses for all farms
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const runCropRotationAnalysisForAllFarms = async (req, res) => {
  try {
    // Get all farms
    const farms = await sequelize.query(
      'SELECT id FROM farms',
      { type: sequelize.QueryTypes.SELECT }
    );

    // Generate analysis for each farm
    const results = [];
    for (const farm of farms) {
      try {
        // Create a mock request and response for the farm
        const mockReq = {
          params: { farmId: farm.id }
        };

        const mockRes = {
          status: (code) => ({
            json: (data) => {
              results.push({
                farmId: farm.id,
                success: true,
                results: data.results
              });
            }
          })
        };

        // Run the analysis for this farm
        await runCropRotationAnalysisForFarm(mockReq, mockRes);
      } catch (error) {
        console.error(`Error running analysis for farm ${farm.id}:`, error);
        results.push({
          farmId: farm.id,
          success: false,
          error: error.message
        });
      }
    }

    return res.status(200).json({ 
      message: 'Crop rotation analysis completed for all farms',
      results 
    });
  } catch (error) {
    return handleServerError(res, error);
  }
};

/**
 * Generate a harvest schedule analysis for a field or farm
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const generateHarvestScheduleAnalysisController = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { farmId, fieldId, cropId, harvestData } = req.body;

    if (!farmId) {
      return res.status(400).json({ error: 'Farm ID is required' });
    }

    const analysis = await generateHarvestScheduleAnalysis(farmId, fieldId || null, cropId || null, harvestData || {});
    return res.status(201).json({ success: true, analysis });
  } catch (error) {
    return handleServerError(res, error);
  }
};

/**
 * Get the latest harvest schedule analysis for a field or farm
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const getLatestHarvestScheduleAnalysisController = async (req, res) => {
  try {
    const { farmId } = req.params;
    const { fieldId, cropId } = req.query;

    if (!farmId) {
      return res.status(400).json({ error: 'Farm ID is required' });
    }

    const analysis = await getLatestHarvestScheduleAnalysis(farmId, fieldId || null, cropId || null);
    return res.status(200).json({ analysis });
  } catch (error) {
    return handleServerError(res, error);
  }
};

/**
 * Apply a harvest schedule analysis to farm planning
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const applyHarvestScheduleAnalysisController = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { id } = req.params;
    const { isApplied } = req.body;

    if (!id) {
      return res.status(400).json({ error: 'Analysis ID is required' });
    }

    const analysis = await applyHarvestScheduleAnalysis(id, isApplied !== false);
    return res.status(200).json({ success: true, analysis });
  } catch (error) {
    return handleServerError(res, error);
  }
};

/**
 * Get all harvest schedule analyses for a farm
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const getAllHarvestScheduleAnalysesController = async (req, res) => {
  try {
    const { farmId } = req.params;

    if (!farmId) {
      return res.status(400).json({ error: 'Farm ID is required' });
    }

    try {
      const analyses = getAllHarvestScheduleAnalyses(farmId);
      return res.status(200).json({ success: true, analyses });
    } catch (error) {
      // Check if the error is about farm not found
      if (error.message.includes('Farm with ID') && error.message.includes('not found')) {
        return res.status(404).json({ error: error.message });
      }
      // Re-throw for general error handling
      throw error;
    }
  } catch (error) {
    return handleServerError(res, error);
  }
};

/**
 * Run harvest schedule analysis for all fields in a farm
 * This is used by the cron job to regularly update analyses
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const runHarvestScheduleAnalysisForFarm = async (req, res) => {
  try {
    const { farmId } = req.params;

    if (!farmId) {
      return res.status(400).json({ error: 'Farm ID is required' });
    }

    // Get all fields with active crops for the farm
    const fields = await sequelize.query(
      `SELECT f.id as field_id, c.id as crop_id 
       FROM fields f
       JOIN crops c ON f.id = c.field_id
       WHERE f.farm_id = $1
       AND c.status = 'active'`,
      { 
        replacements: [farmId],
        type: sequelize.QueryTypes.SELECT
      }
    );

    // Generate analysis for each field with active crops
    const results = [];
    for (const field of fields) {
      try {
        const analysis = await generateHarvestScheduleAnalysis(farmId, field.field_id, field.crop_id);
        results.push({
          fieldId: field.field_id,
          cropId: field.crop_id,
          success: true,
          analysisId: analysis.id
        });
      } catch (error) {
        console.error(`Error generating harvest schedule analysis for field ${field.field_id}, crop ${field.crop_id}:`, error);
        results.push({
          fieldId: field.field_id,
          cropId: field.crop_id,
          success: false,
          error: error.message
        });
      }
    }

    // Also generate a farm-wide analysis if there are active crops
    if (fields.length > 0) {
      try {
        const farmAnalysis = await generateHarvestScheduleAnalysis(farmId);
        results.push({
          farmWide: true,
          success: true,
          analysisId: farmAnalysis.id
        });
      } catch (error) {
        console.error(`Error generating farm-wide harvest schedule analysis:`, error);
        results.push({
          farmWide: true,
          success: false,
          error: error.message
        });
      }
    }

    return res.status(200).json({ 
      message: 'Harvest schedule analysis completed',
      results 
    });
  } catch (error) {
    return handleServerError(res, error);
  }
};

/**
 * Run harvest schedule analysis for all farms
 * This is used by the cron job to regularly update analyses for all farms
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const runHarvestScheduleAnalysisForAllFarms = async (req, res) => {
  try {
    // Get all farms
    const farms = await sequelize.query(
      'SELECT id FROM farms',
      { type: sequelize.QueryTypes.SELECT }
    );

    // Generate analysis for each farm
    const results = [];
    for (const farm of farms) {
      try {
        // Create a mock request and response for the farm
        const mockReq = {
          params: { farmId: farm.id }
        };

        const mockRes = {
          status: (code) => ({
            json: (data) => {
              results.push({
                farmId: farm.id,
                success: true,
                results: data.results
              });
            }
          })
        };

        // Run the analysis for this farm
        await runHarvestScheduleAnalysisForFarm(mockReq, mockRes);
      } catch (error) {
        console.error(`Error running harvest schedule analysis for farm ${farm.id}:`, error);
        results.push({
          farmId: farm.id,
          success: false,
          error: error.message
        });
      }
    }

    return res.status(200).json({ 
      message: 'Harvest schedule analysis completed for all farms',
      results 
    });
  } catch (error) {
    return handleServerError(res, error);
  }
};

/**
 * Generate a soil health analysis for a field or farm
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const generateSoilHealthAnalysisController = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { farmId, fieldId, soilData } = req.body;

    if (!farmId) {
      return res.status(400).json({ error: 'Farm ID is required' });
    }

    const analysis = await generateSoilHealthAnalysis(farmId, fieldId || null, soilData || {});
    return res.status(201).json({ analysis });
  } catch (error) {
    return handleServerError(res, error);
  }
};

/**
 * Get the latest soil health analysis for a field or farm
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const getLatestSoilHealthAnalysisController = async (req, res) => {
  try {
    const { farmId } = req.params;
    const { fieldId } = req.query;

    if (!farmId) {
      return res.status(400).json({ error: 'Farm ID is required' });
    }

    const analysis = await getLatestSoilHealthAnalysis(farmId, fieldId || null);
    return res.status(200).json({ analysis });
  } catch (error) {
    return handleServerError(res, error);
  }
};

/**
 * Apply a soil health analysis to farm planning
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const applySoilHealthAnalysisController = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { id } = req.params;
    const { isApplied } = req.body;

    if (!id) {
      return res.status(400).json({ error: 'Analysis ID is required' });
    }

    const analysis = await applySoilHealthAnalysis(id, isApplied !== false);
    return res.status(200).json({ analysis });
  } catch (error) {
    return handleServerError(res, error);
  }
};

/**
 * Get all soil health analyses for a farm
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const getAllSoilHealthAnalysesController = async (req, res) => {
  try {
    const { farmId } = req.params;

    if (!farmId) {
      return res.status(400).json({ error: 'Farm ID is required' });
    }

    try {
      const analyses = await getAllSoilHealthAnalyses(farmId);
      return res.status(200).json({ analyses });
    } catch (error) {
      // Check if the error is about farm not found
      if (error.message.includes('Farm with ID') && error.message.includes('not found')) {
        return res.status(404).json({ error: error.message });
      }
      // Re-throw for general error handling
      throw error;
    }
  } catch (error) {
    return handleServerError(res, error);
  }
};

/**
 * Run soil health analysis for all fields in a farm
 * This is used by the cron job to regularly update analyses
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const runSoilHealthAnalysisForFarm = async (req, res) => {
  try {
    const { farmId } = req.params;

    if (!farmId) {
      return res.status(400).json({ error: 'Farm ID is required' });
    }

    // Get all fields for the farm
    const fields = await sequelize.query(
      'SELECT id FROM fields WHERE farm_id = $1',
      { 
        replacements: [farmId],
        type: sequelize.QueryTypes.SELECT
      }
    );

    // Generate analysis for each field
    const results = [];
    for (const field of fields) {
      try {
        const analysis = await generateSoilHealthAnalysis(farmId, field.id);
        results.push({
          fieldId: field.id,
          success: true,
          analysisId: analysis.id
        });
      } catch (error) {
        console.error(`Error generating soil health analysis for field ${field.id}:`, error);
        results.push({
          fieldId: field.id,
          success: false,
          error: error.message
        });
      }
    }

    // Also generate a farm-wide analysis
    try {
      const farmAnalysis = await generateSoilHealthAnalysis(farmId);
      results.push({
        farmWide: true,
        success: true,
        analysisId: farmAnalysis.id
      });
    } catch (error) {
      console.error(`Error generating farm-wide soil health analysis:`, error);
      results.push({
        farmWide: true,
        success: false,
        error: error.message
      });
    }

    return res.status(200).json({ 
      message: 'Soil health analysis completed',
      results 
    });
  } catch (error) {
    return handleServerError(res, error);
  }
};

/**
 * Run soil health analysis for all farms
 * This is used by the cron job to regularly update analyses for all farms
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const runSoilHealthAnalysisForAllFarms = async (req, res) => {
  try {
    // Get all farms
    const farms = await sequelize.query(
      'SELECT id FROM farms',
      { type: sequelize.QueryTypes.SELECT }
    );

    // Generate analysis for each farm
    const results = [];
    for (const farm of farms) {
      try {
        // Create a mock request and response for the farm
        const mockReq = {
          params: { farmId: farm.id }
        };

        const mockRes = {
          status: (code) => ({
            json: (data) => {
              results.push({
                farmId: farm.id,
                success: true,
                results: data.results
              });
            }
          })
        };

        // Run the analysis for this farm
        await runSoilHealthAnalysisForFarm(mockReq, mockRes);
      } catch (error) {
        console.error(`Error running soil health analysis for farm ${farm.id}:`, error);
        results.push({
          farmId: farm.id,
          success: false,
          error: error.message
        });
      }
    }

    return res.status(200).json({ 
      message: 'Soil health analysis completed for all farms',
      results 
    });
  } catch (error) {
    return handleServerError(res, error);
  }
};

/**
 * Generate a field health analysis for a field or farm
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const generateFieldHealthAnalysisController = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { farmId, fieldId, cropId, fieldData } = req.body;

    if (!farmId) {
      return res.status(400).json({ error: 'Farm ID is required' });
    }

    const analysis = await generateFieldHealthAnalysis(farmId, fieldId || null, cropId || null, fieldData || {});
    return res.status(201).json({ analysis });
  } catch (error) {
    return handleServerError(res, error);
  }
};

/**
 * Get the latest field health analysis for a field or farm
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const getLatestFieldHealthAnalysisController = async (req, res) => {
  try {
    const { farmId } = req.params;
    const { fieldId, cropId } = req.query;

    if (!farmId) {
      return res.status(400).json({ error: 'Farm ID is required' });
    }

    const analysis = await getLatestFieldHealthAnalysis(farmId, fieldId || null, cropId || null);
    return res.status(200).json({ analysis });
  } catch (error) {
    return handleServerError(res, error);
  }
};

/**
 * Apply a field health analysis to farm planning
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const applyFieldHealthAnalysisController = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { id } = req.params;
    const { isApplied } = req.body;

    if (!id) {
      return res.status(400).json({ error: 'Analysis ID is required' });
    }

    const analysis = await applyFieldHealthAnalysis(id, isApplied !== false);
    return res.status(200).json({ analysis });
  } catch (error) {
    return handleServerError(res, error);
  }
};

/**
 * Get all field health analyses for a farm
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const getAllFieldHealthAnalysesController = async (req, res) => {
  try {
    const { farmId } = req.params;

    if (!farmId) {
      return res.status(400).json({ error: 'Farm ID is required' });
    }

    try {
      const analyses = await getAllFieldHealthAnalyses(farmId);
      return res.status(200).json({ analyses });
    } catch (error) {
      // Check if the error is about farm not found
      if (error.message.includes('Farm with ID') && error.message.includes('not found')) {
        return res.status(404).json({ error: error.message });
      }
      // Re-throw for general error handling
      throw error;
    }
  } catch (error) {
    return handleServerError(res, error);
  }
};

/**
 * Run field health analysis for all fields in a farm
 * This is used by the cron job to regularly update analyses
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const runFieldHealthAnalysisForFarm = async (req, res) => {
  try {
    const { farmId } = req.params;

    if (!farmId) {
      return res.status(400).json({ error: 'Farm ID is required' });
    }

    // Get all fields with active crops for the farm
    const fields = await sequelize.query(
      `SELECT f.id as field_id, c.id as crop_id 
       FROM fields f
       LEFT JOIN crops c ON f.id = c.field_id AND c.status = 'active'
       WHERE f.farm_id = $1`,
      { 
        replacements: [farmId],
        type: sequelize.QueryTypes.SELECT
      }
    );

    // Generate analysis for each field
    const results = [];
    for (const field of fields) {
      try {
        const analysis = await generateFieldHealthAnalysis(farmId, field.field_id, field.crop_id);
        results.push({
          fieldId: field.field_id,
          cropId: field.crop_id,
          success: true,
          analysisId: analysis.id
        });
      } catch (error) {
        console.error(`Error generating field health analysis for field ${field.field_id}:`, error);
        results.push({
          fieldId: field.field_id,
          cropId: field.crop_id,
          success: false,
          error: error.message
        });
      }
    }

    // Also generate a farm-wide analysis
    try {
      const farmAnalysis = await generateFieldHealthAnalysis(farmId);
      results.push({
        farmWide: true,
        success: true,
        analysisId: farmAnalysis.id
      });
    } catch (error) {
      console.error(`Error generating farm-wide field health analysis:`, error);
      results.push({
        farmWide: true,
        success: false,
        error: error.message
      });
    }

    return res.status(200).json({ 
      message: 'Field health analysis completed',
      results 
    });
  } catch (error) {
    return handleServerError(res, error);
  }
};

/**
 * Run field health analysis for all farms
 * This is used by the cron job to regularly update analyses for all farms
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const runFieldHealthAnalysisForAllFarms = async (req, res) => {
  try {
    // Get all farms
    const farms = await sequelize.query(
      'SELECT id FROM farms',
      { type: sequelize.QueryTypes.SELECT }
    );

    // Generate analysis for each farm
    const results = [];
    for (const farm of farms) {
      try {
        // Create a mock request and response for the farm
        const mockReq = {
          params: { farmId: farm.id }
        };

        const mockRes = {
          status: (code) => ({
            json: (data) => {
              results.push({
                farmId: farm.id,
                success: true,
                results: data.results
              });
            }
          })
        };

        // Run the analysis for this farm
        await runFieldHealthAnalysisForFarm(mockReq, mockRes);
      } catch (error) {
        console.error(`Error running field health analysis for farm ${farm.id}:`, error);
        results.push({
          farmId: farm.id,
          success: false,
          error: error.message
        });
      }
    }

    return res.status(200).json({ 
      message: 'Field health analysis completed for all farms',
      results 
    });
  } catch (error) {
    return handleServerError(res, error);
  }
};

/**
 * Generate a herd health analysis for a livestock group or farm
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const generateHerdHealthAnalysisController = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { farmId, livestockGroupId, herdData } = req.body;

    if (!farmId) {
      return res.status(400).json({ error: 'Farm ID is required' });
    }

    const analysis = await generateHerdHealthAnalysis(farmId, livestockGroupId || null, herdData || {});
    return res.status(201).json({ analysis });
  } catch (error) {
    return handleServerError(res, error);
  }
};

/**
 * Get the latest herd health analysis for a livestock group or farm
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const getLatestHerdHealthAnalysisController = async (req, res) => {
  try {
    const { farmId } = req.params;
    const { livestockGroupId } = req.query;

    if (!farmId) {
      return res.status(400).json({ error: 'Farm ID is required' });
    }

    const analysis = await getLatestHerdHealthAnalysis(farmId, livestockGroupId || null);
    return res.status(200).json({ analysis });
  } catch (error) {
    return handleServerError(res, error);
  }
};

/**
 * Apply a herd health analysis to farm planning
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const applyHerdHealthAnalysisController = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { id } = req.params;
    const { isApplied } = req.body;

    if (!id) {
      return res.status(400).json({ error: 'Analysis ID is required' });
    }

    const analysis = await applyHerdHealthAnalysis(id, isApplied !== false);
    return res.status(200).json({ analysis });
  } catch (error) {
    return handleServerError(res, error);
  }
};

/**
 * Get all herd health analyses for a farm
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const getAllHerdHealthAnalysesController = async (req, res) => {
  try {
    const { farmId } = req.params;

    if (!farmId) {
      return res.status(400).json({ error: 'Farm ID is required' });
    }

    try {
      const analyses = await getAllHerdHealthAnalyses(farmId);
      return res.status(200).json({ analyses });
    } catch (error) {
      // Check if the error is about farm not found
      if (error.message.includes('Farm with ID') && error.message.includes('not found')) {
        return res.status(404).json({ error: error.message });
      }
      // Re-throw for general error handling
      throw error;
    }
  } catch (error) {
    return handleServerError(res, error);
  }
};

/**
 * Run herd health analysis for all livestock groups in a farm
 * This is used by the cron job to regularly update analyses
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const runHerdHealthAnalysisForFarm = async (req, res) => {
  try {
    const { farmId } = req.params;

    if (!farmId) {
      return res.status(400).json({ error: 'Farm ID is required' });
    }

    // Get all livestock groups for the farm
    const livestockGroups = await sequelize.query(
      'SELECT id FROM livestock_groups WHERE farm_id = $1',
      { 
        replacements: [farmId],
        type: sequelize.QueryTypes.SELECT
      }
    );

    // Generate analysis for each livestock group
    const results = [];
    for (const group of livestockGroups) {
      try {
        const analysis = await generateHerdHealthAnalysis(farmId, group.id);
        results.push({
          livestockGroupId: group.id,
          success: true,
          analysisId: analysis.id
        });
      } catch (error) {
        console.error(`Error generating herd health analysis for livestock group ${group.id}:`, error);
        results.push({
          livestockGroupId: group.id,
          success: false,
          error: error.message
        });
      }
    }

    // Also generate a farm-wide analysis
    try {
      const farmAnalysis = await generateHerdHealthAnalysis(farmId);
      results.push({
        farmWide: true,
        success: true,
        analysisId: farmAnalysis.id
      });
    } catch (error) {
      console.error(`Error generating farm-wide herd health analysis:`, error);
      results.push({
        farmWide: true,
        success: false,
        error: error.message
      });
    }

    return res.status(200).json({ 
      message: 'Herd health analysis completed',
      results 
    });
  } catch (error) {
    return handleServerError(res, error);
  }
};

/**
 * Run herd health analysis for all farms
 * This is used by the cron job to regularly update analyses for all farms
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const runHerdHealthAnalysisForAllFarms = async (req, res) => {
  try {
    // Get all farms
    const farms = await sequelize.query(
      'SELECT id FROM farms',
      { type: sequelize.QueryTypes.SELECT }
    );

    // Generate analysis for each farm
    const results = [];
    for (const farm of farms) {
      try {
        // Create a mock request and response for the farm
        const mockReq = {
          params: { farmId: farm.id }
        };

        const mockRes = {
          status: (code) => ({
            json: (data) => {
              results.push({
                farmId: farm.id,
                success: true,
                results: data.results
              });
            }
          })
        };

        // Run the analysis for this farm
        await runHerdHealthAnalysisForFarm(mockReq, mockRes);
      } catch (error) {
        console.error(`Error running herd health analysis for farm ${farm.id}:`, error);
        results.push({
          farmId: farm.id,
          success: false,
          error: error.message
        });
      }
    }

    return res.status(200).json({ 
      message: 'Herd health analysis completed for all farms',
      results 
    });
  } catch (error) {
    return handleServerError(res, error);
  }
};
