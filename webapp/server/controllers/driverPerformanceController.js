import { Op } from 'sequelize';
import Farm from '../models/Farm.js';
import Driver from '../models/Driver.js';
import Delivery from '../models/Delivery.js';
import Pickup from '../models/Pickup.js';
import FuelRecord from '../models/FuelRecord.js';

/**
 * Get performance metrics for a driver
 */
export const getDriverPerformance = async (req, res) => {
  try {
    const { driverId } = req.params;
    const { 
      startDate = new Date(new Date().setMonth(new Date().getMonth() - 1)).toISOString(), // Last month
      endDate = new Date().toISOString() // Today
    } = req.query;

    // Find the driver
    const driver = await Driver.findByPk(driverId);
    if (!driver) {
      return res.status(404).json({ error: 'Driver not found' });
    }

    // Get all deliveries for the driver in the specified period
    const deliveries = await Delivery.findAll({
      where: {
        driver_id: driverId,
        scheduled_date: {
          [Op.between]: [new Date(startDate), new Date(endDate)]
        }
      }
    });

    // Get all pickups for the driver in the specified period
    const pickups = await Pickup.findAll({
      where: {
        driver_id: driverId,
        scheduled_date: {
          [Op.between]: [new Date(startDate), new Date(endDate)]
        }
      }
    });

    // Get all fuel records for the driver in the specified period
    const fuelRecords = await FuelRecord.findAll({
      where: {
        driver_id: driverId,
        date: {
          [Op.between]: [new Date(startDate), new Date(endDate)]
        }
      }
    });

    // Calculate performance metrics
    
    // 1. On-time delivery rate
    const totalDeliveries = deliveries.length;
    const onTimeDeliveries = deliveries.filter(delivery => {
      if (!delivery.actual_delivery_time || !delivery.scheduled_time) return false;
      
      const scheduledTime = new Date(delivery.scheduled_time);
      const actualTime = new Date(delivery.actual_delivery_time);
      
      // Consider on-time if within 15 minutes of scheduled time
      const diffMinutes = (actualTime - scheduledTime) / (1000 * 60);
      return diffMinutes <= 15;
    }).length;
    
    const onTimeDeliveryRate = totalDeliveries > 0 ? (onTimeDeliveries / totalDeliveries) * 100 : 0;
    
    // 2. On-time pickup rate
    const totalPickups = pickups.length;
    const onTimePickups = pickups.filter(pickup => {
      if (!pickup.actual_pickup_time || !pickup.scheduled_time) return false;
      
      const scheduledTime = new Date(pickup.scheduled_time);
      const actualTime = new Date(pickup.actual_pickup_time);
      
      // Consider on-time if within 15 minutes of scheduled time
      const diffMinutes = (actualTime - scheduledTime) / (1000 * 60);
      return diffMinutes <= 15;
    }).length;
    
    const onTimePickupRate = totalPickups > 0 ? (onTimePickups / totalPickups) * 100 : 0;
    
    // 3. Completion rate
    const completedDeliveries = deliveries.filter(delivery => delivery.status === 'completed').length;
    const completedPickups = pickups.filter(pickup => pickup.status === 'completed').length;
    
    const totalTasks = totalDeliveries + totalPickups;
    const completedTasks = completedDeliveries + completedPickups;
    
    const completionRate = totalTasks > 0 ? (completedTasks / totalTasks) * 100 : 0;
    
    // 4. Average delay time (in minutes)
    let totalDelayMinutes = 0;
    let delayCount = 0;
    
    deliveries.forEach(delivery => {
      if (delivery.actual_delivery_time && delivery.scheduled_time) {
        const scheduledTime = new Date(delivery.scheduled_time);
        const actualTime = new Date(delivery.actual_delivery_time);
        
        const diffMinutes = (actualTime - scheduledTime) / (1000 * 60);
        if (diffMinutes > 0) {
          totalDelayMinutes += diffMinutes;
          delayCount++;
        }
      }
    });
    
    pickups.forEach(pickup => {
      if (pickup.actual_pickup_time && pickup.scheduled_time) {
        const scheduledTime = new Date(pickup.scheduled_time);
        const actualTime = new Date(pickup.actual_pickup_time);
        
        const diffMinutes = (actualTime - scheduledTime) / (1000 * 60);
        if (diffMinutes > 0) {
          totalDelayMinutes += diffMinutes;
          delayCount++;
        }
      }
    });
    
    const averageDelayMinutes = delayCount > 0 ? totalDelayMinutes / delayCount : 0;
    
    // 5. Fuel efficiency
    let totalFuelAmount = 0;
    let totalDistance = 0;
    
    // Calculate total fuel consumption
    fuelRecords.forEach(record => {
      totalFuelAmount += record.amount;
    });
    
    // Calculate total distance traveled (from delivery and pickup records)
    // This is a simplified approach - in a real app, this would be more accurate
    deliveries.forEach(delivery => {
      if (delivery.distance) {
        totalDistance += delivery.distance;
      }
    });
    
    pickups.forEach(pickup => {
      if (pickup.distance) {
        totalDistance += pickup.distance;
      }
    });
    
    // Calculate fuel efficiency (km/l or mpg)
    const fuelEfficiency = totalFuelAmount > 0 ? totalDistance / totalFuelAmount : 0;
    
    // 6. Tasks per day
    const startDateObj = new Date(startDate);
    const endDateObj = new Date(endDate);
    const daysDiff = Math.ceil((endDateObj - startDateObj) / (1000 * 60 * 60 * 24));
    
    const tasksPerDay = daysDiff > 0 ? totalTasks / daysDiff : 0;
    
    // 7. Customer feedback (mock data - in a real app, this would come from a feedback system)
    const customerFeedback = {
      averageRating: 4.2,
      totalRatings: 15,
      positiveComments: 12,
      negativeComments: 3
    };
    
    // 8. Safety metrics (mock data - in a real app, this would come from incident reports)
    const safetyMetrics = {
      incidents: 0,
      accidentFree: true,
      safetyViolations: 0,
      lastIncidentDate: null
    };

    return res.status(200).json({
      driverPerformance: {
        driverId: driver.id,
        driverName: `${driver.first_name} ${driver.last_name}`,
        period: {
          startDate,
          endDate
        },
        deliveryMetrics: {
          total: totalDeliveries,
          completed: completedDeliveries,
          onTime: onTimeDeliveries,
          onTimeRate: onTimeDeliveryRate
        },
        pickupMetrics: {
          total: totalPickups,
          completed: completedPickups,
          onTime: onTimePickups,
          onTimeRate: onTimePickupRate
        },
        overallMetrics: {
          totalTasks,
          completedTasks,
          completionRate,
          averageDelayMinutes,
          tasksPerDay
        },
        fuelMetrics: {
          totalFuelAmount,
          totalDistance,
          fuelEfficiency
        },
        customerFeedback,
        safetyMetrics
      }
    });
  } catch (error) {
    console.error('Error getting driver performance:', error);
    return res.status(500).json({ error: 'Failed to get driver performance' });
  }
};

/**
 * Get performance comparison for all drivers
 */
export const getDriversPerformanceComparison = async (req, res) => {
  try {
    const { farmId } = req.params;
    const { 
      startDate = new Date(new Date().setMonth(new Date().getMonth() - 1)).toISOString(), // Last month
      endDate = new Date().toISOString(), // Today
      metric = 'onTimeRate' // Default metric to compare
    } = req.query;

    // Validate farm exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Get all drivers for the farm
    const drivers = await Driver.findAll({
      where: {
        farm_id: farmId
      }
    });

    // Get performance metrics for each driver
    const driversPerformance = await Promise.all(
      drivers.map(async (driver) => {
        // Get all deliveries for the driver in the specified period
        const deliveries = await Delivery.findAll({
          where: {
            driver_id: driver.id,
            scheduled_date: {
              [Op.between]: [new Date(startDate), new Date(endDate)]
            }
          }
        });

        // Get all pickups for the driver in the specified period
        const pickups = await Pickup.findAll({
          where: {
            driver_id: driver.id,
            scheduled_date: {
              [Op.between]: [new Date(startDate), new Date(endDate)]
            }
          }
        });

        // Get all fuel records for the driver in the specified period
        const fuelRecords = await FuelRecord.findAll({
          where: {
            driver_id: driver.id,
            date: {
              [Op.between]: [new Date(startDate), new Date(endDate)]
            }
          }
        });

        // Calculate key metrics
        
        // On-time rates
        const totalDeliveries = deliveries.length;
        const onTimeDeliveries = deliveries.filter(delivery => {
          if (!delivery.actual_delivery_time || !delivery.scheduled_time) return false;
          
          const scheduledTime = new Date(delivery.scheduled_time);
          const actualTime = new Date(delivery.actual_delivery_time);
          
          const diffMinutes = (actualTime - scheduledTime) / (1000 * 60);
          return diffMinutes <= 15;
        }).length;
        
        const onTimeDeliveryRate = totalDeliveries > 0 ? (onTimeDeliveries / totalDeliveries) * 100 : 0;
        
        const totalPickups = pickups.length;
        const onTimePickups = pickups.filter(pickup => {
          if (!pickup.actual_pickup_time || !pickup.scheduled_time) return false;
          
          const scheduledTime = new Date(pickup.scheduled_time);
          const actualTime = new Date(pickup.actual_pickup_time);
          
          const diffMinutes = (actualTime - scheduledTime) / (1000 * 60);
          return diffMinutes <= 15;
        }).length;
        
        const onTimePickupRate = totalPickups > 0 ? (onTimePickups / totalPickups) * 100 : 0;
        
        // Overall on-time rate
        const totalTasks = totalDeliveries + totalPickups;
        const onTimeTasks = onTimeDeliveries + onTimePickups;
        const overallOnTimeRate = totalTasks > 0 ? (onTimeTasks / totalTasks) * 100 : 0;
        
        // Completion rate
        const completedDeliveries = deliveries.filter(delivery => delivery.status === 'completed').length;
        const completedPickups = pickups.filter(pickup => pickup.status === 'completed').length;
        
        const completedTasks = completedDeliveries + completedPickups;
        const completionRate = totalTasks > 0 ? (completedTasks / totalTasks) * 100 : 0;
        
        // Fuel efficiency
        let totalFuelAmount = 0;
        let totalDistance = 0;
        
        fuelRecords.forEach(record => {
          totalFuelAmount += record.amount;
        });
        
        deliveries.forEach(delivery => {
          if (delivery.distance) {
            totalDistance += delivery.distance;
          }
        });
        
        pickups.forEach(pickup => {
          if (pickup.distance) {
            totalDistance += pickup.distance;
          }
        });
        
        const fuelEfficiency = totalFuelAmount > 0 ? totalDistance / totalFuelAmount : 0;
        
        // Tasks per day
        const startDateObj = new Date(startDate);
        const endDateObj = new Date(endDate);
        const daysDiff = Math.ceil((endDateObj - startDateObj) / (1000 * 60 * 60 * 24));
        
        const tasksPerDay = daysDiff > 0 ? totalTasks / daysDiff : 0;

        return {
          driverId: driver.id,
          driverName: `${driver.first_name} ${driver.last_name}`,
          metrics: {
            onTimeRate: overallOnTimeRate,
            completionRate,
            tasksPerDay,
            fuelEfficiency,
            totalTasks,
            totalDistance
          }
        };
      })
    );

    // Sort drivers by the selected metric
    driversPerformance.sort((a, b) => {
      // For most metrics, higher is better
      if (metric === 'fuelEfficiency') {
        // For fuel efficiency, higher is better
        return b.metrics[metric] - a.metrics[metric];
      } else {
        // For other metrics, higher is better
        return b.metrics[metric] - a.metrics[metric];
      }
    });

    // Calculate farm averages
    const farmAverages = {
      onTimeRate: calculateAverage(driversPerformance.map(d => d.metrics.onTimeRate)),
      completionRate: calculateAverage(driversPerformance.map(d => d.metrics.completionRate)),
      tasksPerDay: calculateAverage(driversPerformance.map(d => d.metrics.tasksPerDay)),
      fuelEfficiency: calculateAverage(driversPerformance.map(d => d.metrics.fuelEfficiency)),
      totalTasks: driversPerformance.reduce((sum, d) => sum + d.metrics.totalTasks, 0),
      totalDistance: driversPerformance.reduce((sum, d) => sum + d.metrics.totalDistance, 0)
    };

    return res.status(200).json({
      driversPerformance,
      farmAverages,
      period: {
        startDate,
        endDate
      }
    });
  } catch (error) {
    console.error('Error getting drivers performance comparison:', error);
    return res.status(500).json({ error: 'Failed to get drivers performance comparison' });
  }
};

/**
 * Get performance trends for a driver
 */
export const getDriverPerformanceTrends = async (req, res) => {
  try {
    const { driverId } = req.params;
    const { 
      startDate = new Date(new Date().setMonth(new Date().getMonth() - 6)).toISOString(), // Last 6 months
      endDate = new Date().toISOString(), // Today
      interval = 'month' // 'day', 'week', 'month'
    } = req.query;

    // Find the driver
    const driver = await Driver.findByPk(driverId);
    if (!driver) {
      return res.status(404).json({ error: 'Driver not found' });
    }

    // Get all deliveries for the driver in the specified period
    const deliveries = await Delivery.findAll({
      where: {
        driver_id: driverId,
        scheduled_date: {
          [Op.between]: [new Date(startDate), new Date(endDate)]
        }
      }
    });

    // Get all pickups for the driver in the specified period
    const pickups = await Pickup.findAll({
      where: {
        driver_id: driverId,
        scheduled_date: {
          [Op.between]: [new Date(startDate), new Date(endDate)]
        }
      }
    });

    // Get all fuel records for the driver in the specified period
    const fuelRecords = await FuelRecord.findAll({
      where: {
        driver_id: driverId,
        date: {
          [Op.between]: [new Date(startDate), new Date(endDate)]
        }
      }
    });

    // Group data by the specified interval
    const trendData = groupDataByInterval(deliveries, pickups, fuelRecords, interval, startDate, endDate);

    return res.status(200).json({
      driverPerformanceTrends: {
        driverId: driver.id,
        driverName: `${driver.first_name} ${driver.last_name}`,
        period: {
          startDate,
          endDate,
          interval
        },
        trends: trendData
      }
    });
  } catch (error) {
    console.error('Error getting driver performance trends:', error);
    return res.status(500).json({ error: 'Failed to get driver performance trends' });
  }
};

/**
 * Helper function to calculate average
 */
const calculateAverage = (values) => {
  if (values.length === 0) return 0;
  const sum = values.reduce((total, value) => total + value, 0);
  return sum / values.length;
};

/**
 * Helper function to group data by interval
 */
const groupDataByInterval = (deliveries, pickups, fuelRecords, interval, startDate, endDate) => {
  const startDateObj = new Date(startDate);
  const endDateObj = new Date(endDate);
  
  // Generate time periods based on interval
  const periods = [];
  let currentDate = new Date(startDateObj);
  
  while (currentDate <= endDateObj) {
    let periodEnd;
    let periodKey;
    
    if (interval === 'day') {
      periodKey = currentDate.toISOString().split('T')[0]; // YYYY-MM-DD
      periodEnd = new Date(currentDate);
      periodEnd.setDate(periodEnd.getDate() + 1);
    } else if (interval === 'week') {
      // Get the week number
      const firstDayOfYear = new Date(currentDate.getFullYear(), 0, 1);
      const pastDaysOfYear = (currentDate - firstDayOfYear) / 86400000;
      const weekNumber = Math.ceil((pastDaysOfYear + firstDayOfYear.getDay() + 1) / 7);
      
      periodKey = `${currentDate.getFullYear()}-W${weekNumber}`;
      periodEnd = new Date(currentDate);
      periodEnd.setDate(periodEnd.getDate() + 7);
    } else { // month
      periodKey = `${currentDate.getFullYear()}-${currentDate.getMonth() + 1}`; // YYYY-MM
      periodEnd = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 1);
    }
    
    periods.push({
      key: periodKey,
      start: new Date(currentDate),
      end: new Date(periodEnd)
    });
    
    // Move to next period
    currentDate = periodEnd;
  }
  
  // Initialize trend data for each period
  const trendData = periods.map(period => ({
    period: period.key,
    deliveries: {
      total: 0,
      completed: 0,
      onTime: 0,
      onTimeRate: 0
    },
    pickups: {
      total: 0,
      completed: 0,
      onTime: 0,
      onTimeRate: 0
    },
    tasks: {
      total: 0,
      completed: 0,
      completionRate: 0
    },
    fuel: {
      amount: 0,
      distance: 0,
      efficiency: 0
    }
  }));
  
  // Process deliveries
  deliveries.forEach(delivery => {
    const deliveryDate = new Date(delivery.scheduled_date);
    
    // Find the period this delivery belongs to
    const periodIndex = periods.findIndex(period => 
      deliveryDate >= period.start && deliveryDate < period.end
    );
    
    if (periodIndex >= 0) {
      const period = trendData[periodIndex];
      
      // Update delivery metrics
      period.deliveries.total += 1;
      
      if (delivery.status === 'completed') {
        period.deliveries.completed += 1;
      }
      
      if (delivery.actual_delivery_time && delivery.scheduled_time) {
        const scheduledTime = new Date(delivery.scheduled_time);
        const actualTime = new Date(delivery.actual_delivery_time);
        
        const diffMinutes = (actualTime - scheduledTime) / (1000 * 60);
        if (diffMinutes <= 15) {
          period.deliveries.onTime += 1;
        }
      }
      
      // Update task metrics
      period.tasks.total += 1;
      
      if (delivery.status === 'completed') {
        period.tasks.completed += 1;
      }
      
      // Update distance
      if (delivery.distance) {
        period.fuel.distance += delivery.distance;
      }
    }
  });
  
  // Process pickups
  pickups.forEach(pickup => {
    const pickupDate = new Date(pickup.scheduled_date);
    
    // Find the period this pickup belongs to
    const periodIndex = periods.findIndex(period => 
      pickupDate >= period.start && pickupDate < period.end
    );
    
    if (periodIndex >= 0) {
      const period = trendData[periodIndex];
      
      // Update pickup metrics
      period.pickups.total += 1;
      
      if (pickup.status === 'completed') {
        period.pickups.completed += 1;
      }
      
      if (pickup.actual_pickup_time && pickup.scheduled_time) {
        const scheduledTime = new Date(pickup.scheduled_time);
        const actualTime = new Date(pickup.actual_pickup_time);
        
        const diffMinutes = (actualTime - scheduledTime) / (1000 * 60);
        if (diffMinutes <= 15) {
          period.pickups.onTime += 1;
        }
      }
      
      // Update task metrics
      period.tasks.total += 1;
      
      if (pickup.status === 'completed') {
        period.tasks.completed += 1;
      }
      
      // Update distance
      if (pickup.distance) {
        period.fuel.distance += pickup.distance;
      }
    }
  });
  
  // Process fuel records
  fuelRecords.forEach(record => {
    const recordDate = new Date(record.date);
    
    // Find the period this fuel record belongs to
    const periodIndex = periods.findIndex(period => 
      recordDate >= period.start && recordDate < period.end
    );
    
    if (periodIndex >= 0) {
      const period = trendData[periodIndex];
      
      // Update fuel amount
      period.fuel.amount += record.amount;
    }
  });
  
  // Calculate rates and efficiency for each period
  trendData.forEach(period => {
    // Calculate on-time rates
    period.deliveries.onTimeRate = period.deliveries.total > 0 
      ? (period.deliveries.onTime / period.deliveries.total) * 100 
      : 0;
      
    period.pickups.onTimeRate = period.pickups.total > 0 
      ? (period.pickups.onTime / period.pickups.total) * 100 
      : 0;
      
    // Calculate completion rate
    period.tasks.completionRate = period.tasks.total > 0 
      ? (period.tasks.completed / period.tasks.total) * 100 
      : 0;
      
    // Calculate fuel efficiency
    period.fuel.efficiency = period.fuel.amount > 0 
      ? period.fuel.distance / period.fuel.amount 
      : 0;
  });
  
  return trendData;
};