import { sequelize } from '../config/database.js';
import InvoiceDispute from '../models/InvoiceDispute.js';
import InvoiceDisputeMessage from '../models/InvoiceDisputeMessage.js';
import Invoice from '../models/Invoice.js';
import User from '../models/User.js';
import Farm from '../models/Farm.js';
import InvoiceAuditService from '../services/invoiceAuditService.js';
import InvoiceNotificationService from '../services/invoiceNotificationService.js';
import InvoicePermissionService from '../services/invoicePermissionService.js';

/**
 * Create a new invoice dispute
 */
export const createInvoiceDispute = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { invoiceId } = req.params;
    const {
      disputeType,
      subject,
      description,
      disputedAmount,
      priority = 'medium',
      supportingDocuments = [],
      tags = []
    } = req.body;

    // Check permission
    const permissionResult = await InvoicePermissionService.checkInvoicePermission({
      userId: req.user.id,
      farmId: req.farmId || req.user.activeFarmId,
      invoiceId,
      action: 'dispute',
      req,
      isGlobalAdmin: req.user.is_global_admin
    });

    if (!permissionResult.allowed) {
      await transaction.rollback();
      return res.status(403).json({
        error: permissionResult.error || 'Permission denied'
      });
    }

    // Get the invoice
    const invoice = await Invoice.findByPk(invoiceId, {
      include: [
        { model: Farm, as: 'farm', attributes: ['id', 'name'] },
        { model: Farm, as: 'recipientFarm', attributes: ['id', 'name'] }
      ]
    });

    if (!invoice) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Invoice not found' });
    }

    // Check if invoice can be disputed
    if (invoice.status === 'cancelled') {
      await transaction.rollback();
      return res.status(400).json({
        error: 'Cancelled invoices cannot be disputed'
      });
    }

    // Generate dispute number
    const disputeCount = await InvoiceDispute.count();
    const disputeNumber = `DISP-${String(disputeCount + 1).padStart(6, '0')}`;

    // Create the dispute
    const dispute = await InvoiceDispute.create({
      invoice_id: invoiceId,
      dispute_number: disputeNumber,
      raised_by_farm_id: req.farmId || req.user.activeFarmId,
      raised_by_user_id: req.user.id,
      dispute_type: disputeType,
      subject,
      description,
      disputed_amount: disputedAmount,
      priority,
      supporting_documents: supportingDocuments,
      tags,
      due_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 days from now
    }, { transaction });

    // Create initial message
    await InvoiceDisputeMessage.create({
      dispute_id: dispute.id,
      sender_user_id: req.user.id,
      sender_farm_id: req.farmId || req.user.activeFarmId,
      message_type: 'message',
      subject: 'Dispute Created',
      content: description,
      attachments: supportingDocuments
    }, { transaction });

    await transaction.commit();

    // Log the dispute creation
    // Ensure we have a valid farmId for the audit log
    const userFarmId = req.farmId || req.user.activeFarmId;
    const auditFarmId = userFarmId || invoice.farm_id || invoice.recipientFarm?.id;
    if (auditFarmId) {
      await InvoiceAuditService.logDispute(
        invoiceId,
        req.user.id,
        auditFarmId,
        req,
        {
          dispute_id: dispute.id,
          dispute_number: disputeNumber,
          dispute_type: disputeType
        }
      );
    } else {
      console.error('Unable to log invoice dispute: No valid farm ID available');
    }

    // Send notifications
    const otherFarm = invoice.farm_id === userFarmId ? invoice.recipientFarm : invoice.farm;

    if (otherFarm) {
      await InvoiceNotificationService.createNotification({
        invoiceId,
        recipientFarmId: otherFarm.id,
        senderFarmId: userFarmId,
        notificationType: 'invoice_disputed',
        title: `Invoice #${invoice.invoice_number} Disputed`,
        message: `A dispute has been raised for invoice #${invoice.invoice_number}: ${subject}`,
        priority: 'high',
        channels: ['in_app', 'email'],
        actionUrl: `/invoices/${invoiceId}/disputes/${dispute.id}`,
        metadata: {
          dispute_id: dispute.id,
          dispute_number: disputeNumber,
          dispute_type: disputeType
        }
      });
    }

    // Return the created dispute with related data
    const createdDispute = await InvoiceDispute.findByPk(dispute.id, {
      include: [
        {
          model: Invoice,
          as: 'invoice',
          attributes: ['id', 'invoice_number', 'total_amount']
        },
        {
          model: Farm,
          as: 'raisedByFarm',
          attributes: ['id', 'name']
        },
        {
          model: User,
          as: 'raisedByUser',
          attributes: ['id', 'first_name', 'last_name', 'email']
        }
      ]
    });

    return res.status(201).json({
      dispute: createdDispute,
      message: 'Dispute created successfully'
    });

  } catch (error) {
    await transaction.rollback();
    console.error('Error creating invoice dispute:', error);
    return res.status(500).json({
      error: 'Failed to create dispute'
    });
  }
};

/**
 * Get disputes for an invoice
 */
export const getInvoiceDisputes = async (req, res) => {
  try {
    const { invoiceId } = req.params;
    const { status, limit = 20, offset = 0 } = req.query;

    // Check permission to view invoice
    const permissionResult = await InvoicePermissionService.checkInvoicePermission({
      userId: req.user.id,
      farmId: req.farmId || req.user.activeFarmId,
      invoiceId,
      action: 'view',
      req,
      isGlobalAdmin: req.user.is_global_admin
    });

    if (!permissionResult.allowed) {
      return res.status(403).json({
        error: permissionResult.error || 'Permission denied'
      });
    }

    const where = { invoice_id: invoiceId };
    if (status) {
      where.status = status;
    }

    const disputes = await InvoiceDispute.findAll({
      where,
      include: [
        {
          model: Farm,
          as: 'raisedByFarm',
          attributes: ['id', 'name']
        },
        {
          model: User,
          as: 'raisedByUser',
          attributes: ['id', 'first_name', 'last_name', 'email']
        },
        {
          model: User,
          as: 'resolvedByUser',
          attributes: ['id', 'first_name', 'last_name', 'email']
        },
        {
          model: InvoiceDisputeMessage,
          as: 'messages',
          limit: 1,
          order: [['created_at', 'DESC']],
          include: [
            {
              model: User,
              as: 'senderUser',
              attributes: ['id', 'first_name', 'last_name']
            }
          ]
        }
      ],
      order: [['created_at', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

    return res.status(200).json({ disputes });

  } catch (error) {
    console.error('Error getting invoice disputes:', error);
    return res.status(500).json({
      error: 'Failed to get disputes'
    });
  }
};

/**
 * Get a specific dispute
 */
export const getDispute = async (req, res) => {
  try {
    const { disputeId } = req.params;

    const dispute = await InvoiceDispute.findByPk(disputeId, {
      include: [
        {
          model: Invoice,
          as: 'invoice',
          attributes: ['id', 'invoice_number', 'total_amount', 'farm_id', 'recipient_farm_id']
        },
        {
          model: Farm,
          as: 'raisedByFarm',
          attributes: ['id', 'name']
        },
        {
          model: User,
          as: 'raisedByUser',
          attributes: ['id', 'first_name', 'last_name', 'email']
        },
        {
          model: User,
          as: 'resolvedByUser',
          attributes: ['id', 'first_name', 'last_name', 'email']
        },
        {
          model: InvoiceDisputeMessage,
          as: 'messages',
          include: [
            {
              model: User,
              as: 'senderUser',
              attributes: ['id', 'first_name', 'last_name']
            },
            {
              model: Farm,
              as: 'senderFarm',
              attributes: ['id', 'name']
            }
          ],
          order: [['created_at', 'ASC']]
        }
      ]
    });

    if (!dispute) {
      return res.status(404).json({ error: 'Dispute not found' });
    }

    // Check if user has access to this dispute
    const userFarmId = req.farmId || req.user.activeFarmId;
    const hasAccess = (
      dispute.invoice.farm_id === userFarmId ||
      dispute.invoice.recipient_farm_id === userFarmId ||
      req.user.is_global_admin
    );

    if (!hasAccess) {
      return res.status(403).json({
        error: 'You do not have access to this dispute'
      });
    }

    return res.status(200).json({ dispute });

  } catch (error) {
    console.error('Error getting dispute:', error);
    return res.status(500).json({
      error: 'Failed to get dispute'
    });
  }
};

/**
 * Add a message to a dispute
 */
export const addDisputeMessage = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { disputeId } = req.params;
    const {
      messageType = 'message',
      subject,
      content,
      attachments = [],
      isInternal = false
    } = req.body;

    // Get the dispute
    const dispute = await InvoiceDispute.findByPk(disputeId, {
      include: [
        {
          model: Invoice,
          as: 'invoice',
          attributes: ['id', 'farm_id', 'recipient_farm_id']
        }
      ]
    });

    if (!dispute) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Dispute not found' });
    }

    // Check access
    const userFarmId = req.farmId || req.user.activeFarmId;
    const hasAccess = (
      dispute.invoice.farm_id === userFarmId ||
      dispute.invoice.recipient_farm_id === userFarmId ||
      req.user.is_global_admin
    );

    if (!hasAccess) {
      await transaction.rollback();
      return res.status(403).json({
        error: 'You do not have access to this dispute'
      });
    }

    // Create the message
    const message = await InvoiceDisputeMessage.create({
      dispute_id: disputeId,
      sender_user_id: req.user.id,
      sender_farm_id: userFarmId,
      message_type: messageType,
      subject,
      content,
      attachments,
      is_internal: isInternal
    }, { transaction });

    // Update dispute status if needed
    if (dispute.status === 'open') {
      await dispute.update({
        status: 'under_review'
      }, { transaction });
    }

    await transaction.commit();

    // Send notification to other party
    if (!isInternal) {
      const otherFarmId = dispute.invoice.farm_id === userFarmId 
        ? dispute.invoice.recipient_farm_id 
        : dispute.invoice.farm_id;

      if (otherFarmId) {
        await InvoiceNotificationService.createNotification({
          invoiceId: dispute.invoice.id,
          recipientFarmId: otherFarmId,
          senderFarmId: userFarmId,
          notificationType: 'dispute_message',
          title: `New Message in Dispute #${dispute.dispute_number}`,
          message: `A new message has been added to dispute #${dispute.dispute_number}`,
          priority: 'medium',
          channels: ['in_app', 'email'],
          actionUrl: `/invoices/${dispute.invoice.id}/disputes/${disputeId}`,
          metadata: {
            dispute_id: disputeId,
            dispute_number: dispute.dispute_number,
            message_type: messageType
          }
        });
      }
    }

    // Return the created message with related data
    const createdMessage = await InvoiceDisputeMessage.findByPk(message.id, {
      include: [
        {
          model: User,
          as: 'senderUser',
          attributes: ['id', 'first_name', 'last_name']
        },
        {
          model: Farm,
          as: 'senderFarm',
          attributes: ['id', 'name']
        }
      ]
    });

    return res.status(201).json({
      message: createdMessage,
      success: 'Message added successfully'
    });

  } catch (error) {
    await transaction.rollback();
    console.error('Error adding dispute message:', error);
    return res.status(500).json({
      error: 'Failed to add message'
    });
  }
};

/**
 * Get messages for a dispute
 */
export const getDisputeMessages = async (req, res) => {
  try {
    const { disputeId } = req.params;
    const { limit = 50, offset = 0 } = req.query;

    // Get the dispute to check access
    const dispute = await InvoiceDispute.findByPk(disputeId, {
      include: [
        {
          model: Invoice,
          as: 'invoice',
          attributes: ['id', 'farm_id', 'recipient_farm_id']
        }
      ]
    });

    if (!dispute) {
      return res.status(404).json({ error: 'Dispute not found' });
    }

    // Check access
    const userFarmId = req.farmId || req.user.activeFarmId;
    const hasAccess = (
      dispute.invoice.farm_id === userFarmId ||
      dispute.invoice.recipient_farm_id === userFarmId ||
      req.user.is_global_admin
    );

    if (!hasAccess) {
      return res.status(403).json({
        error: 'You do not have access to this dispute'
      });
    }

    const messages = await InvoiceDisputeMessage.findAll({
      where: { dispute_id: disputeId },
      include: [
        {
          model: User,
          as: 'senderUser',
          attributes: ['id', 'first_name', 'last_name']
        },
        {
          model: Farm,
          as: 'senderFarm',
          attributes: ['id', 'name']
        }
      ],
      order: [['created_at', 'ASC']],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

    return res.status(200).json({ messages });

  } catch (error) {
    console.error('Error getting dispute messages:', error);
    return res.status(500).json({
      error: 'Failed to get messages'
    });
  }
};

/**
 * Resolve a dispute
 */
export const resolveDispute = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { disputeId } = req.params;
    const { resolution, resolutionType = 'resolved' } = req.body;

    if (!resolution) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Resolution is required' });
    }

    // Get the dispute
    const dispute = await InvoiceDispute.findByPk(disputeId, {
      include: [
        {
          model: Invoice,
          as: 'invoice',
          attributes: ['id', 'farm_id', 'recipient_farm_id', 'invoice_number']
        }
      ]
    });

    if (!dispute) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Dispute not found' });
    }

    // Check if user can resolve this dispute
    const userFarmId = req.farmId || req.user.activeFarmId;
    const canResolve = (
      dispute.invoice.farm_id === userFarmId || // Sender can resolve
      req.user.is_global_admin
    );

    if (!canResolve) {
      await transaction.rollback();
      return res.status(403).json({
        error: 'Only the invoice sender or global admin can resolve disputes'
      });
    }

    // Update dispute
    await dispute.update({
      status: resolutionType,
      resolution,
      resolved_by_user_id: req.user.id,
      resolved_at: new Date()
    }, { transaction });

    // Add resolution message
    await InvoiceDisputeMessage.create({
      dispute_id: disputeId,
      sender_user_id: req.user.id,
      sender_farm_id: userFarmId,
      message_type: 'status_update',
      subject: 'Dispute Resolved',
      content: `Dispute has been resolved: ${resolution}`,
      is_internal: false
    }, { transaction });

    await transaction.commit();

    // Send notification
    const otherFarmId = dispute.invoice.farm_id === userFarmId
      ? dispute.invoice.recipient_farm_id
      : dispute.invoice.farm_id;

    if (otherFarmId) {
      await InvoiceNotificationService.createNotification({
        invoiceId: dispute.invoice.id,
        recipientFarmId: otherFarmId,
        senderFarmId: userFarmId,
        notificationType: 'dispute_resolved',
        title: `Dispute #${dispute.dispute_number} Resolved`,
        message: `Dispute for invoice #${dispute.invoice.invoice_number} has been resolved.`,
        priority: 'medium',
        channels: ['in_app', 'email'],
        actionUrl: `/invoices/${dispute.invoice.id}/disputes/${disputeId}`,
        metadata: {
          dispute_id: disputeId,
          dispute_number: dispute.dispute_number,
          resolution_type: resolutionType
        }
      });
    }

    return res.status(200).json({
      message: 'Dispute resolved successfully',
      dispute
    });

  } catch (error) {
    await transaction.rollback();
    console.error('Error resolving dispute:', error);
    return res.status(500).json({
      error: 'Failed to resolve dispute'
    });
  }
};

/**
 * Escalate a dispute
 */
export const escalateDispute = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { disputeId } = req.params;
    const { escalationReason, escalateToUserId } = req.body;

    if (!escalationReason) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Escalation reason is required' });
    }

    // Get the dispute
    const dispute = await InvoiceDispute.findByPk(disputeId, {
      include: [
        {
          model: Invoice,
          as: 'invoice',
          attributes: ['id', 'farm_id', 'recipient_farm_id', 'invoice_number']
        }
      ]
    });

    if (!dispute) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Dispute not found' });
    }

    // Check access
    const userFarmId = req.farmId || req.user.activeFarmId;
    const hasAccess = (
      dispute.invoice.farm_id === userFarmId ||
      dispute.invoice.recipient_farm_id === userFarmId ||
      req.user.is_global_admin
    );

    if (!hasAccess) {
      await transaction.rollback();
      return res.status(403).json({
        error: 'You do not have access to this dispute'
      });
    }

    // Update dispute
    await dispute.update({
      status: 'escalated',
      escalated_at: new Date(),
      escalated_to_user_id: escalateToUserId,
      priority: 'high' // Escalated disputes get high priority
    }, { transaction });

    // Add escalation message
    await InvoiceDisputeMessage.create({
      dispute_id: disputeId,
      sender_user_id: req.user.id,
      sender_farm_id: userFarmId,
      message_type: 'status_update',
      subject: 'Dispute Escalated',
      content: `Dispute has been escalated: ${escalationReason}`,
      is_internal: false
    }, { transaction });

    await transaction.commit();

    return res.status(200).json({
      message: 'Dispute escalated successfully',
      dispute
    });

  } catch (error) {
    await transaction.rollback();
    console.error('Error escalating dispute:', error);
    return res.status(500).json({
      error: 'Failed to escalate dispute'
    });
  }
};
