import DashboardLayout from '../models/DashboardLayout.js';
import FarmDashboardLayout from '../models/FarmDashboardLayout.js';
import GlobalDashboardLayout from '../models/GlobalDashboardLayout.js';
import UserFarm from '../models/UserFarm.js';
import Farm from '../models/Farm.js';
import User from '../models/User.js';
import SubscriptionTransaction from '../models/SubscriptionTransaction.js';
import SubscriptionPlan from '../models/SubscriptionPlan.js';
import Document from '../models/Document.js';
import Transaction from '../models/Transaction.js';
import { sequelize } from '../config/database.js';
import { Op } from 'sequelize';
import { format, subMonths, startOfMonth, endOfMonth, parseISO } from 'date-fns';

// Get admin dashboard statistics
export const getStats = async (req, res) => {
  // Save the current search_path to restore it later
  let currentSearchPath;
  // Get the schema from environment variables
  const schema = process.env.DB_SCHEMA || 'site';
  // Get time range from query params (default to 6m)
  const timeRange = req.query.timeRange || '6m';

  try {
    // Check if user is a global admin
    if (!req.user.is_global_admin) {
      return res.status(403).json({ error: 'Unauthorized. Global admin access required.' });
    }

    // Save the current search_path to restore it later
    const currentSearchPathResult = await sequelize.query('SHOW search_path;', { type: sequelize.QueryTypes.SELECT });
    currentSearchPath = currentSearchPathResult[0].search_path;

    // Set search_path to include both the farm-specific schema and site schema
    // If req.farmSchema exists (set by middleware), use it along with site schema
    // Otherwise, just use site schema
    if (req.farmSchema) {
      await sequelize.query(`SET search_path TO ${req.farmSchema}, site;`);
    } else {
      await sequelize.query(`SET search_path TO site;`);
    }

    try {
      // Get total farms count
      const totalFarms = await Farm.count();

      // Get active farms count (farms with active subscription status)
      const activeFarms = await Farm.count({
        where: { subscription_status: 'active' }
      });

      // Get total subscriptions count
      const totalSubscriptions = await Farm.count({
        where: {
          subscription_plan_id: { [Op.ne]: null },
          subscription_status: 'active'
        }
      });

      // Get revenue this month
      const currentDate = new Date();
      const firstDayOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
      const lastDayOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0);

      // Format dates as ISO strings for database compatibility
      const firstDayISO = firstDayOfMonth.toISOString();
      const lastDayISO = lastDayOfMonth.toISOString();

      let revenueThisMonth = 0;
      try {
        // Check if the SubscriptionTransaction table exists
        const tableExists = await sequelize.query(
          `SELECT EXISTS (
            SELECT FROM information_schema.tables
            WHERE table_schema = '${schema}'
            AND table_name = 'subscription_transactions'
          );`,
          { type: sequelize.QueryTypes.SELECT }
        );

        if (tableExists[0].exists) {
          const revenue = await SubscriptionTransaction.sum('amount', {
            where: {
              created_at: {
                [Op.between]: [firstDayISO, lastDayISO]
              },
              status: 'completed'
            }
          });
          revenueThisMonth = revenue || 0;
        } else {
          console.warn('SubscriptionTransaction table does not exist in the database');
        }
      } catch (error) {
        console.error('Error calculating revenue:', error);
        // Continue with revenueThisMonth as 0
      }

      // Calculate date range based on timeRange parameter
      let startDate;
      const endDate = new Date();

      switch(timeRange) {
        case '1m':
          startDate = subMonths(endDate, 1);
          break;
        case '3m':
          startDate = subMonths(endDate, 3);
          break;
        case '1y':
          startDate = subMonths(endDate, 12);
          break;
        case '6m':
        default:
          startDate = subMonths(endDate, 6);
          break;
      }

      // Format dates as ISO strings for database compatibility
      const startDateISO = startDate.toISOString();
      const endDateISO = endDate.toISOString();

      // Generate month labels for charts
      const monthLabels = [];
      let currentMonth = startOfMonth(startDate);
      const lastMonth = endOfMonth(endDate);

      while (currentMonth <= lastMonth) {
        monthLabels.push(format(currentMonth, 'MMM'));
        currentMonth = startOfMonth(subMonths(currentMonth, -1)); // Move to next month
      }

      // Get user growth data
      let userGrowthData = Array(monthLabels.length).fill(0);
      try {
        const usersByMonth = await User.findAll({
          attributes: [
            [sequelize.fn('date_trunc', 'month', sequelize.col('created_at')), 'month'],
            [sequelize.fn('count', sequelize.col('id')), 'count']
          ],
          where: {
            created_at: {
              [Op.between]: [startDateISO, endDateISO]
            }
          },
          group: [sequelize.fn('date_trunc', 'month', sequelize.col('created_at'))],
          order: [sequelize.fn('date_trunc', 'month', sequelize.col('created_at'))]
        });

        usersByMonth.forEach(item => {
          const monthIndex = monthLabels.indexOf(format(parseISO(item.dataValues.month), 'MMM'));
          if (monthIndex !== -1) {
            userGrowthData[monthIndex] = parseInt(item.dataValues.count);
          }
        });
      } catch (error) {
        console.error('Error getting user growth data:', error);
      }

      // Get farm registrations data
      let farmRegistrationsData = Array(monthLabels.length).fill(0);
      try {
        const farmsByMonth = await Farm.findAll({
          attributes: [
            [sequelize.fn('date_trunc', 'month', sequelize.col('created_at')), 'month'],
            [sequelize.fn('count', sequelize.col('id')), 'count']
          ],
          where: {
            created_at: {
              [Op.between]: [startDateISO, endDateISO]
            }
          },
          group: [sequelize.fn('date_trunc', 'month', sequelize.col('created_at'))],
          order: [sequelize.fn('date_trunc', 'month', sequelize.col('created_at'))]
        });

        farmsByMonth.forEach(item => {
          const monthIndex = monthLabels.indexOf(format(parseISO(item.dataValues.month), 'MMM'));
          if (monthIndex !== -1) {
            farmRegistrationsData[monthIndex] = parseInt(item.dataValues.count);
          }
        });
      } catch (error) {
        console.error('Error getting farm registrations data:', error);
      }

      // Get subscription plan distribution
      let subscriptionDistributionLabels = [];
      let subscriptionDistributionData = [];
      try {
        const subscriptionPlans = await SubscriptionPlan.findAll({
          attributes: ['name', 'id']
        });

        const planCounts = await Farm.findAll({
          attributes: [
            'subscription_plan_id',
            [sequelize.fn('count', sequelize.col('id')), 'count']
          ],
          where: {
            subscription_plan_id: { [Op.ne]: null },
            subscription_status: 'active'
          },
          group: ['subscription_plan_id']
        });

        // Create a map of plan IDs to names
        const planMap = {};
        subscriptionPlans.forEach(plan => {
          planMap[plan.id] = plan.name;
        });

        // Map the counts to plan names
        planCounts.forEach(item => {
          const planId = item.dataValues.subscription_plan_id;
          const planName = planMap[planId] || 'Unknown';
          subscriptionDistributionLabels.push(planName);
          subscriptionDistributionData.push(parseInt(item.dataValues.count));
        });

        // If no data, provide default values
        if (subscriptionDistributionLabels.length === 0) {
          subscriptionDistributionLabels = ['No Data'];
          subscriptionDistributionData = [0];
        }
      } catch (error) {
        console.error('Error getting subscription distribution data:', error);
        subscriptionDistributionLabels = ['Error'];
        subscriptionDistributionData = [0];
      }

      // Get monthly revenue data
      let revenueByMonthData = Array(monthLabels.length).fill(0);
      try {
        const revenueByMonth = await SubscriptionTransaction.findAll({
          attributes: [
            [sequelize.fn('date_trunc', 'month', sequelize.col('created_at')), 'month'],
            [sequelize.fn('sum', sequelize.col('amount')), 'total']
          ],
          where: {
            created_at: {
              [Op.between]: [startDateISO, endDateISO]
            },
            status: 'completed'
          },
          group: [sequelize.fn('date_trunc', 'month', sequelize.col('created_at'))],
          order: [sequelize.fn('date_trunc', 'month', sequelize.col('created_at'))]
        });

        revenueByMonth.forEach(item => {
          const monthIndex = monthLabels.indexOf(format(parseISO(item.dataValues.month), 'MMM'));
          if (monthIndex !== -1) {
            revenueByMonthData[monthIndex] = parseFloat(item.dataValues.total);
          }
        });
      } catch (error) {
        console.error('Error getting monthly revenue data:', error);
      }

      // Get document uploads data
      let documentUploadsData = Array(monthLabels.length).fill(0);
      try {
        const documentsByMonth = await Document.findAll({
          attributes: [
            [sequelize.fn('date_trunc', 'month', sequelize.col('created_at')), 'month'],
            [sequelize.fn('count', sequelize.col('id')), 'count']
          ],
          where: {
            created_at: {
              [Op.between]: [startDateISO, endDateISO]
            }
          },
          group: [sequelize.fn('date_trunc', 'month', sequelize.col('created_at'))],
          order: [sequelize.fn('date_trunc', 'month', sequelize.col('created_at'))]
        });

        documentsByMonth.forEach(item => {
          const monthIndex = monthLabels.indexOf(format(parseISO(item.dataValues.month), 'MMM'));
          if (monthIndex !== -1) {
            documentUploadsData[monthIndex] = parseInt(item.dataValues.count);
          }
        });
      } catch (error) {
        console.error('Error getting document uploads data:', error);
      }

      // Get transaction volume data
      let transactionVolumeData = Array(monthLabels.length).fill(0);
      try {
        const transactionsByMonth = await Transaction.findAll({
          attributes: [
            [sequelize.fn('date_trunc', 'month', sequelize.col('created_at')), 'month'],
            [sequelize.fn('count', sequelize.col('id')), 'count']
          ],
          where: {
            created_at: {
              [Op.between]: [startDateISO, endDateISO]
            }
          },
          group: [sequelize.fn('date_trunc', 'month', sequelize.col('created_at'))],
          order: [sequelize.fn('date_trunc', 'month', sequelize.col('created_at'))]
        });

        transactionsByMonth.forEach(item => {
          const monthIndex = monthLabels.indexOf(format(parseISO(item.dataValues.month), 'MMM'));
          if (monthIndex !== -1) {
            transactionVolumeData[monthIndex] = parseInt(item.dataValues.count);
          }
        });
      } catch (error) {
        console.error('Error getting transaction volume data:', error);
      }

      // Get active users count
      let activeUsers = 0;
      try {
        activeUsers = await User.count({
          where: {
            last_login: {
              [Op.gte]: subMonths(new Date(), 1).toISOString()
            }
          }
        });
      } catch (error) {
        console.error('Error getting active users count:', error);
      }

      // Restore the original search_path
      await sequelize.query(`SET search_path TO ${currentSearchPath};`);

      return res.status(200).json({
        totalFarms,
        activeFarms,
        totalSubscriptions,
        revenueThisMonth,
        activeUsers,
        userGrowth: {
          labels: monthLabels,
          data: userGrowthData
        },
        farmRegistrations: {
          labels: monthLabels,
          data: farmRegistrationsData
        },
        subscriptionDistribution: {
          labels: subscriptionDistributionLabels,
          data: subscriptionDistributionData
        },
        revenueByMonth: {
          labels: monthLabels,
          data: revenueByMonthData
        },
        documentUploads: {
          labels: monthLabels,
          data: documentUploadsData
        },
        transactionVolume: {
          labels: monthLabels,
          data: transactionVolumeData
        }
      });
    } catch (error) {
      // Restore the original search_path in case of error
      await sequelize.query(`SET search_path TO ${currentSearchPath};`);
      throw error;
    }
  } catch (error) {
    console.error('Error getting admin dashboard stats:', error);

    // Restore the original search_path in case of error
    if (currentSearchPath) {
      try {
        await sequelize.query(`SET search_path TO ${currentSearchPath};`);
      } catch (restoreError) {
        console.error('Error restoring search_path:', restoreError);
      }
    }

    return res.status(500).json({ error: 'Failed to get dashboard statistics' });
  }
};

// Get dashboard layout for a user
export const getUserDashboardLayout = async (req, res) => {
  try {
    const userId = req.params.userId;
    const layoutId = req.query.layoutId; // Optional: specific layout ID to retrieve

    // Validate UUID format
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(userId)) {
      return res.status(400).json({ error: 'Invalid user ID format' });
    }

    // Validate layoutId format if provided
    if (layoutId && !uuidRegex.test(layoutId)) {
      return res.status(400).json({ error: 'Invalid layout ID format' });
    }

    // Check if the requesting user is authorized to access this user's dashboard layout
    if (req.user.id !== userId && !req.user.is_global_admin) {
      return res.status(403).json({ error: 'Unauthorized to access this dashboard layout' });
    }

    // Save the current search_path to restore it later
    let currentSearchPath = 'site';
    try {
      const currentSearchPathResult = await sequelize.query('SHOW search_path;', { type: sequelize.QueryTypes.SELECT });
      currentSearchPath = currentSearchPathResult[0].search_path;

      // Set search_path to include both the farm-specific schema and site schema
      // If req.farmSchema exists (set by middleware), use it along with site schema
      // Otherwise, just use site schema
      if (req.farmSchema) {
        await sequelize.query(`SET search_path TO ${req.farmSchema}, site;`);
      } else {
        await sequelize.query(`SET search_path TO site;`);
      }
    } catch (error) {
      console.error('Error setting search path:', error);
      // Continue with default search path
    }

    try {
      // Find the user's dashboard layout - either by ID or active status
      let dashboardLayout;

      try {
        if (layoutId) {
          // If layoutId is provided, get that specific layout
          dashboardLayout = await DashboardLayout.findOne({
            where: {
              id: layoutId,
              user_id: userId
            }
          });
        } else {
          // Otherwise, get the active layout
          dashboardLayout = await DashboardLayout.findOne({
            where: {
              user_id: userId,
              is_active: true
            }
          });
        }
      } catch (error) {
        console.error('Error finding user dashboard layout:', error);
        // Continue with dashboardLayout as null
      }

      if (!dashboardLayout) {
        // If no custom layout exists, check if the user belongs to a farm with a default layout
        let userFarms = [];
        let farmDashboardLayout = null;
        let farmId = null;

        try {
          userFarms = await UserFarm.findAll({
            where: { user_id: userId }
          });

          if (userFarms.length > 0) {
            // Get the first farm's default layout
            farmId = userFarms[0].farm_id;
            farmDashboardLayout = await FarmDashboardLayout.findOne({
              where: { farm_id: farmId }
            });

            if (farmDashboardLayout) {
              // Restore the original search_path
              try {
                await sequelize.query(`SET search_path TO ${currentSearchPath};`);
              } catch (error) {
                console.error('Error restoring search path:', error);
              }
              return res.status(200).json({
                layout: farmDashboardLayout.layout_config,
                isDefault: true,
                farmId: farmId
              });
            }
          }
        } catch (error) {
          console.error('Error finding farm dashboard layout:', error);
          // Continue to next fallback
        }

        // If no farm default layout exists, get the user's type to determine the appropriate dashboard type
        let user = null;
        let dashboardType = 'farm'; // Default dashboard type

        try {
          user = await User.findByPk(userId);
          if (user && (user.user_type === 'supplier' || user.user_type === 'vet' || user.is_business_owner)) {
            dashboardType = 'business';
          }
        } catch (error) {
          console.error('Error finding user:', error);
          // Continue with default dashboard type
        }

        // Check if there's a global default layout for this dashboard type
        let globalDashboardLayout = null;

        try {
          globalDashboardLayout = await GlobalDashboardLayout.findOne({
            where: {
              dashboard_type: dashboardType,
              is_active: true
            }
          });
        } catch (error) {
          console.error('Error finding global dashboard layout:', error);
          // Continue with globalDashboardLayout as null
        }

        // Restore the original search_path
        try {
          await sequelize.query(`SET search_path TO ${currentSearchPath};`);
        } catch (error) {
          console.error('Error restoring search path:', error);
        }

        // Return global default if it exists, otherwise return hardcoded default
        if (globalDashboardLayout) {
          return res.status(200).json({
            layout: globalDashboardLayout.layout_config,
            isDefault: true,
            dashboardType,
            isGlobalDefault: true
          });
        } else {
          return res.status(200).json({
            layout: getDefaultDashboardLayout(dashboardType),
            isDefault: true,
            dashboardType
          });
        }
      }

      // Restore the original search_path
      try {
        await sequelize.query(`SET search_path TO ${currentSearchPath};`);
      } catch (error) {
        console.error('Error restoring search path:', error);
      }

      return res.status(200).json({
        id: dashboardLayout.id,
        name: dashboardLayout.name,
        layout: dashboardLayout.layout_config,
        isDefault: false,
        isActive: dashboardLayout.is_active
      });
    } catch (error) {
      // Restore the original search_path in case of error
      try {
        await sequelize.query(`SET search_path TO ${currentSearchPath};`);
      } catch (restoreError) {
        console.error('Error restoring search path:', restoreError);
      }

      console.error('Error in getUserDashboardLayout inner try block:', error);

      // Return default layout instead of throwing an error
      return res.status(200).json({
        layout: getDefaultDashboardLayout('farm'),
        isDefault: true,
        dashboardType: 'farm',
        isErrorFallback: true
      });
    }
  } catch (error) {
    console.error('Error getting user dashboard layout:', error);

    // Return default layout instead of 500 error
    return res.status(200).json({
      layout: getDefaultDashboardLayout('farm'),
      isDefault: true,
      dashboardType: 'farm',
      isErrorFallback: true
    });
  }
};

// Save dashboard layout for a user
export const saveUserDashboardLayout = async (req, res) => {
  try {
    const userId = req.params.userId;
    const { layout, name, layoutId, setAsActive = true } = req.body;

    // Validate UUID format
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(userId)) {
      return res.status(400).json({ error: 'Invalid user ID format' });
    }

    // Validate layoutId format if provided
    if (layoutId && !uuidRegex.test(layoutId)) {
      return res.status(400).json({ error: 'Invalid layout ID format' });
    }

    // Check if the requesting user is authorized to modify this user's dashboard layout
    if (req.user.id !== userId && !req.user.is_global_admin) {
      return res.status(403).json({ error: 'Unauthorized to modify this dashboard layout' });
    }

    // Validate the layout
    if (!layout) {
      return res.status(400).json({ error: 'Layout configuration is required' });
    }

    // Save the current search_path to restore it later
    const currentSearchPathResult = await sequelize.query('SHOW search_path;', { type: sequelize.QueryTypes.SELECT });
    const currentSearchPath = currentSearchPathResult[0].search_path;

    // Set search_path to include both the farm-specific schema and site schema
    // If req.farmSchema exists (set by middleware), use it along with site schema
    // Otherwise, just use site schema
    if (req.farmSchema) {
      await sequelize.query(`SET search_path TO ${req.farmSchema}, site;`);
    } else {
      await sequelize.query(`SET search_path TO site;`);
    }

    try {
      let dashboardLayout;

      // If layoutId is provided, update that specific layout
      if (layoutId) {
        dashboardLayout = await DashboardLayout.findOne({
          where: {
            id: layoutId,
            user_id: userId
          }
        });

        if (!dashboardLayout) {
          await sequelize.query(`SET search_path TO ${currentSearchPath};`);
          return res.status(404).json({ error: 'Layout not found' });
        }

        // Update the existing layout
        dashboardLayout.layout_config = layout;
        if (name) dashboardLayout.name = name;

        // If setAsActive is true, set this layout as active and deactivate others
        if (setAsActive) {
          // First, deactivate all other layouts for this user
          await DashboardLayout.update(
            { is_active: false },
            {
              where: {
                user_id: userId,
                id: { [Op.ne]: layoutId }
              }
            }
          );

          // Then set this layout as active
          dashboardLayout.is_active = true;
        }

        await dashboardLayout.save();
      } else {
        // If setAsActive is true, deactivate all other layouts for this user
        if (setAsActive) {
          await DashboardLayout.update(
            { is_active: false },
            { where: { user_id: userId } }
          );
        }

        // Create a new layout
        dashboardLayout = await DashboardLayout.create({
          user_id: userId,
          layout_config: layout,
          name: name || 'My Layout',
          is_active: setAsActive
        });
      }

      // Restore the original search_path
      await sequelize.query(`SET search_path TO ${currentSearchPath};`);

      return res.status(200).json({
        message: 'Dashboard layout saved successfully',
        id: dashboardLayout.id,
        name: dashboardLayout.name,
        layout: dashboardLayout.layout_config,
        isActive: dashboardLayout.is_active
      });
    } catch (error) {
      // Restore the original search_path in case of error
      await sequelize.query(`SET search_path TO ${currentSearchPath};`);
      throw error;
    }
  } catch (error) {
    console.error('Error saving user dashboard layout:', error);
    return res.status(500).json({ error: 'Failed to save dashboard layout' });
  }
};

// Get default dashboard layout for a farm
export const getFarmDashboardLayout = async (req, res) => {
  try {
    const farmId = req.params.farmId;

    // Validate UUID format
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(farmId)) {
      return res.status(400).json({ error: 'Invalid farm ID format' });
    }

    // Check if the user has access to this farm
    const userFarm = await UserFarm.findOne({
      where: {
        user_id: req.user.id,
        farm_id: farmId
      }
    });

    if (!userFarm && !req.user.is_global_admin) {
      return res.status(403).json({ error: 'Unauthorized to access this farm' });
    }

    // Save the current search_path to restore it later
    const currentSearchPathResult = await sequelize.query('SHOW search_path;', { type: sequelize.QueryTypes.SELECT });
    const currentSearchPath = currentSearchPathResult[0].search_path;

    // Set search_path to include both the farm-specific schema and site schema
    // If req.farmSchema exists (set by middleware), use it along with site schema
    // Otherwise, just use site schema
    if (req.farmSchema) {
      await sequelize.query(`SET search_path TO ${req.farmSchema}, site;`);
    } else {
      await sequelize.query(`SET search_path TO site;`);
    }

    try {
      // Find the farm's dashboard layout
      const farmDashboardLayout = await FarmDashboardLayout.findOne({
        where: { farm_id: farmId }
      });

      // Restore the original search_path
      await sequelize.query(`SET search_path TO ${currentSearchPath};`);

      if (!farmDashboardLayout) {
        // If no farm default layout exists, check for a global default layout
        const globalDashboardLayout = await GlobalDashboardLayout.findOne({
          where: {
            dashboard_type: 'farm',
            is_active: true
          }
        });

        if (globalDashboardLayout) {
          return res.status(200).json({
            layout: globalDashboardLayout.layout_config,
            isDefault: true,
            dashboardType: 'farm',
            isGlobalDefault: true
          });
        } else {
          // If no global default layout exists, return the hardcoded default
          return res.status(200).json({
            layout: getDefaultDashboardLayout('farm'),
            isDefault: true,
            dashboardType: 'farm'
          });
        }
      }

      return res.status(200).json({
        layout: farmDashboardLayout.layout_config,
        isDefault: false
      });
    } catch (error) {
      // Restore the original search_path in case of error
      await sequelize.query(`SET search_path TO ${currentSearchPath};`);
      throw error;
    }
  } catch (error) {
    console.error('Error getting farm dashboard layout:', error);
    return res.status(500).json({ error: 'Failed to get farm dashboard layout' });
  }
};

// Save default dashboard layout for a farm
export const saveFarmDashboardLayout = async (req, res) => {
  try {
    const farmId = req.params.farmId;
    const { layout } = req.body;

    // Validate UUID format
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(farmId)) {
      return res.status(400).json({ error: 'Invalid farm ID format' });
    }

    // Check if the user has admin access to this farm
    const userFarm = await UserFarm.findOne({
      where: {
        user_id: req.user.id,
        farm_id: farmId,
        role: 'admin'
      }
    });

    if (!userFarm && !req.user.is_global_admin) {
      return res.status(403).json({ error: 'Unauthorized to modify this farm\'s dashboard layout' });
    }

    // Validate the layout
    if (!layout) {
      return res.status(400).json({ error: 'Layout configuration is required' });
    }

    // Save the current search_path to restore it later
    const currentSearchPathResult = await sequelize.query('SHOW search_path;', { type: sequelize.QueryTypes.SELECT });
    const currentSearchPath = currentSearchPathResult[0].search_path;

    // Set search_path to include both the farm-specific schema and site schema
    // If req.farmSchema exists (set by middleware), use it along with site schema
    // Otherwise, just use site schema
    if (req.farmSchema) {
      await sequelize.query(`SET search_path TO ${req.farmSchema}, site;`);
    } else {
      await sequelize.query(`SET search_path TO site;`);
    }

    try {
      // Find or create the farm's dashboard layout
      const [farmDashboardLayout, created] = await FarmDashboardLayout.findOrCreate({
        where: { farm_id: farmId },
        defaults: {
          farm_id: farmId,
          layout_config: layout
        }
      });

      // If the dashboard layout already exists, update it
      if (!created) {
        farmDashboardLayout.layout_config = layout;
        await farmDashboardLayout.save();
      }

      // Restore the original search_path
      await sequelize.query(`SET search_path TO ${currentSearchPath};`);

      return res.status(200).json({
        message: 'Farm dashboard layout saved successfully',
        layout: farmDashboardLayout.layout_config
      });
    } catch (error) {
      // Restore the original search_path in case of error
      await sequelize.query(`SET search_path TO ${currentSearchPath};`);
      throw error;
    }
  } catch (error) {
    console.error('Error saving farm dashboard layout:', error);
    return res.status(500).json({ error: 'Failed to save farm dashboard layout' });
  }
};

// Get all dashboard layouts for a user
export const getUserDashboardLayouts = async (req, res) => {
  try {
    const userId = req.params.userId;

    // Validate UUID format
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(userId)) {
      return res.status(400).json({ error: 'Invalid user ID format' });
    }

    // Check if the requesting user is authorized to access this user's dashboard layouts
    if (req.user.id !== userId && !req.user.is_global_admin) {
      return res.status(403).json({ error: 'Unauthorized to access these dashboard layouts' });
    }

    // Save the current search_path to restore it later
    let currentSearchPath = 'site';
    try {
      const currentSearchPathResult = await sequelize.query('SHOW search_path;', { type: sequelize.QueryTypes.SELECT });
      currentSearchPath = currentSearchPathResult[0].search_path;

      // Set search_path to include both the farm-specific schema and site schema
      if (req.farmSchema) {
        await sequelize.query(`SET search_path TO ${req.farmSchema}, site;`);
      } else {
        await sequelize.query(`SET search_path TO site;`);
      }
    } catch (error) {
      console.error('Error setting search path:', error);
      // Continue with default search path
    }

    try {
      // Find all layouts for the user
      let dashboardLayouts = [];
      try {
        dashboardLayouts = await DashboardLayout.findAll({
          where: { user_id: userId },
          order: [['is_active', 'DESC'], ['updated_at', 'DESC']]
        });
      } catch (error) {
        console.error('Error finding dashboard layouts:', error);
        // Continue with empty dashboardLayouts array
      }

      // Check if the user belongs to a farm to get farm default layout info
      let userFarms = [];
      let hasFarmDefault = false;
      let farmId = null;

      try {
        userFarms = await UserFarm.findAll({
          where: { user_id: userId }
        });

        if (userFarms.length > 0) {
          farmId = userFarms[0].farm_id;
          try {
            const farmDashboardLayout = await FarmDashboardLayout.findOne({
              where: { farm_id: farmId }
            });
            hasFarmDefault = !!farmDashboardLayout;
          } catch (error) {
            console.error('Error finding farm dashboard layout:', error);
            // Continue with hasFarmDefault as false
          }
        }
      } catch (error) {
        console.error('Error finding user farms:', error);
        // Continue with default values
      }

      // Restore the original search_path
      try {
        await sequelize.query(`SET search_path TO ${currentSearchPath};`);
      } catch (error) {
        console.error('Error restoring search path:', error);
      }

      return res.status(200).json({
        layouts: dashboardLayouts.map(layout => ({
          id: layout.id,
          name: layout.name,
          isActive: layout.is_active,
          updatedAt: layout.updated_at
        })),
        hasFarmDefault,
        farmId
      });
    } catch (error) {
      // Restore the original search_path in case of error
      try {
        await sequelize.query(`SET search_path TO ${currentSearchPath};`);
      } catch (restoreError) {
        console.error('Error restoring search path:', restoreError);
      }

      console.error('Error in getUserDashboardLayouts inner try block:', error);

      // Return empty layouts array instead of throwing an error
      return res.status(200).json({
        layouts: [],
        hasFarmDefault: false,
        farmId: null,
        isErrorFallback: true
      });
    }
  } catch (error) {
    console.error('Error getting user dashboard layouts:', error);

    // Return empty layouts array instead of 500 error
    return res.status(200).json({
      layouts: [],
      hasFarmDefault: false,
      farmId: null,
      isErrorFallback: true
    });
  }
};

// Set a dashboard layout as active
export const setActiveLayout = async (req, res) => {
  try {
    const userId = req.params.userId;
    const { layoutId, setAsDefault = false } = req.body;

    // Validate UUID format
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(userId)) {
      return res.status(400).json({ error: 'Invalid user ID format' });
    }

    // Validate layoutId format
    if (!layoutId || !uuidRegex.test(layoutId)) {
      return res.status(400).json({ error: 'Invalid layout ID format' });
    }

    // Check if the requesting user is authorized to modify this user's dashboard layouts
    if (req.user.id !== userId && !req.user.is_global_admin) {
      return res.status(403).json({ error: 'Unauthorized to modify these dashboard layouts' });
    }

    // Save the current search_path to restore it later
    const currentSearchPathResult = await sequelize.query('SHOW search_path;', { type: sequelize.QueryTypes.SELECT });
    const currentSearchPath = currentSearchPathResult[0].search_path;

    // Set search_path to include both the farm-specific schema and site schema
    if (req.farmSchema) {
      await sequelize.query(`SET search_path TO ${req.farmSchema}, site;`);
    } else {
      await sequelize.query(`SET search_path TO site;`);
    }

    try {
      // Find the layout to set as active
      const dashboardLayout = await DashboardLayout.findOne({
        where: {
          id: layoutId,
          user_id: userId
        }
      });

      if (!dashboardLayout) {
        await sequelize.query(`SET search_path TO ${currentSearchPath};`);
        return res.status(404).json({ error: 'Layout not found' });
      }

      // Deactivate all layouts for this user
      await DashboardLayout.update(
        { is_active: false },
        {
          where: {
            user_id: userId
          }
        }
      );

      // Set the specified layout as active
      dashboardLayout.is_active = true;

      // If setAsDefault is true, set this layout as the user's default
      if (setAsDefault) {
        dashboardLayout.is_default = true;

        // Ensure no other layout is set as default for this user
        await DashboardLayout.update(
          { is_default: false },
          {
            where: {
              user_id: userId,
              id: { [Op.ne]: layoutId }
            }
          }
        );
      }

      await dashboardLayout.save();

      // Restore the original search_path
      await sequelize.query(`SET search_path TO ${currentSearchPath};`);

      return res.status(200).json({
        message: setAsDefault
          ? 'Layout set as default and active successfully'
          : 'Layout set as active successfully',
        id: dashboardLayout.id,
        name: dashboardLayout.name,
        isActive: dashboardLayout.is_active,
        isDefault: dashboardLayout.is_default
      });
    } catch (error) {
      // Restore the original search_path in case of error
      await sequelize.query(`SET search_path TO ${currentSearchPath};`);
      throw error;
    }
  } catch (error) {
    console.error('Error setting active layout:', error);
    return res.status(500).json({ error: 'Failed to set active layout' });
  }
};

// Delete a user dashboard layout
export const deleteUserDashboardLayout = async (req, res) => {
  try {
    const userId = req.params.userId;
    const { layoutId } = req.params;

    // Validate UUID format
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(userId)) {
      return res.status(400).json({ error: 'Invalid user ID format' });
    }

    // Validate layoutId format
    if (!layoutId || !uuidRegex.test(layoutId)) {
      return res.status(400).json({ error: 'Invalid layout ID format' });
    }

    // Check if the requesting user is authorized to modify this user's dashboard layouts
    if (req.user.id !== userId && !req.user.is_global_admin) {
      return res.status(403).json({ error: 'Unauthorized to delete this dashboard layout' });
    }

    // Save the current search_path to restore it later
    const currentSearchPathResult = await sequelize.query('SHOW search_path;', { type: sequelize.QueryTypes.SELECT });
    const currentSearchPath = currentSearchPathResult[0].search_path;

    // Set search_path to include both the farm-specific schema and site schema
    if (req.farmSchema) {
      await sequelize.query(`SET search_path TO ${req.farmSchema}, site;`);
    } else {
      await sequelize.query(`SET search_path TO site;`);
    }

    try {
      // Find the layout to delete
      const dashboardLayout = await DashboardLayout.findOne({
        where: {
          id: layoutId,
          user_id: userId
        }
      });

      if (!dashboardLayout) {
        await sequelize.query(`SET search_path TO ${currentSearchPath};`);
        return res.status(404).json({ error: 'Layout not found' });
      }

      // Check if this is the active layout
      const isActive = dashboardLayout.is_active;

      // Delete the layout
      await dashboardLayout.destroy();

      // If this was the active layout, find another layout to set as active
      if (isActive) {
        // Find the most recently updated layout
        const newActiveLayout = await DashboardLayout.findOne({
          where: { user_id: userId },
          order: [['updated_at', 'DESC']]
        });

        // If there's another layout, set it as active
        if (newActiveLayout) {
          newActiveLayout.is_active = true;
          await newActiveLayout.save();
        }
      }

      // Restore the original search_path
      await sequelize.query(`SET search_path TO ${currentSearchPath};`);

      return res.status(200).json({
        message: 'Layout deleted successfully'
      });
    } catch (error) {
      // Restore the original search_path in case of error
      await sequelize.query(`SET search_path TO ${currentSearchPath};`);
      throw error;
    }
  } catch (error) {
    console.error('Error deleting dashboard layout:', error);
    return res.status(500).json({ error: 'Failed to delete dashboard layout' });
  }
};

// Reset user dashboard layout to farm default
export const resetUserDashboardLayout = async (req, res) => {
  try {
    const userId = req.params.userId;
    const { farmId } = req.body;

    // Validate UUID format
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(userId)) {
      return res.status(400).json({ error: 'Invalid user ID format' });
    }

    // Validate farmId if provided
    if (farmId && !uuidRegex.test(farmId)) {
      return res.status(400).json({ error: 'Invalid farm ID format' });
    }

    // Check if the requesting user is authorized to modify this user's dashboard layout
    if (req.user.id !== userId && !req.user.is_global_admin) {
      return res.status(403).json({ error: 'Unauthorized to modify this dashboard layout' });
    }

    // Save the current search_path to restore it later
    const currentSearchPathResult = await sequelize.query('SHOW search_path;', { type: sequelize.QueryTypes.SELECT });
    const currentSearchPath = currentSearchPathResult[0].search_path;

    // Set search_path to include both the farm-specific schema and site schema
    // If req.farmSchema exists (set by middleware), use it along with site schema
    // Otherwise, just use site schema
    if (req.farmSchema) {
      await sequelize.query(`SET search_path TO ${req.farmSchema}, site;`);
    } else {
      await sequelize.query(`SET search_path TO site;`);
    }

    try {
      // Delete the user's custom dashboard layout
      await DashboardLayout.destroy({
        where: { user_id: userId }
      });

      // If farmId is provided, return that farm's default layout
      if (farmId) {
        const farmDashboardLayout = await FarmDashboardLayout.findOne({
          where: { farm_id: farmId }
        });

        if (farmDashboardLayout) {
          // Restore the original search_path
          await sequelize.query(`SET search_path TO ${currentSearchPath};`);
          return res.status(200).json({
            message: 'Dashboard layout reset to farm default',
            layout: farmDashboardLayout.layout_config,
            isDefault: true,
            farmId: farmId
          });
        }
      }

      // Restore the original search_path
      await sequelize.query(`SET search_path TO ${currentSearchPath};`);

      // If no farmId or no farm default layout, determine the appropriate dashboard type based on user type
      const user = await User.findByPk(userId);
      if (!user) {
        // If user not found, return a generic default layout
        return res.status(200).json({
          message: 'Dashboard layout reset to default',
          layout: getDefaultDashboardLayout('farm'),
          isDefault: true,
          dashboardType: 'farm'
        });
      }

      // Determine dashboard type based on user type
      let dashboardType = 'farm';
      if (user.user_type === 'supplier' || user.user_type === 'vet' || user.is_business_owner) {
        dashboardType = 'business';
      }

      // Check if there's a global default layout for this dashboard type
      const globalDashboardLayout = await GlobalDashboardLayout.findOne({
        where: {
          dashboard_type: dashboardType,
          is_active: true
        }
      });

      if (globalDashboardLayout) {
        return res.status(200).json({
          message: 'Dashboard layout reset to global default',
          layout: globalDashboardLayout.layout_config,
          isDefault: true,
          dashboardType,
          isGlobalDefault: true
        });
      } else {
        return res.status(200).json({
          message: 'Dashboard layout reset to default',
          layout: getDefaultDashboardLayout(dashboardType),
          isDefault: true,
          dashboardType
        });
      }
    } catch (error) {
      // Restore the original search_path in case of error
      await sequelize.query(`SET search_path TO ${currentSearchPath};`);
      throw error;
    }
  } catch (error) {
    console.error('Error resetting user dashboard layout:', error);
    return res.status(500).json({ error: 'Failed to reset dashboard layout' });
  }
};

// Get global dashboard layout
export const getGlobalDashboardLayout = async (req, res) => {
  try {
    const { type } = req.params;

    // Validate dashboard type
    if (type !== 'farm' && type !== 'business') {
      return res.status(400).json({ error: 'Invalid dashboard type. Must be "farm" or "business".' });
    }

    // Check if user is a global admin
    if (!req.user.is_global_admin) {
      return res.status(403).json({ error: 'Unauthorized. Global admin access required.' });
    }

    // Find the global dashboard layout for the specified type
    const globalDashboardLayout = await GlobalDashboardLayout.findOne({
      where: {
        dashboard_type: type,
        is_active: true
      }
    });

    if (!globalDashboardLayout) {
      // If no global layout exists, return the hardcoded default layout
      return res.status(200).json({
        layout: getDefaultDashboardLayout(type),
        isDefault: true,
        dashboardType: type
      });
    }

    return res.status(200).json({
      layout: globalDashboardLayout.layout_config,
      isDefault: false,
      dashboardType: type
    });
  } catch (error) {
    console.error('Error getting global dashboard layout:', error);
    return res.status(500).json({ error: 'Failed to get global dashboard layout' });
  }
};

// Save global dashboard layout
export const saveGlobalDashboardLayout = async (req, res) => {
  try {
    const { type } = req.params;
    const { layout } = req.body;

    // Validate dashboard type
    if (type !== 'farm' && type !== 'business') {
      return res.status(400).json({ error: 'Invalid dashboard type. Must be "farm" or "business".' });
    }

    // Check if user is a global admin
    if (!req.user.is_global_admin) {
      return res.status(403).json({ error: 'Unauthorized. Global admin access required.' });
    }

    // Validate the layout
    if (!layout) {
      return res.status(400).json({ error: 'Layout configuration is required' });
    }

    // Find or create the global dashboard layout for the specified type
    const [globalDashboardLayout, created] = await GlobalDashboardLayout.findOrCreate({
      where: {
        dashboard_type: type,
        is_active: true
      },
      defaults: {
        dashboard_type: type,
        layout_config: layout,
        is_active: true
      }
    });

    // If the dashboard layout already exists, update it
    if (!created) {
      globalDashboardLayout.layout_config = layout;
      await globalDashboardLayout.save();
    }

    return res.status(200).json({
      message: 'Global dashboard layout saved successfully',
      layout: globalDashboardLayout.layout_config,
      dashboardType: type
    });
  } catch (error) {
    console.error('Error saving global dashboard layout:', error);
    return res.status(500).json({ error: 'Failed to save global dashboard layout' });
  }
};

// Helper function to get the default dashboard layout
const getDefaultDashboardLayout = (type = 'farm') => {
  // Common layout settings
  const layoutSettings = {
    cols: 12,
    rowHeight: 50,
    gap: 20
  };

  // Farm-specific dashboard layout
  if (type === 'farm') {
    return {
      widgets: [
        {
          id: 'getting-started',
          type: 'getting-started',
          position: { x: 0, y: 0, w: 12, h: 6 },
          title: 'Getting Started'
        },
        {
          id: 'weather',
          type: 'weather',
          position: { x: 0, y: 6, w: 6, h: 4 },
          title: 'Weather'
        },
        {
          id: 'field-status',
          type: 'field-status',
          position: { x: 6, y: 6, w: 6, h: 4 },
          title: 'Field Status'
        },
        {
          id: 'crop-calendar',
          type: 'crop-calendar',
          position: { x: 0, y: 10, w: 12, h: 4 },
          title: 'Crop Calendar'
        },
        {
          id: 'recent-transactions',
          type: 'recent-transactions',
          position: { x: 0, y: 14, w: 12, h: 4 },
          title: 'Recent Transactions'
        }
      ],
      layout: layoutSettings
    };
  }

  // Business-specific dashboard layout
  if (type === 'business') {
    return {
      widgets: [
        {
          id: 'accounts',
          type: 'accounts',
          position: { x: 0, y: 0, w: 8, h: 4 },
          title: 'Accounts'
        },
        {
          id: 'quick-actions',
          type: 'quick-actions',
          position: { x: 8, y: 0, w: 4, h: 4 },
          title: 'Quick Actions'
        },
        {
          id: 'customer-overview',
          type: 'customer-overview',
          position: { x: 0, y: 4, w: 12, h: 4 },
          title: 'Customer Overview'
        },
        {
          id: 'recent-transactions',
          type: 'recent-transactions',
          position: { x: 0, y: 8, w: 12, h: 4 },
          title: 'Recent Transactions'
        }
      ],
      layout: layoutSettings
    };
  }

  // Default to farm layout if type is not recognized
  return getDefaultDashboardLayout('farm');
};
