import HarvestSchedule from '../models/HarvestSchedule.js';
import Farm from '../models/Farm.js';
import Field from '../models/Field.js';
import Crop from '../models/Crop.js';
import Weather from '../models/Weather.js';
import { sequelize } from '../config/database.js';
import { Op } from 'sequelize';
import axios from 'axios';

// Cache duration in milliseconds (24 hours)
const WEATHER_CACHE_DURATION = 24 * 60 * 60 * 1000;

// Get all harvest schedules (across all farms)
export const getAllHarvestSchedules = async (req, res) => {
  try {
    // Get query parameters for filtering
    const { page = 1, limit = 20 } = req.query;
    const offset = (page - 1) * limit;

    // Get all harvest schedules with pagination
    const { count, rows: harvestSchedules } = await HarvestSchedule.findAndCountAll({
      limit: parseInt(limit),
      offset: parseInt(offset),
      include: [
        {
          model: Farm,
          attributes: ['id', 'name']
        },
        {
          model: Field,
          attributes: ['id', 'name', 'size', 'size_unit']
        },
        {
          model: Crop,
          attributes: ['id', 'name', 'variety']
        }
      ],
      order: [['scheduled_date', 'ASC']]
    });

    return res.status(200).json({ 
      harvestSchedules,
      totalItems: count,
      totalPages: Math.ceil(count / limit),
      currentPage: parseInt(page)
    });
  } catch (error) {
    console.error('Error getting all harvest schedules:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get harvest schedules by crop
export const getHarvestSchedulesByCrop = async (req, res) => {
  try {
    const { cropId } = req.params;

    // Find crop to ensure it exists
    const crop = await Crop.findByPk(cropId);
    if (!crop) {
      return res.status(404).json({ error: 'Crop not found' });
    }

    // Get all harvest schedules for the crop
    const harvestSchedules = await HarvestSchedule.findAll({
      where: { crop_id: cropId },
      include: [
        {
          model: Farm,
          attributes: ['id', 'name']
        },
        {
          model: Field,
          attributes: ['id', 'name', 'size', 'size_unit']
        }
      ],
      order: [['scheduled_date', 'ASC']]
    });

    return res.status(200).json({ harvestSchedules });
  } catch (error) {
    console.error('Error getting harvest schedules by crop:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get harvest schedules by status
export const getHarvestSchedulesByStatus = async (req, res) => {
  try {
    const { status } = req.params;

    // Validate status
    const validStatuses = ['planned', 'in_progress', 'completed', 'cancelled'];
    if (!validStatuses.includes(status)) {
      return res.status(400).json({ error: 'Invalid status. Must be one of: planned, in_progress, completed, cancelled' });
    }

    // Get all harvest schedules with the specified status
    const harvestSchedules = await HarvestSchedule.findAll({
      where: { status },
      include: [
        {
          model: Farm,
          attributes: ['id', 'name']
        },
        {
          model: Field,
          attributes: ['id', 'name', 'size', 'size_unit']
        },
        {
          model: Crop,
          attributes: ['id', 'name', 'variety']
        }
      ],
      order: [['scheduled_date', 'ASC']]
    });

    return res.status(200).json({ harvestSchedules });
  } catch (error) {
    console.error('Error getting harvest schedules by status:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get harvest schedules by date range
export const getHarvestSchedulesByDateRange = async (req, res) => {
  try {
    const { startDate, endDate } = req.query;

    // Validate dates
    if (!startDate || !endDate) {
      return res.status(400).json({ error: 'Both startDate and endDate are required' });
    }

    // Get all harvest schedules within the date range
    const harvestSchedules = await HarvestSchedule.findAll({
      where: {
        scheduled_date: {
          [Op.between]: [startDate, endDate]
        }
      },
      include: [
        {
          model: Farm,
          attributes: ['id', 'name']
        },
        {
          model: Field,
          attributes: ['id', 'name', 'size', 'size_unit']
        },
        {
          model: Crop,
          attributes: ['id', 'name', 'variety']
        }
      ],
      order: [['scheduled_date', 'ASC']]
    });

    return res.status(200).json({ harvestSchedules });
  } catch (error) {
    console.error('Error getting harvest schedules by date range:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get all harvest schedules for a farm
export const getFarmHarvestSchedules = async (req, res) => {
  try {
    const { farmId } = req.params;

    // Find farm to ensure it exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Get all harvest schedules for the farm
    const harvestSchedules = await HarvestSchedule.findAll({
      where: { farm_id: farmId },
      include: [
        {
          model: Field,
          attributes: ['id', 'name', 'size', 'size_unit']
        },
        {
          model: Crop,
          attributes: ['id', 'name', 'variety']
        }
      ],
      order: [['scheduled_date', 'ASC']]
    });

    return res.status(200).json({ harvestSchedules });
  } catch (error) {
    console.error('Error getting farm harvest schedules:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get all harvest schedules for a field
export const getFieldHarvestSchedules = async (req, res) => {
  try {
    const { fieldId } = req.params;

    // Find field to ensure it exists
    const field = await Field.findByPk(fieldId);
    if (!field) {
      return res.status(404).json({ error: 'Field not found' });
    }

    // Get all harvest schedules for the field
    const harvestSchedules = await HarvestSchedule.findAll({
      where: { field_id: fieldId },
      include: [
        {
          model: Crop,
          attributes: ['id', 'name', 'variety']
        }
      ],
      order: [['scheduled_date', 'ASC']]
    });

    return res.status(200).json({ harvestSchedules });
  } catch (error) {
    console.error('Error getting field harvest schedules:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get a single harvest schedule by ID
export const getHarvestScheduleById = async (req, res) => {
  try {
    const { scheduleId } = req.params;

    const harvestSchedule = await HarvestSchedule.findByPk(scheduleId, {
      include: [
        {
          model: Farm,
          attributes: ['id', 'name']
        },
        {
          model: Field,
          attributes: ['id', 'name', 'size', 'size_unit']
        },
        {
          model: Crop,
          attributes: ['id', 'name', 'variety']
        }
      ]
    });

    if (!harvestSchedule) {
      return res.status(404).json({ error: 'Harvest schedule not found' });
    }

    return res.status(200).json({ harvestSchedule });
  } catch (error) {
    console.error('Error getting harvest schedule:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Create a new harvest schedule
export const createHarvestSchedule = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { 
      farmId, 
      fieldId, 
      cropId, 
      scheduledDate, 
      status, 
      weatherDependent, 
      optimalConditions,
      notes 
    } = req.body;

    // Validate required fields
    if (!farmId) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Farm ID is required' });
    }

    if (!fieldId) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Field ID is required' });
    }

    if (!cropId) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Crop ID is required' });
    }

    if (!scheduledDate) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Scheduled date is required' });
    }

    // Find farm to ensure it exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Find field to ensure it exists and belongs to the farm
    const field = await Field.findOne({ where: { id: fieldId, farm_id: farmId } });
    if (!field) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Field not found or does not belong to the farm' });
    }

    // Find crop to ensure it exists and belongs to the farm
    const crop = await Crop.findOne({ where: { id: cropId, farm_id: farmId } });
    if (!crop) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Crop not found or does not belong to the farm' });
    }

    // Create harvest schedule
    const harvestSchedule = await HarvestSchedule.create({
      farm_id: farmId,
      field_id: fieldId,
      crop_id: cropId,
      scheduled_date: scheduledDate,
      status: status || 'planned',
      weather_dependent: weatherDependent !== undefined ? weatherDependent : true,
      optimal_conditions: optimalConditions || null,
      notes
    }, { transaction });

    await transaction.commit();

    // If weather dependent, get weather-based recommendations
    if (harvestSchedule.weather_dependent) {
      try {
        const recommendations = await getWeatherBasedRecommendations(harvestSchedule);
        return res.status(201).json({ 
          message: 'Harvest schedule created successfully',
          harvestSchedule,
          recommendations
        });
      } catch (weatherError) {
        console.error('Error getting weather recommendations:', weatherError);
        return res.status(201).json({ 
          message: 'Harvest schedule created successfully, but weather recommendations failed',
          harvestSchedule,
          weatherError: weatherError.message
        });
      }
    }

    return res.status(201).json({ 
      message: 'Harvest schedule created successfully',
      harvestSchedule
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error creating harvest schedule:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Update a harvest schedule
export const updateHarvestSchedule = async (req, res) => {
  try {
    const { scheduleId } = req.params;
    const { 
      scheduledDate, 
      status, 
      weatherDependent, 
      optimalConditions,
      actualStartDate,
      actualEndDate,
      yieldAmount,
      yieldUnit,
      notes 
    } = req.body;

    // Find harvest schedule to ensure it exists
    const harvestSchedule = await HarvestSchedule.findByPk(scheduleId);
    if (!harvestSchedule) {
      return res.status(404).json({ error: 'Harvest schedule not found' });
    }

    // Update harvest schedule
    await harvestSchedule.update({
      scheduled_date: scheduledDate || harvestSchedule.scheduled_date,
      status: status || harvestSchedule.status,
      weather_dependent: weatherDependent !== undefined ? weatherDependent : harvestSchedule.weather_dependent,
      optimal_conditions: optimalConditions !== undefined ? optimalConditions : harvestSchedule.optimal_conditions,
      actual_start_date: actualStartDate !== undefined ? actualStartDate : harvestSchedule.actual_start_date,
      actual_end_date: actualEndDate !== undefined ? actualEndDate : harvestSchedule.actual_end_date,
      yield_amount: yieldAmount !== undefined ? yieldAmount : harvestSchedule.yield_amount,
      yield_unit: yieldUnit !== undefined ? yieldUnit : harvestSchedule.yield_unit,
      notes: notes !== undefined ? notes : harvestSchedule.notes
    });

    // If weather dependent and scheduled date changed, get new weather-based recommendations
    if (harvestSchedule.weather_dependent && scheduledDate && scheduledDate !== harvestSchedule.scheduled_date) {
      try {
        const recommendations = await getWeatherBasedRecommendations(harvestSchedule);
        return res.status(200).json({ 
          message: 'Harvest schedule updated successfully',
          harvestSchedule,
          recommendations
        });
      } catch (weatherError) {
        console.error('Error getting weather recommendations:', weatherError);
        return res.status(200).json({ 
          message: 'Harvest schedule updated successfully, but weather recommendations failed',
          harvestSchedule,
          weatherError: weatherError.message
        });
      }
    }

    return res.status(200).json({ 
      message: 'Harvest schedule updated successfully',
      harvestSchedule
    });
  } catch (error) {
    console.error('Error updating harvest schedule:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Delete a harvest schedule
export const deleteHarvestSchedule = async (req, res) => {
  try {
    const { scheduleId } = req.params;

    // Find harvest schedule to ensure it exists
    const harvestSchedule = await HarvestSchedule.findByPk(scheduleId);
    if (!harvestSchedule) {
      return res.status(404).json({ error: 'Harvest schedule not found' });
    }

    // Delete harvest schedule
    await harvestSchedule.destroy();

    return res.status(200).json({ 
      message: 'Harvest schedule deleted successfully' 
    });
  } catch (error) {
    console.error('Error deleting harvest schedule:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get weather-based recommendations for a harvest schedule
export const getHarvestRecommendations = async (req, res) => {
  try {
    const { scheduleId } = req.params;

    // Find harvest schedule to ensure it exists
    const harvestSchedule = await HarvestSchedule.findByPk(scheduleId, {
      include: [
        {
          model: Field,
          attributes: ['id', 'name', 'location_data']
        },
        {
          model: Crop,
          attributes: ['id', 'name', 'variety']
        }
      ]
    });

    if (!harvestSchedule) {
      return res.status(404).json({ error: 'Harvest schedule not found' });
    }

    // Get weather-based recommendations
    const recommendations = await getWeatherBasedRecommendations(harvestSchedule);

    return res.status(200).json({ recommendations });
  } catch (error) {
    console.error('Error getting harvest recommendations:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Helper function to check for cached weather forecast data
const getWeatherForecastFromCache = async (fieldId, latitude, longitude) => {
  try {
    const currentDate = new Date();
    const cacheExpiryDate = new Date(currentDate.getTime() - WEATHER_CACHE_DURATION);

    // Find cached weather data for the field
    const cachedForecasts = await Weather.findAll({
      where: {
        field_id: fieldId,
        forecast_type: 'daily',
        timestamp: {
          [Op.gte]: cacheExpiryDate
        }
      },
      order: [
        ['forecast_day', 'ASC']
      ]
    });

    // If we have at least 7 days of forecast data, use it
    if (cachedForecasts.length >= 7) {
      console.log(`Using cached weather forecast for field ${fieldId}`);

      // Map the database model to the forecast format expected by the frontend
      return cachedForecasts.map(forecast => ({
        date: new Date(forecast.timestamp).toISOString().split('T')[0],
        condition: forecast.condition,
        high: forecast.temperature,
        low: forecast.feels_like, // Using feels_like as low temp since we don't have a separate field
        precipitation_chance: forecast.precipitation_chance,
        wind_speed: forecast.wind_speed
      }));
    }

    return null;
  } catch (error) {
    console.error('Error checking weather forecast cache:', error);
    return null;
  }
};

// Helper function to save weather forecast data to cache
const saveWeatherForecastToCache = async (fieldId, farmId, latitude, longitude, forecast) => {
  try {
    const transaction = await sequelize.transaction();

    try {
      // Save each forecast day to the database
      for (let i = 0; i < forecast.length; i++) {
        const day = forecast[i];
        const forecastDate = new Date(day.date);

        await Weather.create({
          field_id: fieldId,
          farm_id: farmId,
          latitude,
          longitude,
          timestamp: forecastDate,
          temperature: day.high,
          feels_like: day.low,
          humidity: day.humidity || 0,
          wind_speed: day.wind_speed,
          precipitation_chance: day.precipitation_chance,
          condition: day.condition,
          forecast_type: 'daily',
          forecast_day: i
        }, { transaction });
      }

      await transaction.commit();
      console.log(`Cached weather forecast for field ${fieldId}`);
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  } catch (error) {
    console.error('Error saving weather forecast to cache:', error);
    // Don't throw here, just log the error and continue
  }
};

// Helper function to get weather-based recommendations for a harvest schedule
const getWeatherBasedRecommendations = async (harvestSchedule) => {
  try {
    // Get field location data
    const field = await Field.findByPk(harvestSchedule.field_id);
    if (!field || !field.location_data || !field.location_data.center) {
      throw new Error('Field location data is missing or invalid');
    }

    const { latitude, longitude } = field.location_data.center;

    // Get crop data
    const crop = await Crop.findByPk(harvestSchedule.crop_id);
    if (!crop) {
      throw new Error('Crop data is missing or invalid');
    }

    // Check for cached weather forecast
    let forecast = await getWeatherForecastFromCache(field.id, latitude, longitude);

    // If no valid cached data, fetch from API
    if (!forecast) {
      console.log(`No cached weather forecast found for field ${field.id}, fetching from API`);
      const forecastResponse = await axios.get(`http://localhost:${process.env.PORT || 3000}/api/weather/forecast/field/${field.id}?days=10`);
      forecast = forecastResponse.data.forecast;

      // Save forecast to cache
      await saveWeatherForecastToCache(field.id, harvestSchedule.farm_id, latitude, longitude, forecast);
    }

    // Define optimal weather conditions for different crop types
    const optimalConditions = harvestSchedule.optimal_conditions || getDefaultOptimalConditions(crop.name);

    // Analyze forecast to find optimal harvest days
    const recommendations = analyzeWeatherForHarvest(forecast, optimalConditions);

    return {
      scheduled_date: harvestSchedule.scheduled_date,
      optimal_days: recommendations.optimalDays,
      warning_days: recommendations.warningDays,
      reason: recommendations.reason,
      crop_name: crop.name,
      crop_variety: crop.variety,
      field_name: field.name
    };
  } catch (error) {
    console.error('Error getting weather-based recommendations:', error);
    throw new Error(`Failed to get weather-based recommendations: ${error.message}`);
  }
};

// Helper function to get default optimal weather conditions for a crop
const getDefaultOptimalConditions = (cropName) => {
  const cropType = cropName.toLowerCase();

  // Default conditions for common crops
  if (cropType.includes('corn')) {
    return {
      min_temperature: 60,
      max_temperature: 85,
      max_precipitation_chance: 20,
      max_wind_speed: 15,
      preferred_conditions: ['Clear', 'Clouds']
    };
  } else if (cropType.includes('wheat') || cropType.includes('grain')) {
    return {
      min_temperature: 55,
      max_temperature: 80,
      max_precipitation_chance: 10,
      max_wind_speed: 15,
      preferred_conditions: ['Clear', 'Clouds']
    };
  } else if (cropType.includes('soybean')) {
    return {
      min_temperature: 60,
      max_temperature: 85,
      max_precipitation_chance: 20,
      max_wind_speed: 15,
      preferred_conditions: ['Clear', 'Clouds']
    };
  } else if (cropType.includes('hay') || cropType.includes('alfalfa')) {
    return {
      min_temperature: 50,
      max_temperature: 85,
      max_precipitation_chance: 5,
      max_wind_speed: 10,
      preferred_conditions: ['Clear', 'Clouds']
    };
  } else {
    // Default for other crops
    return {
      min_temperature: 55,
      max_temperature: 85,
      max_precipitation_chance: 20,
      max_wind_speed: 15,
      preferred_conditions: ['Clear', 'Clouds']
    };
  }
};

// Helper function to analyze weather forecast for optimal harvest days
const analyzeWeatherForHarvest = (forecast, optimalConditions) => {
  const optimalDays = [];
  const warningDays = [];
  let reason = '';

  forecast.forEach(day => {
    const isTemperatureOptimal = day.high >= optimalConditions.min_temperature && 
                                day.high <= optimalConditions.max_temperature;

    const isPrecipitationOptimal = day.precipitation_chance <= optimalConditions.max_precipitation_chance;

    const isWindOptimal = parseFloat(day.wind_speed) <= optimalConditions.max_wind_speed;

    const isConditionOptimal = optimalConditions.preferred_conditions.includes(day.condition);

    if (isTemperatureOptimal && isPrecipitationOptimal && isWindOptimal && isConditionOptimal) {
      optimalDays.push(day.date);
    } else {
      warningDays.push(day.date);
    }
  });

  // Generate reason based on the analysis
  if (optimalDays.length > 0) {
    reason = `Optimal days have favorable temperature, low precipitation chance, and manageable wind speeds.`;
  } else {
    reason = `No optimal days found in the forecast period. Consider rescheduling or monitoring weather updates.`;
  }

  return {
    optimalDays,
    warningDays,
    reason
  };
};
