import SeedProduct from '../models/SeedProduct.js';
import Product from '../models/Product.js';
import { sequelize } from '../config/database.js';

// Get all seed products for a farm
export const getFarmSeedProducts = async (req, res) => {
  try {
    const { farmId } = req.params;
    
    const seedProducts = await SeedProduct.findAll({
      where: { farm_id: farmId },
      include: [
        {
          model: Product,
          attributes: ['id', 'name', 'description', 'sku', 'price', 'cost', 'unit', 'category', 'is_active']
        }
      ],
      order: [[Product, 'name', 'ASC']]
    });
    
    res.status(200).json(seedProducts);
  } catch (error) {
    console.error('Error fetching farm seed products:', error);
    res.status(500).json({ error: 'Failed to fetch seed products' });
  }
};

// Get a single seed product by ID
export const getSeedProductById = async (req, res) => {
  try {
    const { seedProductId } = req.params;
    
    const seedProduct = await SeedProduct.findByPk(seedProductId, {
      include: [
        {
          model: Product,
          attributes: ['id', 'name', 'description', 'sku', 'price', 'cost', 'unit', 'category', 'is_active']
        }
      ]
    });
    
    if (!seedProduct) {
      return res.status(404).json({ error: 'Seed product not found' });
    }
    
    res.status(200).json(seedProduct);
  } catch (error) {
    console.error('Error fetching seed product:', error);
    res.status(500).json({ error: 'Failed to fetch seed product' });
  }
};

// Create a new seed product
export const createSeedProduct = async (req, res) => {
  const transaction = await sequelize.transaction();
  
  try {
    const { 
      farm_id,
      name,
      description,
      sku,
      price,
      cost,
      unit,
      category,
      is_active,
      variety,
      brand,
      seed_type,
      germination_rate,
      treatment,
      organic,
      maturity_days,
      year,
      supplier,
      notes
    } = req.body;
    
    // Validate required fields
    if (!farm_id || !name || !price) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Farm ID, name, and price are required' });
    }
    
    // Create the base product
    const product = await Product.create({
      farm_id,
      name,
      description,
      sku,
      price,
      cost,
      unit,
      category: category || 'Seeds',
      type: 'seed',
      is_active: is_active !== undefined ? is_active : true
    }, { transaction });
    
    // Create the seed product
    const seedProduct = await SeedProduct.create({
      product_id: product.id,
      farm_id,
      variety,
      brand,
      seed_type,
      germination_rate,
      treatment,
      organic: organic !== undefined ? organic : false,
      maturity_days,
      year,
      supplier,
      notes
    }, { transaction });
    
    // Update the product with the seed product ID
    await product.update({
      source_id: seedProduct.id
    }, { transaction });
    
    await transaction.commit();
    
    // Fetch the complete seed product with product details
    const completeSeedProduct = await SeedProduct.findByPk(seedProduct.id, {
      include: [
        {
          model: Product,
          attributes: ['id', 'name', 'description', 'sku', 'price', 'cost', 'unit', 'category', 'is_active']
        }
      ]
    });
    
    res.status(201).json(completeSeedProduct);
  } catch (error) {
    await transaction.rollback();
    console.error('Error creating seed product:', error);
    res.status(500).json({ error: 'Failed to create seed product' });
  }
};

// Update a seed product
export const updateSeedProduct = async (req, res) => {
  const transaction = await sequelize.transaction();
  
  try {
    const { seedProductId } = req.params;
    const { 
      name,
      description,
      sku,
      price,
      cost,
      unit,
      category,
      is_active,
      variety,
      brand,
      seed_type,
      germination_rate,
      treatment,
      organic,
      maturity_days,
      year,
      supplier,
      notes
    } = req.body;
    
    // Find the seed product
    const seedProduct = await SeedProduct.findByPk(seedProductId, {
      include: [
        {
          model: Product,
          attributes: ['id', 'name', 'description', 'sku', 'price', 'cost', 'unit', 'category', 'is_active']
        }
      ]
    });
    
    if (!seedProduct) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Seed product not found' });
    }
    
    // Update the base product
    if (seedProduct.Product) {
      await seedProduct.Product.update({
        name: name || seedProduct.Product.name,
        description: description !== undefined ? description : seedProduct.Product.description,
        sku: sku !== undefined ? sku : seedProduct.Product.sku,
        price: price !== undefined ? price : seedProduct.Product.price,
        cost: cost !== undefined ? cost : seedProduct.Product.cost,
        unit: unit !== undefined ? unit : seedProduct.Product.unit,
        category: category !== undefined ? category : seedProduct.Product.category,
        is_active: is_active !== undefined ? is_active : seedProduct.Product.is_active
      }, { transaction });
    }
    
    // Update the seed product
    await seedProduct.update({
      variety: variety !== undefined ? variety : seedProduct.variety,
      brand: brand !== undefined ? brand : seedProduct.brand,
      seed_type: seed_type !== undefined ? seed_type : seedProduct.seed_type,
      germination_rate: germination_rate !== undefined ? germination_rate : seedProduct.germination_rate,
      treatment: treatment !== undefined ? treatment : seedProduct.treatment,
      organic: organic !== undefined ? organic : seedProduct.organic,
      maturity_days: maturity_days !== undefined ? maturity_days : seedProduct.maturity_days,
      year: year !== undefined ? year : seedProduct.year,
      supplier: supplier !== undefined ? supplier : seedProduct.supplier,
      notes: notes !== undefined ? notes : seedProduct.notes
    }, { transaction });
    
    await transaction.commit();
    
    // Fetch the updated seed product with product details
    const updatedSeedProduct = await SeedProduct.findByPk(seedProductId, {
      include: [
        {
          model: Product,
          attributes: ['id', 'name', 'description', 'sku', 'price', 'cost', 'unit', 'category', 'is_active']
        }
      ]
    });
    
    res.status(200).json(updatedSeedProduct);
  } catch (error) {
    await transaction.rollback();
    console.error('Error updating seed product:', error);
    res.status(500).json({ error: 'Failed to update seed product' });
  }
};

// Delete a seed product
export const deleteSeedProduct = async (req, res) => {
  const transaction = await sequelize.transaction();
  
  try {
    const { seedProductId } = req.params;
    
    // Find the seed product
    const seedProduct = await SeedProduct.findByPk(seedProductId, {
      include: [
        {
          model: Product
        }
      ]
    });
    
    if (!seedProduct) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Seed product not found' });
    }
    
    // Delete the base product
    if (seedProduct.Product) {
      await seedProduct.Product.destroy({ transaction });
    }
    
    // Delete the seed product
    await seedProduct.destroy({ transaction });
    
    await transaction.commit();
    
    res.status(200).json({ message: 'Seed product deleted successfully' });
  } catch (error) {
    await transaction.rollback();
    console.error('Error deleting seed product:', error);
    res.status(500).json({ error: 'Failed to delete seed product' });
  }
};