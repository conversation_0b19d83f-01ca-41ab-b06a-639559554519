import Invoice from '../models/Invoice.js';
import Bill from '../models/Bill.js';
import RecurringInvoice from '../models/RecurringInvoice.js';
import RecurringBill from '../models/RecurringBill.js';
import { sequelize } from '../config/database.js';
import { Op } from 'sequelize';

/**
 * Get financial planning data for a farm
 * This includes:
 * - Upcoming invoices (due dates, status, amounts)
 * - Upcoming bills (due dates, status, amounts)
 * - Projected recurring invoices
 * - Projected recurring bills
 */
export const getFinancialPlanningData = async (req, res) => {
  try {
    const { farmId } = req.params;
    const { startDate, endDate } = req.query;

    // Validate farm access
    const userFarmId = req.user.current_farm_id;
    if (!req.user.is_global_admin && userFarmId !== farmId) {
      return res.status(403).json({ message: 'You do not have permission to access this farm\'s financial data' });
    }

    // Parse dates or use defaults (current month)
    const today = new Date();
    const parsedStartDate = startDate ? new Date(startDate) : new Date(today.getFullYear(), today.getMonth(), 1);
    const parsedEndDate = endDate ? new Date(endDate) : new Date(today.getFullYear(), today.getMonth() + 3, 0); // Default to 3 months ahead

    // Get invoices
    const invoices = await Invoice.findAll({
      where: {
        farm_id: farmId,
        due_date: {
          [Op.between]: [parsedStartDate, parsedEndDate]
        }
      },
      attributes: [
        'id', 'invoice_number', 'issue_date', 'due_date', 'status', 
        'total_amount', 'payment_amount', 'customer_id', 'recipient_farm_id'
      ],
      order: [['due_date', 'ASC']]
    });

    // Get bills
    const bills = await Bill.findAll({
      where: {
        farm_id: farmId,
        due_date: {
          [Op.between]: [parsedStartDate, parsedEndDate]
        }
      },
      attributes: [
        'id', 'title', 'due_date', 'status', 'amount', 'vendor_id'
      ],
      order: [['due_date', 'ASC']]
    });

    // Get recurring invoices and project future occurrences
    const recurringInvoices = await RecurringInvoice.findAll({
      where: {
        '$invoice.farm_id$': farmId,
        [Op.or]: [
          { end_date: null },
          { end_date: { [Op.gte]: parsedStartDate } }
        ]
      },
      include: [{
        model: Invoice,
        as: 'invoice',
        attributes: ['id', 'invoice_number', 'total_amount', 'customer_id', 'recipient_farm_id']
      }],
      attributes: [
        'id', 'frequency', 'start_date', 'end_date', 'next_due_date',
        'day_of_month', 'day_of_week', 'week_of_month', 'month_of_year'
      ]
    });

    // Get recurring bills and project future occurrences
    const recurringBills = await RecurringBill.findAll({
      where: {
        '$bill.farm_id$': farmId,
        [Op.or]: [
          { end_date: null },
          { end_date: { [Op.gte]: parsedStartDate } }
        ]
      },
      include: [{
        model: Bill,
        as: 'bill',
        attributes: ['id', 'title', 'amount', 'vendor_id']
      }],
      attributes: [
        'id', 'frequency', 'start_date', 'end_date', 'next_due_date',
        'day_of_month', 'day_of_week', 'week_of_month', 'month_of_year'
      ]
    });

    // Project future recurring invoices
    const projectedInvoices = [];
    for (const recurring of recurringInvoices) {
      let currentDate = new Date(Math.max(parsedStartDate, new Date(recurring.next_due_date)));
      const endDate = recurring.end_date ? new Date(recurring.end_date) : parsedEndDate;
      
      while (currentDate <= parsedEndDate && currentDate <= endDate) {
        projectedInvoices.push({
          id: `projected-invoice-${recurring.id}-${currentDate.toISOString()}`,
          recurring_id: recurring.id,
          invoice_number: `${recurring.invoice.invoice_number} (Projected)`,
          due_date: new Date(currentDate),
          total_amount: recurring.invoice.total_amount,
          status: 'projected',
          customer_id: recurring.invoice.customer_id,
          recipient_farm_id: recurring.invoice.recipient_farm_id,
          is_projected: true
        });

        // Calculate next occurrence based on frequency
        currentDate = calculateNextOccurrence(
          currentDate,
          recurring.frequency,
          recurring.day_of_month,
          recurring.day_of_week,
          recurring.week_of_month,
          recurring.month_of_year
        );
      }
    }

    // Project future recurring bills
    const projectedBills = [];
    for (const recurring of recurringBills) {
      let currentDate = new Date(Math.max(parsedStartDate, new Date(recurring.next_due_date)));
      const endDate = recurring.end_date ? new Date(recurring.end_date) : parsedEndDate;
      
      while (currentDate <= parsedEndDate && currentDate <= endDate) {
        projectedBills.push({
          id: `projected-bill-${recurring.id}-${currentDate.toISOString()}`,
          recurring_id: recurring.id,
          title: `${recurring.bill.title} (Projected)`,
          due_date: new Date(currentDate),
          amount: recurring.bill.amount,
          status: 'projected',
          vendor_id: recurring.bill.vendor_id,
          is_projected: true
        });

        // Calculate next occurrence based on frequency
        currentDate = calculateNextOccurrence(
          currentDate,
          recurring.frequency,
          recurring.day_of_month,
          recurring.day_of_week,
          recurring.week_of_month,
          recurring.month_of_year
        );
      }
    }

    // Calculate financial summary
    const unpaidInvoices = invoices.filter(invoice => 
      ['draft', 'sent', 'overdue'].includes(invoice.status.toLowerCase())
    );
    
    const unpaidBills = bills.filter(bill => 
      ['unpaid', 'overdue'].includes(bill.status.toLowerCase())
    );

    const totalIncome = invoices.reduce((sum, invoice) => sum + parseFloat(invoice.total_amount), 0);
    const totalExpenses = bills.reduce((sum, bill) => sum + parseFloat(bill.amount), 0);
    const totalProjectedIncome = projectedInvoices.reduce((sum, invoice) => sum + parseFloat(invoice.total_amount), 0);
    const totalProjectedExpenses = projectedBills.reduce((sum, bill) => sum + parseFloat(bill.amount), 0);
    
    const totalUnpaidInvoices = unpaidInvoices.reduce((sum, invoice) => sum + parseFloat(invoice.total_amount), 0);
    const totalUnpaidBills = unpaidBills.reduce((sum, bill) => sum + parseFloat(bill.amount), 0);

    const financialSummary = {
      totalIncome,
      totalExpenses,
      totalProjectedIncome,
      totalProjectedExpenses,
      netCashFlow: totalIncome - totalExpenses,
      projectedNetCashFlow: (totalIncome + totalProjectedIncome) - (totalExpenses + totalProjectedExpenses),
      unpaidInvoicesCount: unpaidInvoices.length,
      unpaidInvoicesAmount: totalUnpaidInvoices,
      unpaidBillsCount: unpaidBills.length,
      unpaidBillsAmount: totalUnpaidBills
    };

    return res.status(200).json({
      invoices,
      bills,
      projectedInvoices,
      projectedBills,
      financialSummary,
      dateRange: {
        startDate: parsedStartDate,
        endDate: parsedEndDate
      }
    });
  } catch (error) {
    console.error('Error fetching financial planning data:', error);
    return res.status(500).json({ message: 'Error fetching financial planning data', error: error.message });
  }
};

/**
 * Calculate the next occurrence date based on frequency and other parameters
 */
function calculateNextOccurrence(currentDate, frequency, dayOfMonth, dayOfWeek, weekOfMonth, monthOfYear) {
  const date = new Date(currentDate);
  
  switch (frequency.toLowerCase()) {
    case 'daily':
      date.setDate(date.getDate() + 1);
      break;
    case 'weekly':
      date.setDate(date.getDate() + 7);
      break;
    case 'biweekly':
      date.setDate(date.getDate() + 14);
      break;
    case 'monthly':
      date.setMonth(date.getMonth() + 1);
      if (dayOfMonth) {
        // Set to specific day of month, handling month length
        const lastDayOfMonth = new Date(date.getFullYear(), date.getMonth() + 1, 0).getDate();
        date.setDate(Math.min(dayOfMonth, lastDayOfMonth));
      }
      break;
    case 'quarterly':
      date.setMonth(date.getMonth() + 3);
      if (dayOfMonth) {
        const lastDayOfMonth = new Date(date.getFullYear(), date.getMonth() + 1, 0).getDate();
        date.setDate(Math.min(dayOfMonth, lastDayOfMonth));
      }
      break;
    case 'semiannually':
      date.setMonth(date.getMonth() + 6);
      if (dayOfMonth) {
        const lastDayOfMonth = new Date(date.getFullYear(), date.getMonth() + 1, 0).getDate();
        date.setDate(Math.min(dayOfMonth, lastDayOfMonth));
      }
      break;
    case 'annually':
      date.setFullYear(date.getFullYear() + 1);
      if (monthOfYear && dayOfMonth) {
        date.setMonth(monthOfYear - 1);
        const lastDayOfMonth = new Date(date.getFullYear(), date.getMonth() + 1, 0).getDate();
        date.setDate(Math.min(dayOfMonth, lastDayOfMonth));
      }
      break;
    default:
      date.setMonth(date.getMonth() + 1);
  }
  
  return date;
}