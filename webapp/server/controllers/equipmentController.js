import Equipment from '../models/Equipment.js';
import Farm from '../models/Farm.js';
import { sequelize } from '../config/database.js';
import { Op } from 'sequelize';

// Get all equipment (across all farms)
export const getAllEquipment = async (req, res) => {
  try {
    // Get query parameters for filtering
    const { page = 1, limit = 20 } = req.query;
    const offset = (page - 1) * limit;

    // Get all equipment with pagination
    const { count, rows: equipment } = await Equipment.findAndCountAll({
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['name', 'ASC']],
      include: [
        {
          model: Farm,
          as: 'equipmentFarm',
          attributes: ['id', 'name']
        }
      ]
    });

    return res.status(200).json({ 
      equipment,
      totalItems: count,
      totalPages: Math.ceil(count / limit),
      currentPage: parseInt(page)
    });
  } catch (error) {
    console.error('Error getting all equipment:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get equipment by type
export const getEquipmentByType = async (req, res) => {
  try {
    const { type } = req.params;

    // Get all equipment of the specified type
    const equipment = await Equipment.findAll({
      where: { type },
      order: [['name', 'ASC']],
      include: [
        {
          model: Farm,
          as: 'equipmentFarm',
          attributes: ['id', 'name']
        }
      ]
    });

    return res.status(200).json({ equipment });
  } catch (error) {
    console.error('Error getting equipment by type:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get equipment by status
export const getEquipmentByStatus = async (req, res) => {
  try {
    const { status } = req.params;

    // Get all equipment with the specified status
    const equipment = await Equipment.findAll({
      where: { status },
      order: [['name', 'ASC']],
      include: [
        {
          model: Farm,
          as: 'equipmentFarm',
          attributes: ['id', 'name']
        }
      ]
    });

    return res.status(200).json({ equipment });
  } catch (error) {
    console.error('Error getting equipment by status:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get all equipment for a farm
export const getFarmEquipment = async (req, res) => {
  try {
    const { farmId } = req.params;

    // Find farm to ensure it exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Get all equipment for the farm
    const equipment = await Equipment.findAll({
      where: { farm_id: farmId },
      order: [['name', 'ASC']]
    });

    return res.status(200).json({ equipment });
  } catch (error) {
    console.error('Error getting farm equipment:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get a single equipment item by ID
export const getEquipmentById = async (req, res) => {
  try {
    const { equipmentId } = req.params;

    const equipment = await Equipment.findByPk(equipmentId);

    if (!equipment) {
      return res.status(404).json({ error: 'Equipment not found' });
    }

    return res.status(200).json({ equipment });
  } catch (error) {
    console.error('Error getting equipment:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Create a new equipment item
export const createEquipment = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { 
      farmId, 
      name, 
      type, 
      manufacturer, 
      model, 
      year, 
      purchaseDate, 
      purchaseCost, 
      currentValue, 
      status, 
      notes 
    } = req.body;

    // Validate required fields
    if (!farmId) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Farm ID is required' });
    }

    if (!name) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Equipment name is required' });
    }

    // Find farm to ensure it exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Create equipment
    const equipment = await Equipment.create({
      farm_id: farmId,
      name,
      type,
      manufacturer,
      model,
      year,
      purchase_date: purchaseDate,
      purchase_cost: purchaseCost,
      current_value: currentValue,
      status,
      notes
    }, { transaction });

    await transaction.commit();

    return res.status(201).json({ 
      message: 'Equipment created successfully',
      equipment 
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error creating equipment:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Update an equipment item
export const updateEquipment = async (req, res) => {
  try {
    const { equipmentId } = req.params;
    const { 
      name, 
      type, 
      manufacturer, 
      model, 
      year, 
      purchaseDate, 
      purchaseCost, 
      currentValue, 
      status, 
      notes 
    } = req.body;

    // Find equipment to ensure it exists
    const equipment = await Equipment.findByPk(equipmentId);
    if (!equipment) {
      return res.status(404).json({ error: 'Equipment not found' });
    }

    // Update equipment
    await equipment.update({
      name: name || equipment.name,
      type: type !== undefined ? type : equipment.type,
      manufacturer: manufacturer !== undefined ? manufacturer : equipment.manufacturer,
      model: model !== undefined ? model : equipment.model,
      year: year !== undefined ? year : equipment.year,
      purchase_date: purchaseDate !== undefined ? purchaseDate : equipment.purchase_date,
      purchase_cost: purchaseCost !== undefined ? purchaseCost : equipment.purchase_cost,
      current_value: currentValue !== undefined ? currentValue : equipment.current_value,
      status: status !== undefined ? status : equipment.status,
      notes: notes !== undefined ? notes : equipment.notes
    });

    return res.status(200).json({ 
      message: 'Equipment updated successfully',
      equipment 
    });
  } catch (error) {
    console.error('Error updating equipment:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Delete an equipment item
export const deleteEquipment = async (req, res) => {
  try {
    const { equipmentId } = req.params;

    // Find equipment to ensure it exists
    const equipment = await Equipment.findByPk(equipmentId);
    if (!equipment) {
      return res.status(404).json({ error: 'Equipment not found' });
    }

    // Delete equipment
    await equipment.destroy();

    return res.status(200).json({ 
      message: 'Equipment deleted successfully' 
    });
  } catch (error) {
    console.error('Error deleting equipment:', error);
    return res.status(500).json({ error: error.message });
  }
};
