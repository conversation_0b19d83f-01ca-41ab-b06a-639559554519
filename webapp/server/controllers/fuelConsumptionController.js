import { Op } from 'sequelize';
import Farm from '../models/Farm.js';
import Driver from '../models/Driver.js';
import Equipment from '../models/Equipment.js';
import FuelRecord from '../models/FuelRecord.js';

/**
 * Get fuel consumption records for a farm
 */
export const getFuelRecords = async (req, res) => {
  try {
    const { farmId } = req.params;
    const { 
      startDate, 
      endDate, 
      driverId, 
      equipmentId, 
      sort = 'date', 
      order = 'desc',
      limit = 100,
      offset = 0
    } = req.query;

    // Validate farm exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Build query conditions
    const whereConditions = {
      farm_id: farmId
    };

    if (startDate && endDate) {
      whereConditions.date = {
        [Op.between]: [new Date(startDate), new Date(endDate)]
      };
    } else if (startDate) {
      whereConditions.date = {
        [Op.gte]: new Date(startDate)
      };
    } else if (endDate) {
      whereConditions.date = {
        [Op.lte]: new Date(endDate)
      };
    }

    if (driverId) {
      whereConditions.driver_id = driverId;
    }

    if (equipmentId) {
      whereConditions.equipment_id = equipmentId;
    }

    // Determine sort field and order
    const sortField = sort === 'date' ? 'date' : 
                      sort === 'amount' ? 'amount' : 
                      sort === 'cost' ? 'cost' : 'date';
    
    const sortOrder = order.toLowerCase() === 'asc' ? 'ASC' : 'DESC';

    // Get fuel records
    const fuelRecords = await FuelRecord.findAndCountAll({
      where: whereConditions,
      include: [
        { model: Driver, as: 'driver', attributes: ['id', 'first_name', 'last_name'] },
        { model: Equipment, as: 'equipment', attributes: ['id', 'make', 'model', 'type'] }
      ],
      order: [[sortField, sortOrder]],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

    // Format the records
    const formattedRecords = fuelRecords.rows.map(record => ({
      id: record.id,
      date: record.date,
      amount: record.amount,
      cost: record.cost,
      odometer: record.odometer,
      fuelType: record.fuel_type,
      location: record.location,
      notes: record.notes,
      driver: record.driver ? {
        id: record.driver.id,
        name: `${record.driver.first_name} ${record.driver.last_name}`
      } : null,
      equipment: record.equipment ? {
        id: record.equipment.id,
        name: `${record.equipment.make} ${record.equipment.model}`,
        type: record.equipment.type
      } : null,
      createdAt: record.created_at,
      updatedAt: record.updated_at
    }));

    return res.status(200).json({
      fuelRecords: formattedRecords,
      total: fuelRecords.count,
      limit: parseInt(limit),
      offset: parseInt(offset)
    });
  } catch (error) {
    console.error('Error getting fuel records:', error);
    return res.status(500).json({ error: 'Failed to get fuel records' });
  }
};

/**
 * Get a specific fuel record
 */
export const getFuelRecord = async (req, res) => {
  try {
    const { recordId } = req.params;

    // Find the fuel record
    const fuelRecord = await FuelRecord.findByPk(recordId, {
      include: [
        { model: Driver, as: 'driver', attributes: ['id', 'first_name', 'last_name'] },
        { model: Equipment, as: 'equipment', attributes: ['id', 'make', 'model', 'type'] }
      ]
    });

    if (!fuelRecord) {
      return res.status(404).json({ error: 'Fuel record not found' });
    }

    // Format the record
    const formattedRecord = {
      id: fuelRecord.id,
      date: fuelRecord.date,
      amount: fuelRecord.amount,
      cost: fuelRecord.cost,
      odometer: fuelRecord.odometer,
      fuelType: fuelRecord.fuel_type,
      location: fuelRecord.location,
      notes: fuelRecord.notes,
      driver: fuelRecord.driver ? {
        id: fuelRecord.driver.id,
        name: `${fuelRecord.driver.first_name} ${fuelRecord.driver.last_name}`
      } : null,
      equipment: fuelRecord.equipment ? {
        id: fuelRecord.equipment.id,
        name: `${fuelRecord.equipment.make} ${fuelRecord.equipment.model}`,
        type: fuelRecord.equipment.type
      } : null,
      createdAt: fuelRecord.created_at,
      updatedAt: fuelRecord.updated_at
    };

    return res.status(200).json({
      fuelRecord: formattedRecord
    });
  } catch (error) {
    console.error('Error getting fuel record:', error);
    return res.status(500).json({ error: 'Failed to get fuel record' });
  }
};

/**
 * Create a new fuel record
 */
export const createFuelRecord = async (req, res) => {
  try {
    const { farmId } = req.params;
    const { 
      date, 
      amount, 
      cost, 
      odometer, 
      fuelType, 
      location, 
      notes, 
      driverId, 
      equipmentId 
    } = req.body;

    // Validate required fields
    if (!date || !amount || !cost) {
      return res.status(400).json({ error: 'Date, amount, and cost are required' });
    }

    // Validate farm exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Validate driver if provided
    if (driverId) {
      const driver = await Driver.findByPk(driverId);
      if (!driver) {
        return res.status(404).json({ error: 'Driver not found' });
      }
    }

    // Validate equipment if provided
    if (equipmentId) {
      const equipment = await Equipment.findByPk(equipmentId);
      if (!equipment) {
        return res.status(404).json({ error: 'Equipment not found' });
      }
    }

    // Create the fuel record
    const fuelRecord = await FuelRecord.create({
      farm_id: farmId,
      date,
      amount,
      cost,
      odometer: odometer || null,
      fuel_type: fuelType || 'diesel',
      location: location || null,
      notes: notes || null,
      driver_id: driverId || null,
      equipment_id: equipmentId || null
    });

    // If equipment is provided, update its odometer reading
    if (equipmentId && odometer) {
      const equipment = await Equipment.findByPk(equipmentId);
      if (equipment) {
        equipment.odometer = odometer;
        await equipment.save();
      }
    }

    return res.status(201).json({
      fuelRecord: {
        id: fuelRecord.id,
        date: fuelRecord.date,
        amount: fuelRecord.amount,
        cost: fuelRecord.cost,
        odometer: fuelRecord.odometer,
        fuelType: fuelRecord.fuel_type,
        location: fuelRecord.location,
        notes: fuelRecord.notes,
        driverId: fuelRecord.driver_id,
        equipmentId: fuelRecord.equipment_id,
        createdAt: fuelRecord.created_at,
        updatedAt: fuelRecord.updated_at
      }
    });
  } catch (error) {
    console.error('Error creating fuel record:', error);
    return res.status(500).json({ error: 'Failed to create fuel record' });
  }
};

/**
 * Update a fuel record
 */
export const updateFuelRecord = async (req, res) => {
  try {
    const { recordId } = req.params;
    const { 
      date, 
      amount, 
      cost, 
      odometer, 
      fuelType, 
      location, 
      notes, 
      driverId, 
      equipmentId 
    } = req.body;

    // Find the fuel record
    const fuelRecord = await FuelRecord.findByPk(recordId);
    if (!fuelRecord) {
      return res.status(404).json({ error: 'Fuel record not found' });
    }

    // Validate driver if provided
    if (driverId) {
      const driver = await Driver.findByPk(driverId);
      if (!driver) {
        return res.status(404).json({ error: 'Driver not found' });
      }
    }

    // Validate equipment if provided
    if (equipmentId) {
      const equipment = await Equipment.findByPk(equipmentId);
      if (!equipment) {
        return res.status(404).json({ error: 'Equipment not found' });
      }
    }

    // Update the fuel record
    if (date) fuelRecord.date = date;
    if (amount) fuelRecord.amount = amount;
    if (cost) fuelRecord.cost = cost;
    if (odometer !== undefined) fuelRecord.odometer = odometer;
    if (fuelType) fuelRecord.fuel_type = fuelType;
    if (location !== undefined) fuelRecord.location = location;
    if (notes !== undefined) fuelRecord.notes = notes;
    if (driverId !== undefined) fuelRecord.driver_id = driverId;
    if (equipmentId !== undefined) fuelRecord.equipment_id = equipmentId;

    await fuelRecord.save();

    // If equipment is provided and odometer is updated, update equipment's odometer reading
    if (equipmentId && odometer) {
      const equipment = await Equipment.findByPk(equipmentId);
      if (equipment) {
        equipment.odometer = odometer;
        await equipment.save();
      }
    }

    return res.status(200).json({
      fuelRecord: {
        id: fuelRecord.id,
        date: fuelRecord.date,
        amount: fuelRecord.amount,
        cost: fuelRecord.cost,
        odometer: fuelRecord.odometer,
        fuelType: fuelRecord.fuel_type,
        location: fuelRecord.location,
        notes: fuelRecord.notes,
        driverId: fuelRecord.driver_id,
        equipmentId: fuelRecord.equipment_id,
        createdAt: fuelRecord.created_at,
        updatedAt: fuelRecord.updated_at
      }
    });
  } catch (error) {
    console.error('Error updating fuel record:', error);
    return res.status(500).json({ error: 'Failed to update fuel record' });
  }
};

/**
 * Delete a fuel record
 */
export const deleteFuelRecord = async (req, res) => {
  try {
    const { recordId } = req.params;

    // Find the fuel record
    const fuelRecord = await FuelRecord.findByPk(recordId);
    if (!fuelRecord) {
      return res.status(404).json({ error: 'Fuel record not found' });
    }

    // Delete the fuel record
    await fuelRecord.destroy();

    return res.status(200).json({
      message: 'Fuel record deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting fuel record:', error);
    return res.status(500).json({ error: 'Failed to delete fuel record' });
  }
};

/**
 * Get fuel consumption analytics for a farm
 */
export const getFuelAnalytics = async (req, res) => {
  try {
    const { farmId } = req.params;
    const { 
      startDate = new Date(new Date().getFullYear(), 0, 1).toISOString(), // Start of current year
      endDate = new Date().toISOString(),
      groupBy = 'month' // 'day', 'week', 'month', 'year'
    } = req.query;

    // Validate farm exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Get all fuel records for the specified period
    const fuelRecords = await FuelRecord.findAll({
      where: {
        farm_id: farmId,
        date: {
          [Op.between]: [new Date(startDate), new Date(endDate)]
        }
      },
      include: [
        { model: Equipment, as: 'equipment', attributes: ['id', 'make', 'model', 'type'] }
      ],
      order: [['date', 'ASC']]
    });

    // Group records by the specified time period
    const groupedRecords = {};
    const equipmentConsumption = {};
    const fuelTypeConsumption = {};
    
    let totalAmount = 0;
    let totalCost = 0;
    
    fuelRecords.forEach(record => {
      const date = new Date(record.date);
      let groupKey;
      
      // Determine the group key based on groupBy parameter
      if (groupBy === 'day') {
        groupKey = date.toISOString().split('T')[0]; // YYYY-MM-DD
      } else if (groupBy === 'week') {
        // Get the week number
        const firstDayOfYear = new Date(date.getFullYear(), 0, 1);
        const pastDaysOfYear = (date - firstDayOfYear) / 86400000;
        const weekNumber = Math.ceil((pastDaysOfYear + firstDayOfYear.getDay() + 1) / 7);
        groupKey = `${date.getFullYear()}-W${weekNumber}`;
      } else if (groupBy === 'month') {
        groupKey = `${date.getFullYear()}-${date.getMonth() + 1}`; // YYYY-MM
      } else {
        groupKey = date.getFullYear().toString(); // YYYY
      }
      
      // Initialize group if it doesn't exist
      if (!groupedRecords[groupKey]) {
        groupedRecords[groupKey] = {
          period: groupKey,
          amount: 0,
          cost: 0,
          count: 0
        };
      }
      
      // Add record data to the group
      groupedRecords[groupKey].amount += record.amount;
      groupedRecords[groupKey].cost += record.cost;
      groupedRecords[groupKey].count += 1;
      
      // Track equipment consumption
      if (record.equipment) {
        const equipmentKey = record.equipment.id;
        if (!equipmentConsumption[equipmentKey]) {
          equipmentConsumption[equipmentKey] = {
            id: record.equipment.id,
            name: `${record.equipment.make} ${record.equipment.model}`,
            type: record.equipment.type,
            amount: 0,
            cost: 0,
            count: 0
          };
        }
        
        equipmentConsumption[equipmentKey].amount += record.amount;
        equipmentConsumption[equipmentKey].cost += record.cost;
        equipmentConsumption[equipmentKey].count += 1;
      }
      
      // Track fuel type consumption
      const fuelTypeKey = record.fuel_type || 'unknown';
      if (!fuelTypeConsumption[fuelTypeKey]) {
        fuelTypeConsumption[fuelTypeKey] = {
          type: fuelTypeKey,
          amount: 0,
          cost: 0,
          count: 0
        };
      }
      
      fuelTypeConsumption[fuelTypeKey].amount += record.amount;
      fuelTypeConsumption[fuelTypeKey].cost += record.cost;
      fuelTypeConsumption[fuelTypeKey].count += 1;
      
      // Update totals
      totalAmount += record.amount;
      totalCost += record.cost;
    });
    
    // Convert grouped records to array and sort by period
    const consumptionByPeriod = Object.values(groupedRecords).sort((a, b) => a.period.localeCompare(b.period));
    
    // Convert equipment consumption to array and sort by amount
    const consumptionByEquipment = Object.values(equipmentConsumption).sort((a, b) => b.amount - a.amount);
    
    // Convert fuel type consumption to array and sort by amount
    const consumptionByFuelType = Object.values(fuelTypeConsumption).sort((a, b) => b.amount - a.amount);
    
    // Calculate average cost per liter/gallon
    const averageCostPerUnit = totalAmount > 0 ? totalCost / totalAmount : 0;

    return res.status(200).json({
      analytics: {
        totalAmount,
        totalCost,
        averageCostPerUnit,
        recordCount: fuelRecords.length,
        consumptionByPeriod,
        consumptionByEquipment,
        consumptionByFuelType
      }
    });
  } catch (error) {
    console.error('Error getting fuel analytics:', error);
    return res.status(500).json({ error: 'Failed to get fuel analytics' });
  }
};