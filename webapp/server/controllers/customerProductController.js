import Product from '../models/Product.js';
import Farm from '../models/Farm.js';
import { sequelize } from '../config/database.js';

// Get all public products for a farm
export const getPublicProducts = async (req, res) => {
  try {
    // The customer is attached to the request by the authenticateCustomer middleware
    const customer = req.customer;
    const farmId = req.farmId;

    // Find farm to ensure it exists and has customer portal enabled
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Check if customer portal is enabled for this farm
    if (!farm.customer_portal_enabled) {
      return res.status(403).json({ error: 'Customer portal is not enabled for this farm' });
    }

    // Get all public products for the farm
    const products = await Product.findAll({
      where: { 
        farm_id: farmId,
        is_public: true,
        is_active: true
      },
      attributes: ['id', 'name', 'description', 'price', 'unit', 'category', 'type'],
      order: [['name', 'ASC']]
    });

    return res.status(200).json({ products });
  } catch (error) {
    console.error('Error getting public products:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get a single public product by ID
export const getPublicProductById = async (req, res) => {
  try {
    const { productId } = req.params;
    const customer = req.customer;
    const farmId = req.farmId;

    // Find farm to ensure it exists and has customer portal enabled
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Check if customer portal is enabled for this farm
    if (!farm.customer_portal_enabled) {
      return res.status(403).json({ error: 'Customer portal is not enabled for this farm' });
    }

    // Get the product, ensuring it's public and belongs to this farm
    const product = await Product.findOne({
      where: {
        id: productId,
        farm_id: farmId,
        is_public: true,
        is_active: true
      },
      attributes: ['id', 'name', 'description', 'price', 'unit', 'category', 'type']
    });

    if (!product) {
      return res.status(404).json({ error: 'Product not found or not available to the public' });
    }

    return res.status(200).json({ product });
  } catch (error) {
    console.error('Error getting public product:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Request a product (inquiry)
export const requestProduct = async (req, res) => {
  try {
    const { productId } = req.params;
    const { quantity, notes } = req.body;
    const customer = req.customer;
    const farmId = req.farmId;

    // Find farm to ensure it exists and has customer portal enabled
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Check if customer portal is enabled for this farm
    if (!farm.customer_portal_enabled) {
      return res.status(403).json({ error: 'Customer portal is not enabled for this farm' });
    }

    // Get the product, ensuring it's public and belongs to this farm
    const product = await Product.findOne({
      where: {
        id: productId,
        farm_id: farmId,
        is_public: true,
        is_active: true
      }
    });

    if (!product) {
      return res.status(404).json({ error: 'Product not found or not available to the public' });
    }

    // TODO: Implement product request/inquiry logic
    // This would typically involve creating a new record in a product_inquiries table
    // For now, we'll just return a success message

    return res.status(200).json({ 
      message: 'Product request submitted successfully',
      request: {
        product: {
          id: product.id,
          name: product.name
        },
        customer: {
          id: customer.id,
          name: customer.name
        },
        quantity,
        notes
      }
    });
  } catch (error) {
    console.error('Error requesting product:', error);
    return res.status(500).json({ error: error.message });
  }
};