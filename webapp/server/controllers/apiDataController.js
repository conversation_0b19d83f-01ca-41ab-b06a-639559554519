import { Op } from 'sequelize';
import ApiProvider from '../models/ApiProvider.js';
import ApiEndpoint from '../models/ApiEndpoint.js';
import ApiRequest from '../models/ApiRequest.js';
import ApiCache from '../models/ApiCache.js';
import ApiAnalytics from '../models/ApiAnalytics.js';
import { cleanupExpiredCache } from '../utils/apiDataService.js';

/**
 * Get all API providers
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const getAllApiProviders = async (req, res) => {
  try {
    const providers = await ApiProvider.findAll({
      order: [['name', 'ASC']]
    });

    return res.status(200).json(providers);
  } catch (error) {
    console.error('Error getting API providers:', error);
    return res.status(500).json({ error: 'Failed to get API providers' });
  }
};

/**
 * Get a specific API provider by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const getApiProviderById = async (req, res) => {
  try {
    const { providerId } = req.params;

    const provider = await ApiProvider.findByPk(providerId);

    if (!provider) {
      return res.status(404).json({ error: 'API provider not found' });
    }

    return res.status(200).json(provider);
  } catch (error) {
    console.error('Error getting API provider:', error);
    return res.status(500).json({ error: 'Failed to get API provider' });
  }
};

/**
 * Get all API endpoints
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const getAllApiEndpoints = async (req, res) => {
  try {
    const { providerId } = req.query;

    const whereClause = providerId ? { provider_id: providerId } : {};

    const endpoints = await ApiEndpoint.findAll({
      where: whereClause,
      include: [
        {
          model: ApiProvider,
          attributes: ['id', 'name', 'category']
        }
      ],
      order: [['name', 'ASC']]
    });

    return res.status(200).json(endpoints);
  } catch (error) {
    console.error('Error getting API endpoints:', error);
    return res.status(500).json({ error: 'Failed to get API endpoints' });
  }
};

/**
 * Get a specific API endpoint by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const getApiEndpointById = async (req, res) => {
  try {
    const { endpointId } = req.params;

    const endpoint = await ApiEndpoint.findByPk(endpointId, {
      include: [
        {
          model: ApiProvider,
          attributes: ['id', 'name', 'category', 'base_url']
        }
      ]
    });

    if (!endpoint) {
      return res.status(404).json({ error: 'API endpoint not found' });
    }

    return res.status(200).json(endpoint);
  } catch (error) {
    console.error('Error getting API endpoint:', error);
    return res.status(500).json({ error: 'Failed to get API endpoint' });
  }
};

/**
 * Update an API endpoint
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const updateApiEndpoint = async (req, res) => {
  try {
    const { endpointId } = req.params;
    const { 
      name, 
      path, 
      method, 
      description, 
      request_format, 
      response_format, 
      cache_duration_seconds,
      is_enabled
    } = req.body;

    const endpoint = await ApiEndpoint.findByPk(endpointId);

    if (!endpoint) {
      return res.status(404).json({ error: 'API endpoint not found' });
    }

    // Update the endpoint
    await endpoint.update({
      name: name || endpoint.name,
      path: path || endpoint.path,
      method: method || endpoint.method,
      description: description !== undefined ? description : endpoint.description,
      request_format: request_format !== undefined ? request_format : endpoint.request_format,
      response_format: response_format !== undefined ? response_format : endpoint.response_format,
      cache_duration_seconds: cache_duration_seconds || endpoint.cache_duration_seconds,
      is_enabled: is_enabled !== undefined ? is_enabled : endpoint.is_enabled
    });

    return res.status(200).json(endpoint);
  } catch (error) {
    console.error('Error updating API endpoint:', error);
    return res.status(500).json({ error: 'Failed to update API endpoint' });
  }
};

/**
 * Get all cached API data
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const getAllApiCache = async (req, res) => {
  try {
    const { providerId, endpointId, search } = req.query;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const offset = (page - 1) * limit;

    // Build the where clause for filtering
    let whereClause = {};
    let includeClause = [
      {
        model: ApiEndpoint,
        attributes: ['id', 'name', 'path', 'method'],
        include: [
          {
            model: ApiProvider,
            attributes: ['id', 'name', 'category']
          }
        ]
      }
    ];

    // Filter by provider
    if (providerId) {
      includeClause = [
        {
          model: ApiEndpoint,
          attributes: ['id', 'name', 'path', 'method'],
          where: { provider_id: providerId },
          include: [
            {
              model: ApiProvider,
              attributes: ['id', 'name', 'category']
            }
          ]
        }
      ];
    }

    // Filter by endpoint
    if (endpointId) {
      whereClause.endpoint_id = endpointId;
    }

    // Search in request_params or response_body
    if (search) {
      whereClause[Op.or] = [
        { request_params: { [Op.like]: `%${search}%` } },
        { '$ApiEndpoint.name$': { [Op.like]: `%${search}%` } },
        { '$ApiEndpoint.ApiProvider.name$': { [Op.like]: `%${search}%` } }
      ];
    }

    // Get the total count for pagination
    const count = await ApiCache.count({
      where: whereClause,
      include: includeClause
    });

    // Get the cached data
    const cacheData = await ApiCache.findAll({
      where: whereClause,
      include: includeClause,
      order: [['created_at', 'DESC']],
      limit,
      offset
    });

    return res.status(200).json({
      data: cacheData,
      pagination: {
        total: count,
        page,
        limit,
        pages: Math.ceil(count / limit)
      }
    });
  } catch (error) {
    console.error('Error getting API cache data:', error);
    return res.status(500).json({ error: 'Failed to get API cache data' });
  }
};

/**
 * Get a specific cached API data item by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const getApiCacheById = async (req, res) => {
  try {
    const { cacheId } = req.params;

    const cacheItem = await ApiCache.findByPk(cacheId, {
      include: [
        {
          model: ApiEndpoint,
          attributes: ['id', 'name', 'path', 'method'],
          include: [
            {
              model: ApiProvider,
              attributes: ['id', 'name', 'category', 'base_url']
            }
          ]
        }
      ]
    });

    if (!cacheItem) {
      return res.status(404).json({ error: 'Cached item not found' });
    }

    return res.status(200).json(cacheItem);
  } catch (error) {
    console.error('Error getting cached item:', error);
    return res.status(500).json({ error: 'Failed to get cached item' });
  }
};

/**
 * Delete a specific cached API data item by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const deleteApiCache = async (req, res) => {
  try {
    const { cacheId } = req.params;

    const cacheItem = await ApiCache.findByPk(cacheId);

    if (!cacheItem) {
      return res.status(404).json({ error: 'Cached item not found' });
    }

    await cacheItem.destroy();

    return res.status(200).json({ message: 'Cached item deleted successfully' });
  } catch (error) {
    console.error('Error deleting cached item:', error);
    return res.status(500).json({ error: 'Failed to delete cached item' });
  }
};

/**
 * Clean up expired cache entries
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const cleanupCache = async (req, res) => {
  try {
    const { olderThanDays } = req.query;

    const removedCount = await cleanupExpiredCache(parseInt(olderThanDays) || 7);

    return res.status(200).json({ 
      message: `Removed ${removedCount} expired cache entries`,
      removed_count: removedCount
    });
  } catch (error) {
    console.error('Error cleaning up cache:', error);
    return res.status(500).json({ error: 'Failed to clean up cache' });
  }
};

/**
 * Get API analytics data
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const getApiAnalytics = async (req, res) => {
  try {
    const { providerId, endpointId, startDate, endDate } = req.query;

    // Build the where clause for filtering
    let whereClause = {};

    // Filter by provider
    if (providerId) {
      whereClause.provider_id = providerId;
    }

    // Filter by endpoint
    if (endpointId) {
      whereClause.endpoint_id = endpointId;
    }

    // Filter by date range
    if (startDate || endDate) {
      whereClause.date = {};

      if (startDate) {
        whereClause.date[Op.gte] = new Date(startDate);
      }

      if (endDate) {
        whereClause.date[Op.lte] = new Date(endDate);
      }
    }

    // Get the analytics data
    const analytics = await ApiAnalytics.findAll({
      where: whereClause,
      include: [
        {
          model: ApiProvider,
          attributes: ['id', 'name', 'category']
        },
        {
          model: ApiEndpoint,
          attributes: ['id', 'name', 'path', 'method']
        }
      ],
      order: [['date', 'DESC']]
    });

    return res.status(200).json(analytics);
  } catch (error) {
    console.error('Error getting API analytics:', error);
    return res.status(500).json({ error: 'Failed to get API analytics' });
  }
};

/**
 * Get recent API requests
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const getRecentApiRequests = async (req, res) => {
  try {
    const { providerId, endpointId, limit } = req.query;

    // Build the where clause for filtering
    let whereClause = {};
    let includeClause = [
      {
        model: ApiEndpoint,
        attributes: ['id', 'name', 'path', 'method'],
        include: [
          {
            model: ApiProvider,
            attributes: ['id', 'name', 'category']
          }
        ]
      }
    ];

    // Filter by provider
    if (providerId) {
      includeClause = [
        {
          model: ApiEndpoint,
          attributes: ['id', 'name', 'path', 'method'],
          where: { provider_id: providerId },
          include: [
            {
              model: ApiProvider,
              attributes: ['id', 'name', 'category']
            }
          ]
        }
      ];
    }

    // Filter by endpoint
    if (endpointId) {
      whereClause.endpoint_id = endpointId;
    }

    // Get the recent requests
    const requests = await ApiRequest.findAll({
      where: whereClause,
      include: includeClause,
      order: [['request_time', 'DESC']],
      limit: parseInt(limit) || 20
    });

    return res.status(200).json(requests);
  } catch (error) {
    console.error('Error getting recent API requests:', error);
    return res.status(500).json({ error: 'Failed to get recent API requests' });
  }
};

/**
 * Create a new API provider
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const createApiProvider = async (req, res) => {
  try {
    const { name, category, base_url, requires_key, description, is_enabled } = req.body;

    // Validate required fields
    if (!name || !category) {
      return res.status(400).json({ error: 'Name and category are required' });
    }

    // Create the provider
    const provider = await ApiProvider.create({
      name,
      category,
      base_url: base_url || null,
      requires_key: requires_key !== undefined ? requires_key : true,
      description: description || null,
      is_enabled: is_enabled !== undefined ? is_enabled : true
    });

    return res.status(201).json(provider);
  } catch (error) {
    console.error('Error creating API provider:', error);
    return res.status(500).json({ error: 'Failed to create API provider' });
  }
};

/**
 * Update an API provider
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const updateApiProvider = async (req, res) => {
  try {
    const { providerId } = req.params;
    const { name, category, base_url, requires_key, description, is_enabled } = req.body;

    const provider = await ApiProvider.findByPk(providerId);

    if (!provider) {
      return res.status(404).json({ error: 'API provider not found' });
    }

    // Update the provider
    await provider.update({
      name: name || provider.name,
      category: category || provider.category,
      base_url: base_url !== undefined ? base_url : provider.base_url,
      requires_key: requires_key !== undefined ? requires_key : provider.requires_key,
      description: description !== undefined ? description : provider.description,
      is_enabled: is_enabled !== undefined ? is_enabled : provider.is_enabled
    });

    return res.status(200).json(provider);
  } catch (error) {
    console.error('Error updating API provider:', error);
    return res.status(500).json({ error: 'Failed to update API provider' });
  }
};

/**
 * Enable or disable an API provider
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const toggleApiProviderStatus = async (req, res) => {
  try {
    const { providerId } = req.params;
    const { is_enabled } = req.body;

    if (is_enabled === undefined) {
      return res.status(400).json({ error: 'is_enabled field is required' });
    }

    const provider = await ApiProvider.findByPk(providerId);

    if (!provider) {
      return res.status(404).json({ error: 'API provider not found' });
    }

    // Update the provider's enabled status
    await provider.update({ is_enabled });

    return res.status(200).json({
      message: `API provider ${is_enabled ? 'enabled' : 'disabled'} successfully`,
      provider
    });
  } catch (error) {
    console.error('Error toggling API provider status:', error);
    return res.status(500).json({ error: 'Failed to toggle API provider status' });
  }
};

/**
 * Create a new API endpoint
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const createApiEndpoint = async (req, res) => {
  try {
    const { 
      provider_id, 
      name, 
      path, 
      method, 
      description, 
      request_format, 
      response_format, 
      cache_duration_seconds,
      is_enabled
    } = req.body;

    // Validate required fields
    if (!provider_id || !name || !path) {
      return res.status(400).json({ error: 'Provider ID, name, and path are required' });
    }

    // Check if the provider exists
    const provider = await ApiProvider.findByPk(provider_id);

    if (!provider) {
      return res.status(404).json({ error: 'API provider not found' });
    }

    // Create the endpoint
    const endpoint = await ApiEndpoint.create({
      provider_id,
      name,
      path,
      method: method || 'GET',
      description: description || null,
      request_format: request_format || null,
      response_format: response_format || null,
      cache_duration_seconds: cache_duration_seconds || 3600,
      is_enabled: is_enabled !== undefined ? is_enabled : true
    });

    return res.status(201).json(endpoint);
  } catch (error) {
    console.error('Error creating API endpoint:', error);
    return res.status(500).json({ error: 'Failed to create API endpoint' });
  }
};

/**
 * Enable or disable an API endpoint
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const toggleApiEndpointStatus = async (req, res) => {
  try {
    const { endpointId } = req.params;
    const { is_enabled } = req.body;

    if (is_enabled === undefined) {
      return res.status(400).json({ error: 'is_enabled field is required' });
    }

    const endpoint = await ApiEndpoint.findByPk(endpointId);

    if (!endpoint) {
      return res.status(404).json({ error: 'API endpoint not found' });
    }

    // Update the endpoint's enabled status
    await endpoint.update({ is_enabled });

    return res.status(200).json({
      message: `API endpoint ${is_enabled ? 'enabled' : 'disabled'} successfully`,
      endpoint
    });
  } catch (error) {
    console.error('Error toggling API endpoint status:', error);
    return res.status(500).json({ error: 'Failed to toggle API endpoint status' });
  }
};
