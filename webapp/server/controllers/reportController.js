import Report from '../models/Report.js';
import Farm from '../models/Farm.js';
import { Op } from 'sequelize';
import { sequelize } from '../config/database.js';

// Mock chart data for different report types
// This would be replaced with real data generation in a production environment
const mockChartData = {
  'farm-performance': {
    title: 'Farm Performance',
    description: 'Overall farm performance metrics including revenue, expenses, and profitability.',
    metrics: [
      { name: 'Total Revenue', value: '$125,000', change: '+12%', trend: 'up' },
      { name: 'Total Expenses', value: '$78,500', change: '+8%', trend: 'up' },
      { name: 'Net Profit', value: '$46,500', change: '+18%', trend: 'up' },
      { name: 'Profit Margin', value: '37.2%', change: '+2.5%', trend: 'up' }
    ],
    chartDescription: 'Revenue and expenses by month for the current year.',
    tableData: [
      { period: 'January', revenue: '$9,200', expenses: '$5,800', profit: '$3,400' },
      { period: 'February', revenue: '$8,500', expenses: '$5,200', profit: '$3,300' },
      { period: 'March', revenue: '$10,300', expenses: '$6,100', profit: '$4,200' },
      { period: 'April', revenue: '$11,500', expenses: '$7,200', profit: '$4,300' },
      { period: 'May', revenue: '$12,800', expenses: '$7,800', profit: '$5,000' },
    ]
  },
  'field-production': {
    title: 'Field Production',
    description: 'Yields and production metrics by field, crop type, and season.',
    metrics: [
      { name: 'Total Yield', value: '1,250 tons', change: '+5%', trend: 'up' },
      { name: 'Yield per Acre', value: '4.2 tons', change: '+3%', trend: 'up' },
      { name: 'Planted Area', value: '298 acres', change: '+2%', trend: 'up' },
      { name: 'Harvest Efficiency', value: '92%', change: '+1%', trend: 'up' }
    ],
    chartDescription: 'Yield comparison by field for the current season.',
    tableData: [
      { field: 'North Field', crop: 'Corn', area: '45 acres', yield: '198 tons', yieldPerAcre: '4.4 tons' },
      { field: 'South Field', crop: 'Soybeans', area: '38 acres', yield: '152 tons', yieldPerAcre: '4.0 tons' },
      { field: 'East Field', crop: 'Wheat', area: '42 acres', yield: '168 tons', yieldPerAcre: '4.0 tons' },
      { field: 'West Field', crop: 'Corn', area: '40 acres', yield: '180 tons', yieldPerAcre: '4.5 tons' },
      { field: 'Central Field', crop: 'Alfalfa', area: '35 acres', yield: '140 tons', yieldPerAcre: '4.0 tons' },
    ]
  },
  'equipment-utilization': {
    title: 'Equipment Utilization',
    description: 'Usage and efficiency metrics for farm equipment.',
    metrics: [
      { name: 'Total Usage', value: '1,850 hours', change: '+8%', trend: 'up' },
      { name: 'Maintenance Cost', value: '$12,500', change: '-5%', trend: 'down' },
      { name: 'Fuel Consumption', value: '4,200 gal', change: '+3%', trend: 'up' },
      { name: 'Downtime', value: '48 hours', change: '-15%', trend: 'down' }
    ],
    chartDescription: 'Equipment usage hours by type for the current year.',
    tableData: [
      { equipment: 'Tractor A', type: 'Tractor', hours: '420', fuelUsed: '980 gal', maintenanceCost: '$2,800' },
      { equipment: 'Harvester B', type: 'Harvester', hours: '280', fuelUsed: '850 gal', maintenanceCost: '$3,200' },
      { equipment: 'Tractor C', type: 'Tractor', hours: '390', fuelUsed: '920 gal', maintenanceCost: '$2,100' },
      { equipment: 'Sprayer D', type: 'Sprayer', hours: '210', fuelUsed: '480 gal', maintenanceCost: '$1,500' },
      { equipment: 'Plow E', type: 'Plow', hours: '180', fuelUsed: '420 gal', maintenanceCost: '$900' },
    ]
  },
  'inventory-status': {
    title: 'Inventory Status',
    description: 'Current inventory levels, valuation, and reorder alerts.',
    metrics: [
      { name: 'Total Items', value: '128', change: '+12', trend: 'up' },
      { name: 'Total Value', value: '$85,200', change: '+15%', trend: 'up' },
      { name: 'Low Stock Items', value: '8', change: '-2', trend: 'down' },
      { name: 'Expired Items', value: '3', change: '-1', trend: 'down' }
    ],
    chartDescription: 'Inventory value by category.',
    tableData: [
      { item: 'Corn Seed', category: 'Seed', quantity: '250 bags', value: '$12,500', status: 'Adequate' },
      { item: 'Fertilizer A', category: 'Fertilizer', quantity: '180 bags', value: '$9,000', status: 'Low Stock' },
      { item: 'Herbicide B', category: 'Herbicide', quantity: '85 gal', value: '$8,500', status: 'Adequate' },
      { item: 'Diesel Fuel', category: 'Fuel', quantity: '1,200 gal', value: '$4,800', status: 'Adequate' },
      { item: 'Animal Feed', category: 'Feed', quantity: '320 bags', value: '$6,400', status: 'Low Stock' },
    ]
  },
  'financial-summary': {
    title: 'Financial Summary',
    description: 'Income, expenses, and profitability by time period and category.',
    metrics: [
      { name: 'Total Revenue', value: '$125,000', change: '+12%', trend: 'up' },
      { name: 'Total Expenses', value: '$78,500', change: '+8%', trend: 'up' },
      { name: 'Net Profit', value: '$46,500', change: '+18%', trend: 'up' },
      { name: 'Cash on Hand', value: '$32,800', change: '+22%', trend: 'up' }
    ],
    chartDescription: 'Revenue and expenses by category.',
    tableData: [
      { category: 'Crop Sales', revenue: '$85,000', expenses: '$45,000', profit: '$40,000' },
      { category: 'Livestock', revenue: '$25,000', expenses: '$18,000', profit: '$7,000' },
      { category: 'Equipment Rental', revenue: '$8,000', expenses: '$2,500', profit: '$5,500' },
      { category: 'Government Subsidies', revenue: '$7,000', expenses: '$0', profit: '$7,000' },
      { category: 'Other', revenue: '$0', expenses: '$13,000', profit: '-$13,000' },
    ]
  },
  'employee-productivity': {
    title: 'Employee Productivity',
    description: 'Work hours, task completion, and productivity metrics by employee.',
    metrics: [
      { name: 'Total Hours', value: '2,850 hours', change: '+5%', trend: 'up' },
      { name: 'Tasks Completed', value: '385', change: '+12%', trend: 'up' },
      { name: 'Avg. Completion Time', value: '3.2 hours', change: '-8%', trend: 'down' },
      { name: 'Overtime Hours', value: '120 hours', change: '-15%', trend: 'down' }
    ],
    chartDescription: 'Hours worked and tasks completed by employee.',
    tableData: [
      { employee: 'John Smith', role: 'Manager', hoursWorked: '180', tasksCompleted: '45', efficiency: '98%' },
      { employee: 'Jane Doe', role: 'Field Worker', hoursWorked: '165', tasksCompleted: '38', efficiency: '92%' },
      { employee: 'Bob Johnson', role: 'Equipment Operator', hoursWorked: '175', tasksCompleted: '42', efficiency: '95%' },
      { employee: 'Alice Brown', role: 'Administrative', hoursWorked: '160', tasksCompleted: '52', efficiency: '97%' },
      { employee: 'Charlie Davis', role: 'Field Worker', hoursWorked: '170', tasksCompleted: '40', efficiency: '94%' },
    ]
  }
};

// Get all reports for a farm
export const getReports = async (req, res) => {
  try {
    const { farmId } = req.query;
    
    if (!farmId) {
      return res.status(400).json({ 
        success: false, 
        message: 'Farm ID is required' 
      });
    }
    
    const reports = await Report.findAll({
      where: { farm_id: farmId },
      include: [
        {
          model: Farm,
          attributes: ['id', 'name']
        }
      ],
      order: [['created_at', 'DESC']]
    });
    
    return res.status(200).json({ 
      success: true, 
      reports: reports.map(report => ({
        id: report.id,
        name: report.name,
        type: report.type,
        parameters: report.parameters,
        farmId: report.farm_id,
        farmName: report.Farm ? report.Farm.name : null,
        createdAt: report.created_at,
        updatedAt: report.updated_at
      }))
    });
  } catch (error) {
    console.error('Error getting reports:', error);
    return res.status(500).json({ 
      success: false, 
      message: 'Failed to get reports', 
      error: error.message 
    });
  }
};

// Get a single report by ID
export const getReportById = async (req, res) => {
  try {
    const { reportId } = req.params;
    
    const report = await Report.findByPk(reportId, {
      include: [
        {
          model: Farm,
          attributes: ['id', 'name']
        }
      ]
    });
    
    if (!report) {
      return res.status(404).json({ 
        success: false, 
        message: 'Report not found' 
      });
    }
    
    return res.status(200).json({ 
      success: true, 
      report: {
        id: report.id,
        name: report.name,
        type: report.type,
        parameters: report.parameters,
        farmId: report.farm_id,
        farmName: report.Farm ? report.Farm.name : null,
        createdAt: report.created_at,
        updatedAt: report.updated_at
      }
    });
  } catch (error) {
    console.error('Error getting report by ID:', error);
    return res.status(500).json({ 
      success: false, 
      message: 'Failed to get report', 
      error: error.message 
    });
  }
};

// Create a new report
export const createReport = async (req, res) => {
  try {
    const { farmId, name, type, parameters } = req.body;
    
    if (!farmId || !name || !type) {
      return res.status(400).json({ 
        success: false, 
        message: 'Farm ID, name, and type are required' 
      });
    }
    
    // Validate report type
    const validTypes = ['farm-performance', 'field-production', 'equipment-utilization', 
                        'inventory-status', 'financial-summary', 'employee-productivity'];
    if (!validTypes.includes(type)) {
      return res.status(400).json({ 
        success: false, 
        message: `Invalid report type. Must be one of: ${validTypes.join(', ')}` 
      });
    }
    
    // Create the report
    const report = await Report.create({
      farm_id: farmId,
      name,
      type,
      parameters: parameters || {}
    });
    
    return res.status(201).json({ 
      success: true, 
      message: 'Report created successfully',
      report: {
        id: report.id,
        name: report.name,
        type: report.type,
        parameters: report.parameters,
        farmId: report.farm_id,
        createdAt: report.created_at,
        updatedAt: report.updated_at
      }
    });
  } catch (error) {
    console.error('Error creating report:', error);
    return res.status(500).json({ 
      success: false, 
      message: 'Failed to create report', 
      error: error.message 
    });
  }
};

// Update an existing report
export const updateReport = async (req, res) => {
  try {
    const { reportId } = req.params;
    const { name, type, parameters } = req.body;
    
    // Find the report
    const report = await Report.findByPk(reportId);
    
    if (!report) {
      return res.status(404).json({ 
        success: false, 
        message: 'Report not found' 
      });
    }
    
    // Validate report type if provided
    if (type) {
      const validTypes = ['farm-performance', 'field-production', 'equipment-utilization', 
                          'inventory-status', 'financial-summary', 'employee-productivity'];
      if (!validTypes.includes(type)) {
        return res.status(400).json({ 
          success: false, 
          message: `Invalid report type. Must be one of: ${validTypes.join(', ')}` 
        });
      }
    }
    
    // Update the report
    await report.update({
      name: name !== undefined ? name : report.name,
      type: type !== undefined ? type : report.type,
      parameters: parameters !== undefined ? parameters : report.parameters
    });
    
    return res.status(200).json({ 
      success: true, 
      message: 'Report updated successfully',
      report: {
        id: report.id,
        name: report.name,
        type: report.type,
        parameters: report.parameters,
        farmId: report.farm_id,
        createdAt: report.created_at,
        updatedAt: report.updated_at
      }
    });
  } catch (error) {
    console.error('Error updating report:', error);
    return res.status(500).json({ 
      success: false, 
      message: 'Failed to update report', 
      error: error.message 
    });
  }
};

// Delete a report
export const deleteReport = async (req, res) => {
  try {
    const { reportId } = req.params;
    
    // Find the report
    const report = await Report.findByPk(reportId);
    
    if (!report) {
      return res.status(404).json({ 
        success: false, 
        message: 'Report not found' 
      });
    }
    
    // Delete the report
    await report.destroy();
    
    return res.status(200).json({ 
      success: true, 
      message: 'Report deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting report:', error);
    return res.status(500).json({ 
      success: false, 
      message: 'Failed to delete report', 
      error: error.message 
    });
  }
};

// Generate report data based on report type and parameters
export const generateReportData = async (req, res) => {
  try {
    const { reportId } = req.params;
    
    // Find the report
    const report = await Report.findByPk(reportId, {
      include: [
        {
          model: Farm,
          attributes: ['id', 'name']
        }
      ]
    });
    
    if (!report) {
      return res.status(404).json({ 
        success: false, 
        message: 'Report not found' 
      });
    }
    
    // Get report type
    const reportType = report.type;
    
    // Get mock data for the report type
    const reportData = mockChartData[reportType];
    
    if (!reportData) {
      return res.status(404).json({ 
        success: false, 
        message: 'Report type not supported' 
      });
    }
    
    // In a real implementation, this would generate real data based on the report type and parameters
    // For now, we'll return the mock data
    
    return res.status(200).json({ 
      success: true, 
      report: {
        id: report.id,
        name: report.name,
        type: report.type,
        parameters: report.parameters,
        farmId: report.farm_id,
        farmName: report.Farm ? report.Farm.name : null,
        createdAt: report.created_at,
        updatedAt: report.updated_at,
        data: reportData
      }
    });
  } catch (error) {
    console.error('Error generating report data:', error);
    return res.status(500).json({ 
      success: false, 
      message: 'Failed to generate report data', 
      error: error.message 
    });
  }
};

// Get report data by type (without requiring a saved report)
export const getReportDataByType = async (req, res) => {
  try {
    const { reportType } = req.params;
    const { farmId } = req.query;
    
    if (!farmId) {
      return res.status(400).json({ 
        success: false, 
        message: 'Farm ID is required' 
      });
    }
    
    // Validate report type
    const validTypes = ['farm-performance', 'field-production', 'equipment-utilization', 
                        'inventory-status', 'financial-summary', 'employee-productivity'];
    if (!validTypes.includes(reportType)) {
      return res.status(400).json({ 
        success: false, 
        message: `Invalid report type. Must be one of: ${validTypes.join(', ')}` 
      });
    }
    
    // Get mock data for the report type
    const reportData = mockChartData[reportType];
    
    if (!reportData) {
      return res.status(404).json({ 
        success: false, 
        message: 'Report type not supported' 
      });
    }
    
    // In a real implementation, this would generate real data based on the report type and farm ID
    // For now, we'll return the mock data
    
    return res.status(200).json({ 
      success: true, 
      reportType,
      farmId,
      data: reportData
    });
  } catch (error) {
    console.error('Error getting report data by type:', error);
    return res.status(500).json({ 
      success: false, 
      message: 'Failed to get report data', 
      error: error.message 
    });
  }
};