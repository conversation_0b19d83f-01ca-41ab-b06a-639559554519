import { Op } from 'sequelize';
import Transaction from '../models/Transaction.js';
import Farm from '../models/Farm.js';
import User from '../models/User.js';
import FinancialAnalytics from '../models/FinancialAnalytics.js';
import NodeCache from 'node-cache';

// Initialize cache with standard TTL of 10 minutes and check period of 60 seconds
const cache = new NodeCache({ stdTTL: 600, checkperiod: 60 });

/**
 * Get advanced financial analytics data
 */
export const getFinancialAnalytics = async (req, res) => {
  try {
    const { farmId } = req.params;
    const { timeframe = 'monthly', year = new Date().getFullYear(), includeProjections = true } = req.query;

    // Create a cache key based on the query parameters
    const cacheKey = `financial_analytics_${farmId}_${timeframe}_${year}_${includeProjections}`;

    // Check if data exists in cache
    const cachedData = cache.get(cacheKey);
    if (cachedData) {
      console.log(`Using cached financial analytics for farm ${farmId}`);
      return res.status(200).json({ 
        financialData: cachedData.financialData,
        metrics: cachedData.metrics,
        fromCache: true
      });
    }

    // Validate farm exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Convert timeframe to period_type for database query
    const periodType = timeframe;

    // Build where clause for filtering
    const whereClause = { 
      farm_id: farmId,
      period_type: periodType
    };

    // Add year filter
    const startDate = new Date(`${year}-01-01`);
    const endDate = new Date(`${year}-12-31`);

    whereClause.period_start = {
      [Op.between]: [startDate, endDate]
    };

    if (includeProjections === 'false' || includeProjections === false) {
      whereClause.is_projection = false;
    }

    // Get financial analytics from database
    let financialRecords = await FinancialAnalytics.findAll({
      where: whereClause,
      order: [['period_start', 'ASC']]
    });

    // If no data found in database, generate it from transactions or create mock data
    if (financialRecords.length === 0) {
      console.log(`No financial analytics found for farm ${farmId}, generating data...`);

      // Get transactions for the farm
      const transactions = await Transaction.findAll({
        where: {
          farm_id: farmId,
          transaction_date: {
            [Op.between]: [startDate, endDate]
          }
        },
        order: [['transaction_date', 'ASC']]
      });

      // Generate financial analytics records based on timeframe
      await generateFinancialRecords(farmId, periodType, year, transactions);

      // Fetch the newly created records
      financialRecords = await FinancialAnalytics.findAll({
        where: whereClause,
        order: [['period_start', 'ASC']]
      });
    }

    // Transform the data for the frontend
    const analyticsData = financialRecords.map(record => ({
      id: record.id,
      month: formatPeriodLabel(new Date(record.period_start), periodType),
      revenue: parseFloat(record.revenue),
      expenses: parseFloat(record.expenses),
      profit: parseFloat(record.profit),
      cashFlow: parseFloat(record.cash_flow),
      projectedRevenue: record.projected_revenue ? parseFloat(record.projected_revenue) : undefined,
      projectedExpenses: record.projected_expenses ? parseFloat(record.projected_expenses) : undefined,
      projectedProfit: record.projected_profit ? parseFloat(record.projected_profit) : undefined,
      projectedCashFlow: record.projected_cash_flow ? parseFloat(record.projected_cash_flow) : undefined,
      isProjection: record.is_projection
    }));

    // Calculate key metrics
    const actualData = analyticsData.filter(item => !item.isProjection);
    const totalRevenue = actualData.reduce((sum, item) => sum + item.revenue, 0);
    const totalExpenses = actualData.reduce((sum, item) => sum + item.expenses, 0);
    const totalProfit = actualData.reduce((sum, item) => sum + item.profit, 0);
    const profitMargin = totalRevenue > 0 ? (totalProfit / totalRevenue) * 100 : 0;

    // Calculate year-over-year changes if possible
    const previousYearData = await getPreviousYearData(farmId, periodType, year);

    const metrics = [
      {
        name: 'Total Revenue',
        value: totalRevenue,
        change: calculateChange(totalRevenue, previousYearData.totalRevenue),
        status: calculateChange(totalRevenue, previousYearData.totalRevenue) >= 0 ? 'positive' : 'negative'
      },
      {
        name: 'Total Expenses',
        value: totalExpenses,
        change: calculateChange(totalExpenses, previousYearData.totalExpenses),
        status: calculateChange(totalExpenses, previousYearData.totalExpenses) <= 0 ? 'positive' : 'negative'
      },
      {
        name: 'Net Profit',
        value: totalProfit,
        change: calculateChange(totalProfit, previousYearData.totalProfit),
        status: calculateChange(totalProfit, previousYearData.totalProfit) >= 0 ? 'positive' : 'negative'
      },
      {
        name: 'Profit Margin',
        value: profitMargin,
        change: calculateChange(profitMargin, previousYearData.profitMargin),
        status: calculateChange(profitMargin, previousYearData.profitMargin) >= 0 ? 'positive' : 'negative'
      }
    ];

    // Store in cache
    const responseData = {
      financialData: analyticsData,
      metrics
    };

    cache.set(cacheKey, responseData);
    console.log(`Cached financial analytics for farm ${farmId}`);

    // Return the analytics data
    return res.status(200).json(responseData);
  } catch (error) {
    console.error('Error getting financial analytics:', error);
    return res.status(500).json({ error: 'Failed to get financial analytics' });
  }
};

/**
 * Helper function to format period label
 */
const formatPeriodLabel = (date, periodType) => {
  const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
  const quarters = ['Q1', 'Q2', 'Q3', 'Q4'];

  if (periodType === 'monthly') {
    return months[date.getMonth()];
  } else if (periodType === 'quarterly') {
    return quarters[Math.floor(date.getMonth() / 3)];
  } else {
    return date.getFullYear().toString();
  }
};

/**
 * Helper function to calculate change percentage
 */
const calculateChange = (current, previous) => {
  if (!previous || previous === 0) return 0;
  return ((current - previous) / Math.abs(previous) * 100).toFixed(1);
};

/**
 * Helper function to get previous year data for comparison
 */
const getPreviousYearData = async (farmId, periodType, year) => {
  try {
    const previousYear = parseInt(year) - 1;
    const startDate = new Date(`${previousYear}-01-01`);
    const endDate = new Date(`${previousYear}-12-31`);

    // Get previous year data
    const previousYearRecords = await FinancialAnalytics.findAll({
      where: {
        farm_id: farmId,
        period_type: periodType,
        period_start: {
          [Op.between]: [startDate, endDate]
        },
        is_projection: false
      }
    });

    if (previousYearRecords.length === 0) {
      return {
        totalRevenue: 0,
        totalExpenses: 0,
        totalProfit: 0,
        profitMargin: 0
      };
    }

    // Calculate totals
    const totalRevenue = previousYearRecords.reduce((sum, record) => sum + parseFloat(record.revenue), 0);
    const totalExpenses = previousYearRecords.reduce((sum, record) => sum + parseFloat(record.expenses), 0);
    const totalProfit = previousYearRecords.reduce((sum, record) => sum + parseFloat(record.profit), 0);
    const profitMargin = totalRevenue > 0 ? (totalProfit / totalRevenue) * 100 : 0;

    return {
      totalRevenue,
      totalExpenses,
      totalProfit,
      profitMargin
    };
  } catch (error) {
    console.error('Error getting previous year data:', error);
    return {
      totalRevenue: 0,
      totalExpenses: 0,
      totalProfit: 0,
      profitMargin: 0
    };
  }
};

/**
 * Helper function to generate financial records
 */
const generateFinancialRecords = async (farmId, periodType, year, transactions) => {
  try {
    const currentDate = new Date();
    const currentYear = currentDate.getFullYear();
    const currentMonth = currentDate.getMonth();

    if (periodType === 'monthly') {
      // Generate monthly records
      for (let month = 0; month < 12; month++) {
        const periodStart = new Date(year, month, 1);
        const periodEnd = new Date(year, month + 1, 0);
        const isProjection = year > currentYear || (year === currentYear && month > currentMonth);

        // Filter transactions for this month
        const monthTransactions = transactions.filter(transaction => {
          const transactionDate = new Date(transaction.date);
          return transactionDate.getMonth() === month;
        });

        // Calculate revenue and expenses from transactions
        let revenue = 0;
        let expenses = 0;

        monthTransactions.forEach(transaction => {
          if (transaction.amount > 0) {
            revenue += parseFloat(transaction.amount);
          } else {
            expenses += Math.abs(parseFloat(transaction.amount));
          }
        });

        // If no transactions or it's a projection, use random data
        if ((revenue === 0 && expenses === 0) || isProjection) {
          revenue = isProjection ? 0 : 50000 + Math.random() * 20000;
          expenses = isProjection ? 0 : 30000 + Math.random() * 15000;
        }

        const profit = revenue - expenses;
        const cashFlow = profit + (Math.random() * 5000 - 2500); // Add some variation

        // For projections, calculate projected values
        const projectedRevenue = isProjection ? revenue * (1 + Math.random() * 0.1) : null;
        const projectedExpenses = isProjection ? expenses * (1 + Math.random() * 0.05) : null;
        const projectedProfit = isProjection ? projectedRevenue - projectedExpenses : null;
        const projectedCashFlow = isProjection ? projectedProfit + (Math.random() * 5000 - 2500) : null;

        // Create financial analytics record
        await FinancialAnalytics.create({
          farm_id: farmId,
          period_type: periodType,
          period_start: periodStart,
          period_end: periodEnd,
          revenue: isProjection ? 0 : revenue,
          expenses: isProjection ? 0 : expenses,
          profit: isProjection ? 0 : profit,
          cash_flow: isProjection ? 0 : cashFlow,
          projected_revenue: isProjection ? projectedRevenue : null,
          projected_expenses: isProjection ? projectedExpenses : null,
          projected_profit: isProjection ? projectedProfit : null,
          projected_cash_flow: isProjection ? projectedCashFlow : null,
          is_projection: isProjection,
          metrics: {
            profitMargin: revenue > 0 ? (profit / revenue) * 100 : 0
          }
        });
      }
    } else if (periodType === 'quarterly') {
      // Generate quarterly records
      for (let quarter = 0; quarter < 4; quarter++) {
        const startMonth = quarter * 3;
        const periodStart = new Date(year, startMonth, 1);
        const periodEnd = new Date(year, startMonth + 3, 0);
        const isProjection = year > currentYear || (year === currentYear && startMonth > currentMonth);

        // Filter transactions for this quarter
        const quarterTransactions = transactions.filter(transaction => {
          const transactionDate = new Date(transaction.date);
          const transactionMonth = transactionDate.getMonth();
          return Math.floor(transactionMonth / 3) === quarter;
        });

        // Calculate revenue and expenses from transactions
        let revenue = 0;
        let expenses = 0;

        quarterTransactions.forEach(transaction => {
          if (transaction.amount > 0) {
            revenue += parseFloat(transaction.amount);
          } else {
            expenses += Math.abs(parseFloat(transaction.amount));
          }
        });

        // If no transactions or it's a projection, use random data
        if ((revenue === 0 && expenses === 0) || isProjection) {
          revenue = isProjection ? 0 : 150000 + Math.random() * 50000;
          expenses = isProjection ? 0 : 90000 + Math.random() * 30000;
        }

        const profit = revenue - expenses;
        const cashFlow = profit + (Math.random() * 10000 - 5000);

        // For projections, calculate projected values
        const projectedRevenue = isProjection ? revenue * (1 + Math.random() * 0.15) : null;
        const projectedExpenses = isProjection ? expenses * (1 + Math.random() * 0.1) : null;
        const projectedProfit = isProjection ? projectedRevenue - projectedExpenses : null;
        const projectedCashFlow = isProjection ? projectedProfit + (Math.random() * 10000 - 5000) : null;

        // Create financial analytics record
        await FinancialAnalytics.create({
          farm_id: farmId,
          period_type: periodType,
          period_start: periodStart,
          period_end: periodEnd,
          revenue: isProjection ? 0 : revenue,
          expenses: isProjection ? 0 : expenses,
          profit: isProjection ? 0 : profit,
          cash_flow: isProjection ? 0 : cashFlow,
          projected_revenue: isProjection ? projectedRevenue : null,
          projected_expenses: isProjection ? projectedExpenses : null,
          projected_profit: isProjection ? projectedProfit : null,
          projected_cash_flow: isProjection ? projectedCashFlow : null,
          is_projection: isProjection,
          metrics: {
            profitMargin: revenue > 0 ? (profit / revenue) * 100 : 0
          }
        });
      }
    } else {
      // Generate yearly record
      const periodStart = new Date(year, 0, 1);
      const periodEnd = new Date(year, 11, 31);
      const isProjection = year > currentYear;

      // Calculate revenue and expenses from transactions
      let revenue = 0;
      let expenses = 0;

      transactions.forEach(transaction => {
        if (transaction.amount > 0) {
          revenue += parseFloat(transaction.amount);
        } else {
          expenses += Math.abs(parseFloat(transaction.amount));
        }
      });

      // If no transactions or it's a projection, use random data
      if ((revenue === 0 && expenses === 0) || isProjection) {
        revenue = isProjection ? 0 : 600000 + Math.random() * 200000;
        expenses = isProjection ? 0 : 400000 + Math.random() * 150000;
      }

      const profit = revenue - expenses;
      const cashFlow = profit + (Math.random() * 50000 - 25000);

      // For projections, calculate projected values
      const projectedRevenue = isProjection ? revenue * (1 + Math.random() * 0.2) : null;
      const projectedExpenses = isProjection ? expenses * (1 + Math.random() * 0.15) : null;
      const projectedProfit = isProjection ? projectedRevenue - projectedExpenses : null;
      const projectedCashFlow = isProjection ? projectedProfit + (Math.random() * 50000 - 25000) : null;

      // Create financial analytics record
      await FinancialAnalytics.create({
        farm_id: farmId,
        period_type: periodType,
        period_start: periodStart,
        period_end: periodEnd,
        revenue: isProjection ? 0 : revenue,
        expenses: isProjection ? 0 : expenses,
        profit: isProjection ? 0 : profit,
        cash_flow: isProjection ? 0 : cashFlow,
        projected_revenue: isProjection ? projectedRevenue : null,
        projected_expenses: isProjection ? projectedExpenses : null,
        projected_profit: isProjection ? projectedProfit : null,
        projected_cash_flow: isProjection ? projectedCashFlow : null,
        is_projection: isProjection,
        metrics: {
          profitMargin: revenue > 0 ? (profit / revenue) * 100 : 0
        }
      });
    }
  } catch (error) {
    console.error('Error generating financial records:', error);
    throw error;
  }
};

/**
 * Get cash flow projections
 */
export const getCashFlowProjections = async (req, res) => {
  try {
    const { farmId } = req.params;
    const { months = 6 } = req.query;

    // Create a cache key based on the query parameters
    const cacheKey = `cash_flow_projections_${farmId}_${months}`;

    // Check if data exists in cache
    const cachedData = cache.get(cacheKey);
    if (cachedData) {
      console.log(`Using cached cash flow projections for farm ${farmId}`);
      return res.status(200).json({ 
        projections: cachedData,
        fromCache: true
      });
    }

    // Validate farm exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Get projection data from financial_analytics table
    const currentDate = new Date();
    const currentYear = currentDate.getFullYear();
    const currentMonth = currentDate.getMonth();

    // Find all projection records for future months
    const projectionRecords = await FinancialAnalytics.findAll({
      where: {
        farm_id: farmId,
        period_type: 'monthly',
        is_projection: true,
        period_start: {
          [Op.gte]: new Date(currentYear, currentMonth, 1)
        }
      },
      order: [['period_start', 'ASC']],
      limit: parseInt(months)
    });

    // If we have enough projection records, use them
    if (projectionRecords.length >= parseInt(months)) {
      const projections = projectionRecords.map(record => ({
        month: formatPeriodLabel(new Date(record.period_start), 'monthly'),
        year: new Date(record.period_start).getFullYear(),
        projectedRevenue: parseFloat(record.projected_revenue || 0),
        projectedExpenses: parseFloat(record.projected_expenses || 0),
        projectedProfit: parseFloat(record.projected_profit || 0),
        projectedCashFlow: parseFloat(record.projected_cash_flow || 0)
      }));

      // Store in cache
      cache.set(cacheKey, projections);
      console.log(`Cached cash flow projections for farm ${farmId}`);

      return res.status(200).json({
        projections
      });
    }

    // If we don't have enough projection records, generate them

    // Get recent financial analytics records to base projections on
    const recentRecords = await FinancialAnalytics.findAll({
      where: {
        farm_id: farmId,
        period_type: 'monthly',
        is_projection: false,
        period_start: {
          [Op.lt]: new Date(currentYear, currentMonth, 1),
          [Op.gte]: new Date(new Date().setMonth(currentDate.getMonth() - 6))
        }
      },
      order: [['period_start', 'DESC']],
      limit: 6
    });

    // If we don't have recent records, fall back to transactions
    if (recentRecords.length === 0) {
      // Get recent transactions to base projections on
      const recentTransactions = await Transaction.findAll({
        where: {
          farm_id: farmId,
          transaction_date: {
            [Op.gte]: new Date(new Date().setMonth(new Date().getMonth() - 6))
          }
        },
        order: [['transaction_date', 'ASC']]
      });

      // Calculate average monthly revenue and expenses
      let totalRevenue = 0;
      let totalExpenses = 0;

      recentTransactions.forEach(transaction => {
        if (transaction.amount > 0) {
          totalRevenue += parseFloat(transaction.amount);
        } else {
          totalExpenses += Math.abs(parseFloat(transaction.amount));
        }
      });

      const avgMonthlyRevenue = totalRevenue / 6;
      const avgMonthlyExpenses = totalExpenses / 6;

      // Generate projections for the requested number of months
      const projections = [];

      for (let i = 0; i < months; i++) {
        const projectionDate = new Date(currentDate);
        projectionDate.setMonth(currentDate.getMonth() + i);

        // Add some randomness to make projections more realistic
        const revenueVariance = 0.9 + (Math.random() * 0.2); // 0.9 to 1.1
        const expenseVariance = 0.9 + (Math.random() * 0.2); // 0.9 to 1.1

        const projectedRevenue = avgMonthlyRevenue * revenueVariance;
        const projectedExpenses = avgMonthlyExpenses * expenseVariance;
        const projectedProfit = projectedRevenue - projectedExpenses;
        const projectedCashFlow = projectedProfit; // Simplified for now

        // Create a financial analytics record for this projection
        const periodStart = new Date(projectionDate.getFullYear(), projectionDate.getMonth(), 1);
        const periodEnd = new Date(projectionDate.getFullYear(), projectionDate.getMonth() + 1, 0);

        await FinancialAnalytics.create({
          farm_id: farmId,
          period_type: 'monthly',
          period_start: periodStart,
          period_end: periodEnd,
          revenue: 0,
          expenses: 0,
          profit: 0,
          cash_flow: 0,
          projected_revenue: projectedRevenue,
          projected_expenses: projectedExpenses,
          projected_profit: projectedProfit,
          projected_cash_flow: projectedCashFlow,
          is_projection: true,
          metrics: {
            profitMargin: projectedRevenue > 0 ? (projectedProfit / projectedRevenue) * 100 : 0
          }
        });

        projections.push({
          month: projectionDate.toLocaleString('default', { month: 'short' }),
          year: projectionDate.getFullYear(),
          projectedRevenue,
          projectedExpenses,
          projectedProfit,
          projectedCashFlow
        });
      }

      // Store in cache
      cache.set(cacheKey, projections);
      console.log(`Cached cash flow projections for farm ${farmId}`);

      return res.status(200).json({
        projections
      });
    } else {
      // Calculate average monthly revenue and expenses from recent records
      const avgMonthlyRevenue = recentRecords.reduce((sum, record) => sum + parseFloat(record.revenue), 0) / recentRecords.length;
      const avgMonthlyExpenses = recentRecords.reduce((sum, record) => sum + parseFloat(record.expenses), 0) / recentRecords.length;

      // Generate projections for the requested number of months
      const projections = [];

      for (let i = 0; i < months; i++) {
        const projectionDate = new Date(currentDate);
        projectionDate.setMonth(currentDate.getMonth() + i);

        // Add some randomness to make projections more realistic
        const revenueVariance = 0.9 + (Math.random() * 0.2); // 0.9 to 1.1
        const expenseVariance = 0.9 + (Math.random() * 0.2); // 0.9 to 1.1

        const projectedRevenue = avgMonthlyRevenue * revenueVariance;
        const projectedExpenses = avgMonthlyExpenses * expenseVariance;
        const projectedProfit = projectedRevenue - projectedExpenses;
        const projectedCashFlow = projectedProfit; // Simplified for now

        // Create a financial analytics record for this projection
        const periodStart = new Date(projectionDate.getFullYear(), projectionDate.getMonth(), 1);
        const periodEnd = new Date(projectionDate.getFullYear(), projectionDate.getMonth() + 1, 0);

        await FinancialAnalytics.create({
          farm_id: farmId,
          period_type: 'monthly',
          period_start: periodStart,
          period_end: periodEnd,
          revenue: 0,
          expenses: 0,
          profit: 0,
          cash_flow: 0,
          projected_revenue: projectedRevenue,
          projected_expenses: projectedExpenses,
          projected_profit: projectedProfit,
          projected_cash_flow: projectedCashFlow,
          is_projection: true,
          metrics: {
            profitMargin: projectedRevenue > 0 ? (projectedProfit / projectedRevenue) * 100 : 0
          }
        });

        projections.push({
          month: projectionDate.toLocaleString('default', { month: 'short' }),
          year: projectionDate.getFullYear(),
          projectedRevenue,
          projectedExpenses,
          projectedProfit,
          projectedCashFlow
        });
      }

      // Store in cache
      cache.set(cacheKey, projections);
      console.log(`Cached cash flow projections for farm ${farmId}`);

      return res.status(200).json({
        projections
      });
    }
  } catch (error) {
    console.error('Error getting cash flow projections:', error);
    return res.status(500).json({ error: 'Failed to get cash flow projections' });
  }
};
