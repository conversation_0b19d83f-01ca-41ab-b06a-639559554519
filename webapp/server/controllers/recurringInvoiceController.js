import RecurringInvoice from '../models/RecurringInvoice.js';
import Invoice from '../models/Invoice.js';
import InvoiceItem from '../models/InvoiceItem.js';
import Customer from '../models/Customer.js';
import Farm from '../models/Farm.js';
import { sequelize } from '../config/database.js';
import { Op } from 'sequelize';
import { generateNextInvoiceNumber } from './invoiceController.js';
import { sendInvoiceEmail } from '../utils/emailUtils.js';
import { addDays, addWeeks, addMonths, addYears, format, isAfter, isBefore, parseISO } from 'date-fns';

// Create a recurring schedule for an invoice
export const createRecurringInvoice = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { 
      invoiceId, 
      frequency, 
      startDate, 
      endDate, 
      dayOfMonth, 
      dayOfWeek, 
      weekOfMonth, 
      monthOfYear,
      autoSend,
      additionalRecipientEmails
    } = req.body;

    // Validate required fields
    if (!invoiceId) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Invoice ID is required' });
    }

    if (!frequency) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Frequency is required' });
    }

    if (!startDate) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Start date is required' });
    }

    // Find the invoice to ensure it exists
    const invoice = await Invoice.findByPk(invoiceId, {
      include: [
        {
          model: Customer,
          as: 'Customer',
          required: false
        },
        {
          model: Farm,
          as: 'recipientFarm',
          required: false
        }
      ]
    });

    if (!invoice) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Invoice not found' });
    }

    // Check if user has permission to create recurring schedule for this invoice
    const userFarmId = req.farmId || req.user.activeFarmId;

    if (!req.user.is_global_admin) {
      // If this is a received invoice (user's farm is the recipient), deny creating recurring schedule
      if (invoice.recipient_farm_id && invoice.recipient_farm_id === userFarmId) {
        await transaction.rollback();
        return res.status(403).json({
          error: 'You cannot create recurring schedules for invoices received from other farms.'
        });
      }

      // If this is not the user's farm's invoice, deny creating recurring schedule
      if (invoice.farm_id !== userFarmId) {
        await transaction.rollback();
        return res.status(403).json({
          error: 'You can only create recurring schedules for invoices created by your farm.'
        });
      }
    }

    // Calculate the next due date based on frequency and start date
    const nextDueDate = calculateNextDueDate(
      startDate,
      frequency,
      dayOfMonth,
      dayOfWeek,
      weekOfMonth,
      monthOfYear
    );

    // Create the recurring invoice
    const recurringInvoice = await RecurringInvoice.create({
      invoice_id: invoiceId,
      frequency,
      start_date: startDate,
      end_date: endDate || null,
      day_of_month: dayOfMonth || null,
      day_of_week: dayOfWeek || null,
      week_of_month: weekOfMonth || null,
      month_of_year: monthOfYear || null,
      next_due_date: nextDueDate,
      auto_send: autoSend || false,
      additional_recipient_emails: additionalRecipientEmails || []
    }, { transaction });

    await transaction.commit();

    return res.status(201).json({
      message: 'Recurring invoice schedule created successfully',
      recurringInvoice
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error creating recurring invoice:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get a recurring invoice by ID
export const getRecurringInvoice = async (req, res) => {
  try {
    const { recurringInvoiceId } = req.params;

    const recurringInvoice = await RecurringInvoice.findByPk(recurringInvoiceId, {
      include: [
        {
          model: Invoice,
          as: 'invoice',
          include: [
            {
              model: Customer,
              as: 'Customer',
              required: false
            },
            {
              model: Farm,
              as: 'recipientFarm',
              required: false
            }
          ]
        }
      ]
    });

    if (!recurringInvoice) {
      return res.status(404).json({ error: 'Recurring invoice not found' });
    }

    // Check if user has permission to view this recurring invoice
    const userFarmId = req.farmId || req.user.activeFarmId;

    if (!req.user.is_global_admin && recurringInvoice.invoice.farm_id !== userFarmId) {
      return res.status(403).json({
        error: 'You can only view recurring invoices for your farm.'
      });
    }

    return res.status(200).json({ recurringInvoice });
  } catch (error) {
    console.error('Error getting recurring invoice:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get all recurring invoices for a farm
export const getFarmRecurringInvoices = async (req, res) => {
  try {
    const { farmId } = req.params;

    // Find farm to ensure it exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Check if user has permission to view this farm's recurring invoices
    const userFarmId = req.farmId || req.user.activeFarmId;

    if (!req.user.is_global_admin && farmId !== userFarmId) {
      return res.status(403).json({
        error: 'You can only view recurring invoices for your farm.'
      });
    }

    // Get all recurring invoices for the farm
    const recurringInvoices = await RecurringInvoice.findAll({
      include: [
        {
          model: Invoice,
          as: 'invoice',
          where: { farm_id: farmId },
          include: [
            {
              model: Customer,
              as: 'Customer',
              required: false
            },
            {
              model: Farm,
              as: 'recipientFarm',
              required: false
            }
          ]
        }
      ]
    });

    return res.status(200).json({ recurringInvoices });
  } catch (error) {
    console.error('Error getting farm recurring invoices:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Update a recurring invoice
export const updateRecurringInvoice = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { recurringInvoiceId } = req.params;
    const { 
      frequency, 
      startDate, 
      endDate, 
      dayOfMonth, 
      dayOfWeek, 
      weekOfMonth, 
      monthOfYear,
      autoSend,
      additionalRecipientEmails
    } = req.body;

    const recurringInvoice = await RecurringInvoice.findByPk(recurringInvoiceId, {
      include: [
        {
          model: Invoice,
          as: 'invoice'
        }
      ]
    });

    if (!recurringInvoice) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Recurring invoice not found' });
    }

    // Check if user has permission to update this recurring invoice
    const userFarmId = req.farmId || req.user.activeFarmId;

    if (!req.user.is_global_admin && recurringInvoice.invoice.farm_id !== userFarmId) {
      await transaction.rollback();
      return res.status(403).json({
        error: 'You can only update recurring invoices for your farm.'
      });
    }

    // Calculate the next due date if frequency or date parameters changed
    let nextDueDate = recurringInvoice.next_due_date;
    if (frequency || startDate || dayOfMonth || dayOfWeek || weekOfMonth || monthOfYear) {
      nextDueDate = calculateNextDueDate(
        startDate || recurringInvoice.start_date,
        frequency || recurringInvoice.frequency,
        dayOfMonth !== undefined ? dayOfMonth : recurringInvoice.day_of_month,
        dayOfWeek !== undefined ? dayOfWeek : recurringInvoice.day_of_week,
        weekOfMonth !== undefined ? weekOfMonth : recurringInvoice.week_of_month,
        monthOfYear !== undefined ? monthOfYear : recurringInvoice.month_of_year
      );
    }

    // Update the recurring invoice
    await recurringInvoice.update({
      frequency: frequency || recurringInvoice.frequency,
      start_date: startDate || recurringInvoice.start_date,
      end_date: endDate !== undefined ? endDate : recurringInvoice.end_date,
      day_of_month: dayOfMonth !== undefined ? dayOfMonth : recurringInvoice.day_of_month,
      day_of_week: dayOfWeek !== undefined ? dayOfWeek : recurringInvoice.day_of_week,
      week_of_month: weekOfMonth !== undefined ? weekOfMonth : recurringInvoice.week_of_month,
      month_of_year: monthOfYear !== undefined ? monthOfYear : recurringInvoice.month_of_year,
      next_due_date: nextDueDate,
      auto_send: autoSend !== undefined ? autoSend : recurringInvoice.auto_send,
      additional_recipient_emails: additionalRecipientEmails || recurringInvoice.additional_recipient_emails
    }, { transaction });

    await transaction.commit();

    return res.status(200).json({
      message: 'Recurring invoice updated successfully',
      recurringInvoice
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error updating recurring invoice:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Delete a recurring invoice
export const deleteRecurringInvoice = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { recurringInvoiceId } = req.params;

    const recurringInvoice = await RecurringInvoice.findByPk(recurringInvoiceId, {
      include: [
        {
          model: Invoice,
          as: 'invoice'
        }
      ]
    });

    if (!recurringInvoice) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Recurring invoice not found' });
    }

    // Check if user has permission to delete this recurring invoice
    const userFarmId = req.farmId || req.user.activeFarmId;

    if (!req.user.is_global_admin && recurringInvoice.invoice.farm_id !== userFarmId) {
      await transaction.rollback();
      return res.status(403).json({
        error: 'You can only delete recurring invoices for your farm.'
      });
    }

    // Delete the recurring invoice
    await recurringInvoice.destroy({ transaction });

    await transaction.commit();

    return res.status(200).json({
      message: 'Recurring invoice deleted successfully'
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error deleting recurring invoice:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Generate invoices for all due recurring invoices
export const generateRecurringInvoices = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // Find all recurring invoices that are due today
    const dueRecurringInvoices = await RecurringInvoice.findAll({
      where: {
        next_due_date: {
          [Op.lte]: today
        },
        [Op.or]: [
          { end_date: null },
          { end_date: { [Op.gte]: today } }
        ]
      },
      include: [
        {
          model: Invoice,
          as: 'invoice',
          include: [
            {
              model: Customer,
              as: 'Customer',
              required: false
            },
            {
              model: Farm,
              as: 'recipientFarm',
              required: false
            },
            {
              model: InvoiceItem,
              as: 'InvoiceItems'
            }
          ]
        }
      ]
    });

    if (dueRecurringInvoices.length === 0) {
      await transaction.rollback();
      return res.status(200).json({
        message: 'No recurring invoices due for generation'
      });
    }

    const generatedInvoices = [];
    const errors = [];

    // Generate a new invoice for each due recurring invoice
    for (const recurringInvoice of dueRecurringInvoices) {
      try {
        const originalInvoice = recurringInvoice.invoice;
        
        // Generate the next invoice number
        const nextInvoiceNumber = await generateNextInvoiceNumber(originalInvoice.farm_id);

        // Create a new invoice based on the original
        const newInvoice = await Invoice.create({
          farm_id: originalInvoice.farm_id,
          customer_id: originalInvoice.customer_id,
          recipient_farm_id: originalInvoice.recipient_farm_id,
          invoice_number: nextInvoiceNumber,
          issue_date: format(today, 'yyyy-MM-dd'),
          due_date: calculateDueDate(today, originalInvoice.issue_date, originalInvoice.due_date),
          status: 'draft',
          subtotal: originalInvoice.subtotal,
          tax_amount: originalInvoice.tax_amount,
          total_amount: originalInvoice.total_amount,
          notes: originalInvoice.notes
        }, { transaction });

        // Create invoice items for the new invoice
        for (const item of originalInvoice.InvoiceItems) {
          await InvoiceItem.create({
            invoice_id: newInvoice.id,
            description: item.description,
            quantity: item.quantity,
            unit_price: item.unit_price,
            amount: item.amount,
            taxable: item.taxable,
            product_id: item.product_id,
            chart_of_account_id: item.chart_of_account_id
          }, { transaction });
        }

        // Auto-send the invoice if configured
        if (recurringInvoice.auto_send) {
          const farm = await Farm.findByPk(originalInvoice.farm_id);
          const customer = originalInvoice.Customer;
          const recipientFarm = originalInvoice.recipientFarm;
          
          // Determine recipient emails
          let recipientEmails = [...(recurringInvoice.additional_recipient_emails || [])];
          
          // If no additional emails are provided, use the default recipient
          if (recipientEmails.length === 0) {
            if (recipientFarm?.billing_email) {
              recipientEmails.push(recipientFarm.billing_email);
            } else if (customer?.email) {
              recipientEmails.push(customer.email);
            }
          }
          
          // Send the invoice to each recipient
          if (recipientEmails.length > 0) {
            for (const email of recipientEmails) {
              // Send the email (this would typically create an InvoiceEmail record and send the actual email)
              // For now, we'll just update the invoice status
              await newInvoice.update({
                status: 'sent'
              }, { transaction });
            }
          }
        }

        // Calculate the next due date for the recurring invoice
        const nextDueDate = calculateNextDueDate(
          recurringInvoice.start_date,
          recurringInvoice.frequency,
          recurringInvoice.day_of_month,
          recurringInvoice.day_of_week,
          recurringInvoice.week_of_month,
          recurringInvoice.month_of_year,
          today
        );

        // Update the recurring invoice with the new last generated date and next due date
        await recurringInvoice.update({
          last_generated_date: today,
          next_due_date: nextDueDate
        }, { transaction });

        generatedInvoices.push(newInvoice);
      } catch (error) {
        console.error(`Error generating invoice for recurring invoice ${recurringInvoice.id}:`, error);
        errors.push({
          recurringInvoiceId: recurringInvoice.id,
          error: error.message
        });
      }
    }

    await transaction.commit();

    return res.status(200).json({
      message: `Generated ${generatedInvoices.length} invoices from recurring schedules`,
      generatedInvoices,
      errors: errors.length > 0 ? errors : undefined
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error generating recurring invoices:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Helper function to calculate the next due date based on frequency and other parameters
const calculateNextDueDate = (startDate, frequency, dayOfMonth, dayOfWeek, weekOfMonth, monthOfYear, fromDate = null) => {
  let baseDate = fromDate ? new Date(fromDate) : new Date(startDate);
  baseDate.setHours(0, 0, 0, 0);
  
  // If we're calculating from a date other than the start date and it's before the start date,
  // use the start date as the base date
  if (fromDate && isBefore(baseDate, new Date(startDate))) {
    baseDate = new Date(startDate);
    baseDate.setHours(0, 0, 0, 0);
  }

  let nextDate;

  switch (frequency.toLowerCase()) {
    case 'daily':
      nextDate = addDays(baseDate, 1);
      break;
    
    case 'weekly':
      nextDate = addWeeks(baseDate, 1);
      // Adjust to the specified day of the week if provided
      if (dayOfWeek !== null && dayOfWeek !== undefined) {
        const currentDayOfWeek = nextDate.getDay();
        const daysToAdd = (dayOfWeek - currentDayOfWeek + 7) % 7;
        nextDate = addDays(nextDate, daysToAdd);
      }
      break;
    
    case 'monthly':
      nextDate = addMonths(baseDate, 1);
      // Adjust to the specified day of the month if provided
      if (dayOfMonth !== null && dayOfMonth !== undefined) {
        nextDate.setDate(Math.min(dayOfMonth, getDaysInMonth(nextDate.getMonth(), nextDate.getFullYear())));
      }
      break;
    
    case 'quarterly':
      nextDate = addMonths(baseDate, 3);
      // Adjust to the specified day of the month if provided
      if (dayOfMonth !== null && dayOfMonth !== undefined) {
        nextDate.setDate(Math.min(dayOfMonth, getDaysInMonth(nextDate.getMonth(), nextDate.getFullYear())));
      }
      break;
    
    case 'yearly':
      nextDate = addYears(baseDate, 1);
      // Adjust to the specified month and day if provided
      if (monthOfYear !== null && monthOfYear !== undefined) {
        nextDate.setMonth(monthOfYear - 1); // JavaScript months are 0-indexed
      }
      if (dayOfMonth !== null && dayOfMonth !== undefined) {
        nextDate.setDate(Math.min(dayOfMonth, getDaysInMonth(nextDate.getMonth(), nextDate.getFullYear())));
      }
      break;
    
    default:
      // Default to monthly if frequency is not recognized
      nextDate = addMonths(baseDate, 1);
  }

  return format(nextDate, 'yyyy-MM-dd');
};

// Helper function to get the number of days in a month
const getDaysInMonth = (month, year) => {
  return new Date(year, month + 1, 0).getDate();
};

// Helper function to calculate the due date for a new invoice based on the original invoice
const calculateDueDate = (issueDate, originalIssueDate, originalDueDate) => {
  // Calculate the number of days between the original issue date and due date
  const originalIssueDateObj = new Date(originalIssueDate);
  const originalDueDateObj = new Date(originalDueDate);
  const daysDifference = Math.round((originalDueDateObj - originalIssueDateObj) / (1000 * 60 * 60 * 24));
  
  // Add the same number of days to the new issue date
  const newDueDate = addDays(new Date(issueDate), daysDifference);
  
  return format(newDueDate, 'yyyy-MM-dd');
};