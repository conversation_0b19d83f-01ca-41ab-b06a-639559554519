import Session from '../models/Session.js';
import { Op } from 'sequelize';
import UAParser from 'ua-parser-js';
import jwt from 'jsonwebtoken';
import dotenv from 'dotenv';
import { generateFingerprint, getDeviceName } from '../services/deviceFingerprintService.js';
import axios from 'axios';

dotenv.config();

const JWT_SECRET = process.env.JWT_SECRET || 'your_jwt_secret_key';

// Helper function to parse user agent string
const parseUserAgent = (userAgentString) => {
  const parser = new UAParser(userAgentString);
  const result = parser.getResult();

  // Improved device type detection
  let deviceType = result.device.type;
  if (!deviceType) {
    // If no device type is detected, assume desktop unless it's explicitly a mobile device
    deviceType = 'desktop';

    // Check for common mobile indicators
    if (
      /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgentString) ||
      (result.device.vendor && /mobile|tablet/i.test(userAgentString))
    ) {
      deviceType = 'mobile';
    }
  }

  return {
    browser: `${result.browser.name || 'Unknown'} ${result.browser.version || ''}`.trim(),
    operating_system: `${result.os.name || 'Unknown'} ${result.os.version || ''}`.trim(),
    device_type: deviceType
  };
};

// Helper function to get the client's IP address
const getClientIp = (req) => {
  const forwardedFor = req.headers['x-forwarded-for'];
  if (forwardedFor) {
    // Get the first IP if there are multiple in the header
    return forwardedFor.split(',')[0].trim();
  }
  return req.connection.remoteAddress || 
         req.socket.remoteAddress || 
         req.connection.socket?.remoteAddress || 
         '0.0.0.0';
};

// Helper function to get location information from IP address
const getLocationFromIp = async (ipAddress) => {
  try {
    // Skip for local/private IPs
    if (ipAddress === '127.0.0.1' || ipAddress === 'localhost' || ipAddress === '0.0.0.0' || 
        ipAddress.startsWith('192.168.') || ipAddress.startsWith('10.') || ipAddress.startsWith('172.')) {
      return 'Local Network';
    }

    // Use a free IP geolocation API
    const response = await axios.get(`https://ipapi.co/${ipAddress}/json/`);

    if (response.data && response.data.city && response.data.country_name) {
      return `${response.data.city}, ${response.data.country_name}`;
    } else if (response.data && response.data.country_name) {
      return response.data.country_name;
    } else {
      return 'Unknown Location';
    }
  } catch (error) {
    console.error('Error getting location from IP:', error);
    return 'Unknown Location';
  }
};

// Create a new session
export const createSession = async (userId, token, req) => {
  try {
    const userAgent = req.headers['user-agent'];
    const ipAddress = getClientIp(req);

    // Parse user agent for device info
    const { browser, operating_system, device_type } = parseUserAgent(userAgent);

    // Generate device fingerprint
    const deviceFingerprint = generateFingerprint(req);

    // Generate user-friendly device name
    const deviceName = getDeviceName(userAgent);

    // Check if this device is already trusted
    const isTrusted = await isDeviceTrusted(userId, deviceFingerprint);

    // Calculate expiration time
    // Trusted devices never expire automatically, untrusted devices expire after 7 days
    const expiresAt = new Date();
    if (!isTrusted) {
      expiresAt.setDate(expiresAt.getDate() + 7);
    } else {
      // Set a far future date for trusted devices (effectively never expires)
      expiresAt.setFullYear(expiresAt.getFullYear() + 100);
    }

    // Get location information from IP address
    const location = await getLocationFromIp(ipAddress);

    // Create session record
    const session = await Session.create({
      user_id: userId,
      token,
      ip_address: ipAddress,
      user_agent: userAgent,
      device_type,
      browser,
      operating_system,
      device_fingerprint: deviceFingerprint,
      device_name: deviceName,
      is_trusted: isTrusted, // Set trusted status based on device recognition
      location, // Set location based on IP geolocation
      expires_at: expiresAt,
      last_active_at: new Date()
    });

    return session;
  } catch (error) {
    console.error('Error creating session:', error);
    throw error;
  }
};

// Get all active sessions for a user
export const getUserSessions = async (req, res) => {
  try {
    const userId = req.user.id;
    const currentDate = new Date();

    // Get all trusted sessions (regardless of expiration or active status)
    const trustedSessions = await Session.findAll({
      where: {
        user_id: userId,
        is_trusted: true
      },
      order: [['last_active_at', 'DESC']]
    });

    // Get all non-trusted sessions that haven't expired and are active
    const nonTrustedSessions = await Session.findAll({
      where: {
        user_id: userId,
        is_active: true,
        is_trusted: false,
        expires_at: {
          [Op.gt]: currentDate
        }
      },
      order: [['last_active_at', 'DESC']]
    });

    // Combine the results
    const sessions = [...trustedSessions, ...nonTrustedSessions];

    // Sort by last_active_at
    sessions.sort((a, b) => new Date(b.last_active_at) - new Date(a.last_active_at));

    return res.status(200).json(sessions);
  } catch (error) {
    console.error('Error fetching user sessions:', error);
    return res.status(500).json({ error: 'Failed to fetch sessions' });
  }
};

// Terminate a specific session
export const terminateSession = async (req, res) => {
  try {
    const { sessionId } = req.params;
    const userId = req.user.id;

    // Find the session
    const session = await Session.findOne({
      where: {
        id: sessionId,
        user_id: userId
      }
    });

    if (!session) {
      return res.status(404).json({ error: 'Session not found' });
    }

    // Update session to inactive
    session.is_active = false;
    await session.save();

    return res.status(200).json({ message: 'Session terminated successfully' });
  } catch (error) {
    console.error('Error terminating session:', error);
    return res.status(500).json({ error: 'Failed to terminate session' });
  }
};

// Terminate all sessions for a user except the current one
export const terminateAllSessions = async (req, res) => {
  try {
    const userId = req.user.id;
    const currentToken = req.headers.authorization.split(' ')[1];

    // Update all sessions except current one to inactive
    await Session.update(
      { is_active: false },
      {
        where: {
          user_id: userId,
          is_active: true,
          token: {
            [Op.ne]: currentToken
          }
        }
      }
    );

    return res.status(200).json({ message: 'All other sessions terminated successfully' });
  } catch (error) {
    console.error('Error terminating all sessions:', error);
    return res.status(500).json({ error: 'Failed to terminate sessions' });
  }
};

// Update session last active timestamp
export const updateSessionActivity = async (token) => {
  try {
    await Session.update(
      { last_active_at: new Date() },
      {
        where: {
          token,
          is_active: true
        }
      }
    );
  } catch (error) {
    console.error('Error updating session activity:', error);
  }
};

// Terminate a session by token
export const terminateSessionByToken = async (token) => {
  try {
    await Session.update(
      { is_active: false },
      {
        where: {
          token,
          is_active: true
        }
      }
    );
  } catch (error) {
    console.error('Error terminating session by token:', error);
  }
};

// Check if a device is recognized for a user
export const isDeviceRecognized = async (userId, deviceFingerprint) => {
  try {
    // Look for any active or inactive session with this device fingerprint
    const existingSession = await Session.findOne({
      where: {
        user_id: userId,
        device_fingerprint: deviceFingerprint
      }
    });

    return !!existingSession;
  } catch (error) {
    console.error('Error checking if device is recognized:', error);
    return false;
  }
};

// Check if a device is trusted for a user
export const isDeviceTrusted = async (userId, deviceFingerprint) => {
  try {
    // Look for any active session with this device fingerprint that is marked as trusted
    const trustedSession = await Session.findOne({
      where: {
        user_id: userId,
        device_fingerprint: deviceFingerprint,
        is_trusted: true
      }
    });

    return !!trustedSession;
  } catch (error) {
    console.error('Error checking if device is trusted:', error);
    return false;
  }
};

// Mark a device as trusted
export const trustDevice = async (userId, deviceFingerprint) => {
  try {
    // Update all sessions with this device fingerprint to be trusted
    await Session.update(
      { is_trusted: true },
      {
        where: {
          user_id: userId,
          device_fingerprint: deviceFingerprint
        }
      }
    );
    return true;
  } catch (error) {
    console.error('Error marking device as trusted:', error);
    return false;
  }
};

// Mark a device as untrusted
export const untrustDevice = async (userId, deviceFingerprint) => {
  try {
    // Update all sessions with this device fingerprint to be untrusted
    await Session.update(
      { is_trusted: false },
      {
        where: {
          user_id: userId,
          device_fingerprint: deviceFingerprint
        }
      }
    );
    return true;
  } catch (error) {
    console.error('Error marking device as untrusted:', error);
    return false;
  }
};
