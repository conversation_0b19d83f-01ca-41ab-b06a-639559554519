import { sequelize } from '../config/database.js';
import DigitalCertificate from '../models/DigitalCertificate.js';
import User from '../models/User.js';
import Farm from '../models/Farm.js';
import { checkUserFarmAccess } from './documentSigningController.js';
import { createDigitalCertificate, getFarmDigitalCertificates, getUserDigitalCertificates } from '../utils/digitalSignatureUtils.js';

/**
 * Get all digital certificates for a farm
 */
export const getAllDigitalCertificates = async (req, res) => {
  try {
    const { farmId } = req.params;

    // Validate farm access
    const hasAccess = await checkUserFarmAccess(req.user.id, farmId);
    if (!hasAccess && !req.user.is_global_admin) {
      return res.status(403).json({ error: 'You do not have access to this farm' });
    }

    const certificates = await getFarmDigitalCertificates(farmId);

    return res.status(200).json({
      certificates
    });
  } catch (error) {
    console.error('Error getting digital certificates:', error);
    return res.status(500).json({ error: error.message });
  }
};

/**
 * Get a digital certificate by ID
 */
export const getDigitalCertificateById = async (req, res) => {
  try {
    const { id } = req.params;

    const certificate = await DigitalCertificate.findByPk(id, {
      attributes: {
        exclude: ['private_key', 'certificate_data', 'passphrase']
      },
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'first_name', 'last_name', 'email']
        },
        {
          model: Farm,
          as: 'farm',
          attributes: ['id', 'name']
        }
      ]
    });

    if (!certificate) {
      return res.status(404).json({ error: 'Certificate not found' });
    }

    // Validate farm access
    const hasAccess = await checkUserFarmAccess(req.user.id, certificate.farm_id);
    if (!hasAccess && !req.user.is_global_admin) {
      return res.status(403).json({ error: 'You do not have access to this certificate' });
    }

    return res.status(200).json(certificate);
  } catch (error) {
    console.error('Error getting digital certificate:', error);
    return res.status(500).json({ error: error.message });
  }
};

/**
 * Create a new digital certificate
 */
export const createNewDigitalCertificate = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { farmId } = req.params;
    const {
      certificateName,
      certificateType,
      issuer,
      subject,
      validFrom,
      validTo,
      serialNumber,
      passphrase
    } = req.body;

    // Validate farm access
    const hasAccess = await checkUserFarmAccess(req.user.id, farmId);
    if (!hasAccess && !req.user.is_global_admin) {
      await transaction.rollback();
      return res.status(403).json({ error: 'You do not have access to this farm' });
    }

    // Check if certificate file was uploaded
    if (!req.files || !req.files.certificate) {
      await transaction.rollback();
      return res.status(400).json({ error: 'No certificate file uploaded' });
    }

    // Check if private key file was uploaded
    if (!req.files || !req.files.privateKey) {
      await transaction.rollback();
      return res.status(400).json({ error: 'No private key file uploaded' });
    }

    const certificateFile = req.files.certificate;
    const privateKeyFile = req.files.privateKey;

    // Create certificate record
    const certificateData = {
      farm_id: farmId,
      user_id: req.user.id,
      certificate_name: certificateName,
      certificate_data: certificateFile.data,
      private_key: privateKeyFile.data,
      passphrase: passphrase || null,
      certificate_type: certificateType,
      issuer,
      subject,
      valid_from: validFrom,
      valid_to: validTo,
      serial_number: serialNumber,
      is_active: true
    };

    const certificate = await createDigitalCertificate(certificateData);

    await transaction.commit();

    // Return certificate without sensitive data
    const { private_key, certificate_data, passphrase: _, ...certificateWithoutSensitiveData } = certificate.toJSON();

    return res.status(201).json({
      message: 'Certificate created successfully',
      certificate: certificateWithoutSensitiveData
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error creating digital certificate:', error);
    return res.status(500).json({ error: error.message });
  }
};

/**
 * Update a digital certificate
 */
export const updateDigitalCertificate = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { id } = req.params;
    const { certificateName, isActive } = req.body;

    // Get certificate
    const certificate = await DigitalCertificate.findByPk(id);

    if (!certificate) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Certificate not found' });
    }

    // Validate farm access
    const hasAccess = await checkUserFarmAccess(req.user.id, certificate.farm_id);
    if (!hasAccess && !req.user.is_global_admin) {
      await transaction.rollback();
      return res.status(403).json({ error: 'You do not have access to this certificate' });
    }

    // Update certificate
    const updates = {};

    if (certificateName) updates.certificate_name = certificateName;
    if (isActive !== undefined) updates.is_active = isActive;

    await certificate.update(updates, { transaction });

    await transaction.commit();

    return res.status(200).json({
      message: 'Certificate updated successfully',
      certificate: {
        ...certificate.toJSON(),
        private_key: undefined,
        certificate_data: undefined,
        passphrase: undefined
      }
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error updating digital certificate:', error);
    return res.status(500).json({ error: error.message });
  }
};

/**
 * Delete a digital certificate
 */
export const deleteDigitalCertificate = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { id } = req.params;

    // Get certificate
    const certificate = await DigitalCertificate.findByPk(id);

    if (!certificate) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Certificate not found' });
    }

    // Validate farm access
    const hasAccess = await checkUserFarmAccess(req.user.id, certificate.farm_id);
    if (!hasAccess && !req.user.is_global_admin) {
      await transaction.rollback();
      return res.status(403).json({ error: 'You do not have access to this certificate' });
    }

    // Delete certificate
    await certificate.destroy({ transaction });

    await transaction.commit();

    return res.status(200).json({
      message: 'Certificate deleted successfully'
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error deleting digital certificate:', error);
    return res.status(500).json({ error: error.message });
  }
};

/**
 * Get all digital certificates for the current user
 */
export const getUserCertificates = async (req, res) => {
  try {
    const userId = req.user.id;

    const certificates = await getUserDigitalCertificates(userId);

    return res.status(200).json({
      certificates
    });
  } catch (error) {
    console.error('Error getting user digital certificates:', error);
    return res.status(500).json({ error: error.message });
  }
};