import InventoryItem from '../models/InventoryItem.js';
import InventoryCategory from '../models/InventoryCategory.js';
import { Op, Sequelize } from 'sequelize';
import { v4 as uuidv4 } from 'uuid';
import QRCode from 'qrcode';
import { sequelize } from '../config/database.js';

// Get all inventory items for a farm
export const getInventoryItems = async (req, res) => {
  try {
    const { farmId, categoryId, search, lowStock } = req.query;

    if (!farmId) {
      return res.status(400).json({ message: 'Farm ID is required' });
    }

    const whereClause = { farm_id: farmId };

    if (categoryId) {
      whereClause.category_id = categoryId;
    }

    if (search) {
      whereClause[Op.or] = [
        { name: { [Op.iLike]: `%${search}%` } },
        { description: { [Op.iLike]: `%${search}%` } },
        { barcode: { [Op.iLike]: `%${search}%` } },
        { qr_code: { [Op.iLike]: `%${search}%` } }
      ];
    }

    if (lowStock === 'true') {
      whereClause[Op.and] = [
        { reorder_point: { [Op.ne]: null } },
        { quantity: { [Op.lte]: sequelize.col('reorder_point') } }
      ];
    }

    const items = await InventoryItem.findAll({
      where: whereClause,
      include: [
        {
          model: InventoryCategory,
          as: 'category',
          attributes: ['id', 'name']
        }
      ],
      order: [['name', 'ASC']]
    });

    res.status(200).json(items);
  } catch (error) {
    console.error('Error fetching inventory items:', error);
    res.status(500).json({ message: 'Failed to fetch inventory items', error: error.message });
  }
};

// Get a single inventory item by ID
export const getInventoryItem = async (req, res) => {
  try {
    const { id } = req.params;

    const item = await InventoryItem.findByPk(id, {
      include: [
        {
          model: InventoryCategory,
          as: 'category',
          attributes: ['id', 'name']
        }
      ]
    });

    if (!item) {
      return res.status(404).json({ message: 'Inventory item not found' });
    }

    res.status(200).json(item);
  } catch (error) {
    console.error('Error fetching inventory item:', error);
    res.status(500).json({ message: 'Failed to fetch inventory item', error: error.message });
  }
};

// Get inventory item by barcode
export const getInventoryItemByBarcode = async (req, res) => {
  try {
    const { barcode } = req.params;

    const item = await InventoryItem.findOne({
      where: { barcode },
      include: [
        {
          model: InventoryCategory,
          as: 'category',
          attributes: ['id', 'name']
        }
      ]
    });

    if (!item) {
      return res.status(404).json({ message: 'Inventory item not found' });
    }

    res.status(200).json(item);
  } catch (error) {
    console.error('Error fetching inventory item by barcode:', error);
    res.status(500).json({ message: 'Failed to fetch inventory item', error: error.message });
  }
};

// Get inventory item by QR code
export const getInventoryItemByQRCode = async (req, res) => {
  try {
    const { qrCode } = req.params;

    const item = await InventoryItem.findOne({
      where: { qr_code: qrCode },
      include: [
        {
          model: InventoryCategory,
          as: 'category',
          attributes: ['id', 'name']
        }
      ]
    });

    if (!item) {
      return res.status(404).json({ message: 'Inventory item not found' });
    }

    res.status(200).json(item);
  } catch (error) {
    console.error('Error fetching inventory item by QR code:', error);
    res.status(500).json({ message: 'Failed to fetch inventory item', error: error.message });
  }
};

// Create a new inventory item
export const createInventoryItem = async (req, res) => {
  try {
    const { 
      farm_id, 
      category_id, 
      name, 
      description, 
      quantity, 
      unit, 
      unit_price, 
      reorder_point, 
      location,
      barcode,
      generate_qr_code
    } = req.body;

    if (!farm_id || !name) {
      return res.status(400).json({ message: 'Farm ID and name are required' });
    }

    // Generate a unique barcode if not provided
    const finalBarcode = barcode || `INV-${uuidv4().substring(0, 8)}`;

    // Generate QR code if requested
    let qrCodeData = null;
    if (generate_qr_code) {
      const qrCodeValue = `INV-QR-${uuidv4().substring(0, 8)}`;
      qrCodeData = qrCodeValue;
    }

    const newItem = await InventoryItem.create({
      farm_id,
      category_id: category_id || null,
      name,
      description,
      quantity: quantity || 0,
      unit,
      unit_price,
      reorder_point,
      location,
      barcode: finalBarcode,
      qr_code: qrCodeData
    });

    res.status(201).json(newItem);
  } catch (error) {
    console.error('Error creating inventory item:', error);
    res.status(500).json({ message: 'Failed to create inventory item', error: error.message });
  }
};

// Update an inventory item
export const updateInventoryItem = async (req, res) => {
  try {
    const { id } = req.params;
    const { 
      category_id, 
      name, 
      description, 
      quantity, 
      unit, 
      unit_price, 
      reorder_point, 
      location,
      barcode,
      generate_qr_code
    } = req.body;

    const item = await InventoryItem.findByPk(id);

    if (!item) {
      return res.status(404).json({ message: 'Inventory item not found' });
    }

    // Generate QR code if requested and not already present
    let qrCodeData = item.qr_code;
    if (generate_qr_code && !item.qr_code) {
      const qrCodeValue = `INV-QR-${uuidv4().substring(0, 8)}`;
      qrCodeData = qrCodeValue;
    }

    // Update the item
    await item.update({
      category_id: category_id !== undefined ? category_id : item.category_id,
      name: name || item.name,
      description: description !== undefined ? description : item.description,
      quantity: quantity !== undefined ? quantity : item.quantity,
      unit: unit !== undefined ? unit : item.unit,
      unit_price: unit_price !== undefined ? unit_price : item.unit_price,
      reorder_point: reorder_point !== undefined ? reorder_point : item.reorder_point,
      location: location !== undefined ? location : item.location,
      barcode: barcode || item.barcode,
      qr_code: qrCodeData,
      updated_at: new Date()
    });

    res.status(200).json(item);
  } catch (error) {
    console.error('Error updating inventory item:', error);
    res.status(500).json({ message: 'Failed to update inventory item', error: error.message });
  }
};

// Delete an inventory item
export const deleteInventoryItem = async (req, res) => {
  try {
    const { id } = req.params;

    const item = await InventoryItem.findByPk(id);

    if (!item) {
      return res.status(404).json({ message: 'Inventory item not found' });
    }

    await item.destroy();

    res.status(200).json({ message: 'Inventory item deleted successfully' });
  } catch (error) {
    console.error('Error deleting inventory item:', error);
    res.status(500).json({ message: 'Failed to delete inventory item', error: error.message });
  }
};

// Generate QR code for an inventory item
export const generateQRCode = async (req, res) => {
  try {
    const { id } = req.params;

    const item = await InventoryItem.findByPk(id);

    if (!item) {
      return res.status(404).json({ message: 'Inventory item not found' });
    }

    // Generate a new QR code value
    const qrCodeValue = `INV-QR-${uuidv4().substring(0, 8)}`;

    // Update the item with the new QR code
    await item.update({
      qr_code: qrCodeValue,
      updated_at: new Date()
    });

    // Generate QR code image
    const qrCodeImage = await QRCode.toDataURL(qrCodeValue);

    res.status(200).json({ 
      qr_code: qrCodeValue,
      qr_code_image: qrCodeImage
    });
  } catch (error) {
    console.error('Error generating QR code:', error);
    res.status(500).json({ message: 'Failed to generate QR code', error: error.message });
  }
};
