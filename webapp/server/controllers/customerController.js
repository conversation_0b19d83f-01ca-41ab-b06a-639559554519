import Customer from '../models/Customer.js';
import Farm from '../models/Farm.js';
import Invoice from '../models/Invoice.js';
import { sequelize } from '../config/database.js';
import phoneBookService from '../services/phoneBookService.js';

// Get all customers for a farm
export const getFarmCustomers = async (req, res) => {
  try {
    const { farmId } = req.params;

    // Find farm to ensure it exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Get all customers for the farm
    const customers = await Customer.findAll({
      where: { farm_id: farmId },
      order: [['name', 'ASC']]
    });

    return res.status(200).json({ customers });
  } catch (error) {
    console.error('Error getting farm customers:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get a single customer by ID
export const getCustomerById = async (req, res) => {
  try {
    const { customerId } = req.params;

    let customer;
    try {
      customer = await Customer.findByPk(customerId, {
        include: [
          {
            model: Invoice,
            as: 'invoices',
            required: false,
            attributes: ['id', 'invoice_number', 'issue_date', 'due_date', 'status', 'total_amount'],
            order: [['issue_date', 'DESC']]
          }
        ]
      });
    } catch (associationError) {
      console.error('Association error in getCustomerById:', associationError.message);

      // Fallback: Get customer without associations and manually fetch invoices
      customer = await Customer.findByPk(customerId);

      if (customer) {
        try {
          const invoices = await Invoice.findAll({
            where: { customer_id: customerId },
            attributes: ['id', 'invoice_number', 'issue_date', 'due_date', 'status', 'total_amount'],
            order: [['issue_date', 'DESC']]
          });
          customer.invoices = invoices;
        } catch (error) {
          console.error('Error fetching customer invoices:', error);
          customer.invoices = [];
        }
      }
    }

    if (!customer) {
      return res.status(404).json({ error: 'Customer not found' });
    }

    return res.status(200).json({ customer });
  } catch (error) {
    console.error('Error getting customer:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Create a new customer
export const createCustomer = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { 
      farmId, 
      name, 
      contactName, 
      email, 
      phone, 
      address, 
      city, 
      state, 
      zipCode, 
      country, 
      taxId, 
      notes,
      is_tax_exempt
    } = req.body;

    // Validate required fields
    if (!farmId) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Farm ID is required' });
    }

    if (!name) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Customer name is required' });
    }

    // Find farm to ensure it exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Create customer
    const customer = await Customer.create({
      farm_id: farmId,
      name,
      contact_name: contactName,
      email,
      phone,
      address,
      city,
      state,
      zip_code: zipCode,
      country,
      tax_id: taxId,
      is_tax_exempt: is_tax_exempt !== undefined ? is_tax_exempt : false,
      notes
    }, { transaction });

    await transaction.commit();

    return res.status(201).json({ 
      message: 'Customer created successfully',
      customer 
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error creating customer:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Update a customer
export const updateCustomer = async (req, res) => {
  try {
    const { customerId } = req.params;
    const { 
      name, 
      contactName, 
      email, 
      phone, 
      address, 
      city, 
      state, 
      zipCode, 
      country, 
      taxId, 
      notes,
      is_tax_exempt
    } = req.body;

    // Find customer to ensure it exists
    const customer = await Customer.findByPk(customerId);
    if (!customer) {
      return res.status(404).json({ error: 'Customer not found' });
    }

    // Update customer
    await customer.update({
      name: name || customer.name,
      contact_name: contactName !== undefined ? contactName : customer.contact_name,
      email: email !== undefined ? email : customer.email,
      phone: phone !== undefined ? phone : customer.phone,
      address: address !== undefined ? address : customer.address,
      city: city !== undefined ? city : customer.city,
      state: state !== undefined ? state : customer.state,
      zip_code: zipCode !== undefined ? zipCode : customer.zip_code,
      country: country || customer.country,
      tax_id: taxId !== undefined ? taxId : customer.tax_id,
      is_tax_exempt: is_tax_exempt !== undefined ? is_tax_exempt : customer.is_tax_exempt,
      notes: notes !== undefined ? notes : customer.notes
    });

    return res.status(200).json({ 
      message: 'Customer updated successfully',
      customer 
    });
  } catch (error) {
    console.error('Error updating customer:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Delete a customer
export const deleteCustomer = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { customerId } = req.params;

    // Find customer to ensure it exists
    const customer = await Customer.findByPk(customerId);
    if (!customer) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Customer not found' });
    }

    // Check if customer has invoices
    const invoiceCount = await Invoice.count({
      where: { customer_id: customerId }
    });

    if (invoiceCount > 0) {
      await transaction.rollback();
      return res.status(400).json({ 
        error: 'Cannot delete customer with associated invoices',
        invoiceCount
      });
    }

    // If customer is subscribed to phone book, unsubscribe first
    if (customer.phone_book_subscription) {
      try {
        await phoneBookService.unsubscribeCustomer(customerId);
      } catch (phoneBookError) {
        console.error('Error unsubscribing customer from phone book:', phoneBookError);
        // Continue with deletion even if unsubscribe fails
      }
    }

    // Delete customer
    await customer.destroy({ transaction });

    await transaction.commit();

    return res.status(200).json({ 
      message: 'Customer deleted successfully' 
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error deleting customer:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Subscribe a customer to phone book updates
export const subscribeToPhoneBook = async (req, res) => {
  try {
    const { customerId } = req.params;
    const { ios, android } = req.body;

    // Validate that at least one platform is selected
    if (!ios && !android) {
      return res.status(400).json({ 
        error: 'At least one platform (iOS or Android) must be selected for phone book subscription' 
      });
    }

    const customer = await phoneBookService.subscribeCustomer(customerId, { ios, android });

    return res.status(200).json({
      message: 'Customer subscribed to phone book updates successfully',
      customer
    });
  } catch (error) {
    console.error('Error subscribing customer to phone book:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Unsubscribe a customer from phone book updates
export const unsubscribeFromPhoneBook = async (req, res) => {
  try {
    const { customerId } = req.params;

    const customer = await phoneBookService.unsubscribeCustomer(customerId);

    return res.status(200).json({
      message: 'Customer unsubscribed from phone book updates successfully',
      customer
    });
  } catch (error) {
    console.error('Error unsubscribing customer from phone book:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Sync a customer with phone books
export const syncCustomerWithPhoneBook = async (req, res) => {
  try {
    const { customerId } = req.params;

    // Find customer to ensure it exists and is subscribed
    const customer = await Customer.findByPk(customerId);

    if (!customer) {
      return res.status(404).json({ error: 'Customer not found' });
    }

    if (!customer.phone_book_subscription) {
      return res.status(400).json({ 
        error: 'Customer is not subscribed to phone book updates' 
      });
    }

    const updatedCustomer = await phoneBookService.syncCustomerToPhoneBook(customer);

    return res.status(200).json({
      message: 'Customer synced with phone book successfully',
      customer: updatedCustomer
    });
  } catch (error) {
    console.error('Error syncing customer with phone book:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Handle webhook for phone book updates (two-way sync)
export const handlePhoneBookWebhook = async (req, res) => {
  try {
    const { externalId, source, ...contactData } = req.body;

    if (!externalId || !source) {
      return res.status(400).json({ 
        error: 'External ID and source are required' 
      });
    }

    // Validate source
    if (source !== 'ios' && source !== 'android') {
      return res.status(400).json({ 
        error: 'Invalid source. Must be "ios" or "android"' 
      });
    }

    const customer = await phoneBookService.handlePhoneBookUpdate({
      externalId,
      ...contactData
    });

    return res.status(200).json({
      message: 'Phone book update processed successfully',
      customer
    });
  } catch (error) {
    console.error('Error handling phone book webhook:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Sync all subscribed customers with phone books
export const syncAllCustomers = async (req, res) => {
  try {
    const results = await phoneBookService.syncAllCustomers();

    return res.status(200).json({
      message: 'All customers synced with phone books',
      results
    });
  } catch (error) {
    console.error('Error syncing all customers with phone books:', error);
    return res.status(500).json({ error: error.message });
  }
};
