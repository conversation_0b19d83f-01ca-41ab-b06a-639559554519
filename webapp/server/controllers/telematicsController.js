import EquipmentTelematics from '../models/EquipmentTelematics.js';
import Equipment from '../models/Equipment.js';
import { sequelize } from '../config/database.js';
import { Op } from 'sequelize';

// Get telematics data for a specific equipment item
export const getEquipmentTelematics = async (req, res) => {
  try {
    const { equipmentId } = req.params;
    const { startDate, endDate, limit = 100, offset = 0 } = req.query;

    // Find equipment to ensure it exists
    const equipment = await Equipment.findByPk(equipmentId);
    if (!equipment) {
      return res.status(404).json({ error: 'Equipment not found' });
    }

    // Build query conditions
    const whereConditions = { equipment_id: equipmentId };

    // Add date range filter if provided
    if (startDate && endDate) {
      whereConditions.timestamp = {
        [Op.between]: [new Date(startDate), new Date(endDate)]
      };
    } else if (startDate) {
      whereConditions.timestamp = {
        [Op.gte]: new Date(startDate)
      };
    } else if (endDate) {
      whereConditions.timestamp = {
        [Op.lte]: new Date(endDate)
      };
    }

    // Get telematics data with pagination
    const telematics = await EquipmentTelematics.findAndCountAll({
      where: whereConditions,
      order: [['timestamp', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

    return res.status(200).json({
      telematics: telematics.rows,
      total: telematics.count,
      limit: parseInt(limit),
      offset: parseInt(offset)
    });
  } catch (error) {
    console.error('Error getting equipment telematics:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get the latest telematics data for an equipment item
export const getLatestTelematics = async (req, res) => {
  try {
    const { equipmentId } = req.params;

    // Find equipment to ensure it exists
    const equipment = await Equipment.findByPk(equipmentId);
    if (!equipment) {
      return res.status(404).json({ error: 'Equipment not found' });
    }

    // Get the latest telematics data
    const latestTelematics = await EquipmentTelematics.findOne({
      where: { equipment_id: equipmentId },
      order: [['timestamp', 'DESC']]
    });

    if (!latestTelematics) {
      return res.status(404).json({ error: 'No telematics data found for this equipment' });
    }

    return res.status(200).json({ telematics: latestTelematics });
  } catch (error) {
    console.error('Error getting latest telematics:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Create new telematics data record
export const createTelematics = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { 
      equipmentId, 
      timestamp,
      latitude,
      longitude,
      altitude,
      engineHours,
      odometer,
      fuelLevel,
      fuelConsumptionRate,
      engineRpm,
      engineLoad,
      engineTemperature,
      diagnosticCodes,
      operationalStatus,
      rawData
    } = req.body;

    // Validate required fields
    if (!equipmentId) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Equipment ID is required' });
    }

    // Find equipment to ensure it exists
    const equipment = await Equipment.findByPk(equipmentId);
    if (!equipment) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Equipment not found' });
    }

    // Create telematics record
    const telematics = await EquipmentTelematics.create({
      equipment_id: equipmentId,
      timestamp: timestamp || new Date(),
      latitude,
      longitude,
      altitude,
      engine_hours: engineHours,
      odometer,
      fuel_level: fuelLevel,
      fuel_consumption_rate: fuelConsumptionRate,
      engine_rpm: engineRpm,
      engine_load: engineLoad,
      engine_temperature: engineTemperature,
      diagnostic_codes: diagnosticCodes,
      operational_status: operationalStatus,
      raw_data: rawData
    }, { transaction });

    await transaction.commit();

    return res.status(201).json({
      message: 'Telematics data created successfully',
      telematics
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error creating telematics data:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get aggregated telematics data for reporting
export const getAggregatedTelematics = async (req, res) => {
  try {
    const { equipmentId } = req.params;
    const { startDate, endDate, interval = 'day' } = req.query;
    const schema = process.env.DB_SCHEMA || 'site';

    // Find equipment to ensure it exists
    const equipment = await Equipment.findByPk(equipmentId);
    if (!equipment) {
      return res.status(404).json({ error: 'Equipment not found' });
    }

    // Validate dates
    if (!startDate || !endDate) {
      return res.status(400).json({ error: 'Start date and end date are required' });
    }

    // Define time interval for aggregation
    let timeFormat;
    switch (interval) {
      case 'hour':
        timeFormat = 'YYYY-MM-DD HH24';
        break;
      case 'day':
        timeFormat = 'YYYY-MM-DD';
        break;
      case 'week':
        timeFormat = 'YYYY-WW';
        break;
      case 'month':
        timeFormat = 'YYYY-MM';
        break;
      default:
        timeFormat = 'YYYY-MM-DD';
    }

    // Get aggregated data
    const aggregatedData = await sequelize.query(`
      SELECT 
        TO_CHAR(timestamp, '${timeFormat}') as time_period,
        AVG(engine_hours) as avg_engine_hours,
        MAX(engine_hours) - MIN(engine_hours) as engine_hours_change,
        AVG(odometer) as avg_odometer,
        MAX(odometer) - MIN(odometer) as odometer_change,
        AVG(fuel_level) as avg_fuel_level,
        AVG(fuel_consumption_rate) as avg_fuel_consumption,
        AVG(engine_rpm) as avg_engine_rpm,
        AVG(engine_load) as avg_engine_load,
        AVG(engine_temperature) as avg_engine_temp,
        COUNT(*) as data_points
      FROM ${schema}.equipment_telematics
      WHERE equipment_id = :equipmentId
        AND timestamp BETWEEN :startDate AND :endDate
      GROUP BY time_period
      ORDER BY time_period
    `, {
      replacements: { 
        equipmentId, 
        startDate: new Date(startDate), 
        endDate: new Date(endDate),
        schema: process.env.DB_SCHEMA || 'site'
      },
      type: sequelize.QueryTypes.SELECT
    });

    return res.status(200).json({ aggregatedData });
  } catch (error) {
    console.error('Error getting aggregated telematics:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Receive telematics data from external systems
export const receiveExternalTelematics = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { 
      apiKey,
      equipmentIdentifier,
      telematicsData
    } = req.body;

    // Validate API key (in a real system, this would be more secure)
    if (!apiKey || apiKey !== process.env.TELEMATICS_API_KEY) {
      await transaction.rollback();
      return res.status(401).json({ error: 'Invalid API key' });
    }

    // Validate required fields
    if (!equipmentIdentifier || !telematicsData) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Equipment identifier and telematics data are required' });
    }

    // Find equipment by identifier (could be serial number, model number, etc.)
    const equipment = await Equipment.findOne({
      where: {
        [Op.or]: [
          { id: equipmentIdentifier },
          { name: equipmentIdentifier },
          { model: equipmentIdentifier }
        ]
      }
    });

    if (!equipment) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Equipment not found' });
    }

    // Process and store the telematics data
    const telematics = await EquipmentTelematics.create({
      equipment_id: equipment.id,
      timestamp: telematicsData.timestamp || new Date(),
      latitude: telematicsData.latitude,
      longitude: telematicsData.longitude,
      altitude: telematicsData.altitude,
      engine_hours: telematicsData.engineHours,
      odometer: telematicsData.odometer,
      fuel_level: telematicsData.fuelLevel,
      fuel_consumption_rate: telematicsData.fuelConsumptionRate,
      engine_rpm: telematicsData.engineRpm,
      engine_load: telematicsData.engineLoad,
      engine_temperature: telematicsData.engineTemperature,
      diagnostic_codes: telematicsData.diagnosticCodes,
      operational_status: telematicsData.operationalStatus,
      raw_data: telematicsData
    }, { transaction });

    await transaction.commit();

    return res.status(201).json({
      message: 'Telematics data received and processed successfully',
      telematicsId: telematics.id
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error processing external telematics data:', error);
    return res.status(500).json({ error: error.message });
  }
};
