import db from '../db.js';
import { validationResult } from 'express-validator';

// Seasonal Worker Management
export const createSeasonalWorker = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { 
      farmId, 
      firstName, 
      lastName, 
      contactInfo, 
      startDate, 
      endDate, 
      position, 
      status, 
      documentationStatus, 
      notes 
    } = req.body;

    const result = await db.query(
      `INSERT INTO seasonal_workers (
        farm_id, 
        first_name, 
        last_name, 
        contact_info, 
        start_date, 
        end_date, 
        position, 
        status, 
        documentation_status, 
        notes
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10) RETURNING *`,
      [
        farmId, 
        firstName, 
        lastName, 
        contactInfo, 
        startDate, 
        endDate, 
        position, 
        status, 
        documentationStatus, 
        notes
      ]
    );

    res.status(201).json(result.rows[0]);
  } catch (error) {
    console.error('Error creating seasonal worker:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

export const getSeasonalWorkers = async (req, res) => {
  try {
    const { farmId } = req.query;

    if (!farmId) {
      return res.status(400).json({ message: 'Farm ID is required' });
    }

    // Check if farmId is a valid number
    const farmIdNum = parseInt(farmId);
    if (isNaN(farmIdNum)) {
      return res.status(404).json({ 
        message: 'The requested resource could not be found',
        errorType: 'NotFoundError',
        errorCode: '404',
        context: {
          url: req.originalUrl,
          method: req.method
        }
      });
    }

    const result = await db.query(
      'SELECT * FROM seasonal_workers WHERE farm_id = $1 ORDER BY last_name, first_name',
      [farmIdNum]
    );

    res.json(result.rows);
  } catch (error) {
    console.error('Error getting seasonal workers:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

export const getSeasonalWorker = async (req, res) => {
  try {
    const { id } = req.params;

    const result = await db.query(
      'SELECT * FROM seasonal_workers WHERE id = $1',
      [id]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ message: 'Seasonal worker not found' });
    }

    res.json(result.rows[0]);
  } catch (error) {
    console.error('Error getting seasonal worker:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

export const updateSeasonalWorker = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { id } = req.params;
    const { 
      firstName, 
      lastName, 
      contactInfo, 
      startDate, 
      endDate, 
      position, 
      status, 
      documentationStatus, 
      notes 
    } = req.body;

    const result = await db.query(
      `UPDATE seasonal_workers SET 
        first_name = $1, 
        last_name = $2, 
        contact_info = $3, 
        start_date = $4, 
        end_date = $5, 
        position = $6, 
        status = $7, 
        documentation_status = $8, 
        notes = $9
      WHERE id = $10 RETURNING *`,
      [
        firstName, 
        lastName, 
        contactInfo, 
        startDate, 
        endDate, 
        position, 
        status, 
        documentationStatus, 
        notes,
        id
      ]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ message: 'Seasonal worker not found' });
    }

    res.json(result.rows[0]);
  } catch (error) {
    console.error('Error updating seasonal worker:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

export const deleteSeasonalWorker = async (req, res) => {
  try {
    const { id } = req.params;

    const result = await db.query(
      'DELETE FROM seasonal_workers WHERE id = $1 RETURNING *',
      [id]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ message: 'Seasonal worker not found' });
    }

    res.json({ message: 'Seasonal worker deleted successfully' });
  } catch (error) {
    console.error('Error deleting seasonal worker:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// Labor Cost Analysis
export const createLaborCostAnalysis = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { 
      farmId, 
      analysisDate, 
      period, 
      taskId, 
      cropId, 
      fieldId, 
      laborHours, 
      laborCost, 
      productivity, 
      notes 
    } = req.body;

    const result = await db.query(
      `INSERT INTO labor_cost_analyses (
        farm_id, 
        analysis_date, 
        period, 
        task_id, 
        crop_id, 
        field_id, 
        labor_hours, 
        labor_cost, 
        productivity, 
        notes
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10) RETURNING *`,
      [
        farmId, 
        analysisDate, 
        period, 
        taskId, 
        cropId, 
        fieldId, 
        laborHours, 
        laborCost, 
        productivity, 
        notes
      ]
    );

    res.status(201).json(result.rows[0]);
  } catch (error) {
    console.error('Error creating labor cost analysis:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

export const getLaborCostAnalyses = async (req, res) => {
  try {
    const { farmId } = req.query;

    if (!farmId) {
      return res.status(400).json({ message: 'Farm ID is required' });
    }

    // Check if farmId is a valid number
    const farmIdNum = parseInt(farmId);
    if (isNaN(farmIdNum)) {
      return res.status(404).json({ 
        message: 'The requested resource could not be found',
        errorType: 'NotFoundError',
        errorCode: '404',
        context: {
          url: req.originalUrl,
          method: req.method
        }
      });
    }

    const result = await db.query(
      'SELECT * FROM labor_cost_analyses WHERE farm_id = $1 ORDER BY analysis_date DESC',
      [farmIdNum]
    );

    res.json(result.rows);
  } catch (error) {
    console.error('Error getting labor cost analyses:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

export const getLaborCostAnalysis = async (req, res) => {
  try {
    const { id } = req.params;

    const result = await db.query(
      'SELECT * FROM labor_cost_analyses WHERE id = $1',
      [id]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ message: 'Labor cost analysis not found' });
    }

    res.json(result.rows[0]);
  } catch (error) {
    console.error('Error getting labor cost analysis:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

export const updateLaborCostAnalysis = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { id } = req.params;
    const { 
      analysisDate, 
      period, 
      taskId, 
      cropId, 
      fieldId, 
      laborHours, 
      laborCost, 
      productivity, 
      notes 
    } = req.body;

    const result = await db.query(
      `UPDATE labor_cost_analyses SET 
        analysis_date = $1, 
        period = $2, 
        task_id = $3, 
        crop_id = $4, 
        field_id = $5, 
        labor_hours = $6, 
        labor_cost = $7, 
        productivity = $8, 
        notes = $9
      WHERE id = $10 RETURNING *`,
      [
        analysisDate, 
        period, 
        taskId, 
        cropId, 
        fieldId, 
        laborHours, 
        laborCost, 
        productivity, 
        notes,
        id
      ]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ message: 'Labor cost analysis not found' });
    }

    res.json(result.rows[0]);
  } catch (error) {
    console.error('Error updating labor cost analysis:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

export const deleteLaborCostAnalysis = async (req, res) => {
  try {
    const { id } = req.params;

    const result = await db.query(
      'DELETE FROM labor_cost_analyses WHERE id = $1 RETURNING *',
      [id]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ message: 'Labor cost analysis not found' });
    }

    res.json({ message: 'Labor cost analysis deleted successfully' });
  } catch (error) {
    console.error('Error deleting labor cost analysis:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// Compliance Tracking
export const createComplianceRecord = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { 
      farmId, 
      recordType, 
      recordDate, 
      expirationDate, 
      status, 
      details, 
      documentUrl, 
      notes 
    } = req.body;

    const result = await db.query(
      `INSERT INTO compliance_records (
        farm_id, 
        record_type, 
        record_date, 
        expiration_date, 
        status, 
        details, 
        document_url, 
        notes
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8) RETURNING *`,
      [
        farmId, 
        recordType, 
        recordDate, 
        expirationDate, 
        status, 
        details, 
        documentUrl, 
        notes
      ]
    );

    res.status(201).json(result.rows[0]);
  } catch (error) {
    console.error('Error creating compliance record:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

export const getComplianceRecords = async (req, res) => {
  try {
    const { farmId } = req.query;

    if (!farmId) {
      return res.status(400).json({ message: 'Farm ID is required' });
    }

    // Check if farmId is a valid number
    const farmIdNum = parseInt(farmId);
    if (isNaN(farmIdNum)) {
      return res.status(404).json({ 
        message: 'The requested resource could not be found',
        errorType: 'NotFoundError',
        errorCode: '404',
        context: {
          url: req.originalUrl,
          method: req.method
        }
      });
    }

    const result = await db.query(
      'SELECT * FROM compliance_records WHERE farm_id = $1 ORDER BY record_date DESC',
      [farmIdNum]
    );

    res.json(result.rows);
  } catch (error) {
    console.error('Error getting compliance records:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

export const getComplianceRecord = async (req, res) => {
  try {
    const { id } = req.params;

    const result = await db.query(
      'SELECT * FROM compliance_records WHERE id = $1',
      [id]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ message: 'Compliance record not found' });
    }

    res.json(result.rows[0]);
  } catch (error) {
    console.error('Error getting compliance record:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

export const updateComplianceRecord = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { id } = req.params;
    const { 
      recordType, 
      recordDate, 
      expirationDate, 
      status, 
      details, 
      documentUrl, 
      notes 
    } = req.body;

    const result = await db.query(
      `UPDATE compliance_records SET 
        record_type = $1, 
        record_date = $2, 
        expiration_date = $3, 
        status = $4, 
        details = $5, 
        document_url = $6, 
        notes = $7
      WHERE id = $8 RETURNING *`,
      [
        recordType, 
        recordDate, 
        expirationDate, 
        status, 
        details, 
        documentUrl, 
        notes,
        id
      ]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ message: 'Compliance record not found' });
    }

    res.json(result.rows[0]);
  } catch (error) {
    console.error('Error updating compliance record:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

export const deleteComplianceRecord = async (req, res) => {
  try {
    const { id } = req.params;

    const result = await db.query(
      'DELETE FROM compliance_records WHERE id = $1 RETURNING *',
      [id]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ message: 'Compliance record not found' });
    }

    res.json({ message: 'Compliance record deleted successfully' });
  } catch (error) {
    console.error('Error deleting compliance record:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// Worker Certification Tracking
export const createWorkerCertification = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { 
      farmId, 
      workerId, 
      certificationType, 
      issueDate, 
      expirationDate, 
      status, 
      documentUrl, 
      notes 
    } = req.body;

    const result = await db.query(
      `INSERT INTO worker_certifications (
        farm_id, 
        worker_id, 
        certification_type, 
        issue_date, 
        expiration_date, 
        status, 
        document_url, 
        notes
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8) RETURNING *`,
      [
        farmId, 
        workerId, 
        certificationType, 
        issueDate, 
        expirationDate, 
        status, 
        documentUrl, 
        notes
      ]
    );

    res.status(201).json(result.rows[0]);
  } catch (error) {
    console.error('Error creating worker certification:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

export const getWorkerCertifications = async (req, res) => {
  try {
    const { farmId, workerId } = req.query;

    if (!farmId) {
      return res.status(400).json({ message: 'Farm ID is required' });
    }

    // Check if farmId is a valid number
    const farmIdNum = parseInt(farmId);
    if (isNaN(farmIdNum)) {
      return res.status(404).json({ 
        message: 'The requested resource could not be found',
        errorType: 'NotFoundError',
        errorCode: '404',
        context: {
          url: req.originalUrl,
          method: req.method
        }
      });
    }

    let query = 'SELECT * FROM worker_certifications WHERE farm_id = $1';
    let params = [farmIdNum];

    if (workerId) {
      // Check if workerId is a valid number
      const workerIdNum = parseInt(workerId);
      if (isNaN(workerIdNum)) {
        return res.status(404).json({ 
          message: 'The requested resource could not be found',
          errorType: 'NotFoundError',
          errorCode: '404',
          context: {
            url: req.originalUrl,
            method: req.method
          }
        });
      }
      query += ' AND worker_id = $2';
      params.push(workerIdNum);
    }

    query += ' ORDER BY expiration_date ASC';

    const result = await db.query(query, params);

    res.json(result.rows);
  } catch (error) {
    console.error('Error getting worker certifications:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

export const getWorkerCertification = async (req, res) => {
  try {
    const { id } = req.params;

    const result = await db.query(
      'SELECT * FROM worker_certifications WHERE id = $1',
      [id]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ message: 'Worker certification not found' });
    }

    res.json(result.rows[0]);
  } catch (error) {
    console.error('Error getting worker certification:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

export const updateWorkerCertification = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { id } = req.params;
    const { 
      certificationType, 
      issueDate, 
      expirationDate, 
      status, 
      documentUrl, 
      notes 
    } = req.body;

    const result = await db.query(
      `UPDATE worker_certifications SET 
        certification_type = $1, 
        issue_date = $2, 
        expiration_date = $3, 
        status = $4, 
        document_url = $5, 
        notes = $6
      WHERE id = $7 RETURNING *`,
      [
        certificationType, 
        issueDate, 
        expirationDate, 
        status, 
        documentUrl, 
        notes,
        id
      ]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ message: 'Worker certification not found' });
    }

    res.json(result.rows[0]);
  } catch (error) {
    console.error('Error updating worker certification:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

export const deleteWorkerCertification = async (req, res) => {
  try {
    const { id } = req.params;

    const result = await db.query(
      'DELETE FROM worker_certifications WHERE id = $1 RETURNING *',
      [id]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ message: 'Worker certification not found' });
    }

    res.json({ message: 'Worker certification deleted successfully' });
  } catch (error) {
    console.error('Error deleting worker certification:', error);
    res.status(500).json({ message: 'Server error' });
  }
};
