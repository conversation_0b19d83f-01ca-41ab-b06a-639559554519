import quickbooksClient from '../config/quickbooks.js';
import { sequelize } from '../config/database.js';
import QuickBooksConnection from '../models/QuickBooksConnection.js';
import Farm from '../models/Farm.js';
import dotenv from 'dotenv';
import axios from 'axios';
import OAuthClient from 'intuit-oauth';

dotenv.config();

// Generate authorization URL for QuickBooks OAuth
export const getAuthorizationUrl = async (req, res) => {
  try {
    const { farmId } = req.query;

    if (!farmId) {
      return res.status(400).json({ error: 'Farm ID is required' });
    }

    // Find farm to ensure it exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Generate authorization URL
    const authUri = quickbooksClient.authorizeUri({
      scope: [OAuthClient.scopes.Accounting, OAuthClient.scopes.OpenId],
      state: farmId,
    });

    return res.json({ authUrl: authUri });
  } catch (error) {
    console.error('Error generating QuickBooks authorization URL:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Handle OAuth callback from QuickBooks
export const handleCallback = async (req, res) => {
  try {
    const { code, state, realmId } = req.query;

    if (!code || !state || !realmId) {
      return res.status(400).json({ error: 'Missing required parameters' });
    }

    const farmId = state;

    // Find farm to ensure it exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Exchange authorization code for tokens
    const tokenResponse = await quickbooksClient.createToken(req.url);
    const { access_token, refresh_token, expires_in } = tokenResponse.getJson();

    // Calculate token expiration date
    const expiresAt = new Date();
    expiresAt.setSeconds(expiresAt.getSeconds() + expires_in);

    // Get company info
    const companyInfo = await getCompanyInfo(access_token, realmId);

    const transaction = await sequelize.transaction();

    try {
      // Check if connection already exists
      let connection = await QuickBooksConnection.findOne({
        where: { farm_id: farmId },
        transaction,
      });

      if (connection) {
        // Update existing connection
        await connection.update({
          realm_id: realmId,
          access_token,
          refresh_token,
          token_expires_at: expiresAt,
          company_name: companyInfo.CompanyName,
          status: 'active',
        }, { transaction });
      } else {
        // Create new connection
        connection = await QuickBooksConnection.create({
          farm_id: farmId,
          realm_id: realmId,
          access_token,
          refresh_token,
          token_expires_at: expiresAt,
          company_name: companyInfo.CompanyName,
          status: 'active',
        }, { transaction });
      }

      await transaction.commit();

      // Redirect to success page
      return res.redirect(`/quickbooks/success?connectionId=${connection.id}`);
    } catch (dbError) {
      await transaction.rollback();
      console.error('Database error:', dbError);
      return res.status(500).json({ error: 'Failed to store QuickBooks connection' });
    }
  } catch (error) {
    console.error('Error handling QuickBooks callback:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get company info from QuickBooks
const getCompanyInfo = async (accessToken, realmId) => {
  try {
    const url = `${process.env.NODE_ENV === 'production' 
      ? 'https://quickbooks.api.intuit.com' 
      : 'https://sandbox-quickbooks.api.intuit.com'}/v3/company/${realmId}/companyinfo/${realmId}`;

    const response = await axios.get(url, {
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Accept': 'application/json',
      },
    });

    return response.data.CompanyInfo;
  } catch (error) {
    console.error('Error getting company info:', error);
    throw error;
  }
};

// Get QuickBooks connection for a farm
export const getConnection = async (req, res) => {
  try {
    const { farmId } = req.params;

    if (!farmId) {
      return res.status(400).json({ error: 'Farm ID is required' });
    }

    // Find connection
    const connection = await QuickBooksConnection.findOne({
      where: { farm_id: farmId },
    });

    if (!connection) {
      return res.status(404).json({ error: 'QuickBooks connection not found' });
    }

    // Don't return sensitive information
    const { access_token, refresh_token, ...safeConnection } = connection.toJSON();

    return res.json({ connection: safeConnection });
  } catch (error) {
    console.error('Error getting QuickBooks connection:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Disconnect QuickBooks
export const disconnectQuickBooks = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { connectionId } = req.params;

    // Find connection
    const connection = await QuickBooksConnection.findByPk(connectionId);

    if (!connection) {
      await transaction.rollback();
      return res.status(404).json({ error: 'QuickBooks connection not found' });
    }

    // Revoke tokens
    try {
      await quickbooksClient.revoke({
        token: connection.access_token,
      });
    } catch (revokeError) {
      console.error('Error revoking QuickBooks tokens:', revokeError);
      // Continue with deletion even if revoke fails
    }

    // Delete connection
    await connection.destroy({ transaction });

    await transaction.commit();

    return res.json({
      success: true,
      message: 'QuickBooks disconnected successfully',
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error disconnecting QuickBooks:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Sync accounts from QuickBooks
export const syncAccounts = async (req, res) => {
  try {
    const { connectionId } = req.params;

    // Find connection
    const connection = await QuickBooksConnection.findByPk(connectionId);

    if (!connection) {
      return res.status(404).json({ error: 'QuickBooks connection not found' });
    }

    // Check if token is expired
    if (new Date() > new Date(connection.token_expires_at)) {
      // Refresh token
      try {
        const refreshResponse = await quickbooksClient.refreshUsingToken(connection.refresh_token);
        const { access_token, refresh_token, expires_in } = refreshResponse.getJson();

        // Calculate new expiration date
        const expiresAt = new Date();
        expiresAt.setSeconds(expiresAt.getSeconds() + expires_in);

        // Update connection
        await connection.update({
          access_token,
          refresh_token,
          token_expires_at: expiresAt,
        });
      } catch (refreshError) {
        console.error('Error refreshing QuickBooks token:', refreshError);

        // Update connection status
        await connection.update({
          status: 'error',
        });

        return res.status(401).json({ error: 'Failed to refresh QuickBooks token' });
      }
    }

    // Get accounts from QuickBooks
    try {
      const url = `${process.env.NODE_ENV === 'production' 
        ? 'https://quickbooks.api.intuit.com' 
        : 'https://sandbox-quickbooks.api.intuit.com'}/v3/company/${connection.realm_id}/query`;

      const response = await axios.get(url, {
        headers: {
          'Authorization': `Bearer ${connection.access_token}`,
          'Accept': 'application/json',
        },
        params: {
          query: 'SELECT * FROM Account WHERE Active = true',
        },
      });

      const accounts = response.data.QueryResponse.Account || [];

      // Update last sync timestamp
      await connection.update({
        last_sync_at: new Date(),
      });

      return res.json({
        success: true,
        accounts,
      });
    } catch (apiError) {
      console.error('Error getting accounts from QuickBooks:', apiError);

      // Update connection status if there's an API error
      if (apiError.response && apiError.response.status === 401) {
        await connection.update({
          status: 'error',
        });
      }

      return res.status(500).json({ error: 'Failed to get accounts from QuickBooks' });
    }
  } catch (error) {
    console.error('Error syncing QuickBooks accounts:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Import QuickBooks data (simplified version)
export const importData = async (req, res) => {
  try {
    const { connectionId, dataType } = req.params;
    const { startDate, endDate, itemId } = req.query;

    // Validate data type
    const validDataTypes = ['accounts', 'customers', 'vendors', 'items', 'invoices', 'bills', 'transactions', 'payments', 'taxdocuments'];
    if (!validDataTypes.includes(dataType)) {
      return res.status(400).json({ error: 'Invalid data type' });
    }

    // Find connection
    const connection = await QuickBooksConnection.findByPk(connectionId);

    if (!connection) {
      return res.status(404).json({ error: 'QuickBooks connection not found' });
    }

    // Check if token is expired and refresh if needed
    if (new Date() > new Date(connection.token_expires_at)) {
      try {
        const refreshResponse = await quickbooksClient.refreshUsingToken(connection.refresh_token);
        const { access_token, refresh_token, expires_in } = refreshResponse.getJson();

        const expiresAt = new Date();
        expiresAt.setSeconds(expiresAt.getSeconds() + expires_in);

        await connection.update({
          access_token,
          refresh_token,
          token_expires_at: expiresAt,
        });
      } catch (refreshError) {
        console.error('Error refreshing QuickBooks token:', refreshError);
        await connection.update({ status: 'error' });
        return res.status(401).json({ error: 'Failed to refresh QuickBooks token' });
      }
    }

    // Build query based on data type
    let query;
    let entityName;

    switch (dataType) {
      case 'accounts':
        entityName = 'Account';
        break;
      case 'customers':
        entityName = 'Customer';
        break;
      case 'vendors':
        entityName = 'Vendor';
        break;
      case 'items':
        entityName = 'Item';
        break;
      case 'invoices':
        entityName = 'Invoice';
        break;
      case 'bills':
        entityName = 'Bill';
        break;
      case 'transactions':
        entityName = 'Transaction';
        break;
      case 'payments':
        entityName = 'Payment';
        break;
      case 'taxdocuments':
        entityName = 'TaxPayment';
        break;
      default:
        return res.status(400).json({ error: 'Invalid data type' });
    }

    // Build the query
    query = `SELECT * FROM ${entityName}`;

    // Add filters
    const conditions = [];

    // Filter by ID if provided
    if (itemId) {
      conditions.push(`Id = '${itemId}'`);
    }

    // Filter by date range if provided
    if (startDate && endDate && ['invoices', 'bills', 'transactions', 'payments', 'taxdocuments'].includes(dataType)) {
      conditions.push(`TxnDate >= '${startDate}' AND TxnDate <= '${endDate}'`);
    }

    // Add conditions to query
    if (conditions.length > 0) {
      query += ` WHERE ${conditions.join(' AND ')}`;
    }

    // Get data from QuickBooks
    try {
      const url = `${process.env.NODE_ENV === 'production' 
        ? 'https://quickbooks.api.intuit.com' 
        : 'https://sandbox-quickbooks.api.intuit.com'}/v3/company/${connection.realm_id}/query`;

      const response = await axios.get(url, {
        headers: {
          'Authorization': `Bearer ${connection.access_token}`,
          'Accept': 'application/json',
        },
        params: { query },
      });

      // Extract data from response
      const responseKey = Object.keys(response.data.QueryResponse)[0];
      const data = response.data.QueryResponse[responseKey] || [];

      // Update last sync timestamp
      await connection.update({
        last_sync_at: new Date(),
      });

      return res.json({
        success: true,
        data,
      });
    } catch (apiError) {
      console.error(`Error getting ${dataType} from QuickBooks:`, apiError);

      if (apiError.response && apiError.response.status === 401) {
        await connection.update({ status: 'error' });
      }

      return res.status(500).json({ error: `Failed to get ${dataType} from QuickBooks` });
    }
  } catch (error) {
    console.error(`Error importing QuickBooks ${req.params.dataType}:`, error);
    return res.status(500).json({ error: error.message });
  }
};

// Update an item in QuickBooks
export const updateItem = async (req, res) => {
  try {
    const { connectionId, dataType, itemId } = req.params;
    const itemData = req.body;

    // Validate data type
    const validDataTypes = ['accounts', 'customers', 'vendors', 'items', 'invoices', 'bills', 'payments', 'taxdocuments'];
    if (!validDataTypes.includes(dataType)) {
      return res.status(400).json({ error: 'Invalid data type' });
    }

    // Find connection
    const connection = await QuickBooksConnection.findByPk(connectionId);

    if (!connection) {
      return res.status(404).json({ error: 'QuickBooks connection not found' });
    }

    // Check if token is expired and refresh if needed
    if (new Date() > new Date(connection.token_expires_at)) {
      try {
        const refreshResponse = await quickbooksClient.refreshUsingToken(connection.refresh_token);
        const { access_token, refresh_token, expires_in } = refreshResponse.getJson();

        const expiresAt = new Date();
        expiresAt.setSeconds(expiresAt.getSeconds() + expires_in);

        await connection.update({
          access_token,
          refresh_token,
          token_expires_at: expiresAt,
        });
      } catch (refreshError) {
        console.error('Error refreshing QuickBooks token:', refreshError);
        await connection.update({ status: 'error' });
        return res.status(401).json({ error: 'Failed to refresh QuickBooks token' });
      }
    }

    // Map data type to entity name
    let entityName;
    switch (dataType) {
      case 'accounts':
        entityName = 'Account';
        break;
      case 'customers':
        entityName = 'Customer';
        break;
      case 'vendors':
        entityName = 'Vendor';
        break;
      case 'items':
        entityName = 'Item';
        break;
      case 'invoices':
        entityName = 'Invoice';
        break;
      case 'bills':
        entityName = 'Bill';
        break;
      case 'payments':
        entityName = 'Payment';
        break;
      case 'taxdocuments':
        entityName = 'TaxPayment';
        break;
      default:
        return res.status(400).json({ error: 'Invalid data type' });
    }

    // Update item in QuickBooks
    try {
      const url = `${process.env.NODE_ENV === 'production' 
        ? 'https://quickbooks.api.intuit.com' 
        : 'https://sandbox-quickbooks.api.intuit.com'}/v3/company/${connection.realm_id}/${entityName.toLowerCase()}`;

      // Make sure the item has an ID
      if (!itemData.Id || itemData.Id !== itemId) {
        return res.status(400).json({ error: 'Item ID in body must match URL parameter' });
      }

      // QuickBooks requires a SyncToken for updates to prevent conflicts
      if (!itemData.SyncToken) {
        return res.status(400).json({ error: 'SyncToken is required for updates' });
      }

      const response = await axios.post(url, itemData, {
        headers: {
          'Authorization': `Bearer ${connection.access_token}`,
          'Accept': 'application/json',
          'Content-Type': 'application/json',
        },
      });

      // Update last sync timestamp
      await connection.update({
        last_sync_at: new Date(),
      });

      return res.json({
        success: true,
        data: response.data,
      });
    } catch (apiError) {
      console.error(`Error updating ${dataType} in QuickBooks:`, apiError);

      if (apiError.response && apiError.response.status === 401) {
        await connection.update({ status: 'error' });
      }

      return res.status(apiError.response?.status || 500).json({ 
        error: `Failed to update ${dataType} in QuickBooks`,
        details: apiError.response?.data || apiError.message
      });
    }
  } catch (error) {
    console.error(`Error updating QuickBooks ${req.params.dataType}:`, error);
    return res.status(500).json({ error: error.message });
  }
};
