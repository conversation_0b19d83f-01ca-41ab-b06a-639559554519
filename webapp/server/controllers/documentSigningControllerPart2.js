import { sequelize } from '../config/database.js';
import SignableDocument from '../models/SignableDocument.js';
import DocumentSigner from '../models/DocumentSigner.js';
import DocumentSignature from '../models/DocumentSignature.js';
import DocumentField from '../models/DocumentField.js';
import DocumentAuditLog from '../models/DocumentAuditLog.js';
import User from '../models/User.js';
import Farm from '../models/Farm.js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { v4 as uuidv4 } from 'uuid';
import { 
  sendDocumentForSigningEmail, 
  sendDocumentSignedEmail, 
  sendDocumentDeclinedEmail,
  getFrontendUrl
} from '../utils/emailUtils.js';
import { createBlockchainVerification, getBlockchainVerificationDetails, verifyDocumentOnBlockchain } from '../utils/blockchainUtils.js';
import { fileExistsInSpaces, downloadFromSpaces } from '../utils/spacesUtils.js';
import { signPDFWithCertificate, verifyDigitalSignature } from '../utils/digitalSignatureUtils.js';

// Get directory name for ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Base upload directory
const uploadsDir = path.join(__dirname, '..', '..', 'uploads');

// Import the helper functions from documentSigningController.js
import { checkUserFarmAccess, createAuditLog } from './documentSigningController.js';

/**
 * Add signers to a document
 */
export const addSigners = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { id } = req.params;
    const { signers } = req.body;

    if (!signers || !Array.isArray(signers) || signers.length === 0) {
      await transaction.rollback();
      return res.status(400).json({ error: 'No signers provided' });
    }

    // Get document
    const document = await SignableDocument.findByPk(id);

    if (!document) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Document not found' });
    }

    // Validate farm access
    const hasAccess = await checkUserFarmAccess(req.user.id, document.farm_id);
    if (!hasAccess && !req.user.is_global_admin) {
      await transaction.rollback();
      return res.status(403).json({ error: 'You do not have access to this document' });
    }

    // Only allow adding signers if document is in draft status
    if (document.status !== 'draft') {
      await transaction.rollback();
      return res.status(400).json({ error: 'Signers can only be added when document is in draft status' });
    }

    // Get existing signers to determine next order
    const existingSigners = await DocumentSigner.findAll({
      where: { document_id: id },
      order: [['signer_order', 'DESC']]
    });

    let nextOrder = existingSigners.length > 0 ? existingSigners[0].signer_order + 1 : 1;

    // Create signers
    const createdSigners = [];
    for (const signer of signers) {
      // Check if signer with this email already exists for this document
      const existingSigner = existingSigners.find(s => s.signer_email === signer.email);
      if (existingSigner) {
        continue; // Skip this signer
      }

      const newSigner = await DocumentSigner.create({
        document_id: id,
        signer_email: signer.email,
        signer_name: signer.name,
        signer_role: signer.role || null,
        signer_order: signer.order || nextOrder++,
        status: 'pending',
        verification_method: signer.verificationMethod || 'email'
      }, { transaction });

      createdSigners.push(newSigner);
    }

    // Create audit log entry
    await createAuditLog(
      document.id,
      req.user.id,
      null,
      'signers_added',
      { count: createdSigners.length },
      req.ip,
      req.headers['user-agent'],
      transaction
    );

    await transaction.commit();

    return res.status(200).json({
      message: `${createdSigners.length} signers added successfully`,
      signers: createdSigners
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error adding signers:', error);
    return res.status(500).json({ error: error.message });
  }
};

/**
 * Remove a signer from a document
 */
export const removeSigner = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { id, signerId } = req.params;

    // Get document
    const document = await SignableDocument.findByPk(id);

    if (!document) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Document not found' });
    }

    // Validate farm access
    const hasAccess = await checkUserFarmAccess(req.user.id, document.farm_id);
    if (!hasAccess && !req.user.is_global_admin) {
      await transaction.rollback();
      return res.status(403).json({ error: 'You do not have access to this document' });
    }

    // Only allow removing signers if document is in draft status
    if (document.status !== 'draft') {
      await transaction.rollback();
      return res.status(400).json({ error: 'Signers can only be removed when document is in draft status' });
    }

    // Get signer
    const signer = await DocumentSigner.findOne({
      where: {
        id: signerId,
        document_id: id
      }
    });

    if (!signer) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Signer not found' });
    }

    // Create audit log entry before deleting the signer
    await createAuditLog(
      document.id,
      req.user.id,
      signer.id,
      'signer_removed',
      { 
        signer_email: signer.signer_email,
        signer_name: signer.signer_name
      },
      req.ip,
      req.headers['user-agent'],
      transaction
    );

    // Delete signer
    await signer.destroy({ transaction });

    await transaction.commit();

    return res.status(200).json({
      message: 'Signer removed successfully'
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error removing signer:', error);
    return res.status(500).json({ error: error.message });
  }
};

/**
 * Send a document for signing
 */
export const sendDocument = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { id } = req.params;
    const { message } = req.body;

    // Get document with signers and creator
    const document = await SignableDocument.findByPk(id, {
      include: [
        {
          model: DocumentSigner,
          as: 'signers',
          order: [['signer_order', 'ASC']]
        },
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'first_name', 'last_name', 'email']
        },
        {
          model: Farm,
          as: 'signableDocumentFarm',
          attributes: ['id', 'name']
        }
      ]
    });

    if (!document) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Document not found' });
    }

    // Validate farm access
    const hasAccess = await checkUserFarmAccess(req.user.id, document.farm_id);
    if (!hasAccess && !req.user.is_global_admin) {
      await transaction.rollback();
      return res.status(403).json({ error: 'You do not have access to this document' });
    }

    // Only allow sending if document is in draft status
    if (document.status !== 'draft') {
      await transaction.rollback();
      return res.status(400).json({ error: 'Document can only be sent when in draft status' });
    }

    // Check if document has signers
    if (!document.signers || document.signers.length === 0) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Document has no signers' });
    }

    // Update document status
    await document.update({ status: 'sent' }, { transaction });

    // Send emails to signers based on signing order
    const firstOrderSigners = document.signers.filter(signer => signer.signer_order === 1);
    const frontendUrl = await getFrontendUrl(req.user.id);

    for (const signer of firstOrderSigners) {
      // Generate access token for the signer
      const accessToken = uuidv4();

      // Update signer status and sent_at timestamp
      await signer.update({
        status: 'sent',
        access_code: accessToken,
        sent_at: new Date()
      }, { transaction });

      // Generate signing URL
      const signingUrl = `${frontendUrl}/documents/sign/${document.id}/${signer.id}?token=${accessToken}`;

      try {
        // Send email to the signer
        await sendDocumentForSigningEmail(
          document,
          signer,
          document.creator,
          document.signableDocumentFarm,
          signingUrl
        );

        console.log(`Email sent to ${signer.signer_email} with access token ${accessToken}`);
      } catch (emailError) {
        console.error(`Error sending email to ${signer.signer_email}:`, emailError);
        // Continue with the process even if email sending fails
      }
    }

    // Create audit log entry
    await createAuditLog(
      document.id,
      req.user.id,
      null,
      'document_sent',
      { 
        recipients: firstOrderSigners.map(s => s.signer_email).join(', ')
      },
      req.ip,
      req.headers['user-agent'],
      transaction
    );

    await transaction.commit();

    return res.status(200).json({
      message: 'Document sent successfully',
      document
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error sending document:', error);
    return res.status(500).json({ error: error.message });
  }
};

/**
 * Get document for signing (public endpoint)
 */
export const getDocumentForSigning = async (req, res) => {
  try {
    const { documentId, signerId } = req.params;
    const { token } = req.query;

    // Validate token
    if (!token) {
      return res.status(401).json({ error: 'Access token is required' });
    }

    // Get signer
    const signer = await DocumentSigner.findOne({
      where: {
        id: signerId,
        document_id: documentId,
        access_code: token
      }
    });

    if (!signer) {
      return res.status(401).json({ error: 'Invalid access token' });
    }

    // Get document
    const document = await SignableDocument.findByPk(documentId, {
      attributes: { exclude: ['file_path'] }, // Don't expose file path
      include: [
        {
          model: DocumentField,
          as: 'fields',
          where: { signer_id: signerId },
          required: false
        }
      ]
    });

    if (!document) {
      return res.status(404).json({ error: 'Document not found' });
    }

    // Check if document is expired
    if (document.status === 'expired' || (document.expires_at && new Date(document.expires_at) < new Date())) {
      return res.status(400).json({ error: 'Document has expired' });
    }

    // Check if document is canceled
    if (document.status === 'canceled') {
      return res.status(400).json({ error: 'Document has been canceled' });
    }

    // Update signer viewed_at if not already set
    if (!signer.viewed_at) {
      await signer.update({ 
        status: 'viewed',
        viewed_at: new Date() 
      });

      // Create audit log entry
      await createAuditLog(
        document.id,
        null,
        signer.id,
        'document_viewed',
        { 
          signer_email: signer.signer_email,
          signer_name: signer.signer_name
        },
        req.ip,
        req.headers['user-agent']
      );
    }

    return res.status(200).json({
      document,
      signer
    });
  } catch (error) {
    console.error('Error getting document for signing:', error);
    return res.status(500).json({ error: error.message });
  }
};

/**
 * Download document for signing (public endpoint)
 */
export const downloadDocumentForSigning = async (req, res) => {
  try {
    const { documentId, signerId } = req.params;
    const { token } = req.query;

    // Validate token
    if (!token) {
      return res.status(401).json({ error: 'Access token is required' });
    }

    // Get signer
    const signer = await DocumentSigner.findOne({
      where: {
        id: signerId,
        document_id: documentId,
        access_code: token
      }
    });

    if (!signer) {
      return res.status(401).json({ error: 'Invalid access token' });
    }

    // Get document
    const document = await SignableDocument.findByPk(documentId);

    if (!document) {
      return res.status(404).json({ error: 'Document not found' });
    }

    // Check if document is expired
    if (document.status === 'expired' || (document.expires_at && new Date(document.expires_at) < new Date())) {
      return res.status(400).json({ error: 'Document has expired' });
    }

    // Check if document is canceled
    if (document.status === 'canceled') {
      return res.status(400).json({ error: 'Document has been canceled' });
    }

    // Create audit log entry for document view
    await createAuditLog(
      document.id,
      null,
      signer.id,
      'document_viewed',
      { signer_email: signer.signer_email },
      req.ip,
      req.headers['user-agent']
    );

    // Handle encrypted documents
    if (document.is_encrypted) {
      try {
        // Retrieve and decrypt the file
        const decryptedData = await retrieveAndDecryptFile(
          document.file_path,
          document.encryption_key_id,
          document.encryption_iv,
          document.encryption_method
        );

        // Set headers
        res.setHeader('Content-Type', document.mime_type);
        res.setHeader('Content-Disposition', `attachment; filename="${document.title}"`);

        // Send decrypted file
        return res.send(decryptedData);
      } catch (error) {
        console.error('Error decrypting document for signing:', error);
        return res.status(500).json({ error: 'Error decrypting document' });
      }
    }

    // Handle unencrypted documents
    try {
      // First try to get the file from Spaces
      try {
        const fileExists = await fileExistsInSpaces(document.file_path);
        if (fileExists) {
          // File exists in Spaces, download it
          const fileBuffer = await downloadFromSpaces(document.file_path);

          // Set headers
          res.setHeader('Content-Type', document.mime_type);
          res.setHeader('Content-Disposition', `attachment; filename="${document.title}"`);

          // Send file
          return res.send(fileBuffer);
        }
      } catch (error) {
        console.log('File not found in Spaces, trying local filesystem');
      }

      // Fallback to local filesystem
      const filePath = path.join(uploadsDir, document.file_path);

      // Check if file exists locally
      if (!fs.existsSync(filePath)) {
        return res.status(404).json({ error: 'File not found in storage' });
      }

      // Set headers
      res.setHeader('Content-Type', document.mime_type);
      res.setHeader('Content-Disposition', `attachment; filename="${document.title}"`);

      // Stream file to response
      const fileStream = fs.createReadStream(filePath);
      fileStream.pipe(res);
    } catch (error) {
      console.error('Error downloading document:', error);
      return res.status(500).json({ error: 'Error downloading document' });
    }
  } catch (error) {
    console.error('Error downloading document for signing:', error);
    return res.status(500).json({ error: error.message });
  }
};

/**
 * Sign a document
 */
export const signDocument = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { documentId, signerId } = req.params;
    const { token } = req.query;
    const { 
      signatureType, 
      signatureData,
      signaturePosition,
      fields = [] 
    } = req.body;

    // Validate token
    if (!token) {
      await transaction.rollback();
      return res.status(401).json({ error: 'Access token is required' });
    }

    // Get signer
    const signer = await DocumentSigner.findOne({
      where: {
        id: signerId,
        document_id: documentId,
        access_code: token
      }
    });

    if (!signer) {
      await transaction.rollback();
      return res.status(401).json({ error: 'Invalid access token' });
    }

    // Get document with signers, creator, and farm
    const document = await SignableDocument.findByPk(documentId, {
      include: [
        {
          model: DocumentSigner,
          as: 'signers',
          order: [['signer_order', 'ASC']]
        },
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'first_name', 'last_name', 'email']
        },
        {
          model: Farm,
          as: 'signableDocumentFarm',
          attributes: ['id', 'name']
        }
      ]
    });

    if (!document) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Document not found' });
    }

    // Check if document is expired
    if (document.status === 'expired' || (document.expires_at && new Date(document.expires_at) < new Date())) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Document has expired' });
    }

    // Check if document is canceled
    if (document.status === 'canceled') {
      await transaction.rollback();
      return res.status(400).json({ error: 'Document has been canceled' });
    }

    // Check if signer has already signed
    if (signer.status === 'signed') {
      await transaction.rollback();
      return res.status(400).json({ error: 'You have already signed this document' });
    }

    // Create signature record
    const signature = await DocumentSignature.create({
      document_id: documentId,
      signer_id: signerId,
      signature_type: signatureType || 'drawn',
      signature_text: signatureType === 'typed' ? signatureData : null,
      signature_image_path: signatureType !== 'typed' ? signatureData : null,
      page_number: signaturePosition?.page || 1,
      x_position: signaturePosition?.x || 0,
      y_position: signaturePosition?.y || 0,
      width: signaturePosition?.width || 200,
      height: signaturePosition?.height || 50,
      ip_address: req.ip,
      user_agent: req.headers['user-agent']
    }, { transaction });

    // Update field values if provided
    if (fields.length > 0) {
      for (const field of fields) {
        await DocumentField.update(
          { field_value: field.value },
          { 
            where: { 
              id: field.id,
              document_id: documentId,
              signer_id: signerId
            },
            transaction
          }
        );
      }
    }

    // Update signer status
    await signer.update({
      status: 'signed',
      signed_at: new Date()
    }, { transaction });

    // Create audit log entry
    await createAuditLog(
      document.id,
      null,
      signer.id,
      'document_signed',
      { 
        signer_email: signer.signer_email,
        signer_name: signer.signer_name,
        signature_type: signatureType
      },
      req.ip,
      req.headers['user-agent'],
      transaction
    );

    // Check if all signers have signed
    const allSigners = document.signers;
    const allSigned = allSigners.every(s => s.status === 'signed' || s.id === signerId);
    const frontendUrl = await getFrontendUrl(null); // No user ID needed for public URL

    // Get document URL for viewing
    const documentUrl = `${frontendUrl}/documents/view/${document.id}`;

    // Send email to document creator about the signature
    try {
      await sendDocumentSignedEmail(
        document,
        signer,
        document.creator,
        documentUrl,
        allSigned
      );
      console.log(`Email sent to document creator ${document.creator.email} about signature`);
    } catch (emailError) {
      console.error(`Error sending email to document creator:`, emailError);
      // Continue with the process even if email sending fails
    }

    // If all signers have signed, update document status to completed
    if (allSigned) {
      await document.update({
        status: 'completed',
        completed_at: new Date()
      }, { transaction });

      // Create audit log entry for document completion
      await createAuditLog(
        document.id,
        null,
        null,
        'document_completed',
        { title: document.title },
        req.ip,
        req.headers['user-agent'],
        transaction
      );

      // Add blockchain verification for the completed document
      try {
        // Get the document file content
        let documentContent;
        try {
          // First try to get the file from Spaces
          const fileExists = await fileExistsInSpaces(document.file_path);
          if (fileExists) {
            documentContent = await downloadFromSpaces(document.file_path);
          } else {
            // Fallback to local filesystem
            const filePath = path.join(uploadsDir, document.file_path);
            if (fs.existsSync(filePath)) {
              documentContent = await fs.promises.readFile(filePath);
            } else {
              throw new Error('Document file not found');
            }
          }

          // Create blockchain verification
          const verification = await createBlockchainVerification(document.id, documentContent);

          // Create audit log entry for blockchain verification
          await createAuditLog(
            document.id,
            null,
            null,
            'blockchain_verification_created',
            { 
              transaction_hash: verification.transaction_hash,
              blockchain_network: verification.blockchain_network,
              status: verification.verification_status
            },
            req.ip,
            req.headers['user-agent'],
            transaction
          );

          console.log(`Blockchain verification created for document ${document.id}: ${verification.transaction_hash}`);
        } catch (blockchainError) {
          console.error('Error creating blockchain verification:', blockchainError);

          // Create audit log entry for blockchain verification failure
          await createAuditLog(
            document.id,
            null,
            null,
            'blockchain_verification_failed',
            { error: blockchainError.message },
            req.ip,
            req.headers['user-agent'],
            transaction
          );
        }
      } catch (error) {
        console.error('Error in blockchain verification process:', error);
        // Continue with the process even if blockchain verification fails
      }

      // Send emails to all signers about document completion
      for (const otherSigner of allSigners) {
        if (otherSigner.id !== signerId) {
          try {
            await sendDocumentSignedEmail(
              document,
              signer, // The signer who just signed
              { 
                first_name: otherSigner.signer_name.split(' ')[0], 
                email: otherSigner.signer_email 
              }, // Recipient info
              documentUrl,
              true // Document is completed
            );
            console.log(`Email sent to signer ${otherSigner.signer_email} about document completion`);
          } catch (emailError) {
            console.error(`Error sending email to signer ${otherSigner.signer_email}:`, emailError);
            // Continue with the process even if email sending fails
          }
        }
      }
    } else {
      // Check if there are next signers to notify
      const currentOrder = signer.signer_order;
      const nextOrderSigners = allSigners.filter(s => s.signer_order === currentOrder + 1 && s.status === 'pending');

      if (nextOrderSigners.length > 0) {
        // Update document status if it's not already 'sent'
        if (document.status !== 'sent') {
          await document.update({ status: 'sent' }, { transaction });
        }

        // Update next signers' status and send notifications
        for (const nextSigner of nextOrderSigners) {
          // Generate access token for the next signer
          const accessToken = uuidv4();

          // Update signer status and sent_at timestamp
          await nextSigner.update({
            status: 'sent',
            access_code: accessToken,
            sent_at: new Date()
          }, { transaction });

          // Generate signing URL
          const signingUrl = `${frontendUrl}/documents/sign/${document.id}/${nextSigner.id}?token=${accessToken}`;

          try {
            // Send email to the next signer
            await sendDocumentForSigningEmail(
              document,
              nextSigner,
              document.creator,
              document.signableDocumentFarm,
              signingUrl
            );

            console.log(`Email sent to next signer ${nextSigner.signer_email} with access token ${accessToken}`);
          } catch (emailError) {
            console.error(`Error sending email to next signer ${nextSigner.signer_email}:`, emailError);
            // Continue with the process even if email sending fails
          }
        }
      }
    }

    await transaction.commit();

    return res.status(200).json({
      message: 'Document signed successfully',
      signature,
      document_status: document.status
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error signing document:', error);
    return res.status(500).json({ error: error.message });
  }
};

/**
 * Decline to sign a document
 */
export const declineDocument = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { documentId, signerId } = req.params;
    const { token } = req.query;
    const { reason } = req.body;

    // Validate token
    if (!token) {
      await transaction.rollback();
      return res.status(401).json({ error: 'Access token is required' });
    }

    // Get signer
    const signer = await DocumentSigner.findOne({
      where: {
        id: signerId,
        document_id: documentId,
        access_code: token
      }
    });

    if (!signer) {
      await transaction.rollback();
      return res.status(401).json({ error: 'Invalid access token' });
    }

    // Get document with creator and farm
    const document = await SignableDocument.findByPk(documentId, {
      include: [
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'first_name', 'last_name', 'email']
        },
        {
          model: Farm,
          as: 'signableDocumentFarm',
          attributes: ['id', 'name']
        },
        {
          model: DocumentSigner,
          as: 'signers'
        }
      ]
    });

    if (!document) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Document not found' });
    }

    // Check if document is expired
    if (document.status === 'expired' || (document.expires_at && new Date(document.expires_at) < new Date())) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Document has expired' });
    }

    // Check if document is canceled
    if (document.status === 'canceled') {
      await transaction.rollback();
      return res.status(400).json({ error: 'Document has been canceled' });
    }

    // Check if signer has already signed or declined
    if (signer.status === 'signed' || signer.status === 'declined') {
      await transaction.rollback();
      return res.status(400).json({ error: `You have already ${signer.status} this document` });
    }

    // Update signer status
    await signer.update({
      status: 'declined',
      declined_at: new Date(),
      decline_reason: reason || 'No reason provided'
    }, { transaction });

    // Update document status
    await document.update({
      status: 'declined'
    }, { transaction });

    // Create audit log entry
    await createAuditLog(
      document.id,
      null,
      signer.id,
      'document_declined',
      { 
        signer_email: signer.signer_email,
        signer_name: signer.signer_name,
        reason: reason || 'No reason provided'
      },
      req.ip,
      req.headers['user-agent'],
      transaction
    );

    // Get frontend URL and document URL for viewing
    const frontendUrl = await getFrontendUrl(null);
    const documentUrl = `${frontendUrl}/documents/view/${document.id}`;

    // Send email to document creator about the declined document
    try {
      await sendDocumentDeclinedEmail(
        document,
        signer,
        document.creator,
        documentUrl,
        reason || 'No reason provided'
      );
      console.log(`Email sent to document creator ${document.creator.email} about declined document`);
    } catch (emailError) {
      console.error(`Error sending email to document creator:`, emailError);
      // Continue with the process even if email sending fails
    }

    // Send emails to all other signers about the declined document
    for (const otherSigner of document.signers) {
      if (otherSigner.id !== signerId && otherSigner.status !== 'pending') {
        try {
          await sendDocumentDeclinedEmail(
            document,
            signer,
            { 
              first_name: otherSigner.signer_name.split(' ')[0], 
              email: otherSigner.signer_email 
            },
            documentUrl,
            reason || 'No reason provided'
          );
          console.log(`Email sent to signer ${otherSigner.signer_email} about declined document`);
        } catch (emailError) {
          console.error(`Error sending email to signer ${otherSigner.signer_email}:`, emailError);
          // Continue with the process even if email sending fails
        }
      }
    }

    await transaction.commit();

    return res.status(200).json({
      message: 'Document declined successfully'
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error declining document:', error);
    return res.status(500).json({ error: error.message });
  }
};

/**
 * Get blockchain verification details for a document
 */
export const getDocumentBlockchainVerification = async (req, res) => {
  try {
    const { id } = req.params;

    // Get document
    const document = await SignableDocument.findByPk(id);

    if (!document) {
      return res.status(404).json({ error: 'Document not found' });
    }

    // Validate farm access
    const hasAccess = await checkUserFarmAccess(req.user.id, document.farm_id);
    if (!hasAccess && !req.user.is_global_admin) {
      return res.status(403).json({ error: 'You do not have access to this document' });
    }

    // Get blockchain verification details
    const verificationDetails = await getBlockchainVerificationDetails(id);

    if (!verificationDetails) {
      return res.status(404).json({ error: 'No blockchain verification found for this document' });
    }

    return res.status(200).json(verificationDetails);
  } catch (error) {
    console.error('Error getting document blockchain verification:', error);
    return res.status(500).json({ error: error.message });
  }
};

/**
 * Verify a document against its blockchain record
 */
export const verifyDocumentWithBlockchain = async (req, res) => {
  try {
    const { id } = req.params;

    // Get document
    const document = await SignableDocument.findByPk(id);

    if (!document) {
      return res.status(404).json({ error: 'Document not found' });
    }

    // Validate farm access
    const hasAccess = await checkUserFarmAccess(req.user.id, document.farm_id);
    if (!hasAccess && !req.user.is_global_admin) {
      return res.status(403).json({ error: 'You do not have access to this document' });
    }

    // Get document content
    let documentContent;
    try {
      // First try to get the file from Spaces
      const fileExists = await fileExistsInSpaces(document.file_path);
      if (fileExists) {
        documentContent = await downloadFromSpaces(document.file_path);
      } else {
        // Fallback to local filesystem
        const filePath = path.join(uploadsDir, document.file_path);
        if (fs.existsSync(filePath)) {
          documentContent = await fs.promises.readFile(filePath);
        } else {
          return res.status(404).json({ error: 'Document file not found' });
        }
      }

      // Verify document on blockchain
      const verificationResult = await verifyDocumentOnBlockchain(id, documentContent);

      return res.status(200).json(verificationResult);
    } catch (error) {
      console.error('Error verifying document with blockchain:', error);
      return res.status(500).json({ error: error.message });
    }
  } catch (error) {
    console.error('Error verifying document with blockchain:', error);
    return res.status(500).json({ error: error.message });
  }
};

/**
 * Sign a document with a digital certificate
 */
export const signDocumentWithCertificate = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { documentId, signerId } = req.params;
    const { token } = req.query;
    const { 
      certificateId,
      signaturePosition,
      reason,
      location,
      fields = [] 
    } = req.body;

    // Validate token
    if (!token) {
      await transaction.rollback();
      return res.status(401).json({ error: 'Access token is required' });
    }

    // Get signer
    const signer = await DocumentSigner.findOne({
      where: {
        id: signerId,
        document_id: documentId,
        access_code: token
      }
    });

    if (!signer) {
      await transaction.rollback();
      return res.status(401).json({ error: 'Invalid access token' });
    }

    // Get document with signers, creator, and farm
    const document = await SignableDocument.findByPk(documentId, {
      include: [
        {
          model: DocumentSigner,
          as: 'signers',
          order: [['signer_order', 'ASC']]
        },
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'first_name', 'last_name', 'email']
        },
        {
          model: Farm,
          as: 'signableDocumentFarm',
          attributes: ['id', 'name']
        }
      ]
    });

    if (!document) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Document not found' });
    }

    // Check if document is expired
    if (document.status === 'expired' || (document.expires_at && new Date(document.expires_at) < new Date())) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Document has expired' });
    }

    // Check if document is canceled
    if (document.status === 'canceled') {
      await transaction.rollback();
      return res.status(400).json({ error: 'Document has been canceled' });
    }

    // Check if signer has already signed
    if (signer.status === 'signed') {
      await transaction.rollback();
      return res.status(400).json({ error: 'You have already signed this document' });
    }

    // Sign the document with the digital certificate
    const signResult = await signPDFWithCertificate(
      document.file_path,
      certificateId,
      signaturePosition,
      reason,
      location
    );

    // Update the document file path to the signed version
    await document.update({
      file_path: signResult.signedDocumentPath,
      has_digital_certificate_signature: true
    }, { transaction });

    // Create signature record
    const signature = await DocumentSignature.create({
      document_id: documentId,
      signer_id: signerId,
      signature_type: 'digital_certificate',
      signature_text: null,
      signature_image_path: null,
      page_number: signaturePosition?.page || 1,
      x_position: signaturePosition?.x || 0,
      y_position: signaturePosition?.y || 0,
      width: signaturePosition?.width || 200,
      height: signaturePosition?.height || 50,
      ip_address: req.ip,
      user_agent: req.headers['user-agent'],
      is_digital_certificate_signature: true,
      digital_signature_data: {
        certificateId,
        certificateInfo: signResult.certificateInfo,
        documentHash: signResult.documentHash,
        reason,
        location,
        timestamp: new Date().toISOString()
      }
    }, { transaction });

    // Update field values if provided
    if (fields.length > 0) {
      for (const field of fields) {
        await DocumentField.update(
          { field_value: field.value },
          { 
            where: { 
              id: field.id,
              document_id: documentId,
              signer_id: signerId
            },
            transaction
          }
        );
      }
    }

    // Update signer status
    await signer.update({
      status: 'signed',
      signed_at: new Date()
    }, { transaction });

    // Create audit log entry
    await createAuditLog(
      document.id,
      null,
      signer.id,
      'document_signed_with_certificate',
      { 
        signer_email: signer.signer_email,
        signer_name: signer.signer_name,
        certificate_id: certificateId,
        certificate_subject: signResult.certificateInfo.subject
      },
      req.ip,
      req.headers['user-agent'],
      transaction
    );

    // Check if all signers have signed
    const allSigners = document.signers;
    const allSigned = allSigners.every(s => s.status === 'signed' || s.id === signerId);
    const frontendUrl = await getFrontendUrl(null); // No user ID needed for public URL

    // Get document URL for viewing
    const documentUrl = `${frontendUrl}/documents/view/${document.id}`;

    // Send email to document creator about the signature
    try {
      await sendDocumentSignedEmail(
        document,
        signer,
        document.creator,
        documentUrl,
        allSigned
      );
      console.log(`Email sent to document creator ${document.creator.email} about signature`);
    } catch (emailError) {
      console.error(`Error sending email to document creator:`, emailError);
      // Continue with the process even if email sending fails
    }

    // If all signers have signed, update document status to completed
    if (allSigned) {
      await document.update({
        status: 'completed',
        completed_at: new Date()
      }, { transaction });

      // Create audit log entry for document completion
      await createAuditLog(
        document.id,
        null,
        null,
        'document_completed',
        { title: document.title },
        req.ip,
        req.headers['user-agent'],
        transaction
      );

      // Add blockchain verification for the completed document
      try {
        // Get the document file content
        let documentContent;
        try {
          // First try to get the file from Spaces
          const fileExists = await fileExistsInSpaces(document.file_path);
          if (fileExists) {
            documentContent = await downloadFromSpaces(document.file_path);
          } else {
            // Fallback to local filesystem
            const filePath = path.join(uploadsDir, document.file_path);
            if (fs.existsSync(filePath)) {
              documentContent = await fs.promises.readFile(filePath);
            } else {
              throw new Error('Document file not found');
            }
          }

          // Create blockchain verification
          const verification = await createBlockchainVerification(document.id, documentContent);

          // Create audit log entry for blockchain verification
          await createAuditLog(
            document.id,
            null,
            null,
            'blockchain_verification_created',
            { 
              transaction_hash: verification.transaction_hash,
              blockchain_network: verification.blockchain_network,
              status: verification.verification_status
            },
            req.ip,
            req.headers['user-agent'],
            transaction
          );
        } catch (blockchainError) {
          console.error('Error creating blockchain verification:', blockchainError);
          // Continue with the process even if blockchain verification fails
        }
      } catch (contentError) {
        console.error('Error getting document content for blockchain verification:', contentError);
        // Continue with the process even if getting document content fails
      }
    }

    await transaction.commit();

    return res.status(200).json({
      message: 'Document signed with digital certificate successfully',
      signature,
      document: {
        id: document.id,
        title: document.title,
        status: allSigned ? 'completed' : document.status,
        file_path: document.file_path
      }
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error signing document with certificate:', error);
    return res.status(500).json({ error: error.message });
  }
};

/**
 * Verify a document's digital signature
 */
export const verifyDocumentDigitalSignature = async (req, res) => {
  try {
    const { id } = req.params;

    // Get document
    const document = await SignableDocument.findByPk(id);

    if (!document) {
      return res.status(404).json({ error: 'Document not found' });
    }

    // Validate farm access
    const hasAccess = await checkUserFarmAccess(req.user.id, document.farm_id);
    if (!hasAccess && !req.user.is_global_admin) {
      return res.status(403).json({ error: 'You do not have access to this document' });
    }

    // Check if document has digital certificate signature
    if (!document.has_digital_certificate_signature) {
      return res.status(400).json({ error: 'Document does not have a digital certificate signature' });
    }

    // Verify the digital signature
    const verificationResult = await verifyDigitalSignature(document.file_path);

    return res.status(200).json(verificationResult);
  } catch (error) {
    console.error('Error verifying document digital signature:', error);
    return res.status(500).json({ error: error.message });
  }
};
