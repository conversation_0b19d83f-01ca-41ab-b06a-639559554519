import { sequelize } from '../config/database.js';
import Invoice from '../models/Invoice.js';
import Customer from '../models/Customer.js';
import Farm from '../models/Farm.js';

/**
 * Test script to verify that the Invoice-Customer association fix is working
 * This script tests the corrected alias usage (lowercase 'customer' instead of uppercase 'Customer')
 */

async function testInvoiceCustomerAssociation() {
  try {
    console.log('🧪 Testing Invoice-Customer Association Fix...\n');

    // Test 1: Query Invoice with Customer using correct alias
    console.log('Test 1: Query Invoice with Customer using correct alias (lowercase)');
    try {
      const invoiceWithCustomer = await Invoice.findOne({
        include: [
          {
            model: Customer,
            as: 'customer', // Using lowercase alias as defined in associations.js
            required: false,
            attributes: ['id', 'name', 'email']
          }
        ],
        limit: 1
      });

      if (invoiceWithCustomer) {
        console.log('✅ SUCCESS: Invoice query with customer association worked');
        console.log(`   Invoice #${invoiceWithCustomer.invoice_number}`);
        console.log(`   Customer: ${invoiceWithCustomer.customer ? invoiceWithCustomer.customer.name : 'None'}`);
      } else {
        console.log('ℹ️  No invoices found in database');
      }
    } catch (error) {
      console.log('❌ FAILED: Invoice query with customer association failed');
      console.log(`   Error: ${error.message}`);
    }

    console.log('');

    // Test 2: Query Customer with Invoices
    console.log('Test 2: Query Customer with Invoices');
    try {
      const customerWithInvoices = await Customer.findOne({
        include: [
          {
            model: Invoice,
            as: 'invoices', // This should work as defined in associations.js
            required: false,
            attributes: ['id', 'invoice_number', 'status', 'total_amount']
          }
        ],
        limit: 1
      });

      if (customerWithInvoices) {
        console.log('✅ SUCCESS: Customer query with invoices association worked');
        console.log(`   Customer: ${customerWithInvoices.name}`);
        console.log(`   Number of invoices: ${customerWithInvoices.invoices ? customerWithInvoices.invoices.length : 0}`);
      } else {
        console.log('ℹ️  No customers found in database');
      }
    } catch (error) {
      console.log('❌ FAILED: Customer query with invoices association failed');
      console.log(`   Error: ${error.message}`);
    }

    console.log('');

    // Test 3: Test the specific query pattern that was failing before the fix
    console.log('Test 3: Test complex Invoice query with multiple associations');
    try {
      const complexInvoiceQuery = await Invoice.findOne({
        include: [
          {
            model: Customer,
            as: 'customer', // Correct lowercase alias
            required: false,
            attributes: ['id', 'name', 'email', 'phone']
          },
          {
            model: Farm,
            as: 'issuingFarm',
            required: false,
            attributes: ['id', 'name']
          }
        ],
        limit: 1
      });

      if (complexInvoiceQuery) {
        console.log('✅ SUCCESS: Complex invoice query with multiple associations worked');
        console.log(`   Invoice #${complexInvoiceQuery.invoice_number}`);
        console.log(`   Customer: ${complexInvoiceQuery.customer ? complexInvoiceQuery.customer.name : 'None'}`);
        console.log(`   Issuing Farm: ${complexInvoiceQuery.issuingFarm ? complexInvoiceQuery.issuingFarm.name : 'None'}`);
      } else {
        console.log('ℹ️  No invoices found in database');
      }
    } catch (error) {
      console.log('❌ FAILED: Complex invoice query failed');
      console.log(`   Error: ${error.message}`);
    }

    console.log('');

    // Test 4: Verify that the old uppercase alias would fail (this should fail)
    console.log('Test 4: Verify that uppercase alias fails (expected to fail)');
    try {
      const badQuery = await Invoice.findOne({
        include: [
          {
            model: Customer,
            as: 'Customer', // Using incorrect uppercase alias
            required: false,
            attributes: ['id', 'name', 'email']
          }
        ],
        limit: 1
      });

      console.log('❌ UNEXPECTED: Uppercase alias query succeeded (this should have failed)');
    } catch (error) {
      console.log('✅ EXPECTED: Uppercase alias query failed as expected');
      console.log(`   Error: ${error.message}`);
    }

    console.log('\n🎉 Association fix test completed!');
    console.log('The Invoice-Customer association should now work correctly with the lowercase "customer" alias.');

  } catch (error) {
    console.error('❌ Test script failed:', error);
  } finally {
    // Close database connection
    await sequelize.close();
  }
}

// Run the test
testInvoiceCustomerAssociation();
