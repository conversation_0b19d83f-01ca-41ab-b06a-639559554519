import { sequelize } from '../config/database.js';
import Invoice from '../models/Invoice.js';
import Customer from '../models/Customer.js';
import { setupAssociations } from '../models/associations.js';

/**
 * Debug script to understand the association structure
 */

async function debugAssociations() {
  try {
    console.log('🔍 Debugging Association Structure...\n');

    // Set up associations first
    setupAssociations();

    // Check Invoice associations in detail
    console.log('📋 Invoice Associations:');
    console.log('Association names:', Object.keys(Invoice.associations));
    
    if (Invoice.associations.customer) {
      console.log('✅ Invoice.customer association exists');
      console.log('   Type:', Invoice.associations.customer.associationType);
      console.log('   Target:', Invoice.associations.customer.target.name);
      console.log('   Foreign Key:', Invoice.associations.customer.foreignKey);
      console.log('   As:', Invoice.associations.customer.as);
    } else {
      console.log('❌ Invoice.customer association missing');
    }

    console.log('');

    // Check Customer associations in detail
    console.log('📋 Customer Associations:');
    console.log('Association names:', Object.keys(Customer.associations));
    
    if (Customer.associations.invoices) {
      console.log('✅ Customer.invoices association exists');
      console.log('   Type:', Customer.associations.invoices.associationType);
      console.log('   Target:', Customer.associations.invoices.target.name);
      console.log('   Foreign Key:', Customer.associations.invoices.foreignKey);
      console.log('   As:', Customer.associations.invoices.as);
    } else {
      console.log('❌ Customer.invoices association missing');
    }

    console.log('');

    // Try a simple query to test the association
    console.log('🧪 Testing Simple Query...');
    try {
      const invoice = await Invoice.findOne({
        include: [
          {
            model: Customer,
            as: 'customer',
            required: false
          }
        ],
        limit: 1
      });

      if (invoice) {
        console.log('✅ Query succeeded!');
        console.log(`   Invoice #${invoice.invoice_number}`);
        console.log(`   Customer: ${invoice.customer ? invoice.customer.name : 'None'}`);
      } else {
        console.log('ℹ️  No invoices found in database');
      }
    } catch (error) {
      console.log('❌ Query failed:', error.message);
      
      // Try to understand the error better
      if (error.message.includes('not associated')) {
        console.log('💡 This is an association error. Let\'s check the model registration...');
        
        // Check if models are properly registered with Sequelize
        console.log('   Invoice model in sequelize.models:', !!sequelize.models.Invoice);
        console.log('   Customer model in sequelize.models:', !!sequelize.models.Customer);
        
        // Check if the imported models are the same as the ones in sequelize.models
        console.log('   Invoice === sequelize.models.Invoice:', Invoice === sequelize.models.Invoice);
        console.log('   Customer === sequelize.models.Customer:', Customer === sequelize.models.Customer);
      }
    }

    console.log('');

    // Try using sequelize.models instead of imported models
    console.log('🧪 Testing with sequelize.models...');
    try {
      const InvoiceModel = sequelize.models.Invoice;
      const CustomerModel = sequelize.models.Customer;
      
      const invoice = await InvoiceModel.findOne({
        include: [
          {
            model: CustomerModel,
            as: 'customer',
            required: false
          }
        ],
        limit: 1
      });

      if (invoice) {
        console.log('✅ Query with sequelize.models succeeded!');
        console.log(`   Invoice #${invoice.invoice_number}`);
        console.log(`   Customer: ${invoice.customer ? invoice.customer.name : 'None'}`);
      } else {
        console.log('ℹ️  No invoices found in database');
      }
    } catch (error) {
      console.log('❌ Query with sequelize.models failed:', error.message);
    }

  } catch (error) {
    console.error('❌ Debug script failed:', error);
  } finally {
    // Close database connection
    await sequelize.close();
  }
}

// Run the debug
debugAssociations();
