#!/usr/bin/env node

/**
 * Test script to verify that invoice customer associations work correctly
 */

import { sequelize } from '../config/database.js';
import Invoice from '../models/Invoice.js';
import Customer from '../models/Customer.js';
import Farm from '../models/Farm.js';
import InvoiceItem from '../models/InvoiceItem.js';
import { setupAssociations } from '../models/associations.js';

async function testInvoiceAssociations() {
  try {
    console.log('Testing invoice customer associations...');

    // Set up associations
    setupAssociations();

    // Test 1: Find an invoice and try to include Customer with required: false
    console.log('\nTest 1: Finding invoice with Customer association (required: false)');

    const invoice = await Invoice.findOne({
      include: [
        {
          model: Customer,
          as: 'Customer',
          required: false,
          attributes: ['id', 'name', 'email']
        },
        {
          model: Farm,
          as: 'issuingFarm',
          required: false,
          attributes: ['id', 'name']
        },
        {
          model: InvoiceItem,
          required: false,
          attributes: ['id', 'description', 'quantity', 'unit_price', 'amount']
        }
      ],
      limit: 1
    });

    if (invoice) {
      console.log('✅ Successfully found invoice with associations');
      console.log(`Invoice #${invoice.invoice_number}`);
      console.log(`Customer: ${invoice.Customer ? invoice.Customer.name : 'None'}`);
      console.log(`Farm: ${invoice.issuingFarm ? invoice.issuingFarm.name : 'None'}`);
      console.log(`Items: ${invoice.InvoiceItems ? invoice.InvoiceItems.length : 0}`);
    } else {
      console.log('⚠️  No invoices found in database');
    }

    // Test 2: Check for invoices without customer associations
    console.log('\nTest 2: Checking for invoices without customer associations');

    const orphanedInvoices = await Invoice.findAll({
      where: {
        customer_id: null,
        recipient_farm_id: null // Exclude farm-to-farm invoices
      },
      attributes: ['id', 'invoice_number', 'farm_id'],
      limit: 5
    });

    console.log(`Found ${orphanedInvoices.length} invoices without customer associations`);

    if (orphanedInvoices.length > 0) {
      console.log('⚠️  These invoices may cause association errors:');
      orphanedInvoices.forEach(inv => {
        console.log(`  - Invoice #${inv.invoice_number} (ID: ${inv.id})`);
      });
    } else {
      console.log('✅ All invoices have proper customer or farm associations');
    }

    // Test 3: Test the specific query that was failing
    console.log('\nTest 3: Testing the specific query pattern that was failing');

    try {
      const testInvoice = await Invoice.findOne({
        include: [
          {
            model: Customer,
            as: 'customer',
            attributes: ['id', 'name', 'email'],
            required: false // This should prevent the association error
          }
        ]
      });

      console.log('✅ Query with Customer include (required: false) succeeded');

      if (testInvoice) {
        console.log(`Found invoice #${testInvoice.invoice_number}`);
        console.log(`Customer: ${testInvoice.customer ? testInvoice.customer.name : 'None'}`);
      }
    } catch (error) {
      console.error('❌ Query with Customer include failed:', error.message);
    }

    // Test 4: Test without required: false to see if it fails
    console.log('\nTest 4: Testing query without required: false (should fail if orphaned invoices exist)');

    try {
      const testInvoice2 = await Invoice.findOne({
        include: [
          {
            model: Customer,
            as: 'Customer',
            attributes: ['id', 'name', 'email']
            // No required: false - this might fail if there are orphaned invoices
          }
        ]
      });

      console.log('✅ Query with Customer include (required: true) succeeded');

      if (testInvoice2) {
        console.log(`Found invoice #${testInvoice2.invoice_number}`);
        console.log(`Customer: ${testInvoice2.Customer ? testInvoice2.Customer.name : 'None'}`);
      }
    } catch (error) {
      console.error('❌ Query with Customer include (required: true) failed:', error.message);
      console.log('This confirms there are orphaned invoices that need to be fixed');
    }

    console.log('\n=== TEST SUMMARY ===');
    console.log('If you see any ❌ errors above, run the fix script:');
    console.log('node webapp/server/scripts/fix_invoice_customer_associations.js');

  } catch (error) {
    console.error('Test failed:', error);
    process.exit(1);
  }
}

// Run the test if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  testInvoiceAssociations()
    .then(() => {
      console.log('\nTest completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Test failed:', error);
      process.exit(1);
    });
}

export default testInvoiceAssociations;
