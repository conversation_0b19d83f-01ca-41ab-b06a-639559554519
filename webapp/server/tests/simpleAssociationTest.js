import { sequelize } from '../config/database.js';
import { setupAssociations } from '../models/associations.js';

/**
 * Simple test to verify associations work
 */

async function simpleTest() {
  try {
    console.log('🧪 Simple Association Test...\n');

    // Set up associations
    setupAssociations();

    // Get models from sequelize.models (these should be the registered ones)
    const Invoice = sequelize.models.Invoice;
    const Customer = sequelize.models.Customer;

    console.log('Models found:');
    console.log('  Invoice:', !!Invoice);
    console.log('  Customer:', !!Customer);

    if (!Invoice || !Customer) {
      console.log('❌ Models not found in sequelize.models');
      return;
    }

    console.log('\nAssociations:');
    console.log('  Invoice.customer:', !!Invoice.associations.customer);
    console.log('  Customer.invoices:', !!Customer.associations.invoices);

    // Test database connection first
    console.log('\n🔌 Testing database connection...');
    await sequelize.authenticate();
    console.log('✅ Database connection successful');

    // Try a simple query without associations first
    console.log('\n📊 Testing simple queries...');
    
    const invoiceCount = await Invoice.count();
    console.log(`Found ${invoiceCount} invoices in database`);
    
    const customerCount = await Customer.count();
    console.log(`Found ${customerCount} customers in database`);

    if (invoiceCount === 0) {
      console.log('ℹ️  No invoices in database to test associations with');
      return;
    }

    // Now try the association query
    console.log('\n🔗 Testing association query...');
    
    const invoice = await Invoice.findOne({
      include: [
        {
          model: Customer,
          as: 'customer',
          required: false
        }
      ]
    });

    if (invoice) {
      console.log('✅ Association query successful!');
      console.log(`   Invoice: ${invoice.invoice_number}`);
      console.log(`   Customer: ${invoice.customer ? invoice.customer.name : 'None'}`);
    } else {
      console.log('ℹ️  Query returned no results');
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack trace:', error.stack);
  } finally {
    await sequelize.close();
  }
}

// Run the test
simpleTest();
