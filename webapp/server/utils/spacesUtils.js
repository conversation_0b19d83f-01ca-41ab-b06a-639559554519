import { S3Client } from '@aws-sdk/client-s3';
import { PutObjectCommand, GetObjectCommand, DeleteObjectCommand, HeadObjectCommand } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import fs from 'fs';
import path from 'path';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

/**
 * Determines the content type based on file extension
 * @param {string} filePath - The file path or name
 * @returns {string} The content type
 */
const getContentType = (filePath) => {
  const ext = path.extname(filePath).toLowerCase();
  const contentTypes = {
    '.pdf': 'application/pdf',
    '.jpg': 'image/jpeg',
    '.jpeg': 'image/jpeg',
    '.png': 'image/png',
    '.gif': 'image/gif',
    '.svg': 'image/svg+xml',
    '.txt': 'text/plain',
    '.html': 'text/html',
    '.htm': 'text/html',
    '.css': 'text/css',
    '.js': 'application/javascript',
    '.json': 'application/json',
    '.xml': 'application/xml',
    '.zip': 'application/zip',
    '.doc': 'application/msword',
    '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    '.xls': 'application/vnd.ms-excel',
    '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    '.ppt': 'application/vnd.ms-powerpoint',
    '.pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    '.csv': 'text/csv'
  };

  return contentTypes[ext] || 'application/octet-stream'; // Default to binary if unknown
};

// Get configuration from environment variables
const SPACES_REGION = process.env.SPACES_REGION || 'nyc3';
const SPACES_NAME = process.env.SPACES_NAME || 'nxtacre';
const SPACES_KEY = process.env.SPACES_KEY;
const SPACES_SECRET = process.env.SPACES_SECRET;

// Check if required environment variables are set
if (!SPACES_KEY || !SPACES_SECRET) {
  console.error('Error: SPACES_KEY or SPACES_SECRET environment variables are not set');
  console.error('Current values:', { SPACES_KEY, SPACES_SECRET });
  // Use placeholder values for development to avoid crashes
  // In production, these should be properly set
  console.warn('Using placeholder values for development. This will not work in production.');
}

// Ensure we have values for the keys (even if they're placeholders)
const accessKeyId = SPACES_KEY || 'placeholder_key';
const secretAccessKey = SPACES_SECRET || 'placeholder_secret';

// Get the endpoint URL from environment variables or construct it
const SPACES_ENDPOINT = process.env.SPACES_ENDPOINT || `https://${SPACES_REGION}.digitaloceanspaces.com`;

// Create S3 client using AWS SDK v3
const s3Client = new S3Client({
  region: SPACES_REGION,
  endpoint: SPACES_ENDPOINT,
  credentials: {
    accessKeyId,
    secretAccessKey
  },
  forcePathStyle: false, // Set to false for Digital Ocean Spaces
});

/**
 * Uploads a file to Digital Ocean Spaces
 * @param {Buffer} fileData - The file data buffer
 * @param {string} storagePath - The storage path in Spaces
 * @returns {Promise<string>} The path of the uploaded file
 */
export const uploadToSpaces = async (fileData, storagePath) => {
  try {
    // Ensure fileData is a buffer
    if (typeof fileData === 'string') {
      throw new Error('File data must be a buffer, not a path to a temp file');
    }

    const fileBuffer = fileData;

    // Normalize the storage path to use forward slashes
    const normalizedPath = storagePath.replace(/\\/g, '/');

    // Create upload parameters
    const uploadParams = {
      Bucket: SPACES_NAME,
      Key: normalizedPath,
      Body: fileBuffer,
      ContentType: getContentType(normalizedPath),
      ACL: 'public-read' // Make files publicly accessible
    };

    // Upload to Spaces using AWS SDK v3
    const command = new PutObjectCommand(uploadParams);
    await s3Client.send(command);

    // Return the path (not the full URL, to be consistent with local storage)
    return normalizedPath;
  } catch (error) {
    console.error('Error uploading to Spaces:', error);
    throw error;
  }
};

/**
 * Downloads a file from Digital Ocean Spaces
 * @param {string} storagePath - The storage path in Spaces
 * @returns {Promise<Buffer>} The file data
 */
export const downloadFromSpaces = async (storagePath) => {
  try {
    // Normalize the storage path to use forward slashes
    const normalizedPath = storagePath.replace(/\\/g, '/');

    // Create download parameters
    const downloadParams = {
      Bucket: SPACES_NAME,
      Key: normalizedPath
    };

    // Download from Spaces using AWS SDK v3
    const command = new GetObjectCommand(downloadParams);
    const response = await s3Client.send(command);

    // Convert the readable stream to a buffer
    const chunks = [];
    for await (const chunk of response.Body) {
      chunks.push(chunk);
    }

    // Return the file data as a buffer
    return Buffer.concat(chunks);
  } catch (error) {
    console.error('Error downloading from Spaces:', error);
    throw error;
  }
};

/**
 * Deletes a file from Digital Ocean Spaces
 * @param {string} storagePath - The storage path in Spaces
 * @returns {Promise<boolean>} Whether the deletion was successful
 */
export const deleteFromSpaces = async (storagePath) => {
  try {
    // Normalize the storage path to use forward slashes
    const normalizedPath = storagePath.replace(/\\/g, '/');

    // Create delete parameters
    const deleteParams = {
      Bucket: SPACES_NAME,
      Key: normalizedPath
    };

    // Delete from Spaces using AWS SDK v3
    const command = new DeleteObjectCommand(deleteParams);
    await s3Client.send(command);

    return true;
  } catch (error) {
    console.error('Error deleting from Spaces:', error);
    return false;
  }
};

/**
 * Checks if a file exists in Digital Ocean Spaces
 * @param {string} storagePath - The storage path in Spaces
 * @returns {Promise<boolean>} Whether the file exists
 */
export const fileExistsInSpaces = async (storagePath) => {
  try {
    // Normalize the storage path to use forward slashes
    const normalizedPath = storagePath.replace(/\\/g, '/');

    // Create head parameters
    const headParams = {
      Bucket: SPACES_NAME,
      Key: normalizedPath
    };

    // Check if file exists using AWS SDK v3
    try {
      const command = new HeadObjectCommand(headParams);
      await s3Client.send(command);
      return true;
    } catch (error) {
      if (error.name === 'NotFound' || error.$metadata?.httpStatusCode === 404) {
        return false;
      }
      throw error;
    }
  } catch (error) {
    console.error('Error checking if file exists in Spaces:', error);
    throw error;
  }
};

/**
 * Gets the URL for a file in Digital Ocean Spaces
 * @param {string} storagePath - The storage path in Spaces
 * @returns {string} The URL of the file
 */
export const getSpacesUrl = (storagePath) => {
  // Normalize the storage path to use forward slashes
  const normalizedPath = storagePath.replace(/\\/g, '/');

  // Construct the URL using the bucket name and region
  return `https://${SPACES_NAME}.${SPACES_REGION}.digitaloceanspaces.com/${normalizedPath}`;
};

/**
 * Gets a signed URL for temporary access to a file
 * @param {string} storagePath - The storage path in Spaces
 * @param {number} expiresIn - Expiration time in seconds (default: 3600)
 * @returns {Promise<string>} The signed URL
 */
export const getSignedSpacesUrl = async (storagePath, expiresIn = 3600) => {
  try {
    // Normalize the storage path to use forward slashes
    const normalizedPath = storagePath.replace(/\\/g, '/');

    // Create get object parameters
    const params = {
      Bucket: SPACES_NAME,
      Key: normalizedPath
    };

    // Generate signed URL using AWS SDK v3
    const command = new GetObjectCommand(params);
    const signedUrl = await getSignedUrl(s3Client, command, { expiresIn });

    return signedUrl;
  } catch (error) {
    console.error('Error generating signed URL:', error);
    throw error;
  }
};

/**
 * Creates an empty folder in Digital Ocean Spaces
 * @param {string} folderPath - The folder path in Spaces (should end with a slash)
 * @returns {Promise<boolean>} Whether the folder creation was successful
 */
export const createFolderInSpaces = async (folderPath) => {
  try {
    console.log(`Creating folder in Spaces with path: ${folderPath}`);

    // Normalize the folder path to use forward slashes and ensure it ends with a slash
    let normalizedPath = folderPath.replace(/\\/g, '/');
    if (!normalizedPath.endsWith('/')) {
      normalizedPath += '/';
    }

    console.log(`Normalized path: ${normalizedPath}`);

    // Create a zero-byte object with the folder path as the key
    // This serves as a folder marker in object storage
    const uploadParams = {
      Bucket: SPACES_NAME,
      Key: normalizedPath,
      Body: '',
      ContentType: 'application/x-directory',
      ACL: 'private'
    };

    // Upload to Spaces using AWS SDK v3
    const command = new PutObjectCommand(uploadParams);
    await s3Client.send(command);

    console.log(`Successfully created folder in Spaces: ${normalizedPath}`);

    return true;
  } catch (error) {
    console.error(`Error creating folder in Spaces (${folderPath}):`, error);
    return false;
  }
};

/**
 * Deletes a folder from Digital Ocean Spaces
 * @param {string} folderPath - The folder path in Spaces (should end with a slash)
 * @returns {Promise<boolean>} Whether the folder deletion was successful
 */
export const deleteFolderInSpaces = async (folderPath) => {
  try {
    // Normalize the folder path to use forward slashes and ensure it ends with a slash
    let normalizedPath = folderPath.replace(/\\/g, '/');
    if (!normalizedPath.endsWith('/')) {
      normalizedPath += '/';
    }

    // Delete the folder marker object
    const deleteParams = {
      Bucket: SPACES_NAME,
      Key: normalizedPath
    };

    // Delete from Spaces using AWS SDK v3
    const command = new DeleteObjectCommand(deleteParams);
    await s3Client.send(command);

    return true;
  } catch (error) {
    console.error('Error deleting folder in Spaces:', error);
    return false;
  }
};

export default {
  uploadToSpaces,
  downloadFromSpaces,
  deleteFromSpaces,
  fileExistsInSpaces,
  getSpacesUrl,
  getSignedSpacesUrl,
  createFolderInSpaces,
  deleteFolderInSpaces
};
