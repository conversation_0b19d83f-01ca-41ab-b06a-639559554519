import crypto from 'crypto';
import fs from 'fs';
import { promisify } from 'util';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Convert fs functions to promise-based
const readFile = promisify(fs.readFile);
const writeFile = promisify(fs.writeFile);

// Default encryption algorithm
const DEFAULT_ALGORITHM = 'aes-256-cbc';

// Get master key from environment variables or use a default (for development only)
// In production, this should be securely stored and rotated
const MASTER_KEY = process.env.DOCUMENT_ENCRYPTION_MASTER_KEY || 
  '0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef';

/**
 * Generate a random encryption key
 * @returns {string} Hex-encoded encryption key
 */
export const generateEncryptionKey = () => {
  return crypto.randomBytes(32).toString('hex');
};

/**
 * Generate a random initialization vector
 * @returns {string} Hex-encoded initialization vector
 */
export const generateIV = () => {
  return crypto.randomBytes(16).toString('hex');
};

/**
 * Encrypt a key with the master key
 * @param {string} key - The key to encrypt
 * @returns {string} The encrypted key
 */
export const encryptKey = (key) => {
  const iv = crypto.randomBytes(16);
  const cipher = crypto.createCipheriv(DEFAULT_ALGORITHM, Buffer.from(MASTER_KEY, 'hex'), iv);
  let encrypted = cipher.update(key, 'utf8', 'hex');
  encrypted += cipher.final('hex');
  return `${iv.toString('hex')}:${encrypted}`;
};

/**
 * Decrypt a key with the master key
 * @param {string} encryptedKey - The encrypted key
 * @returns {string} The decrypted key
 */
export const decryptKey = (encryptedKey) => {
  const [ivHex, encrypted] = encryptedKey.split(':');
  const iv = Buffer.from(ivHex, 'hex');
  const decipher = crypto.createDecipheriv(DEFAULT_ALGORITHM, Buffer.from(MASTER_KEY, 'hex'), iv);
  let decrypted = decipher.update(encrypted, 'hex', 'utf8');
  decrypted += decipher.final('utf8');
  return decrypted;
};

/**
 * Encrypt a buffer
 * @param {Buffer} buffer - The buffer to encrypt
 * @param {string} key - The encryption key (hex-encoded)
 * @param {string} iv - The initialization vector (hex-encoded)
 * @param {string} algorithm - The encryption algorithm to use
 * @returns {Buffer} The encrypted buffer
 */
export const encryptBuffer = (buffer, key, iv, algorithm = DEFAULT_ALGORITHM) => {
  const keyBuffer = Buffer.from(key, 'hex');
  const ivBuffer = Buffer.from(iv, 'hex');
  const cipher = crypto.createCipheriv(algorithm, keyBuffer, ivBuffer);
  const encrypted = Buffer.concat([cipher.update(buffer), cipher.final()]);
  return encrypted;
};

/**
 * Decrypt a buffer
 * @param {Buffer} encryptedBuffer - The encrypted buffer
 * @param {string} key - The encryption key (hex-encoded)
 * @param {string} iv - The initialization vector (hex-encoded)
 * @param {string} algorithm - The encryption algorithm to use
 * @returns {Buffer} The decrypted buffer
 */
export const decryptBuffer = (encryptedBuffer, key, iv, algorithm = DEFAULT_ALGORITHM) => {
  const keyBuffer = Buffer.from(key, 'hex');
  const ivBuffer = Buffer.from(iv, 'hex');
  const decipher = crypto.createDecipheriv(algorithm, keyBuffer, ivBuffer);
  const decrypted = Buffer.concat([decipher.update(encryptedBuffer), decipher.final()]);
  return decrypted;
};

/**
 * Encrypt a file
 * @param {string|Buffer} fileData - The file data or path to file
 * @returns {Promise<{encryptedData: Buffer, key: string, iv: string, method: string}>}
 */
export const encryptFile = async (fileData) => {
  try {
    // If fileData is a string, it's a path to a file
    let buffer;
    if (typeof fileData === 'string') {
      buffer = await readFile(fileData);
    } else {
      buffer = fileData;
    }

    // Generate encryption key and IV
    const key = generateEncryptionKey();
    const iv = generateIV();
    
    // Encrypt the buffer
    const encryptedData = encryptBuffer(buffer, key, iv);
    
    // Encrypt the key with the master key for secure storage
    const encryptedKey = encryptKey(key);
    
    return {
      encryptedData,
      key: encryptedKey,
      iv,
      method: DEFAULT_ALGORITHM
    };
  } catch (error) {
    console.error('Error encrypting file:', error);
    throw error;
  }
};

/**
 * Decrypt a file
 * @param {Buffer} encryptedData - The encrypted file data
 * @param {string} encryptedKey - The encrypted key
 * @param {string} iv - The initialization vector
 * @param {string} method - The encryption method used
 * @returns {Promise<Buffer>} The decrypted file data
 */
export const decryptFile = async (encryptedData, encryptedKey, iv, method = DEFAULT_ALGORITHM) => {
  try {
    // Decrypt the key
    const key = decryptKey(encryptedKey);
    
    // Decrypt the data
    const decryptedData = decryptBuffer(encryptedData, key, iv, method);
    
    return decryptedData;
  } catch (error) {
    console.error('Error decrypting file:', error);
    throw error;
  }
};

export default {
  generateEncryptionKey,
  generateIV,
  encryptKey,
  decryptKey,
  encryptBuffer,
  decryptBuffer,
  encryptFile,
  decryptFile
};