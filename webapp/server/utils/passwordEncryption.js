/**
 * Password Manager Encryption Utilities
 * 
 * This module provides utilities for end-to-end encryption of password data.
 * It uses AES-256-GCM for encryption and PBKDF2 for key derivation.
 * 
 * The encryption is designed to be secure even if the database is compromised:
 * - User passwords are never stored in plaintext
 * - Encryption keys are derived from user passwords and never stored
 * - Each password entry has its own initialization vector
 * - Recovery keys are encrypted with a separate mechanism
 */

import crypto from 'crypto';
import dotenv from 'dotenv';

dotenv.config();

// Constants
const ENCRYPTION_ALGORITHM = 'aes-256-gcm';
const KEY_LENGTH = 32; // 256 bits
const IV_LENGTH = 16; // 128 bits
const AUTH_TAG_LENGTH = 16; // 128 bits
const SALT_LENGTH = 64;
const PBKDF2_ITERATIONS = 100000;
const PBKDF2_DIGEST = 'sha512';

// Environment variables
const MASTER_KEY = process.env.PASSWORD_ENCRYPTION_MASTER_KEY;
if (!MASTER_KEY) {
  console.warn('WARNING: PASSWORD_ENCRYPTION_MASTER_KEY not set. Using a default key for development. DO NOT USE IN PRODUCTION!');
}

// Fallback key for development (DO NOT USE IN PRODUCTION)
const DEFAULT_DEV_KEY = 'default-dev-key-do-not-use-in-production-environment';

/**
 * Derives an encryption key from a user's master password
 * @param {string} masterPassword - User's master password
 * @param {string} salt - Salt for key derivation
 * @returns {Buffer} Derived key
 */
export const deriveKeyFromPassword = (masterPassword, salt) => {
  return crypto.pbkdf2Sync(
    masterPassword,
    salt,
    PBKDF2_ITERATIONS,
    KEY_LENGTH,
    PBKDF2_DIGEST
  );
};

/**
 * Generates a random salt for key derivation
 * @returns {string} Base64-encoded salt
 */
export const generateSalt = () => {
  return crypto.randomBytes(SALT_LENGTH).toString('base64');
};

/**
 * Encrypts data using AES-256-GCM
 * @param {string} data - Data to encrypt
 * @param {Buffer} key - Encryption key
 * @returns {Object} Encrypted data, IV, and auth tag
 */
export const encrypt = (data, key) => {
  const iv = crypto.randomBytes(IV_LENGTH);
  const cipher = crypto.createCipheriv(ENCRYPTION_ALGORITHM, key, iv);
  
  let encrypted = cipher.update(data, 'utf8', 'base64');
  encrypted += cipher.final('base64');
  
  const authTag = cipher.getAuthTag();
  
  return {
    encrypted: encrypted,
    iv: iv.toString('base64'),
    authTag: authTag.toString('base64')
  };
};

/**
 * Decrypts data using AES-256-GCM
 * @param {string} encryptedData - Base64-encoded encrypted data
 * @param {string} iv - Base64-encoded initialization vector
 * @param {string} authTag - Base64-encoded authentication tag
 * @param {Buffer} key - Decryption key
 * @returns {string} Decrypted data
 */
export const decrypt = (encryptedData, iv, authTag, key) => {
  const decipher = crypto.createDecipheriv(
    ENCRYPTION_ALGORITHM,
    key,
    Buffer.from(iv, 'base64')
  );
  
  decipher.setAuthTag(Buffer.from(authTag, 'base64'));
  
  let decrypted = decipher.update(encryptedData, 'base64', 'utf8');
  decrypted += decipher.final('utf8');
  
  return decrypted;
};

/**
 * Encrypts a password entry
 * @param {Object} passwordData - Password data to encrypt
 * @param {string} masterPassword - User's master password
 * @returns {Object} Encrypted password entry
 */
export const encryptPasswordEntry = (passwordData, masterPassword) => {
  // Generate a salt for key derivation
  const salt = generateSalt();
  
  // Derive encryption key from master password
  const key = deriveKeyFromPassword(masterPassword, salt);
  
  // Encrypt sensitive fields
  const encryptedFields = {};
  ['username', 'password', 'url', 'notes', 'totp_secret'].forEach(field => {
    if (passwordData[field]) {
      const { encrypted, iv, authTag } = encrypt(passwordData[field], key);
      encryptedFields[field] = encrypted;
      encryptedFields[`${field}_iv`] = iv;
      encryptedFields[`${field}_auth_tag`] = authTag;
    }
  });
  
  // Store encryption metadata
  encryptedFields.encryption_key_id = salt;
  
  return {
    ...passwordData,
    ...encryptedFields
  };
};

/**
 * Decrypts a password entry
 * @param {Object} encryptedEntry - Encrypted password entry
 * @param {string} masterPassword - User's master password
 * @returns {Object} Decrypted password entry
 */
export const decryptPasswordEntry = (encryptedEntry, masterPassword) => {
  // Get the salt used for key derivation
  const salt = encryptedEntry.encryption_key_id;
  
  // Derive decryption key from master password
  const key = deriveKeyFromPassword(masterPassword, salt);
  
  // Decrypt sensitive fields
  const decryptedEntry = { ...encryptedEntry };
  
  ['username', 'password', 'url', 'notes', 'totp_secret'].forEach(field => {
    if (encryptedEntry[field] && encryptedEntry[`${field}_iv`] && encryptedEntry[`${field}_auth_tag`]) {
      try {
        decryptedEntry[field] = decrypt(
          encryptedEntry[field],
          encryptedEntry[`${field}_iv`],
          encryptedEntry[`${field}_auth_tag`],
          key
        );
      } catch (error) {
        console.error(`Error decrypting ${field}:`, error);
        decryptedEntry[field] = null;
      }
      
      // Remove encryption metadata from result
      delete decryptedEntry[`${field}_iv`];
      delete decryptedEntry[`${field}_auth_tag`];
    }
  });
  
  // Remove encryption metadata from result
  delete decryptedEntry.encryption_key_id;
  
  return decryptedEntry;
};

/**
 * Generates a recovery key for a user
 * @returns {string} Recovery key
 */
export const generateRecoveryKey = () => {
  return crypto.randomBytes(32).toString('hex');
};

/**
 * Encrypts a recovery key using the master key
 * @param {string} recoveryKey - Recovery key to encrypt
 * @returns {string} Encrypted recovery key
 */
export const encryptRecoveryKey = (recoveryKey) => {
  const key = crypto.createHash('sha256')
    .update(MASTER_KEY || DEFAULT_DEV_KEY)
    .digest();
  
  const { encrypted, iv, authTag } = encrypt(recoveryKey, key);
  
  // Combine encrypted data, IV, and auth tag into a single string
  return `${encrypted}:${iv}:${authTag}`;
};

/**
 * Decrypts a recovery key using the master key
 * @param {string} encryptedRecoveryKey - Encrypted recovery key
 * @returns {string} Decrypted recovery key
 */
export const decryptRecoveryKey = (encryptedRecoveryKey) => {
  const key = crypto.createHash('sha256')
    .update(MASTER_KEY || DEFAULT_DEV_KEY)
    .digest();
  
  const [encrypted, iv, authTag] = encryptedRecoveryKey.split(':');
  
  return decrypt(encrypted, iv, authTag, key);
};

/**
 * Re-encrypts all password entries with a new master password
 * @param {Array} encryptedEntries - Array of encrypted password entries
 * @param {string} oldMasterPassword - Old master password
 * @param {string} newMasterPassword - New master password
 * @returns {Array} Re-encrypted password entries
 */
export const reEncryptPasswordEntries = (encryptedEntries, oldMasterPassword, newMasterPassword) => {
  return encryptedEntries.map(entry => {
    // First decrypt with old password
    const decrypted = decryptPasswordEntry(entry, oldMasterPassword);
    
    // Then re-encrypt with new password
    const reEncrypted = encryptPasswordEntry(decrypted, newMasterPassword);
    
    return {
      ...entry,
      ...reEncrypted
    };
  });
};

/**
 * Verifies if a master password can decrypt a password entry
 * @param {Object} encryptedEntry - Encrypted password entry
 * @param {string} masterPassword - Master password to verify
 * @returns {boolean} True if the password can decrypt the entry
 */
export const verifyMasterPassword = (encryptedEntry, masterPassword) => {
  try {
    // Try to decrypt any encrypted field
    const salt = encryptedEntry.encryption_key_id;
    const key = deriveKeyFromPassword(masterPassword, salt);
    
    for (const field of ['username', 'password', 'url', 'notes', 'totp_secret']) {
      if (encryptedEntry[field] && encryptedEntry[`${field}_iv`] && encryptedEntry[`${field}_auth_tag`]) {
        decrypt(
          encryptedEntry[field],
          encryptedEntry[`${field}_iv`],
          encryptedEntry[`${field}_auth_tag`],
          key
        );
        // If we get here without an error, decryption was successful
        return true;
      }
    }
    
    // No encrypted fields found
    return false;
  } catch (error) {
    // Decryption failed
    return false;
  }
};