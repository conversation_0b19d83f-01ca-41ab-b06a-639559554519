/**
 * Logger utility for consistent logging across the application
 */
const logger = {
  /**
   * Log an informational message
   * @param {string} message - The message to log
   * @param {any} data - Optional data to log
   */
  info: (message, data) => {
    if (data) {
      console.log(`[INFO] ${message}`, data);
    } else {
      console.log(`[INFO] ${message}`);
    }
  },

  /**
   * Log a warning message
   * @param {string} message - The message to log
   * @param {any} data - Optional data to log
   */
  warn: (message, data) => {
    if (data) {
      console.warn(`[WARN] ${message}`, data);
    } else {
      console.warn(`[WARN] ${message}`);
    }
  },

  /**
   * Log an error message
   * @param {string} message - The message to log
   * @param {Error|any} error - The error object or data to log
   */
  error: (message, error) => {
    if (error instanceof Error) {
      console.error(`[ERROR] ${message}`, error.message);
      console.error(error.stack);
    } else if (error) {
      console.error(`[ERROR] ${message}`, error);
    } else {
      console.error(`[ERROR] ${message}`);
    }
  },

  /**
   * Log a debug message (only in development environment)
   * @param {string} message - The message to log
   * @param {any} data - Optional data to log
   */
  debug: (message, data) => {
    if (process.env.NODE_ENV === 'development') {
      if (data) {
        console.debug(`[DEBUG] ${message}`, data);
      } else {
        console.debug(`[DEBUG] ${message}`);
      }
    }
  }
};

export default logger;