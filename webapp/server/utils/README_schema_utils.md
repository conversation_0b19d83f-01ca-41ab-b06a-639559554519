# Schema Utilities for Dynamic Schema Selection

This document describes the schema utilities implemented to support dynamic schema selection for models. These utilities are designed to prepare for splitting site data from farm data by creating separate schemas for each farm.

## Overview

The schema utilities provide a way to dynamically set the schema for models at runtime. This is achieved through a callback mechanism that determines which schema to use based on the context (e.g., farm ID).

## Files

- `schemaUtils.js`: Contains the core schema callback mechanism
- `modelUtils.js`: Provides a wrapper around <PERSON><PERSON><PERSON>'s `define` method to use the schema callback
- `models/index.js`: Exports the schema utilities for easy import in other modules

## Usage

### Setting up a Model with Dynamic Schema

To update an existing model to use dynamic schema selection:

1. Import the `defineModel` function from `modelUtils.js`:

```javascript
import { defineModel } from '../utils/modelUtils.js';
```

2. Replace `sequelize.define` with `defineModel`:

```javascript
// Before
const User = sequelize.define('User', {
  // attributes
}, {
  tableName: 'users',
  schema: process.env.DB_SCHEMA || 'site',
  // other options
});

// After
const User = defineModel('User', {
  // attributes
}, {
  tableName: 'users',
  // other options (schema is handled by defineModel)
});
```

### Setting a Custom Schema Callback

To set a custom schema callback that determines which schema to use:

```javascript
import { setSchemaCallback } from '../models/index.js';

// Simple callback that always returns a specific schema
setSchemaCallback(() => 'my_custom_schema');

// Callback that uses farm ID to determine schema
setSchemaCallback((options) => {
  if (options.farmId) {
    return `farm_${options.farmId}`;
  }
  return 'site'; // Default schema
});
```

### Creating a Farm-Specific Schema Callback

For farm-specific schema selection, use the `createFarmSchemaCallback` helper:

```javascript
import { createFarmSchemaCallback, setSchemaCallback } from '../models/index.js';

// Function that resolves farm ID to schema name
const farmSchemaResolver = (farmId) => `farm_${farmId}`;

// Create and set the callback
const farmSchemaCallback = createFarmSchemaCallback(farmSchemaResolver);
setSchemaCallback(farmSchemaCallback);
```

### Getting the Current Schema

To get the current schema based on the active callback:

```javascript
import { getSchema } from '../models/index.js';

// Get schema with no options
const defaultSchema = getSchema();

// Get schema with farm ID
const farmSchema = getSchema({ farmId: 'farm123' });
```

### Resetting to Default Schema

To reset the schema callback to use the default schema:

```javascript
import { resetSchemaCallback } from '../models/index.js';

resetSchemaCallback();
```

## Implementation Details

The schema utilities use a callback mechanism to determine which schema to use at runtime. The callback is stored in memory and can be changed dynamically.

The default schema is determined from the environment variable `DB_SCHEMA` or falls back to 'site' if not set.

The `defineModel` function wraps Sequelize's `define` method and adds the schema from the callback to the model options.

## Next Steps

1. Update all model files to use `defineModel` instead of `sequelize.define`
2. Implement farm-specific schema selection in controllers or middleware
3. Create migration scripts to move data to farm-specific schemas when needed

## Notes

- This implementation only prepares the models for dynamic schema selection. It does not perform any data migration.
- The schema callback is global, so it affects all models that use `defineModel`.
- When using farm-specific schemas, ensure that cross-schema queries are handled properly.