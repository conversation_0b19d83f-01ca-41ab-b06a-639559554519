import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Cache for secrets to avoid repeated lookups
const secretsCache = new Map();
// TTL for cache in milliseconds (default: 15 minutes)
const CACHE_TTL = 15 * 60 * 1000;

/**
 * Get a secret from environment variables
 * @param {string} secretName - The name of the environment variable to retrieve
 * @param {boolean} useCache - Whether to use the cache (default: true)
 * @returns {Promise<object|string>} - The environment variable value
 */
export const getSecret = async (secretName, useCache = true) => {
  // Check if secret is in cache and not expired
  if (useCache && secretsCache.has(secretName)) {
    const cachedSecret = secretsCache.get(secretName);
    if (Date.now() < cachedSecret.expiresAt) {
      return cachedSecret.value;
    }
    // Remove expired secret from cache
    secretsCache.delete(secretName);
  }

  // Get the secret from environment variables
  const secretValue = process.env[secretName] || null;

  // Cache the secret with expiration if it exists
  if (secretValue && useCache) {
    secretsCache.set(secretName, {
      value: secretValue,
      expiresAt: Date.now() + CACHE_TTL
    });
  }

  return secretValue;
};

/**
 * Create a new secret in the environment
 * @param {string} secretName - The name of the secret to create
 * @param {object|string} secretValue - The value of the secret
 * @returns {Promise<object>} - A response object
 */
export const createSecret = async (secretName, secretValue) => {
  console.warn(`createSecret: Cannot create secret ${secretName} at runtime. Environment variables must be set before application starts.`);
  console.warn('Please update your .env file or environment configuration with this secret.');

  // Update cache for current session
  secretsCache.set(secretName, {
    value: secretValue,
    expiresAt: Date.now() + CACHE_TTL
  });

  return {
    message: 'Secret cached for current session only. Environment variable not updated.',
    name: secretName
  };
};

/**
 * Update an existing secret in the environment
 * @param {string} secretName - The name of the secret to update
 * @param {object|string} secretValue - The new value of the secret
 * @returns {Promise<object>} - A response object
 */
export const updateSecret = async (secretName, secretValue) => {
  console.warn(`updateSecret: Cannot update secret ${secretName} at runtime. Environment variables must be set before application starts.`);
  console.warn('Please update your .env file or environment configuration with this secret.');

  // Update cache for current session
  secretsCache.set(secretName, {
    value: secretValue,
    expiresAt: Date.now() + CACHE_TTL
  });

  return {
    message: 'Secret cached for current session only. Environment variable not updated.',
    name: secretName
  };
};

/**
 * Get database configuration from environment variables
 * @returns {Promise<object>} - Database configuration object
 */
export const getDatabaseConfig = async () => {
  // Get database configuration from environment variables
  const config = {
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 5432,
    database: process.env.DB_NAME || 'farmbooks',
    username: process.env.DB_USER || 'postgres',
    password: process.env.DB_PASSWORD || 'postgres',
    schema: process.env.DB_SCHEMA || 'site'
  };

  // Log the configuration source (without sensitive information)
  console.log('Using database configuration from environment variables:', {
    host: config.host,
    port: config.port,
    database: config.database,
    username: config.username,
    schema: config.schema
    // Not logging password for security reasons
  });

  return config;
};

/**
 * Get JWT configuration from environment variables
 * @returns {Promise<object>} - JWT configuration object
 */
export const getJwtConfig = async () => {
  // Get JWT configuration from environment variables
  return {
    secret: process.env.JWT_SECRET,
    expiresIn: process.env.JWT_EXPIRES_IN || '1d',
    refreshSecret: process.env.JWT_REFRESH_SECRET,
    refreshExpiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '7d'
  };
};

/**
 * Get API key from environment variables
 * @param {string} apiKeyName - The name of the API key
 * @returns {Promise<string>} - The API key
 */
export const getApiKey = async (apiKeyName) => {
  // Get API key from environment variables
  return process.env[apiKeyName];
};

/**
 * Clear the secrets cache
 */
export const clearCache = () => {
  secretsCache.clear();
};

export default {
  getSecret,
  createSecret,
  updateSecret,
  getDatabaseConfig,
  getJwtConfig,
  getApiKey,
  clearCache
};
