/**
 * SMS utility functions
 * This is a placeholder implementation. In production, you would integrate with
 * an SMS service provider like Twilio, AWS SNS, or similar.
 */

/**
 * Send an SMS message
 * @param {Object} options - SMS options
 * @param {string} options.to - Recipient phone number
 * @param {string} options.message - Message content
 * @param {string} options.from - Sender phone number (optional)
 * @returns {Promise<Object>} SMS result
 */
export const sendSMS = async ({ to, message, from = null }) => {
  try {
    // Validate phone number format (basic validation)
    if (!to || !isValidPhoneNumber(to)) {
      throw new Error('Invalid phone number');
    }

    // Validate message content
    if (!message || message.trim().length === 0) {
      throw new Error('Message content is required');
    }

    // Check message length (SMS limit is typically 160 characters)
    if (message.length > 160) {
      console.warn('SMS message exceeds 160 characters, may be split into multiple messages');
    }

    // In a real implementation, you would call your SMS service provider here
    // For now, we'll just log the SMS and return a success response
    console.log('SMS would be sent:', {
      to,
      from: from || process.env.SMS_FROM_NUMBER || '+**********',
      message,
      timestamp: new Date().toISOString()
    });

    // Simulate SMS service response
    const result = {
      success: true,
      messageId: `sms_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      to,
      message,
      status: 'sent',
      timestamp: new Date().toISOString(),
      cost: 0.01 // Simulated cost in USD
    };

    return result;

  } catch (error) {
    console.error('Error sending SMS:', error);
    return {
      success: false,
      error: error.message,
      to,
      message,
      timestamp: new Date().toISOString()
    };
  }
};

/**
 * Send bulk SMS messages
 * @param {Array} messages - Array of SMS message objects
 * @returns {Promise<Array>} Array of SMS results
 */
export const sendBulkSMS = async (messages) => {
  const results = [];
  
  for (const message of messages) {
    const result = await sendSMS(message);
    results.push(result);
    
    // Add a small delay between messages to avoid rate limiting
    await new Promise(resolve => setTimeout(resolve, 100));
  }
  
  return results;
};

/**
 * Basic phone number validation
 * @param {string} phoneNumber - Phone number to validate
 * @returns {boolean} True if valid format
 */
export const isValidPhoneNumber = (phoneNumber) => {
  if (!phoneNumber || typeof phoneNumber !== 'string') {
    return false;
  }

  // Remove all non-digit characters
  const cleaned = phoneNumber.replace(/\D/g, '');
  
  // Check if it's a valid length (10-15 digits)
  if (cleaned.length < 10 || cleaned.length > 15) {
    return false;
  }

  // Basic format validation (starts with country code or area code)
  const phoneRegex = /^(\+?1?)?[2-9]\d{2}[2-9]\d{2}\d{4}$/;
  return phoneRegex.test(cleaned);
};

/**
 * Format phone number for display
 * @param {string} phoneNumber - Phone number to format
 * @returns {string} Formatted phone number
 */
export const formatPhoneNumber = (phoneNumber) => {
  if (!phoneNumber) return '';
  
  const cleaned = phoneNumber.replace(/\D/g, '');
  
  if (cleaned.length === 10) {
    // US format: (*************
    return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6)}`;
  } else if (cleaned.length === 11 && cleaned.startsWith('1')) {
    // US format with country code: +****************
    return `+1 (${cleaned.slice(1, 4)}) ${cleaned.slice(4, 7)}-${cleaned.slice(7)}`;
  }
  
  // For other formats, just return the cleaned number
  return cleaned;
};

/**
 * Get SMS delivery status
 * @param {string} messageId - SMS message ID
 * @returns {Promise<Object>} Delivery status
 */
export const getSMSStatus = async (messageId) => {
  // In a real implementation, you would query your SMS service provider
  // For now, return a simulated status
  return {
    messageId,
    status: 'delivered',
    deliveredAt: new Date().toISOString(),
    cost: 0.01
  };
};

/**
 * Validate SMS configuration
 * @returns {Object} Configuration validation result
 */
export const validateSMSConfig = () => {
  const config = {
    fromNumber: process.env.SMS_FROM_NUMBER,
    apiKey: process.env.SMS_API_KEY,
    apiSecret: process.env.SMS_API_SECRET,
    provider: process.env.SMS_PROVIDER || 'twilio'
  };

  const missing = [];
  if (!config.fromNumber) missing.push('SMS_FROM_NUMBER');
  if (!config.apiKey) missing.push('SMS_API_KEY');
  if (!config.apiSecret) missing.push('SMS_API_SECRET');

  return {
    valid: missing.length === 0,
    missing,
    config: missing.length === 0 ? config : null
  };
};

// Example integration with Twilio (commented out)
/*
import twilio from 'twilio';

const twilioClient = twilio(
  process.env.TWILIO_ACCOUNT_SID,
  process.env.TWILIO_AUTH_TOKEN
);

export const sendSMSWithTwilio = async ({ to, message, from }) => {
  try {
    const result = await twilioClient.messages.create({
      body: message,
      from: from || process.env.TWILIO_PHONE_NUMBER,
      to: to
    });

    return {
      success: true,
      messageId: result.sid,
      status: result.status,
      to: result.to,
      from: result.from,
      timestamp: result.dateCreated
    };
  } catch (error) {
    return {
      success: false,
      error: error.message,
      to,
      message
    };
  }
};
*/
