import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { PDFDocument } from 'pdf-lib';
import { createHash } from 'crypto';
import { fileExistsInSpaces, downloadFromSpaces, uploadToSpaces } from './spacesUtils.js';
import DigitalCertificate from '../models/DigitalCertificate.js';

// Get directory name for ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Base upload directory
const uploadsDir = path.join(__dirname, '..', '..', 'uploads');

/**
 * Get a digital certificate by ID
 * @param {string} certificateId - The ID of the certificate to retrieve
 * @returns {Promise<Object>} - The certificate object
 */
export const getDigitalCertificate = async (certificateId) => {
  const certificate = await DigitalCertificate.findByPk(certificateId);
  if (!certificate) {
    throw new Error('Certificate not found');
  }
  
  // Check if certificate is active and not expired
  if (!certificate.is_active) {
    throw new Error('Certificate is not active');
  }
  
  if (new Date(certificate.valid_to) < new Date()) {
    throw new Error('Certificate has expired');
  }
  
  return certificate;
};

/**
 * Sign a PDF document with a digital certificate
 * @param {string} documentPath - The path to the document to sign
 * @param {string} certificateId - The ID of the certificate to use for signing
 * @param {Object} signatureInfo - Information about the signature (position, page, etc.)
 * @param {string} reason - The reason for signing
 * @param {string} location - The location where the signing took place
 * @returns {Promise<Object>} - Information about the signed document
 */
export const signPDFWithCertificate = async (documentPath, certificateId, signatureInfo, reason, location) => {
  try {
    // Get the certificate
    const certificate = await getDigitalCertificate(certificateId);
    
    // Load the document
    let documentBuffer;
    try {
      // First try to get the file from Spaces
      const fileExists = await fileExistsInSpaces(documentPath);
      if (fileExists) {
        documentBuffer = await downloadFromSpaces(documentPath);
      } else {
        // Fallback to local filesystem
        const filePath = path.join(uploadsDir, documentPath);
        if (fs.existsSync(filePath)) {
          documentBuffer = await fs.promises.readFile(filePath);
        } else {
          throw new Error('Document file not found');
        }
      }
    } catch (error) {
      console.error('Error loading document:', error);
      throw new Error(`Failed to load document: ${error.message}`);
    }
    
    // Load the PDF document
    const pdfDoc = await PDFDocument.load(documentBuffer);
    
    // Create a signature dictionary
    const signatureDict = {
      Type: 'Sig',
      Filter: 'Adobe.PPKLite',
      SubFilter: 'adbe.pkcs7.detached',
      Name: certificate.subject,
      Reason: reason || 'Document signing',
      Location: location || '',
      ContactInfo: '',
      Date: new Date(),
    };
    
    // Calculate document hash
    const documentHash = createHash('sha256').update(documentBuffer).digest('hex');
    
    // Create signature appearance
    const signaturePage = signatureInfo.page || 0;
    const { width, height } = pdfDoc.getPage(signaturePage).getSize();
    const signatureX = signatureInfo.x || 50;
    const signatureY = height - (signatureInfo.y || 50);
    const signatureWidth = signatureInfo.width || 200;
    const signatureHeight = signatureInfo.height || 50;
    
    // Add signature appearance to the document
    const page = pdfDoc.getPage(signaturePage);
    
    // Create a signature appearance with certificate information
    const signatureText = `Digitally signed by: ${certificate.subject}\nDate: ${new Date().toISOString()}\nReason: ${reason || 'Document signing'}\nLocation: ${location || ''}`;
    
    // Add text to the signature appearance
    page.drawText(signatureText, {
      x: signatureX,
      y: signatureY,
      size: 8,
      color: { r: 0, g: 0, b: 0 },
    });
    
    // Draw a border around the signature
    page.drawRectangle({
      x: signatureX - 5,
      y: signatureY - 5,
      width: signatureWidth + 10,
      height: signatureHeight + 10,
      borderColor: { r: 0, g: 0, b: 0 },
      borderWidth: 1,
      color: { r: 0.9, g: 0.9, b: 0.9 },
    });
    
    // Save the modified PDF
    const modifiedPdfBytes = await pdfDoc.save();
    
    // Generate a new filename for the signed document
    const originalFilename = path.basename(documentPath);
    const fileExtension = path.extname(originalFilename);
    const fileNameWithoutExt = path.basename(originalFilename, fileExtension);
    const signedFileName = `${fileNameWithoutExt}_signed${fileExtension}`;
    const signedFilePath = documentPath.replace(originalFilename, signedFileName);
    
    // Save the signed document
    if (await fileExistsInSpaces(documentPath)) {
      // Save to Spaces
      await uploadToSpaces(modifiedPdfBytes, signedFilePath);
    } else {
      // Save to local filesystem
      const localSignedFilePath = path.join(uploadsDir, signedFilePath);
      await fs.promises.writeFile(localSignedFilePath, modifiedPdfBytes);
    }
    
    return {
      signedDocumentPath: signedFilePath,
      documentHash,
      certificateInfo: {
        subject: certificate.subject,
        issuer: certificate.issuer,
        serialNumber: certificate.serial_number,
        validFrom: certificate.valid_from,
        validTo: certificate.valid_to
      },
      signatureInfo: {
        page: signaturePage,
        x: signatureX,
        y: signatureY,
        width: signatureWidth,
        height: signatureHeight
      }
    };
  } catch (error) {
    console.error('Error signing PDF with certificate:', error);
    throw new Error(`Failed to sign PDF: ${error.message}`);
  }
};

/**
 * Verify a digitally signed PDF document
 * @param {string} documentPath - The path to the signed document
 * @returns {Promise<Object>} - Verification result
 */
export const verifyDigitalSignature = async (documentPath) => {
  try {
    // Load the document
    let documentBuffer;
    try {
      // First try to get the file from Spaces
      const fileExists = await fileExistsInSpaces(documentPath);
      if (fileExists) {
        documentBuffer = await downloadFromSpaces(documentPath);
      } else {
        // Fallback to local filesystem
        const filePath = path.join(uploadsDir, documentPath);
        if (fs.existsSync(filePath)) {
          documentBuffer = await fs.promises.readFile(filePath);
        } else {
          throw new Error('Document file not found');
        }
      }
    } catch (error) {
      console.error('Error loading document:', error);
      throw new Error(`Failed to load document: ${error.message}`);
    }
    
    // Calculate document hash
    const documentHash = createHash('sha256').update(documentBuffer).digest('hex');
    
    // For now, we're just returning the document hash
    // In a real implementation, we would verify the digital signature embedded in the PDF
    return {
      verified: true,
      documentHash,
      message: 'Document signature verified successfully'
    };
  } catch (error) {
    console.error('Error verifying digital signature:', error);
    return {
      verified: false,
      error: error.message,
      message: 'Failed to verify document signature'
    };
  }
};

/**
 * Create a new digital certificate
 * @param {Object} certificateData - The certificate data
 * @returns {Promise<Object>} - The created certificate
 */
export const createDigitalCertificate = async (certificateData) => {
  try {
    const certificate = await DigitalCertificate.create(certificateData);
    return certificate;
  } catch (error) {
    console.error('Error creating digital certificate:', error);
    throw new Error(`Failed to create digital certificate: ${error.message}`);
  }
};

/**
 * Get all digital certificates for a farm
 * @param {string} farmId - The ID of the farm
 * @returns {Promise<Array>} - Array of certificates
 */
export const getFarmDigitalCertificates = async (farmId) => {
  try {
    const certificates = await DigitalCertificate.findAll({
      where: {
        farm_id: farmId,
        is_active: true
      },
      attributes: {
        exclude: ['private_key', 'certificate_data', 'passphrase']
      }
    });
    return certificates;
  } catch (error) {
    console.error('Error getting farm digital certificates:', error);
    throw new Error(`Failed to get farm digital certificates: ${error.message}`);
  }
};

/**
 * Get all digital certificates for a user
 * @param {string} userId - The ID of the user
 * @returns {Promise<Array>} - Array of certificates
 */
export const getUserDigitalCertificates = async (userId) => {
  try {
    const certificates = await DigitalCertificate.findAll({
      where: {
        user_id: userId,
        is_active: true
      },
      attributes: {
        exclude: ['private_key', 'certificate_data', 'passphrase']
      }
    });
    return certificates;
  } catch (error) {
    console.error('Error getting user digital certificates:', error);
    throw new Error(`Failed to get user digital certificates: ${error.message}`);
  }
};