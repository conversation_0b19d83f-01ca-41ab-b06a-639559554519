import nodemailer from 'nodemailer';
import fs from 'fs';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';
import dotenv from 'dotenv';
import { getFrontendUrl } from './emailUtils.js';

dotenv.config();

// Email configuration
const transporter = nodemailer.createTransport({
  host: process.env.EMAIL_HOST,
  port: process.env.EMAIL_PORT,
  secure: process.env.EMAIL_PORT === '465',
  auth: {
    user: process.env.EMAIL_USER,
    pass: process.env.EMAIL_PASSWORD,
  },
});

/**
 * Send an order request confirmation email to a customer
 * 
 * @param {Object} orderRequest - The order request object
 * @param {Object} customer - The customer who placed the order
 * @param {Object} farm - The farm that received the order
 * @param {Array} items - The items in the order
 * @param {string} frontendUrl - The URL to the frontend application
 * @returns {Promise<Object>} - The result of the email sending operation
 */
export const sendOrderRequestConfirmationEmail = async (orderRequest, customer, farm, items, frontendUrl) => {
  try {
    // Read email template
    const templatePath = path.join(process.cwd(), 'server', 'templates', 'emails', 'marketplace', 'order-request-confirmation.html');
    let emailTemplate = fs.readFileSync(templatePath, 'utf8');

    // Format the order date
    const orderDate = new Date(orderRequest.created_at).toLocaleString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });

    // Calculate subtotal, delivery fee, and total
    const subtotal = items.reduce((sum, item) => sum + (item.price * item.quantity), 0).toFixed(2);
    const deliveryFee = orderRequest.fulfillment_method === 'delivery' ? (orderRequest.delivery_fee || 0).toFixed(2) : 0;
    const total = (parseFloat(subtotal) + parseFloat(deliveryFee)).toFixed(2);

    // Create the order URL
    const orderUrl = `${frontendUrl}/marketplace/orders/${orderRequest.id}`;

    // Replace placeholders in template
    emailTemplate = emailTemplate
      .replace(/{{customerName}}/g, customer.name)
      .replace(/{{farmName}}/g, farm.name)
      .replace(/{{orderRequestId}}/g, orderRequest.id)
      .replace(/{{orderDate}}/g, orderDate)
      .replace(/{{fulfillmentMethod}}/g, orderRequest.fulfillment_method === 'delivery' ? 'Delivery' : 'Pickup')
      .replace(/{{orderUrl}}/g, orderUrl)
      .replace(/{{email}}/g, customer.email)
      .replace(/{{year}}/g, new Date().getFullYear())
      .replace(/{{subtotal}}/g, subtotal)
      .replace(/{{total}}/g, total);

    // Handle conditional sections
    if (orderRequest.fulfillment_method === 'delivery') {
      emailTemplate = emailTemplate.replace(/{{#if isDelivery}}([\s\S]*?){{\/if}}/g, '$1')
        .replace(/{{deliveryAddress}}/g, orderRequest.delivery_address)
        .replace(/{{deliveryCity}}/g, orderRequest.delivery_city)
        .replace(/{{deliveryState}}/g, orderRequest.delivery_state)
        .replace(/{{deliveryZipCode}}/g, orderRequest.delivery_zip_code)
        .replace(/{{deliveryFee}}/g, deliveryFee);
      
      // Remove pickup section
      emailTemplate = emailTemplate.replace(/{{#if isPickup}}[\s\S]*?{{\/if}}/g, '');
    } else {
      emailTemplate = emailTemplate.replace(/{{#if isPickup}}([\s\S]*?){{\/if}}/g, '$1')
        .replace(/{{pickupLocation}}/g, farm.name)
        .replace(/{{pickupDate}}/g, orderRequest.pickup_date ? new Date(orderRequest.pickup_date).toLocaleString('en-US', {
          year: 'numeric',
          month: 'long',
          day: 'numeric',
          hour: '2-digit',
          minute: '2-digit'
        }) : 'To be determined');
      
      // Remove delivery section
      emailTemplate = emailTemplate.replace(/{{#if isDelivery}}[\s\S]*?{{\/if}}/g, '');
      // Remove delivery fee section
      emailTemplate = emailTemplate.replace(/{{#if deliveryFee}}[\s\S]*?{{\/if}}/g, '');
    }

    // Add items to the template
    const itemsHtml = items.map(item => `
      <div class="order-item">
        <p>${item.quantity} x ${item.product_name} - $${item.price.toFixed(2)} each</p>
      </div>
    `).join('');
    emailTemplate = emailTemplate.replace(/{{#each items}}[\s\S]*?{{\/each}}/g, itemsHtml);

    // Set up email options
    const mailOptions = {
      from: `"${farm.name}" <<EMAIL>>`,
      to: customer.email,
      replyTo: farm.email || process.env.EMAIL_FROM || '<EMAIL>',
      subject: `Order Request Confirmation: ${farm.name}`,
      html: emailTemplate,
      headers: {
        'Message-ID': `<order-request-${orderRequest.id}-${Date.now()}@nxtacre.com>`,
        'X-Order-Request-ID': orderRequest.id,
        'X-Farm-ID': farm.id,
        'X-Customer-ID': customer.id
      }
    };

    // Send email
    const result = await transporter.sendMail(mailOptions);
    return result;
  } catch (error) {
    console.error('Error sending order request confirmation email:', error);
    throw error;
  }
};

/**
 * Send an order request approved email to a customer
 * 
 * @param {Object} orderRequest - The order request object
 * @param {Object} customer - The customer who placed the order
 * @param {Object} farm - The farm that approved the order
 * @param {Array} items - The items in the order
 * @param {string} frontendUrl - The URL to the frontend application
 * @returns {Promise<Object>} - The result of the email sending operation
 */
export const sendOrderRequestApprovedEmail = async (orderRequest, customer, farm, items, frontendUrl) => {
  try {
    // Read email template
    const templatePath = path.join(process.cwd(), 'server', 'templates', 'emails', 'marketplace', 'order-request-approved.html');
    let emailTemplate = fs.readFileSync(templatePath, 'utf8');

    // Format the order date and approval date
    const orderDate = new Date(orderRequest.created_at).toLocaleString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });

    const approvalDate = new Date().toLocaleString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });

    // Calculate subtotal, delivery fee, and total
    const subtotal = items.reduce((sum, item) => sum + (item.price * item.quantity), 0).toFixed(2);
    const deliveryFee = orderRequest.fulfillment_method === 'delivery' ? (orderRequest.delivery_fee || 0).toFixed(2) : 0;
    const total = (parseFloat(subtotal) + parseFloat(deliveryFee)).toFixed(2);

    // Create the order URL
    const orderUrl = `${frontendUrl}/marketplace/orders/${orderRequest.id}`;

    // Replace placeholders in template
    emailTemplate = emailTemplate
      .replace(/{{customerName}}/g, customer.name)
      .replace(/{{farmName}}/g, farm.name)
      .replace(/{{orderRequestId}}/g, orderRequest.id)
      .replace(/{{orderDate}}/g, orderDate)
      .replace(/{{approvalDate}}/g, approvalDate)
      .replace(/{{fulfillmentMethod}}/g, orderRequest.fulfillment_method === 'delivery' ? 'Delivery' : 'Pickup')
      .replace(/{{orderUrl}}/g, orderUrl)
      .replace(/{{email}}/g, customer.email)
      .replace(/{{year}}/g, new Date().getFullYear())
      .replace(/{{subtotal}}/g, subtotal)
      .replace(/{{total}}/g, total);

    // Handle conditional sections
    if (orderRequest.fulfillment_method === 'delivery') {
      emailTemplate = emailTemplate.replace(/{{#if isDelivery}}([\s\S]*?){{\/if}}/g, '$1')
        .replace(/{{deliveryAddress}}/g, orderRequest.delivery_address)
        .replace(/{{deliveryCity}}/g, orderRequest.delivery_city)
        .replace(/{{deliveryState}}/g, orderRequest.delivery_state)
        .replace(/{{deliveryZipCode}}/g, orderRequest.delivery_zip_code)
        .replace(/{{deliveryFee}}/g, deliveryFee);
      
      // Remove pickup section
      emailTemplate = emailTemplate.replace(/{{#if isPickup}}[\s\S]*?{{\/if}}/g, '');
    } else {
      emailTemplate = emailTemplate.replace(/{{#if isPickup}}([\s\S]*?){{\/if}}/g, '$1')
        .replace(/{{pickupLocation}}/g, farm.name)
        .replace(/{{pickupDate}}/g, orderRequest.pickup_date ? new Date(orderRequest.pickup_date).toLocaleString('en-US', {
          year: 'numeric',
          month: 'long',
          day: 'numeric',
          hour: '2-digit',
          minute: '2-digit'
        }) : 'To be determined');
      
      // Remove delivery section
      emailTemplate = emailTemplate.replace(/{{#if isDelivery}}[\s\S]*?{{\/if}}/g, '');
      // Remove delivery fee section
      emailTemplate = emailTemplate.replace(/{{#if deliveryFee}}[\s\S]*?{{\/if}}/g, '');
    }

    // Add items to the template
    const itemsHtml = items.map(item => `
      <div class="order-item">
        <p>${item.quantity} x ${item.product_name} - $${item.price.toFixed(2)} each</p>
      </div>
    `).join('');
    emailTemplate = emailTemplate.replace(/{{#each items}}[\s\S]*?{{\/each}}/g, itemsHtml);

    // Set up email options
    const mailOptions = {
      from: `"${farm.name}" <<EMAIL>>`,
      to: customer.email,
      replyTo: farm.email || process.env.EMAIL_FROM || '<EMAIL>',
      subject: `Order Approved: ${farm.name}`,
      html: emailTemplate,
      headers: {
        'Message-ID': `<order-approved-${orderRequest.id}-${Date.now()}@nxtacre.com>`,
        'X-Order-Request-ID': orderRequest.id,
        'X-Farm-ID': farm.id,
        'X-Customer-ID': customer.id
      }
    };

    // Send email
    const result = await transporter.sendMail(mailOptions);
    return result;
  } catch (error) {
    console.error('Error sending order request approved email:', error);
    throw error;
  }
};

/**
 * Send an order request declined email to a customer
 * 
 * @param {Object} orderRequest - The order request object
 * @param {Object} customer - The customer who placed the order
 * @param {Object} farm - The farm that declined the order
 * @param {string} declineReason - The reason for declining the order (optional)
 * @param {string} frontendUrl - The URL to the frontend application
 * @returns {Promise<Object>} - The result of the email sending operation
 */
export const sendOrderRequestDeclinedEmail = async (orderRequest, customer, farm, declineReason, frontendUrl) => {
  try {
    // Read email template
    const templatePath = path.join(process.cwd(), 'server', 'templates', 'emails', 'marketplace', 'order-request-declined.html');
    let emailTemplate = fs.readFileSync(templatePath, 'utf8');

    // Format the order date and decline date
    const orderDate = new Date(orderRequest.created_at).toLocaleString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });

    const declineDate = new Date().toLocaleString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });

    // Create the marketplace URL
    const marketplaceUrl = `${frontendUrl}/marketplace`;

    // Replace placeholders in template
    emailTemplate = emailTemplate
      .replace(/{{customerName}}/g, customer.name)
      .replace(/{{farmName}}/g, farm.name)
      .replace(/{{orderRequestId}}/g, orderRequest.id)
      .replace(/{{orderDate}}/g, orderDate)
      .replace(/{{declineDate}}/g, declineDate)
      .replace(/{{marketplaceUrl}}/g, marketplaceUrl)
      .replace(/{{email}}/g, customer.email)
      .replace(/{{year}}/g, new Date().getFullYear());

    // Handle conditional decline reason
    if (declineReason) {
      emailTemplate = emailTemplate.replace(/{{#if declineReason}}([\s\S]*?){{\/if}}/g, '$1')
        .replace(/{{declineReason}}/g, declineReason);
    } else {
      emailTemplate = emailTemplate.replace(/{{#if declineReason}}[\s\S]*?{{\/if}}/g, '');
    }

    // Set up email options
    const mailOptions = {
      from: `"${farm.name}" <<EMAIL>>`,
      to: customer.email,
      replyTo: farm.email || process.env.EMAIL_FROM || '<EMAIL>',
      subject: `Order Request Declined: ${farm.name}`,
      html: emailTemplate,
      headers: {
        'Message-ID': `<order-declined-${orderRequest.id}-${Date.now()}@nxtacre.com>`,
        'X-Order-Request-ID': orderRequest.id,
        'X-Farm-ID': farm.id,
        'X-Customer-ID': customer.id
      }
    };

    // Send email
    const result = await transporter.sendMail(mailOptions);
    return result;
  } catch (error) {
    console.error('Error sending order request declined email:', error);
    throw error;
  }
};

/**
 * Send a delivery schedule options email to a customer
 * 
 * @param {Object} orderRequest - The order request object
 * @param {Object} customer - The customer who placed the order
 * @param {Object} farm - The farm offering delivery
 * @param {Array} deliveryOptions - Available delivery date/time options
 * @param {string} frontendUrl - The URL to the frontend application
 * @returns {Promise<Object>} - The result of the email sending operation
 */
export const sendDeliveryScheduleOptionsEmail = async (orderRequest, customer, farm, deliveryOptions, frontendUrl) => {
  try {
    // Read email template
    const templatePath = path.join(process.cwd(), 'server', 'templates', 'emails', 'marketplace', 'delivery-schedule-options.html');
    let emailTemplate = fs.readFileSync(templatePath, 'utf8');

    // Create the delivery scheduling URL
    const schedulingUrl = `${frontendUrl}/marketplace/orders/${orderRequest.id}/schedule`;

    // Replace placeholders in template
    emailTemplate = emailTemplate
      .replace(/{{customerName}}/g, customer.name)
      .replace(/{{farmName}}/g, farm.name)
      .replace(/{{orderRequestId}}/g, orderRequest.id)
      .replace(/{{schedulingUrl}}/g, schedulingUrl)
      .replace(/{{email}}/g, customer.email)
      .replace(/{{year}}/g, new Date().getFullYear());

    // Add delivery options to the template
    const optionsHtml = deliveryOptions.map(option => `
      <div class="delivery-option">
        <p><strong>Date:</strong> ${new Date(option.date).toLocaleDateString('en-US', {
          weekday: 'long',
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        })}</p>
        <p><strong>Time Window:</strong> ${option.timeWindow}</p>
      </div>
    `).join('');
    emailTemplate = emailTemplate.replace(/{{#each deliveryOptions}}[\s\S]*?{{\/each}}/g, optionsHtml);

    // Set up email options
    const mailOptions = {
      from: `"${farm.name}" <<EMAIL>>`,
      to: customer.email,
      replyTo: farm.email || process.env.EMAIL_FROM || '<EMAIL>',
      subject: `Delivery Schedule Options for Your Order: ${farm.name}`,
      html: emailTemplate,
      headers: {
        'Message-ID': `<delivery-options-${orderRequest.id}-${Date.now()}@nxtacre.com>`,
        'X-Order-Request-ID': orderRequest.id,
        'X-Farm-ID': farm.id,
        'X-Customer-ID': customer.id
      }
    };

    // Send email
    const result = await transporter.sendMail(mailOptions);
    return result;
  } catch (error) {
    console.error('Error sending delivery schedule options email:', error);
    throw error;
  }
};

/**
 * Send a delivery scheduled confirmation email to a customer
 * 
 * @param {Object} orderRequest - The order request object
 * @param {Object} customer - The customer who placed the order
 * @param {Object} farm - The farm providing delivery
 * @param {Object} deliveryInfo - Delivery date, time, and other details
 * @param {string} frontendUrl - The URL to the frontend application
 * @returns {Promise<Object>} - The result of the email sending operation
 */
export const sendDeliveryScheduledConfirmationEmail = async (orderRequest, customer, farm, deliveryInfo, frontendUrl) => {
  try {
    // Read email template
    const templatePath = path.join(process.cwd(), 'server', 'templates', 'emails', 'marketplace', 'delivery-scheduled-confirmation.html');
    let emailTemplate = fs.readFileSync(templatePath, 'utf8');

    // Format the delivery date
    const deliveryDate = new Date(deliveryInfo.date).toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });

    // Create the order tracking URL
    const trackingUrl = `${frontendUrl}/marketplace/orders/${orderRequest.id}/track`;

    // Replace placeholders in template
    emailTemplate = emailTemplate
      .replace(/{{customerName}}/g, customer.name)
      .replace(/{{farmName}}/g, farm.name)
      .replace(/{{orderRequestId}}/g, orderRequest.id)
      .replace(/{{deliveryDate}}/g, deliveryDate)
      .replace(/{{deliveryTimeWindow}}/g, deliveryInfo.timeWindow)
      .replace(/{{deliveryAddress}}/g, orderRequest.delivery_address)
      .replace(/{{deliveryCity}}/g, orderRequest.delivery_city)
      .replace(/{{deliveryState}}/g, orderRequest.delivery_state)
      .replace(/{{deliveryZipCode}}/g, orderRequest.delivery_zip_code)
      .replace(/{{trackingUrl}}/g, trackingUrl)
      .replace(/{{email}}/g, customer.email)
      .replace(/{{year}}/g, new Date().getFullYear());

    // Handle conditional delivery instructions
    if (deliveryInfo.instructions) {
      emailTemplate = emailTemplate.replace(/{{#if deliveryInstructions}}([\s\S]*?){{\/if}}/g, '$1')
        .replace(/{{deliveryInstructions}}/g, deliveryInfo.instructions);
    } else {
      emailTemplate = emailTemplate.replace(/{{#if deliveryInstructions}}[\s\S]*?{{\/if}}/g, '');
    }

    // Set up email options
    const mailOptions = {
      from: `"${farm.name}" <<EMAIL>>`,
      to: customer.email,
      replyTo: farm.email || process.env.EMAIL_FROM || '<EMAIL>',
      subject: `Delivery Scheduled: ${farm.name}`,
      html: emailTemplate,
      headers: {
        'Message-ID': `<delivery-scheduled-${orderRequest.id}-${Date.now()}@nxtacre.com>`,
        'X-Order-Request-ID': orderRequest.id,
        'X-Farm-ID': farm.id,
        'X-Customer-ID': customer.id
      }
    };

    // Send email
    const result = await transporter.sendMail(mailOptions);
    return result;
  } catch (error) {
    console.error('Error sending delivery scheduled confirmation email:', error);
    throw error;
  }
};

/**
 * Send a delivery in progress email to a customer
 * 
 * @param {Object} orderRequest - The order request object
 * @param {Object} customer - The customer who placed the order
 * @param {Object} farm - The farm providing delivery
 * @param {Object} deliveryInfo - Delivery tracking information
 * @param {string} frontendUrl - The URL to the frontend application
 * @returns {Promise<Object>} - The result of the email sending operation
 */
export const sendDeliveryInProgressEmail = async (orderRequest, customer, farm, deliveryInfo, frontendUrl) => {
  try {
    // Read email template
    const templatePath = path.join(process.cwd(), 'server', 'templates', 'emails', 'marketplace', 'delivery-in-progress.html');
    let emailTemplate = fs.readFileSync(templatePath, 'utf8');

    // Create the order tracking URL
    const trackingUrl = `${frontendUrl}/marketplace/orders/${orderRequest.id}/track`;

    // Replace placeholders in template
    emailTemplate = emailTemplate
      .replace(/{{customerName}}/g, customer.name)
      .replace(/{{farmName}}/g, farm.name)
      .replace(/{{orderRequestId}}/g, orderRequest.id)
      .replace(/{{estimatedArrival}}/g, deliveryInfo.estimatedArrival)
      .replace(/{{driverName}}/g, deliveryInfo.driverName)
      .replace(/{{trackingUrl}}/g, trackingUrl)
      .replace(/{{email}}/g, customer.email)
      .replace(/{{year}}/g, new Date().getFullYear());

    // Set up email options
    const mailOptions = {
      from: `"${farm.name}" <<EMAIL>>`,
      to: customer.email,
      replyTo: farm.email || process.env.EMAIL_FROM || '<EMAIL>',
      subject: `Your Delivery is on the Way: ${farm.name}`,
      html: emailTemplate,
      headers: {
        'Message-ID': `<delivery-in-progress-${orderRequest.id}-${Date.now()}@nxtacre.com>`,
        'X-Order-Request-ID': orderRequest.id,
        'X-Farm-ID': farm.id,
        'X-Customer-ID': customer.id
      }
    };

    // Send email
    const result = await transporter.sendMail(mailOptions);
    return result;
  } catch (error) {
    console.error('Error sending delivery in progress email:', error);
    throw error;
  }
};

/**
 * Send a delivery completed email to a customer
 * 
 * @param {Object} orderRequest - The order request object
 * @param {Object} customer - The customer who placed the order
 * @param {Object} farm - The farm that provided delivery
 * @param {Array} items - The items in the order
 * @param {string} frontendUrl - The URL to the frontend application
 * @returns {Promise<Object>} - The result of the email sending operation
 */
export const sendDeliveryCompletedEmail = async (orderRequest, customer, farm, items, frontendUrl) => {
  try {
    // Read email template
    const templatePath = path.join(process.cwd(), 'server', 'templates', 'emails', 'marketplace', 'delivery-completed.html');
    let emailTemplate = fs.readFileSync(templatePath, 'utf8');

    // Format the delivery date
    const deliveryDate = new Date().toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });

    // Create the order URL and feedback URL
    const orderUrl = `${frontendUrl}/marketplace/orders/${orderRequest.id}`;
    const feedbackUrl = `${frontendUrl}/marketplace/feedback/${orderRequest.id}`;

    // Replace placeholders in template
    emailTemplate = emailTemplate
      .replace(/{{customerName}}/g, customer.name)
      .replace(/{{farmName}}/g, farm.name)
      .replace(/{{orderRequestId}}/g, orderRequest.id)
      .replace(/{{deliveryDate}}/g, deliveryDate)
      .replace(/{{deliveryAddress}}/g, orderRequest.delivery_address)
      .replace(/{{deliveryCity}}/g, orderRequest.delivery_city)
      .replace(/{{deliveryState}}/g, orderRequest.delivery_state)
      .replace(/{{deliveryZipCode}}/g, orderRequest.delivery_zip_code)
      .replace(/{{orderUrl}}/g, orderUrl)
      .replace(/{{feedbackUrl}}/g, feedbackUrl)
      .replace(/{{email}}/g, customer.email)
      .replace(/{{year}}/g, new Date().getFullYear());

    // Add items to the template
    const itemsHtml = items.map(item => `
      <div class="order-item">
        <p>${item.quantity} x ${item.product_name}</p>
      </div>
    `).join('');
    emailTemplate = emailTemplate.replace(/{{#each items}}[\s\S]*?{{\/each}}/g, itemsHtml);

    // Set up email options
    const mailOptions = {
      from: `"${farm.name}" <<EMAIL>>`,
      to: customer.email,
      replyTo: farm.email || process.env.EMAIL_FROM || '<EMAIL>',
      subject: `Delivery Completed: ${farm.name}`,
      html: emailTemplate,
      headers: {
        'Message-ID': `<delivery-completed-${orderRequest.id}-${Date.now()}@nxtacre.com>`,
        'X-Order-Request-ID': orderRequest.id,
        'X-Farm-ID': farm.id,
        'X-Customer-ID': customer.id
      }
    };

    // Send email
    const result = await transporter.sendMail(mailOptions);
    return result;
  } catch (error) {
    console.error('Error sending delivery completed email:', error);
    throw error;
  }
};

/**
 * Send a new order request notification to a farm
 * 
 * @param {Object} orderRequest - The order request object
 * @param {Object} customer - The customer who placed the order
 * @param {Object} farm - The farm that received the order
 * @param {Object} farmAdmin - The farm administrator who should receive the email
 * @param {Array} items - The items in the order
 * @param {string} frontendUrl - The URL to the frontend application
 * @returns {Promise<Object>} - The result of the email sending operation
 */
export const sendNewOrderRequestReceivedEmail = async (orderRequest, customer, farm, farmAdmin, items, frontendUrl) => {
  try {
    // Read email template
    const templatePath = path.join(process.cwd(), 'server', 'templates', 'emails', 'marketplace', 'new-order-request-received.html');
    let emailTemplate = fs.readFileSync(templatePath, 'utf8');

    // Format the order date
    const orderDate = new Date(orderRequest.created_at).toLocaleString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });

    // Calculate subtotal, delivery fee, and total
    const subtotal = items.reduce((sum, item) => sum + (item.price * item.quantity), 0).toFixed(2);
    const deliveryFee = orderRequest.fulfillment_method === 'delivery' ? (orderRequest.delivery_fee || 0).toFixed(2) : 0;
    const total = (parseFloat(subtotal) + parseFloat(deliveryFee)).toFixed(2);

    // Create the URLs for approve, decline, and management
    const approveUrl = `${frontendUrl}/farms/${farm.id}/orders/${orderRequest.id}/approve`;
    const declineUrl = `${frontendUrl}/farms/${farm.id}/orders/${orderRequest.id}/decline`;
    const orderManagementUrl = `${frontendUrl}/farms/${farm.id}/orders/${orderRequest.id}`;

    // Replace placeholders in template
    emailTemplate = emailTemplate
      .replace(/{{farmAdminName}}/g, `${farmAdmin.first_name} ${farmAdmin.last_name}`)
      .replace(/{{customerName}}/g, customer.name)
      .replace(/{{customerEmail}}/g, customer.email)
      .replace(/{{customerPhone}}/g, customer.phone || 'Not provided')
      .replace(/{{orderRequestId}}/g, orderRequest.id)
      .replace(/{{orderDate}}/g, orderDate)
      .replace(/{{fulfillmentMethod}}/g, orderRequest.fulfillment_method === 'delivery' ? 'Delivery' : 'Pickup')
      .replace(/{{approveUrl}}/g, approveUrl)
      .replace(/{{declineUrl}}/g, declineUrl)
      .replace(/{{orderManagementUrl}}/g, orderManagementUrl)
      .replace(/{{email}}/g, farmAdmin.email)
      .replace(/{{year}}/g, new Date().getFullYear())
      .replace(/{{subtotal}}/g, subtotal)
      .replace(/{{total}}/g, total);

    // Handle conditional sections
    if (orderRequest.fulfillment_method === 'delivery') {
      emailTemplate = emailTemplate.replace(/{{#if isDelivery}}([\s\S]*?){{\/if}}/g, '$1')
        .replace(/{{deliveryAddress}}/g, orderRequest.delivery_address)
        .replace(/{{deliveryCity}}/g, orderRequest.delivery_city)
        .replace(/{{deliveryState}}/g, orderRequest.delivery_state)
        .replace(/{{deliveryZipCode}}/g, orderRequest.delivery_zip_code)
        .replace(/{{deliveryFee}}/g, deliveryFee);
      
      // Handle delivery instructions if present
      if (orderRequest.delivery_instructions) {
        emailTemplate = emailTemplate.replace(/{{#if deliveryInstructions}}([\s\S]*?){{\/if}}/g, '$1')
          .replace(/{{deliveryInstructions}}/g, orderRequest.delivery_instructions);
      } else {
        emailTemplate = emailTemplate.replace(/{{#if deliveryInstructions}}[\s\S]*?{{\/if}}/g, '');
      }
      
      // Remove pickup section
      emailTemplate = emailTemplate.replace(/{{#if isPickup}}[\s\S]*?{{\/if}}/g, '');
    } else {
      emailTemplate = emailTemplate.replace(/{{#if isPickup}}([\s\S]*?){{\/if}}/g, '$1')
        .replace(/{{pickupDate}}/g, orderRequest.pickup_date ? new Date(orderRequest.pickup_date).toLocaleString('en-US', {
          year: 'numeric',
          month: 'long',
          day: 'numeric',
          hour: '2-digit',
          minute: '2-digit'
        }) : 'To be determined');
      
      // Remove delivery section
      emailTemplate = emailTemplate.replace(/{{#if isDelivery}}[\s\S]*?{{\/if}}/g, '');
      // Remove delivery fee section
      emailTemplate = emailTemplate.replace(/{{#if deliveryFee}}[\s\S]*?{{\/if}}/g, '');
    }

    // Add items to the template
    const itemsHtml = items.map(item => {
      let html = `
        <div class="order-item">
          <p>${item.quantity} x ${item.product_name} - $${item.price.toFixed(2)} each</p>
      `;
      
      if (item.notes) {
        html += `<p><em>Notes: ${item.notes}</em></p>`;
      }
      
      html += `</div>`;
      return html;
    }).join('');
    emailTemplate = emailTemplate.replace(/{{#each items}}[\s\S]*?{{\/each}}/g, itemsHtml);

    // Set up email options
    const mailOptions = {
      from: `"NxtAcre Marketplace" <<EMAIL>>`,
      to: farmAdmin.email,
      subject: `New Order Request: ${customer.name}`,
      html: emailTemplate,
      headers: {
        'Message-ID': `<new-order-request-${orderRequest.id}-${Date.now()}@nxtacre.com>`,
        'X-Order-Request-ID': orderRequest.id,
        'X-Farm-ID': farm.id,
        'X-Customer-ID': customer.id
      }
    };

    // Send email
    const result = await transporter.sendMail(mailOptions);
    return result;
  } catch (error) {
    console.error('Error sending new order request received email:', error);
    throw error;
  }
};

/**
 * Send a customer selected delivery date notification to a farm
 * 
 * @param {Object} orderRequest - The order request object
 * @param {Object} customer - The customer who selected the delivery date
 * @param {Object} farm - The farm providing delivery
 * @param {Object} farmAdmin - The farm administrator who should receive the email
 * @param {Object} deliveryInfo - Delivery date, time, and other details
 * @param {string} frontendUrl - The URL to the frontend application
 * @returns {Promise<Object>} - The result of the email sending operation
 */
export const sendCustomerSelectedDeliveryDateEmail = async (orderRequest, customer, farm, farmAdmin, deliveryInfo, frontendUrl) => {
  try {
    // Read email template
    const templatePath = path.join(process.cwd(), 'server', 'templates', 'emails', 'marketplace', 'customer-selected-delivery-date.html');
    let emailTemplate = fs.readFileSync(templatePath, 'utf8');

    // Format the order date
    const orderDate = new Date(orderRequest.created_at).toLocaleString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });

    // Format the delivery date
    const deliveryDate = new Date(deliveryInfo.date).toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });

    // Create the order management URL
    const orderManagementUrl = `${frontendUrl}/farms/${farm.id}/orders/${orderRequest.id}`;

    // Replace placeholders in template
    emailTemplate = emailTemplate
      .replace(/{{farmAdminName}}/g, `${farmAdmin.first_name} ${farmAdmin.last_name}`)
      .replace(/{{customerName}}/g, customer.name)
      .replace(/{{customerEmail}}/g, customer.email)
      .replace(/{{customerPhone}}/g, customer.phone || 'Not provided')
      .replace(/{{orderRequestId}}/g, orderRequest.id)
      .replace(/{{orderDate}}/g, orderDate)
      .replace(/{{deliveryDate}}/g, deliveryDate)
      .replace(/{{deliveryTimeWindow}}/g, deliveryInfo.timeWindow)
      .replace(/{{deliveryAddress}}/g, orderRequest.delivery_address)
      .replace(/{{deliveryCity}}/g, orderRequest.delivery_city)
      .replace(/{{deliveryState}}/g, orderRequest.delivery_state)
      .replace(/{{deliveryZipCode}}/g, orderRequest.delivery_zip_code)
      .replace(/{{orderManagementUrl}}/g, orderManagementUrl)
      .replace(/{{email}}/g, farmAdmin.email)
      .replace(/{{year}}/g, new Date().getFullYear());

    // Handle conditional delivery instructions
    if (orderRequest.delivery_instructions) {
      emailTemplate = emailTemplate.replace(/{{#if deliveryInstructions}}([\s\S]*?){{\/if}}/g, '$1')
        .replace(/{{deliveryInstructions}}/g, orderRequest.delivery_instructions);
    } else {
      emailTemplate = emailTemplate.replace(/{{#if deliveryInstructions}}[\s\S]*?{{\/if}}/g, '');
    }

    // Set up email options
    const mailOptions = {
      from: `"NxtAcre Marketplace" <<EMAIL>>`,
      to: farmAdmin.email,
      subject: `Customer Selected Delivery Date: ${customer.name}`,
      html: emailTemplate,
      headers: {
        'Message-ID': `<delivery-date-selected-${orderRequest.id}-${Date.now()}@nxtacre.com>`,
        'X-Order-Request-ID': orderRequest.id,
        'X-Farm-ID': farm.id,
        'X-Customer-ID': customer.id
      }
    };

    // Send email
    const result = await transporter.sendMail(mailOptions);
    return result;
  } catch (error) {
    console.error('Error sending customer selected delivery date email:', error);
    throw error;
  }
};

/**
 * Send a delivery reminder to farm staff
 * 
 * @param {Object} farm - The farm providing delivery
 * @param {Object} farmStaff - The farm staff member who should receive the email
 * @param {Array} deliveries - Array of deliveries scheduled for the day
 * @param {Object} driverInfo - Information about the delivery driver
 * @param {string} frontendUrl - The URL to the frontend application
 * @returns {Promise<Object>} - The result of the email sending operation
 */
export const sendDeliveryReminderForFarmStaffEmail = async (farm, farmStaff, deliveries, driverInfo, frontendUrl) => {
  try {
    // Read email template
    const templatePath = path.join(process.cwd(), 'server', 'templates', 'emails', 'marketplace', 'delivery-reminder-for-farm-staff.html');
    let emailTemplate = fs.readFileSync(templatePath, 'utf8');

    // Format the delivery date
    const deliveryDate = new Date(deliveries[0].date).toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });

    // Calculate total items across all deliveries
    const totalItems = deliveries.reduce((sum, delivery) => sum + delivery.itemCount, 0);

    // Create the delivery management URL
    const deliveryManagementUrl = `${frontendUrl}/farms/${farm.id}/deliveries`;

    // Replace placeholders in template
    emailTemplate = emailTemplate
      .replace(/{{farmStaffName}}/g, `${farmStaff.first_name} ${farmStaff.last_name}`)
      .replace(/{{deliveryCount}}/g, deliveries.length)
      .replace(/{{deliveryDate}}/g, deliveryDate)
      .replace(/{{totalItems}}/g, totalItems)
      .replace(/{{driverName}}/g, driverInfo.name)
      .replace(/{{departureTime}}/g, driverInfo.departureTime)
      .replace(/{{deliveryManagementUrl}}/g, deliveryManagementUrl)
      .replace(/{{email}}/g, farmStaff.email)
      .replace(/{{year}}/g, new Date().getFullYear());

    // Add deliveries to the template
    const deliveriesHtml = deliveries.map(delivery => `
      <tr>
        <td>${delivery.orderId}</td>
        <td>${delivery.customerName}</td>
        <td>${delivery.deliveryTime}</td>
        <td>${delivery.itemCount} items</td>
      </tr>
    `).join('');
    emailTemplate = emailTemplate.replace(/{{#each deliveries}}[\s\S]*?{{\/each}}/g, deliveriesHtml);

    // Set up email options
    const mailOptions = {
      from: `"NxtAcre Marketplace" <<EMAIL>>`,
      to: farmStaff.email,
      subject: `Delivery Reminder: ${deliveries.length} Deliveries Scheduled for ${deliveryDate}`,
      html: emailTemplate,
      headers: {
        'Message-ID': `<delivery-reminder-${farm.id}-${Date.now()}@nxtacre.com>`,
        'X-Farm-ID': farm.id
      }
    };

    // Send email
    const result = await transporter.sendMail(mailOptions);
    return result;
  } catch (error) {
    console.error('Error sending delivery reminder for farm staff email:', error);
    throw error;
  }
};

/**
 * Send a customer message notification to a farm
 * 
 * @param {Object} message - The message object
 * @param {Object} customer - The customer who sent the message
 * @param {Object} farm - The farm that received the message
 * @param {Object} farmAdmin - The farm administrator who should receive the email
 * @param {Object} orderRequest - The related order request (optional)
 * @param {string} frontendUrl - The URL to the frontend application
 * @returns {Promise<Object>} - The result of the email sending operation
 */
export const sendCustomerMessageReceivedEmail = async (message, customer, farm, farmAdmin, orderRequest, frontendUrl) => {
  try {
    // Read email template
    const templatePath = path.join(process.cwd(), 'server', 'templates', 'emails', 'marketplace', 'customer-message-received.html');
    let emailTemplate = fs.readFileSync(templatePath, 'utf8');

    // Format the message date and time
    const messageDate = new Date(message.created_at).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
    
    const messageTime = new Date(message.created_at).toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit'
    });

    // Create the message URL
    const messageUrl = orderRequest 
      ? `${frontendUrl}/farms/${farm.id}/orders/${orderRequest.id}/messages`
      : `${frontendUrl}/farms/${farm.id}/messages`;

    // Replace placeholders in template
    emailTemplate = emailTemplate
      .replace(/{{farmAdminName}}/g, `${farmAdmin.first_name} ${farmAdmin.last_name}`)
      .replace(/{{customerName}}/g, customer.name)
      .replace(/{{customerEmail}}/g, customer.email)
      .replace(/{{customerPhone}}/g, customer.phone || 'Not provided')
      .replace(/{{messageContent}}/g, message.content)
      .replace(/{{messageDate}}/g, messageDate)
      .replace(/{{messageTime}}/g, messageTime)
      .replace(/{{messageUrl}}/g, messageUrl)
      .replace(/{{email}}/g, farmAdmin.email)
      .replace(/{{year}}/g, new Date().getFullYear());

    // Handle conditional order reference
    if (orderRequest) {
      emailTemplate = emailTemplate.replace(/{{#if orderReference}}([\s\S]*?){{\/if}}/g, '$1')
        .replace(/{{orderId}}/g, orderRequest.id)
        .replace(/{{orderDate}}/g, new Date(orderRequest.created_at).toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        }))
        .replace(/{{orderStatus}}/g, orderRequest.status.charAt(0).toUpperCase() + orderRequest.status.slice(1));
    } else {
      emailTemplate = emailTemplate.replace(/{{#if orderReference}}[\s\S]*?{{\/if}}/g, '');
    }

    // Set up email options
    const mailOptions = {
      from: `"NxtAcre Marketplace" <<EMAIL>>`,
      to: farmAdmin.email,
      subject: `New Message from ${customer.name}`,
      html: emailTemplate,
      headers: {
        'Message-ID': `<customer-message-${message.id}-${Date.now()}@nxtacre.com>`,
        'X-Message-ID': message.id,
        'X-Farm-ID': farm.id,
        'X-Customer-ID': customer.id
      }
    };

    // Send email
    const result = await transporter.sendMail(mailOptions);
    return result;
  } catch (error) {
    console.error('Error sending customer message received email:', error);
    throw error;
  }
};