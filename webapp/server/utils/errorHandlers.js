/**
 * Utility functions for handling server errors
 */

/**
 * Handle server errors in a consistent way
 * @param {Object} res - Express response object
 * @param {Error} error - Error object
 * @returns {Object} Response with appropriate error message
 */
export const handleServerError = (res, error) => {
  console.error('Server error:', error);
  
  // Determine if this is a known error type
  if (error.name === 'SequelizeValidationError' || error.name === 'SequelizeUniqueConstraintError') {
    return res.status(400).json({ 
      error: 'Validation error', 
      details: error.errors.map(e => e.message) 
    });
  }
  
  if (error.name === 'SequelizeForeignKeyConstraintError') {
    return res.status(400).json({ 
      error: 'Foreign key constraint error', 
      details: 'The referenced record does not exist' 
    });
  }
  
  if (error.name === 'SequelizeDatabaseError') {
    return res.status(500).json({ 
      error: 'Database error', 
      details: process.env.NODE_ENV === 'development' ? error.message : 'An error occurred while processing your request' 
    });
  }
  
  // Default server error
  return res.status(500).json({ 
    error: 'Server error', 
    details: process.env.NODE_ENV === 'development' ? error.message : 'An error occurred while processing your request' 
  });
};