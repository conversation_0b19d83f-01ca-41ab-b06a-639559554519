import axios from 'axios';
import dotenv from 'dotenv';

dotenv.config();

// National Weather Service API base URL
const NWS_API_BASE = 'https://api.weather.gov';

// Tomorrow.io API key and base URL
const TOMORROW_API_KEY = process.env.TOMORROW_API_KEY;
const TOMORROW_API_BASE = 'https://api.tomorrow.io/v4';

// Open-Meteo API base URL
const OPEN_METEO_API_URL = process.env.OPEN_METEO_API_URL || 'https://api.open-meteo.com/v1';

// Map NWS forecast icons to our icon codes (similar to OpenWeatherMap)
const mapNWSIconToCode = (iconUrl) => {
  if (!iconUrl) return '01d'; // default to clear day

  // Extract the icon name from the URL
  const iconName = iconUrl.split('/').pop().replace(/\?.*$/, '');

  // Check if it's day or night
  const isDay = !iconName.includes('night');
  const suffix = isDay ? 'd' : 'n';

  // Map NWS icons to codes similar to OpenWeatherMap
  if (iconName.includes('skc') || iconName.includes('few')) return `01${suffix}`; // clear
  if (iconName.includes('sct')) return `02${suffix}`; // partly cloudy
  if (iconName.includes('bkn')) return `03${suffix}`; // mostly cloudy
  if (iconName.includes('ovc')) return `04${suffix}`; // overcast
  if (iconName.includes('rain') || iconName.includes('showers')) return `09${suffix}`; // rain
  if (iconName.includes('tsra')) return `11${suffix}`; // thunderstorm
  if (iconName.includes('snow')) return `13${suffix}`; // snow
  if (iconName.includes('fog') || iconName.includes('haze')) return `50${suffix}`; // fog

  return `01${suffix}`; // default to clear
};

// Map NWS forecast descriptions to conditions (similar to OpenWeatherMap)
const mapNWSDescriptionToCondition = (description) => {
  if (!description) return 'Clear';

  const desc = description.toLowerCase();

  if (desc.includes('sunny') || desc.includes('clear')) return 'Clear';
  if (desc.includes('cloud')) return 'Clouds';
  if (desc.includes('rain') || desc.includes('shower')) return 'Rain';
  if (desc.includes('thunderstorm') || desc.includes('tstorm')) return 'Thunderstorm';
  if (desc.includes('snow') || desc.includes('flurries')) return 'Snow';
  if (desc.includes('fog') || desc.includes('mist') || desc.includes('haze')) return 'Mist';

  return 'Clear'; // default
};

// Map Tomorrow.io weather codes to our icon codes
const mapTomorrowCodeToIcon = (weatherCode, isDay = true) => {
  const suffix = isDay ? 'd' : 'n';
  
  // Map Tomorrow.io weather codes to our icon codes
  switch (weatherCode) {
    case 1000: // Clear, Sunny
      return `01${suffix}`;
    case 1100: // Mostly Clear
      return `02${suffix}`;
    case 1101: // Partly Cloudy
      return `02${suffix}`;
    case 1102: // Mostly Cloudy
      return `03${suffix}`;
    case 1001: // Cloudy
      return `04${suffix}`;
    case 2000: // Fog
    case 2100: // Light Fog
      return `50${suffix}`;
    case 4000: // Drizzle
    case 4001: // Rain
    case 4200: // Light Rain
      return `09${suffix}`;
    case 4201: // Heavy Rain
      return `10${suffix}`;
    case 5000: // Snow
    case 5001: // Flurries
    case 5100: // Light Snow
      return `13${suffix}`;
    case 5101: // Heavy Snow
      return `13${suffix}`;
    case 6000: // Freezing Drizzle
    case 6001: // Freezing Rain
    case 6200: // Light Freezing Rain
    case 6201: // Heavy Freezing Rain
      return `09${suffix}`;
    case 7000: // Ice Pellets
    case 7101: // Heavy Ice Pellets
    case 7102: // Light Ice Pellets
      return `13${suffix}`;
    case 8000: // Thunderstorm
      return `11${suffix}`;
    default:
      return `01${suffix}`; // Default to clear
  }
};

// Map Tomorrow.io weather codes to conditions
const mapTomorrowCodeToCondition = (weatherCode) => {
  switch (weatherCode) {
    case 1000: // Clear, Sunny
      return 'Clear';
    case 1100: // Mostly Clear
    case 1101: // Partly Cloudy
    case 1102: // Mostly Cloudy
    case 1001: // Cloudy
      return 'Clouds';
    case 2000: // Fog
    case 2100: // Light Fog
      return 'Mist';
    case 4000: // Drizzle
    case 4001: // Rain
    case 4200: // Light Rain
    case 4201: // Heavy Rain
      return 'Rain';
    case 5000: // Snow
    case 5001: // Flurries
    case 5100: // Light Snow
    case 5101: // Heavy Snow
      return 'Snow';
    case 6000: // Freezing Drizzle
    case 6001: // Freezing Rain
    case 6200: // Light Freezing Rain
    case 6201: // Heavy Freezing Rain
      return 'Rain';
    case 7000: // Ice Pellets
    case 7101: // Heavy Ice Pellets
    case 7102: // Light Ice Pellets
      return 'Snow';
    case 8000: // Thunderstorm
      return 'Thunderstorm';
    default:
      return 'Clear'; // Default to clear
  }
};

// Map Open-Meteo weather codes to our icon codes
const mapOpenMeteoCodeToIcon = (weatherCode, isDay = true) => {
  const suffix = isDay ? 'd' : 'n';
  
  // Map WMO weather codes to our icon codes
  // https://open-meteo.com/en/docs
  switch (weatherCode) {
    case 0: // Clear sky
      return `01${suffix}`;
    case 1: // Mainly clear
      return `02${suffix}`;
    case 2: // Partly cloudy
      return `02${suffix}`;
    case 3: // Overcast
      return `04${suffix}`;
    case 45: // Fog
    case 48: // Depositing rime fog
      return `50${suffix}`;
    case 51: // Light drizzle
    case 53: // Moderate drizzle
    case 55: // Dense drizzle
    case 56: // Light freezing drizzle
    case 57: // Dense freezing drizzle
      return `09${suffix}`;
    case 61: // Slight rain
    case 63: // Moderate rain
    case 65: // Heavy rain
    case 66: // Light freezing rain
    case 67: // Heavy freezing rain
      return `10${suffix}`;
    case 71: // Slight snow fall
    case 73: // Moderate snow fall
    case 75: // Heavy snow fall
    case 77: // Snow grains
      return `13${suffix}`;
    case 80: // Slight rain showers
    case 81: // Moderate rain showers
    case 82: // Violent rain showers
      return `09${suffix}`;
    case 85: // Slight snow showers
    case 86: // Heavy snow showers
      return `13${suffix}`;
    case 95: // Thunderstorm
    case 96: // Thunderstorm with slight hail
    case 99: // Thunderstorm with heavy hail
      return `11${suffix}`;
    default:
      return `01${suffix}`; // Default to clear
  }
};

// Map Open-Meteo weather codes to conditions
const mapOpenMeteoCodeToCondition = (weatherCode) => {
  // Map WMO weather codes to conditions
  switch (weatherCode) {
    case 0: // Clear sky
      return 'Clear';
    case 1: // Mainly clear
    case 2: // Partly cloudy
    case 3: // Overcast
      return 'Clouds';
    case 45: // Fog
    case 48: // Depositing rime fog
      return 'Mist';
    case 51: // Light drizzle
    case 53: // Moderate drizzle
    case 55: // Dense drizzle
    case 56: // Light freezing drizzle
    case 57: // Dense freezing drizzle
    case 61: // Slight rain
    case 63: // Moderate rain
    case 65: // Heavy rain
    case 66: // Light freezing rain
    case 67: // Heavy freezing rain
    case 80: // Slight rain showers
    case 81: // Moderate rain showers
    case 82: // Violent rain showers
      return 'Rain';
    case 71: // Slight snow fall
    case 73: // Moderate snow fall
    case 75: // Heavy snow fall
    case 77: // Snow grains
    case 85: // Slight snow showers
    case 86: // Heavy snow showers
      return 'Snow';
    case 95: // Thunderstorm
    case 96: // Thunderstorm with slight hail
    case 99: // Thunderstorm with heavy hail
      return 'Thunderstorm';
    default:
      return 'Clear'; // Default to clear
  }
};

// Helper function to determine if it's day or night
const isDayTime = (timestamp, latitude, longitude) => {
  const date = new Date(timestamp);
  const hour = date.getHours();
  
  // Simple approximation: daytime is between 6 AM and 6 PM
  // A more accurate calculation would use sunrise/sunset based on location
  return hour >= 6 && hour < 18;
};

// NWS Weather Service Implementation
export const fetchNWSWeatherData = async (latitude, longitude) => {
  try {
    // Step 1: Get the grid point for the location
    const pointsUrl = `${NWS_API_BASE}/points/${latitude},${longitude}`;
    let pointsResponse;
    try {
      pointsResponse = await axios.get(pointsUrl, {
        headers: {
          'User-Agent': '(NxtAcre Farm Management, <EMAIL>)',
          'Accept': 'application/geo+json'
        }
      });
    } catch (error) {
      console.error('Error fetching grid points:', error);
      throw new Error(`Failed to fetch grid points: ${error.message}`);
    }

    const { gridId, gridX, gridY } = pointsResponse.data.properties;

    // Step 2: Get the forecast for the grid point
    const forecastUrl = `${NWS_API_BASE}/gridpoints/${gridId}/${gridX},${gridY}/forecast`;
    let forecastResponse;
    try {
      forecastResponse = await axios.get(forecastUrl, {
        headers: {
          'User-Agent': '(NxtAcre Farm Management, <EMAIL>)',
          'Accept': 'application/geo+json'
        }
      });
    } catch (error) {
      console.error('Error fetching forecast:', error);
      throw new Error(`Failed to fetch forecast: ${error.message}`);
    }

    // Step 3: Get the hourly forecast for current conditions
    const hourlyForecastUrl = `${NWS_API_BASE}/gridpoints/${gridId}/${gridX},${gridY}/forecast/hourly`;
    let hourlyForecastResponse;
    try {
      hourlyForecastResponse = await axios.get(hourlyForecastUrl, {
        headers: {
          'User-Agent': '(NxtAcre Farm Management, <EMAIL>)',
          'Accept': 'application/geo+json'
        }
      });
    } catch (error) {
      console.error('Error fetching hourly forecast:', error);
      throw new Error(`Failed to fetch hourly forecast: ${error.message}`);
    }

    // Process current weather data (from the first hourly forecast period)
    const currentPeriod = hourlyForecastResponse.data.properties.periods[0];

    const current = {
      temp: Math.round(currentPeriod.temperature),
      feels_like: Math.round(currentPeriod.temperature), // NWS doesn't provide feels_like
      condition: mapNWSDescriptionToCondition(currentPeriod.shortForecast),
      icon: mapNWSIconToCode(currentPeriod.icon),
      wind_speed: currentPeriod.windSpeed,
      wind_direction: currentPeriod.windDirection,
      precipitation_chance: currentPeriod.probabilityOfPrecipitation?.value || 0,
      humidity: currentPeriod.relativeHumidity?.value || 0,
      timestamp: currentPeriod.startTime
    };

    // Process hourly forecast data
    const hourlyPeriods = hourlyForecastResponse.data.properties.periods;
    const hourlyForecast = hourlyPeriods.slice(0, 24).map(period => {
      return {
        time: period.startTime,
        temp: Math.round(period.temperature),
        condition: mapNWSDescriptionToCondition(period.shortForecast),
        icon: mapNWSIconToCode(period.icon),
        wind_speed: period.windSpeed,
        wind_direction: period.windDirection,
        precipitation_chance: period.probabilityOfPrecipitation?.value || 0,
        humidity: period.relativeHumidity?.value || 0
      };
    });

    // Process forecast data
    const dailyPeriods = forecastResponse.data.properties.periods;
    const dailyForecasts = {};

    // NWS API returns separate periods for day and night
    // We need to combine them to get high/low temps for each day
    dailyPeriods.forEach((period) => {
      const date = new Date(period.startTime);
      const day = date.toLocaleDateString('en-US', { weekday: 'short' });
      const isDaytime = period.isDaytime;

      if (!dailyForecasts[day]) {
        dailyForecasts[day] = { 
          high: isDaytime ? period.temperature : -Infinity,
          low: !isDaytime ? period.temperature : Infinity,
          condition: isDaytime ? mapNWSDescriptionToCondition(period.shortForecast) : '',
          icon: isDaytime ? mapNWSIconToCode(period.icon) : '',
          date: date,
          wind_speed: period.windSpeed,
          wind_direction: period.windDirection,
          precipitation_chance: period.probabilityOfPrecipitation?.value || 0,
          humidity: period.relativeHumidity?.value || 0
        };
      } else {
        if (isDaytime) {
          dailyForecasts[day].high = period.temperature;
          dailyForecasts[day].condition = mapNWSDescriptionToCondition(period.shortForecast);
          dailyForecasts[day].icon = mapNWSIconToCode(period.icon);
        } else {
          dailyForecasts[day].low = period.temperature;
        }
      }
    });

    // Convert to array and limit to 7 days
    const forecast = Object.entries(dailyForecasts)
      .sort((a, b) => a[1].date - b[1].date) // Sort by date
      .map(([day, data]) => {
        return {
          day: day === new Date().toLocaleDateString('en-US', { weekday: 'short' }) ? 'Today' : day,
          date: data.date.toISOString().split('T')[0],
          high: Math.round(data.high),
          low: Math.round(data.low),
          condition: data.condition,
          icon: data.icon,
          wind_speed: data.wind_speed,
          wind_direction: data.wind_direction,
          precipitation_chance: data.precipitation_chance,
          humidity: data.humidity
        };
      })
      .slice(0, 7);

    return { current, forecast, hourly: hourlyForecast, provider: 'nws' };
  } catch (error) {
    console.error('Error fetching NWS weather data:', error);
    throw new Error(`Failed to fetch NWS weather data: ${error.message}`);
  }
};

// Tomorrow.io Weather Service Implementation
export const fetchTomorrowWeatherData = async (latitude, longitude) => {
  try {
    if (!TOMORROW_API_KEY) {
      throw new Error('Tomorrow.io API key is not configured');
    }

    // Fetch current conditions
    const currentUrl = `${TOMORROW_API_BASE}/weather/realtime?location=${latitude},${longitude}&units=imperial&apikey=${TOMORROW_API_KEY}`;
    let currentResponse;
    try {
      currentResponse = await axios.get(currentUrl);
    } catch (error) {
      console.error('Error fetching Tomorrow.io current weather:', error);
      throw new Error(`Failed to fetch Tomorrow.io current weather: ${error.message}`);
    }

    // Fetch forecast
    const forecastUrl = `${TOMORROW_API_BASE}/weather/forecast?location=${latitude},${longitude}&timesteps=1d&units=imperial&apikey=${TOMORROW_API_KEY}`;
    let forecastResponse;
    try {
      forecastResponse = await axios.get(forecastUrl);
    } catch (error) {
      console.error('Error fetching Tomorrow.io forecast:', error);
      throw new Error(`Failed to fetch Tomorrow.io forecast: ${error.message}`);
    }

    // Fetch hourly forecast
    const hourlyUrl = `${TOMORROW_API_BASE}/weather/forecast?location=${latitude},${longitude}&timesteps=1h&units=imperial&apikey=${TOMORROW_API_KEY}`;
    let hourlyResponse;
    try {
      hourlyResponse = await axios.get(hourlyUrl);
    } catch (error) {
      console.error('Error fetching Tomorrow.io hourly forecast:', error);
      throw new Error(`Failed to fetch Tomorrow.io hourly forecast: ${error.message}`);
    }

    // Process current weather data
    const currentData = currentResponse.data.data;
    const currentValues = currentData.values;
    const currentTime = new Date(currentData.time);
    const isDay = isDayTime(currentTime, latitude, longitude);

    const current = {
      temp: Math.round(currentValues.temperature),
      feels_like: Math.round(currentValues.temperatureApparent),
      condition: mapTomorrowCodeToCondition(currentValues.weatherCode),
      icon: mapTomorrowCodeToIcon(currentValues.weatherCode, isDay),
      wind_speed: `${Math.round(currentValues.windSpeed)} mph`,
      wind_direction: currentValues.windDirection.toString(),
      precipitation_chance: currentValues.precipitationProbability || 0,
      humidity: currentValues.humidity || 0,
      timestamp: currentData.time
    };

    // Process forecast data
    const forecastData = forecastResponse.data.timelines.daily;
    const forecast = forecastData.map(day => {
      const date = new Date(day.time);
      const dayName = date.toLocaleDateString('en-US', { weekday: 'short' });
      const isToday = date.toDateString() === new Date().toDateString();
      
      return {
        day: isToday ? 'Today' : dayName,
        date: day.time.split('T')[0],
        high: Math.round(day.values.temperatureMax),
        low: Math.round(day.values.temperatureMin),
        condition: mapTomorrowCodeToCondition(day.values.weatherCodeMax),
        icon: mapTomorrowCodeToIcon(day.values.weatherCodeMax, true),
        wind_speed: `${Math.round(day.values.windSpeedAvg)} mph`,
        wind_direction: day.values.windDirectionAvg.toString(),
        precipitation_chance: day.values.precipitationProbabilityAvg || 0,
        humidity: day.values.humidityAvg || 0
      };
    }).slice(0, 7);

    // Process hourly forecast data
    const hourlyData = hourlyResponse.data.timelines.hourly;
    const hourlyForecast = hourlyData.slice(0, 24).map(hour => {
      const hourTime = new Date(hour.time);
      const isHourDay = isDayTime(hourTime, latitude, longitude);
      
      return {
        time: hour.time,
        temp: Math.round(hour.values.temperature),
        condition: mapTomorrowCodeToCondition(hour.values.weatherCode),
        icon: mapTomorrowCodeToIcon(hour.values.weatherCode, isHourDay),
        wind_speed: `${Math.round(hour.values.windSpeed)} mph`,
        wind_direction: hour.values.windDirection.toString(),
        precipitation_chance: hour.values.precipitationProbability || 0,
        humidity: hour.values.humidity || 0
      };
    });

    return { current, forecast, hourly: hourlyForecast, provider: 'tomorrow' };
  } catch (error) {
    console.error('Error fetching Tomorrow.io weather data:', error);
    throw new Error(`Failed to fetch Tomorrow.io weather data: ${error.message}`);
  }
};

// Open-Meteo Weather Service Implementation
export const fetchOpenMeteoWeatherData = async (latitude, longitude) => {
  try {
    // Fetch current conditions, forecast, and hourly forecast in one request
    const url = `${OPEN_METEO_API_URL}/forecast?latitude=${latitude}&longitude=${longitude}&current=temperature,apparent_temperature,relative_humidity,precipitation,weather_code,wind_speed_10m,wind_direction_10m&hourly=temperature_2m,relative_humidity_2m,precipitation_probability,weather_code,wind_speed_10m,wind_direction_10m&daily=weather_code,temperature_2m_max,temperature_2m_min,precipitation_probability_max&temperature_unit=fahrenheit&wind_speed_unit=mph&precipitation_unit=inch&timezone=auto`;
    
    let response;
    try {
      response = await axios.get(url);
    } catch (error) {
      console.error('Error fetching Open-Meteo weather data:', error);
      throw new Error(`Failed to fetch Open-Meteo weather data: ${error.message}`);
    }

    const data = response.data;
    
    // Process current weather data
    const currentData = data.current;
    const currentTime = new Date(currentData.time);
    const isDay = isDayTime(currentTime, latitude, longitude);

    const current = {
      temp: Math.round(currentData.temperature),
      feels_like: Math.round(currentData.apparent_temperature),
      condition: mapOpenMeteoCodeToCondition(currentData.weather_code),
      icon: mapOpenMeteoCodeToIcon(currentData.weather_code, isDay),
      wind_speed: `${Math.round(currentData.wind_speed_10m)} mph`,
      wind_direction: currentData.wind_direction_10m.toString(),
      precipitation_chance: data.hourly.precipitation_probability[0] || 0,
      humidity: currentData.relative_humidity || 0,
      timestamp: currentData.time
    };

    // Process daily forecast data
    const forecast = [];
    for (let i = 0; i < data.daily.time.length && i < 7; i++) {
      const date = new Date(data.daily.time[i]);
      const dayName = date.toLocaleDateString('en-US', { weekday: 'short' });
      const isToday = date.toDateString() === new Date().toDateString();
      
      forecast.push({
        day: isToday ? 'Today' : dayName,
        date: data.daily.time[i],
        high: Math.round(data.daily.temperature_2m_max[i]),
        low: Math.round(data.daily.temperature_2m_min[i]),
        condition: mapOpenMeteoCodeToCondition(data.daily.weather_code[i]),
        icon: mapOpenMeteoCodeToIcon(data.daily.weather_code[i], true),
        wind_speed: `${Math.round(data.hourly.wind_speed_10m[i * 24])} mph`,
        wind_direction: data.hourly.wind_direction_10m[i * 24].toString(),
        precipitation_chance: data.daily.precipitation_probability_max[i] || 0,
        humidity: data.hourly.relative_humidity_2m[i * 24] || 0
      });
    }

    // Process hourly forecast data
    const hourlyForecast = [];
    for (let i = 0; i < 24; i++) {
      const hourTime = new Date(data.hourly.time[i]);
      const isHourDay = isDayTime(hourTime, latitude, longitude);
      
      hourlyForecast.push({
        time: data.hourly.time[i],
        temp: Math.round(data.hourly.temperature_2m[i]),
        condition: mapOpenMeteoCodeToCondition(data.hourly.weather_code[i]),
        icon: mapOpenMeteoCodeToIcon(data.hourly.weather_code[i], isHourDay),
        wind_speed: `${Math.round(data.hourly.wind_speed_10m[i])} mph`,
        wind_direction: data.hourly.wind_direction_10m[i].toString(),
        precipitation_chance: data.hourly.precipitation_probability[i] || 0,
        humidity: data.hourly.relative_humidity_2m[i] || 0
      });
    }

    return { current, forecast, hourly: hourlyForecast, provider: 'open-meteo' };
  } catch (error) {
    console.error('Error fetching Open-Meteo weather data:', error);
    throw new Error(`Failed to fetch Open-Meteo weather data: ${error.message}`);
  }
};

// Helper function to fetch weather data from all providers and return the first successful one
export const fetchWeatherData = async (latitude, longitude, preferredProvider = null) => {
  // Define the order of providers to try
  let providers = ['nws', 'tomorrow', 'open-meteo'];
  
  // If a preferred provider is specified, try it first
  if (preferredProvider && providers.includes(preferredProvider)) {
    providers = [preferredProvider, ...providers.filter(p => p !== preferredProvider)];
  }
  
  let lastError = null;
  
  // Try each provider in order until one succeeds
  for (const provider of providers) {
    try {
      let weatherData;
      
      switch (provider) {
        case 'nws':
          weatherData = await fetchNWSWeatherData(latitude, longitude);
          break;
        case 'tomorrow':
          weatherData = await fetchTomorrowWeatherData(latitude, longitude);
          break;
        case 'open-meteo':
          weatherData = await fetchOpenMeteoWeatherData(latitude, longitude);
          break;
        default:
          continue;
      }
      
      return weatherData;
    } catch (error) {
      console.error(`Error fetching weather data from ${provider}:`, error);
      lastError = error;
      // Continue to the next provider
    }
  }
  
  // If all providers failed, throw the last error
  throw lastError || new Error('Failed to fetch weather data from all providers');
};

// Helper function to fetch detailed forecast
export const fetchDetailedForecast = async (latitude, longitude, days = 7, preferredProvider = null) => {
  const weatherData = await fetchWeatherData(latitude, longitude, preferredProvider);
  return { forecast: weatherData.forecast.slice(0, days) };
};

// Helper function to fetch hourly forecast
export const fetchHourlyForecast = async (latitude, longitude, hours = 48, preferredProvider = null) => {
  const weatherData = await fetchWeatherData(latitude, longitude, preferredProvider);
  return { hourly: weatherData.hourly.slice(0, hours) };
};