import axios from 'axios';
import dotenv from 'dotenv';
import { makeApiRequestWithCaching, getApiProviderByName, getApiEndpointByName } from '../utils/apiDataService.js';

dotenv.config();

/**
 * Service for interacting with the Digital Ocean API
 * This service provides functions for managing domains and DNS records
 */
class DigitalOceanService {
  constructor() {
    this.apiKey = process.env.DIGITAL_OCEAN_API_KEY;
    this.baseUrl = 'https://api.digitalocean.com/v2';
    this.providerId = null;
    this.endpointIds = {
      addDomain: null,
      getDomain: null,
      addRecord: null,
      getRecords: null,
      deleteRecord: null
    };
  }

  /**
   * Initialize the service by getting API provider and endpoint IDs
   */
  async initialize() {
    try {
      // Get the Digital Ocean API provider
      const provider = await getApiProviderByName('Digital Ocean');
      if (!provider) {
        console.error('Digital Ocean API provider not found');
        return false;
      }
      this.providerId = provider.id;

      // Get endpoint IDs
      const endpoints = {
        addDomain: await getApiEndpointByName(this.providerId, 'addDomain'),
        getDomain: await getApiEndpointByName(this.providerId, 'getDomain'),
        addRecord: await getApiEndpointByName(this.providerId, 'addRecord'),
        getRecords: await getApiEndpointByName(this.providerId, 'getRecords'),
        deleteRecord: await getApiEndpointByName(this.providerId, 'deleteRecord')
      };

      // Check if all endpoints exist
      for (const [key, endpoint] of Object.entries(endpoints)) {
        if (!endpoint) {
          console.error(`Digital Ocean API endpoint ${key} not found`);
          return false;
        }
        this.endpointIds[key] = endpoint.id;
      }

      return true;
    } catch (error) {
      console.error('Error initializing Digital Ocean service:', error);
      return false;
    }
  }

  /**
   * Get headers for Digital Ocean API requests
   * @returns {Object} - Headers for API requests
   */
  getHeaders() {
    return {
      'Authorization': `Bearer ${this.apiKey}`,
      'Content-Type': 'application/json'
    };
  }

  /**
   * Add a domain to Digital Ocean
   * @param {string} domain - The domain name to add
   * @param {string} farmId - The ID of the farm
   * @returns {Object} - The response from the API
   */
  async addDomain(domain, farmId) {
    try {
      const requestData = {
        farmId,
        params: { domain },
        headers: this.getHeaders()
      };

      const requestFn = async () => {
        const response = await axios.post(
          `${this.baseUrl}/domains`,
          { name: domain },
          { headers: this.getHeaders() }
        );
        return response;
      };

      const response = await makeApiRequestWithCaching(
        this.endpointIds.addDomain,
        requestData,
        requestFn
      );

      return response.data;
    } catch (error) {
      console.error('Error adding domain to Digital Ocean:', error);
      throw error;
    }
  }

  /**
   * Get a domain from Digital Ocean
   * @param {string} domain - The domain name to get
   * @param {string} farmId - The ID of the farm
   * @returns {Object} - The response from the API
   */
  async getDomain(domain, farmId) {
    try {
      const requestData = {
        farmId,
        params: { domain },
        headers: this.getHeaders()
      };

      const requestFn = async () => {
        const response = await axios.get(
          `${this.baseUrl}/domains/${domain}`,
          { headers: this.getHeaders() }
        );
        return response;
      };

      const response = await makeApiRequestWithCaching(
        this.endpointIds.getDomain,
        requestData,
        requestFn
      );

      return response.data;
    } catch (error) {
      console.error('Error getting domain from Digital Ocean:', error);
      throw error;
    }
  }

  /**
   * Add a CNAME record to a domain
   * @param {string} domain - The domain name
   * @param {string} name - The name of the record (e.g., 'www')
   * @param {string} data - The data for the record (e.g., 'example.com')
   * @param {string} farmId - The ID of the farm
   * @returns {Object} - The response from the API
   */
  async addCnameRecord(domain, name, data, farmId) {
    try {
      const requestData = {
        farmId,
        params: { domain, name, data },
        headers: this.getHeaders()
      };

      const requestFn = async () => {
        const response = await axios.post(
          `${this.baseUrl}/domains/${domain}/records`,
          {
            type: 'CNAME',
            name,
            data,
            ttl: 3600
          },
          { headers: this.getHeaders() }
        );
        return response;
      };

      const response = await makeApiRequestWithCaching(
        this.endpointIds.addRecord,
        requestData,
        requestFn
      );

      return response.data;
    } catch (error) {
      console.error('Error adding CNAME record to Digital Ocean:', error);
      throw error;
    }
  }

  /**
   * Get all DNS records for a domain
   * @param {string} domain - The domain name
   * @param {string} farmId - The ID of the farm
   * @returns {Object} - The response from the API
   */
  async getDnsRecords(domain, farmId) {
    try {
      const requestData = {
        farmId,
        params: { domain },
        headers: this.getHeaders()
      };

      const requestFn = async () => {
        const response = await axios.get(
          `${this.baseUrl}/domains/${domain}/records`,
          { headers: this.getHeaders() }
        );
        return response;
      };

      const response = await makeApiRequestWithCaching(
        this.endpointIds.getRecords,
        requestData,
        requestFn
      );

      return response.data;
    } catch (error) {
      console.error('Error getting DNS records from Digital Ocean:', error);
      throw error;
    }
  }

  /**
   * Delete a DNS record
   * @param {string} domain - The domain name
   * @param {string} recordId - The ID of the record to delete
   * @param {string} farmId - The ID of the farm
   * @returns {boolean} - Whether the record was successfully deleted
   */
  async deleteDnsRecord(domain, recordId, farmId) {
    try {
      const requestData = {
        farmId,
        params: { domain, recordId },
        headers: this.getHeaders()
      };

      const requestFn = async () => {
        const response = await axios.delete(
          `${this.baseUrl}/domains/${domain}/records/${recordId}`,
          { headers: this.getHeaders() }
        );
        return response;
      };

      await makeApiRequestWithCaching(
        this.endpointIds.deleteRecord,
        requestData,
        requestFn
      );

      return true;
    } catch (error) {
      console.error('Error deleting DNS record from Digital Ocean:', error);
      throw error;
    }
  }

  /**
   * Set up a custom domain for a farm
   * @param {string} customDomain - The custom domain to set up
   * @param {string} subdomain - The farm's subdomain
   * @param {string} farmId - The ID of the farm
   * @returns {Object} - The result of the operation
   */
  async setupCustomDomain(customDomain, subdomain, farmId) {
    try {
      // Add the domain to Digital Ocean
      await this.addDomain(customDomain, farmId);

      // Add CNAME record pointing to the farm's subdomain
      const mainDomain = process.env.VITE_MAIN_DOMAIN || 'nxtacre.com';
      const targetDomain = `${subdomain}.${mainDomain}`;
      
      // Add www CNAME record
      await this.addCnameRecord(customDomain, 'www', targetDomain, farmId);
      
      // Add @ CNAME record (root domain)
      await this.addCnameRecord(customDomain, '@', targetDomain, farmId);

      return {
        success: true,
        message: 'Custom domain set up successfully',
        domain: customDomain,
        targetDomain
      };
    } catch (error) {
      console.error('Error setting up custom domain:', error);
      return {
        success: false,
        message: error.response?.data?.message || error.message,
        error
      };
    }
  }

  /**
   * Verify a custom domain
   * @param {string} customDomain - The custom domain to verify
   * @param {string} farmId - The ID of the farm
   * @returns {Object} - The result of the verification
   */
  async verifyCustomDomain(customDomain, farmId) {
    try {
      // Get the domain from Digital Ocean
      const domainResponse = await this.getDomain(customDomain, farmId);
      
      if (!domainResponse || !domainResponse.domain) {
        return {
          success: false,
          verified: false,
          message: 'Domain not found on Digital Ocean'
        };
      }

      // Get DNS records for the domain
      const recordsResponse = await this.getDnsRecords(customDomain, farmId);
      
      if (!recordsResponse || !recordsResponse.domain_records) {
        return {
          success: false,
          verified: false,
          message: 'DNS records not found for domain'
        };
      }

      // Check if CNAME records exist
      const cnameRecords = recordsResponse.domain_records.filter(
        record => record.type === 'CNAME'
      );

      if (cnameRecords.length === 0) {
        return {
          success: false,
          verified: false,
          message: 'No CNAME records found for domain'
        };
      }

      // Domain is verified if we got this far
      return {
        success: true,
        verified: true,
        message: 'Domain verified successfully',
        records: cnameRecords
      };
    } catch (error) {
      console.error('Error verifying custom domain:', error);
      return {
        success: false,
        verified: false,
        message: error.response?.data?.message || error.message,
        error
      };
    }
  }
}

export default new DigitalOceanService();