/**
 * iOS Phone Book API Client
 * This module provides methods to interact with the iOS Contacts API
 */

import axios from 'axios';
import logger from '../utils/logger.js';
import dotenv from 'dotenv';

dotenv.config();

// iOS Contacts API configuration
const IOS_CONTACTS_API_URL = process.env.IOS_CONTACTS_API_URL || 'https://api.apple.com/contacts';
const IOS_CONTACTS_API_KEY = process.env.IOS_CONTACTS_API_KEY;

// Validate required environment variables
if (!IOS_CONTACTS_API_KEY) {
  logger.warn('iOS Contacts API key is not set. iOS phone book integration will not work properly.');
}

/**
 * iOS Phone Book API Client
 */
class IOSPhoneBookAPI {
  /**
   * Create an instance of the iOS Phone Book API client
   */
  constructor() {
    this.client = axios.create({
      baseURL: IOS_CONTACTS_API_URL,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${IOS_CONTACTS_API_KEY}`,
        'User-Agent': 'NxtAcre Farm Management/1.0'
      }
    });
  }

  /**
   * Add or update a contact in the iOS phone book
   * @param {Object} contactData - The contact data
   * @param {string} contactData.name - The contact name
   * @param {string} contactData.phone - The contact phone number
   * @param {string} contactData.email - The contact email
   * @param {string} contactData.address - The contact address
   * @param {string} contactData.city - The contact city
   * @param {string} contactData.state - The contact state
   * @param {string} contactData.zipCode - The contact zip code
   * @param {string} contactData.country - The contact country
   * @param {string} [contactData.externalId] - The existing contact ID (for updates)
   * @returns {Promise<Object>} The created or updated contact
   */
  async addContact(contactData) {
    try {
      // Format the contact data according to iOS Contacts API requirements
      const formattedContact = this.formatContactData(contactData);
      
      let response;
      
      // If external ID exists, update the contact, otherwise create a new one
      if (contactData.externalId) {
        logger.info(`Updating iOS contact: ${contactData.externalId}`);
        response = await this.client.put(`/contacts/${contactData.externalId}`, formattedContact);
      } else {
        logger.info('Creating new iOS contact');
        response = await this.client.post('/contacts', formattedContact);
      }
      
      // Return the contact data with the external ID
      return {
        id: response.data.id,
        success: true,
        message: contactData.externalId ? 'Contact updated successfully' : 'Contact created successfully'
      };
    } catch (error) {
      logger.error('Error adding/updating iOS contact:', error);
      
      // Handle specific API errors
      if (error.response) {
        const status = error.response.status;
        const errorData = error.response.data;
        
        if (status === 401 || status === 403) {
          throw new Error('Authentication failed with iOS Contacts API. Check your API key.');
        } else if (status === 400) {
          throw new Error(`Invalid contact data: ${errorData.message || 'Unknown error'}`);
        } else if (status === 404 && contactData.externalId) {
          // If contact not found during update, try to create a new one
          logger.warn(`Contact ${contactData.externalId} not found in iOS, creating new contact`);
          return this.addContact({ ...contactData, externalId: null });
        }
      }
      
      throw new Error(`iOS Contacts API error: ${error.message}`);
    }
  }

  /**
   * Remove a contact from the iOS phone book
   * @param {string} contactId - The contact ID
   * @returns {Promise<Object>} The result of the operation
   */
  async removeContact(contactId) {
    try {
      if (!contactId) {
        throw new Error('Contact ID is required');
      }
      
      logger.info(`Removing iOS contact: ${contactId}`);
      await this.client.delete(`/contacts/${contactId}`);
      
      return {
        success: true,
        message: 'Contact removed successfully'
      };
    } catch (error) {
      logger.error('Error removing iOS contact:', error);
      
      // Handle specific API errors
      if (error.response) {
        const status = error.response.status;
        
        if (status === 401 || status === 403) {
          throw new Error('Authentication failed with iOS Contacts API. Check your API key.');
        } else if (status === 404) {
          // If contact not found, consider it already removed
          logger.warn(`Contact ${contactId} not found in iOS, considering it already removed`);
          return {
            success: true,
            message: 'Contact already removed or not found'
          };
        }
      }
      
      throw new Error(`iOS Contacts API error: ${error.message}`);
    }
  }

  /**
   * Format contact data according to iOS Contacts API requirements
   * @param {Object} contactData - The raw contact data
   * @returns {Object} The formatted contact data
   */
  formatContactData(contactData) {
    // Create a properly formatted contact object for iOS Contacts API
    return {
      firstName: contactData.name.split(' ')[0] || '',
      lastName: contactData.name.split(' ').slice(1).join(' ') || '',
      company: 'NxtAcre Farm Management',
      phoneNumbers: [
        {
          label: 'main',
          number: contactData.phone || ''
        }
      ],
      emailAddresses: [
        {
          label: 'work',
          email: contactData.email || ''
        }
      ],
      postalAddresses: [
        {
          label: 'work',
          street: contactData.address || '',
          city: contactData.city || '',
          state: contactData.state || '',
          postalCode: contactData.zipCode || '',
          country: contactData.country || 'USA'
        }
      ],
      // Add custom properties to identify this contact as managed by our system
      customProperties: {
        source: 'nxtacre-farm-management',
        customerId: contactData.customerId || '',
        farmId: contactData.farmId || ''
      }
    };
  }
}

export default new IOSPhoneBookAPI();