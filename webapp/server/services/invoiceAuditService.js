import InvoiceAuditLog from '../models/InvoiceAuditLog.js';
import { getClientIp } from '../utils/requestUtils.js';

/**
 * Service for logging invoice-related actions for audit purposes
 */
class InvoiceAuditService {
  /**
   * Log an invoice action
   * @param {Object} params - Audit log parameters
   * @param {string} params.invoiceId - Invoice ID
   * @param {string} params.userId - User ID (null for system actions)
   * @param {string} params.farmId - Farm ID
   * @param {string} params.action - Action performed
   * @param {string} params.description - Human-readable description
   * @param {Object} params.changes - Before/after values for updates
   * @param {Object} params.metadata - Additional metadata
   * @param {Object} params.req - Express request object
   * @param {string} params.result - Result of the action (success, failure, denied)
   * @param {string} params.errorMessage - Error message if action failed
   * @returns {Promise<Object>} Created audit log entry
   */
  static async logAction({
    invoiceId,
    userId = null,
    farmId,
    action,
    description = null,
    changes = null,
    metadata = null,
    req = null,
    result = 'success',
    errorMessage = null
  }) {
    try {
      const auditData = {
        invoice_id: invoiceId,
        user_id: userId,
        farm_id: farmId,
        action,
        description,
        changes,
        metadata,
        result,
        error_message: errorMessage
      };

      // Extract request information if available
      if (req) {
        auditData.ip_address = getClientIp(req);
        auditData.user_agent = req.get('User-Agent');
        auditData.session_id = req.sessionID || req.user?.sessionId;
      }

      const auditLog = await InvoiceAuditLog.create(auditData);
      return auditLog;
    } catch (error) {
      console.error('Error creating invoice audit log:', error);
      // Don't throw error to avoid breaking the main operation
      return null;
    }
  }

  /**
   * Log invoice creation
   */
  static async logCreation(invoiceId, userId, farmId, req, metadata = {}) {
    return this.logAction({
      invoiceId,
      userId,
      farmId,
      action: 'created',
      description: 'Invoice created',
      metadata: { ...metadata, invoice_id: invoiceId },
      req
    });
  }

  /**
   * Log invoice view
   */
  static async logView(invoiceId, userId, farmId, req, metadata = {}) {
    return this.logAction({
      invoiceId,
      userId,
      farmId,
      action: 'viewed',
      description: 'Invoice viewed',
      metadata: { ...metadata, view_timestamp: new Date() },
      req
    });
  }

  /**
   * Log invoice update
   */
  static async logUpdate(invoiceId, userId, farmId, req, changes = {}, metadata = {}) {
    return this.logAction({
      invoiceId,
      userId,
      farmId,
      action: 'updated',
      description: 'Invoice updated',
      changes,
      metadata,
      req
    });
  }

  /**
   * Log invoice deletion
   */
  static async logDeletion(invoiceId, userId, farmId, req, metadata = {}) {
    return this.logAction({
      invoiceId,
      userId,
      farmId,
      action: 'deleted',
      description: 'Invoice deleted',
      metadata,
      req
    });
  }

  /**
   * Log invoice sending
   */
  static async logSending(invoiceId, userId, farmId, req, metadata = {}) {
    return this.logAction({
      invoiceId,
      userId,
      farmId,
      action: 'sent',
      description: 'Invoice sent to recipient',
      metadata: { ...metadata, sent_timestamp: new Date() },
      req
    });
  }

  /**
   * Log invoice payment
   */
  static async logPayment(invoiceId, userId, farmId, req, metadata = {}) {
    return this.logAction({
      invoiceId,
      userId,
      farmId,
      action: 'paid',
      description: 'Invoice payment received',
      metadata: { ...metadata, payment_timestamp: new Date() },
      req
    });
  }

  /**
   * Log invoice cancellation
   */
  static async logCancellation(invoiceId, userId, farmId, req, metadata = {}) {
    return this.logAction({
      invoiceId,
      userId,
      farmId,
      action: 'cancelled',
      description: 'Invoice cancelled',
      metadata,
      req
    });
  }

  /**
   * Log invoice dispute
   */
  static async logDispute(invoiceId, userId, farmId, req, metadata = {}) {
    return this.logAction({
      invoiceId,
      userId,
      farmId,
      action: 'disputed',
      description: 'Invoice disputed',
      metadata: { ...metadata, dispute_timestamp: new Date() },
      req
    });
  }

  /**
   * Log document upload
   */
  static async logDocumentUpload(invoiceId, userId, farmId, req, metadata = {}) {
    return this.logAction({
      invoiceId,
      userId,
      farmId,
      action: 'document_uploaded',
      description: 'Document uploaded to invoice',
      metadata,
      req
    });
  }

  /**
   * Log document deletion
   */
  static async logDocumentDeletion(invoiceId, userId, farmId, req, metadata = {}) {
    return this.logAction({
      invoiceId,
      userId,
      farmId,
      action: 'document_deleted',
      description: 'Document deleted from invoice',
      metadata,
      req
    });
  }

  /**
   * Log reminder sending
   */
  static async logReminderSent(invoiceId, userId, farmId, req, metadata = {}) {
    return this.logAction({
      invoiceId,
      userId,
      farmId,
      action: 'reminder_sent',
      description: 'Invoice reminder sent',
      metadata: { ...metadata, reminder_timestamp: new Date() },
      req
    });
  }

  /**
   * Log status change
   */
  static async logStatusChange(invoiceId, userId, farmId, req, changes = {}, metadata = {}) {
    return this.logAction({
      invoiceId,
      userId,
      farmId,
      action: 'status_changed',
      description: `Invoice status changed from ${changes.from} to ${changes.to}`,
      changes,
      metadata,
      req
    });
  }

  /**
   * Log access denied
   */
  static async logAccessDenied(invoiceId, userId, farmId, req, metadata = {}) {
    return this.logAction({
      invoiceId,
      userId,
      farmId,
      action: 'access_denied',
      description: 'Access denied to invoice',
      metadata: { ...metadata, attempted_action: metadata.action },
      req,
      result: 'denied'
    });
  }

  /**
   * Log permission check
   */
  static async logPermissionCheck(invoiceId, userId, farmId, req, metadata = {}) {
    return this.logAction({
      invoiceId,
      userId,
      farmId,
      action: 'permission_checked',
      description: 'Permission check performed',
      metadata: { ...metadata, check_timestamp: new Date() },
      req
    });
  }

  /**
   * Get audit logs for an invoice
   * @param {string} invoiceId - Invoice ID
   * @param {Object} options - Query options
   * @returns {Promise<Array>} Audit log entries
   */
  static async getAuditLogs(invoiceId, options = {}) {
    const {
      limit = 50,
      offset = 0,
      action = null,
      userId = null,
      farmId = null,
      result = null,
      startDate = null,
      endDate = null
    } = options;

    const where = { invoice_id: invoiceId };

    if (action) where.action = action;
    if (userId) where.user_id = userId;
    if (farmId) where.farm_id = farmId;
    if (result) where.result = result;

    if (startDate || endDate) {
      where.created_at = {};
      if (startDate) where.created_at[Op.gte] = startDate;
      if (endDate) where.created_at[Op.lte] = endDate;
    }

    return await InvoiceAuditLog.findAll({
      where,
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'first_name', 'last_name', 'email']
        },
        {
          model: Farm,
          as: 'farm',
          attributes: ['id', 'name']
        }
      ],
      order: [['created_at', 'DESC']],
      limit,
      offset
    });
  }
}

export default InvoiceAuditService;
