-- Update foreign key constraint on dashboard_layouts table to include CASCADE option
-- Generated on 2025-05-01
-- Set the search path to the appropriate schema
SET search_path TO site;

-- Get the schema from environment variable or use 'site' as default
DO $$
DECLARE
    schema_name TEXT;
    constraint_name TEXT;
BEGIN
    -- Try to get schema from DB_SCHEMA environment variable, fall back to current_schema() if not available
    BEGIN
        -- Check if DB_SCHEMA is set as a PostgreSQL variable with proper namespace
        SELECT current_setting('app.db_schema') INTO schema_name;
        EXCEPTION WHEN OTHERS THEN
            -- If not set, use current schema or 'site' as default
            SELECT current_schema() INTO schema_name;
    END;

    -- Get the name of the constraint
    SELECT information_schema.table_constraints.constraint_name INTO constraint_name
    FROM information_schema.table_constraints
    WHERE table_schema = schema_name
    AND table_name = 'dashboard_layouts'
    AND constraint_type = 'FOREIGN KEY'
    AND information_schema.table_constraints.constraint_name LIKE '%user_id%';

    -- If constraint exists, drop it and recreate with CASCADE option
    IF constraint_name IS NOT NULL THEN
        EXECUTE format('
            ALTER TABLE %I.dashboard_layouts 
            DROP CONSTRAINT %I;
        ', schema_name, constraint_name);

        EXECUTE format('
            ALTER TABLE %I.dashboard_layouts 
            ADD CONSTRAINT dashboard_layouts_user_id_fkey 
            FOREIGN KEY (user_id) 
            REFERENCES %I.users(id) 
            ON DELETE CASCADE 
            ON UPDATE CASCADE;
        ', schema_name, schema_name);

        RAISE NOTICE 'Successfully updated foreign key constraint on dashboard_layouts table in schema %', schema_name;
    ELSE
        RAISE NOTICE 'Foreign key constraint not found on dashboard_layouts table in schema %', schema_name;

        -- Try to add the constraint anyway
        BEGIN
            EXECUTE format('
                ALTER TABLE %I.dashboard_layouts 
                ADD CONSTRAINT dashboard_layouts_user_id_fkey 
                FOREIGN KEY (user_id) 
                REFERENCES %I.users(id) 
                ON DELETE CASCADE 
                ON UPDATE CASCADE;
            ', schema_name, schema_name);

            RAISE NOTICE 'Successfully added foreign key constraint on dashboard_layouts table in schema %', schema_name;
        EXCEPTION WHEN OTHERS THEN
            RAISE NOTICE 'Could not add foreign key constraint: %', SQLERRM;
        END;
    END IF;
END $$;
