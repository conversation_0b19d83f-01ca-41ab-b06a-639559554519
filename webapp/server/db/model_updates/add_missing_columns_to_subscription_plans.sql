-- Add missing columns to subscription_plans table
-- Generated on 2025-04-30T19:58:20.353Z
-- Set the search path to the appropriate schema
SET search_path TO site;

-- Get the schema from environment variable or use 'site' as default
DO $$
DECLARE
    schema_name TEXT;
BEGIN
    -- Get the current schema or use 'site' as default
    BEGIN
        -- Check if DB_SCHEMA is set as a PostgreSQL variable with proper namespace
        SELECT current_setting('app.db_schema') INTO schema_name;
        EXCEPTION WHEN OTHERS THEN
            -- If not set, use current schema or 'site' as default
            SELECT current_schema() INTO schema_name;
    END;

    -- Add is_trial column if it doesn't exist
    EXECUTE format('
        ALTER TABLE %I.subscription_plans 
        ADD COLUMN IF NOT EXISTS is_trial BOOLEAN DEFAULT false;
    ', schema_name);
    -- Add comment to the column
    EXECUTE format('
        COMMENT ON COLUMN %I.subscription_plans.is_trial IS ''Whether this plan is a trial subscription plan'';
    ', schema_name);

    -- Add is_default column if it doesn't exist
    EXECUTE format('
        ALTER TABLE %I.subscription_plans 
        ADD COLUMN IF NOT EXISTS is_default BOOLEAN DEFAULT false;
    ', schema_name);
    -- Add comment to the column
    EXECUTE format('
        COMMENT ON COLUMN %I.subscription_plans.is_default IS ''Whether this plan is the default trial subscription plan'';
    ', schema_name);

    RAISE NOTICE 'Successfully added missing columns to subscription_plans table in schema %', schema_name;
END $$;
