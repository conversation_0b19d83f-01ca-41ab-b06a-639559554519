-- Migration: Add financial_connection_id to farms table
-- Depends on:

SET search_path TO site;

-- Add financial_connection_id column to farms table
ALTER TABLE farms
ADD COLUMN IF NOT EXISTS financial_connection_id UUID NULL,
ADD CONSTRAINT fk_farms_financial_connection_id FOREIGN KEY (financial_connection_id) REFERENCES stripe_financial_connections(id) ON DELETE SET NULL;

-- Add index for financial_connection_id
CREATE INDEX IF NOT EXISTS idx_farms_financial_connection_id ON farms(financial_connection_id);

-- Add comment to the column
COMMENT ON COLUMN farms.financial_connection_id IS 'Foreign key to stripe_financial_connections table';