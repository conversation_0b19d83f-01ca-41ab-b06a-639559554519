-- Migration: Add tax system tables for farm tax management
-- Depends on: add_document_signing_tables.sql

-- Set the search path to the appropriate schema
SET search_path TO site;

-- Get the schema name from environment variable or use 'site' as default
DO $$
DECLARE
    schema_name TEXT;
BEGIN
    -- Get the schema name from environment variable
    schema_name := current_setting('app.db_schema', true);

    -- If the environment variable is not set, use 'site' as default
    IF schema_name IS NULL THEN
        schema_name := 'site';
    END IF;

    -- Create tax_categories table if it doesn't exist
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.tables
        WHERE table_schema = schema_name
        AND table_name = 'tax_categories'
    ) THEN
        EXECUTE format('
            CREATE TABLE %I.tax_categories (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                farm_id UUID NOT NULL REFERENCES %I.farms(id),
                name VARCHAR(255) NOT NULL,
                description TEXT,
                deductible BOOLEAN NOT NULL DEFAULT FALSE,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
            )
        ', schema_name, schema_name);

        EXECUTE format('COMMENT ON TABLE %I.tax_categories IS ''Categories for tax-related items''', schema_name);
    END IF;

    -- Create tax_deductions table if it doesn't exist
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.tables
        WHERE table_schema = schema_name
        AND table_name = 'tax_deductions'
    ) THEN
        EXECUTE format('
            CREATE TABLE %I.tax_deductions (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                farm_id UUID NOT NULL REFERENCES %I.farms(id),
                name VARCHAR(255) NOT NULL,
                description TEXT,
                amount DECIMAL(10, 2) NOT NULL,
                category VARCHAR(255) NOT NULL,
                tax_year INTEGER NOT NULL,
                status VARCHAR(20) NOT NULL DEFAULT ''pending'',
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                CONSTRAINT tax_deductions_status_check CHECK (status IN (''pending'', ''approved'', ''rejected''))
            )
        ', schema_name, schema_name);

        EXECUTE format('COMMENT ON TABLE %I.tax_deductions IS ''Tax deductions for farms''', schema_name);
    END IF;

    -- Create tax_documents table if it doesn't exist
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.tables
        WHERE table_schema = schema_name
        AND table_name = 'tax_documents'
    ) THEN
        EXECUTE format('
            CREATE TABLE %I.tax_documents (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                farm_id UUID NOT NULL REFERENCES %I.farms(id),
                title VARCHAR(255) NOT NULL,
                description TEXT,
                document_type VARCHAR(50) NOT NULL,
                tax_year INTEGER NOT NULL,
                file_path VARCHAR(255),
                file_name VARCHAR(255),
                file_size INTEGER,
                file_type VARCHAR(100),
                status VARCHAR(20) NOT NULL DEFAULT ''draft'',
                submitted_date TIMESTAMP WITH TIME ZONE,
                created_by UUID NOT NULL REFERENCES %I.users(id),
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                CONSTRAINT tax_documents_document_type_check CHECK (document_type IN (''w2'', ''1099'', ''schedule_f'', ''depreciation'', ''expense'', ''income'', ''other'')),
                CONSTRAINT tax_documents_status_check CHECK (status IN (''draft'', ''pending'', ''submitted'', ''approved'', ''rejected''))
            )
        ', schema_name, schema_name, schema_name);

        EXECUTE format('COMMENT ON TABLE %I.tax_documents IS ''Tax-related documents for farms''', schema_name);
    END IF;

    -- Create employee_tax_info table if it doesn't exist
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.tables
        WHERE table_schema = schema_name
        AND table_name = 'employee_tax_info'
    ) THEN
        EXECUTE format('
            CREATE TABLE %I.employee_tax_info (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                employee_id UUID NOT NULL REFERENCES %I.employees(id),
                farm_id UUID NOT NULL REFERENCES %I.farms(id),
                tax_year INTEGER NOT NULL,
                ssn VARCHAR(255),
                filing_status VARCHAR(50),
                withholding_allowances INTEGER DEFAULT 0,
                additional_withholding DECIMAL(10, 2) DEFAULT 0.00,
                is_exempt BOOLEAN NOT NULL DEFAULT FALSE,
                w2_generated BOOLEAN NOT NULL DEFAULT FALSE,
                w2_document_id UUID REFERENCES %I.tax_documents(id),
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                CONSTRAINT employee_tax_info_filing_status_check CHECK (filing_status IN (''single'', ''married_joint'', ''married_separate'', ''head_of_household'', ''qualifying_widow''))
            )
        ', schema_name, schema_name, schema_name, schema_name);

        EXECUTE format('COMMENT ON TABLE %I.employee_tax_info IS ''Tax information for employees''', schema_name);
    END IF;

    -- Create contractor_tax_info table if it doesn't exist
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.tables
        WHERE table_schema = schema_name
        AND table_name = 'contractor_tax_info'
    ) THEN
        EXECUTE format('
            CREATE TABLE %I.contractor_tax_info (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                farm_id UUID NOT NULL REFERENCES %I.farms(id),
                contractor_name VARCHAR(255) NOT NULL,
                business_name VARCHAR(255),
                tax_id VARCHAR(255),
                address VARCHAR(255),
                city VARCHAR(100),
                state VARCHAR(50),
                zip_code VARCHAR(20),
                tax_year INTEGER NOT NULL,
                total_payments DECIMAL(10, 2) NOT NULL DEFAULT 0.00,
                form_1099_generated BOOLEAN NOT NULL DEFAULT FALSE,
                form_1099_document_id UUID REFERENCES %I.tax_documents(id),
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
            )
        ', schema_name, schema_name, schema_name);

        EXECUTE format('COMMENT ON TABLE %I.contractor_tax_info IS ''Tax information for contractors''', schema_name);
    END IF;

    -- Create tax_payments table if it doesn't exist
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.tables
        WHERE table_schema = schema_name
        AND table_name = 'tax_payments'
    ) THEN
        EXECUTE format('
            CREATE TABLE %I.tax_payments (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                farm_id UUID NOT NULL REFERENCES %I.farms(id),
                payment_date TIMESTAMP WITH TIME ZONE NOT NULL,
                amount DECIMAL(10, 2) NOT NULL,
                payment_method VARCHAR(50) NOT NULL,
                payment_reference VARCHAR(255),
                tax_year INTEGER NOT NULL,
                tax_period VARCHAR(20) NOT NULL,
                tax_type VARCHAR(50) NOT NULL,
                description TEXT,
                receipt_document_id UUID REFERENCES %I.tax_documents(id),
                created_by UUID NOT NULL REFERENCES %I.users(id),
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                CONSTRAINT tax_payments_payment_method_check CHECK (payment_method IN (''check'', ''electronic'', ''credit_card'', ''other'')),
                CONSTRAINT tax_payments_tax_period_check CHECK (tax_period IN (''q1'', ''q2'', ''q3'', ''q4'', ''annual'')),
                CONSTRAINT tax_payments_tax_type_check CHECK (tax_type IN (''income'', ''property'', ''payroll'', ''sales'', ''other''))
            )
        ', schema_name, schema_name, schema_name, schema_name);

        EXECUTE format('COMMENT ON TABLE %I.tax_payments IS ''Tax payments made by farms''', schema_name);
    END IF;

    -- Create tax_filings table if it doesn't exist
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.tables
        WHERE table_schema = schema_name
        AND table_name = 'tax_filings'
    ) THEN
        EXECUTE format('
            CREATE TABLE %I.tax_filings (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                farm_id UUID NOT NULL REFERENCES %I.farms(id),
                tax_year INTEGER NOT NULL,
                filing_type VARCHAR(50) NOT NULL,
                form_number VARCHAR(100),
                due_date TIMESTAMP WITH TIME ZONE NOT NULL,
                filing_date TIMESTAMP WITH TIME ZONE,
                extension_filed BOOLEAN NOT NULL DEFAULT FALSE,
                extension_date TIMESTAMP WITH TIME ZONE,
                status VARCHAR(50) NOT NULL DEFAULT ''not_started'',
                filing_method VARCHAR(50),
                confirmation_number VARCHAR(255),
                total_tax DECIMAL(10, 2),
                total_paid DECIMAL(10, 2),
                balance_due DECIMAL(10, 2),
                refund_amount DECIMAL(10, 2),
                notes TEXT,
                document_id UUID REFERENCES %I.tax_documents(id),
                created_by UUID NOT NULL REFERENCES %I.users(id),
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                CONSTRAINT tax_filings_filing_type_check CHECK (filing_type IN (''federal_income'', ''state_income'', ''property'', ''payroll'', ''sales'', ''other'')),
                CONSTRAINT tax_filings_status_check CHECK (status IN (''not_started'', ''in_progress'', ''ready_to_file'', ''filed'', ''accepted'', ''rejected'', ''amended'')),
                CONSTRAINT tax_filings_filing_method_check CHECK (filing_method IN (''electronic'', ''paper'', ''tax_professional''))
            )
        ', schema_name, schema_name, schema_name, schema_name);

        EXECUTE format('COMMENT ON TABLE %I.tax_filings IS ''Tax filings for farms''', schema_name);
    END IF;

    -- Create indexes for better performance
    EXECUTE format('CREATE INDEX IF NOT EXISTS idx_tax_categories_farm_id ON %I.tax_categories(farm_id)', schema_name);
    EXECUTE format('CREATE INDEX IF NOT EXISTS idx_tax_deductions_farm_id ON %I.tax_deductions(farm_id)', schema_name);
    EXECUTE format('CREATE INDEX IF NOT EXISTS idx_tax_deductions_tax_year ON %I.tax_deductions(tax_year)', schema_name);
    EXECUTE format('CREATE INDEX IF NOT EXISTS idx_tax_documents_farm_id ON %I.tax_documents(farm_id)', schema_name);
    EXECUTE format('CREATE INDEX IF NOT EXISTS idx_tax_documents_tax_year ON %I.tax_documents(tax_year)', schema_name);
    EXECUTE format('CREATE INDEX IF NOT EXISTS idx_tax_documents_created_by ON %I.tax_documents(created_by)', schema_name);
    EXECUTE format('CREATE INDEX IF NOT EXISTS idx_employee_tax_info_employee_id ON %I.employee_tax_info(employee_id)', schema_name);
    EXECUTE format('CREATE INDEX IF NOT EXISTS idx_employee_tax_info_farm_id ON %I.employee_tax_info(farm_id)', schema_name);
    EXECUTE format('CREATE INDEX IF NOT EXISTS idx_employee_tax_info_tax_year ON %I.employee_tax_info(tax_year)', schema_name);
    EXECUTE format('CREATE INDEX IF NOT EXISTS idx_contractor_tax_info_farm_id ON %I.contractor_tax_info(farm_id)', schema_name);
    EXECUTE format('CREATE INDEX IF NOT EXISTS idx_contractor_tax_info_tax_year ON %I.contractor_tax_info(tax_year)', schema_name);
    EXECUTE format('CREATE INDEX IF NOT EXISTS idx_tax_payments_farm_id ON %I.tax_payments(farm_id)', schema_name);
    EXECUTE format('CREATE INDEX IF NOT EXISTS idx_tax_payments_tax_year ON %I.tax_payments(tax_year)', schema_name);
    EXECUTE format('CREATE INDEX IF NOT EXISTS idx_tax_payments_created_by ON %I.tax_payments(created_by)', schema_name);
    EXECUTE format('CREATE INDEX IF NOT EXISTS idx_tax_filings_farm_id ON %I.tax_filings(farm_id)', schema_name);
    EXECUTE format('CREATE INDEX IF NOT EXISTS idx_tax_filings_tax_year ON %I.tax_filings(tax_year)', schema_name);
    EXECUTE format('CREATE INDEX IF NOT EXISTS idx_tax_filings_created_by ON %I.tax_filings(created_by)', schema_name);

END $$;
