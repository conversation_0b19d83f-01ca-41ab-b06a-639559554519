# Schema Migration Dependency System

## Overview

The NxtAcre Farm Management Platform now includes a robust schema migration system that allows migration files to specify dependencies on other schema files. This ensures that migrations are run in the correct order based on dependencies, rather than just alphabetical or creation date order.

## How It Works

The system uses a simple comment-based approach to specify dependencies in SQL migration files. When migrations are run, the system:

1. Parses all migration files to extract dependency information
2. Builds a dependency graph
3. Uses topological sorting to determine the correct execution order
4. Executes migrations in the determined order
5. Tracks which migrations have been applied in the `schema_migrations` table
6. Records dependencies between migrations in the `schema_migration_dependencies` table

## Specifying Dependencies

To specify that a migration depends on other migrations, add a comment at the top of your SQL file using the following format:

```sql
-- Migration: Your migration description
-- Depends on: file1.sql, file2.sql, file3.sql
```

For example:

```sql
-- Migration: Add user preferences table
-- Depends on: add_users_table.sql, add_user_settings_table.sql

SET search_path TO site;

CREATE TABLE IF NOT EXISTS user_preferences (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  preference_key VARCHAR(255) NOT NULL,
  preference_value TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

This migration will only be executed after both `add_users_table.sql` and `add_user_settings_table.sql` have been applied.

## Running Migrations

To run migrations with dependency resolution, use the new `run-schema-migration.js` script:

```bash
node server/scripts/run-schema-migration.js
```

This script will:

1. Ensure the `schema_migrations` table exists (creating it if necessary)
2. Parse all migration files in the `server/db/migrations` directory
3. Build a dependency graph and determine the correct execution order
4. Execute migrations that haven't been applied yet, in the correct order
5. Record successful migrations in the `schema_migrations` table
6. Record dependencies in the `schema_migration_dependencies` table

## Migration Tables

The system uses two tables to track migrations:

### schema_migrations

Tracks which migrations have been applied:

- `id`: UUID primary key
- `filename`: Name of the migration file
- `description`: Description of the migration
- `applied_at`: When the migration was applied
- `applied_by`: Who applied the migration
- `status`: Status of the migration (success, failed)
- `error_message`: Error message if the migration failed
- `created_at`: When the record was created
- `updated_at`: When the record was last updated

### schema_migration_dependencies

Tracks dependencies between migrations:

- `id`: UUID primary key
- `migration_filename`: Name of the migration file
- `depends_on_filename`: Name of the dependency file
- `created_at`: When the record was created
- `updated_at`: When the record was last updated

## Best Practices

1. **Always specify dependencies explicitly**: Don't rely on alphabetical ordering or implicit dependencies.
2. **Keep dependencies minimal**: Only specify direct dependencies, not transitive ones.
3. **Avoid circular dependencies**: The system will detect and report circular dependencies.
4. **Use descriptive migration names**: This makes it easier to understand dependencies.
5. **Include a description comment**: The first comment line should describe what the migration does.

## Example

Here's a complete example of a migration file with dependencies:

```sql
-- Migration: Add document categories table
-- Depends on: add_document_folders_table.sql, add_document_tags_table.sql

SET search_path TO site;

CREATE TABLE IF NOT EXISTS document_categories (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name VARCHAR(255) NOT NULL,
  description TEXT,
  folder_id UUID REFERENCES document_folders(id) ON DELETE SET NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX IF NOT EXISTS idx_document_categories_folder_id ON document_categories(folder_id);

COMMENT ON TABLE document_categories IS 'Categories for organizing documents';
```

## Troubleshooting

If you encounter issues with the migration system:

1. **Check for circular dependencies**: The system will report if it detects circular dependencies.
2. **Verify file names**: Make sure the dependency file names exactly match the actual file names.
3. **Check migration status**: Query the `schema_migrations` table to see which migrations have been applied.
4. **Check dependencies**: Query the `schema_migration_dependencies` table to see recorded dependencies.

## Conclusion

The dependency-aware migration system ensures that schema migrations are applied in the correct order, regardless of file naming or creation date. This makes the schema migration process more robust and easier to manage, especially as the number of migrations grows.