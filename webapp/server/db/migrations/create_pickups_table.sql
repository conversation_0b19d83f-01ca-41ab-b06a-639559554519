-- Create pickups table for transport management

-- Set the search path to the site schema
SET search_path TO site;

-- Create pickups table if it doesn't exist
CREATE TABLE IF NOT EXISTS site.pickups (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  farm_id UUID NOT NULL REFERENCES site.farms(id),
  driver_id UUID REFERENCES site.drivers(id),
  supplier_id UUID REFERENCES site.suppliers(id),
  customer_id UUID REFERENCES site.customers(id),
  order_id UUID REFERENCES site.orders(id),
  pickup_type VARCHAR(20) NOT NULL DEFAULT 'product',
  status VARCHAR(20) NOT NULL DEFAULT 'scheduled',
  scheduled_date TIMESTAMP WITH TIME ZONE NOT NULL,
  estimated_completion TIMESTAMP WITH TIME ZONE,
  actual_pickup_date TIMESTAMP WITH TIME ZONE,
  pickup_address VARCHAR(255) NOT NULL,
  pickup_city VARCHAR(100) NOT NULL,
  pickup_state VARCHAR(50) NOT NULL,
  pickup_zip VARCHAR(20) NOT NULL,
  pickup_country VARCHAR(100) NOT NULL DEFAULT 'USA',
  pickup_instructions TEXT,
  confirmation_required BOOLEAN DEFAULT FALSE,
  confirmation_signature VARCHAR(255),
  proof_of_pickup_image VARCHAR(255),
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Add enum constraint for pickup_type
ALTER TABLE site.pickups DROP CONSTRAINT IF EXISTS pickups_pickup_type_check;
ALTER TABLE site.pickups ADD CONSTRAINT pickups_pickup_type_check 
  CHECK (pickup_type IN ('product', 'equipment', 'return', 'other'));

-- Add enum constraint for status
ALTER TABLE site.pickups DROP CONSTRAINT IF EXISTS pickups_status_check;
ALTER TABLE site.pickups ADD CONSTRAINT pickups_status_check 
  CHECK (status IN ('scheduled', 'in_progress', 'completed', 'failed', 'cancelled'));

-- Add comments to columns
COMMENT ON COLUMN site.pickups.id IS 'Unique identifier for the pickup';
COMMENT ON COLUMN site.pickups.farm_id IS 'ID of the farm this pickup belongs to';
COMMENT ON COLUMN site.pickups.driver_id IS 'ID of the driver assigned to this pickup';
COMMENT ON COLUMN site.pickups.supplier_id IS 'ID of the supplier for this pickup';
COMMENT ON COLUMN site.pickups.customer_id IS 'ID of the customer for this pickup (if applicable)';
COMMENT ON COLUMN site.pickups.order_id IS 'ID of the order associated with this pickup';
COMMENT ON COLUMN site.pickups.pickup_type IS 'Type of pickup: product, equipment, return, or other';
COMMENT ON COLUMN site.pickups.status IS 'Status of the pickup: scheduled, in_progress, completed, failed, or cancelled';
COMMENT ON COLUMN site.pickups.scheduled_date IS 'Scheduled date and time for the pickup';
COMMENT ON COLUMN site.pickups.estimated_completion IS 'Estimated completion time';
COMMENT ON COLUMN site.pickups.actual_pickup_date IS 'Actual date and time when pickup was completed';
COMMENT ON COLUMN site.pickups.pickup_address IS 'Street address for pickup';
COMMENT ON COLUMN site.pickups.pickup_city IS 'City for pickup';
COMMENT ON COLUMN site.pickups.pickup_state IS 'State/province for pickup';
COMMENT ON COLUMN site.pickups.pickup_zip IS 'ZIP/postal code for pickup';
COMMENT ON COLUMN site.pickups.pickup_country IS 'Country for pickup';
COMMENT ON COLUMN site.pickups.pickup_instructions IS 'Special instructions for the pickup';
COMMENT ON COLUMN site.pickups.confirmation_required IS 'Whether confirmation is required upon pickup';
COMMENT ON COLUMN site.pickups.confirmation_signature IS 'Path to the confirmation signature image file';
COMMENT ON COLUMN site.pickups.proof_of_pickup_image IS 'Path to the proof of pickup image file';
COMMENT ON COLUMN site.pickups.notes IS 'Additional notes about the pickup';
COMMENT ON COLUMN site.pickups.created_at IS 'Timestamp when the pickup record was created';
COMMENT ON COLUMN site.pickups.updated_at IS 'Timestamp when the pickup record was last updated';

-- Create indexes
CREATE INDEX IF NOT EXISTS pickups_farm_id_idx ON site.pickups(farm_id);
CREATE INDEX IF NOT EXISTS pickups_driver_id_idx ON site.pickups(driver_id);
CREATE INDEX IF NOT EXISTS pickups_supplier_id_idx ON site.pickups(supplier_id);
CREATE INDEX IF NOT EXISTS pickups_customer_id_idx ON site.pickups(customer_id);
CREATE INDEX IF NOT EXISTS pickups_order_id_idx ON site.pickups(order_id);
CREATE INDEX IF NOT EXISTS pickups_status_idx ON site.pickups(status);
CREATE INDEX IF NOT EXISTS pickups_scheduled_date_idx ON site.pickups(scheduled_date);
CREATE INDEX IF NOT EXISTS pickups_pickup_type_idx ON site.pickups(pickup_type);

-- Verify the table was created
DO $$
DECLARE
    table_exists BOOLEAN;
BEGIN
    SELECT EXISTS (
        SELECT 1 
        FROM information_schema.tables 
        WHERE table_schema = 'site' 
        AND table_name = 'pickups'
    ) INTO table_exists;

    IF table_exists THEN
        RAISE NOTICE 'The pickups table was successfully created.';
    ELSE
        RAISE EXCEPTION 'Failed to create the pickups table.';
    END IF;
END $$;

-- Create trigger for updated_at
CREATE OR REPLACE FUNCTION update_pickups_updated_at()
RETURNS TRIGGER AS $$
BEGIN
   NEW.updated_at = CURRENT_TIMESTAMP;
   RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_pickups_timestamp
BEFORE UPDATE ON site.pickups
FOR EACH ROW EXECUTE FUNCTION update_pickups_updated_at();