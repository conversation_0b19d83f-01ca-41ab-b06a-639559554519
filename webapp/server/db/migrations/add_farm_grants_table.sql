-- Set the search path to the appropriate schema
SET search_path TO site;

-- Create farm-specific grants table
CREATE TABLE IF NOT EXISTS site.farm_grants (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    farm_id UUID NOT NULL,
    name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    provider VA<PERSON>HAR(255) NOT NULL,
    amount DECIMAL(10, 2) NOT NULL,
    status VARCHAR(50) DEFAULT 'applied' NOT NULL,
    deadline TIMESTAMP NOT NULL,
    description TEXT,
    requirements JSONB DEFAULT '[]',
    application_date TIMESTAMP,
    approval_date TIMESTAMP,
    disbursement_date TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (farm_id) REFERENCES site.farms(id) ON DELETE CASCADE
);

-- Add indexes for faster lookups
CREATE INDEX IF NOT EXISTS idx_farm_grants_farm_id ON site.farm_grants(farm_id);
CREATE INDEX IF NOT EXISTS idx_farm_grants_status ON site.farm_grants(status);
CREATE INDEX IF NOT EXISTS idx_farm_grants_deadline ON site.farm_grants(deadline);

-- Add comment to the table
COMMENT ON TABLE site.farm_grants IS 'Stores farm-specific grant data';