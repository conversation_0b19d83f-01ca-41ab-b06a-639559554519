-- Migration: Add missing customer_pays_stripe_fees column to farms table
-- Depends on:

SET search_path TO site;

-- Check if the column already exists before adding it
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'farms'
        AND column_name = 'customer_pays_stripe_fees'
    ) THEN
        -- Add customer_pays_stripe_fees to farms table
        ALTER TABLE farms
        ADD COLUMN customer_pays_stripe_fees BOOLEAN NOT NULL DEFAULT FALSE;
    END IF;
END $$;

-- Check if the column already exists before adding it
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'farms'
        AND column_name = 'customer_portal_stripe_fee_message'
    ) THEN
        -- Add customer_portal_stripe_fee_message to farms table
        ALTER TABLE farms
        ADD COLUMN customer_portal_stripe_fee_message TEXT NULL;
    END IF;
END $$;