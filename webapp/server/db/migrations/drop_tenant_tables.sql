-- Migration: Drop tenant tables and columns after migration to farms
-- Depends on:

-- Set search path to site schema
SET search_path TO site;

-- Check if tenants table exists and drop it if it does
DO $$
BEGIN
  IF EXISTS (
    SELECT FROM information_schema.tables 
    WHERE table_schema = 'site' 
    AND table_name = 'tenants'
  ) THEN
    -- Drop the tenants table
    DROP TABLE site.tenants CASCADE;
    RAISE NOTICE 'Dropped tenants table';
  ELSE
    RAISE NOTICE 'tenants table does not exist, skipping drop';
  END IF;
END $$;

-- Check if tenant_admins table exists and drop it if it does
DO $$
BEGIN
  IF EXISTS (
    SELECT FROM information_schema.tables 
    WHERE table_schema = 'site' 
    AND table_name = 'tenant_admins'
  ) THEN
    -- Drop the tenant_admins table
    DROP TABLE site.tenant_admins CASCADE;
    RAISE NOTICE 'Dropped tenant_admins table';
  ELSE
    RAISE NOTICE 'tenant_admins table does not exist, skipping drop';
  END IF;
END $$;

-- Check if tenant_storage_usage table exists and drop it if it does
DO $$
BEGIN
  IF EXISTS (
    SELECT FROM information_schema.tables 
    WHERE table_schema = 'site' 
    AND table_name = 'tenant_storage_usage'
  ) THEN
    -- Drop the tenant_storage_usage table
    DROP TABLE site.tenant_storage_usage CASCADE;
    RAISE NOTICE 'Dropped tenant_storage_usage table';
  ELSE
    RAISE NOTICE 'tenant_storage_usage table does not exist, skipping drop';
  END IF;
END $$;

-- Check if users table has tenant_id column and drop it if it does
DO $$
BEGIN
  IF EXISTS (
    SELECT FROM information_schema.columns 
    WHERE table_schema = 'site' 
    AND table_name = 'users' 
    AND column_name = 'tenant_id'
  ) THEN
    -- Drop the tenant_id column from users table
    ALTER TABLE site.users DROP COLUMN tenant_id;
    RAISE NOTICE 'Dropped tenant_id column from users table';
  ELSE
    RAISE NOTICE 'users table does not have tenant_id column, skipping drop';
  END IF;
END $$;

-- Check if subscription_transactions table has tenant_id column and drop it if it does
DO $$
BEGIN
  IF EXISTS (
    SELECT FROM information_schema.columns 
    WHERE table_schema = 'site' 
    AND table_name = 'subscription_transactions' 
    AND column_name = 'tenant_id'
  ) THEN
    -- Drop the tenant_id column from subscription_transactions table
    ALTER TABLE site.subscription_transactions DROP COLUMN tenant_id;
    RAISE NOTICE 'Dropped tenant_id column from subscription_transactions table';
  ELSE
    RAISE NOTICE 'subscription_transactions table does not have tenant_id column, skipping drop';
  END IF;
END $$;

-- Check if feature_usage table has tenant_id column and drop it if it does
DO $$
BEGIN
  IF EXISTS (
    SELECT FROM information_schema.columns 
    WHERE table_schema = 'site' 
    AND table_name = 'feature_usage' 
    AND column_name = 'tenant_id'
  ) THEN
    -- Drop the tenant_id column from feature_usage table
    ALTER TABLE site.feature_usage DROP COLUMN tenant_id;
    RAISE NOTICE 'Dropped tenant_id column from feature_usage table';
  ELSE
    RAISE NOTICE 'feature_usage table does not have tenant_id column, skipping drop';
  END IF;
END $$;

-- Check if document_folders table has tenant_id column and drop it if it does
DO $$
BEGIN
  IF EXISTS (
    SELECT FROM information_schema.columns 
    WHERE table_schema = 'site' 
    AND table_name = 'document_folders' 
    AND column_name = 'tenant_id'
  ) THEN
    -- Drop the tenant_id column from document_folders table
    ALTER TABLE site.document_folders DROP COLUMN tenant_id;
    RAISE NOTICE 'Dropped tenant_id column from document_folders table';
  ELSE
    RAISE NOTICE 'document_folders table does not have tenant_id column, skipping drop';
  END IF;
END $$;

-- Check if documents table has tenant_id column and drop it if it does
DO $$
BEGIN
  IF EXISTS (
    SELECT FROM information_schema.columns 
    WHERE table_schema = 'site' 
    AND table_name = 'documents' 
    AND column_name = 'tenant_id'
  ) THEN
    -- Drop the tenant_id column from documents table
    ALTER TABLE site.documents DROP COLUMN tenant_id;
    RAISE NOTICE 'Dropped tenant_id column from documents table';
  ELSE
    RAISE NOTICE 'documents table does not have tenant_id column, skipping drop';
  END IF;
END $$;

-- Check if document_permissions table has tenant_id column and drop it if it does
DO $$
BEGIN
  IF EXISTS (
    SELECT FROM information_schema.columns 
    WHERE table_schema = 'site' 
    AND table_name = 'document_permissions' 
    AND column_name = 'tenant_id'
  ) THEN
    -- Drop the tenant_id column from document_permissions table
    ALTER TABLE site.document_permissions DROP COLUMN tenant_id;
    RAISE NOTICE 'Dropped tenant_id column from document_permissions table';
  ELSE
    RAISE NOTICE 'document_permissions table does not have tenant_id column, skipping drop';
  END IF;
END $$;