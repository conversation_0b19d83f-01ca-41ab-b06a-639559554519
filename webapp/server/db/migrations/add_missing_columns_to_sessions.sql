-- Add missing columns to Sessions table

-- Set the search path to the appropriate schema
SET search_path TO site;

-- Add device_name column to the sessions table
ALTER TABLE site.sessions ADD COLUMN IF NOT EXISTS device_name VARCHAR(255);

-- Add is_trusted column to the sessions table
ALTER TABLE site.sessions ADD COLUMN IF NOT EXISTS is_trusted BOOLEAN NOT NULL DEFAULT FALSE;

-- Add comments to the columns
COMMENT ON COLUMN site.sessions.device_name IS 'Stores user-friendly device name for better identification';
COMMENT ON COLUMN site.sessions.is_trusted IS 'Indicates whether this device is trusted by the user for authentication purposes';