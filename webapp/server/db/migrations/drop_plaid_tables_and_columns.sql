-- Migration: Drop unused Plaid tables and columns
-- Depends on:

SET search_path TO site;

-- Drop the trigger for plaid_items table
DROP TRIGGER IF EXISTS update_plaid_items_timestamp ON plaid_items;

-- Drop the plaid_items table
DROP TABLE IF EXISTS plaid_items;

-- Remove Plaid-related columns from financial_accounts table
ALTER TABLE financial_accounts 
  DROP COLUMN IF EXISTS is_plaid_connected,
  DROP COLUMN IF EXISTS plaid_item_id,
  DROP COLUMN IF EXISTS plaid_access_token;

-- Remove plaid_transaction_id column from transactions table
ALTER TABLE transactions
  DROP COLUMN IF EXISTS plaid_transaction_id;