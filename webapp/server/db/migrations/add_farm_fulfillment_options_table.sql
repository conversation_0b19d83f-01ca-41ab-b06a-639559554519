-- Migration: Add farm fulfillment options table
-- Depends on: add_shopping_cart_tables.sql

SET search_path TO site;

CREATE TABLE farm_fulfillment_options (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  farm_id UUID NOT NULL REFERENCES farms(id) ON DELETE CASCADE,
  offers_delivery BOOLEAN NOT NULL DEFAULT true,
  offers_pickup BOOLEAN NOT NULL DEFAULT true,
  delivery_fee DECIMAL(10, 2) DEFAULT 0.00,
  min_order_for_free_delivery DECIMAL(10, 2),
  delivery_radius_miles INTEGER,
  pickup_instructions TEXT,
  delivery_instructions TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX farm_fulfillment_options_farm_id_idx ON farm_fulfillment_options(farm_id);

-- Add fulfillment options to products table
ALTER TABLE products
ADD COLUMN override_farm_fulfillment BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN offers_delivery BOOLEAN,
ADD COLUMN offers_pickup BOOLEAN;

COMMENT ON COLUMN products.override_farm_fulfillment IS 'Whether this product overrides farm-level fulfillment options';
COMMENT ON COLUMN products.offers_delivery IS 'Whether this product offers delivery (if overriding farm settings)';
COMMENT ON COLUMN products.offers_pickup IS 'Whether this product offers pickup (if overriding farm settings)';

-- Add fulfillment method to purchase requests table
ALTER TABLE purchase_requests
ADD COLUMN fulfillment_method VARCHAR(50) NOT NULL DEFAULT 'delivery',
ADD COLUMN pickup_date TIMESTAMP WITH TIME ZONE,
ADD COLUMN delivery_address_id UUID REFERENCES customer_addresses(id);

COMMENT ON COLUMN purchase_requests.fulfillment_method IS 'Method of fulfillment: delivery, pickup';
COMMENT ON COLUMN purchase_requests.pickup_date IS 'Scheduled date for pickup (if applicable)';
COMMENT ON COLUMN purchase_requests.delivery_address_id IS 'Reference to the delivery address (if applicable)';