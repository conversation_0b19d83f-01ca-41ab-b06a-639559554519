-- Migration: Add customer notifications table for invoice question notifications
-- Depends on: add_farm_ownership_constraints.sql

SET search_path TO site;

CREATE TABLE customer_notifications (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  title VARCHAR(255) NOT NULL,
  message TEXT NOT NULL,
  type VA<PERSON><PERSON><PERSON>(10) NOT NULL DEFAULT 'info' CHECK (type IN ('info', 'warning', 'success')),
  read BOOLEAN NOT NULL DEFAULT FALSE,
  farm_id UUID NOT NULL REFERENCES farms(id) ON DELETE CASCADE,
  customer_id UUID NOT NULL REFERENCES customers(id) ON DELETE CASCADE,
  related_entity_type VARCHAR(50),
  related_entity_id UUID,
  action_url VARCHAR(255),
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

CREATE INDEX customer_notifications_customer_id_idx ON customer_notifications(customer_id);
CREATE INDEX customer_notifications_farm_id_idx ON customer_notifications(farm_id);
CREATE INDEX customer_notifications_read_idx ON customer_notifications(read);
CREATE INDEX customer_notifications_created_at_idx ON customer_notifications(created_at);

COMMENT ON TABLE customer_notifications IS 'Notifications for customers in the customer portal';
COMMENT ON COLUMN customer_notifications.title IS 'Notification title';
COMMENT ON COLUMN customer_notifications.message IS 'Notification message content';
COMMENT ON COLUMN customer_notifications.type IS 'Notification type';
COMMENT ON COLUMN customer_notifications.read IS 'Whether the notification has been read';
COMMENT ON COLUMN customer_notifications.farm_id IS 'Farm this notification belongs to';
COMMENT ON COLUMN customer_notifications.customer_id IS 'Customer this notification is for';
COMMENT ON COLUMN customer_notifications.related_entity_type IS 'Type of entity this notification is related to (e.g., invoice_question)';
COMMENT ON COLUMN customer_notifications.related_entity_id IS 'ID of the entity this notification is related to';
COMMENT ON COLUMN customer_notifications.action_url IS 'URL for action to take on this notification';