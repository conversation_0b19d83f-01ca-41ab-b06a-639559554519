-- Add AI configuration tables for global admin page
-- This migration adds tables for AI providers, models, configurations, and instructions

-- Set the search path to the site schema
SET search_path TO site;

-- Create a function to log migration progress
CREATE OR REPLACE FUNCTION log_migration_step(step_name TEXT)
RETURNS VOID AS $$
BEGIN
    RAISE NOTICE 'Migration step: %', step_name;
END;
$$ LANGUAGE plpgsql;

-- Begin transaction
BEGIN;

-- Step 1: Create ai_providers table
SELECT log_migration_step('Creating ai_providers table');

CREATE TABLE IF NOT EXISTS site.ai_providers (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(255) NOT NULL,
  description TEXT,
  api_base_url VARCHAR(255),
  auth_type VARCHAR(50) NOT NULL DEFAULT 'api_key',
  is_enabled BOOLEAN NOT NULL DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Add comments to columns
COMMENT ON COLUMN site.ai_providers.id IS 'Unique identifier for the AI provider';
COMMENT ON COLUMN site.ai_providers.name IS 'Name of the AI provider (e.g., OpenAI, Google, Anthropic)';
COMMENT ON COLUMN site.ai_providers.description IS 'Description of the AI provider';
COMMENT ON COLUMN site.ai_providers.api_base_url IS 'Base URL for the provider API';
COMMENT ON COLUMN site.ai_providers.auth_type IS 'Authentication type (api_key, oauth, etc.)';
COMMENT ON COLUMN site.ai_providers.is_enabled IS 'Whether this provider is enabled';
COMMENT ON COLUMN site.ai_providers.created_at IS 'Timestamp when the provider was created';
COMMENT ON COLUMN site.ai_providers.updated_at IS 'Timestamp when the provider was last updated';

-- Create indexes
CREATE INDEX IF NOT EXISTS ai_providers_name_idx ON site.ai_providers(name);
CREATE INDEX IF NOT EXISTS ai_providers_is_enabled_idx ON site.ai_providers(is_enabled);

-- Create trigger for updated_at
CREATE OR REPLACE FUNCTION update_ai_providers_updated_at()
RETURNS TRIGGER AS $$
BEGIN
   NEW.updated_at = CURRENT_TIMESTAMP;
   RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_ai_providers_timestamp
BEFORE UPDATE ON site.ai_providers
FOR EACH ROW EXECUTE FUNCTION update_ai_providers_updated_at();

-- Step 2: Create ai_models table
SELECT log_migration_step('Creating ai_models table');

CREATE TABLE IF NOT EXISTS site.ai_models (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  provider_id UUID NOT NULL REFERENCES site.ai_providers(id) ON DELETE CASCADE,
  name VARCHAR(255) NOT NULL,
  model_identifier VARCHAR(255) NOT NULL,
  description TEXT,
  capabilities TEXT[],
  max_tokens INTEGER,
  is_enabled BOOLEAN NOT NULL DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Add comments to columns
COMMENT ON COLUMN site.ai_models.id IS 'Unique identifier for the AI model';
COMMENT ON COLUMN site.ai_models.provider_id IS 'ID of the provider this model belongs to';
COMMENT ON COLUMN site.ai_models.name IS 'Display name of the model';
COMMENT ON COLUMN site.ai_models.model_identifier IS 'Identifier used in API calls (e.g., gpt-3.5-turbo)';
COMMENT ON COLUMN site.ai_models.description IS 'Description of the model';
COMMENT ON COLUMN site.ai_models.capabilities IS 'Array of capabilities this model supports';
COMMENT ON COLUMN site.ai_models.max_tokens IS 'Maximum tokens this model can process';
COMMENT ON COLUMN site.ai_models.is_enabled IS 'Whether this model is enabled';
COMMENT ON COLUMN site.ai_models.created_at IS 'Timestamp when the model was created';
COMMENT ON COLUMN site.ai_models.updated_at IS 'Timestamp when the model was last updated';

-- Create indexes
CREATE INDEX IF NOT EXISTS ai_models_provider_id_idx ON site.ai_models(provider_id);
CREATE INDEX IF NOT EXISTS ai_models_name_idx ON site.ai_models(name);
CREATE INDEX IF NOT EXISTS ai_models_is_enabled_idx ON site.ai_models(is_enabled);

-- Create trigger for updated_at
CREATE OR REPLACE FUNCTION update_ai_models_updated_at()
RETURNS TRIGGER AS $$
BEGIN
   NEW.updated_at = CURRENT_TIMESTAMP;
   RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_ai_models_timestamp
BEFORE UPDATE ON site.ai_models
FOR EACH ROW EXECUTE FUNCTION update_ai_models_updated_at();

-- Step 3: Create ai_configurations table
SELECT log_migration_step('Creating ai_configurations table');

CREATE TABLE IF NOT EXISTS site.ai_configurations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(255) NOT NULL,
  description TEXT,
  provider_id UUID NOT NULL REFERENCES site.ai_providers(id) ON DELETE CASCADE,
  default_model_id UUID REFERENCES site.ai_models(id) ON DELETE SET NULL,
  api_key TEXT,
  api_key_encrypted BOOLEAN NOT NULL DEFAULT FALSE,
  additional_settings JSONB,
  is_global BOOLEAN NOT NULL DEFAULT FALSE,
  is_enabled BOOLEAN NOT NULL DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Add comments to columns
COMMENT ON COLUMN site.ai_configurations.id IS 'Unique identifier for the AI configuration';
COMMENT ON COLUMN site.ai_configurations.name IS 'Name of the configuration';
COMMENT ON COLUMN site.ai_configurations.description IS 'Description of the configuration';
COMMENT ON COLUMN site.ai_configurations.provider_id IS 'ID of the AI provider';
COMMENT ON COLUMN site.ai_configurations.default_model_id IS 'ID of the default model to use';
COMMENT ON COLUMN site.ai_configurations.api_key IS 'API key for authentication (should be encrypted)';
COMMENT ON COLUMN site.ai_configurations.api_key_encrypted IS 'Whether the API key is encrypted';
COMMENT ON COLUMN site.ai_configurations.additional_settings IS 'Additional settings as JSON';
COMMENT ON COLUMN site.ai_configurations.is_global IS 'Whether this is a global configuration';
COMMENT ON COLUMN site.ai_configurations.is_enabled IS 'Whether this configuration is enabled';
COMMENT ON COLUMN site.ai_configurations.created_at IS 'Timestamp when the configuration was created';
COMMENT ON COLUMN site.ai_configurations.updated_at IS 'Timestamp when the configuration was last updated';

-- Create indexes
CREATE INDEX IF NOT EXISTS ai_configurations_provider_id_idx ON site.ai_configurations(provider_id);
CREATE INDEX IF NOT EXISTS ai_configurations_default_model_id_idx ON site.ai_configurations(default_model_id);
CREATE INDEX IF NOT EXISTS ai_configurations_is_global_idx ON site.ai_configurations(is_global);
CREATE INDEX IF NOT EXISTS ai_configurations_is_enabled_idx ON site.ai_configurations(is_enabled);

-- Create trigger for updated_at
CREATE OR REPLACE FUNCTION update_ai_configurations_updated_at()
RETURNS TRIGGER AS $$
BEGIN
   NEW.updated_at = CURRENT_TIMESTAMP;
   RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_ai_configurations_timestamp
BEFORE UPDATE ON site.ai_configurations
FOR EACH ROW EXECUTE FUNCTION update_ai_configurations_updated_at();

-- Step 4: Create ai_instructions table
SELECT log_migration_step('Creating ai_instructions table');

CREATE TABLE IF NOT EXISTS site.ai_instructions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(255) NOT NULL,
  description TEXT,
  task_type VARCHAR(255) NOT NULL,
  instructions TEXT NOT NULL,
  example_input TEXT,
  example_output TEXT,
  is_enabled BOOLEAN NOT NULL DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Add comments to columns
COMMENT ON COLUMN site.ai_instructions.id IS 'Unique identifier for the AI instruction';
COMMENT ON COLUMN site.ai_instructions.name IS 'Name of the instruction set';
COMMENT ON COLUMN site.ai_instructions.description IS 'Description of the instruction set';
COMMENT ON COLUMN site.ai_instructions.task_type IS 'Type of task this instruction is for';
COMMENT ON COLUMN site.ai_instructions.instructions IS 'The actual instructions for the AI';
COMMENT ON COLUMN site.ai_instructions.example_input IS 'Example input for testing';
COMMENT ON COLUMN site.ai_instructions.example_output IS 'Example expected output';
COMMENT ON COLUMN site.ai_instructions.is_enabled IS 'Whether this instruction set is enabled';
COMMENT ON COLUMN site.ai_instructions.created_at IS 'Timestamp when the instruction was created';
COMMENT ON COLUMN site.ai_instructions.updated_at IS 'Timestamp when the instruction was last updated';

-- Create indexes
CREATE INDEX IF NOT EXISTS ai_instructions_task_type_idx ON site.ai_instructions(task_type);
CREATE INDEX IF NOT EXISTS ai_instructions_is_enabled_idx ON site.ai_instructions(is_enabled);

-- Create trigger for updated_at
CREATE OR REPLACE FUNCTION update_ai_instructions_updated_at()
RETURNS TRIGGER AS $$
BEGIN
   NEW.updated_at = CURRENT_TIMESTAMP;
   RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_ai_instructions_timestamp
BEFORE UPDATE ON site.ai_instructions
FOR EACH ROW EXECUTE FUNCTION update_ai_instructions_updated_at();

-- Insert default data for OpenAI
SELECT log_migration_step('Inserting default data');

-- Insert OpenAI provider
INSERT INTO site.ai_providers (name, description, api_base_url, auth_type, is_enabled)
VALUES (
  'OpenAI',
  'OpenAI provides various AI models including GPT-3.5 and GPT-4.',
  'https://api.openai.com/v1',
  'api_key',
  TRUE
);

-- Get the OpenAI provider ID
DO $$
DECLARE
  openai_id UUID;
BEGIN
  SELECT id INTO openai_id FROM site.ai_providers WHERE name = 'OpenAI';
  
  -- Insert OpenAI models
  INSERT INTO site.ai_models (provider_id, name, model_identifier, description, capabilities, max_tokens, is_enabled)
  VALUES
    (openai_id, 'GPT-3.5 Turbo', 'gpt-3.5-turbo', 'GPT-3.5 Turbo is a good balance between capability and cost.', ARRAY['text_generation', 'chat', 'summarization'], 4096, TRUE),
    (openai_id, 'GPT-4', 'gpt-4', 'GPT-4 is more capable than GPT-3.5 but more expensive.', ARRAY['text_generation', 'chat', 'summarization', 'reasoning'], 8192, TRUE),
    (openai_id, 'GPT-4 Turbo', 'gpt-4-turbo-preview', 'GPT-4 Turbo is the latest version of GPT-4 with improved capabilities.', ARRAY['text_generation', 'chat', 'summarization', 'reasoning', 'code_generation'], 128000, TRUE);
  
  -- Insert default configuration using environment variable
  INSERT INTO site.ai_configurations (name, description, provider_id, default_model_id, api_key, api_key_encrypted, is_global, is_enabled)
  VALUES (
    'Default OpenAI Configuration',
    'Default configuration using the OPENAI_API_KEY environment variable.',
    openai_id,
    (SELECT id FROM site.ai_models WHERE model_identifier = 'gpt-3.5-turbo' AND provider_id = openai_id),
    NULL, -- Will be populated from environment variable at runtime
    FALSE,
    TRUE,
    TRUE
  );
  
  -- Insert default instructions
  INSERT INTO site.ai_instructions (name, description, task_type, instructions, example_input, example_output, is_enabled)
  VALUES
    (
      'Crop Rotation Analysis',
      'Instructions for analyzing crop rotation patterns and making recommendations.',
      'crop_rotation',
      'Analyze the provided crop history and soil data to recommend optimal crop rotation patterns. Consider soil health, pest management, and nutrient cycling in your analysis. Provide specific crop recommendations for each field based on its history and characteristics.',
      '{"fields": [{"name": "Field 1", "acres": 50, "soil_type": "Clay loam", "crop_history": ["Corn", "Soybeans", "Wheat"]}]}',
      '{"recommendations": [{"field": "Field 1", "next_crop": "Alfalfa", "reason": "After 3 years of row crops, a legume like alfalfa will help restore soil nitrogen and break pest cycles."}]}',
      TRUE
    ),
    (
      'Harvest Scheduling',
      'Instructions for optimizing harvest timing based on crop maturity and weather.',
      'harvest_scheduling',
      'Analyze the provided crop data, maturity indicators, and weather forecast to recommend optimal harvest timing. Consider crop moisture levels, weather risks, and equipment availability in your analysis. Provide specific harvest date recommendations for each field.',
      '{"crops": [{"field": "Field 1", "crop": "Corn", "planting_date": "2023-05-01", "current_moisture": 22, "maturity_indicators": "Milk line at 1/2"}], "weather_forecast": [...]}',
      '{"recommendations": [{"field": "Field 1", "crop": "Corn", "recommended_harvest_date": "2023-09-15", "reason": "Moisture content will reach optimal level by this date, and weather forecast shows dry conditions."}]}',
      TRUE
    ),
    (
      'Soil Health Analysis',
      'Instructions for analyzing soil test results and recommending improvements.',
      'soil_health',
      'Analyze the provided soil test results to assess soil health and recommend improvements. Consider nutrient levels, pH, organic matter, and soil structure in your analysis. Provide specific recommendations for amendments and practices to improve soil health.',
      '{"soil_tests": [{"field": "Field 1", "pH": 6.2, "organic_matter": 2.5, "nitrogen": 15, "phosphorus": 25, "potassium": 120}]}',
      '{"recommendations": [{"field": "Field 1", "issues": ["Low organic matter", "Slightly acidic pH"], "amendments": ["Add 2 tons/acre of compost", "Apply 1 ton/acre of lime"], "practices": ["Plant cover crops", "Reduce tillage"]}]}',
      TRUE
    ),
    (
      'Field Health Analysis',
      'Instructions for analyzing field conditions and identifying issues.',
      'field_health',
      'Analyze the provided field data, including crop appearance, pest presence, and environmental conditions to assess field health. Identify potential issues and recommend interventions. Consider crop stage, weather history, and management practices in your analysis.',
      '{"fields": [{"name": "Field 1", "crop": "Corn", "stage": "V6", "observations": ["Yellowing leaves in low areas", "Some cutworm damage"], "weather_history": [...]}]}',
      '{"assessments": [{"field": "Field 1", "issues": ["Nitrogen deficiency in low areas", "Early cutworm infestation"], "recommendations": ["Apply supplemental nitrogen to affected areas", "Scout for cutworms and apply insecticide if threshold is exceeded"]}]}',
      TRUE
    ),
    (
      'Herd Health Analysis',
      'Instructions for analyzing livestock health data and identifying issues.',
      'herd_health',
      'Analyze the provided livestock health data, including weight trends, feed consumption, and health observations to assess herd health. Identify potential issues and recommend interventions. Consider seasonal factors, feed quality, and management practices in your analysis.',
      '{"herd": {"type": "Dairy", "animals": [{"id": "A123", "age": 3, "weight_trend": "Declining", "milk_production": "Decreased 15%", "observations": "Reduced appetite"}]}}',
      '{"assessments": [{"animal_id": "A123", "issues": ["Potential subclinical mastitis", "Feed intake reduction"], "recommendations": ["Conduct CMT test", "Check feed quality", "Monitor temperature"]}]}',
      TRUE
    );
END $$;

-- Record the migration in the database_migrations table if it exists
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'site' AND table_name = 'database_migrations') THEN
        INSERT INTO site.database_migrations (id, name, file_path, "order", applied_at, created_at, updated_at)
        VALUES (
            gen_random_uuid(), 
            'add_ai_configuration_tables', 
            'webapp/server/db/migrations/add_ai_configuration_tables.sql',
            (SELECT COALESCE(MAX("order"), 0) + 1 FROM site.database_migrations),
            CURRENT_TIMESTAMP,
            CURRENT_TIMESTAMP,
            CURRENT_TIMESTAMP
        );
    END IF;
END $$;

-- Commit transaction
COMMIT;

-- Verify all tables were created
DO $$
DECLARE
    missing_tables TEXT := '';
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'site' AND table_name = 'ai_providers') THEN
        missing_tables := missing_tables || 'ai_providers, ';
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'site' AND table_name = 'ai_models') THEN
        missing_tables := missing_tables || 'ai_models, ';
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'site' AND table_name = 'ai_configurations') THEN
        missing_tables := missing_tables || 'ai_configurations, ';
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'site' AND table_name = 'ai_instructions') THEN
        missing_tables := missing_tables || 'ai_instructions, ';
    END IF;

    IF missing_tables <> '' THEN
        missing_tables := SUBSTRING(missing_tables, 1, LENGTH(missing_tables) - 2);
        RAISE EXCEPTION 'Migration failed. The following tables were not created: %', missing_tables;
    ELSE
        RAISE NOTICE 'Migration successful. All tables were created.';
    END IF;
END $$;