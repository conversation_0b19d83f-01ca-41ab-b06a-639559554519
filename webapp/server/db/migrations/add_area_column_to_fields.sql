-- Migration: Add area column to fields table
-- This migration adds an area column to the fields table that mirrors the size column

-- Set the search path to the appropriate schema
SET search_path TO site;

-- Add area column to fields table
ALTER TABLE fields ADD COLUMN IF NOT EXISTS area DECIMAL(10, 2);

-- Add area_unit column to fields table if it doesn't exist
ALTER TABLE fields ADD COLUMN IF NOT EXISTS area_unit VARCHAR(50);

-- Update area column with values from size column
UPDATE fields SET area = CAST(size AS DECIMAL(10, 2)) WHERE area IS NULL;

-- Update area_unit column with values from size_unit column
UPDATE fields SET area_unit = size_unit WHERE area_unit IS NULL;

-- Add comment to the column
COMMENT ON COLUMN fields.area IS 'Field area (mirrors size column)';
COMMENT ON COLUMN fields.area_unit IS 'Unit of measurement for area (mirrors size_unit column)';

-- Create a function to keep size and area in sync
CREATE OR REPLACE FUNCTION sync_field_size_area()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'UPDATE' THEN
        -- If size is updated, update area
        IF NEW.size IS DISTINCT FROM OLD.size THEN
            NEW.area := CAST(NEW.size AS DECIMAL(10, 2));
        END IF;

        -- If area is updated, update size
        IF NEW.area IS DISTINCT FROM OLD.area THEN
            NEW.size := NEW.area;
        END IF;

        -- If size_unit is updated, update area_unit
        IF NEW.size_unit IS DISTINCT FROM OLD.size_unit THEN
            NEW.area_unit := NEW.size_unit;
        END IF;

        -- If area_unit is updated, update size_unit
        IF NEW.area_unit IS DISTINCT FROM OLD.area_unit THEN
            NEW.size_unit := NEW.area_unit;
        END IF;
    ELSIF TG_OP = 'INSERT' THEN
        -- For new records, ensure area and size are in sync
        IF NEW.size IS NOT NULL AND NEW.area IS NULL THEN
            NEW.area := CAST(NEW.size AS DECIMAL(10, 2));
        ELSIF NEW.area IS NOT NULL AND NEW.size IS NULL THEN
            NEW.size := NEW.area;
        END IF;

        -- Ensure area_unit and size_unit are in sync
        IF NEW.size_unit IS NOT NULL AND NEW.area_unit IS NULL THEN
            NEW.area_unit := NEW.size_unit;
        ELSIF NEW.area_unit IS NOT NULL AND NEW.size_unit IS NULL THEN
            NEW.size_unit := NEW.area_unit;
        END IF;
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create a trigger to call the function before insert or update
DROP TRIGGER IF EXISTS sync_field_size_area_trigger ON fields;
CREATE TRIGGER sync_field_size_area_trigger
BEFORE INSERT OR UPDATE ON fields
FOR EACH ROW
EXECUTE FUNCTION sync_field_size_area();
