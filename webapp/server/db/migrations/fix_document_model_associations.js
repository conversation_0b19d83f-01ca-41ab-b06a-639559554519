// Migration: Fix duplicate associations in document-related models
// This migration removes duplicate associations from document-related model files
// and keeps only the ones in associations.js

import fs from 'fs';
import path from 'path';

const modelsDir = path.join(process.cwd(), 'server', 'models');

// List of models with duplicate associations
const modelsToFix = [
  {
    file: 'DocumentSigner.js',
    duplicateAssociations: [
      "DocumentSigner.belongsTo(SignableDocument, { foreignKey: 'document_id', as: 'signerDocument' });"
    ]
  },
  {
    file: 'DocumentSignature.js',
    duplicateAssociations: [
      "DocumentSignature.belongsTo(SignableDocument, { foreignKey: 'document_id', as: 'signatureDocument' });",
      "DocumentSignature.belongsTo(DocumentSigner, { foreignKey: 'signer_id', as: 'signer' });"
    ]
  },
  {
    file: 'DocumentField.js',
    duplicateAssociations: [
      "DocumentField.belongsTo(SignableDocument, { foreignKey: 'document_id', as: 'fieldDocument' });",
      "DocumentField.belongsTo(DocumentSigner, { foreignKey: 'signer_id', as: 'signer' });"
    ]
  },
  {
    file: 'DocumentAuditLog.js',
    duplicateAssociations: [
      "DocumentAuditLog.belongsTo(SignableDocument, { foreignKey: 'document_id', as: 'auditLogDocument' });",
      "DocumentAuditLog.belongsTo(DocumentSigner, { foreignKey: 'signer_id', as: 'signer' });",
      "DocumentAuditLog.belongsTo(User, { foreignKey: 'user_id', as: 'user' });"
    ]
  }
];

// Function to fix a model file
const fixModelFile = (modelFile, duplicateAssociations) => {
  const filePath = path.join(modelsDir, modelFile);
  
  // Read the file content
  let content = fs.readFileSync(filePath, 'utf8');
  
  // Remove each duplicate association
  duplicateAssociations.forEach(association => {
    content = content.replace(association, '// Association moved to associations.js');
  });
  
  // Write the updated content back to the file
  fs.writeFileSync(filePath, content);
  
  console.log(`Fixed duplicate associations in ${modelFile}`);
};

// Main function to run the migration
const runMigration = async () => {
  try {
    console.log('Starting migration to fix duplicate associations in document-related models...');
    
    // Fix each model file
    modelsToFix.forEach(model => {
      fixModelFile(model.file, model.duplicateAssociations);
    });
    
    console.log('Migration completed successfully.');
  } catch (error) {
    console.error('Migration failed:', error);
    process.exit(1);
  }
};

// Run the migration
runMigration();