-- Migration: Add customer portal features including Stripe fee settings and invoice payment tracking
-- Depends on: add_stripe_customer_id_to_users.sql

SET search_path TO site;

-- Add customer_pays_stripe_fees to farms table
ALTER TABLE farms
ADD COLUMN customer_pays_stripe_fees BOOLEAN NOT NULL DEFAULT FALSE,
ADD COLUMN customer_portal_stripe_fee_message TEXT NULL;

-- Add payment_transaction_id to invoices table
ALTER TABLE invoices
ADD COLUMN payment_transaction_id UUID NULL,
ADD COLUMN payment_method VARCHAR(50) NULL,
ADD COLUMN payment_date TIMESTAMP NULL,
ADD COLUMN payment_amount DECIMAL(15, 2) NULL,
ADD COLUMN stripe_payment_intent_id VARCHAR(255) NULL,
ADD COLUMN stripe_payment_method_id VARCHAR(255) NULL;

-- Create invoice_questions table for customer questions about invoices
CREATE TABLE invoice_questions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  invoice_id UUID NOT NULL REFERENCES invoices(id) ON DELETE CASCADE,
  customer_id UUID NOT NULL,
  farm_id UUID NOT NULL REFERENCES farms(id) ON DELETE CASCADE,
  question TEXT NOT NULL,
  response TEXT NULL,
  is_resolved BOOLEAN NOT NULL DEFAULT FALSE,
  created_at TIMESTAMP NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
  responded_at TIMESTAMP NULL,
  responded_by_user_id UUID NULL
);

-- Create index for faster lookups
CREATE INDEX idx_invoice_questions_invoice_id ON invoice_questions(invoice_id);
CREATE INDEX idx_invoice_questions_customer_id ON invoice_questions(customer_id);
CREATE INDEX idx_invoice_questions_farm_id ON invoice_questions(farm_id);

-- Add foreign key constraint for payment_transaction_id
ALTER TABLE invoices
ADD CONSTRAINT fk_invoices_payment_transaction
FOREIGN KEY (payment_transaction_id)
REFERENCES transactions(id)
ON DELETE SET NULL;