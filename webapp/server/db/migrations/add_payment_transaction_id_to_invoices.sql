-- Migration: Add payment_transaction_id column to invoices table
-- Depends on:

SET search_path TO site;

-- Check if payment_transaction_id column exists in invoices table
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_schema = 'site'
        AND table_name = 'invoices'
        AND column_name = 'payment_transaction_id'
    ) THEN
        -- Add payment_transaction_id to invoices table
        ALTER TABLE invoices
        ADD COLUMN payment_transaction_id UUID NULL,
        ADD COLUMN payment_method VARCHAR(50) NULL,
        ADD COLUMN payment_date TIMESTAMP NULL,
        ADD COLUMN payment_amount DECIMAL(15, 2) NULL,
        ADD COLUMN stripe_payment_intent_id VARCHAR(255) NULL,
        ADD COLUMN stripe_payment_method_id VARCHAR(255) NULL;

        -- Add foreign key constraint for payment_transaction_id if transactions table exists
        IF EXISTS (
            SELECT 1
            FROM information_schema.tables
            WHERE table_schema = 'site'
            AND table_name = 'transactions'
        ) THEN
            ALTER TABLE invoices
            ADD CONSTRAINT fk_invoices_payment_transaction
            FOREIGN KEY (payment_transaction_id)
            REFERENCES transactions(id)
            ON DELETE SET NULL;
        END IF;
    END IF;
END $$;

-- Record the migration in the database_migrations table if it exists
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'site' AND table_name = 'database_migrations') THEN
        INSERT INTO site.database_migrations (id, name, file_path, "order", applied_at, created_at, updated_at, status)
        VALUES (
            gen_random_uuid(),
            'Add payment_transaction_id to invoices table',
            'webapp/server/db/migrations/add_payment_transaction_id_to_invoices.sql',
            (SELECT COALESCE(MAX("order"), 0) + 1 FROM site.database_migrations),
            NOW(),
            NOW(),
            NOW(),
            'applied'
        );
    END IF;
END $$;