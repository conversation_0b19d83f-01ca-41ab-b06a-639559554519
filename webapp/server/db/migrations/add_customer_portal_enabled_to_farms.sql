-- Migration: Add customer_portal_enabled field to farms table to allow enabling/disabling the customer portal
-- Depends on: add_custom_login_columns.sql

-- Set the search path to the appropriate schema
SET search_path TO site;

-- Get the schema name from environment variable or use 'site' as default
DO $$
DECLARE
    schema_name TEXT;
BEGIN
    -- Get the schema name from environment variable
    schema_name := current_setting('app.db_schema', true);

    -- If the environment variable is not set, use 'site' as default
    IF schema_name IS NULL THEN
        schema_name := 'site';
    END IF;

    -- Add customer_portal_enabled column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_schema = schema_name
        AND table_name = 'farms'
        AND column_name = 'customer_portal_enabled'
    ) THEN
        EXECUTE format('ALTER TABLE %I.farms ADD COLUMN customer_portal_enabled BOOLEAN NOT NULL DEFAULT FALSE', schema_name);
    END IF;

    -- Record the migration in the database_migrations table if it exists
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = schema_name AND table_name = 'database_migrations') THEN
        INSERT INTO site.database_migrations (id, name, file_path, "order", applied_at, created_at, updated_at, status)
        VALUES (
            gen_random_uuid(),
            'Add customer portal enabled field to farms',
            'webapp/server/db/migrations/add_customer_portal_enabled_to_farms.sql',
            (SELECT COALESCE(MAX("order"), 0) + 1 FROM site.database_migrations),
            NOW(),
            NOW(),
            NOW(),
            'applied'
        );
    END IF;
END $$;