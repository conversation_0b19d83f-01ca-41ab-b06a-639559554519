-- Migration: Add delivery scheduling tables
-- Depends on: add_farm_fulfillment_options_table.sql

SET search_path TO site;

-- Delivery schedules table for farm's available delivery days/times
CREATE TABLE delivery_schedules (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  farm_id UUID NOT NULL REFERENCES farms(id) ON DELETE CASCADE,
  day_of_week INTEGER NOT NULL, -- 0 = Sunday, 1 = Monday, etc.
  start_time TIME NOT NULL,
  end_time TIME NOT NULL,
  max_deliveries INTEGER,
  is_active BOOLEAN NOT NULL DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX delivery_schedules_farm_id_idx ON delivery_schedules(farm_id);

-- Delivery slots table for specific delivery time slots
CREATE TABLE delivery_slots (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  schedule_id UUID NOT NULL REFERENCES delivery_schedules(id) ON DELETE CASCADE,
  slot_time TIME NOT NULL,
  max_deliveries INTEGER,
  is_active BOOLEAN NOT NULL DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX delivery_slots_schedule_id_idx ON delivery_slots(schedule_id);

-- Delivery routes table for grouping deliveries by route
CREATE TABLE delivery_routes (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  farm_id UUID NOT NULL REFERENCES farms(id) ON DELETE CASCADE,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  delivery_date DATE NOT NULL,
  driver_id UUID REFERENCES users(id),
  status VARCHAR(50) NOT NULL DEFAULT 'pending', -- pending, in_progress, completed, canceled
  start_time TIMESTAMP WITH TIME ZONE,
  end_time TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX delivery_routes_farm_id_idx ON delivery_routes(farm_id);
CREATE INDEX delivery_routes_driver_id_idx ON delivery_routes(driver_id);

-- Delivery assignments table for linking purchase requests to delivery routes
CREATE TABLE delivery_assignments (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  route_id UUID NOT NULL REFERENCES delivery_routes(id) ON DELETE CASCADE,
  purchase_request_id UUID NOT NULL REFERENCES purchase_requests(id) ON DELETE CASCADE,
  delivery_order INTEGER NOT NULL, -- Order in the delivery route
  estimated_arrival_time TIMESTAMP WITH TIME ZONE,
  actual_arrival_time TIMESTAMP WITH TIME ZONE,
  status VARCHAR(50) NOT NULL DEFAULT 'pending', -- pending, in_progress, completed, canceled
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  UNIQUE(route_id, purchase_request_id)
);

CREATE INDEX delivery_assignments_route_id_idx ON delivery_assignments(route_id);
CREATE INDEX delivery_assignments_purchase_request_id_idx ON delivery_assignments(purchase_request_id);

-- Delivery tracking table for real-time driver location tracking
CREATE TABLE delivery_tracking (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  route_id UUID NOT NULL REFERENCES delivery_routes(id) ON DELETE CASCADE,
  latitude DECIMAL(10, 8) NOT NULL,
  longitude DECIMAL(11, 8) NOT NULL,
  timestamp TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX delivery_tracking_route_id_idx ON delivery_tracking(route_id);
CREATE INDEX delivery_tracking_timestamp_idx ON delivery_tracking(timestamp);

-- Add delivery_slot_id to purchase_requests table
ALTER TABLE purchase_requests
ADD COLUMN delivery_slot_id UUID REFERENCES delivery_slots(id),
ADD COLUMN delivery_route_id UUID REFERENCES delivery_routes(id);

COMMENT ON COLUMN purchase_requests.delivery_slot_id IS 'Reference to the selected delivery slot (if applicable)';
COMMENT ON COLUMN purchase_requests.delivery_route_id IS 'Reference to the assigned delivery route (if applicable)';