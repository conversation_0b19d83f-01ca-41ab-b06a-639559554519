-- Migration: Add document blockchain verification table
-- Depends on: add_document_signing_tables.sql

-- Set the search path to the appropriate schema
SET search_path TO site;

-- Add document_blockchain_verifications table
CREATE TABLE IF NOT EXISTS document_blockchain_verifications (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  document_id UUID NOT NULL REFERENCES signable_documents(id) ON DELETE CASCADE,
  blockchain_network VARCHAR(50) NOT NULL,
  transaction_hash VARCHAR(255) NOT NULL,
  block_number INTEGER,
  document_hash VARCHAR(255) NOT NULL,
  verification_data JSONB,
  verification_status VARCHAR(10) NOT NULL DEFAULT 'pending' CHECK (verification_status IN ('pending', 'confirmed', 'failed')),
  verified_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add indexes
CREATE INDEX IF NOT EXISTS document_blockchain_verifications_document_id_idx ON document_blockchain_verifications(document_id);
CREATE INDEX IF NOT EXISTS document_blockchain_verifications_transaction_hash_idx ON document_blockchain_verifications(transaction_hash);
CREATE INDEX IF NOT EXISTS document_blockchain_verifications_verification_status_idx ON document_blockchain_verifications(verification_status);

-- Add a column to signable_documents to indicate if a document has blockchain verification
ALTER TABLE signable_documents ADD COLUMN IF NOT EXISTS has_blockchain_verification BOOLEAN DEFAULT FALSE;
