-- Migration to add field_soil_data table
-- This table stores soil type and health information for fields

-- Set the search path to the appropriate schema
SET search_path TO site;

-- Step 1: Create field_soil_data table

CREATE TABLE IF NOT EXISTS site.field_soil_data (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    farm_id UUID NOT NULL REFERENCES site.farms(id) ON DELETE CASCADE,
    field_id UUID NOT NULL REFERENCES site.fields(id) ON DELETE CASCADE,
    soil_type VARCHAR(100) NOT NULL,
    soil_health_index INTEGER,
    land_capability_class VARCHAR(50),
    erosion_risk VARCHAR(50),
    water_availability VARCHAR(50),
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Add comments to the table and columns
COMMENT ON TABLE site.field_soil_data IS 'Stores soil type and health information for fields';
COMMENT ON COLUMN site.field_soil_data.id IS 'Unique identifier for the soil data record';
COMMENT ON COLUMN site.field_soil_data.farm_id IS 'ID of the farm this soil data belongs to';
COMMENT ON COLUMN site.field_soil_data.field_id IS 'ID of the field this soil data is for';
COMMENT ON COLUMN site.field_soil_data.soil_type IS 'Type of soil (e.g., Loam, Clay, Sandy)';
COMMENT ON COLUMN site.field_soil_data.soil_health_index IS 'Soil health index (0-100)';
COMMENT ON COLUMN site.field_soil_data.land_capability_class IS 'Land capability classification (e.g., Class I, Class II)';
COMMENT ON COLUMN site.field_soil_data.erosion_risk IS 'Erosion risk level (Low, Medium, High)';
COMMENT ON COLUMN site.field_soil_data.water_availability IS 'Water availability level (Low, Medium, High)';
COMMENT ON COLUMN site.field_soil_data.last_updated IS 'Date when the soil data was last updated';

-- Step 2: Create indexes for better performance

CREATE INDEX IF NOT EXISTS idx_field_soil_data_farm_id ON site.field_soil_data(farm_id);
CREATE INDEX IF NOT EXISTS idx_field_soil_data_field_id ON site.field_soil_data(field_id);
CREATE INDEX IF NOT EXISTS idx_field_soil_data_soil_type ON site.field_soil_data(soil_type);

-- Step 3: Create trigger for updated_at timestamp

CREATE TRIGGER update_field_soil_data_timestamp
BEFORE UPDATE ON site.field_soil_data
FOR EACH ROW
EXECUTE FUNCTION update_timestamp();

