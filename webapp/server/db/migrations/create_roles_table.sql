-- Create roles table for role-based permissions

-- Set the search path to the site schema
SET search_path TO site;

-- Create roles table if it doesn't exist
CREATE TABLE IF NOT EXISTS site.roles (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  farm_id UUID REFERENCES site.farms(id),
  name VARCHAR(50) NOT NULL,
  description VARCHAR(255),
  is_system_role BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Add comments to columns
COMMENT ON COLUMN site.roles.id IS 'Unique identifier for the role';
COMMENT ON COLUMN site.roles.farm_id IS 'If null, this is a global role. If set, this is a farm-specific role.';
COMMENT ON COLUMN site.roles.name IS 'Name of the role';
COMMENT ON COLUMN site.roles.description IS 'Description of the role';
COMMENT ON COLUMN site.roles.is_system_role IS 'Whether this is a system-defined role that cannot be deleted';
COMMENT ON COLUMN site.roles.created_at IS 'Timestamp when the role was created';
COMMENT ON COLUMN site.roles.updated_at IS 'Timestamp when the role was last updated';

-- Create unique index on farm_id and name
CREATE UNIQUE INDEX IF NOT EXISTS roles_farm_name_idx ON site.roles(farm_id, name);

-- Verify the table was created
DO $$
DECLARE
    table_exists BOOLEAN;
BEGIN
    SELECT EXISTS (
        SELECT 1 
        FROM information_schema.tables 
        WHERE table_schema = 'site' 
        AND table_name = 'roles'
    ) INTO table_exists;

    IF table_exists THEN
        RAISE NOTICE 'The roles table was successfully created.';
    ELSE
        RAISE EXCEPTION 'Failed to create the roles table.';
    END IF;
END $$;

-- Create default global roles
INSERT INTO site.roles (id, farm_id, name, description, is_system_role)
VALUES 
  (gen_random_uuid(), NULL, 'Global Admin', 'Full access to all platform features', TRUE),
  (gen_random_uuid(), NULL, 'Support Staff', 'Access to support features and limited admin functions', TRUE),
  (gen_random_uuid(), NULL, 'Read Only', 'View-only access to platform data', TRUE)
ON CONFLICT (farm_id, name) DO NOTHING;

-- Create default farm roles
DO $$
DECLARE
    farm_cursor CURSOR FOR SELECT id FROM site.farms;
    current_farm_id UUID;
BEGIN
    OPEN farm_cursor;
    LOOP
        FETCH farm_cursor INTO current_farm_id;
        EXIT WHEN NOT FOUND;

        -- Insert default roles for each farm
        INSERT INTO site.roles (id, farm_id, name, description, is_system_role)
        VALUES 
          (gen_random_uuid(), current_farm_id, 'Farm Owner', 'Full access to farm features', TRUE),
          (gen_random_uuid(), current_farm_id, 'Farm Admin', 'Administrative access to farm features', TRUE),
          (gen_random_uuid(), current_farm_id, 'Farm Manager', 'Management access to farm operations', TRUE),
          (gen_random_uuid(), current_farm_id, 'Farm Employee', 'Limited access to assigned farm tasks', TRUE),
          (gen_random_uuid(), current_farm_id, 'Accountant', 'Access to financial features', TRUE)
        ON CONFLICT (farm_id, name) DO NOTHING;
    END LOOP;
    CLOSE farm_cursor;
END $$;
