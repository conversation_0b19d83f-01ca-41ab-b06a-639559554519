-- Add user_id column to vendors table

-- Set the search path to the site schema
SET search_path TO site;

-- Add user_id column if it doesn't exist
ALTER TABLE site.vendors ADD COLUMN IF NOT EXISTS user_id UUID REFERENCES site.users(id);
COMMENT ON COLUMN site.vendors.user_id IS 'User account associated with this vendor';

-- Verify the column was added
DO $$
DECLARE
    column_exists BOOLEAN;
BEGIN
    SELECT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_schema = 'site' 
        AND table_name = 'vendors' 
        AND column_name = 'user_id'
    ) INTO column_exists;
    
    IF column_exists THEN
        RAISE NOTICE 'The user_id column was successfully added to the vendors table.';
    ELSE
        RAISE EXCEPTION 'Failed to add the user_id column to the vendors table.';
    END IF;
END $$;

-- Create index for better query performance if it doesn't exist
CREATE INDEX IF NOT EXISTS idx_vendors_user_id ON site.vendors(user_id);
