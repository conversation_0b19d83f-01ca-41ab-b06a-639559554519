-- Migration: Add foreign key constraint to MatrixRoomMapping.conversation_id
-- Depends on: add_chat_system_tables.sql

SET search_path TO site;

-- Add foreign key constraint to matrix_room_mappings table
-- This constraint was temporarily disabled to allow server startup before chat_conversations table existed
ALTER TABLE matrix_room_mappings 
ADD CONSTRAINT fk_matrix_room_mapping_conversation_id 
FOREIGN KEY (conversation_id) 
REFERENCES chat_conversations(id) 
ON DELETE CASCADE;

-- Add index for performance
CREATE INDEX IF NOT EXISTS idx_matrix_room_mappings_conversation_id 
ON matrix_room_mappings(conversation_id);

-- Add comment
COMMENT ON CONSTRAINT fk_matrix_room_mapping_conversation_id ON matrix_room_mappings 
IS 'Foreign key constraint linking matrix room mappings to chat conversations';
