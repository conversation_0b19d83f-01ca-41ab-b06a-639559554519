-- Migration: Add user_online_status table
-- Depends on:

SET search_path TO site;

-- Check if the table already exists
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.tables
        WHERE table_schema = 'site'
        AND table_name = 'user_online_status'
    ) THEN
        -- User Online Status Table
        CREATE TABLE user_online_status (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            user_id UUID REFERENCES users(id) ON DELETE CASCADE,
            is_online BOOLEAN DEFAULT FALSE,
            last_active_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE(user_id)
        );

        -- Create index for performance
        CREATE INDEX idx_user_online_status_user_id ON user_online_status(user_id);

        -- Create trigger for updated_at timestamp
        CREATE TRIGGER update_user_online_status_timestamp BEFORE UPDATE ON user_online_status
        FOR EACH ROW EXECUTE FUNCTION update_timestamp();

        -- Add comment to table
        COMMENT ON TABLE user_online_status IS 'Stores online status of users';
    END IF;
END $$;

-- Record the migration in the database_migrations table if it exists
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'site' AND table_name = 'database_migrations') THEN
        INSERT INTO site.database_migrations (
            id, 
            name, 
            file_path, 
            "order", 
            applied_at, 
            created_at, 
            updated_at, 
            status
        )
        VALUES (
            uuid_generate_v4(), 
            'add_user_online_status_table', 
            'webapp/server/db/migrations/add_user_online_status_table.sql',
            (SELECT COALESCE(MAX("order"), 0) + 1 FROM site.database_migrations),
            NOW(),
            NOW(),
            NOW(),
            'completed'
        )
        ON CONFLICT (name) DO NOTHING;
    END IF;
END $$;