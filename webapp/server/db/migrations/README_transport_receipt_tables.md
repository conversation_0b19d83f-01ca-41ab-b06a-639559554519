# Transport and Receipt Management Tables Migration

## Overview

This migration adds the necessary database tables to support the transport management and receipt management features of the NxtAcre Farm Management Platform. These features were already implemented in the application code but were missing the corresponding database tables.

## Tables Added

1. **drivers** - Stores information about drivers who transport goods to and from farms
2. **driver_locations** - Tracks the real-time location of drivers
3. **deliveries** - Manages deliveries from farms to customers
4. **pickups** - Manages pickups from suppliers or customers
5. **driver_schedules** - Manages driver schedules for deliveries, pickups, and other activities
6. **receipts** - Stores receipt information for expense tracking and approval

## How to Apply the Migration

To apply this migration, run the following command from the PostgreSQL command line:

```sql
\i '/path/to/webapp/server/db/migrations/add_transport_and_receipt_tables.sql'
```

Or you can run it using the database migration script:

```bash
node server/scripts/run-migration.js add_transport_and_receipt_tables.sql
```

## Dependencies

This migration has dependencies on the following existing tables:
- farms
- users
- customers
- suppliers
- orders
- expenses
- tenants

Make sure these tables exist before running this migration.

## Verification

After running the migration, you can verify that the tables were created correctly by running:

```sql
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'site' 
AND table_name IN ('drivers', 'driver_locations', 'deliveries', 'pickups', 'receipts');
```

You should see all five tables listed in the results.

## Models

These tables correspond to the following models in the application:
- Driver.js
- DriverLocation.js
- Delivery.js
- Pickup.js
- DriverSchedule.js
- Receipt.js

The database schema has been designed to match the fields and relationships defined in these models.

## Notes

- All tables include created_at and updated_at timestamps that are automatically managed
- Foreign key relationships are properly defined to maintain data integrity
- Appropriate indexes have been added for performance optimization
- ENUM constraints are implemented using CHECK constraints
- All columns have descriptive comments
