-- Add is_approved column to user_farms table

-- Set the search path to the site schema
SET search_path TO site;

-- Add is_approved column if it doesn't exist
ALTER TABLE site.user_farms ADD COLUMN IF NOT EXISTS is_approved BOOLEAN DEFAULT FALSE;
COMMENT ON COLUMN site.user_farms.is_approved IS 'Whether this user has been approved by a farm owner or admin';

-- Verify the column was added
DO $$
DECLARE
    column_exists BOOLEAN;
BEGIN
    SELECT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_schema = 'site' 
        AND table_name = 'user_farms' 
        AND column_name = 'is_approved'
    ) INTO column_exists;
    
    IF column_exists THEN
        RAISE NOTICE 'The is_approved column was successfully added to the user_farms table.';
    ELSE
        RAISE EXCEPTION 'Failed to add the is_approved column to the user_farms table.';
    END IF;
END $$;

-- Create index for better query performance if it doesn't exist
CREATE INDEX IF NOT EXISTS idx_user_farms_is_approved ON site.user_farms(is_approved);
