-- Add AI analysis tables for farm management
-- This migration adds tables for crop rotation, harvest scheduling, soil health, field health, and herd health analyses

-- Set the search path to the site schema
SET search_path TO site;

-- Create a function to log migration progress
CREATE OR REPLACE FUNCTION log_migration_step(step_name TEXT)
RETURNS VOID AS $$
BEGIN
    RAISE NOTICE 'Migration step: %', step_name;
END;
$$ LANGUAGE plpgsql;

-- Begin transaction
BEGIN;

-- Step 1: Create ai_crop_rotation_analyses table
SELECT log_migration_step('Creating ai_crop_rotation_analyses table');

CREATE TABLE IF NOT EXISTS site.ai_crop_rotation_analyses (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  farm_id UUID NOT NULL REFERENCES site.farms(id) ON DELETE CASCADE,
  field_id UUID REFERENCES site.fields(id) ON DELETE CASCADE,
  analysis_date TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
  current_crop VARCHAR(255),
  previous_crops JSONB,
  recommended_next_crops JSONB NOT NULL,
  rotation_plan JSONB,
  soil_health_impact TEXT,
  pest_management_impact TEXT,
  yield_impact TEXT,
  confidence_score DECIMAL(5, 2) NOT NULL,
  is_applied BOOLEAN NOT NULL DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Add comments to columns
COMMENT ON COLUMN site.ai_crop_rotation_analyses.id IS 'Unique identifier for the crop rotation analysis';
COMMENT ON COLUMN site.ai_crop_rotation_analyses.farm_id IS 'ID of the farm this analysis belongs to';
COMMENT ON COLUMN site.ai_crop_rotation_analyses.field_id IS 'ID of the field this analysis applies to (optional, can be farm-wide)';
COMMENT ON COLUMN site.ai_crop_rotation_analyses.analysis_date IS 'Date when the analysis was performed';
COMMENT ON COLUMN site.ai_crop_rotation_analyses.current_crop IS 'Current crop in the field';
COMMENT ON COLUMN site.ai_crop_rotation_analyses.previous_crops IS 'JSON array of previous crops with years';
COMMENT ON COLUMN site.ai_crop_rotation_analyses.recommended_next_crops IS 'JSON array of recommended next crops with reasons';
COMMENT ON COLUMN site.ai_crop_rotation_analyses.rotation_plan IS 'JSON object with multi-year rotation plan';
COMMENT ON COLUMN site.ai_crop_rotation_analyses.soil_health_impact IS 'Impact of the rotation plan on soil health';
COMMENT ON COLUMN site.ai_crop_rotation_analyses.pest_management_impact IS 'Impact of the rotation plan on pest management';
COMMENT ON COLUMN site.ai_crop_rotation_analyses.yield_impact IS 'Expected impact on yield';
COMMENT ON COLUMN site.ai_crop_rotation_analyses.confidence_score IS 'AI confidence score for this analysis (0-100)';
COMMENT ON COLUMN site.ai_crop_rotation_analyses.is_applied IS 'Whether this analysis has been applied to farm planning';
COMMENT ON COLUMN site.ai_crop_rotation_analyses.created_at IS 'Timestamp when the analysis was created';
COMMENT ON COLUMN site.ai_crop_rotation_analyses.updated_at IS 'Timestamp when the analysis was last updated';

-- Create indexes
CREATE INDEX IF NOT EXISTS ai_crop_rotation_analyses_farm_id_idx ON site.ai_crop_rotation_analyses(farm_id);
CREATE INDEX IF NOT EXISTS ai_crop_rotation_analyses_field_id_idx ON site.ai_crop_rotation_analyses(field_id);
CREATE INDEX IF NOT EXISTS ai_crop_rotation_analyses_analysis_date_idx ON site.ai_crop_rotation_analyses(analysis_date);
CREATE INDEX IF NOT EXISTS ai_crop_rotation_analyses_is_applied_idx ON site.ai_crop_rotation_analyses(is_applied);

-- Create trigger for updated_at
CREATE OR REPLACE FUNCTION update_ai_crop_rotation_analyses_updated_at()
RETURNS TRIGGER AS $$
BEGIN
   NEW.updated_at = CURRENT_TIMESTAMP;
   RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_ai_crop_rotation_analyses_timestamp
BEFORE UPDATE ON site.ai_crop_rotation_analyses
FOR EACH ROW EXECUTE FUNCTION update_ai_crop_rotation_analyses_updated_at();

-- Step 2: Create ai_harvest_schedule_analyses table
SELECT log_migration_step('Creating ai_harvest_schedule_analyses table');

CREATE TABLE IF NOT EXISTS site.ai_harvest_schedule_analyses (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  farm_id UUID NOT NULL REFERENCES site.farms(id) ON DELETE CASCADE,
  field_id UUID REFERENCES site.fields(id) ON DELETE CASCADE,
  crop_id UUID REFERENCES site.crops(id) ON DELETE SET NULL,
  analysis_date TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
  planting_date DATE,
  crop_maturity_data JSONB,
  recommended_harvest_window JSONB NOT NULL,
  optimal_harvest_date DATE,
  weather_considerations TEXT,
  equipment_availability_impact TEXT,
  quality_impact TEXT,
  yield_impact TEXT,
  confidence_score DECIMAL(5, 2) NOT NULL,
  is_applied BOOLEAN NOT NULL DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Add comments to columns
COMMENT ON COLUMN site.ai_harvest_schedule_analyses.id IS 'Unique identifier for the harvest schedule analysis';
COMMENT ON COLUMN site.ai_harvest_schedule_analyses.farm_id IS 'ID of the farm this analysis belongs to';
COMMENT ON COLUMN site.ai_harvest_schedule_analyses.field_id IS 'ID of the field this analysis applies to';
COMMENT ON COLUMN site.ai_harvest_schedule_analyses.crop_id IS 'ID of the crop this analysis applies to';
COMMENT ON COLUMN site.ai_harvest_schedule_analyses.analysis_date IS 'Date when the analysis was performed';
COMMENT ON COLUMN site.ai_harvest_schedule_analyses.planting_date IS 'Date when the crop was planted';
COMMENT ON COLUMN site.ai_harvest_schedule_analyses.crop_maturity_data IS 'JSON object with crop maturity indicators';
COMMENT ON COLUMN site.ai_harvest_schedule_analyses.recommended_harvest_window IS 'JSON object with start and end dates for harvest window';
COMMENT ON COLUMN site.ai_harvest_schedule_analyses.optimal_harvest_date IS 'Optimal date for harvest';
COMMENT ON COLUMN site.ai_harvest_schedule_analyses.weather_considerations IS 'Weather considerations for harvest timing';
COMMENT ON COLUMN site.ai_harvest_schedule_analyses.equipment_availability_impact IS 'Impact of equipment availability on harvest timing';
COMMENT ON COLUMN site.ai_harvest_schedule_analyses.quality_impact IS 'Impact of harvest timing on crop quality';
COMMENT ON COLUMN site.ai_harvest_schedule_analyses.yield_impact IS 'Impact of harvest timing on yield';
COMMENT ON COLUMN site.ai_harvest_schedule_analyses.confidence_score IS 'AI confidence score for this analysis (0-100)';
COMMENT ON COLUMN site.ai_harvest_schedule_analyses.is_applied IS 'Whether this analysis has been applied to farm planning';
COMMENT ON COLUMN site.ai_harvest_schedule_analyses.created_at IS 'Timestamp when the analysis was created';
COMMENT ON COLUMN site.ai_harvest_schedule_analyses.updated_at IS 'Timestamp when the analysis was last updated';

-- Create indexes
CREATE INDEX IF NOT EXISTS ai_harvest_schedule_analyses_farm_id_idx ON site.ai_harvest_schedule_analyses(farm_id);
CREATE INDEX IF NOT EXISTS ai_harvest_schedule_analyses_field_id_idx ON site.ai_harvest_schedule_analyses(field_id);
CREATE INDEX IF NOT EXISTS ai_harvest_schedule_analyses_crop_id_idx ON site.ai_harvest_schedule_analyses(crop_id);
CREATE INDEX IF NOT EXISTS ai_harvest_schedule_analyses_analysis_date_idx ON site.ai_harvest_schedule_analyses(analysis_date);
CREATE INDEX IF NOT EXISTS ai_harvest_schedule_analyses_optimal_harvest_date_idx ON site.ai_harvest_schedule_analyses(optimal_harvest_date);
CREATE INDEX IF NOT EXISTS ai_harvest_schedule_analyses_is_applied_idx ON site.ai_harvest_schedule_analyses(is_applied);

-- Create trigger for updated_at
CREATE OR REPLACE FUNCTION update_ai_harvest_schedule_analyses_updated_at()
RETURNS TRIGGER AS $$
BEGIN
   NEW.updated_at = CURRENT_TIMESTAMP;
   RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_ai_harvest_schedule_analyses_timestamp
BEFORE UPDATE ON site.ai_harvest_schedule_analyses
FOR EACH ROW EXECUTE FUNCTION update_ai_harvest_schedule_analyses_updated_at();

-- Step 3: Create ai_soil_health_analyses table
SELECT log_migration_step('Creating ai_soil_health_analyses table');

CREATE TABLE IF NOT EXISTS site.ai_soil_health_analyses (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  farm_id UUID NOT NULL REFERENCES site.farms(id) ON DELETE CASCADE,
  field_id UUID REFERENCES site.fields(id) ON DELETE CASCADE,
  analysis_date TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
  soil_test_data JSONB,
  nutrient_levels JSONB,
  organic_matter DECIMAL(5, 2),
  ph_level DECIMAL(4, 2),
  issues_identified JSONB,
  recommendations JSONB NOT NULL,
  expected_improvements TEXT,
  implementation_timeline TEXT,
  confidence_score DECIMAL(5, 2) NOT NULL,
  is_applied BOOLEAN NOT NULL DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Add comments to columns
COMMENT ON COLUMN site.ai_soil_health_analyses.id IS 'Unique identifier for the soil health analysis';
COMMENT ON COLUMN site.ai_soil_health_analyses.farm_id IS 'ID of the farm this analysis belongs to';
COMMENT ON COLUMN site.ai_soil_health_analyses.field_id IS 'ID of the field this analysis applies to';
COMMENT ON COLUMN site.ai_soil_health_analyses.analysis_date IS 'Date when the analysis was performed';
COMMENT ON COLUMN site.ai_soil_health_analyses.soil_test_data IS 'JSON object with soil test data';
COMMENT ON COLUMN site.ai_soil_health_analyses.nutrient_levels IS 'JSON object with nutrient levels';
COMMENT ON COLUMN site.ai_soil_health_analyses.organic_matter IS 'Percentage of organic matter in soil';
COMMENT ON COLUMN site.ai_soil_health_analyses.ph_level IS 'pH level of soil';
COMMENT ON COLUMN site.ai_soil_health_analyses.issues_identified IS 'JSON array of soil health issues identified';
COMMENT ON COLUMN site.ai_soil_health_analyses.recommendations IS 'JSON array of recommendations for improving soil health';
COMMENT ON COLUMN site.ai_soil_health_analyses.expected_improvements IS 'Expected improvements from implementing recommendations';
COMMENT ON COLUMN site.ai_soil_health_analyses.implementation_timeline IS 'Timeline for implementing recommendations';
COMMENT ON COLUMN site.ai_soil_health_analyses.confidence_score IS 'AI confidence score for this analysis (0-100)';
COMMENT ON COLUMN site.ai_soil_health_analyses.is_applied IS 'Whether this analysis has been applied to farm planning';
COMMENT ON COLUMN site.ai_soil_health_analyses.created_at IS 'Timestamp when the analysis was created';
COMMENT ON COLUMN site.ai_soil_health_analyses.updated_at IS 'Timestamp when the analysis was last updated';

-- Create indexes
CREATE INDEX IF NOT EXISTS ai_soil_health_analyses_farm_id_idx ON site.ai_soil_health_analyses(farm_id);
CREATE INDEX IF NOT EXISTS ai_soil_health_analyses_field_id_idx ON site.ai_soil_health_analyses(field_id);
CREATE INDEX IF NOT EXISTS ai_soil_health_analyses_analysis_date_idx ON site.ai_soil_health_analyses(analysis_date);
CREATE INDEX IF NOT EXISTS ai_soil_health_analyses_is_applied_idx ON site.ai_soil_health_analyses(is_applied);

-- Create trigger for updated_at
CREATE OR REPLACE FUNCTION update_ai_soil_health_analyses_updated_at()
RETURNS TRIGGER AS $$
BEGIN
   NEW.updated_at = CURRENT_TIMESTAMP;
   RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_ai_soil_health_analyses_timestamp
BEFORE UPDATE ON site.ai_soil_health_analyses
FOR EACH ROW EXECUTE FUNCTION update_ai_soil_health_analyses_updated_at();

-- Step 4: Create ai_field_health_analyses table
SELECT log_migration_step('Creating ai_field_health_analyses table');

CREATE TABLE IF NOT EXISTS site.ai_field_health_analyses (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  farm_id UUID NOT NULL REFERENCES site.farms(id) ON DELETE CASCADE,
  field_id UUID REFERENCES site.fields(id) ON DELETE CASCADE,
  crop_id UUID REFERENCES site.crops(id) ON DELETE SET NULL,
  analysis_date TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
  vegetation_index_data JSONB,
  pest_pressure_data JSONB,
  disease_indicators JSONB,
  weather_impact_data JSONB,
  issues_identified JSONB,
  recommendations JSONB NOT NULL,
  priority_level VARCHAR(20),
  expected_impact TEXT,
  confidence_score DECIMAL(5, 2) NOT NULL,
  is_applied BOOLEAN NOT NULL DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Add comments to columns
COMMENT ON COLUMN site.ai_field_health_analyses.id IS 'Unique identifier for the field health analysis';
COMMENT ON COLUMN site.ai_field_health_analyses.farm_id IS 'ID of the farm this analysis belongs to';
COMMENT ON COLUMN site.ai_field_health_analyses.field_id IS 'ID of the field this analysis applies to';
COMMENT ON COLUMN site.ai_field_health_analyses.crop_id IS 'ID of the crop this analysis applies to';
COMMENT ON COLUMN site.ai_field_health_analyses.analysis_date IS 'Date when the analysis was performed';
COMMENT ON COLUMN site.ai_field_health_analyses.vegetation_index_data IS 'JSON object with vegetation index data';
COMMENT ON COLUMN site.ai_field_health_analyses.pest_pressure_data IS 'JSON object with pest pressure data';
COMMENT ON COLUMN site.ai_field_health_analyses.disease_indicators IS 'JSON object with disease indicators';
COMMENT ON COLUMN site.ai_field_health_analyses.weather_impact_data IS 'JSON object with weather impact data';
COMMENT ON COLUMN site.ai_field_health_analyses.issues_identified IS 'JSON array of field health issues identified';
COMMENT ON COLUMN site.ai_field_health_analyses.recommendations IS 'JSON array of recommendations for improving field health';
COMMENT ON COLUMN site.ai_field_health_analyses.priority_level IS 'Priority level for addressing issues (High, Medium, Low)';
COMMENT ON COLUMN site.ai_field_health_analyses.expected_impact IS 'Expected impact of implementing recommendations';
COMMENT ON COLUMN site.ai_field_health_analyses.confidence_score IS 'AI confidence score for this analysis (0-100)';
COMMENT ON COLUMN site.ai_field_health_analyses.is_applied IS 'Whether this analysis has been applied to farm planning';
COMMENT ON COLUMN site.ai_field_health_analyses.created_at IS 'Timestamp when the analysis was created';
COMMENT ON COLUMN site.ai_field_health_analyses.updated_at IS 'Timestamp when the analysis was last updated';

-- Create indexes
CREATE INDEX IF NOT EXISTS ai_field_health_analyses_farm_id_idx ON site.ai_field_health_analyses(farm_id);
CREATE INDEX IF NOT EXISTS ai_field_health_analyses_field_id_idx ON site.ai_field_health_analyses(field_id);
CREATE INDEX IF NOT EXISTS ai_field_health_analyses_crop_id_idx ON site.ai_field_health_analyses(crop_id);
CREATE INDEX IF NOT EXISTS ai_field_health_analyses_analysis_date_idx ON site.ai_field_health_analyses(analysis_date);
CREATE INDEX IF NOT EXISTS ai_field_health_analyses_priority_level_idx ON site.ai_field_health_analyses(priority_level);
CREATE INDEX IF NOT EXISTS ai_field_health_analyses_is_applied_idx ON site.ai_field_health_analyses(is_applied);

-- Create trigger for updated_at
CREATE OR REPLACE FUNCTION update_ai_field_health_analyses_updated_at()
RETURNS TRIGGER AS $$
BEGIN
   NEW.updated_at = CURRENT_TIMESTAMP;
   RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_ai_field_health_analyses_timestamp
BEFORE UPDATE ON site.ai_field_health_analyses
FOR EACH ROW EXECUTE FUNCTION update_ai_field_health_analyses_updated_at();

-- Step 5: Create ai_herd_health_analyses table
SELECT log_migration_step('Creating ai_herd_health_analyses table');

CREATE TABLE IF NOT EXISTS site.ai_herd_health_analyses (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  farm_id UUID NOT NULL REFERENCES site.farms(id) ON DELETE CASCADE,
  livestock_group_id UUID REFERENCES site.livestock_groups(id) ON DELETE CASCADE,
  analysis_date TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
  health_indicators JSONB,
  nutrition_data JSONB,
  weight_trends JSONB,
  reproduction_data JSONB,
  issues_identified JSONB,
  recommendations JSONB NOT NULL,
  priority_level VARCHAR(20),
  expected_impact TEXT,
  confidence_score DECIMAL(5, 2) NOT NULL,
  is_applied BOOLEAN NOT NULL DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Add comments to columns
COMMENT ON COLUMN site.ai_herd_health_analyses.id IS 'Unique identifier for the herd health analysis';
COMMENT ON COLUMN site.ai_herd_health_analyses.farm_id IS 'ID of the farm this analysis belongs to';
COMMENT ON COLUMN site.ai_herd_health_analyses.livestock_group_id IS 'ID of the livestock group this analysis applies to';
COMMENT ON COLUMN site.ai_herd_health_analyses.analysis_date IS 'Date when the analysis was performed';
COMMENT ON COLUMN site.ai_herd_health_analyses.health_indicators IS 'JSON object with health indicators';
COMMENT ON COLUMN site.ai_herd_health_analyses.nutrition_data IS 'JSON object with nutrition data';
COMMENT ON COLUMN site.ai_herd_health_analyses.weight_trends IS 'JSON object with weight trends';
COMMENT ON COLUMN site.ai_herd_health_analyses.reproduction_data IS 'JSON object with reproduction data';
COMMENT ON COLUMN site.ai_herd_health_analyses.issues_identified IS 'JSON array of herd health issues identified';
COMMENT ON COLUMN site.ai_herd_health_analyses.recommendations IS 'JSON array of recommendations for improving herd health';
COMMENT ON COLUMN site.ai_herd_health_analyses.priority_level IS 'Priority level for addressing issues (High, Medium, Low)';
COMMENT ON COLUMN site.ai_herd_health_analyses.expected_impact IS 'Expected impact of implementing recommendations';
COMMENT ON COLUMN site.ai_herd_health_analyses.confidence_score IS 'AI confidence score for this analysis (0-100)';
COMMENT ON COLUMN site.ai_herd_health_analyses.is_applied IS 'Whether this analysis has been applied to farm planning';
COMMENT ON COLUMN site.ai_herd_health_analyses.created_at IS 'Timestamp when the analysis was created';
COMMENT ON COLUMN site.ai_herd_health_analyses.updated_at IS 'Timestamp when the analysis was last updated';

-- Create indexes
CREATE INDEX IF NOT EXISTS ai_herd_health_analyses_farm_id_idx ON site.ai_herd_health_analyses(farm_id);
CREATE INDEX IF NOT EXISTS ai_herd_health_analyses_livestock_group_id_idx ON site.ai_herd_health_analyses(livestock_group_id);
CREATE INDEX IF NOT EXISTS ai_herd_health_analyses_analysis_date_idx ON site.ai_herd_health_analyses(analysis_date);
CREATE INDEX IF NOT EXISTS ai_herd_health_analyses_priority_level_idx ON site.ai_herd_health_analyses(priority_level);
CREATE INDEX IF NOT EXISTS ai_herd_health_analyses_is_applied_idx ON site.ai_herd_health_analyses(is_applied);

-- Create trigger for updated_at
CREATE OR REPLACE FUNCTION update_ai_herd_health_analyses_updated_at()
RETURNS TRIGGER AS $$
BEGIN
   NEW.updated_at = CURRENT_TIMESTAMP;
   RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_ai_herd_health_analyses_timestamp
BEFORE UPDATE ON site.ai_herd_health_analyses
FOR EACH ROW EXECUTE FUNCTION update_ai_herd_health_analyses_updated_at();

-- Record the migration in the database_migrations table if it exists
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'site' AND table_name = 'database_migrations') THEN
        INSERT INTO site.database_migrations (id, name, file_path, "order", applied_at, created_at, updated_at)
        VALUES (
            gen_random_uuid(), 
            'add_ai_analysis_tables', 
            'webapp/server/db/migrations/add_ai_analysis_tables.sql',
            (SELECT COALESCE(MAX("order"), 0) + 1 FROM site.database_migrations),
            CURRENT_TIMESTAMP,
            CURRENT_TIMESTAMP,
            CURRENT_TIMESTAMP
        );
    END IF;
END $$;

-- Commit transaction
COMMIT;

-- Verify all tables were created
DO $$
DECLARE
    missing_tables TEXT := '';
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'site' AND table_name = 'ai_crop_rotation_analyses') THEN
        missing_tables := missing_tables || 'ai_crop_rotation_analyses, ';
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'site' AND table_name = 'ai_harvest_schedule_analyses') THEN
        missing_tables := missing_tables || 'ai_harvest_schedule_analyses, ';
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'site' AND table_name = 'ai_soil_health_analyses') THEN
        missing_tables := missing_tables || 'ai_soil_health_analyses, ';
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'site' AND table_name = 'ai_field_health_analyses') THEN
        missing_tables := missing_tables || 'ai_field_health_analyses, ';
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'site' AND table_name = 'ai_herd_health_analyses') THEN
        missing_tables := missing_tables || 'ai_herd_health_analyses, ';
    END IF;

    IF missing_tables <> '' THEN
        missing_tables := SUBSTRING(missing_tables, 1, LENGTH(missing_tables) - 2);
        RAISE EXCEPTION 'Migration failed. The following tables were not created: %', missing_tables;
    ELSE
        RAISE NOTICE 'Migration successful. All tables were created.';
    END IF;
END $$;