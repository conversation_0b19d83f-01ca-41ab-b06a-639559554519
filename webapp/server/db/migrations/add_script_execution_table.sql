-- Migration: Add script execution table
-- Creates a table to track executed scripts from the server/scripts folder
-- Set the search path to the appropriate schema
SET search_path TO site;

CREATE TABLE IF NOT EXISTS script_executions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name VARCHAR(255) NOT NULL,
  description TEXT,
  file_path VARCHAR(255) NOT NULL,
  executed BOOLEAN DEFAULT FALSE,
  executed_at TIMESTAMP WITH TIME ZONE,
  executed_by UUID REFERENCES users(id),
  status VARCHAR(20) DEFAULT 'pending',
  output TEXT,
  error_message TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add index on file_path for faster lookups
CREATE INDEX IF NOT EXISTS idx_script_executions_file_path ON script_executions(file_path);

-- Add index on executed status for filtering
CREATE INDEX IF NOT EXISTS idx_script_executions_executed ON script_executions(executed);

-- Add index on status for filtering
CREATE INDEX IF NOT EXISTS idx_script_executions_status ON script_executions(status);