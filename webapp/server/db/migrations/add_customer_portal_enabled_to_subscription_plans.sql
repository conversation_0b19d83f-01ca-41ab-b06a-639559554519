-- Migration: Add customer portal enabled feature to subscription plans
-- Depends on:

SET search_path TO site;

-- Add customer_portal_enabled to subscription_plans features

-- Update Basic plan (no customer portal)
UPDATE subscription_plans 
SET features = features || '{"customer_portal_enabled": false}'::jsonb
WHERE name = 'Basic' AND NOT (features ? 'customer_portal_enabled');

-- Update Standard plan (no customer portal)
UPDATE subscription_plans 
SET features = features || '{"customer_portal_enabled": false}'::jsonb
WHERE name = 'Standard' AND NOT (features ? 'customer_portal_enabled');

-- Update Premium plan (includes customer portal)
UPDATE subscription_plans 
SET features = features || '{"customer_portal_enabled": true}'::jsonb
WHERE name = 'Premium' AND NOT (features ? 'customer_portal_enabled');

-- Update Enterprise plan (includes customer portal)
UPDATE subscription_plans 
SET features = features || '{"customer_portal_enabled": true}'::jsonb
WHERE name = 'Enterprise' AND NOT (features ? 'customer_portal_enabled');

-- Verify the migration was successful
DO $$
DECLARE
  basic_plan_features JSONB;
  standard_plan_features JSONB;
  premium_plan_features JSONB;
  enterprise_plan_features JSONB;
BEGIN
  -- Get features for each plan
  SELECT features INTO basic_plan_features FROM site.subscription_plans WHERE name = 'Basic' LIMIT 1;
  SELECT features INTO standard_plan_features FROM site.subscription_plans WHERE name = 'Standard' LIMIT 1;
  SELECT features INTO premium_plan_features FROM site.subscription_plans WHERE name = 'Premium' LIMIT 1;
  SELECT features INTO enterprise_plan_features FROM site.subscription_plans WHERE name = 'Enterprise' LIMIT 1;

  -- Check if customer_portal_enabled is set for each plan
  IF NOT (basic_plan_features ? 'customer_portal_enabled') THEN
    RAISE EXCEPTION 'Migration failed. customer_portal_enabled not set for Basic plan.';
  END IF;

  IF NOT (standard_plan_features ? 'customer_portal_enabled') THEN
    RAISE EXCEPTION 'Migration failed. customer_portal_enabled not set for Standard plan.';
  END IF;

  IF NOT (premium_plan_features ? 'customer_portal_enabled') THEN
    RAISE EXCEPTION 'Migration failed. customer_portal_enabled not set for Premium plan.';
  END IF;

  IF NOT (enterprise_plan_features ? 'customer_portal_enabled') THEN
    RAISE EXCEPTION 'Migration failed. customer_portal_enabled not set for Enterprise plan.';
  END IF;

  RAISE NOTICE 'Migration successful. customer_portal_enabled feature added to all subscription plans.';
END $$;