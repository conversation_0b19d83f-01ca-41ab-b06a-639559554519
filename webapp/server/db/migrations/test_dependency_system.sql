-- Migration: Test dependency system
-- This migration is used to test the dependency system
-- Depends on: add_schema_migrations_table.sql

-- Set the search path to the appropriate schema
SET search_path TO site;

-- Create a test table to demonstrate dependencies
CREATE TABLE IF NOT EXISTS dependency_test (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name VARCHAR(255) NOT NULL,
  description TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add a comment to the table
COMMENT ON TABLE dependency_test IS 'This table was created to test the dependency system';

-- Insert a test record
INSERT INTO dependency_test (name, description)
VALUES ('Test Dependency', 'This record was created by a migration that depends on add_schema_migrations_table.sql');