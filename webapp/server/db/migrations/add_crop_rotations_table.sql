-- Migration to add crop_rotations table
-- This table stores crop rotation plans for fields

-- Set the search path to the appropriate schema
SET search_path TO site;

-- Step 1: Create crop_rotations table

CREATE TABLE IF NOT EXISTS site.crop_rotations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    farm_id UUID NOT NULL REFERENCES site.farms(id) ON DELETE CASCADE,
    field_id UUID NOT NULL REFERENCES site.fields(id) ON DELETE CASCADE,
    current_crop VARCHAR(100) NOT NULL,
    recommended_sequence JSONB NOT NULL,
    benefits JSONB,
    rotation_years INTEGER,
    soil_health_impact VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Add comments to the table and columns
COMMENT ON TABLE site.crop_rotations IS 'Stores crop rotation plans for fields';
COMMENT ON COLUMN site.crop_rotations.id IS 'Unique identifier for the crop rotation plan';
COMMENT ON COLUMN site.crop_rotations.farm_id IS 'ID of the farm this rotation plan belongs to';
COMMENT ON COLUMN site.crop_rotations.field_id IS 'ID of the field this rotation plan is for';
COMMENT ON COLUMN site.crop_rotations.current_crop IS 'Current crop in the field';
COMMENT ON COLUMN site.crop_rotations.recommended_sequence IS 'Recommended crop rotation sequence';
COMMENT ON COLUMN site.crop_rotations.benefits IS 'Benefits of the recommended rotation';
COMMENT ON COLUMN site.crop_rotations.rotation_years IS 'Number of years in the rotation cycle';
COMMENT ON COLUMN site.crop_rotations.soil_health_impact IS 'Impact on soil health (positive, neutral, negative)';

-- Step 2: Create indexes for better performance

CREATE INDEX IF NOT EXISTS idx_crop_rotations_farm_id ON site.crop_rotations(farm_id);
CREATE INDEX IF NOT EXISTS idx_crop_rotations_field_id ON site.crop_rotations(field_id);
CREATE INDEX IF NOT EXISTS idx_crop_rotations_current_crop ON site.crop_rotations(current_crop);

-- Step 3: Create trigger for updated_at timestamp

CREATE TRIGGER update_crop_rotations_timestamp
BEFORE UPDATE ON site.crop_rotations
FOR EACH ROW
EXECUTE FUNCTION update_timestamp();

