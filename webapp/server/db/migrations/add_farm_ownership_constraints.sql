-- Migration: Add farm ownership constraints
-- Depends on: update_user_farms_table.sql

SET search_path TO site;

-- Create a function to check if there's only one farm owner per farm
CREATE OR REPLACE FUNCTION check_one_farm_owner_per_farm()
RETURNS TRIGGER AS $$
DECLARE
    farm_owner_role_id UUID;
    farm_owner_count INTEGER;
BEGIN
    -- Get the role_id for 'Farm Owner'
    SELECT id INTO farm_owner_role_id FROM roles 
    WHERE name = 'Farm Owner' AND farm_id = NEW.farm_id;
    
    -- If the new record is not for a farm owner, allow it
    IF NEW.role_id != farm_owner_role_id THEN
        RETURN NEW;
    END IF;
    
    -- Count existing farm owners for this farm (excluding the current record if it's an update)
    IF TG_OP = 'UPDATE' THEN
        SELECT COUNT(*) INTO farm_owner_count FROM user_farms
        WHERE farm_id = NEW.farm_id 
        AND role_id = farm_owner_role_id
        AND id != NEW.id;
    ELSE
        SELECT COUNT(*) INTO farm_owner_count FROM user_farms
        WHERE farm_id = NEW.farm_id 
        AND role_id = farm_owner_role_id;
    END IF;
    
    -- If there's already a farm owner, raise an exception
    IF farm_owner_count > 0 THEN
        RAISE EXCEPTION 'A farm can only have one owner. Please transfer ownership before adding another owner.';
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create a trigger to enforce the one farm owner per farm constraint
DROP TRIGGER IF EXISTS enforce_one_farm_owner_per_farm ON user_farms;
CREATE TRIGGER enforce_one_farm_owner_per_farm
BEFORE INSERT OR UPDATE ON user_farms
FOR EACH ROW
EXECUTE FUNCTION check_one_farm_owner_per_farm();

-- Log the migration
SELECT log_migration_step('Added constraint to ensure only one farm owner per farm');