-- Create receipts table for receipt management

-- Set the search path to the site schema
SET search_path TO site;

-- Create receipts table if it doesn't exist
CREATE TABLE IF NOT EXISTS site.receipts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  receipt_number VARCHAR(100),
  vendor_name VARCHAR(255),
  amount DECIMAL(10, 2),
  currency VARCHAR(3) NOT NULL DEFAULT 'USD',
  receipt_date TIMESTAMP WITH TIME ZONE,
  description TEXT,
  status VARCHAR(20) NOT NULL DEFAULT 'pending',
  file_path VARCHAR(1024),
  file_size BIGINT,
  file_type VARCHAR(100),
  mime_type VARCHAR(100),
  email_source VARCHAR(255),
  email_subject VARCHAR(512),
  email_received_at TIMESTAMP WITH TIME ZONE,
  tenant_id UUID NOT NULL REFERENCES site.tenants(id),
  farm_id UUID NOT NULL REFERENCES site.farms(id),
  expense_id UUID REFERENCES site.expenses(id),
  uploaded_by UUID REFERENCES site.users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Add enum constraint for status
ALTER TABLE site.receipts DROP CONSTRAINT IF EXISTS receipts_status_check;
ALTER TABLE site.receipts ADD CONSTRAINT receipts_status_check 
  CHECK (status IN ('pending', 'approved', 'rejected'));

-- Add comments to columns
COMMENT ON COLUMN site.receipts.id IS 'Unique identifier for the receipt';
COMMENT ON COLUMN site.receipts.receipt_number IS 'Receipt number or identifier';
COMMENT ON COLUMN site.receipts.vendor_name IS 'Name of the vendor or supplier';
COMMENT ON COLUMN site.receipts.amount IS 'Total amount on the receipt';
COMMENT ON COLUMN site.receipts.currency IS 'Currency code (e.g., USD, EUR)';
COMMENT ON COLUMN site.receipts.receipt_date IS 'Date on the receipt';
COMMENT ON COLUMN site.receipts.description IS 'Description or notes about the receipt';
COMMENT ON COLUMN site.receipts.status IS 'Status of the receipt: pending, approved, or rejected';
COMMENT ON COLUMN site.receipts.file_path IS 'Path to the stored receipt file';
COMMENT ON COLUMN site.receipts.file_size IS 'Size of the receipt file in bytes';
COMMENT ON COLUMN site.receipts.file_type IS 'Type of the receipt file';
COMMENT ON COLUMN site.receipts.mime_type IS 'MIME type of the receipt file';
COMMENT ON COLUMN site.receipts.email_source IS 'Email address that sent the receipt (if received via email)';
COMMENT ON COLUMN site.receipts.email_subject IS 'Subject of the email containing the receipt';
COMMENT ON COLUMN site.receipts.email_received_at IS 'When the email with the receipt was received';
COMMENT ON COLUMN site.receipts.tenant_id IS 'ID of the tenant this receipt belongs to';
COMMENT ON COLUMN site.receipts.farm_id IS 'ID of the farm this receipt belongs to';
COMMENT ON COLUMN site.receipts.expense_id IS 'ID of the associated expense record (if any)';
COMMENT ON COLUMN site.receipts.uploaded_by IS 'ID of the user who uploaded the receipt';
COMMENT ON COLUMN site.receipts.created_at IS 'Timestamp when the receipt was created';
COMMENT ON COLUMN site.receipts.updated_at IS 'Timestamp when the receipt was last updated';

-- Create indexes
CREATE INDEX IF NOT EXISTS receipts_receipt_number_idx ON site.receipts(receipt_number);
CREATE INDEX IF NOT EXISTS receipts_vendor_name_idx ON site.receipts(vendor_name);
CREATE INDEX IF NOT EXISTS receipts_receipt_date_idx ON site.receipts(receipt_date);
CREATE INDEX IF NOT EXISTS receipts_status_idx ON site.receipts(status);
CREATE INDEX IF NOT EXISTS receipts_tenant_id_idx ON site.receipts(tenant_id);
CREATE INDEX IF NOT EXISTS receipts_farm_id_idx ON site.receipts(farm_id);
CREATE INDEX IF NOT EXISTS receipts_expense_id_idx ON site.receipts(expense_id);

-- Verify the table was created
DO $$
DECLARE
    table_exists BOOLEAN;
BEGIN
    SELECT EXISTS (
        SELECT 1 
        FROM information_schema.tables 
        WHERE table_schema = 'site' 
        AND table_name = 'receipts'
    ) INTO table_exists;

    IF table_exists THEN
        RAISE NOTICE 'The receipts table was successfully created.';
    ELSE
        RAISE EXCEPTION 'Failed to create the receipts table.';
    END IF;
END $$;

-- Create trigger for updated_at
CREATE OR REPLACE FUNCTION update_receipts_updated_at()
RETURNS TRIGGER AS $$
BEGIN
   NEW.updated_at = CURRENT_TIMESTAMP;
   RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_receipts_timestamp
BEFORE UPDATE ON site.receipts
FOR EACH ROW EXECUTE FUNCTION update_receipts_updated_at();