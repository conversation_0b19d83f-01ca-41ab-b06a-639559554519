-- Migration to add yield_predictions table
-- This table stores yield predictions for crops

-- Set the search path to the appropriate schema
SET search_path TO site;

-- Step 1: Create yield_predictions table

CREATE TABLE IF NOT EXISTS site.yield_predictions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    farm_id UUID NOT NULL REFERENCES site.farms(id) ON DELETE CASCADE,
    crop_id UUID NOT NULL REFERENCES site.crops(id) ON DELETE CASCADE,
    field_id UUID REFERENCES site.fields(id) ON DELETE SET NULL,
    predicted_yield DECIMAL(10, 2) NOT NULL,
    yield_unit VARCHAR(50) NOT NULL,
    confidence_level DECIMAL(5, 4) NOT NULL,
    factors JSONB,
    prediction_date DATE NOT NULL DEFAULT CURRENT_DATE,
    harvest_year INTEGER NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Add comments to the table and columns
COMMENT ON TABLE site.yield_predictions IS 'Stores yield predictions for crops';
COMMENT ON COLUMN site.yield_predictions.id IS 'Unique identifier for the yield prediction';
COMMENT ON COLUMN site.yield_predictions.farm_id IS 'ID of the farm this prediction belongs to';
COMMENT ON COLUMN site.yield_predictions.crop_id IS 'ID of the crop this prediction is for';
COMMENT ON COLUMN site.yield_predictions.field_id IS 'ID of the field this prediction is for (optional)';
COMMENT ON COLUMN site.yield_predictions.predicted_yield IS 'Predicted yield amount';
COMMENT ON COLUMN site.yield_predictions.yield_unit IS 'Unit of measurement for yield (e.g., bushels/acre, tons/acre)';
COMMENT ON COLUMN site.yield_predictions.confidence_level IS 'Confidence level of the prediction (0-1)';
COMMENT ON COLUMN site.yield_predictions.factors IS 'Factors that influenced the prediction';
COMMENT ON COLUMN site.yield_predictions.prediction_date IS 'Date when the prediction was made';
COMMENT ON COLUMN site.yield_predictions.harvest_year IS 'Year for which the yield is predicted';

-- Step 2: Create indexes for better performance

CREATE INDEX IF NOT EXISTS idx_yield_predictions_farm_id ON site.yield_predictions(farm_id);
CREATE INDEX IF NOT EXISTS idx_yield_predictions_crop_id ON site.yield_predictions(crop_id);
CREATE INDEX IF NOT EXISTS idx_yield_predictions_field_id ON site.yield_predictions(field_id);
CREATE INDEX IF NOT EXISTS idx_yield_predictions_prediction_date ON site.yield_predictions(prediction_date);
CREATE INDEX IF NOT EXISTS idx_yield_predictions_harvest_year ON site.yield_predictions(harvest_year);

-- Step 3: Create trigger for updated_at timestamp

CREATE TRIGGER update_yield_predictions_timestamp
BEFORE UPDATE ON site.yield_predictions
FOR EACH ROW
EXECUTE FUNCTION update_timestamp();

