-- Migration: Create stripe_financial_connections table
-- Depends on:

SET search_path TO site;

-- Create stripe_financial_connections table if it doesn't exist
CREATE TABLE IF NOT EXISTS stripe_financial_connections (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    farm_id UUID NOT NULL REFERENCES farms(id) ON DELETE CASCADE,
    stripe_session_id VARCHAR(255) NOT NULL,
    stripe_account_id VARCHAR(255) NOT NULL,
    institution_name VARCHAR(255),
    last_successful_update TIMESTAMP WITH TIME ZONE,
    status VARCHAR(50) DEFAULT 'active',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add comment to the table
COMMENT ON TABLE stripe_financial_connections IS 'Stores Stripe Financial Connections data for bank account connections';

-- Add comments to columns
COMMENT ON COLUMN stripe_financial_connections.id IS 'Primary key';
COMMENT ON COLUMN stripe_financial_connections.farm_id IS 'Foreign key to farms table';
COMMENT ON COLUMN stripe_financial_connections.stripe_session_id IS 'Stripe Financial Connections session ID';
COMMENT ON COLUMN stripe_financial_connections.stripe_account_id IS 'Stripe Financial Connections account ID';
COMMENT ON COLUMN stripe_financial_connections.institution_name IS 'Name of the financial institution';
COMMENT ON COLUMN stripe_financial_connections.last_successful_update IS 'Timestamp of the last successful data update';
COMMENT ON COLUMN stripe_financial_connections.status IS 'Status of the connection (active, inactive, etc.)';
COMMENT ON COLUMN stripe_financial_connections.created_at IS 'Timestamp when the record was created';
COMMENT ON COLUMN stripe_financial_connections.updated_at IS 'Timestamp when the record was last updated';

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_stripe_financial_connections_farm_id ON stripe_financial_connections(farm_id);
CREATE INDEX IF NOT EXISTS idx_stripe_financial_connections_stripe_account_id ON stripe_financial_connections(stripe_account_id);
CREATE INDEX IF NOT EXISTS idx_stripe_financial_connections_status ON stripe_financial_connections(status);

-- Create trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_stripe_financial_connections_timestamp()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS update_stripe_financial_connections_timestamp ON stripe_financial_connections;
CREATE TRIGGER update_stripe_financial_connections_timestamp
BEFORE UPDATE ON stripe_financial_connections
FOR EACH ROW
EXECUTE FUNCTION update_stripe_financial_connections_timestamp();