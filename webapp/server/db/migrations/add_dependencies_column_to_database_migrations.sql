-- Migration: Add dependencies column to database_migrations table
-- This migration adds the missing dependencies column to the database_migrations table

-- Set the search path to the appropriate schema
SET search_path TO site;

-- Add the dependencies column to the database_migrations table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_schema = 'site'
        AND table_name = 'database_migrations'
        AND column_name = 'dependencies'
    ) THEN
        ALTER TABLE site.database_migrations
        ADD COLUMN dependencies TEXT[] DEFAULT '{}';
        
        COMMENT ON COLUMN site.database_migrations.dependencies IS 'Array of migration names that this migration depends on';
    END IF;
END $$;