-- Migration: Add Ambrook tables for caching API data

-- Set the search path to the appropriate schema
SET search_path TO site;

-- Create AmbrookGrant table
CREATE TABLE IF NOT EXISTS ambrook_grants (
  id VARCHAR(255) PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  amount DECIMAL(10, 2),
  deadline DATE,
  eligibility TEXT,
  status VARCHAR(50),
  category VARCHAR(100),
  url TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create AmbrookLoan table
CREATE TABLE IF NOT EXISTS ambrook_loans (
  id VARCHAR(255) PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  provider VARCHAR(255),
  interest_rate VARCHAR(50),
  term VARCHAR(100),
  eligibility TEXT,
  status VARCHAR(50),
  category VARCHAR(100),
  url TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create AmbrookReport table
CREATE TABLE IF NOT EXISTS ambrook_reports (
  id VARCHAR(255) PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  date DATE,
  type VARCHAR(100),
  url TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create index for faster lookups
CREATE INDEX idx_ambrook_grants_status ON ambrook_grants(status);
CREATE INDEX idx_ambrook_grants_category ON ambrook_grants(category);
CREATE INDEX idx_ambrook_loans_status ON ambrook_loans(status);
CREATE INDEX idx_ambrook_loans_category ON ambrook_loans(category);
CREATE INDEX idx_ambrook_reports_type ON ambrook_reports(type);
CREATE INDEX idx_ambrook_reports_date ON ambrook_reports(date);
