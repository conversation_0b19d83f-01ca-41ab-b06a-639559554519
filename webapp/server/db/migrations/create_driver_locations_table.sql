-- Create driver_locations table for transport management

-- Set the search path to the site schema
SET search_path TO site;

-- Create driver_locations table if it doesn't exist
CREATE TABLE IF NOT EXISTS site.driver_locations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  farm_id UUID NOT NULL REFERENCES site.farms(id),
  driver_id UUID NOT NULL REFERENCES site.drivers(id),
  latitude DECIMAL(10, 7) NOT NULL,
  longitude DECIMAL(10, 7) NOT NULL,
  accuracy DECIMAL(10, 2),
  altitude DECIMAL(10, 2),
  speed DECIMAL(10, 2),
  heading DECIMAL(5, 2),
  address VARCHAR(255),
  timestamp TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
  device_id VARCHAR(100),
  battery_level DECIMAL(5, 2),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Add comments to columns
COMMENT ON COLUMN site.driver_locations.id IS 'Unique identifier for the driver location record';
COMMENT ON COLUMN site.driver_locations.farm_id IS 'ID of the farm this driver location belongs to';
COMMENT ON COLUMN site.driver_locations.driver_id IS 'ID of the driver';
COMMENT ON COLUMN site.driver_locations.latitude IS 'Latitude coordinate';
COMMENT ON COLUMN site.driver_locations.longitude IS 'Longitude coordinate';
COMMENT ON COLUMN site.driver_locations.accuracy IS 'Accuracy of the location in meters';
COMMENT ON COLUMN site.driver_locations.altitude IS 'Altitude in meters';
COMMENT ON COLUMN site.driver_locations.speed IS 'Speed in km/h';
COMMENT ON COLUMN site.driver_locations.heading IS 'Direction in degrees (0-360)';
COMMENT ON COLUMN site.driver_locations.address IS 'Reverse geocoded address';
COMMENT ON COLUMN site.driver_locations.timestamp IS 'When the location was recorded';
COMMENT ON COLUMN site.driver_locations.device_id IS 'ID of the device that reported the location';
COMMENT ON COLUMN site.driver_locations.battery_level IS 'Battery level of the device (0-100)';
COMMENT ON COLUMN site.driver_locations.created_at IS 'Timestamp when the record was created';
COMMENT ON COLUMN site.driver_locations.updated_at IS 'Timestamp when the record was last updated';

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_driver_locations_driver_id ON site.driver_locations(driver_id);
CREATE INDEX IF NOT EXISTS idx_driver_locations_timestamp ON site.driver_locations(timestamp);
CREATE INDEX IF NOT EXISTS idx_driver_locations_farm_id ON site.driver_locations(farm_id);
CREATE INDEX IF NOT EXISTS idx_driver_locations_coordinates ON site.driver_locations(latitude, longitude);

-- Verify the table was created
DO $$
DECLARE
    table_exists BOOLEAN;
BEGIN
    SELECT EXISTS (
        SELECT 1 
        FROM information_schema.tables 
        WHERE table_schema = 'site' 
        AND table_name = 'driver_locations'
    ) INTO table_exists;

    IF table_exists THEN
        RAISE NOTICE 'The driver_locations table was successfully created.';
    ELSE
        RAISE EXCEPTION 'Failed to create the driver_locations table.';
    END IF;
END $$;

-- Create trigger for updated_at
CREATE OR REPLACE FUNCTION update_driver_locations_updated_at()
RETURNS TRIGGER AS $$
BEGIN
   NEW.updated_at = CURRENT_TIMESTAMP;
   RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_driver_locations_timestamp
BEFORE UPDATE ON site.driver_locations
FOR EACH ROW EXECUTE FUNCTION update_driver_locations_updated_at();