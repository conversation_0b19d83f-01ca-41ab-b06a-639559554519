-- Migration: Add chat message tags table
-- Depends on: add_chat_system_tables.sql

SET search_path TO site;

-- Chat Message Tags Table
CREATE TABLE chat_message_tags (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    message_id UUID REFERENCES chat_messages(id) ON DELETE CASCADE,
    tag VARCHAR(50) NOT NULL,
    created_by UUID REFERENCES users(id) ON DELETE SET NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(message_id, tag)
);

-- Create indexes for performance
CREATE INDEX idx_chat_message_tags_message_id ON chat_message_tags(message_id);
CREATE INDEX idx_chat_message_tags_tag ON chat_message_tags(tag);
CREATE INDEX idx_chat_message_tags_created_by ON chat_message_tags(created_by);

-- Create trigger for updated_at timestamp
CREATE TRIGGER update_chat_message_tags_timestamp BEFORE UPDATE ON chat_message_tags
FOR EACH ROW EXECUTE FUNCTION update_timestamp();

-- Add comments to table and columns
COMMENT ON TABLE chat_message_tags IS 'Stores tags for chat messages';
COMMENT ON COLUMN chat_message_tags.message_id IS 'Reference to the chat message';
COMMENT ON COLUMN chat_message_tags.tag IS 'Tag text';
COMMENT ON COLUMN chat_message_tags.created_by IS 'User who created the tag';