-- Migration: Add encryption fields to documents (existing table) and signable_documents (document signing system) tables
-- Depends on: add_document_signing_tables.sql
-- This migration adds fields for tracking encryption status, method, and key information

-- Set the search path to the appropriate schema
SET search_path TO site;

-- Function to log migration progress
CREATE OR <PERSON><PERSON>LACE FUNCTION log_migration_step(step_name TEXT)
RETURNS VOID AS $$
BEGIN
  RAISE NOTICE 'Migration step: %', step_name;
  RETURN;
END;
$$ LANGUAGE plpgsql;

BEGIN;

-- Add encryption fields to documents table (existing table for general document storage)
SELECT log_migration_step('Adding encryption fields to documents table');

ALTER TABLE documents
ADD COLUMN is_encrypted BOOLEAN NOT NULL DEFAULT FALSE,
ADD COLUMN encryption_method VARCHAR(50) NULL,
ADD COLUMN encryption_key_id VARCHAR(255) NULL,
ADD COLUMN encryption_iv VARCHAR(255) NULL;

-- Add encryption fields to signable_documents table (for document signing system)
SELECT log_migration_step('Adding encryption fields to signable_documents table');

ALTER TABLE signable_documents
ADD COLUMN is_encrypted BOOLEAN NOT NULL DEFAULT FALSE,
ADD COLUMN encryption_method VARCHAR(50) NULL,
ADD COLUMN encryption_key_id VARCHAR(255) NULL,
ADD COLUMN encryption_iv VARCHAR(255) NULL;

-- Add indexes for better query performance
SELECT log_migration_step('Adding indexes for encryption fields');

CREATE INDEX idx_documents_is_encrypted ON documents(is_encrypted);
CREATE INDEX idx_signable_documents_is_encrypted ON signable_documents(is_encrypted);


COMMIT;

-- Add comments to explain the purpose of the new columns
-- Comments for the existing documents table
COMMENT ON COLUMN documents.is_encrypted IS 'Indicates whether the document is encrypted';
COMMENT ON COLUMN documents.encryption_method IS 'The encryption method used (e.g., AES-256-CBC)';
COMMENT ON COLUMN documents.encryption_key_id IS 'Identifier for the encryption key used';
COMMENT ON COLUMN documents.encryption_iv IS 'Initialization vector used for encryption';

-- Comments for the document signing system table
COMMENT ON COLUMN signable_documents.is_encrypted IS 'Indicates whether the document is encrypted';
COMMENT ON COLUMN signable_documents.encryption_method IS 'The encryption method used (e.g., AES-256-CBC)';
COMMENT ON COLUMN signable_documents.encryption_key_id IS 'Identifier for the encryption key used';
COMMENT ON COLUMN signable_documents.encryption_iv IS 'Initialization vector used for encryption';
