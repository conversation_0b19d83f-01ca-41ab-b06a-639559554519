-- Migration: Add document storage for invoices and additional farm contact information
-- Depends on:

SET search_path TO site;

-- Check if document_url column exists in invoices table
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1
    FROM information_schema.columns
    WHERE table_schema = 'site'
    AND table_name = 'invoices'
    AND column_name = 'document_url'
  ) THEN
    -- Add document_url column to invoices table
    ALTER TABLE invoices
    ADD COLUMN document_url TEXT NULL,
    ADD COLUMN document_name TEXT NULL,
    ADD COLUMN document_type TEXT NULL,
    ADD COLUMN document_size INTEGER NULL,
    ADD COLUMN document_uploaded_at TIMESTAMP WITH TIME ZONE NULL,
    ADD COLUMN document_uploaded_by UUID NULL REFERENCES users(id) ON DELETE SET NULL;

    -- Add comment for the new columns
    COMMENT ON COLUMN invoices.document_url IS 'URL to the uploaded document for this invoice';
    COMMENT ON COLUMN invoices.document_name IS 'Original filename of the uploaded document';
    COMMENT ON COLUMN invoices.document_type IS 'MIME type of the uploaded document (e.g., application/pdf, text/plain)';
    COMMENT ON COLUMN invoices.document_size IS 'Size of the uploaded document in bytes';
    COMMENT ON COLUMN invoices.document_uploaded_at IS 'When the document was uploaded';
    COMMENT ON COLUMN invoices.document_uploaded_by IS 'User who uploaded the document';
  END IF;
END $$;

-- Check if phone column exists in farms table
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1
    FROM information_schema.columns
    WHERE table_schema = 'site'
    AND table_name = 'farms'
    AND column_name = 'phone'
  ) THEN
    -- Add phone and contact information columns to farms table
    ALTER TABLE farms
    ADD COLUMN phone VARCHAR(50) NULL,
    ADD COLUMN primary_contact_name VARCHAR(255) NULL,
    ADD COLUMN primary_contact_email VARCHAR(255) NULL,
    ADD COLUMN primary_contact_phone VARCHAR(50) NULL,
    ADD COLUMN payment_terms TEXT NULL;

    -- Add comment for the new columns
    COMMENT ON COLUMN farms.phone IS 'Main phone number for the farm';
    COMMENT ON COLUMN farms.primary_contact_name IS 'Name of the primary contact person for the farm';
    COMMENT ON COLUMN farms.primary_contact_email IS 'Email of the primary contact person for the farm';
    COMMENT ON COLUMN farms.primary_contact_phone IS 'Phone number of the primary contact person for the farm';
    COMMENT ON COLUMN farms.payment_terms IS 'Default payment terms for invoices from this farm';
  END IF;
END $$;
