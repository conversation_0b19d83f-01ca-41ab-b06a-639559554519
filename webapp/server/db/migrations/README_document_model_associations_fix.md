# Document Model Associations Fix

## Overview

This migration fixes an issue with duplicate associations in document-related models. The problem was that associations were defined both in individual model files and in the central `associations.js` file, which could lead to duplicate associations at runtime.

The affected models are:
- `DocumentSigner.js`
- `DocumentSignature.js`
- `DocumentField.js`
- `DocumentAuditLog.js`

## Files

This migration consists of two files:

1. `fix_document_model_associations.js` - A JavaScript file that modifies the model files to remove duplicate associations
2. `fix_document_model_associations.sql` - A SQL file that records the migration in the database_migrations table

## How to Apply the Migration

To apply this migration, follow these steps:

1. Run the JavaScript migration file:

```bash
node server/db/migrations/fix_document_model_associations.js
```

2. Run the SQL migration file:

```bash
psql -U your_username -d your_database -f server/db/migrations/fix_document_model_associations.sql
```

Or you can use the database migration script:

```bash
node server/scripts/run-migration.js fix_document_model_associations.sql
```

## Dependencies

This migration depends on:
- `add_document_signing_tables.sql` - The migration that created the document signing tables

## Verification

After running the migration, you can verify that the changes were applied correctly by:

1. Checking that the model files no longer contain the duplicate associations
2. Verifying that the application still works correctly with document-related functionality
3. Confirming that the migration was recorded in the database_migrations table:

```sql
SELECT * FROM site.database_migrations WHERE name = 'Fix duplicate associations in document-related models';
```

## Technical Details

The migration removes the following associations from the model files:

### DocumentSigner.js
```javascript
DocumentSigner.belongsTo(SignableDocument, { foreignKey: 'document_id', as: 'signerDocument' });
```

### DocumentSignature.js
```javascript
DocumentSignature.belongsTo(SignableDocument, { foreignKey: 'document_id', as: 'signatureDocument' });
DocumentSignature.belongsTo(DocumentSigner, { foreignKey: 'signer_id', as: 'signer' });
```

### DocumentField.js
```javascript
DocumentField.belongsTo(SignableDocument, { foreignKey: 'document_id', as: 'fieldDocument' });
DocumentField.belongsTo(DocumentSigner, { foreignKey: 'signer_id', as: 'signer' });
```

### DocumentAuditLog.js
```javascript
DocumentAuditLog.belongsTo(SignableDocument, { foreignKey: 'document_id', as: 'auditLogDocument' });
DocumentAuditLog.belongsTo(DocumentSigner, { foreignKey: 'signer_id', as: 'signer' });
DocumentAuditLog.belongsTo(User, { foreignKey: 'user_id', as: 'user' });
```

These associations are already defined in the central `associations.js` file, so they don't need to be duplicated in the individual model files.