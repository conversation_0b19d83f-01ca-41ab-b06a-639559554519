-- Migration: Add recurring invoices table for auto-generating invoices on a schedule
-- Depends on: add_schema_migrations_table.sql

SET search_path TO site;

-- Create the recurring_invoices table
CREATE TABLE IF NOT EXISTS recurring_invoices (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  invoice_id UUID NOT NULL,
  frequency VARCHAR(50) NOT NULL,
  start_date DATE NOT NULL,
  end_date DATE,
  day_of_month INTEGER,
  day_of_week INTEGER,
  week_of_month INTEGER,
  month_of_year INTEGER,
  last_generated_date DATE,
  next_due_date DATE NOT NULL,
  auto_send BOOLEAN DEFAULT FALSE,
  additional_recipient_emails TEXT[],
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  FOREIGN KEY (invoice_id) REFERENCES invoices(id) ON DELETE CASCADE
);

-- Add index for faster lookups
CREATE INDEX IF NOT EXISTS idx_recurring_invoices_invoice_id ON recurring_invoices(invoice_id);
CREATE INDEX IF NOT EXISTS idx_recurring_invoices_next_due_date ON recurring_invoices(next_due_date);

-- Add comment to explain the table
COMMENT ON TABLE recurring_invoices IS 'Stores configuration for recurring invoices that are automatically generated on a schedule';
COMMENT ON COLUMN recurring_invoices.frequency IS 'Frequency of recurrence (daily, weekly, monthly, quarterly, yearly)';
COMMENT ON COLUMN recurring_invoices.start_date IS 'Date when the recurring schedule starts';
COMMENT ON COLUMN recurring_invoices.end_date IS 'Optional date when the recurring schedule ends';
COMMENT ON COLUMN recurring_invoices.day_of_month IS 'For monthly/yearly recurrence, the day of the month to generate the invoice';
COMMENT ON COLUMN recurring_invoices.day_of_week IS 'For weekly recurrence, the day of the week to generate the invoice (0=Sunday, 6=Saturday)';
COMMENT ON COLUMN recurring_invoices.week_of_month IS 'For monthly recurrence, the week of the month to generate the invoice';
COMMENT ON COLUMN recurring_invoices.month_of_year IS 'For yearly recurrence, the month to generate the invoice (1=January, 12=December)';
COMMENT ON COLUMN recurring_invoices.last_generated_date IS 'The date when the last invoice was generated';
COMMENT ON COLUMN recurring_invoices.next_due_date IS 'The next date when an invoice should be generated';
COMMENT ON COLUMN recurring_invoices.auto_send IS 'Whether to automatically send the invoice when generated';
COMMENT ON COLUMN recurring_invoices.additional_recipient_emails IS 'Additional email addresses to send the invoice to';