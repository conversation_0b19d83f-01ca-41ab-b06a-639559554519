-- Migration: Add customer_pickup type to pickups table
-- This migration adds a new pickup_type value 'customer_pickup' to the pickups table

-- Set the search path to the site schema
SET search_path TO site;

-- Update the pickup_type enum constraint to include 'customer_pickup'
ALTER TABLE site.pickups DROP CONSTRAINT IF EXISTS pickups_pickup_type_check;
ALTER TABLE site.pickups ADD CONSTRAINT pickups_pickup_type_check 
  CHECK (pickup_type IN ('product', 'equipment', 'return', 'other', 'customer_pickup'));

-- Update the comment on the pickup_type column
COMMENT ON COLUMN site.pickups.pickup_type IS 'Type of pickup: product, equipment, return, customer_pickup, or other';

-- Verify the constraint was updated
DO $$
DECLARE
    constraint_exists BOOLEAN;
BEGIN
    SELECT EXISTS (
        SELECT 1 
        FROM information_schema.constraint_column_usage 
        WHERE table_schema = 'site' 
        AND table_name = 'pickups'
        AND constraint_name = 'pickups_pickup_type_check'
    ) INTO constraint_exists;

    IF constraint_exists THEN
        RAISE NOTICE 'The pickups_pickup_type_check constraint was successfully updated.';
    ELSE
        RAISE EXCEPTION 'Failed to update the pickups_pickup_type_check constraint.';
    END IF;
END $$;