-- Migration: Add role_id and is_inherited fields to document_permissions table
-- Depends on:

SET search_path TO site;

-- Add role_id column to document_permissions table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_schema = 'site'
        AND table_name = 'document_permissions'
        AND column_name = 'role_id'
    ) THEN
        ALTER TABLE site.document_permissions ADD COLUMN role_id UUID REFERENCES site.roles(id);
        COMMENT ON COLUMN site.document_permissions.role_id IS 'Reference to the role this permission applies to (NULL for user-specific permissions)';
    END IF;

    -- Add is_inherited column to document_permissions table if it doesn't exist
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_schema = 'site'
        AND table_name = 'document_permissions'
        AND column_name = 'is_inherited'
    ) THEN
        ALTER TABLE site.document_permissions ADD COLUMN is_inherited BOOLEAN DEFAULT FALSE;
        COMMENT ON COLUMN site.document_permissions.is_inherited IS 'Whether this permission was inherited from a parent folder';
    END IF;

    -- Make user_id nullable (since we can now have role-based permissions)
    ALTER TABLE site.document_permissions ALTER COLUMN user_id DROP NOT NULL;

    -- Add constraint to ensure either user_id or role_id is set
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.constraint_column_usage
        WHERE table_schema = 'site'
        AND table_name = 'document_permissions'
        AND constraint_name = 'document_permissions_user_or_role_check'
    ) THEN
        ALTER TABLE site.document_permissions ADD CONSTRAINT document_permissions_user_or_role_check
            CHECK (
                (user_id IS NOT NULL AND role_id IS NULL) OR
                (user_id IS NULL AND role_id IS NOT NULL)
            );
    END IF;

    -- Add index on role_id
    IF NOT EXISTS (
        SELECT 1
        FROM pg_indexes
        WHERE schemaname = 'site'
        AND tablename = 'document_permissions'
        AND indexname = 'document_permissions_role_idx'
    ) THEN
        CREATE INDEX document_permissions_role_idx ON site.document_permissions(role_id);
    END IF;

    -- Add index on is_inherited
    IF NOT EXISTS (
        SELECT 1
        FROM pg_indexes
        WHERE schemaname = 'site'
        AND tablename = 'document_permissions'
        AND indexname = 'document_permissions_inherited_idx'
    ) THEN
        CREATE INDEX document_permissions_inherited_idx ON site.document_permissions(is_inherited);
    END IF;

    RAISE NOTICE 'Successfully added role_id and is_inherited columns to document_permissions table';
END $$;

-- Record the migration in the database_migrations table if it exists
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'site' AND table_name = 'database_migrations') THEN
        INSERT INTO site.database_migrations (id, name, file_path, "order", applied_at, created_at, updated_at, status, dependencies)
        VALUES (
            gen_random_uuid(),
            'Add role_id and is_inherited to document_permissions',
            'webapp/server/db/migrations/add_role_to_document_permissions.sql',
            (SELECT COALESCE(MAX("order"), 0) + 1 FROM site.database_migrations),
            NOW(),
            NOW(),
            NOW(),
            'applied',
            ARRAY['add_farm_id_to_document_permissions.sql']
        );
    END IF;
END $$;