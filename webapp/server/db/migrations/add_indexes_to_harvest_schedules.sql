-- Add indexes to harvest_schedules table for faster queries
-- This migration adds indexes to commonly queried columns in the harvest_schedules table

-- Set the search path to the appropriate schema
SET search_path TO site;

-- Add index for farm_id
CREATE INDEX IF NOT EXISTS harvest_schedules_farm_id_idx ON site.harvest_schedules(farm_id);

-- Add index for field_id
CREATE INDEX IF NOT EXISTS harvest_schedules_field_id_idx ON site.harvest_schedules(field_id);

-- Add index for crop_id
CREATE INDEX IF NOT EXISTS harvest_schedules_crop_id_idx ON site.harvest_schedules(crop_id);

-- Add index for scheduled_date
CREATE INDEX IF NOT EXISTS harvest_schedules_scheduled_date_idx ON site.harvest_schedules(scheduled_date);

-- Add index for status
CREATE INDEX IF NOT EXISTS harvest_schedules_status_idx ON site.harvest_schedules(status);

-- Add index for weather_dependent
CREATE INDEX IF NOT EXISTS harvest_schedules_weather_dependent_idx ON site.harvest_schedules(weather_dependent);

-- Add composite index for farm_id and scheduled_date for faster lookups of farm schedules by date
CREATE INDEX IF NOT EXISTS harvest_schedules_farm_id_scheduled_date_idx ON site.harvest_schedules(farm_id, scheduled_date);

-- Add composite index for field_id and scheduled_date for faster lookups of field schedules by date
CREATE INDEX IF NOT EXISTS harvest_schedules_field_id_scheduled_date_idx ON site.harvest_schedules(field_id, scheduled_date);