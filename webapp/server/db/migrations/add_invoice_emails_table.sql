-- Migration: Add invoice_emails table to track sent invoices
-- Depends on:

SET search_path TO site;

-- Create the invoice_emails table
CREATE TABLE IF NOT EXISTS invoice_emails (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  invoice_id UUID NOT NULL REFERENCES invoices(id) ON DELETE CASCADE,
  sent_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  sent_by_user_id UUID REFERENCES users(id) ON DELETE SET NULL,
  recipient_email VARCHAR(255) NOT NULL,
  status VARCHAR(50) NOT NULL DEFAULT 'sent',
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Add indexes
CREATE INDEX idx_invoice_emails_invoice_id ON invoice_emails(invoice_id);
CREATE INDEX idx_invoice_emails_sent_by_user_id ON invoice_emails(sent_by_user_id);

-- Add comments
COMMENT ON TABLE invoice_emails IS 'Tracks when invoices are sent to customers via email';
COMMENT ON COLUMN invoice_emails.invoice_id IS 'The invoice that was sent';
COMMENT ON COLUMN invoice_emails.sent_at IS 'When the invoice was sent';
COMMENT ON COLUMN invoice_emails.sent_by_user_id IS 'The user who sent the invoice';
COMMENT ON COLUMN invoice_emails.recipient_email IS 'The email address the invoice was sent to';
COMMENT ON COLUMN invoice_emails.status IS 'The status of the email (sent, delivered, opened, etc.)';