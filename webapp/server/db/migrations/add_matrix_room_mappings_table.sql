-- Migration: Add matrix_room_mappings table to store mappings between NxtAcre conversations and Matrix rooms
-- Depends on:

-- Set search path to site schema
SET search_path TO site;

-- Create matrix_room_mappings table
CREATE TABLE IF NOT EXISTS matrix_room_mappings (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  conversation_id UUID NOT NULL REFERENCES chat_conversations(id) ON DELETE CASCADE,
  matrix_room_id TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  UNIQUE(conversation_id),
  UNIQUE(matrix_room_id)
);

-- Add indexes
CREATE INDEX IF NOT EXISTS idx_matrix_room_mappings_conversation_id ON matrix_room_mappings(conversation_id);
CREATE INDEX IF NOT EXISTS idx_matrix_room_mappings_matrix_room_id ON matrix_room_mappings(matrix_room_id);

-- Add comment
COMMENT ON TABLE matrix_room_mappings IS 'Stores mappings between NxtAcre conversations and Matrix rooms';

-- <PERSON><PERSON> function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_matrix_room_mappings_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to update updated_at timestamp
DROP TRIGGER IF EXISTS update_matrix_room_mappings_updated_at ON matrix_room_mappings;
CREATE TRIGGER update_matrix_room_mappings_updated_at
BEFORE UPDATE ON matrix_room_mappings
FOR EACH ROW
EXECUTE FUNCTION update_matrix_room_mappings_updated_at();