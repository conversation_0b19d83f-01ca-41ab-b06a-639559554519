-- Migration: Add farm feature toggles table for marketplace settings
-- Depends on: add_farm_fulfillment_options_table.sql

SET search_path TO site;

CREATE TABLE farm_feature_toggles (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  farm_id UUID NOT NULL REFERENCES farms(id) ON DELETE CASCADE,
  marketplace_enabled BOOLEAN NOT NULL DEFAULT false,
  customer_orders_enabled BOOLEAN NOT NULL DEFAULT false,
  delivery_scheduling_enabled BOOLEAN NOT NULL DEFAULT false,
  driver_tracking_enabled BOOLEAN NOT NULL DEFAULT false,
  customer_messaging_enabled BOOLEAN NOT NULL DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

CREATE UNIQUE INDEX farm_feature_toggles_farm_id_idx ON farm_feature_toggles(farm_id);

COMMENT ON TABLE farm_feature_toggles IS 'Stores feature toggle settings for each farm';
COMMENT ON COLUMN farm_feature_toggles.marketplace_enabled IS 'Whether the farm is visible in the marketplace';
COMMENT ON COLUMN farm_feature_toggles.customer_orders_enabled IS 'Whether customers can submit order requests to the farm';
COMMENT ON COLUMN farm_feature_toggles.delivery_scheduling_enabled IS 'Whether delivery scheduling is enabled for the farm';
COMMENT ON COLUMN farm_feature_toggles.driver_tracking_enabled IS 'Whether driver location tracking is enabled for the farm';
COMMENT ON COLUMN farm_feature_toggles.customer_messaging_enabled IS 'Whether customers can send messages to the farm';

-- Insert default settings for existing farms
INSERT INTO farm_feature_toggles (farm_id, marketplace_enabled, customer_orders_enabled, delivery_scheduling_enabled, driver_tracking_enabled, customer_messaging_enabled)
SELECT id, false, false, false, false, false
FROM farms
WHERE NOT EXISTS (
  SELECT 1 FROM farm_feature_toggles WHERE farm_feature_toggles.farm_id = farms.id
);