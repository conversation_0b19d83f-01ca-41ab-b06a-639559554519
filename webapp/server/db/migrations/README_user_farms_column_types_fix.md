# User Farms Column Types Fix

## Overview

This migration fixes data type inconsistencies in the `user_farms` table. The issue was that the `created_at`, `updated_at`, and `permissions` columns were defined with incorrect data types in the database schema, which did not match the model definition.

## Issues Fixed

1. `created_at` and `updated_at` columns were defined as `varchar` in the database schema, but as `DataTypes.DATE` in the model definition.
2. `permissions` column was defined as `varchar` in the database schema, but as `DataTypes.JSON` in the model definition.

These inconsistencies could lead to issues with data storage and retrieval, as the model expects dates and JSON objects but the database stores strings.

## Migration Details

The migration performs the following actions:

1. Creates a backup of the `user_farms` table
2. Alters the `created_at` and `updated_at` columns to be `timestamp with time zone`
3. Alters the `permissions` column to be `jsonb`
4. Records the migration in the `database_migrations` table if it exists

## How to Apply the Migration

To apply this migration, run the following command:

```bash
psql -U your_username -d your_database -f server/db/migrations/fix_user_farms_column_types.sql
```

Or you can use the database migration script:

```bash
node server/scripts/run-migration.js fix_user_farms_column_types.sql
```

## Verification

After running the migration, you can verify that the changes were applied correctly by:

1. Checking the column types in the database:

```sql
SELECT column_name, data_type 
FROM information_schema.columns 
WHERE table_name = 'user_farms' 
AND column_name IN ('created_at', 'updated_at', 'permissions');
```

2. Verifying that the application still works correctly with the `user_farms` table.

## Related Files

- `webapp/server/models/UserFarm.js` - The model definition that expects `created_at` and `updated_at` to be dates and `permissions` to be JSON.
- `webapp/server/db/migrations/fix_user_farms_column_types.sql` - The migration file that fixes the data type inconsistencies.