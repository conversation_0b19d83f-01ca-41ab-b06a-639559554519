-- Migration to add crop_diseases table
-- This table stores disease predictions for crops

-- Set the search path to the appropriate schema
SET search_path TO site;

-- Step 1: Create crop_diseases table

CREATE TABLE IF NOT EXISTS site.crop_diseases (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    farm_id UUID NOT NULL REFERENCES site.farms(id) ON DELETE CASCADE,
    crop_id UUID NOT NULL REFERENCES site.crops(id) ON DELETE CASCADE,
    field_id UUID REFERENCES site.fields(id) ON DELETE SET NULL,
    disease_name VARCHAR(255) NOT NULL,
    probability DECIMAL(5, 4) NOT NULL,
    recommended_actions TEXT,
    risk_factors JSONB,
    prediction_date DATE NOT NULL DEFAULT CURRENT_DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Add comments to the table and columns
COMMENT ON TABLE site.crop_diseases IS 'Stores disease predictions for crops';
COMMENT ON COLUMN site.crop_diseases.id IS 'Unique identifier for the disease prediction';
COMMENT ON COLUMN site.crop_diseases.farm_id IS 'ID of the farm this prediction belongs to';
COMMENT ON COLUMN site.crop_diseases.crop_id IS 'ID of the crop this prediction is for';
COMMENT ON COLUMN site.crop_diseases.field_id IS 'ID of the field this prediction is for (optional)';
COMMENT ON COLUMN site.crop_diseases.disease_name IS 'Name of the potential disease';
COMMENT ON COLUMN site.crop_diseases.probability IS 'Probability of the disease occurring (0-1)';
COMMENT ON COLUMN site.crop_diseases.recommended_actions IS 'Recommended actions to prevent or treat the disease';
COMMENT ON COLUMN site.crop_diseases.risk_factors IS 'Factors that contribute to the disease risk';
COMMENT ON COLUMN site.crop_diseases.prediction_date IS 'Date when the prediction was made';

-- Step 2: Create indexes for better performance

CREATE INDEX IF NOT EXISTS idx_crop_diseases_farm_id ON site.crop_diseases(farm_id);
CREATE INDEX IF NOT EXISTS idx_crop_diseases_crop_id ON site.crop_diseases(crop_id);
CREATE INDEX IF NOT EXISTS idx_crop_diseases_field_id ON site.crop_diseases(field_id);
CREATE INDEX IF NOT EXISTS idx_crop_diseases_prediction_date ON site.crop_diseases(prediction_date);
CREATE INDEX IF NOT EXISTS idx_crop_diseases_disease_name ON site.crop_diseases(disease_name);
CREATE INDEX IF NOT EXISTS idx_crop_diseases_probability ON site.crop_diseases(probability);

-- Step 3: Create trigger for updated_at timestamp

CREATE TRIGGER update_crop_diseases_timestamp
BEFORE UPDATE ON site.crop_diseases
FOR EACH ROW
EXECUTE FUNCTION update_timestamp();

