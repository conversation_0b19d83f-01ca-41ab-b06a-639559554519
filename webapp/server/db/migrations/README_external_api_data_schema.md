# External API Data Schema

This document describes the database schema for storing external API data in an organized way. This schema provides easier caching of data, more reliable results, and enables analysis of API data.

## Tables

The schema consists of the following tables:

### api_providers

Stores information about external API providers.

- **id**: UUID - Unique identifier for the API provider
- **name**: VARCHAR(100) - Name of the API provider (e.g., National Weather Service, OpenWeatherMap)
- **category**: VARCHAR(50) - Category of the API (e.g., Weather, Financial, Agricultural)
- **base_url**: VARCHAR(255) - Base URL for the API
- **requires_key**: BOOLEAN - Whether the API requires an API key
- **description**: TEXT - Description of the API provider and its services
- **created_at**: TIMESTAMP - Time when the record was created
- **updated_at**: TIMESTAMP - Time when the record was last updated

### api_endpoints

Stores information about specific API endpoints.

- **id**: UUID - Unique identifier for the API endpoint
- **provider_id**: UUID - ID of the API provider this endpoint belongs to
- **name**: VARCHAR(100) - Name of the API endpoint (e.g., Current Weather, Forecast)
- **path**: VARCHAR(255) - Path for the API endpoint
- **method**: VARCHAR(10) - HTTP method for the API endpoint (GET, POST, etc.)
- **description**: TEXT - Description of the API endpoint and its purpose
- **request_format**: JSONB - JSON schema for the request format
- **response_format**: JSONB - JSON schema for the response format
- **cache_duration_seconds**: INTEGER - Default cache duration in seconds for this endpoint
- **created_at**: TIMESTAMP - Time when the record was created
- **updated_at**: TIMESTAMP - Time when the record was last updated

### api_requests

Stores information about API requests and responses.

- **id**: UUID - Unique identifier for the API request
- **endpoint_id**: UUID - ID of the API endpoint this request was made to
- **farm_id**: UUID - ID of the farm this request is related to (if applicable)
- **field_id**: UUID - ID of the field this request is related to (if applicable)
- **user_id**: UUID - ID of the user who made the request (if applicable)
- **request_params**: JSONB - Query parameters for the request
- **request_headers**: JSONB - Headers for the request
- **request_body**: JSONB - Body of the request (for POST, PUT, etc.)
- **response_status**: INTEGER - HTTP status code of the response
- **response_headers**: JSONB - Headers of the response
- **response_body**: JSONB - Body of the response
- **error_message**: TEXT - Error message if the request failed
- **request_time**: TIMESTAMP - Time when the request was made
- **response_time**: TIMESTAMP - Time when the response was received
- **duration_ms**: INTEGER - Duration of the request in milliseconds
- **cache_hit**: BOOLEAN - Whether the response was served from cache
- **expires_at**: TIMESTAMP - Time when the cached response expires
- **created_at**: TIMESTAMP - Time when the record was created
- **updated_at**: TIMESTAMP - Time when the record was last updated

### api_cache

Stores cached API responses.

- **id**: UUID - Unique identifier for the cached response
- **endpoint_id**: UUID - ID of the API endpoint this cache is for
- **cache_key**: VARCHAR(255) - Unique key for the cached response
- **request_params**: JSONB - Query parameters for the request
- **response_body**: JSONB - Body of the response
- **created_at**: TIMESTAMP - Time when the cache entry was created
- **expires_at**: TIMESTAMP - Time when the cache entry expires
- **last_accessed_at**: TIMESTAMP - Time when the cache entry was last accessed

### api_analytics

Stores analytics data for API usage.

- **id**: UUID - Unique identifier for the analytics entry
- **provider_id**: UUID - ID of the API provider
- **endpoint_id**: UUID - ID of the API endpoint (if applicable)
- **date**: DATE - Date for the analytics data
- **request_count**: INTEGER - Number of requests made
- **error_count**: INTEGER - Number of requests that resulted in errors
- **cache_hit_count**: INTEGER - Number of requests served from cache
- **avg_response_time_ms**: INTEGER - Average response time in milliseconds
- **created_at**: TIMESTAMP - Time when the record was created
- **updated_at**: TIMESTAMP - Time when the record was last updated

## Usage

The schema is designed to be used with the `apiDataService.js` utility service, which provides functions for storing API requests, retrieving cached responses, and updating analytics data.

### Making API Requests with Caching

```javascript
import { makeApiRequestWithCaching, getApiProviderByName, getApiEndpointByName } from '../utils/apiDataService.js';

// Get the API provider and endpoint
const weatherProvider = await getApiProviderByName('National Weather Service');
const forecastEndpoint = await getApiEndpointByName(weatherProvider.id, 'Forecast');

// Make the API request with caching
const response = await makeApiRequestWithCaching(
  forecastEndpoint.id,
  {
    farmId: '123e4567-e89b-12d3-a456-426614174000',
    params: { lat: 37.7749, lon: -122.4194 }
  },
  async () => {
    // This function is only called if the response is not in the cache
    return await axios.get(`https://api.weather.gov/points/37.7749,-122.4194`);
  }
);
```

### Manually Caching API Responses

```javascript
import { cacheApiResponse } from '../utils/apiDataService.js';

// Cache an API response
await cacheApiResponse(
  forecastEndpoint.id,
  { lat: 37.7749, lon: -122.4194 },
  responseData,
  3600 // Cache for 1 hour
);
```

### Retrieving Cached Responses

```javascript
import { getCachedResponse } from '../utils/apiDataService.js';

// Get a cached response
const cachedResponse = await getCachedResponse(
  forecastEndpoint.id,
  { lat: 37.7749, lon: -122.4194 }
);

if (cachedResponse) {
  // Use the cached response
} else {
  // Make a new API request
}
```

### Cleaning Up Expired Cache Entries

```javascript
import { cleanupExpiredCache } from '../utils/apiDataService.js';

// Clean up expired cache entries older than 7 days
const removedCount = await cleanupExpiredCache(7);
console.log(`Removed ${removedCount} expired cache entries`);
```

## Benefits

This schema provides several benefits:

1. **Caching**: Reduces the number of API requests made to external services, which can save on API usage costs and improve performance.
2. **Reliability**: Cached responses can be served even if the external API is temporarily unavailable.
3. **Analytics**: Provides insights into API usage, performance, and error rates.
4. **Organization**: Stores API data in a structured way, making it easier to manage and analyze.
5. **Debugging**: Stores request and response data, which can be useful for debugging issues with external APIs.

## Implementation

The schema is implemented using Sequelize models and a migration file. The models are:

- `ApiProvider`
- `ApiEndpoint`
- `ApiRequest`
- `ApiCache`
- `ApiAnalytics`

These models are defined in the `webapp/server/models` directory and are exported from the `index.js` file.

The migration file is `webapp/server/db/migrations/add_external_api_data_tables.sql`.

The utility service is `webapp/server/utils/apiDataService.js`.