-- Migration: Add recipient_farm_id to document_shares table
-- Depends on: add_one_time_use_to_document_shares.sql

SET search_path TO site;

-- Add recipient_farm_id to document_shares table to allow farm-to-farm document sharing
ALTER TABLE document_shares
ADD COLUMN recipient_farm_id UUID NULL,
ADD CONSTRAINT fk_document_shares_recipient_farm FOREIGN KEY (recipient_farm_id) REFERENCES farms(id) ON DELETE CASCADE;

-- Add index for faster lookups
CREATE INDEX idx_document_shares_recipient_farm_id ON document_shares(recipient_farm_id);

-- Add comment to explain the purpose of the column
COMMENT ON COLUMN document_shares.recipient_farm_id IS 'The farm that the document is being shared with (for farm-to-farm sharing)';