-- Migration: Fix invoice_audit_logs foreign key constraint to add CASCADE option
-- Depends on:

-- Set search path to site schema
SET search_path TO site;

-- Begin transaction
BEGIN;

-- Drop the existing constraint
ALTER TABLE invoice_audit_logs DROP CONSTRAINT IF EXISTS invoice_audit_logs_invoice_id_fkey;

-- Recreate the constraint with ON DELETE CASCADE
ALTER TABLE invoice_audit_logs 
ADD CONSTRAINT invoice_audit_logs_invoice_id_fkey 
FOREIGN KEY (invoice_id) 
REFERENCES invoices(id) 
ON DELETE CASCADE;

-- Also check and fix user_id and farm_id foreign keys if they exist
-- For user_id
ALTER TABLE invoice_audit_logs DROP CONSTRAINT IF EXISTS invoice_audit_logs_user_id_fkey;
ALTER TABLE invoice_audit_logs 
ADD CONSTRAINT invoice_audit_logs_user_id_fkey 
FOREIGN KEY (user_id) 
REFERENCES users(id) 
ON DELETE SET NULL;

-- For farm_id
ALTER TABLE invoice_audit_logs DROP CONSTRAINT IF EXISTS invoice_audit_logs_farm_id_fkey;
ALTER TABLE invoice_audit_logs 
ADD CONSTRAINT invoice_audit_logs_farm_id_fkey 
FOREIGN KEY (farm_id) 
REFERENCES farms(id) 
ON DELETE CASCADE;

-- Commit the transaction
COMMIT;
