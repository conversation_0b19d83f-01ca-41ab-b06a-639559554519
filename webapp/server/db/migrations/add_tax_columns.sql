-- Migration to add tax_rate column to farms table and is_tax_exempt column to products and customers tables

-- Set the search path to the appropriate schema
SET search_path TO site;

-- Get the schema name from environment variable or use 'site' as default
DO $$
DECLARE
    schema_name TEXT;
BEGIN
    -- Get the schema name from environment variable
    schema_name := current_setting('app.db_schema', true);

    -- If the environment variable is not set, use 'site' as default
    IF schema_name IS NULL THEN
        schema_name := 'site';
    END IF;

    -- Add tax_rate column to farms table if it doesn't exist
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_schema = schema_name
        AND table_name = 'farms'
        AND column_name = 'tax_rate'
    ) THEN
        EXECUTE format('ALTER TABLE %I.farms ADD COLUMN tax_rate DECIMAL(5, 2) DEFAULT NULL', schema_name);
        EXECUTE format('COMMENT ON COLUMN %I.farms.tax_rate IS ''Tax rate percentage for the farm state''', schema_name);
    END IF;

    -- Add is_tax_exempt column to products table if it doesn't exist
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_schema = schema_name
        AND table_name = 'products'
        AND column_name = 'is_tax_exempt'
    ) THEN
        EXECUTE format('ALTER TABLE %I.products ADD COLUMN is_tax_exempt BOOLEAN NOT NULL DEFAULT FALSE', schema_name);
        EXECUTE format('COMMENT ON COLUMN %I.products.is_tax_exempt IS ''Whether this product is exempt from taxes''', schema_name);
    END IF;

    -- Add is_tax_exempt column to customers table if it doesn't exist
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_schema = schema_name
        AND table_name = 'customers'
        AND column_name = 'is_tax_exempt'
    ) THEN
        EXECUTE format('ALTER TABLE %I.customers ADD COLUMN is_tax_exempt BOOLEAN NOT NULL DEFAULT FALSE', schema_name);
        EXECUTE format('COMMENT ON COLUMN %I.customers.is_tax_exempt IS ''Whether this customer is exempt from taxes''', schema_name);
    END IF;
END $$;