-- Allow NULL farm_id in role_permissions table
-- This script modifies the role_permissions table to allow NULL farm_id values for system-wide default permissions

-- Set the search path to the appropriate schema
-- This will be replaced with the actual schema name by the script
SET search_path TO site;

-- Modify the farm_id column to allow NULL values
ALTER TABLE role_permissions ALTER COLUMN farm_id DROP NOT NULL;

-- Add a comment to explain the purpose of NULL farm_id values
COMMENT ON COLUMN role_permissions.farm_id IS 'Farm ID (NULL for system-wide default permissions)';
