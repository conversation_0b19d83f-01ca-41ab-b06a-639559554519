-- Add missing columns to users table

-- Set the search path to the site schema
SET search_path TO site;

-- Add is_business_owner column
ALTER TABLE site.users ADD COLUMN IF NOT EXISTS is_business_owner BOOLEAN DEFAULT FALSE;
COMMENT ON COLUMN site.users.is_business_owner IS 'Whether the user owns a business listing (supplier or vet)';

-- Add is_approved column
ALTER TABLE site.users ADD COLUMN IF NOT EXISTS is_approved BOOLEAN DEFAULT FALSE;
COMMENT ON COLUMN site.users.is_approved IS 'Whether the business account has been approved by an admin';

-- Add subscription_plan_id column
ALTER TABLE site.users ADD COLUMN IF NOT EXISTS subscription_plan_id UUID REFERENCES site.subscription_plans(id);
COMMENT ON COLUMN site.users.subscription_plan_id IS 'Subscription plan for business owners';

-- Fix user_type column to match the model definition
-- First, create the enum type if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'user_type_enum') THEN
        CREATE TYPE user_type_enum AS ENUM ('farmer', 'supplier', 'vet', 'admin', 'accountant');
    END IF;
END$$;

-- Then update the column to use the enum type
-- Note: This is a complex operation that might require data migration
-- For now, we'll keep the VARCHAR type but add a check constraint to enforce valid values
ALTER TABLE site.users DROP CONSTRAINT IF EXISTS check_user_type;
ALTER TABLE site.users ADD CONSTRAINT check_user_type 
    CHECK (user_type IN ('farmer', 'supplier', 'vet', 'admin', 'accountant'));

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_users_subscription_plan_id ON site.users(subscription_plan_id);
CREATE INDEX IF NOT EXISTS idx_users_is_business_owner ON site.users(is_business_owner);
CREATE INDEX IF NOT EXISTS idx_users_is_approved ON site.users(is_approved);
