-- Set the search path to the appropriate schema
SET search_path TO site;

-- Check if document_folders table exists before trying to alter it
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables 
               WHERE table_schema = current_schema() 
               AND table_name = 'document_folders') THEN

        -- Check if tenant_id column exists
        IF EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_schema = current_schema()
                  AND table_name = 'document_folders' 
                  AND column_name = 'tenant_id') THEN

            -- Drop the tenant_id column
            ALTER TABLE document_folders DROP COLUMN tenant_id;
            
            -- Drop the index on tenant_id if it exists
            DROP INDEX IF EXISTS document_folders_tenant_id_idx;
            
            RAISE NOTICE 'tenant_id column dropped from document_folders table';
        ELSE
            RAISE NOTICE 'tenant_id column does not exist in document_folders table, no action needed';
        END IF;
    ELSE
        RAISE NOTICE 'document_folders table does not exist, skipping modifications';
    END IF;
END $$;