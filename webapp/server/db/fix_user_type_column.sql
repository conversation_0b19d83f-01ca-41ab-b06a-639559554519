-- Fix user_type column in users table
-- Set the search path to the appropriate schema
SET search_path TO site;

-- First, create the ENUM type if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'user_type_enum') THEN
        CREATE TYPE user_type_enum AS ENUM ('farmer', 'supplier', 'vet', 'admin', 'accountant');
    END IF;
END$$;

-- Check if the column exists with VARCHAR type
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_schema = current_schema()
               AND table_name = 'users' 
               AND column_name = 'user_type' 
               AND data_type = 'character varying') THEN
        
        -- Create a temporary column to preserve data
        ALTER TABLE users ADD COLUMN user_type_temp user_type_enum;
        
        -- Convert existing values to the enum type
        UPDATE users SET user_type_temp = user_type::user_type_enum;
        
        -- Drop the existing VARCHAR column
        ALTER TABLE users DROP COLUMN user_type;
        
        -- Rename the temporary column to user_type
        ALTER TABLE users RENAME COLUMN user_type_temp TO user_type;
        
        -- Add NOT NULL constraint and default value
        ALTER TABLE users ALTER COLUMN user_type SET NOT NULL;
        ALTER TABLE users ALTER COLUMN user_type SET DEFAULT 'farmer';
    END IF;
END$$;

-- Check if the column doesn't exist at all
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_schema = current_schema()
                  AND table_name = 'users' 
                  AND column_name = 'user_type') THEN
        
        -- Add the column with the ENUM type
        ALTER TABLE users ADD COLUMN user_type user_type_enum NOT NULL DEFAULT 'farmer';
    END IF;
END$$;

-- Add a comment to the column
COMMENT ON COLUMN users.user_type IS 'Type of user account';

-- Update existing users to have a default type if needed
UPDATE users SET user_type = 'farmer' WHERE user_type IS NULL;