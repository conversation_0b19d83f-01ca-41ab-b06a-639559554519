
-- Add user_type column to users table
-- Set the search path to the appropriate schema
SET search_path TO site;

-- First, create the ENUM type if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'user_type_enum') THEN
        CREATE TYPE user_type_enum AS ENUM ('farmer', 'supplier', 'vet', 'admin', 'accountant');
    END IF;
END$$;

-- Check if the column exists with VARCHAR type
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'users' 
               AND column_name = 'user_type' 
               AND data_type = 'character varying') THEN
        
        -- Drop the existing VARCHAR column
        ALTER TABLE users DROP COLUMN user_type;
    END IF;
END$$;

-- Then add the column with the ENUM type
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS user_type user_type_enum NOT NULL DEFAULT 'farmer';

-- Add a comment to the column
COMMENT ON COLUMN users.user_type IS 'Type of user account';

-- Update existing users to have a default type if needed
UPDATE users SET user_type = 'farmer' WHERE user_type IS NULL;
