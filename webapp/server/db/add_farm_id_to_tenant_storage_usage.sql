-- Check if tenant_storage_usage table exists before trying to alter it

-- Set the search path to the appropriate schema
SET search_path TO site;
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables 
               WHERE table_schema = current_schema() 
               AND table_name = 'tenant_storage_usage') THEN

        -- Add farm_id column to tenant_storage_usage table if it doesn't exist
        ALTER TABLE site.tenant_storage_usage ADD COLUMN IF NOT EXISTS farm_id UUID REFERENCES site.farms(id);

        -- Create index for better query performance
        CREATE INDEX IF NOT EXISTS tenant_storage_usage_farm_id_idx ON site.tenant_storage_usage(farm_id);

        -- Create a unique index on tenant_id and farm_id
        CREATE UNIQUE INDEX IF NOT EXISTS tenant_storage_usage_tenant_farm_idx ON site.tenant_storage_usage(tenant_id, farm_id) WHERE farm_id IS NOT NULL;

        -- Add comment explaining the purpose of this column
        COMMENT ON COLUMN site.tenant_storage_usage.farm_id IS 'Reference to the farm this storage usage belongs to';

        -- Update existing records to set farm_id based on tenant_id
        -- This assumes there's a relationship between tenants and farms
        -- Adjust the join condition as needed for your data model
        UPDATE site.tenant_storage_usage tsu
        SET farm_id = f.id
        FROM site.farms f
        WHERE tsu.tenant_id = f.tenant_id
        AND tsu.farm_id IS NULL;

        -- Remove the unique constraint on tenant_id if it exists
        ALTER TABLE site.tenant_storage_usage DROP CONSTRAINT IF EXISTS tenant_storage_usage_tenant_id_key;
    ELSE
        RAISE NOTICE 'tenant_storage_usage table does not exist, skipping modifications';
    END IF;
END $$;
