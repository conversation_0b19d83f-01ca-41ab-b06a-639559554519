-- Add subscription_plan_id column to farms table if it doesn't exist

-- Set the search path to the appropriate schema
SET search_path TO site;

ALTER TABLE site.farms ADD COLUMN IF NOT EXISTS subscription_plan_id UUID REFERENCES site.subscription_plans(id);

-- Add subscription_plan_id column to users table if it doesn't exist
ALTER TABLE site.users ADD COLUMN IF NOT EXISTS subscription_plan_id UUID REFERENCES site.subscription_plans(id);

-- Add indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_farms_subscription_plan_id ON site.farms(subscription_plan_id);
CREATE INDEX IF NOT EXISTS idx_users_subscription_plan_id ON site.users(subscription_plan_id);

-- Comment explaining the purpose of this migration
COMMENT ON COLUMN site.farms.subscription_plan_id IS 'Reference to the subscription plan for this farm';
COMMENT ON COLUMN site.users.subscription_plan_id IS 'Subscription plan for business owners';
