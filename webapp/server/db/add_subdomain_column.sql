-- Add subdomain column to farms table

-- Set the search path to the appropriate schema
SET search_path TO site;

ALTER TABLE site.farms ADD COLUMN IF NOT EXISTS subdomain VARCHAR(50);

-- Add unique constraint to subdomain column
ALTER TABLE site.farms ADD CONSTRAINT farms_subdomain_key UNIQUE (subdomain);

-- Update existing farms to have a default subdomain based on their name
-- This is a placeholder and should be executed with caution
-- It's recommended to manually set subdomains for existing farms
-- or implement a more sophisticated algorithm to generate unique subdomains
