-- Missing Tables for Farm Management Platform
-- Set the search path to the appropriate schema
SET search_path TO site;


-- Soil Management Tables
CREATE TABLE soil_samples (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    farm_id UUID REFERENCES farms(id) ON DELETE CASCADE,
    field_id UUID,  -- Will reference fields table once created
    sample_date DATE NOT NULL,
    location VARCHAR(255),
    depth VARCHAR(50),
    lab_name VA<PERSON>HAR(255),
    lab_reference VARCHAR(100),
    status VARCHAR(50) NOT NULL, -- pending, received, analyzed
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE soil_test_results (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    soil_sample_id UUID REFERENCES soil_samples(id) ON DELETE CASCADE,
    ph DECIMAL(4, 2),
    organic_matter DECIMAL(5, 2),
    nitrogen DECIMAL(5, 2),
    phosphorus DECIMAL(5, 2),
    potassium DECIMAL(5, 2),
    calcium DECIMAL(5, 2),
    magnesium DECIMAL(5, 2),
    sulfur DECIMAL(5, 2),
    zinc DECIMAL(5, 2),
    manganese DECIMAL(5, 2),
    copper DECIMAL(5, 2),
    iron DECIMAL(5, 2),
    boron DECIMAL(5, 2),
    cec DECIMAL(5, 2),
    base_saturation DECIMAL(5, 2),
    other_results JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE soil_amendments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    farm_id UUID REFERENCES farms(id) ON DELETE CASCADE,
    field_id UUID,  -- Will reference fields table once created
    amendment_date DATE NOT NULL,
    amendment_type VARCHAR(100) NOT NULL, -- lime, gypsum, compost, etc.
    quantity DECIMAL(10, 2),
    unit VARCHAR(50),
    cost_per_unit DECIMAL(10, 2),
    total_cost DECIMAL(10, 2),
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Equipment Maintenance Tables
CREATE TABLE maintenance_schedules (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    equipment_id UUID REFERENCES equipment(id) ON DELETE CASCADE,
    maintenance_type VARCHAR(100) NOT NULL, -- oil change, filter replacement, etc.
    frequency_type VARCHAR(50) NOT NULL, -- hours, miles, days, months
    frequency_value INTEGER NOT NULL,
    last_performed_date DATE,
    last_performed_hours DECIMAL(10, 2),
    next_due_date DATE,
    next_due_hours DECIMAL(10, 2),
    alert_threshold INTEGER, -- hours/days before due to alert
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE maintenance_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    equipment_id UUID REFERENCES equipment(id) ON DELETE CASCADE,
    maintenance_schedule_id UUID REFERENCES maintenance_schedules(id),
    service_date DATE NOT NULL,
    service_hours DECIMAL(10, 2),
    service_type VARCHAR(100) NOT NULL, -- scheduled maintenance, repair, etc.
    description TEXT NOT NULL,
    performed_by VARCHAR(255),
    parts_used TEXT,
    labor_hours DECIMAL(5, 2),
    labor_cost DECIMAL(10, 2),
    parts_cost DECIMAL(10, 2),
    total_cost DECIMAL(10, 2),
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Fields Table
CREATE TABLE fields (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    farm_id UUID REFERENCES farms(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    size DECIMAL(10, 2),
    size_unit VARCHAR(50) DEFAULT 'acres',
    field_type VARCHAR(100), -- cropland, pasture, orchard, etc.
    status VARCHAR(50) DEFAULT 'active', -- active, fallow, planted, harvested, inactive
    location_data JSONB, -- GeoJSON for field boundaries
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Inventory Tables
CREATE TABLE inventory_categories (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    farm_id UUID REFERENCES farms(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE inventory_items (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    farm_id UUID REFERENCES farms(id) ON DELETE CASCADE,
    category_id UUID REFERENCES inventory_categories(id),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    sku VARCHAR(100),
    unit VARCHAR(50) NOT NULL, -- lb, gal, each, etc.
    quantity_on_hand DECIMAL(10, 2) DEFAULT 0,
    reorder_point DECIMAL(10, 2),
    reorder_quantity DECIMAL(10, 2),
    cost_per_unit DECIMAL(10, 2),
    value_on_hand DECIMAL(10, 2),
    location VARCHAR(255),
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE inventory_transactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    inventory_item_id UUID REFERENCES inventory_items(id) ON DELETE CASCADE,
    transaction_date DATE NOT NULL,
    transaction_type VARCHAR(50) NOT NULL, -- purchase, use, adjustment, transfer
    quantity DECIMAL(10, 2) NOT NULL,
    unit_price DECIMAL(10, 2),
    total_price DECIMAL(10, 2),
    reference_id UUID, -- Could reference a purchase, field operation, etc.
    reference_type VARCHAR(100), -- Type of reference (purchase, field operation, etc.)
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tasks Tables
CREATE TABLE tasks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    farm_id UUID REFERENCES farms(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    task_type VARCHAR(100), -- field work, equipment maintenance, administrative, etc.
    priority VARCHAR(50) DEFAULT 'medium', -- low, medium, high, urgent
    status VARCHAR(50) DEFAULT 'pending', -- pending, in_progress, completed, cancelled
    due_date DATE,
    assigned_to UUID REFERENCES users(id),
    assigned_by UUID REFERENCES users(id),
    completed_date DATE,
    completed_by UUID REFERENCES users(id),
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE task_recurrence (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    task_id UUID REFERENCES tasks(id) ON DELETE CASCADE,
    recurrence_type VARCHAR(50) NOT NULL, -- daily, weekly, monthly, yearly
    recurrence_interval INTEGER DEFAULT 1,
    recurrence_day_of_week INTEGER[], -- 0-6 for Sunday-Saturday
    recurrence_day_of_month INTEGER[], -- 1-31
    recurrence_month INTEGER[], -- 1-12
    start_date DATE NOT NULL,
    end_date DATE,
    last_generated_date DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Employees Tables
CREATE TABLE employees (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    farm_id UUID REFERENCES farms(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id),
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    email VARCHAR(255),
    phone VARCHAR(20),
    position VARCHAR(100),
    hire_date DATE,
    termination_date DATE,
    status VARCHAR(50) DEFAULT 'active', -- active, inactive, terminated
    hourly_rate DECIMAL(10, 2),
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE time_entries (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    employee_id UUID REFERENCES employees(id) ON DELETE CASCADE,
    task_id UUID REFERENCES tasks(id),
    start_time TIMESTAMP NOT NULL,
    end_time TIMESTAMP,
    duration DECIMAL(10, 2), -- in hours
    break_duration DECIMAL(10, 2), -- in hours
    activity_type VARCHAR(100),
    notes TEXT,
    approved BOOLEAN DEFAULT FALSE,
    approved_by UUID REFERENCES users(id),
    approved_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create triggers for updated_at timestamps
CREATE TRIGGER update_soil_samples_timestamp BEFORE UPDATE ON soil_samples
FOR EACH ROW EXECUTE FUNCTION update_timestamp();

CREATE TRIGGER update_soil_test_results_timestamp BEFORE UPDATE ON soil_test_results
FOR EACH ROW EXECUTE FUNCTION update_timestamp();

CREATE TRIGGER update_soil_amendments_timestamp BEFORE UPDATE ON soil_amendments
FOR EACH ROW EXECUTE FUNCTION update_timestamp();

CREATE TRIGGER update_maintenance_schedules_timestamp BEFORE UPDATE ON maintenance_schedules
FOR EACH ROW EXECUTE FUNCTION update_timestamp();

CREATE TRIGGER update_maintenance_logs_timestamp BEFORE UPDATE ON maintenance_logs
FOR EACH ROW EXECUTE FUNCTION update_timestamp();

CREATE TRIGGER update_fields_timestamp BEFORE UPDATE ON fields
FOR EACH ROW EXECUTE FUNCTION update_timestamp();

CREATE TRIGGER update_inventory_categories_timestamp BEFORE UPDATE ON inventory_categories
FOR EACH ROW EXECUTE FUNCTION update_timestamp();

CREATE TRIGGER update_inventory_items_timestamp BEFORE UPDATE ON inventory_items
FOR EACH ROW EXECUTE FUNCTION update_timestamp();

CREATE TRIGGER update_inventory_transactions_timestamp BEFORE UPDATE ON inventory_transactions
FOR EACH ROW EXECUTE FUNCTION update_timestamp();

CREATE TRIGGER update_tasks_timestamp BEFORE UPDATE ON tasks
FOR EACH ROW EXECUTE FUNCTION update_timestamp();

CREATE TRIGGER update_task_recurrence_timestamp BEFORE UPDATE ON task_recurrence
FOR EACH ROW EXECUTE FUNCTION update_timestamp();

CREATE TRIGGER update_employees_timestamp BEFORE UPDATE ON employees
FOR EACH ROW EXECUTE FUNCTION update_timestamp();

CREATE TRIGGER update_time_entries_timestamp BEFORE UPDATE ON time_entries
FOR EACH ROW EXECUTE FUNCTION update_timestamp();

-- Create indexes for performance
CREATE INDEX idx_soil_samples_farm_id ON soil_samples(farm_id);
CREATE INDEX idx_soil_samples_field_id ON soil_samples(field_id);
CREATE INDEX idx_soil_test_results_soil_sample_id ON soil_test_results(soil_sample_id);
CREATE INDEX idx_soil_amendments_farm_id ON soil_amendments(farm_id);
CREATE INDEX idx_soil_amendments_field_id ON soil_amendments(field_id);
CREATE INDEX idx_maintenance_schedules_equipment_id ON maintenance_schedules(equipment_id);
CREATE INDEX idx_maintenance_logs_equipment_id ON maintenance_logs(equipment_id);
CREATE INDEX idx_maintenance_logs_maintenance_schedule_id ON maintenance_logs(maintenance_schedule_id);
CREATE INDEX idx_fields_farm_id ON fields(farm_id);
CREATE INDEX idx_inventory_categories_farm_id ON inventory_categories(farm_id);
CREATE INDEX idx_inventory_items_farm_id ON inventory_items(farm_id);
CREATE INDEX idx_inventory_items_category_id ON inventory_items(category_id);
CREATE INDEX idx_inventory_transactions_inventory_item_id ON inventory_transactions(inventory_item_id);
CREATE INDEX idx_tasks_farm_id ON tasks(farm_id);
CREATE INDEX idx_tasks_assigned_to ON tasks(assigned_to);
CREATE INDEX idx_task_recurrence_task_id ON task_recurrence(task_id);
CREATE INDEX idx_employees_farm_id ON employees(farm_id);
CREATE INDEX idx_employees_user_id ON employees(user_id);
CREATE INDEX idx_time_entries_employee_id ON time_entries(employee_id);
CREATE INDEX idx_time_entries_task_id ON time_entries(task_id);