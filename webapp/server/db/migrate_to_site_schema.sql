-- Migration script to move tables from public schema to site schema

-- Create the site schema if it doesn't exist
CREATE SCHEMA IF NOT EXISTS site;

-- Get a list of all tables in the public schema
DO $$
DECLARE
    table_record RECORD;
BEGIN
    -- Loop through all tables in the public schema
    FOR table_record IN 
        SELECT tablename 
        FROM pg_tables 
        WHERE schemaname = 'public' 
        AND tablename NOT IN ('pg_stat_statements')
    LOOP
        -- Create the table in the site schema
        EXECUTE format('CREATE TABLE IF NOT EXISTS site.%I (LIKE public.%I INCLUDING ALL)', 
                      table_record.tablename, table_record.tablename);
        
        -- Copy data from public schema to site schema
        EXECUTE format('INSERT INTO site.%I SELECT * FROM public.%I', 
                      table_record.tablename, table_record.tablename);
        
        RAISE NOTICE 'Migrated table: %', table_record.tablename;
    END LOOP;
END $$;

-- Set the search_path to use the site schema
SET search_path TO site;

-- Output success message
DO $$
BEGIN
    RAISE NOTICE 'Migration to site schema completed successfully.';
    RAISE NOTICE 'Please update your DB_SCHEMA environment variable to ''site''.';
END $$;