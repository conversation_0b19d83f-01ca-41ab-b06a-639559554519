-- Add trial columns to subscription_plans table

-- Set the search path to the site schema
SET search_path TO site;

-- Add trial duration columns to subscription_plans table
ALTER TABLE site.subscription_plans 
ADD COLUMN IF NOT EXISTS trial_duration_days INTEGER DEFAULT 14,
ADD COLUMN IF NOT EXISTS trial_features JSONB,
ADD COLUMN IF NOT EXISTS requires_payment_method BOOLEAN DEFAULT FALSE;

-- Add comments to explain the new columns
COMMENT ON COLUMN site.subscription_plans.trial_duration_days IS 'Number of days the trial lasts for this plan';
COMMENT ON COLUMN site.subscription_plans.trial_features IS 'Features available during the trial period, if different from regular plan features';
COMMENT ON COLUMN site.subscription_plans.requires_payment_method IS 'Whether this trial requires a payment method upfront';

-- Update existing trial plans with default values
UPDATE site.subscription_plans 
SET 
    trial_duration_days = 14,
    trial_features = features,
    requires_payment_method = FALSE
WHERE is_trial = TRUE;

-- Create index for better query performance
CREATE INDEX IF NOT EXISTS idx_subscription_plans_trial_duration ON site.subscription_plans(trial_duration_days);