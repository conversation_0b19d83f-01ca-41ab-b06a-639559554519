-- Remove trial_ends_at column from users table

-- Set the search path to the site schema
SET search_path TO site;

-- First, make sure all users with trial subscriptions have a subscription transaction
DO $$
DECLARE
    trial_plan_id UUID;
BEGIN
    -- Find a trial subscription plan
    SELECT id INTO trial_plan_id FROM site.subscription_plans WHERE is_trial = TRUE LIMIT 1;
    
    IF trial_plan_id IS NOT NULL THEN
        -- For each user with trial_ends_at set, create a subscription transaction if one doesn't exist
        INSERT INTO site.subscription_transactions (
            id, user_id, subscription_plan_id, amount, currency, 
            status, payment_method, payment_reference, transaction_date, 
            billing_period_start, billing_period_end, created_at, updated_at
        )
        SELECT 
            gen_random_uuid(), u.id, trial_plan_id, 0, 'USD',
            'succeeded', 'trial', 'migration-' || extract(epoch from now()), NOW(),
            NOW(), u.trial_ends_at, NOW(), NOW()
        FROM site.users u
        WHERE u.trial_ends_at IS NOT NULL
        AND NOT EXISTS (
            SELECT 1 FROM site.subscription_transactions st 
            WHERE st.user_id = u.id AND st.subscription_plan_id = trial_plan_id
        );
    END IF;
END $$;

-- Now remove the trial_ends_at column
ALTER TABLE site.users DROP COLUMN IF EXISTS trial_ends_at;