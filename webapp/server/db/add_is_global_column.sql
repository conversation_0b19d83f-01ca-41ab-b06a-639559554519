-- Add is_global column to vets, integrations, and suppliers tables
-- Set the search path to the appropriate schema
SET search_path TO site;

-- Add is_global column to vets table if it doesn't exist
ALTER TABLE site.vets ADD COLUMN IF NOT EXISTS is_global BOOLEAN DEFAULT FALSE;

-- Add is_global column to integrations table if it doesn't exist
ALTER TABLE site.integrations ADD COLUMN IF NOT EXISTS is_global BOOLEAN DEFAULT FALSE;

-- Add is_global column to suppliers table if it doesn't exist
ALTER TABLE site.suppliers ADD COLUMN IF NOT EXISTS is_global BOOLEAN DEFAULT FALSE;

-- Update the Supplier model to include the is_global column
-- This needs to be done in the Supplier.js file