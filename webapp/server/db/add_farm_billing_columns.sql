-- Add missing billing and subscription columns to farms table

-- Set the search path to the site schema
SET search_path TO site;

-- Add subscription_plan_id column
ALTER TABLE site.farms ADD COLUMN IF NOT EXISTS subscription_plan_id UUID REFERENCES site.subscription_plans(id);
COMMENT ON COLUMN site.farms.subscription_plan_id IS 'Reference to the subscription plan for this farm';

-- Add subscription_status column if it doesn't exist (already added in a previous migration)
ALTER TABLE site.farms ADD COLUMN IF NOT EXISTS subscription_status VARCHAR(50) NOT NULL DEFAULT 'active';
COMMENT ON COLUMN site.farms.subscription_status IS 'Status of the subscription: active, past_due, canceled, etc.';

-- Add subscription date columns
ALTER TABLE site.farms ADD COLUMN IF NOT EXISTS subscription_start_date DATE;
COMMENT ON COLUMN site.farms.subscription_start_date IS 'Date when the subscription started';

ALTER TABLE site.farms ADD COLUMN IF NOT EXISTS subscription_end_date DATE;
COMMENT ON COLUMN site.farms.subscription_end_date IS 'Date when the subscription ends';

-- Add billing address columns
ALTER TABLE site.farms ADD COLUMN IF NOT EXISTS billing_address VARCHAR(255);
COMMENT ON COLUMN site.farms.billing_address IS 'Billing address for the farm';

ALTER TABLE site.farms ADD COLUMN IF NOT EXISTS billing_city VARCHAR(100);
COMMENT ON COLUMN site.farms.billing_city IS 'Billing city for the farm';

ALTER TABLE site.farms ADD COLUMN IF NOT EXISTS billing_state VARCHAR(50);
COMMENT ON COLUMN site.farms.billing_state IS 'Billing state/province for the farm';

ALTER TABLE site.farms ADD COLUMN IF NOT EXISTS billing_zip_code VARCHAR(20);
COMMENT ON COLUMN site.farms.billing_zip_code IS 'Billing postal/zip code for the farm';

ALTER TABLE site.farms ADD COLUMN IF NOT EXISTS billing_country VARCHAR(100) DEFAULT 'USA';
COMMENT ON COLUMN site.farms.billing_country IS 'Billing country for the farm';

-- Add payment method columns
ALTER TABLE site.farms ADD COLUMN IF NOT EXISTS payment_method_id VARCHAR(255);
COMMENT ON COLUMN site.farms.payment_method_id IS 'ID of the payment method used for billing';

ALTER TABLE site.farms ADD COLUMN IF NOT EXISTS stripe_customer_id VARCHAR(255);
COMMENT ON COLUMN site.farms.stripe_customer_id IS 'Stripe customer ID for this farm';

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_farms_subscription_plan_id ON site.farms(subscription_plan_id);
CREATE INDEX IF NOT EXISTS idx_farms_subscription_status ON site.farms(subscription_status);
CREATE INDEX IF NOT EXISTS idx_farms_stripe_customer_id ON site.farms(stripe_customer_id);
