-- Add email verification fields to users table

-- Set the search path to the appropriate schema
SET search_path TO site;

-- Check if the schema exists, if not use site
DO $$
DECLARE
    schema_name text;
BEGIN
    -- Get the schema name from environment variable or use 'site'
    schema_name := current_setting('app.db_schema', true);
    IF schema_name IS NULL THEN
        schema_name := 'site';
    END IF;

    -- Add email_verified column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_schema = schema_name 
        AND table_name = 'users' 
        AND column_name = 'email_verified'
    ) THEN
        EXECUTE format('ALTER TABLE %I.users ADD COLUMN email_verified BOOLEAN DEFAULT FALSE', schema_name);
    END IF;

    -- Add email_verification_token column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_schema = schema_name 
        AND table_name = 'users' 
        AND column_name = 'email_verification_token'
    ) THEN
        EXECUTE format('ALTER TABLE %I.users ADD COLUMN email_verification_token VARCHAR(255)', schema_name);
    END IF;

    -- Add email_verification_expires column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_schema = schema_name 
        AND table_name = 'users' 
        AND column_name = 'email_verification_expires'
    ) THEN
        EXECUTE format('ALTER TABLE %I.users ADD COLUMN email_verification_expires TIMESTAMP', schema_name);
    END IF;
END $$;
