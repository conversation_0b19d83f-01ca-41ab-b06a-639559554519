# Database Schema Management

This directory contains SQL scripts for managing the database schema for the NxtAcre Farm Management Platform.

## Schema Files

- `schema.sql`: The main schema file that defines the initial database structure.
- `fix_user_type_column.sql`: <PERSON><PERSON><PERSON> to fix the user_type column in the users table, converting it from VARCHAR to ENUM type.
- `add_roles_table.sql`: <PERSON><PERSON><PERSON> to add the roles table to the database.
- `add_role_id_to_user_farms.sql`: <PERSON><PERSON><PERSON> to add the role_id column to the user_farms table.
- Other alter schema files: Various scripts to add or modify columns in existing tables.

## User Type Column Fix

The `fix_user_type_column.sql` script addresses an issue where the user_type column in the users table was initially created as a VARCHAR type, but needed to be converted to an ENUM type for better data validation.

### What the Fix Does

1. Creates a user_type_enum type if it doesn't exist
2. Checks if the user_type column exists as VARCHAR:
   - If it does, creates a temporary column, converts the data, drops the old column, and renames the temporary column
   - Adds NOT NULL constraint and default value
3. Checks if the user_type column doesn't exist at all:
   - If it doesn't, adds it as an ENUM type with NOT NULL constraint and default value
4. Adds a comment to the column
5. Updates any NULL values to 'farmer'

### Recent Improvements

The fix has been improved to ensure the user_type column is properly added to the users table:

1. The database.js file now executes the fix_user_type_column.sql script as a single statement instead of splitting it by semicolons, which preserves the integrity of the DO blocks in the script.
2. Verification steps have been added to confirm that the column exists and has the correct type after execution.
3. A new verification script has been created to check the status of the user_type column.

### Running the Fix Manually

If you need to run the fix manually, you can use the `run_fix_user_type.js` script in the `scripts` directory:

```bash
node server/scripts/run_fix_user_type.js
```

This script will:
1. Execute the fix_user_type_column.sql script
2. Verify that the user_type column exists and has the correct type
3. Provide detailed output about the column's properties

### Verifying the Fix

You can verify that the user_type column exists and has the correct type using the `verify_user_type_column.js` script:

```bash
node server/scripts/verify_user_type_column.js
```

This script will:
1. Check if the user_type column exists in the users table
2. Verify if it's properly defined as an ENUM type
3. Check if the user_type_enum type exists
4. List the values in the enum type

## Role-Related Migrations

The `add_roles_table.sql` and `add_role_id_to_user_farms.sql` scripts address a discrepancy between the database schema and the Sequelize models. The UserFarm model requires a non-null role_id field that references the Role model, but these tables and columns didn't exist in the database.

### What the Migrations Do

1. `add_roles_table.sql`:
   - Creates the roles table with columns for id, farm_id, name, description, is_system_role, created_at, and updated_at
   - Adds a unique index on farm_id and name
   - Inserts default global roles (farm_owner, farm_admin, farm_manager, farm_employee, accountant)

2. `add_role_id_to_user_farms.sql`:
   - Adds the role_id column to the user_farms table, which references the id column in the roles table
   - Updates existing records to set the role_id based on the role field
   - Makes the role_id column NOT NULL after all existing records have been updated

### Running the Migrations

To run the role-related migrations, use the following command:

```bash
node server/scripts/run_role_migration.js
```

This script will run both migration files in the correct order.

### Testing the Migrations

To test if the migrations have been applied correctly, use the following command:

```bash
node server/scripts/test_role_migration.js
```

This script will check if the roles table exists, if the role_id column exists in the user_farms table, and if all records have a non-null role_id.

## Schema Initialization Process

The database schema is initialized in the following order:

1. The main schema.sql file is executed to create the initial tables
2. Alter schema files are executed to apply any necessary changes to existing tables
3. Sequelize models are synchronized with the database

This process is managed by the `initializeSchema` function in `server/config/database.js`.
