-- Add billing_email column to farms table if it doesn't exist

-- Set the search path to the appropriate schema
SET search_path TO site;

ALTER TABLE site.farms ADD COLUMN IF NOT EXISTS billing_email VARCHAR(255);

-- Add comment explaining the purpose of this column
COMMENT ON COLUMN site.farms.billing_email IS 'Email address for billing communications and invoices';

-- Create index for better query performance
CREATE INDEX IF NOT EXISTS idx_farms_billing_email ON site.farms(billing_email);
