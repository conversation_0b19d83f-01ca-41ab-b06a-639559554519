-- Migration to add additional permission columns to the role_permissions table
SET search_path TO site;

-- Add can_approve column if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'site' 
        AND table_name = 'role_permissions' 
        AND column_name = 'can_approve'
    ) THEN
        ALTER TABLE role_permissions ADD COLUMN can_approve BOOLEAN DEFAULT FALSE;
        COMMENT ON COLUMN role_permissions.can_approve IS 'Whether the role can approve items in this feature';
    END IF;
END
$$;

-- Add can_reject column if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'site' 
        AND table_name = 'role_permissions' 
        AND column_name = 'can_reject'
    ) THEN
        ALTER TABLE role_permissions ADD COLUMN can_reject BOOLEAN DEFAULT FALSE;
        COMMENT ON COLUMN role_permissions.can_reject IS 'Whether the role can reject items in this feature';
    END IF;
END
$$;

-- Add can_assign column if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'site' 
        AND table_name = 'role_permissions' 
        AND column_name = 'can_assign'
    ) THEN
        ALTER TABLE role_permissions ADD COLUMN can_assign BOOLEAN DEFAULT FALSE;
        COMMENT ON COLUMN role_permissions.can_assign IS 'Whether the role can assign items to users in this feature';
    END IF;
END
$$;

-- Add can_export column if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'site' 
        AND table_name = 'role_permissions' 
        AND column_name = 'can_export'
    ) THEN
        ALTER TABLE role_permissions ADD COLUMN can_export BOOLEAN DEFAULT FALSE;
        COMMENT ON COLUMN role_permissions.can_export IS 'Whether the role can export data from this feature';
    END IF;
END
$$;

-- Add can_import column if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'site' 
        AND table_name = 'role_permissions' 
        AND column_name = 'can_import'
    ) THEN
        ALTER TABLE role_permissions ADD COLUMN can_import BOOLEAN DEFAULT FALSE;
        COMMENT ON COLUMN role_permissions.can_import IS 'Whether the role can import data into this feature';
    END IF;
END
$$;

-- Add can_manage_settings column if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'site' 
        AND table_name = 'role_permissions' 
        AND column_name = 'can_manage_settings'
    ) THEN
        ALTER TABLE role_permissions ADD COLUMN can_manage_settings BOOLEAN DEFAULT FALSE;
        COMMENT ON COLUMN role_permissions.can_manage_settings IS 'Whether the role can manage settings for this feature';
    END IF;
END
$$;

-- Add can_generate_reports column if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'site' 
        AND table_name = 'role_permissions' 
        AND column_name = 'can_generate_reports'
    ) THEN
        ALTER TABLE role_permissions ADD COLUMN can_generate_reports BOOLEAN DEFAULT FALSE;
        COMMENT ON COLUMN role_permissions.can_generate_reports IS 'Whether the role can generate reports for this feature';
    END IF;
END
$$;

-- Add can_view_sensitive column if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'site' 
        AND table_name = 'role_permissions' 
        AND column_name = 'can_view_sensitive'
    ) THEN
        ALTER TABLE role_permissions ADD COLUMN can_view_sensitive BOOLEAN DEFAULT FALSE;
        COMMENT ON COLUMN role_permissions.can_view_sensitive IS 'Whether the role can view sensitive information in this feature';
    END IF;
END
$$;

-- Add a comment to explain the purpose of this migration
COMMENT ON TABLE role_permissions IS 'Stores permissions for roles to access features with expanded permission types';
