-- Migration to add help_tips_disabled column to users table

-- Set the search path to the site schema
SET search_path TO site;

-- Add help_tips_disabled column to users table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_schema = 'site'
        AND table_name = 'users'
        AND column_name = 'help_tips_disabled'
    ) THEN
        ALTER TABLE users
        ADD COLUMN help_tips_disabled BOOLEAN DEFAULT FALSE;
        
        COMMENT ON COLUMN users.help_tips_disabled IS 'Whether the user has disabled all help tips';
    END IF;
END $$;

-- Add index on help_tips_disabled column
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM pg_indexes
        WHERE schemaname = 'site'
        AND tablename = 'users'
        AND indexname = 'idx_users_help_tips_disabled'
    ) THEN
        CREATE INDEX idx_users_help_tips_disabled ON users(help_tips_disabled);
    END IF;
END $$;