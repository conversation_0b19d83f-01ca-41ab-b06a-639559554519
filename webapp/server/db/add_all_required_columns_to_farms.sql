-- Add all required columns to farms table

-- Set the search path to the site schema
SET search_path TO site;

-- Add id column if it doesn't exist (should already exist as primary key)
-- ALTER TABLE site.farms ADD COLUMN IF NOT EXISTS id UUID PRIMARY KEY DEFAULT uuid_generate_v4();

-- Add name column if it doesn't exist (should already exist)
-- ALTER TABLE site.farms ADD COLUMN IF NOT EXISTS name VARCHAR(255) NOT NULL;

-- Add address column if it doesn't exist
ALTER TABLE site.farms ADD COLUMN IF NOT EXISTS address VARCHAR(255);
COMMENT ON COLUMN site.farms.address IS 'Physical address of the farm';

-- Add city column if it doesn't exist
ALTER TABLE site.farms ADD COLUMN IF NOT EXISTS city VARCHAR(100);
COMMENT ON COLUMN site.farms.city IS 'City where the farm is located';

-- Add state column if it doesn't exist
ALTER TABLE site.farms ADD COLUMN IF NOT EXISTS state VARCHAR(50);
COMMENT ON COLUMN site.farms.state IS 'State/province where the farm is located';

-- Add zip_code column if it doesn't exist
ALTER TABLE site.farms ADD COLUMN IF NOT EXISTS zip_code VARCHAR(20);
COMMENT ON COLUMN site.farms.zip_code IS 'Postal/zip code of the farm';

-- Add country column if it doesn't exist
ALTER TABLE site.farms ADD COLUMN IF NOT EXISTS country VARCHAR(100) DEFAULT 'USA';
COMMENT ON COLUMN site.farms.country IS 'Country where the farm is located';

-- Add tax_id column if it doesn't exist
ALTER TABLE site.farms ADD COLUMN IF NOT EXISTS tax_id VARCHAR(50);
COMMENT ON COLUMN site.farms.tax_id IS 'Tax identification number for the farm';

-- Add subscription_plan_id column if it doesn't exist
ALTER TABLE site.farms ADD COLUMN IF NOT EXISTS subscription_plan_id UUID REFERENCES site.subscription_plans(id);
COMMENT ON COLUMN site.farms.subscription_plan_id IS 'Reference to the subscription plan for this farm';

-- Add subscription_status column if it doesn't exist
ALTER TABLE site.farms ADD COLUMN IF NOT EXISTS subscription_status VARCHAR(50) NOT NULL DEFAULT 'active';
COMMENT ON COLUMN site.farms.subscription_status IS 'Status of the subscription: active, past_due, canceled, etc.';

-- Add subscription_start_date column if it doesn't exist
ALTER TABLE site.farms ADD COLUMN IF NOT EXISTS subscription_start_date DATE;
COMMENT ON COLUMN site.farms.subscription_start_date IS 'Date when the subscription started';

-- Add subscription_end_date column if it doesn't exist
ALTER TABLE site.farms ADD COLUMN IF NOT EXISTS subscription_end_date DATE;
COMMENT ON COLUMN site.farms.subscription_end_date IS 'Date when the subscription ends';

-- Add billing_email column if it doesn't exist
ALTER TABLE site.farms ADD COLUMN IF NOT EXISTS billing_email VARCHAR(255);
COMMENT ON COLUMN site.farms.billing_email IS 'Email address for billing communications and invoices';

-- Add billing_address column if it doesn't exist
ALTER TABLE site.farms ADD COLUMN IF NOT EXISTS billing_address VARCHAR(255);
COMMENT ON COLUMN site.farms.billing_address IS 'Billing address for the farm';

-- Add billing_city column if it doesn't exist
ALTER TABLE site.farms ADD COLUMN IF NOT EXISTS billing_city VARCHAR(100);
COMMENT ON COLUMN site.farms.billing_city IS 'Billing city for the farm';

-- Add billing_state column if it doesn't exist
ALTER TABLE site.farms ADD COLUMN IF NOT EXISTS billing_state VARCHAR(50);
COMMENT ON COLUMN site.farms.billing_state IS 'Billing state/province for the farm';

-- Add billing_zip_code column if it doesn't exist
ALTER TABLE site.farms ADD COLUMN IF NOT EXISTS billing_zip_code VARCHAR(20);
COMMENT ON COLUMN site.farms.billing_zip_code IS 'Billing postal/zip code for the farm';

-- Add billing_country column if it doesn't exist
ALTER TABLE site.farms ADD COLUMN IF NOT EXISTS billing_country VARCHAR(100) DEFAULT 'USA';
COMMENT ON COLUMN site.farms.billing_country IS 'Billing country for the farm';

-- Add payment_method_id column if it doesn't exist
ALTER TABLE site.farms ADD COLUMN IF NOT EXISTS payment_method_id VARCHAR(255);
COMMENT ON COLUMN site.farms.payment_method_id IS 'ID of the payment method used for billing';

-- Add stripe_customer_id column if it doesn't exist
ALTER TABLE site.farms ADD COLUMN IF NOT EXISTS stripe_customer_id VARCHAR(255);
COMMENT ON COLUMN site.farms.stripe_customer_id IS 'Stripe customer ID for this farm';

-- Add subdomain column if it doesn't exist
ALTER TABLE site.farms ADD COLUMN IF NOT EXISTS subdomain VARCHAR(50) UNIQUE;
COMMENT ON COLUMN site.farms.subdomain IS 'Subdomain for accessing the farm''s dashboard';

-- Add created_at column if it doesn't exist
ALTER TABLE site.farms ADD COLUMN IF NOT EXISTS created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
COMMENT ON COLUMN site.farms.created_at IS 'Timestamp when the farm record was created';

-- Add updated_at column if it doesn't exist
ALTER TABLE site.farms ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
COMMENT ON COLUMN site.farms.updated_at IS 'Timestamp when the farm record was last updated';

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_farms_subscription_plan_id ON site.farms(subscription_plan_id);
CREATE INDEX IF NOT EXISTS idx_farms_subscription_status ON site.farms(subscription_status);
CREATE INDEX IF NOT EXISTS idx_farms_billing_email ON site.farms(billing_email);
CREATE INDEX IF NOT EXISTS idx_farms_stripe_customer_id ON site.farms(stripe_customer_id);

-- Verify that all required columns exist
DO $$
DECLARE
    missing_columns TEXT := '';
    column_exists BOOLEAN;
BEGIN
    -- Check each required column
    SELECT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'site' AND table_name = 'farms' AND column_name = 'id'
    ) INTO column_exists;
    IF NOT column_exists THEN missing_columns := missing_columns || 'id, '; END IF;
    
    SELECT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'site' AND table_name = 'farms' AND column_name = 'name'
    ) INTO column_exists;
    IF NOT column_exists THEN missing_columns := missing_columns || 'name, '; END IF;
    
    SELECT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'site' AND table_name = 'farms' AND column_name = 'address'
    ) INTO column_exists;
    IF NOT column_exists THEN missing_columns := missing_columns || 'address, '; END IF;
    
    SELECT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'site' AND table_name = 'farms' AND column_name = 'city'
    ) INTO column_exists;
    IF NOT column_exists THEN missing_columns := missing_columns || 'city, '; END IF;
    
    SELECT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'site' AND table_name = 'farms' AND column_name = 'state'
    ) INTO column_exists;
    IF NOT column_exists THEN missing_columns := missing_columns || 'state, '; END IF;
    
    SELECT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'site' AND table_name = 'farms' AND column_name = 'zip_code'
    ) INTO column_exists;
    IF NOT column_exists THEN missing_columns := missing_columns || 'zip_code, '; END IF;
    
    SELECT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'site' AND table_name = 'farms' AND column_name = 'country'
    ) INTO column_exists;
    IF NOT column_exists THEN missing_columns := missing_columns || 'country, '; END IF;
    
    SELECT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'site' AND table_name = 'farms' AND column_name = 'tax_id'
    ) INTO column_exists;
    IF NOT column_exists THEN missing_columns := missing_columns || 'tax_id, '; END IF;
    
    SELECT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'site' AND table_name = 'farms' AND column_name = 'subscription_plan_id'
    ) INTO column_exists;
    IF NOT column_exists THEN missing_columns := missing_columns || 'subscription_plan_id, '; END IF;
    
    SELECT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'site' AND table_name = 'farms' AND column_name = 'subscription_status'
    ) INTO column_exists;
    IF NOT column_exists THEN missing_columns := missing_columns || 'subscription_status, '; END IF;
    
    SELECT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'site' AND table_name = 'farms' AND column_name = 'subscription_start_date'
    ) INTO column_exists;
    IF NOT column_exists THEN missing_columns := missing_columns || 'subscription_start_date, '; END IF;
    
    SELECT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'site' AND table_name = 'farms' AND column_name = 'subscription_end_date'
    ) INTO column_exists;
    IF NOT column_exists THEN missing_columns := missing_columns || 'subscription_end_date, '; END IF;
    
    SELECT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'site' AND table_name = 'farms' AND column_name = 'billing_email'
    ) INTO column_exists;
    IF NOT column_exists THEN missing_columns := missing_columns || 'billing_email, '; END IF;
    
    SELECT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'site' AND table_name = 'farms' AND column_name = 'billing_address'
    ) INTO column_exists;
    IF NOT column_exists THEN missing_columns := missing_columns || 'billing_address, '; END IF;
    
    SELECT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'site' AND table_name = 'farms' AND column_name = 'billing_city'
    ) INTO column_exists;
    IF NOT column_exists THEN missing_columns := missing_columns || 'billing_city, '; END IF;
    
    SELECT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'site' AND table_name = 'farms' AND column_name = 'billing_state'
    ) INTO column_exists;
    IF NOT column_exists THEN missing_columns := missing_columns || 'billing_state, '; END IF;
    
    SELECT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'site' AND table_name = 'farms' AND column_name = 'billing_zip_code'
    ) INTO column_exists;
    IF NOT column_exists THEN missing_columns := missing_columns || 'billing_zip_code, '; END IF;
    
    SELECT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'site' AND table_name = 'farms' AND column_name = 'billing_country'
    ) INTO column_exists;
    IF NOT column_exists THEN missing_columns := missing_columns || 'billing_country, '; END IF;
    
    SELECT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'site' AND table_name = 'farms' AND column_name = 'payment_method_id'
    ) INTO column_exists;
    IF NOT column_exists THEN missing_columns := missing_columns || 'payment_method_id, '; END IF;
    
    SELECT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'site' AND table_name = 'farms' AND column_name = 'stripe_customer_id'
    ) INTO column_exists;
    IF NOT column_exists THEN missing_columns := missing_columns || 'stripe_customer_id, '; END IF;
    
    SELECT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'site' AND table_name = 'farms' AND column_name = 'subdomain'
    ) INTO column_exists;
    IF NOT column_exists THEN missing_columns := missing_columns || 'subdomain, '; END IF;
    
    SELECT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'site' AND table_name = 'farms' AND column_name = 'created_at'
    ) INTO column_exists;
    IF NOT column_exists THEN missing_columns := missing_columns || 'created_at, '; END IF;
    
    SELECT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'site' AND table_name = 'farms' AND column_name = 'updated_at'
    ) INTO column_exists;
    IF NOT column_exists THEN missing_columns := missing_columns || 'updated_at, '; END IF;
    
    -- Report results
    IF missing_columns = '' THEN
        RAISE NOTICE 'All required columns exist in the farms table.';
    ELSE
        missing_columns := SUBSTRING(missing_columns, 1, LENGTH(missing_columns) - 2); -- Remove trailing comma and space
        RAISE WARNING 'The following columns are still missing from the farms table: %', missing_columns;
    END IF;
END $$;