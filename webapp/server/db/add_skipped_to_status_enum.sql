-- Add 'skipped' to the status ENUM in the database_migrations table

-- Set the search path to the site schema
SET search_path TO site;

-- Create the ENUM type if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'database_migrations_status_enum') THEN
        CREATE TYPE database_migrations_status_enum AS ENUM ('pending', 'in_progress', 'completed', 'failed');
    END IF;
END$$;

-- Add 'skipped' to the ENUM type
ALTER TYPE database_migrations_status_enum ADD VALUE 'skipped' AFTER 'failed';

-- Add a comment to explain the purpose of this migration
COMMENT ON COLUMN database_migrations.status IS 'Status of the migration: pending, in_progress, completed, failed, skipped';
