/**
 * Global error handling middleware
 * @param {Error} err - Error object
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
export const handleErrors = (err, req, res, next) => {
  console.error('Error caught by error middleware:');
  console.error('Error message:', err.message);
  console.error('Error stack:', err.stack);

  // Log additional error details if available
  if (err.name) console.error('Error name:', err.name);
  if (err.code) console.error('Error code:', err.code);
  if (err.errno) console.error('Error errno:', err.errno);
  if (err.sql) console.error('SQL query that caused the error:', err.sql);

  // Log request details
  console.error('Request method:', req.method);
  console.error('Request URL:', req.originalUrl);
  console.error('Request body:', req.body);
  console.error('Request headers:', req.headers);

  // Determine status code
  const statusCode = err.statusCode || err.status || 500;

  // Send appropriate response with useful error information
  const errorResponse = {
    message: err.message || 'Internal Server Error',
    errorType: err.name || 'Error',
    errorCode: err.code || statusCode.toString(),
    // Include additional context if available
    context: err.context || null,
    // Only include stack trace in development
    error: process.env.NODE_ENV === 'development' ? {
      stack: err.stack
    } : undefined
  };

  res.status(statusCode).json(errorResponse);
};

/**
 * Middleware to handle 404 Not Found errors
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const notFound = (req, res) => {
  console.error(`[404 Error] Not Found - ${req.originalUrl}`);
  console.error(`[404 Error] Hostname: ${req.hostname}`);
  console.error(`[404 Error] Path: ${req.path}`);
  console.error(`[404 Error] Method: ${req.method}`);
  console.error(`[404 Error] Headers:`, req.headers);

  res.status(404).json({
    message: `The requested resource could not be found`,
    errorType: 'NotFoundError',
    errorCode: '404',
    context: {
      url: req.originalUrl,
      method: req.method
    }
  });
};

/**
 * Async handler to catch errors in async route handlers
 * @param {Function} fn - Async route handler function
 * @returns {Function} Express middleware function
 */
export const asyncHandler = (fn) => (req, res, next) => {
  Promise.resolve(fn(req, res, next)).catch(next);
};
