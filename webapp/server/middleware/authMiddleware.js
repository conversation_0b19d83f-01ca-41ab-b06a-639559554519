import jwt from 'jsonwebtoken';
import User from '../models/User.js';
import UserFarm from '../models/UserFarm.js';
import RolePermission from '../models/RolePermission.js';
import { checkSubscriptionStatus, hasBillingPermissions, isEmployeeOrAccountant } from '../utils/subscriptionUtils.js';
import dotenv from 'dotenv';

dotenv.config();

const JWT_SECRET = process.env.JWT_SECRET || 'your_jwt_secret_key';
const DB_MIGRATION_TOKEN = process.env.DB_MIGRATION_TOKEN;

// Middleware to authenticate JWT tokens
export const authenticate = async (req, res, next) => {
  // Skip authentication for OPTIONS requests (preflight)
  if (req.method === 'OPTIONS') {
    return next();
  }

  // Skip regular auth check for store.nxtacre.com subdomain
  if (req.skipRegularAuth) {
    return next();
  }

  try {
    // Get token from header, query parameter, or cookies
    const authHeader = req.headers.authorization;
    const queryToken = req.query.token;
    const cookieToken = req.cookies?.auth_token; // Only check for auth_token cookie

    let token;

    if (authHeader && authHeader.startsWith('Bearer ')) {
      token = authHeader.split(' ')[1];
    } else if (queryToken) {
      token = queryToken;
    } else if (cookieToken) {
      token = cookieToken;
    } else {
      return res.status(401).json({ error: 'Authentication required' });
    }

    if (!token) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    // Verify token
    const decoded = jwt.verify(token, JWT_SECRET);

    // Find user
    const user = await User.findByPk(decoded.id);

    if (!user) {
      return res.status(401).json({ error: 'User not found' });
    }

    // Add user to request
    req.user = user;

    next();
  } catch (error) {
    // Handle token expiration without logging to console
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({ error: 'Token expired', tokenExpired: true });
    }

    // Provide detailed error message for debugging
    const errorReason = error.message || error.name || 'Unknown error';
    console.error('Authentication error:', errorReason);

    return res.status(401).json({ 
      error: 'Authentication failed', 
      details: process.env.NODE_ENV === 'production' ? undefined : errorReason
    });
  }
};

// Middleware to check if user is a farm owner or farm admin
export const isAdmin = async (req, res, next) => {
  try {
    if (!req.user) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    // Check if user is a farm owner or farm admin for any farm
    const userFarm = await UserFarm.findOne({
      where: {
        user_id: req.user.id,
        role: ['farm_owner', 'farm_admin']
      }
    });

    if (!userFarm) {
      return res.status(403).json({ error: 'Admin access required' });
    }

    // Add the farm to the request for later use
    req.farmId = userFarm.farm_id;

    next();
  } catch (error) {
    // Handle admin authorization errors without logging to console
    return res.status(500).json({ error: 'Authorization failed' });
  }
};

// Middleware to check if user is a global admin
export const isGlobalAdmin = async (req, res, next) => {
  try {
    if (!req.user) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    // Check if user is a global admin
    if (!req.user.is_global_admin) {
      return res.status(403).json({ error: 'Global admin access required' });
    }

    next();
  } catch (error) {
    // Handle global admin authorization errors without logging to console
    return res.status(500).json({ error: 'Authorization failed' });
  }
};

// Middleware to check if user is an approved business owner
export const isApprovedBusinessOwner = async (req, res, next) => {
  try {
    if (!req.user) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    // Check if user is a business owner and is approved
    if (!req.user.is_business_owner || !req.user.is_approved) {
      return res.status(403).json({ 
        error: 'Approved business account required',
        isBusinessOwner: req.user.is_business_owner,
        isApproved: req.user.is_approved
      });
    }

    next();
  } catch (error) {
    // Handle business owner authorization errors without logging to console
    return res.status(500).json({ error: 'Authorization failed' });
  }
};

// Middleware to check if user has specific role for a farm
export const authorize = (roles = [], feature = null, permission = null) => {
  return async (req, res, next) => {
    try {
      if (!req.user) {
        return res.status(401).json({ error: 'Authentication required' });
      }

      // If user is a global admin, allow access
      if (req.user.is_global_admin) {
        return next();
      }

      // Get the farm ID from the request or query parameters
      const farmId = req.farmId || req.params.farmId || req.query.farmId;

      if (!farmId) {
        return res.status(400).json({ error: 'Farm ID is required' });
      }

      // If no roles specified, check if user has any role for this farm
      if (roles.length === 0) {
        const userFarm = await UserFarm.findOne({
          where: {
            user_id: req.user.id,
            farm_id: farmId
          }
        });

        if (!userFarm) {
          return res.status(403).json({ error: 'You do not have access to this farm' });
        }

        // Check if the user is approved (farm employees need approval)
        if (userFarm.role === 'farm_employee' && !userFarm.is_approved) {
          return res.status(403).json({ 
            error: 'Your account is pending approval by the farm owner or admin',
            isPendingApproval: true
          });
        }

        // Add the user's role to the request for later use
        req.userRole = userFarm.role;
        return next();
      }

      // Check if user has any of the required roles for this farm
      const userFarm = await UserFarm.findOne({
        where: {
          user_id: req.user.id,
          farm_id: farmId,
          role: roles
        }
      });

      if (!userFarm) {
        return res.status(403).json({ error: 'Insufficient permissions for this farm' });
      }

      // Check if the user is approved (farm employees need approval)
      if (userFarm.role === 'farm_employee' && !userFarm.is_approved) {
        return res.status(403).json({ 
          error: 'Your account is pending approval by the farm owner or admin',
          isPendingApproval: true
        });
      }

      // Add the user's role to the request for later use
      req.userRole = userFarm.role;

      // If feature and permission are specified, check if user has the required permission
      if (feature && permission) {
        // First check if user has custom permissions in the UserFarm model
        if (userFarm.permissions && userFarm.permissions[feature] && userFarm.permissions[feature][permission]) {
          return next();
        }

        // If not, check the RolePermission model for default permissions for this role
        const rolePermission = await RolePermission.findOne({
          where: {
            farm_id: farmId,
            role_id: userFarm.role_id,
            feature: feature
          }
        });

        // Check if the role permission exists and if the user has the required permission
        // The permission parameter could be any of: view, create, edit, delete, approve, reject, assign, export, import, manage_settings, generate_reports, view_sensitive
        if (!rolePermission || !rolePermission[`can_${permission}`]) {
          return res.status(403).json({ error: `You do not have ${permission} permission for ${feature}` });
        }
      }

      next();
    } catch (error) {
      // Handle role authorization errors without logging to console
      return res.status(500).json({ error: 'Authorization failed' });
    }
  };
};

// Middleware to check if user has access to a specific farm
export const checkFarmAccess = async (req, res, next) => {
  try {
    if (!req.user) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    // If user is a global admin, allow access
    if (req.user.is_global_admin) {
      return next();
    }

    // Get the farm ID from the request parameters
    const farmId = req.params.farmId;

    if (!farmId) {
      return res.status(400).json({ error: 'Farm ID is required' });
    }

    // Check if user has any role for this farm
    const userFarm = await UserFarm.findOne({
      where: {
        user_id: req.user.id,
        farm_id: farmId
      }
    });

    if (!userFarm) {
      return res.status(403).json({ error: 'You do not have access to this farm' });
    }

    // Check if the user is approved (farm employees need approval)
    if (userFarm.role === 'farm_employee' && !userFarm.is_approved) {
      return res.status(403).json({ 
        error: 'Your account is pending approval by the farm owner or admin',
        isPendingApproval: true
      });
    }

    // Add the user's role to the request for later use
    req.userRole = userFarm.role;
    req.farmId = farmId;

    next();
  } catch (error) {
    return res.status(500).json({ error: 'Authorization failed' });
  }
};

// Middleware to authenticate using a secret token for database migrations
export const authenticateWithToken = async (req, res, next) => {
  try {
    // Skip authentication for OPTIONS requests (preflight)
    if (req.method === 'OPTIONS') {
      return next();
    }

    // Check if DB_MIGRATION_TOKEN is configured
    if (!DB_MIGRATION_TOKEN) {
      return res.status(500).json({ error: 'DB_MIGRATION_TOKEN is not configured' });
    }

    // Get token from header
    const token = req.headers['x-migration-token'];

    if (!token) {
      return res.status(401).json({ error: 'Migration token required' });
    }

    // Verify token
    if (token !== DB_MIGRATION_TOKEN) {
      return res.status(401).json({ error: 'Invalid migration token' });
    }

    // Create a mock admin user for the request
    req.user = {
      id: 0,
      email: '<EMAIL>',
      first_name: 'System',
      last_name: 'Migration',
      is_global_admin: true
    };

    next();
  } catch (error) {
    // Provide detailed error message for debugging
    const errorReason = error.message || error.name || 'Unknown error';
    console.error('Token authentication error:', errorReason);

    return res.status(401).json({ 
      error: 'Authentication failed', 
      details: process.env.NODE_ENV === 'production' ? undefined : errorReason 
    });
  }
};

// Middleware for optional authentication (for public routes that can be enhanced with user data)
export const optionalAuthenticate = async (req, res, next) => {
  // Skip authentication for OPTIONS requests (preflight)
  if (req.method === 'OPTIONS') {
    return next();
  }

  try {
    // Get token from header, query parameter, or cookies
    const authHeader = req.headers.authorization;
    const queryToken = req.query.token;
    const cookieToken = req.cookies?.auth_token; // Only check for auth_token cookie

    let token;

    if (authHeader && authHeader.startsWith('Bearer ')) {
      token = authHeader.split(' ')[1];
    } else if (queryToken) {
      token = queryToken;
    } else if (cookieToken) {
      token = cookieToken;
    }

    // If no token is found, continue without authentication
    if (!token) {
      req.user = null;
      return next();
    }

    // Verify token
    const decoded = jwt.verify(token, JWT_SECRET);

    // Find user
    const user = await User.findByPk(decoded.id);

    // If user is found, add to request
    if (user) {
      req.user = user;
    } else {
      req.user = null;
    }

    next();
  } catch (error) {
    // If authentication fails, continue without user
    req.user = null;
    next();
  }
};

// Middleware to check subscription status and handle access accordingly
export const checkSubscriptionAccess = async (req, res, next) => {
  try {
    // Skip if user is not authenticated
    if (!req.user) {
      return next();
    }

    // Skip if user is a global admin
    if (req.user.is_global_admin) {
      return next();
    }

    // Get the farm ID from the request
    const farmId = req.farmId || req.params.farmId || req.query.farmId;

    // Skip if no farm ID is provided
    if (!farmId) {
      return next();
    }

    // Check subscription status
    const subscriptionStatus = await checkSubscriptionStatus(farmId);

    // Add subscription status to request for use in frontend
    req.subscriptionStatus = subscriptionStatus;

    // If subscription is valid, continue
    if (subscriptionStatus.isValid) {
      // If subscription is expiring soon or trial is ending soon, add warning flags
      if ((subscriptionStatus.isExpiringSoon && !subscriptionStatus.hasAutoPayEnabled) || 
          subscriptionStatus.isTrialEndingSoon) {
        // Check if user has billing permissions
        const userHasBillingPermissions = await hasBillingPermissions(req.user.id, farmId);

        if (userHasBillingPermissions) {
          // Add warning flags to request for use in frontend
          req.subscriptionWarning = {
            isExpiringSoon: subscriptionStatus.isExpiringSoon && !subscriptionStatus.hasAutoPayEnabled,
            isTrialEndingSoon: subscriptionStatus.isTrialEndingSoon,
            subscriptionEndDate: subscriptionStatus.subscriptionEndDate
          };
        }
      }
      return next();
    }

    // If subscription is not valid (expired), check user role
    const userIsEmployeeOrAccountant = await isEmployeeOrAccountant(req.user.id, farmId);
    const userHasBillingPermissions = await hasBillingPermissions(req.user.id, farmId);

    // Block employees and accountants from accessing expired subscriptions
    if (userIsEmployeeOrAccountant) {
      return res.status(403).json({
        error: 'Subscription expired',
        message: 'Your farm\'s subscription has expired. Please contact your farm manager for assistance.',
        subscriptionExpired: true
      });
    }

    // Allow farm owners and billing contacts to access with a warning
    if (userHasBillingPermissions) {
      // Add expired flag to request for use in frontend
      req.subscriptionExpired = true;
      return next();
    }

    // For other users, block access
    return res.status(403).json({
      error: 'Subscription expired',
      message: 'Your farm\'s subscription has expired. Please contact your farm manager for assistance.',
      subscriptionExpired: true
    });
  } catch (error) {
    console.error('Error checking subscription access:', error);
    // Continue to next middleware in case of error
    return next();
  }
};

// Combined middleware for authentication and subscription checking
export const authenticateWithSubscription = async (req, res, next) => {
  // First authenticate the user
  authenticate(req, res, async (err) => {
    if (err) return next(err);

    // Then check subscription access
    await checkSubscriptionAccess(req, res, next);
  });
};

// Export protect, authenticateToken, and authenticateJWT as aliases for authenticate middleware
// Note: These still use the original authenticate middleware without subscription checking
// for backward compatibility
export const protect = authenticate;
export const authenticateToken = authenticate;
export const authenticateJWT = authenticate;
