import { body, param, query, validationResult } from 'express-validator';

/**
 * Middleware to validate user input
 * @param {Array} validations - Array of express-validator validation chains
 * @returns {Function} Express middleware function
 */
export const validate = (validations) => {
  return async (req, res, next) => {
    // Execute all validations
    await Promise.all(validations.map(validation => validation.run(req)));
    
    // Check if there are validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }
    
    next();
  };
};

/**
 * Validation middleware for user-related routes
 */
export const validateUser = {
  create: validate([
    body('email').isEmail().withMessage('Must be a valid email address'),
    body('password').isLength({ min: 8 }).withMessage('Password must be at least 8 characters long'),
    body('first_name').notEmpty().withMessage('First name is required'),
    body('last_name').notEmpty().withMessage('Last name is required')
  ]),
  
  update: validate([
    body('email').optional().isEmail().withMessage('Must be a valid email address'),
    body('password').optional().isLength({ min: 8 }).withMessage('Password must be at least 8 characters long'),
    body('first_name').optional().notEmpty().withMessage('First name cannot be empty'),
    body('last_name').optional().notEmpty().withMessage('Last name cannot be empty')
  ]),
  
  login: validate([
    body('email').isEmail().withMessage('Must be a valid email address'),
    body('password').notEmpty().withMessage('Password is required')
  ])
};

/**
 * Validation middleware for farm-related routes
 */
export const validateFarm = {
  create: validate([
    body('name').notEmpty().withMessage('Farm name is required'),
    body('address').optional().notEmpty().withMessage('Address cannot be empty'),
    body('city').optional().notEmpty().withMessage('City cannot be empty'),
    body('state').optional().notEmpty().withMessage('State cannot be empty'),
    body('zip').optional().notEmpty().withMessage('ZIP code cannot be empty')
  ]),
  
  update: validate([
    body('name').optional().notEmpty().withMessage('Farm name cannot be empty'),
    body('address').optional().notEmpty().withMessage('Address cannot be empty'),
    body('city').optional().notEmpty().withMessage('City cannot be empty'),
    body('state').optional().notEmpty().withMessage('State cannot be empty'),
    body('zip').optional().notEmpty().withMessage('ZIP code cannot be empty')
  ])
};

/**
 * Validation middleware for equipment-related routes
 */
export const validateEquipment = {
  create: validate([
    body('name').notEmpty().withMessage('Equipment name is required'),
    body('type').notEmpty().withMessage('Equipment type is required'),
    body('farm_id').isNumeric().withMessage('Farm ID must be a number')
  ]),
  
  update: validate([
    body('name').optional().notEmpty().withMessage('Equipment name cannot be empty'),
    body('type').optional().notEmpty().withMessage('Equipment type cannot be empty')
  ])
};