import UserFarm from '../models/UserFarm.js';
import TaxDocument from '../models/TaxDocument.js';
import EmployeeTaxInfo from '../models/EmployeeTaxInfo.js';
import ContractorTaxInfo from '../models/ContractorTaxInfo.js';
import TaxPayment from '../models/TaxPayment.js';
import TaxFiling from '../models/TaxFiling.js';
import TaxDeduction from '../models/TaxDeduction.js';

/**
 * Middleware to check if a user has access to a specific farm
 * This middleware should be used after the authenticate middleware
 */
export const checkFarmAccess = async (req, res, next) => {
  try {
    if (!req.user) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    // If user is a global admin, allow access
    if (req.user.is_global_admin) {
      return next();
    }

    // Get the farm ID from the request parameters, query, or body
    let farmId = req.params.farmId || req.query.farmId || (req.body && req.body.farmId);

    // If farmId is not provided, try to get it from the resource ID
    if (!farmId) {
      const { documentId, taxInfoId, paymentId, filingId, deductionId } = req.params;

      if (documentId) {
        // Get farmId from TaxDocument
        const document = await TaxDocument.findByPk(documentId);
        if (document) {
          farmId = document.farm_id;
        }
      } else if (taxInfoId) {
        // Check if it's an employee tax info or contractor tax info
        const employeeTaxInfo = await EmployeeTaxInfo.findByPk(taxInfoId);
        if (employeeTaxInfo) {
          farmId = employeeTaxInfo.farm_id;
        } else {
          const contractorTaxInfo = await ContractorTaxInfo.findByPk(taxInfoId);
          if (contractorTaxInfo) {
            farmId = contractorTaxInfo.farm_id;
          }
        }
      } else if (paymentId) {
        // Get farmId from TaxPayment
        const payment = await TaxPayment.findByPk(paymentId);
        if (payment) {
          farmId = payment.farm_id;
        }
      } else if (filingId) {
        // Get farmId from TaxFiling
        const filing = await TaxFiling.findByPk(filingId);
        if (filing) {
          farmId = filing.farm_id;
        }
      } else if (deductionId) {
        // Get farmId from TaxDeduction
        const deduction = await TaxDeduction.findByPk(deductionId);
        if (deduction) {
          farmId = deduction.farm_id;
        }
      }
    }

    if (!farmId) {
      return res.status(400).json({ error: 'Farm ID is required and could not be determined from the request' });
    }

    // Check if user has any role for this farm
    const userFarm = await UserFarm.findOne({
      where: {
        user_id: req.user.id,
        farm_id: farmId
      }
    });

    if (!userFarm) {
      return res.status(403).json({ error: 'You do not have access to this farm' });
    }

    // Check if the user is approved (farm employees need approval)
    if (userFarm.role === 'farm_employee' && !userFarm.is_approved) {
      return res.status(403).json({ 
        error: 'Your account is pending approval by the farm owner or admin',
        isPendingApproval: true
      });
    }

    // Add the user's role and farm ID to the request for later use
    req.userRole = userFarm.role;
    req.farmId = farmId;

    next();
  } catch (error) {
    console.error('Farm access check error:', error);
    return res.status(500).json({ error: 'Authorization failed' });
  }
};
