import { uploadToSpaces, getSpacesUrl } from '../utils/spacesUtils.js';
import { v4 as uuidv4 } from 'uuid';
import path from 'path';

/**
 * Custom storage engine for multer that uses Digital Ocean Spaces
 */
class SpacesStorage {
  constructor(options) {
    this.options = options || {};
    this.getDestination = options.destination || this.defaultDestination;
    this.getFilename = options.filename || this.defaultFilename;
  }

  /**
   * Default destination function if none is provided
   * @param {Object} req - Express request object
   * @param {Object} file - File object from multer
   * @param {Function} cb - Callback function
   */
  defaultDestination(req, file, cb) {
    cb(null, 'uploads/temp');
  }

  /**
   * Default filename function if none is provided
   * @param {Object} req - Express request object
   * @param {Object} file - File object from multer
   * @param {Function} cb - Callback function
   */
  defaultFilename(req, file, cb) {
    const uniqueFilename = `${uuidv4()}${path.extname(file.originalname)}`;
    cb(null, uniqueFilename);
  }

  /**
   * Handle file upload
   * @param {Object} req - Express request object
   * @param {Object} file - File object from multer
   * @param {Function} cb - Callback function
   */
  _handleFile(req, file, cb) {
    this.getDestination(req, file, (err, destination) => {
      if (err) return cb(err);

      this.getFilename(req, file, async (err, filename) => {
        if (err) return cb(err);

        // Create the full storage path
        const storagePath = path.join(destination, filename).replace(/\\/g, '/');

        // Create a buffer to store the file data
        const chunks = [];
        let fileSize = 0;

        file.stream.on('data', (chunk) => {
          chunks.push(chunk);
          fileSize += chunk.length;
        });

        file.stream.on('end', async () => {
          try {
            // Combine chunks into a single buffer
            const fileBuffer = Buffer.concat(chunks);

            // Upload to Digital Ocean Spaces
            await uploadToSpaces(fileBuffer, storagePath);

            // Get the URL for the file
            const fileUrl = getSpacesUrl(storagePath);

            // Return file information
            cb(null, {
              destination: destination,
              filename: filename,
              path: storagePath,
              size: fileSize,
              spacesUrl: fileUrl
            });
          } catch (error) {
            console.error('Error uploading to Digital Ocean Spaces:', error);
            cb(error);
          }
        });

        file.stream.on('error', (error) => {
          console.error('Error reading file stream:', error);
          cb(error);
        });
      });
    });
  }

  /**
   * Handle file removal (not used in this implementation)
   * @param {Object} req - Express request object
   * @param {Object} file - File object from multer
   * @param {Function} cb - Callback function
   */
  _removeFile(req, file, cb) {
    // This method is called when an error occurs during file upload
    // We don't need to do anything here since the file is not stored locally
    cb(null);
  }
}

/**
 * Create a new instance of SpacesStorage
 * @param {Object} options - Options for the storage engine
 * @returns {SpacesStorage} A new instance of SpacesStorage
 */
export default function (options) {
  return new SpacesStorage(options);
}