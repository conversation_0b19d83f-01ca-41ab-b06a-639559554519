# Support Ticket Email Integration

This document describes the email integration for the support ticket system, which allows users to reply to ticket notifications via email without having to log in to the support panel.

## Overview

The email integration consists of two main components:

1. **Email Notifications**: When a comment is added to a support ticket, an email notification is sent to the ticket creator. The email includes the comment content and a reference to the ticket.

2. **Email Reply Processing**: Users can reply directly to the notification email, and their reply will be automatically added as a comment to the ticket. This is handled by a webhook endpoint that processes incoming emails.

## Setup

### Prerequisites

1. An SMTP server for sending emails
2. An email service that can forward incoming emails to a webhook (e.g., SendGrid, Mailgun, etc.)

### Configuration

1. Set the following environment variables in your `.env` file:

```
EMAIL_HOST=your-smtp-host
EMAIL_PORT=your-smtp-port
EMAIL_USER=your-smtp-username
EMAIL_PASSWORD=your-smtp-password
EMAIL_FROM=<EMAIL>
FRONTEND_URL=https://app.yourdomain.com
```

2. Configure your email service to forward incoming emails to the webhook endpoint:
   - Webhook URL: `https://your-api-domain.com/api/webhooks/email`
   - HTTP Method: POST
   - Content Type: application/json

   Note: The system will automatically route emails based on the recipient address:
   - Emails to `<EMAIL>` will be processed as support ticket replies
   - Emails to `receipts@{farmsubdomain}.nxtacre.com` will be processed as receipts

3. Run the database migration script to add the required fields:

```
psql -U your-db-user -d your-db-name -f server/db/support_ticket_email_integration.sql
```

## How It Works

### Sending Email Notifications

When a comment is added to a support ticket, the system:

1. Checks if the comment is not internal and if email notifications are enabled for the ticket
2. Generates a unique email thread ID if one doesn't exist
3. Loads the email template and replaces placeholders with actual values
4. Sends the email with appropriate headers for threading
5. Updates the last_email_sent_at timestamp on the ticket

### Processing Email Replies

When a user replies to a notification email, the email service forwards the reply to the webhook endpoint, which:

1. Extracts the sender's email address
2. Extracts the ticket ID from the subject or headers
3. Finds the ticket and the user associated with the email
4. Cleans the email content to remove quoted replies and signatures
5. Creates a new comment on the ticket with the email content
6. Updates the ticket's status (reopens it if it was closed)

## Email Templates

The email templates are located in `server/templates/emails/support/`. The main template is `ticket-response.html`, which is used for sending notifications about ticket comments.

## Models

The following fields have been added to the models to support email integration:

### SupportTicket

- `email_thread_id`: Unique ID for tracking email thread conversations
- `last_email_sent_at`: Timestamp of the last email sent for this ticket
- `email_notifications_enabled`: Whether email notifications are enabled for this ticket

### SupportTicketComment

- `is_from_email`: Whether the comment was received via email
- `email_source`: Email address that sent this comment (if from email)
- `email_message_id`: Message ID from the email headers

## API Endpoints

### Generic Email Webhook Endpoint

- **URL**: `/api/webhooks/email`
- **Method**: POST
- **Description**: Processes all incoming emails and routes them to the appropriate handler based on the recipient address
- **Request Body**:
  ```json
  {
    "from": "User Name <<EMAIL>>",
    "subject": "Re: [Ticket #abc-123] Your ticket subject",
    "text": "This is my reply to the ticket.",
    "html": "<p>This is my reply to the ticket.</p>",
    "recipient": "<EMAIL>",
    "headers": {
      "Message-ID": "<<EMAIL>>",
      "In-Reply-To": "ticket-abc-123-xyz",
      "References": "ticket-abc-123-xyz",
      "X-Ticket-ID": "abc-123"
    },
    "timestamp": "2023-06-01T12:00:00Z",
    "attachments": [
      {
        "filename": "receipt.pdf",
        "content": "base64-encoded-content",
        "contentType": "application/pdf",
        "size": 12345
      }
    ]
  }
  ```

### Legacy Webhook Endpoints

The following endpoints are maintained for backward compatibility:

#### Support Ticket Email Endpoint

- **URL**: `/api/webhooks/email/ticket`
- **Method**: POST
- **Description**: Processes incoming email replies and adds them as comments to the appropriate tickets

#### Receipt Email Endpoint

- **URL**: `/api/webhooks/receipts/email`
- **Method**: POST
- **Description**: Processes incoming emails with receipts and creates receipt records

## Testing

### Testing Support Ticket Email Integration

To test the support ticket email integration:

1. Create a support ticket
2. Add a comment to the ticket (this should send an email notification)
3. Reply to the email notification
4. Verify that the reply appears as a comment on the ticket

### Testing Receipt Email Integration

To test the receipt email integration:

1. Set up a farm with a subdomain (e.g., "farm1")
2. Send an email with an <NAME_EMAIL>
3. Verify that a new receipt is created in the system for the farm
4. Check that the attachment is properly stored and linked to the receipt

## Troubleshooting

- Check the server logs for any errors related to sending or receiving emails
- Verify that the email service is correctly forwarding incoming emails to the webhook endpoint
- Ensure that the email headers are being preserved when forwarding emails
