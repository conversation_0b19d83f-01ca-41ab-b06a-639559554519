# InvoiceEmail to User Association Fix

## Issue
The system was reporting: "User is not associated to InvoiceEmail!"

This issue occurred because the association between the `InvoiceEmail` model and the `User` model was not properly defined in the application's model associations.

## Root Cause
After investigation, we found that:

1. The `InvoiceEmail` model had a `sent_by_user_id` field that referenced the `User` model
2. However, the `InvoiceEmail` model was not imported in the `associations.js` file
3. As a result, no association was defined between `InvoiceEmail` and `User`

## Solution
The following changes were made to fix the issue:

1. Imported the `InvoiceEmail` model in `associations.js`:
   ```javascript
   import InvoiceEmail from './InvoiceEmail.js';
   ```

2. Added the association between `InvoiceEmail` and `User` in the INVOICING & BILLING section:
   ```javascript
   // Invoice emails
   InvoiceEmail.belongsTo(Invoice, { foreignKey: 'invoice_id', as: 'invoice' });
   Invoice.hasMany(InvoiceEmail, { foreignKey: 'invoice_id', as: 'emails' });
   InvoiceEmail.belongsTo(User, { foreignKey: 'sent_by_user_id', as: 'sentByUser' });
   User.hasMany(InvoiceEmail, { foreignKey: 'sent_by_user_id', as: 'sentInvoiceEmails' });
   ```

## Testing
A test script was created to verify the association between `InvoiceEmail` and `User`:
- `webapp/server/scripts/test_invoice_email_user_association.js`

This script:
1. Sets up the associations
2. Finds an InvoiceEmail and includes its associated User
3. Displays information about the InvoiceEmail and its associated User
4. Tests the reverse association (User to InvoiceEmail)
5. Provides clear output about whether the associations are working

## Impact
With this fix:
- The system can now properly associate users with invoice emails
- This enables proper tracking of which user sent which invoice email
- It also allows for querying a user's sent invoice emails

## Related Models
- `InvoiceEmail`: Represents an email sent for an invoice
- `User`: Represents a user in the system
- `Invoice`: Represents an invoice in the system