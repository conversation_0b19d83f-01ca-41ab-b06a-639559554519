# SMS Receipt Integration with Twilio

This document describes the SMS receipt integration using Twilio, which allows users to submit receipts by sending photos via SMS.

## Overview

The SMS receipt integration consists of two main components:

1. **Twilio Service**: A service that handles communication with the Twilio API, including sending SMS messages and retrieving media content.

2. **SMS Receipt Processing**: A webhook endpoint that processes incoming SMS messages, extracts receipt images, and creates receipt records in the database.

## Setup

### Prerequisites

1. A Twilio account (you can sign up at [twilio.com](https://www.twilio.com))
2. A Twilio phone number with SMS capabilities
3. The Twilio Account SID and Auth Token from your Twilio dashboard

### Configuration

1. Set the following environment variables in your `.env` file:

```
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_PHONE_NUMBER=your_twilio_phone_number
```

2. Configure your Twilio phone number to forward incoming SMS messages to the webhook endpoint:
   - Log in to your Twilio account
   - Navigate to Phone Numbers > Manage > Active Numbers
   - Select your phone number
   - Under "Messaging", set the webhook URL for "A MESSAGE COMES IN" to:
     - Webhook URL: `https://your-api-domain.com/api/webhooks/receipts/sms`
     - HTTP Method: POST

3. Ensure that users have their phone numbers registered in the system. Users can add their phone numbers in their profile settings.

## How It Works

### User Flow

1. A user takes a photo of a receipt and sends it as an MMS to the Twilio phone number
2. The system identifies the user by their phone number
3. If the user has access to multiple farms, the system asks them to select which farm the receipt is for
4. The system then asks for a category for the receipt
5. The receipt image is processed, saved, and a receipt record is created in the database

### Technical Flow

When a user sends an SMS with a receipt image:

1. Twilio forwards the message to the webhook endpoint
2. The system validates the phone number and finds the associated user
3. The system checks which farms the user has access to
4. The system manages a conversation flow to:
   - Select a farm (if the user has access to multiple farms)
   - Specify a category for the receipt
5. The system processes the receipt image, saves it, and creates a receipt record

## API Endpoints

### Webhook Endpoint

- **URL**: `/api/webhooks/receipts/sms`
- **Method**: POST
- **Description**: Processes incoming SMS messages and creates receipt records
- **Request Body**: Twilio's standard webhook format, including:
  ```
  From: The sender's phone number
  Body: The message text
  NumMedia: Number of media attachments
  MediaUrl0, MediaUrl1, etc.: URLs of the media attachments
  MessageSid: Unique identifier for the message
  ```

## Testing

To test the SMS receipt integration:

1. Ensure your Twilio account is set up and the webhook is configured
2. Send an MMS with a receipt image to your Twilio phone number
3. Follow the conversation prompts to select a farm (if applicable) and specify a category
4. Verify that the receipt appears in the system under the specified farm

## Troubleshooting

- Check the server logs for any errors related to processing SMS messages
- Verify that the Twilio webhook is correctly configured
- Ensure that users have their phone numbers registered in the system
- Check that the phone numbers are in the correct format (e.g., +1XXXXXXXXXX for US numbers)
- Verify that the Twilio account has sufficient funds for sending and receiving messages

## Security Considerations

- The Twilio webhook endpoint is publicly accessible, but it only processes messages from Twilio
- User authentication is based on the phone number, so it's important that users keep their phone numbers up to date
- Media content is downloaded from Twilio's servers using authentication to ensure only authorized access