openapi: 3.0.0
info:
  title: NxtAcre API
  description: API for the NxtAcre Farm Management Platform
  version: 1.0.0
tags:
  - name: System
    description: System-related endpoints
  - name: Authentication
    description: User authentication and authorization
  - name: Users
    description: User management
  - name: Farms
    description: Farm management
  - name: Fields
    description: Field management
  - name: Equipment
    description: Equipment management
  - name: Inventory
    description: Inventory management
  - name: Products
    description: Product management
  - name: Weather
    description: Weather data
  - name: Transport
    description: Transport management (deliveries, pickups, drivers)
  - name: Receipts
    description: Receipt management
servers:
  - url: https://api.nxtacre.com
    description: Production API server
paths:
  /health:
    get:
      tags:
        - System
      summary: Health check endpoint
      description: Returns the health status of the API and database
      responses:
        '200':
          description: Server is running
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: ok
                  message:
                    type: string
                    example: Server is running
                  database:
                    type: object

  # Auth Routes
  /auth/register:
    post:
      tags:
        - Authentication
      summary: Register a new user
      description: Creates a new user account
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - email
                - password
                - firstName
                - lastName
              properties:
                email:
                  type: string
                  format: email
                password:
                  type: string
                  format: password
                firstName:
                  type: string
                lastName:
                  type: string
                phone:
                  type: string
      responses:
        '201':
          description: User registered successfully
        '400':
          description: Invalid input
        '409':
          description: Email already exists

  /auth/login:
    post:
      tags:
        - Authentication
      summary: User login
      description: Authenticates a user and returns a JWT token
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - email
                - password
              properties:
                email:
                  type: string
                  format: email
                password:
                  type: string
                  format: password
      responses:
        '200':
          description: Login successful
        '401':
          description: Invalid credentials

  /auth/logout:
    post:
      tags:
        - Authentication
      summary: User logout
      description: Logs out the current user
      security:
        - bearerAuth: []
      responses:
        '200':
          description: Logout successful

  /auth/verify-2fa:
    post:
      tags:
        - Authentication
      summary: Verify two-factor authentication
      description: Verifies the two-factor authentication code
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - userId
                - code
              properties:
                userId:
                  type: integer
                code:
                  type: string
      responses:
        '200':
          description: Two-factor authentication verified
        '401':
          description: Invalid code

  /auth/forgot-password:
    post:
      tags:
        - Authentication
      summary: Forgot password
      description: Sends a password reset email to the user
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - email
              properties:
                email:
                  type: string
                  format: email
      responses:
        '200':
          description: Password reset email sent
        '404':
          description: User not found

  /auth/reset-password:
    post:
      tags:
        - Authentication
      summary: Reset password
      description: Resets the user's password using a reset token
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - token
                - password
              properties:
                token:
                  type: string
                password:
                  type: string
                  format: password
      responses:
        '200':
          description: Password reset successful
        '400':
          description: Invalid token

  /auth/refresh-token:
    post:
      tags:
        - Authentication
      summary: Refresh token
      description: Refreshes the JWT token
      security:
        - bearerAuth: []
      responses:
        '200':
          description: Token refreshed
        '401':
          description: Invalid refresh token

  /auth/check-auth:
    get:
      tags:
        - Authentication
      summary: Check authentication
      description: Checks if the user is authenticated
      security:
        - bearerAuth: []
      responses:
        '200':
          description: User is authenticated
        '401':
          description: User is not authenticated

  /auth/send-sms-2fa-code:
    post:
      tags:
        - Authentication
      summary: Send SMS 2FA code
      description: Sends a two-factor authentication code via SMS
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - userId
              properties:
                userId:
                  type: integer
      responses:
        '200':
          description: SMS code sent
        '400':
          description: Invalid input
        '404':
          description: User not found

  /auth/verify-sms-2fa:
    post:
      tags:
        - Authentication
      summary: Verify SMS 2FA code
      description: Verifies the SMS two-factor authentication code
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - userId
                - code
              properties:
                userId:
                  type: integer
                code:
                  type: string
      responses:
        '200':
          description: SMS code verified
        '400':
          description: Invalid code
        '404':
          description: User not found

  /auth/send-email-2fa-code:
    post:
      tags:
        - Authentication
      summary: Send Email 2FA code
      description: Sends a two-factor authentication code via email
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - userId
              properties:
                userId:
                  type: integer
      responses:
        '200':
          description: Email code sent
        '400':
          description: Invalid input
        '404':
          description: User not found

  /auth/verify-email-2fa:
    post:
      tags:
        - Authentication
      summary: Verify Email 2FA code
      description: Verifies the email two-factor authentication code
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - userId
                - code
              properties:
                userId:
                  type: integer
                code:
                  type: string
      responses:
        '200':
          description: Email code verified
        '400':
          description: Invalid code
        '404':
          description: User not found

  /auth/verify-email/{token}:
    get:
      tags:
        - Authentication
      summary: Verify email with token
      description: Verifies a user's email address using a token
      parameters:
        - name: token
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Email verified
        '400':
          description: Invalid token
        '404':
          description: User not found

  /auth/verify-email-code:
    post:
      tags:
        - Authentication
      summary: Verify email with code
      description: Verifies a user's email address using a code
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - email
                - code
              properties:
                email:
                  type: string
                  format: email
                code:
                  type: string
      responses:
        '200':
          description: Email verified
        '400':
          description: Invalid code
        '404':
          description: User not found

  /auth/resend-email-verification:
    post:
      tags:
        - Authentication
      summary: Resend email verification
      description: Resends the email verification link
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - email
              properties:
                email:
                  type: string
                  format: email
      responses:
        '200':
          description: Email verification sent
        '404':
          description: User not found

  /auth/available-2fa-methods/{userId}:
    get:
      tags:
        - Authentication
      summary: Get available 2FA methods
      description: Returns the available two-factor authentication methods for a user
      parameters:
        - name: userId
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Available 2FA methods
          content:
            application/json:
              schema:
                type: object
                properties:
                  methods:
                    type: array
                    items:
                      type: string
                      enum: [sms, email, app]
        '404':
          description: User not found

  /auth/setup-2fa/{userId}:
    get:
      tags:
        - Authentication
      summary: Setup two-factor authentication
      description: Sets up two-factor authentication for a user
      parameters:
        - name: userId
          in: path
          required: true
          schema:
            type: integer
      security:
        - bearerAuth: []
      responses:
        '200':
          description: Two-factor authentication setup
        '401':
          description: Unauthorized

  /auth/confirm-2fa:
    post:
      tags:
        - Authentication
      summary: Confirm two-factor authentication
      description: Confirms two-factor authentication setup
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - userId
                - code
              properties:
                userId:
                  type: integer
                code:
                  type: string
      responses:
        '200':
          description: Two-factor authentication confirmed
        '401':
          description: Invalid code

  /auth/disable-2fa:
    post:
      tags:
        - Authentication
      summary: Disable two-factor authentication
      description: Disables two-factor authentication for a user
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - userId
              properties:
                userId:
                  type: integer
      responses:
        '200':
          description: Two-factor authentication disabled
        '401':
          description: Unauthorized

  # Farm Routes
  /farms:
    get:
      tags:
        - Farms
      summary: Get all farms
      description: Returns a list of all farms the user has access to
      security:
        - bearerAuth: []
      responses:
        '200':
          description: List of farms
    post:
      tags:
        - Farms
      summary: Create a new farm
      description: Creates a new farm
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - name
              properties:
                name:
                  type: string
                address:
                  type: string
                city:
                  type: string
                state:
                  type: string
                zipCode:
                  type: string
                country:
                  type: string
      responses:
        '201':
          description: Farm created successfully
        '400':
          description: Invalid input

  /farms/register:
    post:
      tags:
        - Farms
      summary: Create a new farm during registration
      description: Creates a new farm during user registration (no authentication required)
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - name
              properties:
                name:
                  type: string
                address:
                  type: string
                city:
                  type: string
                state:
                  type: string
                zipCode:
                  type: string
                country:
                  type: string
      responses:
        '201':
          description: Farm created successfully
        '400':
          description: Invalid input

  /farms/subdomain/{subdomain}/check:
    get:
      tags:
        - Farms
      summary: Check if a subdomain is available
      description: Checks if a subdomain is available for use
      parameters:
        - name: subdomain
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Subdomain availability status
          content:
            application/json:
              schema:
                type: object
                properties:
                  available:
                    type: boolean

  /farms/by-subdomain/{subdomain}:
    get:
      tags:
        - Farms
      summary: Get farm details by subdomain
      description: Returns farm details by subdomain (public endpoint for login page)
      parameters:
        - name: subdomain
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Farm details
          content:
            application/json:
              schema:
                type: object
                properties:
                  id:
                    type: integer
                  name:
                    type: string
                  logo:
                    type: string
        '404':
          description: Farm not found

  /farms/public/{farmId}:
    get:
      tags:
        - Farms
      summary: Get public farm details
      description: Returns public details for a farm (for debugging)
      parameters:
        - name: farmId
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Farm details
          content:
            application/json:
              schema:
                type: object
                properties:
                  id:
                    type: integer
                  name:
                    type: string
                  address:
                    type: string
                  city:
                    type: string
                  state:
                    type: string
                  zipCode:
                    type: string
                  country:
                    type: string
        '404':
          description: Farm not found

  # Field Routes
  /fields:
    get:
      tags:
        - Fields
      summary: Get all fields
      description: Returns a list of all fields
      security:
        - bearerAuth: []
      responses:
        '200':
          description: List of fields
        '401':
          description: Unauthorized
    post:
      tags:
        - Fields
      summary: Create a new field
      description: Creates a new field
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - name
                - farmId
              properties:
                name:
                  type: string
                farmId:
                  type: integer
                acres:
                  type: number
                boundaries:
                  type: array
                  items:
                    type: object
      responses:
        '201':
          description: Field created successfully
        '400':
          description: Invalid input
        '401':
          description: Unauthorized

  /fields/farm/{farmId}:
    get:
      tags:
        - Fields
      summary: Get all fields for a farm
      description: Returns a list of all fields for a specific farm
      security:
        - bearerAuth: []
      parameters:
        - name: farmId
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: List of fields
        '401':
          description: Unauthorized
        '404':
          description: Farm not found

  /fields/farm/{farmId}/crop-type/{cropType}:
    get:
      tags:
        - Fields
      summary: Get fields by crop type
      description: Returns a list of fields with a specific crop type
      security:
        - bearerAuth: []
      parameters:
        - name: farmId
          in: path
          required: true
          schema:
            type: integer
        - name: cropType
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: List of fields
        '401':
          description: Unauthorized
        '404':
          description: Farm not found

  /fields/{fieldId}:
    get:
      tags:
        - Fields
      summary: Get field by ID
      description: Returns a field by ID
      security:
        - bearerAuth: []
      parameters:
        - name: fieldId
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Field details
        '401':
          description: Unauthorized
        '404':
          description: Field not found
    put:
      tags:
        - Fields
      summary: Update field
      description: Updates a field
      security:
        - bearerAuth: []
      parameters:
        - name: fieldId
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                acres:
                  type: number
                boundaries:
                  type: array
                  items:
                    type: object
      responses:
        '200':
          description: Field updated
        '400':
          description: Invalid input
        '401':
          description: Unauthorized
        '404':
          description: Field not found
    delete:
      tags:
        - Fields
      summary: Delete field
      description: Deletes a field
      security:
        - bearerAuth: []
      parameters:
        - name: fieldId
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Field deleted
        '401':
          description: Unauthorized
        '404':
          description: Field not found

  /fields/{fieldId}/crop-type:
    patch:
      tags:
        - Fields
      summary: Update field crop type
      description: Updates the crop type for a field
      security:
        - bearerAuth: []
      parameters:
        - name: fieldId
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - cropType
              properties:
                cropType:
                  type: string
      responses:
        '200':
          description: Field crop type updated
        '400':
          description: Invalid input
        '401':
          description: Unauthorized
        '404':
          description: Field not found

  /fields/{fieldId}/data-gov:
    get:
      tags:
        - Fields
      summary: Get field data from data.gov
      description: Returns field data from data.gov for a specific field
      security:
        - bearerAuth: []
      parameters:
        - name: fieldId
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Field data from data.gov
        '401':
          description: Unauthorized
        '404':
          description: Field not found

  /fields/farm/{farmId}/data-gov:
    get:
      tags:
        - Fields
      summary: Get field data from data.gov for a farm
      description: Returns field data from data.gov for all fields in a farm
      security:
        - bearerAuth: []
      parameters:
        - name: farmId
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Field data from data.gov
        '401':
          description: Unauthorized
        '404':
          description: Farm not found

  /fields/{fieldId}/recommended-crops:
    get:
      tags:
        - Fields
      summary: Get recommended crops for a field
      description: Returns recommended crops for a field based on data.gov information
      security:
        - bearerAuth: []
      parameters:
        - name: fieldId
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Recommended crops
        '401':
          description: Unauthorized
        '404':
          description: Field not found

  /fields/{fieldId}/conservation-practices:
    get:
      tags:
        - Fields
      summary: Get conservation practices for a field
      description: Returns conservation practices for a field based on data.gov information
      security:
        - bearerAuth: []
      parameters:
        - name: fieldId
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Conservation practices
        '401':
          description: Unauthorized
        '404':
          description: Field not found

  /fields/{fieldId}/historical-yield:
    get:
      tags:
        - Fields
      summary: Get historical yield data for a field
      description: Returns historical yield data for a field based on data.gov information
      security:
        - bearerAuth: []
      parameters:
        - name: fieldId
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Historical yield data
        '401':
          description: Unauthorized
        '404':
          description: Field not found

  /fields/{fieldId}/all-data:
    get:
      tags:
        - Fields
      summary: Get all field data
      description: Returns all field data in a single request
      security:
        - bearerAuth: []
      parameters:
        - name: fieldId
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: All field data
        '401':
          description: Unauthorized
        '404':
          description: Field not found

  /fields/{fieldId}/harvest-direction-maps:
    get:
      tags:
        - Fields
      summary: Get harvest direction maps for a field
      description: Returns all harvest direction maps for a field
      security:
        - bearerAuth: []
      parameters:
        - name: fieldId
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Harvest direction maps
        '401':
          description: Unauthorized
        '404':
          description: Field not found
    post:
      tags:
        - Fields
      summary: Create harvest direction map for a field
      description: Creates a new harvest direction map for a specific field
      security:
        - bearerAuth: []
      parameters:
        - name: fieldId
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - name
                - directions
              properties:
                name:
                  type: string
                directions:
                  type: array
                  items:
                    type: object
                    properties:
                      startPoint:
                        type: object
                        properties:
                          lat:
                            type: number
                          lng:
                            type: number
                      endPoint:
                        type: object
                        properties:
                          lat:
                            type: number
                          lng:
                            type: number
                notes:
                  type: string
      responses:
        '201':
          description: Harvest direction map created
        '400':
          description: Invalid input
        '401':
          description: Unauthorized
        '404':
          description: Field not found

  /fields/harvest-direction-maps/{mapId}:
    get:
      tags:
        - Fields
      summary: Get harvest direction map by ID
      description: Returns a harvest direction map by ID
      security:
        - bearerAuth: []
      parameters:
        - name: mapId
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Harvest direction map
        '401':
          description: Unauthorized
        '404':
          description: Harvest direction map not found
    put:
      tags:
        - Fields
      summary: Update harvest direction map
      description: Updates a harvest direction map
      security:
        - bearerAuth: []
      parameters:
        - name: mapId
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                directions:
                  type: array
                  items:
                    type: object
                    properties:
                      startPoint:
                        type: object
                        properties:
                          lat:
                            type: number
                          lng:
                            type: number
                      endPoint:
                        type: object
                        properties:
                          lat:
                            type: number
                          lng:
                            type: number
                notes:
                  type: string
      responses:
        '200':
          description: Harvest direction map updated
        '400':
          description: Invalid input
        '401':
          description: Unauthorized
        '404':
          description: Harvest direction map not found
    delete:
      tags:
        - Fields
      summary: Delete harvest direction map
      description: Deletes a harvest direction map
      security:
        - bearerAuth: []
      parameters:
        - name: mapId
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Harvest direction map deleted
        '401':
          description: Unauthorized
        '404':
          description: Harvest direction map not found

  # Equipment Routes
  /equipment:
    get:
      tags:
        - Equipment
      summary: Get all equipment
      description: Returns a list of all equipment
      security:
        - bearerAuth: []
      responses:
        '200':
          description: List of equipment
    post:
      tags:
        - Equipment
      summary: Create new equipment
      description: Creates a new equipment item
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - name
                - farmId
              properties:
                name:
                  type: string
                farmId:
                  type: integer
                make:
                  type: string
                model:
                  type: string
                year:
                  type: integer
      responses:
        '201':
          description: Equipment created successfully
        '400':
          description: Invalid input

  # User Routes
  /users/me:
    get:
      tags:
        - Users
      summary: Get current user
      description: Returns the currently authenticated user
      security:
        - bearerAuth: []
      responses:
        '200':
          description: Current user
        '401':
          description: Unauthorized
    patch:
      tags:
        - Users
      summary: Update current user
      description: Updates the currently authenticated user
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                firstName:
                  type: string
                lastName:
                  type: string
                email:
                  type: string
                  format: email
                phone:
                  type: string
      responses:
        '200':
          description: User updated
        '401':
          description: Unauthorized
        '400':
          description: Invalid input

  /users:
    get:
      tags:
        - Users
      summary: Get all users
      description: Returns a list of all users
      security:
        - bearerAuth: []
      responses:
        '200':
          description: List of users
        '401':
          description: Unauthorized

  /users/pending-approval:
    get:
      tags:
        - Users
      summary: Get users pending approval
      description: Returns a list of users pending approval
      security:
        - bearerAuth: []
      responses:
        '200':
          description: List of users pending approval
        '401':
          description: Unauthorized

  /users/{userId}:
    get:
      tags:
        - Users
      summary: Get user by ID
      description: Returns a user by ID
      security:
        - bearerAuth: []
      parameters:
        - name: userId
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: User
        '401':
          description: Unauthorized
        '404':
          description: User not found
    put:
      tags:
        - Users
      summary: Update user
      description: Updates a user
      security:
        - bearerAuth: []
      parameters:
        - name: userId
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                firstName:
                  type: string
                lastName:
                  type: string
                email:
                  type: string
                  format: email
                phone:
                  type: string
                role:
                  type: string
      responses:
        '200':
          description: User updated
        '401':
          description: Unauthorized
        '404':
          description: User not found
    delete:
      tags:
        - Users
      summary: Delete user
      description: Deletes a user
      security:
        - bearerAuth: []
      parameters:
        - name: userId
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: User deleted
        '401':
          description: Unauthorized
        '404':
          description: User not found

  /users/{userId}/details:
    get:
      tags:
        - Users
      summary: Get user details
      description: Returns detailed information about a user
      security:
        - bearerAuth: []
      parameters:
        - name: userId
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: User details
        '401':
          description: Unauthorized
        '404':
          description: User not found

  /users/{userId}/approve:
    post:
      tags:
        - Users
      summary: Approve business user
      description: Approves a business user
      security:
        - bearerAuth: []
      parameters:
        - name: userId
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: User approved
        '401':
          description: Unauthorized
        '404':
          description: User not found

  # User-Farm Routes
  /user-farms:
    post:
      tags:
        - Users
        - Farms
      summary: Create user-farm association
      description: Creates a new user-farm association
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - userId
                - farmId
              properties:
                userId:
                  type: integer
                farmId:
                  type: integer
                role:
                  type: string
      responses:
        '201':
          description: User-farm association created
        '401':
          description: Unauthorized
        '400':
          description: Invalid input

  /user-farms/{userFarmId}:
    put:
      tags:
        - Users
        - Farms
      summary: Update user-farm association
      description: Updates a user-farm association
      security:
        - bearerAuth: []
      parameters:
        - name: userFarmId
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                role:
                  type: string
      responses:
        '200':
          description: User-farm association updated
        '401':
          description: Unauthorized
        '404':
          description: User-farm association not found
    delete:
      tags:
        - Users
        - Farms
      summary: Delete user-farm association
      description: Deletes a user-farm association
      security:
        - bearerAuth: []
      parameters:
        - name: userFarmId
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: User-farm association deleted
        '401':
          description: Unauthorized
        '404':
          description: User-farm association not found

  /user-farms/user/{userId}:
    get:
      tags:
        - Users
        - Farms
      summary: Get user-farm associations by user ID
      description: Returns a list of user-farm associations for a user
      security:
        - bearerAuth: []
      parameters:
        - name: userId
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: List of user-farm associations
        '401':
          description: Unauthorized
        '404':
          description: User not found

  /user-farms/farm/{farmId}:
    get:
      tags:
        - Users
        - Farms
      summary: Get user-farm associations by farm ID
      description: Returns a list of user-farm associations for a farm
      security:
        - bearerAuth: []
      parameters:
        - name: farmId
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: List of user-farm associations
        '401':
          description: Unauthorized
        '404':
          description: Farm not found

  # Inventory Routes
  /inventory/categories:
    get:
      tags:
        - Inventory
      summary: Get all inventory categories
      description: Returns a list of all inventory categories
      security:
        - bearerAuth: []
      responses:
        '200':
          description: List of inventory categories
        '401':
          description: Unauthorized
    post:
      tags:
        - Inventory
      summary: Create inventory category
      description: Creates a new inventory category
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - name
              properties:
                name:
                  type: string
                description:
                  type: string
                farmId:
                  type: integer
      responses:
        '201':
          description: Inventory category created
        '401':
          description: Unauthorized
        '400':
          description: Invalid input

  /inventory/categories/{id}:
    get:
      tags:
        - Inventory
      summary: Get inventory category by ID
      description: Returns an inventory category by ID
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Inventory category
        '401':
          description: Unauthorized
        '404':
          description: Inventory category not found
    put:
      tags:
        - Inventory
      summary: Update inventory category
      description: Updates an inventory category
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                description:
                  type: string
      responses:
        '200':
          description: Inventory category updated
        '401':
          description: Unauthorized
        '404':
          description: Inventory category not found
    delete:
      tags:
        - Inventory
      summary: Delete inventory category
      description: Deletes an inventory category
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Inventory category deleted
        '401':
          description: Unauthorized
        '404':
          description: Inventory category not found

  /inventory:
    get:
      tags:
        - Inventory
      summary: Get all inventory items
      description: Returns a list of all inventory items
      security:
        - bearerAuth: []
      responses:
        '200':
          description: List of inventory items
        '401':
          description: Unauthorized
    post:
      tags:
        - Inventory
      summary: Create inventory item
      description: Creates a new inventory item
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - name
                - categoryId
              properties:
                name:
                  type: string
                description:
                  type: string
                categoryId:
                  type: integer
                farmId:
                  type: integer
                quantity:
                  type: number
                unit:
                  type: string
                barcode:
                  type: string
                qrCode:
                  type: string
                location:
                  type: string
                purchaseDate:
                  type: string
                  format: date
                purchasePrice:
                  type: number
                supplier:
                  type: string
      responses:
        '201':
          description: Inventory item created
        '401':
          description: Unauthorized
        '400':
          description: Invalid input

  /inventory/{id}:
    get:
      tags:
        - Inventory
      summary: Get inventory item by ID
      description: Returns an inventory item by ID
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Inventory item
        '401':
          description: Unauthorized
        '404':
          description: Inventory item not found
    put:
      tags:
        - Inventory
      summary: Update inventory item
      description: Updates an inventory item
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                description:
                  type: string
                categoryId:
                  type: integer
                quantity:
                  type: number
                unit:
                  type: string
                barcode:
                  type: string
                qrCode:
                  type: string
                location:
                  type: string
      responses:
        '200':
          description: Inventory item updated
        '401':
          description: Unauthorized
        '404':
          description: Inventory item not found
    delete:
      tags:
        - Inventory
      summary: Delete inventory item
      description: Deletes an inventory item
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Inventory item deleted
        '401':
          description: Unauthorized
        '404':
          description: Inventory item not found

  /inventory/barcode/{barcode}:
    get:
      tags:
        - Inventory
      summary: Get inventory item by barcode
      description: Returns an inventory item by barcode
      security:
        - bearerAuth: []
      parameters:
        - name: barcode
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Inventory item
        '401':
          description: Unauthorized
        '404':
          description: Inventory item not found

  /inventory/qrcode/{qrCode}:
    get:
      tags:
        - Inventory
      summary: Get inventory item by QR code
      description: Returns an inventory item by QR code
      security:
        - bearerAuth: []
      parameters:
        - name: qrCode
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Inventory item
        '401':
          description: Unauthorized
        '404':
          description: Inventory item not found

  /inventory/{id}/generate-qrcode:
    post:
      tags:
        - Inventory
      summary: Generate QR code for inventory item
      description: Generates a QR code for an inventory item
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: QR code generated
        '401':
          description: Unauthorized
        '404':
          description: Inventory item not found

  /inventory/transactions:
    get:
      tags:
        - Inventory
      summary: Get all inventory transactions
      description: Returns a list of all inventory transactions
      security:
        - bearerAuth: []
      responses:
        '200':
          description: List of inventory transactions
        '401':
          description: Unauthorized
    post:
      tags:
        - Inventory
      summary: Create inventory transaction
      description: Creates a new inventory transaction
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - inventoryItemId
                - type
                - quantity
              properties:
                inventoryItemId:
                  type: integer
                type:
                  type: string
                  enum: [purchase, sale, adjustment, transfer]
                quantity:
                  type: number
                date:
                  type: string
                  format: date-time
                notes:
                  type: string
                price:
                  type: number
                fromLocation:
                  type: string
                toLocation:
                  type: string
      responses:
        '201':
          description: Inventory transaction created
        '401':
          description: Unauthorized
        '400':
          description: Invalid input

  /inventory/transactions/{id}:
    get:
      tags:
        - Inventory
      summary: Get inventory transaction by ID
      description: Returns an inventory transaction by ID
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Inventory transaction
        '401':
          description: Unauthorized
        '404':
          description: Inventory transaction not found
    put:
      tags:
        - Inventory
      summary: Update inventory transaction
      description: Updates an inventory transaction
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                type:
                  type: string
                  enum: [purchase, sale, adjustment, transfer]
                quantity:
                  type: number
                date:
                  type: string
                  format: date-time
                notes:
                  type: string
                price:
                  type: number
                fromLocation:
                  type: string
                toLocation:
                  type: string
      responses:
        '200':
          description: Inventory transaction updated
        '401':
          description: Unauthorized
        '404':
          description: Inventory transaction not found
    delete:
      tags:
        - Inventory
      summary: Delete inventory transaction
      description: Deletes an inventory transaction
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Inventory transaction deleted
        '401':
          description: Unauthorized
        '404':
          description: Inventory transaction not found

  # Delivery Routes
  /deliveries:
    get:
      tags:
        - Transport
      summary: Get all deliveries
      description: Returns a list of all deliveries
      security:
        - bearerAuth: []
      responses:
        '200':
          description: List of deliveries
        '401':
          description: Unauthorized
    post:
      tags:
        - Transport
      summary: Create a new delivery
      description: Creates a new delivery
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - farmId
                - customerId
                - scheduledDate
              properties:
                farmId:
                  type: integer
                customerId:
                  type: integer
                driverId:
                  type: integer
                scheduledDate:
                  type: string
                  format: date-time
                status:
                  type: string
                  enum: [scheduled, in-transit, delivered, cancelled]
                items:
                  type: array
                  items:
                    type: object
                    properties:
                      productId:
                        type: integer
                      quantity:
                        type: number
                notes:
                  type: string
                address:
                  type: string
                city:
                  type: string
                state:
                  type: string
                zipCode:
                  type: string
      responses:
        '201':
          description: Delivery created successfully
        '400':
          description: Invalid input
        '401':
          description: Unauthorized

  /deliveries/{deliveryId}:
    get:
      tags:
        - Transport
      summary: Get delivery by ID
      description: Returns a delivery by ID
      security:
        - bearerAuth: []
      parameters:
        - name: deliveryId
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Delivery details
        '401':
          description: Unauthorized
        '404':
          description: Delivery not found
    put:
      tags:
        - Transport
      summary: Update delivery
      description: Updates a delivery
      security:
        - bearerAuth: []
      parameters:
        - name: deliveryId
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                driverId:
                  type: integer
                scheduledDate:
                  type: string
                  format: date-time
                status:
                  type: string
                  enum: [scheduled, in-transit, delivered, cancelled]
                items:
                  type: array
                  items:
                    type: object
                    properties:
                      productId:
                        type: integer
                      quantity:
                        type: number
                notes:
                  type: string
                address:
                  type: string
                city:
                  type: string
                state:
                  type: string
                zipCode:
                  type: string
      responses:
        '200':
          description: Delivery updated
        '400':
          description: Invalid input
        '401':
          description: Unauthorized
        '404':
          description: Delivery not found
    delete:
      tags:
        - Transport
      summary: Delete delivery
      description: Deletes a delivery
      security:
        - bearerAuth: []
      parameters:
        - name: deliveryId
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Delivery deleted
        '401':
          description: Unauthorized
        '404':
          description: Delivery not found

  /deliveries/{deliveryId}/status:
    patch:
      tags:
        - Transport
      summary: Update delivery status
      description: Updates the status of a delivery
      security:
        - bearerAuth: []
      parameters:
        - name: deliveryId
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - status
              properties:
                status:
                  type: string
                  enum: [scheduled, in-transit, delivered, cancelled]
                notes:
                  type: string
      responses:
        '200':
          description: Delivery status updated
        '400':
          description: Invalid input
        '401':
          description: Unauthorized
        '404':
          description: Delivery not found

  # Driver Routes
  /drivers:
    get:
      tags:
        - Transport
      summary: Get all drivers
      description: Returns a list of all drivers
      security:
        - bearerAuth: []
      responses:
        '200':
          description: List of drivers
        '401':
          description: Unauthorized
    post:
      tags:
        - Transport
      summary: Create a new driver
      description: Creates a new driver
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - userId
                - farmId
              properties:
                userId:
                  type: integer
                farmId:
                  type: integer
                licenseNumber:
                  type: string
                licenseExpiration:
                  type: string
                  format: date
                vehicleType:
                  type: string
                vehicleMake:
                  type: string
                vehicleModel:
                  type: string
                vehicleYear:
                  type: integer
                vehiclePlate:
                  type: string
                status:
                  type: string
                  enum: [active, inactive, on-leave]
      responses:
        '201':
          description: Driver created successfully
        '400':
          description: Invalid input
        '401':
          description: Unauthorized

  /drivers/{driverId}:
    get:
      tags:
        - Transport
      summary: Get driver by ID
      description: Returns a driver by ID
      security:
        - bearerAuth: []
      parameters:
        - name: driverId
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Driver details
        '401':
          description: Unauthorized
        '404':
          description: Driver not found
    put:
      tags:
        - Transport
      summary: Update driver
      description: Updates a driver
      security:
        - bearerAuth: []
      parameters:
        - name: driverId
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                licenseNumber:
                  type: string
                licenseExpiration:
                  type: string
                  format: date
                vehicleType:
                  type: string
                vehicleMake:
                  type: string
                vehicleModel:
                  type: string
                vehicleYear:
                  type: integer
                vehiclePlate:
                  type: string
                status:
                  type: string
                  enum: [active, inactive, on-leave]
      responses:
        '200':
          description: Driver updated
        '400':
          description: Invalid input
        '401':
          description: Unauthorized
        '404':
          description: Driver not found
    delete:
      tags:
        - Transport
      summary: Delete driver
      description: Deletes a driver
      security:
        - bearerAuth: []
      parameters:
        - name: driverId
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Driver deleted
        '401':
          description: Unauthorized
        '404':
          description: Driver not found

  # Driver Location Routes
  /driver-locations:
    get:
      tags:
        - Transport
      summary: Get all driver locations
      description: Returns a list of all driver locations
      security:
        - bearerAuth: []
      responses:
        '200':
          description: List of driver locations
        '401':
          description: Unauthorized
    post:
      tags:
        - Transport
      summary: Create a new driver location
      description: Creates a new driver location entry
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - driverId
                - latitude
                - longitude
              properties:
                driverId:
                  type: integer
                latitude:
                  type: number
                  format: float
                longitude:
                  type: number
                  format: float
                accuracy:
                  type: number
                  format: float
                altitude:
                  type: number
                  format: float
                speed:
                  type: number
                  format: float
                heading:
                  type: number
                  format: float
                timestamp:
                  type: string
                  format: date-time
      responses:
        '201':
          description: Driver location created successfully
        '400':
          description: Invalid input
        '401':
          description: Unauthorized

  /driver-locations/driver/{driverId}/latest:
    get:
      tags:
        - Transport
      summary: Get latest driver location
      description: Returns the latest location for a driver
      security:
        - bearerAuth: []
      parameters:
        - name: driverId
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Latest driver location
        '401':
          description: Unauthorized
        '404':
          description: Driver location not found

  /driver-locations/driver/{driverId}/history:
    get:
      tags:
        - Transport
      summary: Get driver location history
      description: Returns location history for a driver
      security:
        - bearerAuth: []
      parameters:
        - name: driverId
          in: path
          required: true
          schema:
            type: integer
        - name: startDate
          in: query
          schema:
            type: string
            format: date-time
        - name: endDate
          in: query
          schema:
            type: string
            format: date-time
      responses:
        '200':
          description: Driver location history
        '401':
          description: Unauthorized
        '404':
          description: Driver not found
    delete:
      tags:
        - Transport
      summary: Delete driver location history
      description: Deletes location history for a driver
      security:
        - bearerAuth: []
      parameters:
        - name: driverId
          in: path
          required: true
          schema:
            type: integer
        - name: startDate
          in: query
          schema:
            type: string
            format: date-time
        - name: endDate
          in: query
          schema:
            type: string
            format: date-time
      responses:
        '200':
          description: Driver location history deleted
        '401':
          description: Unauthorized
        '404':
          description: Driver not found

  /driver-locations/{locationId}:
    delete:
      tags:
        - Transport
      summary: Delete driver location
      description: Deletes a specific driver location
      security:
        - bearerAuth: []
      parameters:
        - name: locationId
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Driver location deleted
        '401':
          description: Unauthorized
        '404':
          description: Driver location not found

  # Driver Schedule Routes
  /driver-schedules:
    get:
      tags:
        - Transport
      summary: Get all driver schedules
      description: Returns a list of all driver schedules
      security:
        - bearerAuth: []
      responses:
        '200':
          description: List of driver schedules
        '401':
          description: Unauthorized
    post:
      tags:
        - Transport
      summary: Create a new driver schedule
      description: Creates a new driver schedule
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - driverId
                - startTime
                - endTime
              properties:
                driverId:
                  type: integer
                startTime:
                  type: string
                  format: date-time
                endTime:
                  type: string
                  format: date-time
                status:
                  type: string
                  enum: [scheduled, in-progress, completed, cancelled]
                notes:
                  type: string
      responses:
        '201':
          description: Driver schedule created successfully
        '400':
          description: Invalid input
        '401':
          description: Unauthorized

  /driver-schedules/driver/{driverId}:
    get:
      tags:
        - Transport
      summary: Get driver schedules by driver ID
      description: Returns schedules for a specific driver
      security:
        - bearerAuth: []
      parameters:
        - name: driverId
          in: path
          required: true
          schema:
            type: integer
        - name: startDate
          in: query
          schema:
            type: string
            format: date
        - name: endDate
          in: query
          schema:
            type: string
            format: date
      responses:
        '200':
          description: List of driver schedules
        '401':
          description: Unauthorized
        '404':
          description: Driver not found

  /driver-schedules/{scheduleId}:
    get:
      tags:
        - Transport
      summary: Get driver schedule by ID
      description: Returns a driver schedule by ID
      security:
        - bearerAuth: []
      parameters:
        - name: scheduleId
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Driver schedule details
        '401':
          description: Unauthorized
        '404':
          description: Driver schedule not found
    put:
      tags:
        - Transport
      summary: Update driver schedule
      description: Updates a driver schedule
      security:
        - bearerAuth: []
      parameters:
        - name: scheduleId
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                driverId:
                  type: integer
                startTime:
                  type: string
                  format: date-time
                endTime:
                  type: string
                  format: date-time
                status:
                  type: string
                  enum: [scheduled, in-progress, completed, cancelled]
                notes:
                  type: string
      responses:
        '200':
          description: Driver schedule updated
        '400':
          description: Invalid input
        '401':
          description: Unauthorized
        '404':
          description: Driver schedule not found
    delete:
      tags:
        - Transport
      summary: Delete driver schedule
      description: Deletes a driver schedule
      security:
        - bearerAuth: []
      parameters:
        - name: scheduleId
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Driver schedule deleted
        '401':
          description: Unauthorized
        '404':
          description: Driver schedule not found

  # Pickup Routes
  /pickups:
    get:
      tags:
        - Transport
      summary: Get all pickups
      description: Returns a list of all pickups
      security:
        - bearerAuth: []
      responses:
        '200':
          description: List of pickups
        '401':
          description: Unauthorized
    post:
      tags:
        - Transport
      summary: Create a new pickup
      description: Creates a new pickup
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - farmId
                - supplierId
                - scheduledDate
              properties:
                farmId:
                  type: integer
                supplierId:
                  type: integer
                driverId:
                  type: integer
                scheduledDate:
                  type: string
                  format: date-time
                status:
                  type: string
                  enum: [scheduled, in-transit, completed, cancelled]
                items:
                  type: array
                  items:
                    type: object
                    properties:
                      productId:
                        type: integer
                      quantity:
                        type: number
                notes:
                  type: string
                address:
                  type: string
                city:
                  type: string
                state:
                  type: string
                zipCode:
                  type: string
      responses:
        '201':
          description: Pickup created successfully
        '400':
          description: Invalid input
        '401':
          description: Unauthorized

  /pickups/{pickupId}:
    get:
      tags:
        - Transport
      summary: Get pickup by ID
      description: Returns a pickup by ID
      security:
        - bearerAuth: []
      parameters:
        - name: pickupId
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Pickup details
        '401':
          description: Unauthorized
        '404':
          description: Pickup not found
    put:
      tags:
        - Transport
      summary: Update pickup
      description: Updates a pickup
      security:
        - bearerAuth: []
      parameters:
        - name: pickupId
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                driverId:
                  type: integer
                scheduledDate:
                  type: string
                  format: date-time
                status:
                  type: string
                  enum: [scheduled, in-transit, completed, cancelled]
                items:
                  type: array
                  items:
                    type: object
                    properties:
                      productId:
                        type: integer
                      quantity:
                        type: number
                notes:
                  type: string
                address:
                  type: string
                city:
                  type: string
                state:
                  type: string
                zipCode:
                  type: string
      responses:
        '200':
          description: Pickup updated
        '400':
          description: Invalid input
        '401':
          description: Unauthorized
        '404':
          description: Pickup not found
    delete:
      tags:
        - Transport
      summary: Delete pickup
      description: Deletes a pickup
      security:
        - bearerAuth: []
      parameters:
        - name: pickupId
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Pickup deleted
        '401':
          description: Unauthorized
        '404':
          description: Pickup not found

  /pickups/{pickupId}/status:
    patch:
      tags:
        - Transport
      summary: Update pickup status
      description: Updates the status of a pickup
      security:
        - bearerAuth: []
      parameters:
        - name: pickupId
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - status
              properties:
                status:
                  type: string
                  enum: [scheduled, in-transit, completed, cancelled]
                notes:
                  type: string
      responses:
        '200':
          description: Pickup status updated
        '400':
          description: Invalid input
        '401':
          description: Unauthorized
        '404':
          description: Pickup not found

  # Receipt Routes
  /receipts/farm/{farmId}:
    get:
      tags:
        - Receipts
      summary: Get all receipts for a farm
      description: Returns a list of all receipts for a farm
      security:
        - bearerAuth: []
      parameters:
        - name: farmId
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: List of receipts
        '401':
          description: Unauthorized
        '404':
          description: Farm not found
    post:
      tags:
        - Receipts
      summary: Upload a new receipt
      description: Uploads a new receipt for a farm
      security:
        - bearerAuth: []
      parameters:
        - name: farmId
          in: path
          required: true
          schema:
            type: integer
        - name: date
          in: query
          required: false
          schema:
            type: string
            format: date
        - name: amount
          in: query
          required: false
          schema:
            type: number
        - name: vendor
          in: query
          required: false
          schema:
            type: string
        - name: category
          in: query
          required: false
          schema:
            type: string
        - name: notes
          in: query
          required: false
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - fileData
              properties:
                fileData:
                  type: string
                  description: Base64-encoded receipt image data
      responses:
        '201':
          description: Receipt uploaded successfully
        '400':
          description: Invalid input
        '401':
          description: Unauthorized
        '404':
          description: Farm not found

  /receipts/farm/{farmId}/summary:
    get:
      tags:
        - Receipts
      summary: Get receipt summary for a farm
      description: Returns a summary of receipts for a farm
      security:
        - bearerAuth: []
      parameters:
        - name: farmId
          in: path
          required: true
          schema:
            type: integer
        - name: startDate
          in: query
          schema:
            type: string
            format: date
        - name: endDate
          in: query
          schema:
            type: string
            format: date
      responses:
        '200':
          description: Receipt summary
        '401':
          description: Unauthorized
        '404':
          description: Farm not found

  /receipts/farm/{farmId}/report:
    get:
      tags:
        - Receipts
      summary: Generate expense report
      description: Generates an expense report from receipts for a farm
      security:
        - bearerAuth: []
      parameters:
        - name: farmId
          in: path
          required: true
          schema:
            type: integer
        - name: startDate
          in: query
          schema:
            type: string
            format: date
        - name: endDate
          in: query
          schema:
            type: string
            format: date
        - name: format
          in: query
          schema:
            type: string
            enum: [pdf, csv, xlsx]
      responses:
        '200':
          description: Expense report generated
        '401':
          description: Unauthorized
        '404':
          description: Farm not found

  /receipts/{id}:
    get:
      tags:
        - Receipts
      summary: Get receipt by ID
      description: Returns a receipt by ID
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Receipt details
        '401':
          description: Unauthorized
        '404':
          description: Receipt not found
    put:
      tags:
        - Receipts
      summary: Update receipt
      description: Updates a receipt
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                date:
                  type: string
                  format: date
                amount:
                  type: number
                vendor:
                  type: string
                category:
                  type: string
                notes:
                  type: string
      responses:
        '200':
          description: Receipt updated
        '400':
          description: Invalid input
        '401':
          description: Unauthorized
        '404':
          description: Receipt not found
    delete:
      tags:
        - Receipts
      summary: Delete receipt
      description: Deletes a receipt
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Receipt deleted
        '401':
          description: Unauthorized
        '404':
          description: Receipt not found

  /receipts/{id}/download:
    get:
      tags:
        - Receipts
      summary: Download receipt
      description: Downloads a receipt file
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Receipt file
          content:
            application/octet-stream:
              schema:
                type: string
                format: binary
        '401':
          description: Unauthorized
        '404':
          description: Receipt not found

  /receipts/{id}/ocr:
    post:
      tags:
        - Receipts
      summary: Process receipt with enhanced OCR
      description: Processes a receipt with enhanced OCR to extract data
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: OCR processing completed
        '401':
          description: Unauthorized
        '404':
          description: Receipt not found

  /receipts/{id}/categorize:
    post:
      tags:
        - Receipts
      summary: Categorize receipt using AI
      description: Categorizes a receipt using AI
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Receipt categorized
        '401':
          description: Unauthorized
        '404':
          description: Receipt not found

  /receipts/{id}/match:
    post:
      tags:
        - Receipts
      summary: Match receipt to transactions
      description: Matches a receipt to financial transactions
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Receipt matched to transactions
        '401':
          description: Unauthorized
        '404':
          description: Receipt not found

  /receipts/{receiptId}/link/{expenseId}:
    post:
      tags:
        - Receipts
      summary: Link receipt to expense
      description: Links a receipt to an expense
      security:
        - bearerAuth: []
      parameters:
        - name: receiptId
          in: path
          required: true
          schema:
            type: integer
        - name: expenseId
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Receipt linked to expense
        '401':
          description: Unauthorized
        '404':
          description: Receipt or expense not found

  # Product Routes
  /products:
    get:
      tags:
        - Products
      summary: Get all products
      description: Returns a list of all products for a farm with query parameters
      security:
        - bearerAuth: []
      parameters:
        - name: farmId
          in: query
          schema:
            type: integer
          description: ID of the farm to get products for
        - name: query
          in: query
          schema:
            type: string
          description: Search query to filter products
      responses:
        '200':
          description: List of products
        '401':
          description: Unauthorized
    post:
      tags:
        - Products
      summary: Create a new product
      description: Creates a new product
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - name
                - farmId
              properties:
                name:
                  type: string
                farmId:
                  type: integer
                description:
                  type: string
                price:
                  type: number
                unit:
                  type: string
                sku:
                  type: string
                barcode:
                  type: string
                category:
                  type: string
                tags:
                  type: array
                  items:
                    type: string
                images:
                  type: array
                  items:
                    type: string
      responses:
        '201':
          description: Product created successfully
        '400':
          description: Invalid input
        '401':
          description: Unauthorized

  /products/farm/{farmId}:
    get:
      tags:
        - Products
      summary: Get all products for a farm
      description: Returns a list of all products for a specific farm
      security:
        - bearerAuth: []
      parameters:
        - name: farmId
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: List of products
        '401':
          description: Unauthorized
        '404':
          description: Farm not found

  /products/{productId}:
    get:
      tags:
        - Products
      summary: Get product by ID
      description: Returns a product by ID
      security:
        - bearerAuth: []
      parameters:
        - name: productId
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Product details
        '401':
          description: Unauthorized
        '404':
          description: Product not found
    put:
      tags:
        - Products
      summary: Update product
      description: Updates a product
      security:
        - bearerAuth: []
      parameters:
        - name: productId
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                description:
                  type: string
                price:
                  type: number
                unit:
                  type: string
                sku:
                  type: string
                barcode:
                  type: string
                category:
                  type: string
                tags:
                  type: array
                  items:
                    type: string
                images:
                  type: array
                  items:
                    type: string
      responses:
        '200':
          description: Product updated
        '400':
          description: Invalid input
        '401':
          description: Unauthorized
        '404':
          description: Product not found
    delete:
      tags:
        - Products
      summary: Delete product
      description: Deletes a product
      security:
        - bearerAuth: []
      parameters:
        - name: productId
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Product deleted
        '401':
          description: Unauthorized
        '404':
          description: Product not found

  /products/from-crop/{cropId}:
    post:
      tags:
        - Products
      summary: Create product from crop
      description: Creates a new product from a crop
      security:
        - bearerAuth: []
      parameters:
        - name: cropId
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                price:
                  type: number
                unit:
                  type: string
                description:
                  type: string
      responses:
        '201':
          description: Product created successfully
        '400':
          description: Invalid input
        '401':
          description: Unauthorized
        '404':
          description: Crop not found

  /products/from-livestock/{livestockId}:
    post:
      tags:
        - Products
      summary: Create product from livestock
      description: Creates a new product from livestock
      security:
        - bearerAuth: []
      parameters:
        - name: livestockId
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                price:
                  type: number
                unit:
                  type: string
                description:
                  type: string
      responses:
        '201':
          description: Product created successfully
        '400':
          description: Invalid input
        '401':
          description: Unauthorized
        '404':
          description: Livestock not found

  /products/from-equipment/{equipmentId}:
    post:
      tags:
        - Products
      summary: Create product from equipment
      description: Creates a new product from equipment
      security:
        - bearerAuth: []
      parameters:
        - name: equipmentId
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                price:
                  type: number
                unit:
                  type: string
                description:
                  type: string
      responses:
        '201':
          description: Product created successfully
        '400':
          description: Invalid input
        '401':
          description: Unauthorized
        '404':
          description: Equipment not found

  # Weather Routes
  /weather/farm/{farmId}:
    get:
      tags:
        - Weather
      summary: Get weather data for a farm
      description: Returns current weather data for a farm
      security:
        - bearerAuth: []
      parameters:
        - name: farmId
          in: path
          required: true
          schema:
            type: integer
        - name: provider
          in: query
          schema:
            type: string
            enum: [nws, tomorrow, open-meteo]
          description: Weather data provider
      responses:
        '200':
          description: Weather data
        '401':
          description: Unauthorized
        '404':
          description: Farm not found

  /weather/field/{fieldId}:
    get:
      tags:
        - Weather
      summary: Get weather data for a field
      description: Returns current weather data for a field
      security:
        - bearerAuth: []
      parameters:
        - name: fieldId
          in: path
          required: true
          schema:
            type: integer
        - name: provider
          in: query
          schema:
            type: string
            enum: [nws, tomorrow, open-meteo]
          description: Weather data provider
      responses:
        '200':
          description: Weather data
        '401':
          description: Unauthorized
        '404':
          description: Field not found

  /weather/forecast/farm/{farmId}:
    get:
      tags:
        - Weather
      summary: Get forecast for a farm
      description: Returns detailed forecast for a farm
      security:
        - bearerAuth: []
      parameters:
        - name: farmId
          in: path
          required: true
          schema:
            type: integer
        - name: days
          in: query
          schema:
            type: integer
            default: 7
          description: Number of days to forecast
        - name: provider
          in: query
          schema:
            type: string
            enum: [nws, tomorrow, open-meteo]
          description: Weather data provider
      responses:
        '200':
          description: Forecast data
        '401':
          description: Unauthorized
        '404':
          description: Farm not found

  /weather/forecast/field/{fieldId}:
    get:
      tags:
        - Weather
      summary: Get forecast for a field
      description: Returns detailed forecast for a field
      security:
        - bearerAuth: []
      parameters:
        - name: fieldId
          in: path
          required: true
          schema:
            type: integer
        - name: days
          in: query
          schema:
            type: integer
            default: 7
          description: Number of days to forecast
        - name: provider
          in: query
          schema:
            type: string
            enum: [nws, tomorrow, open-meteo]
          description: Weather data provider
      responses:
        '200':
          description: Forecast data
        '401':
          description: Unauthorized
        '404':
          description: Field not found

  /weather/hourly/farm/{farmId}:
    get:
      tags:
        - Weather
      summary: Get hourly forecast for a farm
      description: Returns hourly forecast for a farm
      security:
        - bearerAuth: []
      parameters:
        - name: farmId
          in: path
          required: true
          schema:
            type: integer
        - name: hours
          in: query
          schema:
            type: integer
            default: 48
          description: Number of hours to forecast
        - name: provider
          in: query
          schema:
            type: string
            enum: [nws, tomorrow, open-meteo]
          description: Weather data provider
      responses:
        '200':
          description: Hourly forecast data
        '401':
          description: Unauthorized
        '404':
          description: Farm not found

  /weather/hourly/field/{fieldId}:
    get:
      tags:
        - Weather
      summary: Get hourly forecast for a field
      description: Returns hourly forecast for a field
      security:
        - bearerAuth: []
      parameters:
        - name: fieldId
          in: path
          required: true
          schema:
            type: integer
        - name: hours
          in: query
          schema:
            type: integer
            default: 48
          description: Number of hours to forecast
        - name: provider
          in: query
          schema:
            type: string
            enum: [nws, tomorrow, open-meteo]
          description: Weather data provider
      responses:
        '200':
          description: Hourly forecast data
        '401':
          description: Unauthorized
        '404':
          description: Field not found

  /weather/all/farm/{farmId}:
    get:
      tags:
        - Weather
      summary: Get all weather data for a farm
      description: Returns all weather data for a farm in a single request
      security:
        - bearerAuth: []
      parameters:
        - name: farmId
          in: path
          required: true
          schema:
            type: integer
        - name: days
          in: query
          schema:
            type: integer
            default: 7
          description: Number of days to forecast
        - name: hours
          in: query
          schema:
            type: integer
            default: 48
          description: Number of hours to forecast
        - name: startDate
          in: query
          schema:
            type: string
            format: date
          description: Start date for historical data
        - name: endDate
          in: query
          schema:
            type: string
            format: date
          description: End date for historical data
        - name: interval
          in: query
          schema:
            type: string
            default: weekly
            enum: [daily, weekly, monthly]
          description: Interval for historical data
        - name: provider
          in: query
          schema:
            type: string
            enum: [nws, tomorrow, open-meteo]
          description: Weather data provider
      responses:
        '200':
          description: All weather data
        '401':
          description: Unauthorized
        '404':
          description: Farm not found

  /weather/all/field/{fieldId}:
    get:
      tags:
        - Weather
      summary: Get all weather data for a field
      description: Returns all weather data for a field in a single request
      security:
        - bearerAuth: []
      parameters:
        - name: fieldId
          in: path
          required: true
          schema:
            type: integer
        - name: days
          in: query
          schema:
            type: integer
            default: 7
          description: Number of days to forecast
        - name: hours
          in: query
          schema:
            type: integer
            default: 48
          description: Number of hours to forecast
        - name: startDate
          in: query
          schema:
            type: string
            format: date
          description: Start date for historical data
        - name: endDate
          in: query
          schema:
            type: string
            format: date
          description: End date for historical data
        - name: interval
          in: query
          schema:
            type: string
            default: weekly
            enum: [daily, weekly, monthly]
          description: Interval for historical data
        - name: provider
          in: query
          schema:
            type: string
            enum: [nws, tomorrow, open-meteo]
          description: Weather data provider
      responses:
        '200':
          description: All weather data
        '401':
          description: Unauthorized
        '404':
          description: Field not found

  # Environment Routes
  /environment/variables:
    get:
      tags:
        - System
      summary: Get environment variables
      description: Returns environment variables (restricted to global admin users)
      security:
        - bearerAuth: []
      responses:
        '200':
          description: Environment variables retrieved successfully
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - Not a global admin

  /environment/frontend-variables:
    get:
      tags:
        - System
      summary: Get frontend environment variables
      description: Returns frontend environment variables (restricted to global admin users)
      security:
        - bearerAuth: []
      responses:
        '200':
          description: Frontend environment variables retrieved successfully
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - Not a global admin

  # Market Routes
  /market/contracts:
    get:
      tags:
        - Market
      summary: Get all contracts
      description: Returns all market contracts for the authenticated user
      security:
        - bearerAuth: []
      responses:
        '200':
          description: Contracts retrieved successfully
        '401':
          description: Unauthorized
    post:
      tags:
        - Market
      summary: Create a new contract
      description: Creates a new market contract
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - farmId
                - buyerId
                - productId
                - quantity
                - price
                - startDate
                - endDate
              properties:
                farmId:
                  type: integer
                buyerId:
                  type: integer
                productId:
                  type: integer
                quantity:
                  type: number
                price:
                  type: number
                startDate:
                  type: string
                  format: date
                endDate:
                  type: string
                  format: date
                terms:
                  type: string
                status:
                  type: string
                  enum: [draft, active, completed, cancelled]
      responses:
        '201':
          description: Contract created successfully
        '400':
          description: Invalid input
        '401':
          description: Unauthorized

  /market/contracts/{id}:
    get:
      tags:
        - Market
      summary: Get a contract by ID
      description: Returns a specific market contract
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
          description: Contract ID
      responses:
        '200':
          description: Contract retrieved successfully
        '401':
          description: Unauthorized
        '404':
          description: Contract not found
    put:
      tags:
        - Market
      summary: Update a contract
      description: Updates an existing market contract
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
          description: Contract ID
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                quantity:
                  type: number
                price:
                  type: number
                startDate:
                  type: string
                  format: date
                endDate:
                  type: string
                  format: date
                terms:
                  type: string
                status:
                  type: string
                  enum: [draft, active, completed, cancelled]
      responses:
        '200':
          description: Contract updated successfully
        '400':
          description: Invalid input
        '401':
          description: Unauthorized
        '404':
          description: Contract not found
    delete:
      tags:
        - Market
      summary: Delete a contract
      description: Deletes a market contract
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
          description: Contract ID
      responses:
        '200':
          description: Contract deleted successfully
        '401':
          description: Unauthorized
        '404':
          description: Contract not found

  # Sustainability Routes
  /sustainability/carbon-footprint:
    get:
      tags:
        - Sustainability
      summary: Get carbon footprints
      description: Returns all carbon footprint calculations for the authenticated user
      security:
        - bearerAuth: []
      responses:
        '200':
          description: Carbon footprints retrieved successfully
        '401':
          description: Unauthorized
    post:
      tags:
        - Sustainability
      summary: Calculate carbon footprint
      description: Calculates and stores carbon footprint data
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - farmId
                - calculationDate
              properties:
                farmId:
                  type: integer
                calculationDate:
                  type: string
                  format: date
                fuelEmissions:
                  type: number
                electricityEmissions:
                  type: number
                fertilizerEmissions:
                  type: number
                livestockEmissions:
                  type: number
                wasteEmissions:
                  type: number
      responses:
        '201':
          description: Carbon footprint calculated successfully
        '400':
          description: Invalid input
        '401':
          description: Unauthorized

  # AI Assistant Routes
  /ai-assistant/query:
    post:
      tags:
        - AI Assistant
      summary: Get AI response for a query
      description: Returns AI assistant response for a user query
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - query
                - farmId
              properties:
                query:
                  type: string
                farmId:
                  type: integer
      responses:
        '200':
          description: AI response retrieved successfully
        '400':
          description: Invalid input
        '401':
          description: Unauthorized

  /ai-assistant/history/{farmId}:
    get:
      tags:
        - AI Assistant
      summary: Get AI query history
      description: Returns AI assistant query history for a farm
      security:
        - bearerAuth: []
      parameters:
        - name: farmId
          in: path
          required: true
          schema:
            type: integer
          description: Farm ID
      responses:
        '200':
          description: Query history retrieved successfully
        '401':
          description: Unauthorized
        '404':
          description: Farm not found

  # AI Configuration Routes
  /ai-configuration/providers:
    get:
      tags:
        - AI Configuration
      summary: Get all AI providers
      description: Returns a list of all AI providers
      security:
        - bearerAuth: []
      responses:
        '200':
          description: List of AI providers
        '401':
          description: Unauthorized
    post:
      tags:
        - AI Configuration
      summary: Create a new AI provider
      description: Creates a new AI provider
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - name
              properties:
                name:
                  type: string
                description:
                  type: string
                api_base_url:
                  type: string
                auth_type:
                  type: string
      responses:
        '201':
          description: AI provider created
        '400':
          description: Invalid input
        '401':
          description: Unauthorized

  /ai-configuration/providers/{id}:
    get:
      tags:
        - AI Configuration
      summary: Get an AI provider by ID
      description: Returns a specific AI provider
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: AI provider details
        '401':
          description: Unauthorized
        '404':
          description: AI provider not found
    put:
      tags:
        - AI Configuration
      summary: Update an AI provider
      description: Updates an existing AI provider
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - name
              properties:
                name:
                  type: string
                description:
                  type: string
                api_base_url:
                  type: string
                auth_type:
                  type: string
                is_enabled:
                  type: boolean
      responses:
        '200':
          description: AI provider updated
        '400':
          description: Invalid input
        '401':
          description: Unauthorized
        '404':
          description: AI provider not found
    delete:
      tags:
        - AI Configuration
      summary: Delete an AI provider
      description: Deletes an AI provider
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: AI provider deleted
        '401':
          description: Unauthorized
        '404':
          description: AI provider not found

  /ai-configuration/models:
    get:
      tags:
        - AI Configuration
      summary: Get all AI models
      description: Returns a list of all AI models
      security:
        - bearerAuth: []
      responses:
        '200':
          description: List of AI models
        '401':
          description: Unauthorized

  /ai-configuration/providers/{providerId}/models:
    get:
      tags:
        - AI Configuration
      summary: Get AI models by provider ID
      description: Returns a list of AI models for a specific provider
      security:
        - bearerAuth: []
      parameters:
        - name: providerId
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: List of AI models for the provider
        '401':
          description: Unauthorized
        '404':
          description: Provider not found

  /ai-configuration/models/{id}:
    get:
      tags:
        - AI Configuration
      summary: Get an AI model by ID
      description: Returns a specific AI model
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: AI model details
        '401':
          description: Unauthorized
        '404':
          description: AI model not found

  # AI Analysis Routes
  /ai-analysis/crop-rotation:
    post:
      tags:
        - AI Analysis
      summary: Generate crop rotation analysis
      description: Generates a crop rotation analysis for a field or farm
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - farmId
              properties:
                farmId:
                  type: integer
                fieldId:
                  type: integer
                cropData:
                  type: object
      responses:
        '201':
          description: Crop rotation analysis generated
        '400':
          description: Invalid input
        '401':
          description: Unauthorized

  /ai-analysis/crop-rotation/{farmId}:
    get:
      tags:
        - AI Analysis
      summary: Get latest crop rotation analysis
      description: Returns the latest crop rotation analysis for a farm or field
      security:
        - bearerAuth: []
      parameters:
        - name: farmId
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Latest crop rotation analysis
        '401':
          description: Unauthorized
        '404':
          description: Farm not found

  /ai-analysis/crop-rotation/{farmId}/all:
    get:
      tags:
        - AI Analysis
      summary: Get all crop rotation analyses
      description: Returns all crop rotation analyses for a farm
      security:
        - bearerAuth: []
      parameters:
        - name: farmId
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: All crop rotation analyses
        '401':
          description: Unauthorized
        '404':
          description: Farm not found

  /ai-analysis/harvest-schedule:
    post:
      tags:
        - AI Analysis
      summary: Generate harvest schedule analysis
      description: Generates a harvest schedule analysis for a field or farm
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - farmId
              properties:
                farmId:
                  type: integer
                fieldId:
                  type: integer
                cropId:
                  type: integer
                harvestData:
                  type: object
      responses:
        '201':
          description: Harvest schedule analysis generated
        '400':
          description: Invalid input
        '401':
          description: Unauthorized

  /ai-analysis/soil-health:
    post:
      tags:
        - AI Analysis
      summary: Generate soil health analysis
      description: Generates a soil health analysis for a field or farm
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - farmId
              properties:
                farmId:
                  type: integer
                fieldId:
                  type: integer
                soilData:
                  type: object
      responses:
        '201':
          description: Soil health analysis generated
        '400':
          description: Invalid input
        '401':
          description: Unauthorized

  /ai-analysis/field-health:
    post:
      tags:
        - AI Analysis
      summary: Generate field health analysis
      description: Generates a field health analysis for a field or farm
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - farmId
              properties:
                farmId:
                  type: integer
                fieldId:
                  type: integer
                cropId:
                  type: integer
                fieldData:
                  type: object
      responses:
        '201':
          description: Field health analysis generated
        '400':
          description: Invalid input
        '401':
          description: Unauthorized

  /ai-analysis/herd-health:
    post:
      tags:
        - AI Analysis
      summary: Generate herd health analysis
      description: Generates a herd health analysis for a livestock group or farm
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - farmId
              properties:
                farmId:
                  type: integer
                livestockGroupId:
                  type: integer
                herdData:
                  type: object
      responses:
        '201':
          description: Herd health analysis generated
        '400':
          description: Invalid input
        '401':
          description: Unauthorized

  # AI Document Generation Routes
  /ai-document-generation/generate:
    post:
      tags:
        - AI Document Generation
      summary: Generate document
      description: Generates a document using AI based on user prompt
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - prompt
                - farmId
              properties:
                prompt:
                  type: string
                farmId:
                  type: integer
                documentType:
                  type: string
      responses:
        '201':
          description: Document generated
        '400':
          description: Invalid input
        '401':
          description: Unauthorized

  /ai-document-generation/save-as-template:
    post:
      tags:
        - AI Document Generation
      summary: Save as template
      description: Saves an AI-generated document as a template
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - documentId
                - farmId
              properties:
                documentId:
                  type: integer
                farmId:
                  type: integer
                title:
                  type: string
                description:
                  type: string
      responses:
        '201':
          description: Document saved as template
        '400':
          description: Invalid input
        '401':
          description: Unauthorized

  # FAQ Routes
  /faqs:
    get:
      tags:
        - System
      summary: Get all FAQs
      description: Returns a list of all FAQs
      responses:
        '200':
          description: List of FAQs
    post:
      tags:
        - System
      summary: Create a new FAQ
      description: Creates a new FAQ (admin only)
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - question
                - answer
                - category
              properties:
                question:
                  type: string
                answer:
                  type: string
                category:
                  type: string
                isPublished:
                  type: boolean
                  default: true
                order:
                  type: integer
      responses:
        '201':
          description: FAQ created successfully
        '400':
          description: Invalid input
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - Not an admin

  /faqs/categories:
    get:
      tags:
        - System
      summary: Get FAQ categories
      description: Returns a list of all FAQ categories
      responses:
        '200':
          description: List of FAQ categories

  /faqs/{faqId}:
    get:
      tags:
        - System
      summary: Get a specific FAQ
      description: Returns a specific FAQ by ID
      parameters:
        - name: faqId
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: FAQ details
        '404':
          description: FAQ not found
    put:
      tags:
        - System
      summary: Update a FAQ
      description: Updates an existing FAQ (admin only)
      security:
        - bearerAuth: []
      parameters:
        - name: faqId
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                question:
                  type: string
                answer:
                  type: string
                category:
                  type: string
                isPublished:
                  type: boolean
                order:
                  type: integer
      responses:
        '200':
          description: FAQ updated successfully
        '400':
          description: Invalid input
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - Not an admin
        '404':
          description: FAQ not found
    delete:
      tags:
        - System
      summary: Delete a FAQ
      description: Deletes a FAQ (admin only)
      security:
        - bearerAuth: []
      parameters:
        - name: faqId
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: FAQ deleted successfully
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - Not an admin
        '404':
          description: FAQ not found

  # Session Routes
  /sessions:
    get:
      tags:
        - Authentication
      summary: Get all active sessions
      description: Returns all active sessions for the current user
      security:
        - bearerAuth: []
      responses:
        '200':
          description: List of active sessions
        '401':
          description: Unauthorized
    delete:
      tags:
        - Authentication
      summary: Terminate all sessions
      description: Terminates all sessions except the current one
      security:
        - bearerAuth: []
      responses:
        '200':
          description: All sessions terminated successfully
        '401':
          description: Unauthorized

  /sessions/{sessionId}:
    delete:
      tags:
        - Authentication
      summary: Terminate a specific session
      description: Terminates a specific session by ID
      security:
        - bearerAuth: []
      parameters:
        - name: sessionId
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Session terminated successfully
        '401':
          description: Unauthorized
        '404':
          description: Session not found

  /sessions/trust/{deviceFingerprint}:
    post:
      tags:
        - Authentication
      summary: Trust a device
      description: Marks a device as trusted for the current user
      security:
        - bearerAuth: []
      parameters:
        - name: deviceFingerprint
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Device trusted successfully
        '401':
          description: Unauthorized
        '500':
          description: Failed to trust device

  /sessions/untrust/{deviceFingerprint}:
    post:
      tags:
        - Authentication
      summary: Untrust a device
      description: Removes a device from the trusted devices list for the current user
      security:
        - bearerAuth: []
      parameters:
        - name: deviceFingerprint
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Device untrusted successfully
        '401':
          description: Unauthorized
        '500':
          description: Failed to untrust device

  # Report Routes
  /reports:
    get:
      tags:
        - Reports
      summary: Get all reports
      description: Returns all reports for a farm
      security:
        - bearerAuth: []
      responses:
        '200':
          description: List of reports
        '401':
          description: Unauthorized
    post:
      tags:
        - Reports
      summary: Create a new report
      description: Creates a new report
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - name
                - reportType
                - farmId
              properties:
                name:
                  type: string
                reportType:
                  type: string
                farmId:
                  type: integer
                description:
                  type: string
                parameters:
                  type: object
      responses:
        '201':
          description: Report created successfully
        '400':
          description: Invalid input
        '401':
          description: Unauthorized

  /reports/{reportId}:
    get:
      tags:
        - Reports
      summary: Get a report by ID
      description: Returns a specific report by ID
      security:
        - bearerAuth: []
      parameters:
        - name: reportId
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Report details
        '401':
          description: Unauthorized
        '404':
          description: Report not found
    put:
      tags:
        - Reports
      summary: Update a report
      description: Updates an existing report
      security:
        - bearerAuth: []
      parameters:
        - name: reportId
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                description:
                  type: string
                parameters:
                  type: object
      responses:
        '200':
          description: Report updated successfully
        '400':
          description: Invalid input
        '401':
          description: Unauthorized
        '404':
          description: Report not found
    delete:
      tags:
        - Reports
      summary: Delete a report
      description: Deletes a report
      security:
        - bearerAuth: []
      parameters:
        - name: reportId
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Report deleted successfully
        '401':
          description: Unauthorized
        '404':
          description: Report not found

  /reports/{reportId}/data:
    get:
      tags:
        - Reports
      summary: Generate report data
      description: Generates report data based on report type and parameters
      security:
        - bearerAuth: []
      parameters:
        - name: reportId
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Report data
        '401':
          description: Unauthorized
        '404':
          description: Report not found

  /reports/type/{reportType}:
    get:
      tags:
        - Reports
      summary: Get report data by type
      description: Returns report data by type without requiring a saved report
      security:
        - bearerAuth: []
      parameters:
        - name: reportType
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Report data
        '401':
          description: Unauthorized
        '404':
          description: Report type not found

  # Notification Routes
  /notifications/preferences:
    get:
      tags:
        - Notifications
      summary: Get notification preferences
      description: Returns notification preferences for the current user
      security:
        - bearerAuth: []
      responses:
        '200':
          description: Notification preferences
          content:
            application/json:
              schema:
                type: object
                properties:
                  enableInApp:
                    type: boolean
                  enableEmail:
                    type: boolean
                  chatMessageNotifications:
                    type: boolean
                  taskNotifications:
                    type: boolean
                  documentNotifications:
                    type: boolean
                  systemNotifications:
                    type: boolean
        '401':
          description: Unauthorized
    post:
      tags:
        - Notifications
      summary: Update notification preferences
      description: Updates notification preferences for the current user
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                enableInApp:
                  type: boolean
                enableEmail:
                  type: boolean
                chatMessageNotifications:
                  type: boolean
                taskNotifications:
                  type: boolean
                documentNotifications:
                  type: boolean
                systemNotifications:
                  type: boolean
      responses:
        '200':
          description: Notification preferences updated successfully
        '401':
          description: Unauthorized

  # Chat Routes
  /chat/conversations:
    get:
      tags:
        - Chat
      summary: Get user conversations
      description: Returns all conversations for the current user
      security:
        - bearerAuth: []
      responses:
        '200':
          description: List of conversations
        '401':
          description: Unauthorized
    post:
      tags:
        - Chat
      summary: Create a new conversation
      description: Creates a new conversation
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - participants
              properties:
                participants:
                  type: array
                  items:
                    type: integer
                title:
                  type: string
                isGroup:
                  type: boolean
                  default: false
      responses:
        '201':
          description: Conversation created successfully
        '400':
          description: Invalid input
        '401':
          description: Unauthorized

  /chat/conversations/{id}:
    get:
      tags:
        - Chat
      summary: Get a conversation
      description: Returns a specific conversation by ID
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Conversation details
        '401':
          description: Unauthorized
        '404':
          description: Conversation not found

  /chat/conversations/{id}/messages:
    get:
      tags:
        - Chat
      summary: Get conversation messages
      description: Returns all messages for a specific conversation
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: List of messages
        '401':
          description: Unauthorized
        '404':
          description: Conversation not found
    post:
      tags:
        - Chat
      summary: Send a message
      description: Sends a message to a conversation
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - content
              properties:
                content:
                  type: string
                attachments:
                  type: array
                  items:
                    type: object
      responses:
        '201':
          description: Message sent successfully
        '400':
          description: Invalid input
        '401':
          description: Unauthorized
        '404':
          description: Conversation not found

  /chat/conversations/{id}/messages/{message_id}/reactions:
    post:
      tags:
        - Chat
      summary: Add a reaction
      description: Adds a reaction to a message
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
        - name: message_id
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - reaction
              properties:
                reaction:
                  type: string
      responses:
        '201':
          description: Reaction added successfully
        '400':
          description: Invalid input
        '401':
          description: Unauthorized
        '404':
          description: Message not found

  /chat/conversations/{id}/read:
    post:
      tags:
        - Chat
      summary: Mark as read
      description: Marks a conversation as read
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Conversation marked as read
        '401':
          description: Unauthorized
        '404':
          description: Conversation not found

  /chat/connections:
    get:
      tags:
        - Chat
      summary: Get user connections
      description: Returns all connections for the current user
      security:
        - bearerAuth: []
      responses:
        '200':
          description: List of connections
        '401':
          description: Unauthorized

  # Tax Management Routes
  /tax/farms/{farmId}/tax-summary:
    get:
      tags:
        - Tax Management
      summary: Get tax summary
      description: Returns tax summary for a farm
      security:
        - bearerAuth: []
      parameters:
        - name: farmId
          in: path
          required: true
          schema:
            type: integer
          description: Farm ID
      responses:
        '200':
          description: Tax summary retrieved successfully
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - No access to farm
        '404':
          description: Farm not found

  /tax/farms/{farmId}/tax-categories:
    get:
      tags:
        - Tax Management
      summary: Get tax categories
      description: Returns tax categories for a farm
      security:
        - bearerAuth: []
      parameters:
        - name: farmId
          in: path
          required: true
          schema:
            type: integer
          description: Farm ID
      responses:
        '200':
          description: Tax categories retrieved successfully
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - No access to farm
        '404':
          description: Farm not found

  /tax/farms/{farmId}/tax-deductions:
    get:
      tags:
        - Tax Management
      summary: Get tax deductions
      description: Returns tax deductions for a farm
      security:
        - bearerAuth: []
      parameters:
        - name: farmId
          in: path
          required: true
          schema:
            type: integer
          description: Farm ID
      responses:
        '200':
          description: Tax deductions retrieved successfully
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - No access to farm
        '404':
          description: Farm not found
    post:
      tags:
        - Tax Management
      summary: Create tax deduction
      description: Creates a new tax deduction for a farm
      security:
        - bearerAuth: []
      parameters:
        - name: farmId
          in: path
          required: true
          schema:
            type: integer
          description: Farm ID
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - category
                - amount
                - date
              properties:
                category:
                  type: string
                amount:
                  type: number
                date:
                  type: string
                  format: date
                description:
                  type: string
                receiptId:
                  type: integer
      responses:
        '201':
          description: Tax deduction created successfully
        '400':
          description: Invalid input
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - No access to farm
        '404':
          description: Farm not found

  /tax/tax-deductions/{deductionId}:
    put:
      tags:
        - Tax Management
      summary: Update tax deduction
      description: Updates an existing tax deduction
      security:
        - bearerAuth: []
      parameters:
        - name: deductionId
          in: path
          required: true
          schema:
            type: integer
          description: Deduction ID
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                category:
                  type: string
                amount:
                  type: number
                date:
                  type: string
                  format: date
                description:
                  type: string
                receiptId:
                  type: integer
      responses:
        '200':
          description: Tax deduction updated successfully
        '400':
          description: Invalid input
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - No access to farm
        '404':
          description: Tax deduction not found
    delete:
      tags:
        - Tax Management
      summary: Delete tax deduction
      description: Deletes a tax deduction
      security:
        - bearerAuth: []
      parameters:
        - name: deductionId
          in: path
          required: true
          schema:
            type: integer
          description: Deduction ID
      responses:
        '200':
          description: Tax deduction deleted successfully
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - No access to farm
        '404':
          description: Tax deduction not found

  /tax/farms/{farmId}/tax-planning-recommendations:
    get:
      tags:
        - Tax Management
      summary: Get tax planning recommendations
      description: Returns tax planning recommendations for a farm
      security:
        - bearerAuth: []
      parameters:
        - name: farmId
          in: path
          required: true
          schema:
            type: integer
          description: Farm ID
      responses:
        '200':
          description: Tax planning recommendations retrieved successfully
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - No access to farm
        '404':
          description: Farm not found

  /tax/farms/{farmId}/tax-documents:
    get:
      tags:
        - Tax Management
      summary: Get tax documents
      description: Returns tax documents for a farm
      security:
        - bearerAuth: []
      parameters:
        - name: farmId
          in: path
          required: true
          schema:
            type: integer
          description: Farm ID
      responses:
        '200':
          description: Tax documents retrieved successfully
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - No access to farm
        '404':
          description: Farm not found
    post:
      tags:
        - Tax Management
      summary: Create tax document
      description: Creates a new tax document for a farm
      security:
        - bearerAuth: []
      parameters:
        - name: farmId
          in: path
          required: true
          schema:
            type: integer
          description: Farm ID
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - title
                - taxYear
                - documentType
              properties:
                title:
                  type: string
                taxYear:
                  type: integer
                documentType:
                  type: string
                description:
                  type: string
                fileId:
                  type: integer
      responses:
        '201':
          description: Tax document created successfully
        '400':
          description: Invalid input
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - No access to farm
        '404':
          description: Farm not found

  /tax/tax-documents/{documentId}:
    get:
      tags:
        - Tax Management
      summary: Get tax document
      description: Returns a specific tax document
      security:
        - bearerAuth: []
      parameters:
        - name: documentId
          in: path
          required: true
          schema:
            type: integer
          description: Document ID
      responses:
        '200':
          description: Tax document retrieved successfully
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - No access to farm
        '404':
          description: Tax document not found
    put:
      tags:
        - Tax Management
      summary: Update tax document
      description: Updates an existing tax document
      security:
        - bearerAuth: []
      parameters:
        - name: documentId
          in: path
          required: true
          schema:
            type: integer
          description: Document ID
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                title:
                  type: string
                taxYear:
                  type: integer
                documentType:
                  type: string
                description:
                  type: string
                fileId:
                  type: integer
      responses:
        '200':
          description: Tax document updated successfully
        '400':
          description: Invalid input
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - No access to farm
        '404':
          description: Tax document not found
    delete:
      tags:
        - Tax Management
      summary: Delete tax document
      description: Deletes a tax document
      security:
        - bearerAuth: []
      parameters:
        - name: documentId
          in: path
          required: true
          schema:
            type: integer
          description: Document ID
      responses:
        '200':
          description: Tax document deleted successfully
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - No access to farm
        '404':
          description: Tax document not found

  /tax/tax-documents/{documentId}/upload:
    post:
      tags:
        - Tax Management
      summary: Upload tax document file
      description: Uploads a file for a tax document
      security:
        - bearerAuth: []
      parameters:
        - name: documentId
          in: path
          required: true
          schema:
            type: integer
          description: Document ID
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - fileData
              properties:
                fileData:
                  type: string
                  description: Base64-encoded file data
      responses:
        '200':
          description: File uploaded successfully
        '400':
          description: Invalid input
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - No access to farm
        '404':
          description: Tax document not found

  /tax/farms/{farmId}/employee-tax-info:
    get:
      tags:
        - Tax Management
      summary: Get employee tax info
      description: Returns employee tax information for a farm
      security:
        - bearerAuth: []
      parameters:
        - name: farmId
          in: path
          required: true
          schema:
            type: integer
          description: Farm ID
      responses:
        '200':
          description: Employee tax information retrieved successfully
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - No access to farm
        '404':
          description: Farm not found

  /tax/employee-tax-info/{taxInfoId}:
    get:
      tags:
        - Tax Management
      summary: Get employee tax info by ID
      description: Returns specific employee tax information
      security:
        - bearerAuth: []
      parameters:
        - name: taxInfoId
          in: path
          required: true
          schema:
            type: integer
          description: Tax Info ID
      responses:
        '200':
          description: Employee tax information retrieved successfully
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - No access to farm
        '404':
          description: Employee tax information not found
    put:
      tags:
        - Tax Management
      summary: Update employee tax info
      description: Updates employee tax information
      security:
        - bearerAuth: []
      parameters:
        - name: taxInfoId
          in: path
          required: true
          schema:
            type: integer
          description: Tax Info ID
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                ssn:
                  type: string
                filingStatus:
                  type: string
                withholdings:
                  type: number
                additionalWithholdings:
                  type: number
                exemptions:
                  type: integer
      responses:
        '200':
          description: Employee tax information updated successfully
        '400':
          description: Invalid input
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - No access to farm
        '404':
          description: Employee tax information not found
    delete:
      tags:
        - Tax Management
      summary: Delete employee tax info
      description: Deletes employee tax information
      security:
        - bearerAuth: []
      parameters:
        - name: taxInfoId
          in: path
          required: true
          schema:
            type: integer
          description: Tax Info ID
      responses:
        '200':
          description: Employee tax information deleted successfully
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - No access to farm
        '404':
          description: Employee tax information not found

  /tax/farms/{farmId}/employees/{employeeId}/tax-info:
    post:
      tags:
        - Tax Management
      summary: Create employee tax info
      description: Creates tax information for an employee
      security:
        - bearerAuth: []
      parameters:
        - name: farmId
          in: path
          required: true
          schema:
            type: integer
          description: Farm ID
        - name: employeeId
          in: path
          required: true
          schema:
            type: integer
          description: Employee ID
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - ssn
                - filingStatus
              properties:
                ssn:
                  type: string
                filingStatus:
                  type: string
                withholdings:
                  type: number
                additionalWithholdings:
                  type: number
                exemptions:
                  type: integer
      responses:
        '201':
          description: Employee tax information created successfully
        '400':
          description: Invalid input
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - No access to farm
        '404':
          description: Farm or employee not found

  /tax/employee-tax-info/{taxInfoId}/generate-w2:
    post:
      tags:
        - Tax Management
      summary: Generate W-2
      description: Generates a W-2 form for an employee
      security:
        - bearerAuth: []
      parameters:
        - name: taxInfoId
          in: path
          required: true
          schema:
            type: integer
          description: Tax Info ID
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - taxYear
              properties:
                taxYear:
                  type: integer
      responses:
        '200':
          description: W-2 generated successfully
        '400':
          description: Invalid input
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - No access to farm
        '404':
          description: Employee tax information not found

  # Matrix Chat Routes
  /matrix-chat/conversations:
    get:
      tags:
        - Matrix Chat
      summary: Get user conversations
      description: Returns all Matrix conversations for the current user
      security:
        - bearerAuth: []
      responses:
        '200':
          description: List of Matrix conversations
        '401':
          description: Unauthorized
    post:
      tags:
        - Matrix Chat
      summary: Create a new Matrix conversation
      description: Creates a new Matrix conversation
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - participants
              properties:
                participants:
                  type: array
                  items:
                    type: integer
                title:
                  type: string
                isGroup:
                  type: boolean
                  default: false
      responses:
        '201':
          description: Matrix conversation created successfully
        '400':
          description: Invalid input
        '401':
          description: Unauthorized

  /matrix-chat/conversations/{id}:
    get:
      tags:
        - Matrix Chat
      summary: Get a Matrix conversation
      description: Returns a specific Matrix conversation by ID
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Matrix conversation details
        '401':
          description: Unauthorized
        '404':
          description: Matrix conversation not found

  /matrix-chat/conversations/{id}/messages:
    get:
      tags:
        - Matrix Chat
      summary: Get Matrix conversation messages
      description: Returns all messages for a specific Matrix conversation
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: List of Matrix messages
        '401':
          description: Unauthorized
        '404':
          description: Matrix conversation not found
    post:
      tags:
        - Matrix Chat
      summary: Send a Matrix message
      description: Sends a message to a Matrix conversation
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - content
              properties:
                content:
                  type: string
                attachments:
                  type: array
                  items:
                    type: object
      responses:
        '201':
          description: Matrix message sent successfully
        '400':
          description: Invalid input
        '401':
          description: Unauthorized
        '404':
          description: Matrix conversation not found

  # Yield Prediction Routes
  /yield-predictions:
    get:
      tags:
        - Yield Predictions
      summary: Get all yield predictions
      description: Returns all yield predictions for a farm
      security:
        - bearerAuth: []
      responses:
        '200':
          description: List of yield predictions
        '401':
          description: Unauthorized
    post:
      tags:
        - Yield Predictions
      summary: Create a new yield prediction
      description: Creates a new yield prediction
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - farmId
                - fieldId
                - cropId
              properties:
                farmId:
                  type: integer
                fieldId:
                  type: integer
                cropId:
                  type: integer
                plantingDate:
                  type: string
                  format: date
                harvestDate:
                  type: string
                  format: date
                expectedYield:
                  type: number
                notes:
                  type: string
      responses:
        '201':
          description: Yield prediction created successfully
        '400':
          description: Invalid input
        '401':
          description: Unauthorized

  /yield-predictions/{predictionId}:
    get:
      tags:
        - Yield Predictions
      summary: Get a yield prediction
      description: Returns a specific yield prediction by ID
      security:
        - bearerAuth: []
      parameters:
        - name: predictionId
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Yield prediction details
        '401':
          description: Unauthorized
        '404':
          description: Yield prediction not found
    delete:
      tags:
        - Yield Predictions
      summary: Delete a yield prediction
      description: Deletes a yield prediction
      security:
        - bearerAuth: []
      parameters:
        - name: predictionId
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Yield prediction deleted successfully
        '401':
          description: Unauthorized
        '404':
          description: Yield prediction not found

  /yield-predictions/generate:
    post:
      tags:
        - Yield Predictions
      summary: Generate a yield prediction
      description: Generates a yield prediction using AI
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - farmId
                - fieldId
                - cropId
              properties:
                farmId:
                  type: integer
                fieldId:
                  type: integer
                cropId:
                  type: integer
                plantingDate:
                  type: string
                  format: date
                weatherData:
                  type: object
                soilData:
                  type: object
      responses:
        '201':
          description: Yield prediction generated successfully
        '400':
          description: Invalid input
        '401':
          description: Unauthorized

  # Crop Disease Routes
  /crop-diseases:
    get:
      tags:
        - Crop Diseases
      summary: Get all crop disease predictions
      description: Returns all crop disease predictions for a farm
      security:
        - bearerAuth: []
      responses:
        '200':
          description: List of crop disease predictions
        '401':
          description: Unauthorized
    post:
      tags:
        - Crop Diseases
      summary: Create a new crop disease prediction
      description: Creates a new crop disease prediction
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - farmId
                - fieldId
                - cropId
              properties:
                farmId:
                  type: integer
                fieldId:
                  type: integer
                cropId:
                  type: integer
                observationDate:
                  type: string
                  format: date
                symptoms:
                  type: string
                severity:
                  type: string
                  enum: [low, medium, high]
                notes:
                  type: string
      responses:
        '201':
          description: Crop disease prediction created successfully
        '400':
          description: Invalid input
        '401':
          description: Unauthorized

  /crop-diseases/{predictionId}:
    get:
      tags:
        - Crop Diseases
      summary: Get a crop disease prediction
      description: Returns a specific crop disease prediction by ID
      security:
        - bearerAuth: []
      parameters:
        - name: predictionId
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Crop disease prediction details
        '401':
          description: Unauthorized
        '404':
          description: Crop disease prediction not found
    delete:
      tags:
        - Crop Diseases
      summary: Delete a crop disease prediction
      description: Deletes a crop disease prediction
      security:
        - bearerAuth: []
      parameters:
        - name: predictionId
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Crop disease prediction deleted successfully
        '401':
          description: Unauthorized
        '404':
          description: Crop disease prediction not found

  /crop-diseases/generate:
    post:
      tags:
        - Crop Diseases
      summary: Generate a crop disease prediction
      description: Generates a crop disease prediction using AI
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - farmId
                - fieldId
                - cropId
              properties:
                farmId:
                  type: integer
                fieldId:
                  type: integer
                cropId:
                  type: integer
                observationDate:
                  type: string
                  format: date
                symptoms:
                  type: string
                images:
                  type: array
                  items:
                    type: string
                    description: Base64-encoded image data
                weatherData:
                  type: object
      responses:
        '201':
          description: Crop disease prediction generated successfully
        '400':
          description: Invalid input
        '401':
          description: Unauthorized

  # Financial Analytics Routes
  /financial-analytics/farms/{farmId}/financial-analytics:
    get:
      tags:
        - Financial Analytics
      summary: Get financial analytics
      description: Returns financial analytics for a farm
      security:
        - bearerAuth: []
      parameters:
        - name: farmId
          in: path
          required: true
          schema:
            type: integer
          description: Farm ID
      responses:
        '200':
          description: Financial analytics retrieved successfully
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - No access to farm
        '404':
          description: Farm not found

  /financial-analytics/farms/{farmId}/cash-flow-projections:
    get:
      tags:
        - Financial Analytics
      summary: Get cash flow projections
      description: Returns cash flow projections for a farm
      security:
        - bearerAuth: []
      parameters:
        - name: farmId
          in: path
          required: true
          schema:
            type: integer
          description: Farm ID
      responses:
        '200':
          description: Cash flow projections retrieved successfully
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - No access to farm
        '404':
          description: Farm not found

  # Crop Rotation Routes
  /crop-rotations:
    get:
      tags:
        - Crop Rotations
      summary: Get all crop rotation plans
      description: Returns all crop rotation plans for a farm
      security:
        - bearerAuth: []
      responses:
        '200':
          description: List of crop rotation plans
        '401':
          description: Unauthorized
    post:
      tags:
        - Crop Rotations
      summary: Create a new crop rotation plan
      description: Creates a new crop rotation plan
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - farmId
                - fieldId
                - startYear
                - endYear
              properties:
                farmId:
                  type: integer
                fieldId:
                  type: integer
                startYear:
                  type: integer
                endYear:
                  type: integer
                rotationSequence:
                  type: array
                  items:
                    type: object
                    properties:
                      year:
                        type: integer
                      season:
                        type: string
                      cropId:
                        type: integer
                notes:
                  type: string
      responses:
        '201':
          description: Crop rotation plan created successfully
        '400':
          description: Invalid input
        '401':
          description: Unauthorized

  /crop-rotations/{planId}:
    get:
      tags:
        - Crop Rotations
      summary: Get a crop rotation plan
      description: Returns a specific crop rotation plan by ID
      security:
        - bearerAuth: []
      parameters:
        - name: planId
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Crop rotation plan details
        '401':
          description: Unauthorized
        '404':
          description: Crop rotation plan not found
    delete:
      tags:
        - Crop Rotations
      summary: Delete a crop rotation plan
      description: Deletes a crop rotation plan
      security:
        - bearerAuth: []
      parameters:
        - name: planId
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Crop rotation plan deleted successfully
        '401':
          description: Unauthorized
        '404':
          description: Crop rotation plan not found

  /crop-rotations/generate:
    post:
      tags:
        - Crop Rotations
      summary: Generate a crop rotation plan
      description: Generates a crop rotation plan using AI
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - farmId
                - fieldId
                - startYear
                - endYear
              properties:
                farmId:
                  type: integer
                fieldId:
                  type: integer
                startYear:
                  type: integer
                endYear:
                  type: integer
                soilData:
                  type: object
                cropPreferences:
                  type: array
                  items:
                    type: integer
      responses:
        '201':
          description: Crop rotation plan generated successfully
        '400':
          description: Invalid input
        '401':
          description: Unauthorized

  # Customer Auth Routes
  /customer-auth/login:
    post:
      tags:
        - Customer Authentication
      summary: Customer login
      description: Authenticates a customer and returns a JWT token
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - email
                - password
              properties:
                email:
                  type: string
                  format: email
                password:
                  type: string
                  format: password
      responses:
        '200':
          description: Login successful
        '401':
          description: Invalid credentials

  /customer-auth/register:
    post:
      tags:
        - Customer Authentication
      summary: Customer registration
      description: Registers a new customer account
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - email
                - password
                - firstName
                - lastName
              properties:
                email:
                  type: string
                  format: email
                password:
                  type: string
                  format: password
                firstName:
                  type: string
                lastName:
                  type: string
                phone:
                  type: string
                farmId:
                  type: integer
      responses:
        '201':
          description: Registration successful
        '400':
          description: Invalid input
        '409':
          description: Email already exists

  /customer-auth/verify-email/{token}:
    get:
      tags:
        - Customer Authentication
      summary: Verify customer email
      description: Verifies a customer's email address using a token
      parameters:
        - name: token
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Email verified successfully
        '400':
          description: Invalid token
        '404':
          description: Token not found

  /customer-auth/forgot-password:
    post:
      tags:
        - Customer Authentication
      summary: Forgot password
      description: Sends a password reset email to the customer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - email
              properties:
                email:
                  type: string
                  format: email
      responses:
        '200':
          description: Password reset email sent
        '404':
          description: Customer not found

  /customer-auth/reset-password/{token}:
    post:
      tags:
        - Customer Authentication
      summary: Reset password
      description: Resets a customer's password using a token
      parameters:
        - name: token
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - password
              properties:
                password:
                  type: string
                  format: password
      responses:
        '200':
          description: Password reset successful
        '400':
          description: Invalid token
        '404':
          description: Token not found

  /customer-auth/refresh-token:
    post:
      tags:
        - Customer Authentication
      summary: Refresh token
      description: Refreshes a customer's authentication token
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - refreshToken
              properties:
                refreshToken:
                  type: string
      responses:
        '200':
          description: Token refreshed successfully
        '401':
          description: Invalid refresh token

  /customer-auth/logout:
    post:
      tags:
        - Customer Authentication
      summary: Customer logout
      description: Logs out a customer
      responses:
        '200':
          description: Logout successful

  # Customer Invoice Routes
  /customer/invoices:
    get:
      tags:
        - Customer
      summary: Get customer invoices
      description: Returns all invoices for the authenticated customer
      security:
        - bearerAuth: []
      responses:
        '200':
          description: List of invoices
        '401':
          description: Unauthorized

  /customer/invoices/{invoiceId}:
    get:
      tags:
        - Customer
      summary: Get customer invoice
      description: Returns a specific invoice for the authenticated customer
      security:
        - bearerAuth: []
      parameters:
        - name: invoiceId
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Invoice details
        '401':
          description: Unauthorized
        '404':
          description: Invoice not found

  /customer/invoices/{invoiceId}/pay:
    post:
      tags:
        - Customer
      summary: Pay invoice
      description: Pays an invoice
      security:
        - bearerAuth: []
      parameters:
        - name: invoiceId
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - paymentMethod
              properties:
                paymentMethod:
                  type: string
                  enum: [credit_card, bank_transfer, paypal]
                paymentDetails:
                  type: object
      responses:
        '200':
          description: Payment successful
        '400':
          description: Invalid input
        '401':
          description: Unauthorized
        '404':
          description: Invoice not found

  # Customer Product Routes
  /customer/products:
    get:
      tags:
        - Customer
      summary: Get public products
      description: Returns all public products for the farm
      security:
        - bearerAuth: []
      responses:
        '200':
          description: List of public products
        '401':
          description: Unauthorized

  /customer/products/{productId}:
    get:
      tags:
        - Customer
      summary: Get public product
      description: Returns a specific public product
      security:
        - bearerAuth: []
      parameters:
        - name: productId
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Product details
        '401':
          description: Unauthorized
        '404':
          description: Product not found

  /customer/products/{productId}/request:
    post:
      tags:
        - Customer
      summary: Request product
      description: Sends a product inquiry
      security:
        - bearerAuth: []
      parameters:
        - name: productId
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - message
              properties:
                message:
                  type: string
                quantity:
                  type: number
                requestedDeliveryDate:
                  type: string
                  format: date
      responses:
        '201':
          description: Product request sent successfully
        '400':
          description: Invalid input
        '401':
          description: Unauthorized
        '404':
          description: Product not found

  # Customer Delivery Routes
  /customer/deliveries:
    get:
      tags:
        - Customer
      summary: Get customer deliveries
      description: Returns all deliveries for the authenticated customer
      security:
        - bearerAuth: []
      responses:
        '200':
          description: List of deliveries
        '401':
          description: Unauthorized

  /customer/deliveries/{deliveryId}:
    get:
      tags:
        - Customer
      summary: Get customer delivery
      description: Returns a specific delivery for the authenticated customer
      security:
        - bearerAuth: []
      parameters:
        - name: deliveryId
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Delivery details
        '401':
          description: Unauthorized
        '404':
          description: Delivery not found

  /customer/deliveries/request:
    post:
      tags:
        - Customer
      summary: Request delivery
      description: Requests a delivery
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - products
                - deliveryAddress
                - requestedDeliveryDate
              properties:
                products:
                  type: array
                  items:
                    type: object
                    properties:
                      productId:
                        type: integer
                      quantity:
                        type: number
                deliveryAddress:
                  type: object
                  properties:
                    street:
                      type: string
                    city:
                      type: string
                    state:
                      type: string
                    zipCode:
                      type: string
                    country:
                      type: string
                requestedDeliveryDate:
                  type: string
                  format: date
                notes:
                  type: string
      responses:
        '201':
          description: Delivery request sent successfully
        '400':
          description: Invalid input
        '401':
          description: Unauthorized

  # Customer Contact Routes
  /customer/contact/info:
    get:
      tags:
        - Customer
      summary: Get farm contact info
      description: Returns contact information for the farm
      security:
        - bearerAuth: []
      responses:
        '200':
          description: Farm contact information
        '401':
          description: Unauthorized

  /customer/contact/message:
    post:
      tags:
        - Customer
      summary: Contact farm
      description: Sends a contact message to the farm
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - subject
                - message
              properties:
                subject:
                  type: string
                message:
                  type: string
                attachments:
                  type: array
                  items:
                    type: string
                    description: Base64-encoded attachment data
      responses:
        '201':
          description: Message sent successfully
        '400':
          description: Invalid input
        '401':
          description: Unauthorized

  # Script Execution Routes
  /script-executions:
    get:
      tags:
        - System
      summary: Get all script executions
      description: Returns all script executions (admin only)
      security:
        - bearerAuth: []
      responses:
        '200':
          description: List of script executions
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - Not a global admin

  /script-executions/{scriptId}:
    get:
      tags:
        - System
      summary: Get script execution
      description: Returns a specific script execution by ID (admin only)
      security:
        - bearerAuth: []
      parameters:
        - name: scriptId
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Script execution details
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - Not a global admin
        '404':
          description: Script execution not found

  /script-executions/{scriptId}/execute:
    post:
      tags:
        - System
      summary: Execute script
      description: Executes a script (admin only)
      security:
        - bearerAuth: []
      parameters:
        - name: scriptId
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: false
        content:
          application/json:
            schema:
              type: object
              properties:
                parameters:
                  type: object
      responses:
        '200':
          description: Script executed successfully
        '400':
          description: Invalid input
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - Not a global admin
        '404':
          description: Script not found

  /script-executions/scan:
    post:
      tags:
        - System
      summary: Scan for scripts
      description: Scans for new scripts in the scripts directory (admin only)
      security:
        - bearerAuth: []
      responses:
        '200':
          description: Scan completed successfully
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - Not a global admin

  /script-executions/status/check:
    get:
      tags:
        - System
      summary: Check script execution status
      description: Checks the status of script executions (admin only)
      security:
        - bearerAuth: []
      responses:
        '200':
          description: Script execution status
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - Not a global admin

  # Crops Routes
  /crops:
    get:
      tags:
        - Crops
      summary: Get crops by farm ID
      description: Returns all crops for a specific farm
      security:
        - bearerAuth: []
      responses:
        '200':
          description: List of crops
        '401':
          description: Unauthorized
    post:
      tags:
        - Crops
      summary: Create a new crop
      description: Creates a new crop
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - name
                - farmId
              properties:
                name:
                  type: string
                farmId:
                  type: integer
                variety:
                  type: string
                plantingDate:
                  type: string
                  format: date
                harvestDate:
                  type: string
                  format: date
                fieldId:
                  type: integer
                notes:
                  type: string
      responses:
        '201':
          description: Crop created successfully
        '400':
          description: Invalid input
        '401':
          description: Unauthorized

  /crops/farm/{farmId}:
    get:
      tags:
        - Crops
      summary: Get farm crops
      description: Returns all crops for a specific farm
      security:
        - bearerAuth: []
      parameters:
        - name: farmId
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: List of crops
        '401':
          description: Unauthorized
        '404':
          description: Farm not found

  /crops/{cropId}:
    get:
      tags:
        - Crops
      summary: Get a crop by ID
      description: Returns a specific crop by ID
      security:
        - bearerAuth: []
      parameters:
        - name: cropId
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Crop details
        '401':
          description: Unauthorized
        '404':
          description: Crop not found
    put:
      tags:
        - Crops
      summary: Update a crop
      description: Updates an existing crop
      security:
        - bearerAuth: []
      parameters:
        - name: cropId
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                variety:
                  type: string
                plantingDate:
                  type: string
                  format: date
                harvestDate:
                  type: string
                  format: date
                fieldId:
                  type: integer
                notes:
                  type: string
      responses:
        '200':
          description: Crop updated successfully
        '400':
          description: Invalid input
        '401':
          description: Unauthorized
        '404':
          description: Crop not found
    delete:
      tags:
        - Crops
      summary: Delete a crop
      description: Deletes a crop
      security:
        - bearerAuth: []
      parameters:
        - name: cropId
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Crop deleted successfully
        '401':
          description: Unauthorized
        '404':
          description: Crop not found

  /crops/{cropId}/activities:
    get:
      tags:
        - Crops
      summary: Get crop activities
      description: Returns all activities for a specific crop
      security:
        - bearerAuth: []
      parameters:
        - name: cropId
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: List of crop activities
        '401':
          description: Unauthorized
        '404':
          description: Crop not found
    post:
      tags:
        - Crops
      summary: Create a crop activity
      description: Creates a new activity for a crop
      security:
        - bearerAuth: []
      parameters:
        - name: cropId
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - activityType
                - date
              properties:
                activityType:
                  type: string
                date:
                  type: string
                  format: date
                notes:
                  type: string
                cost:
                  type: number
      responses:
        '201':
          description: Crop activity created successfully
        '400':
          description: Invalid input
        '401':
          description: Unauthorized
        '404':
          description: Crop not found

  /crops/activities/{activityId}:
    put:
      tags:
        - Crops
      summary: Update a crop activity
      description: Updates an existing crop activity
      security:
        - bearerAuth: []
      parameters:
        - name: activityId
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                activityType:
                  type: string
                date:
                  type: string
                  format: date
                notes:
                  type: string
                cost:
                  type: number
      responses:
        '200':
          description: Crop activity updated successfully
        '400':
          description: Invalid input
        '401':
          description: Unauthorized
        '404':
          description: Crop activity not found
    delete:
      tags:
        - Crops
      summary: Delete a crop activity
      description: Deletes a crop activity
      security:
        - bearerAuth: []
      parameters:
        - name: activityId
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Crop activity deleted successfully
        '401':
          description: Unauthorized
        '404':
          description: Crop activity not found

  # Invoice Routes
  /invoices:
    post:
      tags:
        - Invoices
      summary: Create a new invoice
      description: Creates a new invoice
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - farmId
                - customerId
                - items
                - dueDate
              properties:
                farmId:
                  type: integer
                customerId:
                  type: integer
                items:
                  type: array
                  items:
                    type: object
                    properties:
                      description:
                        type: string
                      quantity:
                        type: number
                      unitPrice:
                        type: number
                dueDate:
                  type: string
                  format: date
                notes:
                  type: string
                status:
                  type: string
                  enum: [draft, sent, paid, overdue, cancelled]
      responses:
        '201':
          description: Invoice created successfully
        '400':
          description: Invalid input
        '401':
          description: Unauthorized

  /invoices/farm/{farmId}:
    get:
      tags:
        - Invoices
      summary: Get farm invoices
      description: Returns all invoices for a specific farm
      security:
        - bearerAuth: []
      parameters:
        - name: farmId
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: List of invoices
        '401':
          description: Unauthorized
        '404':
          description: Farm not found

  /invoices/{invoiceId}:
    get:
      tags:
        - Invoices
      summary: Get an invoice by ID
      description: Returns a specific invoice by ID
      security:
        - bearerAuth: []
      parameters:
        - name: invoiceId
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Invoice details
        '401':
          description: Unauthorized
        '404':
          description: Invoice not found
    put:
      tags:
        - Invoices
      summary: Update an invoice
      description: Updates an existing invoice
      security:
        - bearerAuth: []
      parameters:
        - name: invoiceId
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                customerId:
                  type: integer
                items:
                  type: array
                  items:
                    type: object
                    properties:
                      description:
                        type: string
                      quantity:
                        type: number
                      unitPrice:
                        type: number
                dueDate:
                  type: string
                  format: date
                notes:
                  type: string
                status:
                  type: string
                  enum: [draft, sent, paid, overdue, cancelled]
      responses:
        '200':
          description: Invoice updated successfully
        '400':
          description: Invalid input
        '401':
          description: Unauthorized
        '404':
          description: Invoice not found
    delete:
      tags:
        - Invoices
      summary: Delete an invoice
      description: Deletes an invoice
      security:
        - bearerAuth: []
      parameters:
        - name: invoiceId
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Invoice deleted successfully
        '401':
          description: Unauthorized
        '404':
          description: Invoice not found

  # Customer Routes
  /customers:
    post:
      tags:
        - Customers
      summary: Create a new customer
      description: Creates a new customer
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - farmId
                - name
              properties:
                farmId:
                  type: integer
                name:
                  type: string
                email:
                  type: string
                  format: email
                phone:
                  type: string
                address:
                  type: string
                city:
                  type: string
                state:
                  type: string
                zipCode:
                  type: string
                country:
                  type: string
                notes:
                  type: string
      responses:
        '201':
          description: Customer created successfully
        '400':
          description: Invalid input
        '401':
          description: Unauthorized

  /customers/farm/{farmId}:
    get:
      tags:
        - Customers
      summary: Get farm customers
      description: Returns all customers for a specific farm
      security:
        - bearerAuth: []
      parameters:
        - name: farmId
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: List of customers
        '401':
          description: Unauthorized
        '404':
          description: Farm not found

  /customers/{customerId}:
    get:
      tags:
        - Customers
      summary: Get a customer by ID
      description: Returns a specific customer by ID
      security:
        - bearerAuth: []
      parameters:
        - name: customerId
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Customer details
        '401':
          description: Unauthorized
        '404':
          description: Customer not found
    put:
      tags:
        - Customers
      summary: Update a customer
      description: Updates an existing customer
      security:
        - bearerAuth: []
      parameters:
        - name: customerId
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                email:
                  type: string
                  format: email
                phone:
                  type: string
                address:
                  type: string
                city:
                  type: string
                state:
                  type: string
                zipCode:
                  type: string
                country:
                  type: string
                notes:
                  type: string
      responses:
        '200':
          description: Customer updated successfully
        '400':
          description: Invalid input
        '401':
          description: Unauthorized
        '404':
          description: Customer not found
    delete:
      tags:
        - Customers
      summary: Delete a customer
      description: Deletes a customer
      security:
        - bearerAuth: []
      parameters:
        - name: customerId
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Customer deleted successfully
        '401':
          description: Unauthorized
        '404':
          description: Customer not found

  /customers/{customerId}/phone-book/subscribe:
    post:
      tags:
        - Customers
      summary: Subscribe to phone book
      description: Subscribes a customer to phone book updates
      security:
        - bearerAuth: []
      parameters:
        - name: customerId
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Customer subscribed to phone book successfully
        '401':
          description: Unauthorized
        '404':
          description: Customer not found

  /customers/{customerId}/phone-book/unsubscribe:
    post:
      tags:
        - Customers
      summary: Unsubscribe from phone book
      description: Unsubscribes a customer from phone book updates
      security:
        - bearerAuth: []
      parameters:
        - name: customerId
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Customer unsubscribed from phone book successfully
        '401':
          description: Unauthorized
        '404':
          description: Customer not found

  /customers/{customerId}/phone-book/sync:
    post:
      tags:
        - Customers
      summary: Sync with phone book
      description: Syncs a customer with phone books
      security:
        - bearerAuth: []
      parameters:
        - name: customerId
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Customer synced with phone book successfully
        '401':
          description: Unauthorized
        '404':
          description: Customer not found

  /customers/phone-book/sync-all:
    post:
      tags:
        - Customers
      summary: Sync all customers
      description: Syncs all subscribed customers with phone books
      security:
        - bearerAuth: []
      responses:
        '200':
          description: All customers synced successfully
        '401':
          description: Unauthorized

  /customers/phone-book/webhook:
    post:
      tags:
        - Customers
      summary: Phone book webhook
      description: Webhook for phone book updates (two-way sync)
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
      responses:
        '200':
          description: Webhook processed successfully
        '400':
          description: Invalid input

  # Product Routes (Extended)
  /products/extended:
    get:
      tags:
        - Products
      summary: Get products by query (extended)
      description: Returns products filtered by query parameters with extended options
      security:
        - bearerAuth: []
      parameters:
        - name: farmId
          in: query
          schema:
            type: integer
          description: Farm ID
        - name: type
          in: query
          schema:
            type: string
          description: Product type
        - name: category
          in: query
          schema:
            type: string
          description: Product category
      responses:
        '200':
          description: List of products
        '401':
          description: Unauthorized
    post:
      tags:
        - Products
      summary: Create a new product (extended)
      description: Creates a new product with extended properties
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - farmId
                - name
                - price
              properties:
                farmId:
                  type: integer
                name:
                  type: string
                description:
                  type: string
                price:
                  type: number
                unit:
                  type: string
                category:
                  type: string
                type:
                  type: string
                sku:
                  type: string
                isPublic:
                  type: boolean
                  default: false
                imageUrl:
                  type: string
      responses:
        '201':
          description: Product created successfully
        '400':
          description: Invalid input
        '401':
          description: Unauthorized

  /products/extended/farm/{farmId}:
    get:
      tags:
        - Products
      summary: Get farm products (extended)
      description: Returns all products for a specific farm with extended information
      security:
        - bearerAuth: []
      parameters:
        - name: farmId
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: List of products
        '401':
          description: Unauthorized
        '404':
          description: Farm not found

  /products/extended/{productId}:
    get:
      tags:
        - Products
      summary: Get a product by ID (extended)
      description: Returns a specific product by ID with extended information
      security:
        - bearerAuth: []
      parameters:
        - name: productId
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Product details
        '401':
          description: Unauthorized
        '404':
          description: Product not found
    put:
      tags:
        - Products
      summary: Update a product (extended)
      description: Updates an existing product with extended properties
      security:
        - bearerAuth: []
      parameters:
        - name: productId
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                description:
                  type: string
                price:
                  type: number
                unit:
                  type: string
                category:
                  type: string
                type:
                  type: string
                sku:
                  type: string
                isPublic:
                  type: boolean
                imageUrl:
                  type: string
      responses:
        '200':
          description: Product updated successfully
        '400':
          description: Invalid input
        '401':
          description: Unauthorized
        '404':
          description: Product not found
    delete:
      tags:
        - Products
      summary: Delete a product (extended)
      description: Deletes a product with extended validation
      security:
        - bearerAuth: []
      parameters:
        - name: productId
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Product deleted successfully
        '401':
          description: Unauthorized
        '404':
          description: Product not found

  /products/extended/from-crop/{cropId}:
    post:
      tags:
        - Products
      summary: Create product from crop (extended)
      description: Creates a new product from a crop with extended properties
      security:
        - bearerAuth: []
      parameters:
        - name: cropId
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                price:
                  type: number
                unit:
                  type: string
                isPublic:
                  type: boolean
                  default: false
      responses:
        '201':
          description: Product created successfully
        '400':
          description: Invalid input
        '401':
          description: Unauthorized
        '404':
          description: Crop not found

  /products/extended/from-livestock/{livestockId}:
    post:
      tags:
        - Products
      summary: Create product from livestock (extended)
      description: Creates a new product from livestock with extended properties
      security:
        - bearerAuth: []
      parameters:
        - name: livestockId
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                price:
                  type: number
                unit:
                  type: string
                isPublic:
                  type: boolean
                  default: false
      responses:
        '201':
          description: Product created successfully
        '400':
          description: Invalid input
        '401':
          description: Unauthorized
        '404':
          description: Livestock not found

  /products/extended/from-equipment/{equipmentId}:
    post:
      tags:
        - Products
      summary: Create product from equipment (extended)
      description: Creates a new product from equipment with extended properties
      security:
        - bearerAuth: []
      parameters:
        - name: equipmentId
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                price:
                  type: number
                unit:
                  type: string
                isPublic:
                  type: boolean
                  default: false
      responses:
        '201':
          description: Product created successfully
        '400':
          description: Invalid input
        '401':
          description: Unauthorized
        '404':
          description: Equipment not found

  # Subscription Routes
  /subscriptions/plans:
    get:
      tags:
        - Subscriptions
      summary: Get all subscription plans
      description: Returns all subscription plans
      security:
        - bearerAuth: []
      responses:
        '200':
          description: List of subscription plans
        '401':
          description: Unauthorized
    post:
      tags:
        - Subscriptions
      summary: Create a new subscription plan
      description: Creates a new subscription plan (global admin only)
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - name
                - price
                - interval
              properties:
                name:
                  type: string
                description:
                  type: string
                price:
                  type: number
                interval:
                  type: string
                  enum: [monthly, yearly]
                features:
                  type: array
                  items:
                    type: string
                isActive:
                  type: boolean
                  default: true
      responses:
        '201':
          description: Subscription plan created successfully
        '400':
          description: Invalid input
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - Not a global admin

  /subscriptions/plans/{planId}:
    get:
      tags:
        - Subscriptions
      summary: Get a subscription plan by ID
      description: Returns a specific subscription plan by ID
      security:
        - bearerAuth: []
      parameters:
        - name: planId
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Subscription plan details
        '401':
          description: Unauthorized
        '404':
          description: Subscription plan not found
    put:
      tags:
        - Subscriptions
      summary: Update a subscription plan
      description: Updates an existing subscription plan (global admin only)
      security:
        - bearerAuth: []
      parameters:
        - name: planId
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                description:
                  type: string
                price:
                  type: number
                interval:
                  type: string
                  enum: [monthly, yearly]
                features:
                  type: array
                  items:
                    type: string
                isActive:
                  type: boolean
      responses:
        '200':
          description: Subscription plan updated successfully
        '400':
          description: Invalid input
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - Not a global admin
        '404':
          description: Subscription plan not found
    delete:
      tags:
        - Subscriptions
      summary: Delete a subscription plan
      description: Deletes a subscription plan (global admin only)
      security:
        - bearerAuth: []
      parameters:
        - name: planId
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Subscription plan deleted successfully
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - Not a global admin
        '404':
          description: Subscription plan not found

  /subscriptions/transactions/farm/{farmId}:
    get:
      tags:
        - Subscriptions
      summary: Get farm subscription transactions
      description: Returns all subscription transactions for a specific farm
      security:
        - bearerAuth: []
      parameters:
        - name: farmId
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: List of subscription transactions
        '401':
          description: Unauthorized
        '404':
          description: Farm not found

  /subscriptions/transactions:
    post:
      tags:
        - Subscriptions
      summary: Create a subscription transaction
      description: Creates a new subscription transaction
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - farmId
                - planId
                - amount
              properties:
                farmId:
                  type: integer
                planId:
                  type: string
                amount:
                  type: number
                paymentMethod:
                  type: string
                paymentStatus:
                  type: string
                  enum: [pending, completed, failed]
                promoCodeId:
                  type: string
      responses:
        '201':
          description: Subscription transaction created successfully
        '400':
          description: Invalid input
        '401':
          description: Unauthorized

  /subscriptions/farms/trial:
    post:
      tags:
        - Subscriptions
      summary: Create a farm trial subscription
      description: Creates a free trial subscription for a farm (global admin only)
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - farmId
                - trialDays
              properties:
                farmId:
                  type: integer
                trialDays:
                  type: integer
                  default: 30
      responses:
        '201':
          description: Farm trial subscription created successfully
        '400':
          description: Invalid input
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - Not a global admin

  /subscriptions/farms/{farmId}/trial:
    put:
      tags:
        - Subscriptions
      summary: Update farm trial period
      description: Updates a farm's trial period (global admin only)
      security:
        - bearerAuth: []
      parameters:
        - name: farmId
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - trialDays
              properties:
                trialDays:
                  type: integer
      responses:
        '200':
          description: Farm trial period updated successfully
        '400':
          description: Invalid input
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - Not a global admin
        '404':
          description: Farm not found

  /subscriptions/farms/{farmId}/upgrade-plan:
    put:
      tags:
        - Subscriptions
      summary: Upgrade farm plan
      description: Upgrades a farm to a full access plan
      security:
        - bearerAuth: []
      parameters:
        - name: farmId
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - planId
              properties:
                planId:
                  type: string
                paymentMethod:
                  type: string
                promoCodeId:
                  type: string
      responses:
        '200':
          description: Farm plan upgraded successfully
        '400':
          description: Invalid input
        '401':
          description: Unauthorized
        '404':
          description: Farm not found

  /subscriptions/promo-codes:
    get:
      tags:
        - Subscriptions
      summary: Get all promo codes
      description: Returns all promo codes (global admin only)
      security:
        - bearerAuth: []
      responses:
        '200':
          description: List of promo codes
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - Not a global admin
    post:
      tags:
        - Subscriptions
      summary: Create a new promo code
      description: Creates a new promo code (global admin only)
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - code
                - discountType
                - discountValue
              properties:
                code:
                  type: string
                discountType:
                  type: string
                  enum: [percentage, fixed]
                discountValue:
                  type: number
                maxUses:
                  type: integer
                expirationDate:
                  type: string
                  format: date
                isActive:
                  type: boolean
                  default: true
      responses:
        '201':
          description: Promo code created successfully
        '400':
          description: Invalid input
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - Not a global admin

  /subscriptions/promo-codes/{promoCodeId}:
    get:
      tags:
        - Subscriptions
      summary: Get a promo code by ID
      description: Returns a specific promo code by ID (global admin only)
      security:
        - bearerAuth: []
      parameters:
        - name: promoCodeId
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Promo code details
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - Not a global admin
        '404':
          description: Promo code not found
    put:
      tags:
        - Subscriptions
      summary: Update a promo code
      description: Updates an existing promo code (global admin only)
      security:
        - bearerAuth: []
      parameters:
        - name: promoCodeId
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                code:
                  type: string
                discountType:
                  type: string
                  enum: [percentage, fixed]
                discountValue:
                  type: number
                maxUses:
                  type: integer
                expirationDate:
                  type: string
                  format: date
                isActive:
                  type: boolean
      responses:
        '200':
          description: Promo code updated successfully
        '400':
          description: Invalid input
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - Not a global admin
        '404':
          description: Promo code not found
    delete:
      tags:
        - Subscriptions
      summary: Delete a promo code
      description: Deletes a promo code (global admin only)
      security:
        - bearerAuth: []
      parameters:
        - name: promoCodeId
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Promo code deleted successfully
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - Not a global admin
        '404':
          description: Promo code not found

  /subscriptions/promo-codes/validate:
    post:
      tags:
        - Subscriptions
      summary: Validate a promo code
      description: Validates a promo code for a user
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - code
              properties:
                code:
                  type: string
                planId:
                  type: string
      responses:
        '200':
          description: Promo code validated successfully
        '400':
          description: Invalid promo code
        '401':
          description: Unauthorized

  /subscriptions/promo-codes/apply:
    post:
      tags:
        - Subscriptions
      summary: Apply a promo code
      description: Applies a promo code (increments usage counter)
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - code
                - planId
              properties:
                code:
                  type: string
                planId:
                  type: string
                farmId:
                  type: integer
      responses:
        '200':
          description: Promo code applied successfully
        '400':
          description: Invalid promo code
        '401':
          description: Unauthorized

  # Livestock Routes
  /livestock:
    post:
      tags:
        - Livestock
      summary: Create a new livestock item
      description: Creates a new livestock item
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - farmId
                - type
                - quantity
              properties:
                farmId:
                  type: integer
                type:
                  type: string
                breed:
                  type: string
                quantity:
                  type: integer
                acquisitionDate:
                  type: string
                  format: date
                cost:
                  type: number
                notes:
                  type: string
      responses:
        '201':
          description: Livestock item created successfully
        '400':
          description: Invalid input
        '401':
          description: Unauthorized

  /livestock/farm/{farmId}:
    get:
      tags:
        - Livestock
      summary: Get farm livestock
      description: Returns all livestock for a specific farm
      security:
        - bearerAuth: []
      parameters:
        - name: farmId
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: List of livestock
        '401':
          description: Unauthorized
        '404':
          description: Farm not found

  /livestock/{livestockId}:
    get:
      tags:
        - Livestock
      summary: Get a livestock item by ID
      description: Returns a specific livestock item by ID
      security:
        - bearerAuth: []
      parameters:
        - name: livestockId
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Livestock item details
        '401':
          description: Unauthorized
        '404':
          description: Livestock item not found
    put:
      tags:
        - Livestock
      summary: Update a livestock item
      description: Updates an existing livestock item
      security:
        - bearerAuth: []
      parameters:
        - name: livestockId
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                type:
                  type: string
                breed:
                  type: string
                quantity:
                  type: integer
                acquisitionDate:
                  type: string
                  format: date
                cost:
                  type: number
                notes:
                  type: string
      responses:
        '200':
          description: Livestock item updated successfully
        '400':
          description: Invalid input
        '401':
          description: Unauthorized
        '404':
          description: Livestock item not found
    delete:
      tags:
        - Livestock
      summary: Delete a livestock item
      description: Deletes a livestock item
      security:
        - bearerAuth: []
      parameters:
        - name: livestockId
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Livestock item deleted successfully
        '401':
          description: Unauthorized
        '404':
          description: Livestock item not found

  # Financial Connections Routes
  /financial-connections/create-session:
    post:
      tags:
        - Financial
      summary: Create a Financial Connections Session
      description: Creates a new Stripe Financial Connections Session
      security:
        - bearerAuth: []
      responses:
        '200':
          description: Financial Connections Session created successfully
        '401':
          description: Unauthorized

  /financial-connections/callback:
    post:
      tags:
        - Financial
      summary: Handle Financial Connections callback
      description: Handles the callback from Stripe Financial Connections
      security:
        - bearerAuth: []
      responses:
        '200':
          description: Callback handled successfully
        '401':
          description: Unauthorized

  /financial-connections/transactions:
    get:
      tags:
        - Financial
      summary: Get transactions
      description: Returns transactions for a specific account
      responses:
        '200':
          description: Transactions retrieved successfully

  /financial-connections/accounts/{connectionId}:
    get:
      tags:
        - Financial
      summary: Get account balances
      description: Returns account balances for a specific connection
      parameters:
        - name: connectionId
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Account balances retrieved successfully

  /financial-connections/sync/{farmId}:
    post:
      tags:
        - Financial
      summary: Sync transactions
      description: Syncs transactions for all financial connections of a farm
      parameters:
        - name: farmId
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Transactions synced successfully

  /financial-connections/connections/{farmId}:
    get:
      tags:
        - Financial
      summary: Get financial connections
      description: Returns all financial connections for a farm
      parameters:
        - name: farmId
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Financial connections retrieved successfully

  # This is a partial schema. In a real implementation, all API endpoints would be documented.
  # The schema would include all routes, methods, parameters, request bodies, and responses.
  # For brevity, only a subset of endpoints is shown here.

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    Product:
      type: object
      properties:
        id:
          type: integer
        name:
          type: string
        description:
          type: string
        farmId:
          type: integer
        price:
          type: number
        unit:
          type: string
        sku:
          type: string
        barcode:
          type: string
        category:
          type: string
        tags:
          type: array
          items:
            type: string
        images:
          type: array
          items:
            type: string
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time

    Weather:
      type: object
      properties:
        current:
          type: object
          properties:
            temp:
              type: number
            feels_like:
              type: number
            humidity:
              type: number
            wind_speed:
              type: number
            wind_direction:
              type: string
            condition:
              type: string
            icon:
              type: string
            precipitation:
              type: number
            pressure:
              type: number
            uv_index:
              type: number
            visibility:
              type: number
            cloud_cover:
              type: number
            timestamp:
              type: string
              format: date-time
        forecast:
          type: array
          items:
            type: object
            properties:
              date:
                type: string
                format: date
              day:
                type: string
              high:
                type: number
              low:
                type: number
              condition:
                type: string
              icon:
                type: string
              precipitation_chance:
                type: number
              humidity:
                type: number
              wind_speed:
                type: number
              wind_direction:
                type: string
        hourly:
          type: array
          items:
            type: object
            properties:
              time:
                type: string
                format: date-time
              temp:
                type: number
              condition:
                type: string
              icon:
                type: string
              precipitation_chance:
                type: number
              humidity:
                type: number
              wind_speed:
                type: number
              wind_direction:
                type: string

    HarvestDirectionMap:
      type: object
      properties:
        id:
          type: integer
        name:
          type: string
        fieldId:
          type: integer
        directions:
          type: array
          items:
            type: object
            properties:
              startPoint:
                type: object
                properties:
                  lat:
                    type: number
                  lng:
                    type: number
              endPoint:
                type: object
                properties:
                  lat:
                    type: number
                  lng:
                    type: number
        notes:
          type: string
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
    User:
      type: object
      properties:
        id:
          type: integer
        email:
          type: string
        firstName:
          type: string
        lastName:
          type: string
        phone:
          type: string
        role:
          type: string
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time

    Farm:
      type: object
      properties:
        id:
          type: integer
        name:
          type: string
        address:
          type: string
        city:
          type: string
        state:
          type: string
        zipCode:
          type: string
        country:
          type: string
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time

    Field:
      type: object
      properties:
        id:
          type: integer
        name:
          type: string
        farmId:
          type: integer
        acres:
          type: number
        boundaries:
          type: array
          items:
            type: object
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time

    Equipment:
      type: object
      properties:
        id:
          type: integer
        name:
          type: string
        farmId:
          type: integer
        make:
          type: string
        model:
          type: string
        year:
          type: integer
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time

    InventoryCategory:
      type: object
      properties:
        id:
          type: integer
        name:
          type: string
        description:
          type: string
        farmId:
          type: integer
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time

    InventoryItem:
      type: object
      properties:
        id:
          type: integer
        name:
          type: string
        description:
          type: string
        categoryId:
          type: integer
        farmId:
          type: integer
        quantity:
          type: number
        unit:
          type: string
        barcode:
          type: string
        qrCode:
          type: string
        location:
          type: string
        purchaseDate:
          type: string
          format: date
        purchasePrice:
          type: number
        supplier:
          type: string
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time

    InventoryTransaction:
      type: object
      properties:
        id:
          type: integer
        inventoryItemId:
          type: integer
        type:
          type: string
          enum: [purchase, sale, adjustment, transfer]
        quantity:
          type: number
        date:
          type: string
          format: date-time
        notes:
          type: string
        price:
          type: number
        fromLocation:
          type: string
        toLocation:
          type: string
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time

    UserFarm:
      type: object
      properties:
        id:
          type: integer
        userId:
          type: integer
        farmId:
          type: integer
        role:
          type: string
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time

    Delivery:
      type: object
      properties:
        id:
          type: integer
        farmId:
          type: integer
        customerId:
          type: integer
        driverId:
          type: integer
        scheduledDate:
          type: string
          format: date-time
        status:
          type: string
          enum: [scheduled, in-transit, delivered, cancelled]
        items:
          type: array
          items:
            type: object
            properties:
              productId:
                type: integer
              quantity:
                type: number
        notes:
          type: string
        address:
          type: string
        city:
          type: string
        state:
          type: string
        zipCode:
          type: string
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time

    Driver:
      type: object
      properties:
        id:
          type: integer
        userId:
          type: integer
        farmId:
          type: integer
        licenseNumber:
          type: string
        licenseExpiration:
          type: string
          format: date
        vehicleType:
          type: string
        vehicleMake:
          type: string
        vehicleModel:
          type: string
        vehicleYear:
          type: integer
        vehiclePlate:
          type: string
        status:
          type: string
          enum: [active, inactive, on-leave]
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time

    DriverLocation:
      type: object
      properties:
        id:
          type: integer
        driverId:
          type: integer
        latitude:
          type: number
          format: float
        longitude:
          type: number
          format: float
        accuracy:
          type: number
          format: float
        altitude:
          type: number
          format: float
        speed:
          type: number
          format: float
        heading:
          type: number
          format: float
        timestamp:
          type: string
          format: date-time
        createdAt:
          type: string
          format: date-time

    DriverSchedule:
      type: object
      properties:
        id:
          type: integer
        driverId:
          type: integer
        startTime:
          type: string
          format: date-time
        endTime:
          type: string
          format: date-time
        status:
          type: string
          enum: [scheduled, in-progress, completed, cancelled]
        notes:
          type: string
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time

    Pickup:
      type: object
      properties:
        id:
          type: integer
        farmId:
          type: integer
        supplierId:
          type: integer
        driverId:
          type: integer
        scheduledDate:
          type: string
          format: date-time
        status:
          type: string
          enum: [scheduled, in-transit, completed, cancelled]
        items:
          type: array
          items:
            type: object
            properties:
              productId:
                type: integer
              quantity:
                type: number
        notes:
          type: string
        address:
          type: string
        city:
          type: string
        state:
          type: string
        zipCode:
          type: string
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time

    Receipt:
      type: object
      properties:
        id:
          type: integer
        farmId:
          type: integer
        userId:
          type: integer
        fileName:
          type: string
        fileUrl:
          type: string
        fileSize:
          type: integer
        fileType:
          type: string
        date:
          type: string
          format: date
        amount:
          type: number
        vendor:
          type: string
        category:
          type: string
        notes:
          type: string
        ocrProcessed:
          type: boolean
        ocrData:
          type: object
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time

security:
  - bearerAuth: []
