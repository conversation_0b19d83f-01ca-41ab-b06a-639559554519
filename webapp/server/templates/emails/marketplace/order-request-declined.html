<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Order Request Declined</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      margin: 0;
      padding: 0;
    }
    .container {
      max-width: 600px;
      margin: 0 auto;
      padding: 20px;
    }
    .header {
      background-color: #f8d7da;
      padding: 20px;
      text-align: center;
    }
    .header h1 {
      color: #721c24;
      margin: 0;
      font-size: 24px;
    }
    .content {
      padding: 20px;
      background-color: #f9f9f9;
    }
    .footer {
      padding: 20px;
      text-align: center;
      font-size: 12px;
      color: #777;
    }
    .button {
      display: inline-block;
      background-color: #4CAF50;
      color: white;
      text-decoration: none;
      padding: 10px 20px;
      border-radius: 4px;
      margin-top: 20px;
    }
    .order-details {
      margin-top: 20px;
      border: 1px solid #ddd;
      border-radius: 4px;
      padding: 15px;
      background-color: white;
    }
    .order-item {
      padding: 10px 0;
      border-bottom: 1px solid #eee;
    }
    .order-item:last-child {
      border-bottom: none;
    }
    .total {
      margin-top: 15px;
      font-weight: bold;
      text-align: right;
    }
    .decline-message {
      background-color: #f8d7da;
      border: 1px solid #f5c6cb;
      color: #721c24;
      padding: 15px;
      border-radius: 4px;
      margin-bottom: 20px;
    }
    .alternatives {
      background-color: #e8f4f8;
      border: 1px solid #bee5eb;
      color: #0c5460;
      padding: 15px;
      border-radius: 4px;
      margin-top: 20px;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>Order Request Declined</h1>
    </div>
    <div class="content">
      <div class="decline-message">
        <p><strong>We're sorry.</strong> Your order request has been declined by {{farmName}}.</p>
      </div>
      
      <p>Hello {{customerName}},</p>
      
      <p>Unfortunately, your recent order request could not be fulfilled at this time.</p>
      
      {{#if declineReason}}
      <p><strong>Reason provided by {{farmName}}:</strong></p>
      <p>{{declineReason}}</p>
      {{/if}}
      
      <p><strong>Order Request Details:</strong></p>
      <div class="order-details">
        <p><strong>Order Request ID:</strong> {{orderRequestId}}</p>
        <p><strong>Date Submitted:</strong> {{orderDate}}</p>
        <p><strong>Date Declined:</strong> {{declineDate}}</p>
        
        <p><strong>Items Requested:</strong></p>
        {{#each items}}
        <div class="order-item">
          <p>{{quantity}} x {{productName}} - ${{price}} each</p>
        </div>
        {{/each}}
        
        <div class="total">
          <p>Total: ${{total}}</p>
        </div>
      </div>
      
      {{#if hasAlternatives}}
      <div class="alternatives">
        <p><strong>Alternative Products Suggested by {{farmName}}:</strong></p>
        <ul>
          {{#each alternatives}}
          <li>{{name}} - ${{price}} each</li>
          {{/each}}
        </ul>
        <p>You can browse these alternatives and other products by visiting the farm's marketplace.</p>
      </div>
      {{/if}}
      
      <p>You can place a new order or contact the farm by clicking the button below:</p>
      
      <a href="{{farmMarketplaceUrl}}" class="button">Visit Farm Marketplace</a>
      
      <p>If you have any questions, please contact {{farmName}} directly.</p>
      
      <p>Thank you for your understanding and for supporting local farms!</p>
    </div>
    <div class="footer">
      <p>&copy; {{year}} NxtAcre. All rights reserved.</p>
      <p>This email was sent to {{email}}.</p>
    </div>
  </div>
</body>
</html>