<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>New Customer Message</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      margin: 0;
      padding: 0;
    }
    .container {
      max-width: 600px;
      margin: 0 auto;
      padding: 20px;
    }
    .header {
      background-color: #2ecc71;
      padding: 20px;
      text-align: center;
    }
    .header h1 {
      color: white;
      margin: 0;
      font-size: 24px;
    }
    .content {
      padding: 20px;
      background-color: #f9f9f9;
    }
    .footer {
      padding: 20px;
      text-align: center;
      font-size: 12px;
      color: #777;
    }
    .button {
      display: inline-block;
      background-color: #2ecc71;
      color: white;
      text-decoration: none;
      padding: 10px 20px;
      border-radius: 4px;
      margin-top: 20px;
    }
    .message-box {
      background-color: #f0f0f0;
      border-left: 4px solid #2ecc71;
      padding: 15px;
      margin: 20px 0;
      border-radius: 4px;
    }
    .customer-info {
      margin-top: 20px;
      border: 1px solid #ddd;
      border-radius: 4px;
      padding: 15px;
      background-color: white;
    }
    .order-reference {
      margin-top: 20px;
      border: 1px solid #ddd;
      border-radius: 4px;
      padding: 15px;
      background-color: white;
    }
    .message-icon {
      font-size: 24px;
      margin-right: 10px;
      vertical-align: middle;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>New Customer Message</h1>
    </div>
    <div class="content">
      <p>Hello {{farmAdminName}},</p>
      
      <p>You have received a new message from a customer.</p>
      
      <p><strong>Customer Information:</strong></p>
      <div class="customer-info">
        <p><strong>Name:</strong> {{customerName}}</p>
        <p><strong>Email:</strong> {{customerEmail}}</p>
        <p><strong>Phone:</strong> {{customerPhone}}</p>
      </div>
      
      {{#if orderReference}}
      <p><strong>Related Order:</strong></p>
      <div class="order-reference">
        <p><strong>Order ID:</strong> {{orderId}}</p>
        <p><strong>Order Date:</strong> {{orderDate}}</p>
        <p><strong>Order Status:</strong> {{orderStatus}}</p>
      </div>
      {{/if}}
      
      <p><strong>Message:</strong></p>
      <div class="message-box">
        <span class="message-icon">💬</span>
        <p>{{messageContent}}</p>
        <p><em>Sent on {{messageDate}} at {{messageTime}}</em></p>
      </div>
      
      <p>Please respond to this customer message as soon as possible. Good customer communication helps build trust and loyalty.</p>
      
      <p>You can reply to this message directly from your farm dashboard:</p>
      
      <a href="{{messageUrl}}" class="button">Reply to Message</a>
      
      <p>If you have any questions or need assistance with customer communications, please contact NxtAcre support.</p>
    </div>
    <div class="footer">
      <p>&copy; {{year}} NxtAcre. All rights reserved.</p>
      <p>This email was sent to {{email}}.</p>
    </div>
  </div>
</body>
</html>