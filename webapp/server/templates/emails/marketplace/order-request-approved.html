<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Order Request Approved</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      margin: 0;
      padding: 0;
    }
    .container {
      max-width: 600px;
      margin: 0 auto;
      padding: 20px;
    }
    .header {
      background-color: #4CAF50;
      padding: 20px;
      text-align: center;
    }
    .header h1 {
      color: white;
      margin: 0;
      font-size: 24px;
    }
    .content {
      padding: 20px;
      background-color: #f9f9f9;
    }
    .footer {
      padding: 20px;
      text-align: center;
      font-size: 12px;
      color: #777;
    }
    .button {
      display: inline-block;
      background-color: #4CAF50;
      color: white;
      text-decoration: none;
      padding: 10px 20px;
      border-radius: 4px;
      margin-top: 20px;
    }
    .order-details {
      margin-top: 20px;
      border: 1px solid #ddd;
      border-radius: 4px;
      padding: 15px;
      background-color: white;
    }
    .order-item {
      padding: 10px 0;
      border-bottom: 1px solid #eee;
    }
    .order-item:last-child {
      border-bottom: none;
    }
    .total {
      margin-top: 15px;
      font-weight: bold;
      text-align: right;
    }
    .success-message {
      background-color: #dff0d8;
      border: 1px solid #d6e9c6;
      color: #3c763d;
      padding: 15px;
      border-radius: 4px;
      margin-bottom: 20px;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>Order Request Approved</h1>
    </div>
    <div class="content">
      <div class="success-message">
        <p><strong>Good news!</strong> Your order request has been approved by {{farmName}}.</p>
      </div>
      
      <p>Hello {{customerName}},</p>
      
      <p>We're pleased to inform you that your order request has been approved and is now being processed.</p>
      
      <p><strong>Order Details:</strong></p>
      <div class="order-details">
        <p><strong>Order ID:</strong> {{orderId}}</p>
        <p><strong>Date Approved:</strong> {{approvalDate}}</p>
        <p><strong>Fulfillment Method:</strong> {{fulfillmentMethod}}</p>
        
        {{#if isDelivery}}
        <p><strong>Delivery Address:</strong><br>
          {{deliveryAddress}}<br>
          {{deliveryCity}}, {{deliveryState}} {{deliveryZipCode}}
        </p>
        <p><strong>Estimated Delivery:</strong> {{estimatedDeliveryDate}}</p>
        {{/if}}
        
        {{#if isPickup}}
        <p><strong>Pickup Information:</strong><br>
          Location: {{pickupLocation}}<br>
          Date: {{pickupDate}}
        </p>
        {{/if}}
        
        <p><strong>Items:</strong></p>
        {{#each items}}
        <div class="order-item">
          <p>{{quantity}} x {{productName}} - ${{price}} each</p>
        </div>
        {{/each}}
        
        <div class="total">
          <p>Subtotal: ${{subtotal}}</p>
          {{#if deliveryFee}}
          <p>Delivery Fee: ${{deliveryFee}}</p>
          {{/if}}
          <p>Total: ${{total}}</p>
        </div>
      </div>
      
      {{#if farmNotes}}
      <p><strong>Notes from {{farmName}}:</strong></p>
      <p>{{farmNotes}}</p>
      {{/if}}
      
      <p>You can track your order by clicking the button below:</p>
      
      <a href="{{orderUrl}}" class="button">Track Order</a>
      
      <p>If you have any questions about your order, please contact {{farmName}} directly.</p>
      
      <p>Thank you for supporting local farms!</p>
    </div>
    <div class="footer">
      <p>&copy; {{year}} NxtAcre. All rights reserved.</p>
      <p>This email was sent to {{email}}.</p>
    </div>
  </div>
</body>
</html>