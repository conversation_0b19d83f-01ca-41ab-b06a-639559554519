<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>New Order Request Received</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      margin: 0;
      padding: 0;
    }
    .container {
      max-width: 600px;
      margin: 0 auto;
      padding: 20px;
    }
    .header {
      background-color: #3498db;
      padding: 20px;
      text-align: center;
    }
    .header h1 {
      color: white;
      margin: 0;
      font-size: 24px;
    }
    .content {
      padding: 20px;
      background-color: #f9f9f9;
    }
    .footer {
      padding: 20px;
      text-align: center;
      font-size: 12px;
      color: #777;
    }
    .button {
      display: inline-block;
      background-color: #3498db;
      color: white;
      text-decoration: none;
      padding: 10px 20px;
      border-radius: 4px;
      margin-top: 20px;
    }
    .action-buttons {
      margin-top: 30px;
      text-align: center;
    }
    .approve-button {
      display: inline-block;
      background-color: #4CAF50;
      color: white;
      text-decoration: none;
      padding: 10px 20px;
      border-radius: 4px;
      margin-right: 10px;
    }
    .decline-button {
      display: inline-block;
      background-color: #e74c3c;
      color: white;
      text-decoration: none;
      padding: 10px 20px;
      border-radius: 4px;
    }
    .order-details {
      margin-top: 20px;
      border: 1px solid #ddd;
      border-radius: 4px;
      padding: 15px;
      background-color: white;
    }
    .order-item {
      padding: 10px 0;
      border-bottom: 1px solid #eee;
    }
    .order-item:last-child {
      border-bottom: none;
    }
    .total {
      margin-top: 15px;
      font-weight: bold;
      text-align: right;
    }
    .customer-info {
      margin-top: 20px;
      border: 1px solid #ddd;
      border-radius: 4px;
      padding: 15px;
      background-color: white;
    }
    .notification {
      background-color: #ffeaa7;
      border-left: 4px solid #fdcb6e;
      padding: 10px 15px;
      margin: 20px 0;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>New Order Request Received</h1>
    </div>
    <div class="content">
      <p>Hello {{farmAdminName}},</p>
      
      <p>You have received a new order request from the marketplace.</p>
      
      <div class="notification">
        <p><strong>Action Required:</strong> Please review and respond to this order request within 24 hours.</p>
      </div>
      
      <p><strong>Customer Information:</strong></p>
      <div class="customer-info">
        <p><strong>Name:</strong> {{customerName}}</p>
        <p><strong>Email:</strong> {{customerEmail}}</p>
        <p><strong>Phone:</strong> {{customerPhone}}</p>
      </div>
      
      <p><strong>Order Request Details:</strong></p>
      <div class="order-details">
        <p><strong>Order Request ID:</strong> {{orderRequestId}}</p>
        <p><strong>Date Submitted:</strong> {{orderDate}}</p>
        <p><strong>Fulfillment Method:</strong> {{fulfillmentMethod}}</p>
        
        {{#if isDelivery}}
        <p><strong>Delivery Address:</strong><br>
          {{deliveryAddress}}<br>
          {{deliveryCity}}, {{deliveryState}} {{deliveryZipCode}}
        </p>
        {{#if deliveryInstructions}}
        <p><strong>Delivery Instructions:</strong> {{deliveryInstructions}}</p>
        {{/if}}
        {{/if}}
        
        {{#if isPickup}}
        <p><strong>Requested Pickup Date:</strong> {{pickupDate}}</p>
        {{/if}}
        
        <p><strong>Items:</strong></p>
        {{#each items}}
        <div class="order-item">
          <p>{{quantity}} x {{productName}} - ${{price}} each</p>
          {{#if notes}}
          <p><em>Notes: {{notes}}</em></p>
          {{/if}}
        </div>
        {{/each}}
        
        <div class="total">
          <p>Subtotal: ${{subtotal}}</p>
          {{#if deliveryFee}}
          <p>Delivery Fee: ${{deliveryFee}}</p>
          {{/if}}
          <p>Total: ${{total}}</p>
        </div>
      </div>
      
      <div class="action-buttons">
        <a href="{{approveUrl}}" class="approve-button">Approve Order</a>
        <a href="{{declineUrl}}" class="decline-button">Decline Order</a>
      </div>
      
      <p>You can also view and manage this order request in your farm dashboard:</p>
      
      <a href="{{orderManagementUrl}}" class="button">View in Dashboard</a>
      
      <p>If you have any questions or need assistance, please contact NxtAcre support.</p>
    </div>
    <div class="footer">
      <p>&copy; {{year}} NxtAcre. All rights reserved.</p>
      <p>This email was sent to {{email}}.</p>
    </div>
  </div>
</body>
</html>