<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="color-scheme" content="light">
  <meta name="supported-color-schemes" content="light">
  <title>Subscription Renewal Notice</title>
  <style>
    :root {
      color-scheme: light;
      supported-color-schemes: light;
    }
    html {
      background-color: #ffffff !important;
    }
    body {
      font-family: Arial, sans-serif;
      line-height: 1.6;
      color: #333 !important;
      margin: 0;
      padding: 0;
      background-color: #ffffff !important;
    }
    .container {
      max-width: 600px;
      margin: 0 auto;
      padding: 20px;
      background-color: #ffffff !important;
    }
    .header {
      background-color: #0ea5e9;
      padding: 20px;
      text-align: center;
    }
    .logo {
      max-width: 200px;
      margin: 0 auto;
      display: block;
    }
    .header h1 {
      color: white;
      margin: 10px 0 0 0;
    }
    .content {
      padding: 20px;
      background-color: #f9f9f9;
    }
    .footer {
      text-align: center;
      padding: 10px;
      font-size: 12px;
      color: #666;
    }
    .button {
      display: inline-block;
      background-color: #0ea5e9;
      color: white;
      text-decoration: none;
      padding: 10px 20px;
      border-radius: 5px;
      margin-top: 20px;
    }
    .subscription-details {
      background-color: #f0f9ff;
      padding: 15px;
      border-radius: 5px;
      margin: 20px 0;
      border-left: 4px solid #0ea5e9;
    }
    .price {
      font-size: 24px;
      font-weight: bold;
      color: #0ea5e9;
    }
    .warning {
      color: #ef4444;
      font-weight: bold;
    }
    .accent {
      color: #14b8a6;
    }
    .secondary {
      color: #8b5cf6;
    }
    .status-box {
      padding: 15px;
      border-radius: 5px;
      margin: 15px 0;
    }
    .status-box.upcoming {
      background-color: rgba(139, 92, 246, 0.1);
      border-left: 4px solid #8b5cf6;
    }
    .status-box.successful {
      background-color: rgba(20, 184, 166, 0.1);
      border-left: 4px solid #14b8a6;
    }
    .status-box.failed {
      background-color: rgba(239, 68, 68, 0.1);
      border-left: 4px solid #ef4444;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <img src="https://app.nxtacre.com/logo.svg" alt="nxtAcre Logo" class="logo">
      <h1>Subscription Renewal Notice</h1>
    </div>
    <div class="content">
      <p>Hello {{firstName}},</p>

      {{#if upcoming}}
      <div class="status-box upcoming">
        <p>This is a friendly reminder that your <span class="accent">nxtAcre</span> subscription will renew automatically on <strong>{{renewalDate}}</strong>.</p>
      </div>
      <div class="subscription-details">
        <p><strong>Subscription Plan:</strong> {{planName}}</p>
        <p><strong>Renewal Amount:</strong> <span class="price">${{amount}}</span></p>
        <p><strong>Billing Cycle:</strong> {{billingCycle}}</p>
      </div>
      <p>No action is required if you wish to continue your subscription. Your payment method on file will be charged automatically.</p>
      <p>If you would like to make changes to your subscription, please click the button below to access your account settings:</p>
      <a href="{{accountUrl}}" class="button">Manage Subscription</a>
      {{/if}}

      {{#if successful}}
      <div class="status-box successful">
        <p>Thank you for your continued subscription to <span class="accent">nxtAcre</span>! Your subscription has been successfully renewed.</p>
      </div>
      <div class="subscription-details">
        <p><strong>Subscription Plan:</strong> {{planName}}</p>
        <p><strong>Amount Charged:</strong> <span class="price">${{amount}}</span></p>
        <p><strong>Next Renewal Date:</strong> {{nextRenewalDate}}</p>
      </div>
      <p>Your invoice is attached to this email for your records.</p>
      <p>If you have any questions about your subscription or need to make changes, please click the button below:</p>
      <a href="{{accountUrl}}" class="button">Manage Subscription</a>
      {{/if}}

      {{#if failed}}
      <div class="status-box failed">
        <p class="warning">We were unable to process your subscription renewal payment for <span class="accent">nxtAcre</span>.</p>
      </div>
      <div class="subscription-details">
        <p><strong>Subscription Plan:</strong> {{planName}}</p>
        <p><strong>Amount Due:</strong> <span class="price">${{amount}}</span></p>
        <p><strong>Payment Due Date:</strong> {{dueDate}}</p>
      </div>
      <p>Please update your payment information as soon as possible to avoid any interruption to your service. Your subscription will be suspended on {{suspensionDate}} if payment is not received.</p>
      <p>Click the button below to update your payment method:</p>
      <a href="{{paymentUpdateUrl}}" class="button">Update Payment Method</a>
      {{/if}}

      <p>If you have any questions or need assistance, please don't hesitate to contact our support team.</p>
      <p>Best regards,<br>The <span class="accent">nxtAcre</span> Team</p>
    </div>
    <div class="footer">
      <p>&copy; {{year}} nxtAcre. All rights reserved.</p>
      <p>This email was sent to {{email}}.</p>
    </div>
  </div>
</body>
</html>
