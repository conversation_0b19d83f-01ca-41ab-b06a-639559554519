import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';
import { defineModel } from '../utils/modelUtils.js';
import Farm from './Farm.js';
import Field from './Field.js';
import dotenv from 'dotenv';

dotenv.config();

const FarmFieldCollaboration = defineModel('FarmFieldCollaboration', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  field_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: Field,
      key: 'id'
    }
  },
  owner_farm_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: Farm,
      key: 'id'
    }
  },
  collaborator_farm_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: Farm,
      key: 'id'
    }
  },
  permission_level: {
    type: DataTypes.STRING(50),
    allowNull: false,
    defaultValue: 'read',
    validate: {
      isIn: [['read', 'write', 'admin']]
    },
    comment: 'Level of access granted to the collaborator farm: read, write, or admin'
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'farm_field_collaborations',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

// Associations are defined in associations.js

export default FarmFieldCollaboration;