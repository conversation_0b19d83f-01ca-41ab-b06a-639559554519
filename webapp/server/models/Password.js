import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';
import { defineModel } from '../utils/modelUtils.js';

const Password = defineModel('Password', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  group_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'password_groups',
      key: 'id'
    }
  },
  name: {
    type: DataTypes.STRING(100),
    allowNull: false
  },
  username: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: 'Encrypted username'
  },
  password: {
    type: DataTypes.STRING(1024),
    allowNull: true,
    comment: 'Encrypted password'
  },
  url: {
    type: DataTypes.STRING(1024),
    allowNull: true,
    comment: 'Encrypted URL'
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'Encrypted notes'
  },
  has_2fa: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  totp_secret: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: 'Encrypted TOTP secret for 2FA'
  },
  encryption_key_id: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: 'Identifier for the encryption key used'
  },
  encryption_iv: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: 'Initialization vector used for encryption'
  },
  created_by: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'passwords',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

// Instance methods
Password.prototype.toJSON = function() {
  const values = { ...this.get() };
  // Don't include sensitive data in JSON responses
  delete values.encryption_key_id;
  delete values.encryption_iv;
  return values;
};

// Static methods
Password.findByGroup = async function(groupId) {
  return await this.findAll({
    where: { group_id: groupId }
  });
};

export default Password;