import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';
import { defineModel } from '../utils/modelUtils.js';
import SignableDocument from './SignableDocument.js';

const DocumentBlockchainVerification = defineModel('DocumentBlockchainVerification', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  document_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: SignableDocument,
      key: 'id'
    }
  },
  blockchain_network: {
    type: DataTypes.STRING(50),
    allowNull: false,
    comment: 'The blockchain network used (e.g., Ethereum, Polygon, etc.)'
  },
  transaction_hash: {
    type: DataTypes.STRING(255),
    allowNull: false,
    comment: 'The transaction hash on the blockchain'
  },
  block_number: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'The block number where the transaction was included'
  },
  document_hash: {
    type: DataTypes.STRING(255),
    allowNull: false,
    comment: 'The hash of the document content'
  },
  verification_data: {
    type: DataTypes.JSONB,
    allowNull: true,
    comment: 'Additional verification data'
  },
  verification_status: {
    type: DataTypes.ENUM('pending', 'confirmed', 'failed'),
    allowNull: false,
    defaultValue: 'pending'
  },
  verified_at: {
    type: DataTypes.DATE,
    allowNull: true
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'document_blockchain_verifications',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      name: 'document_blockchain_verifications_document_id_idx',
      fields: ['document_id']
    },
    {
      name: 'document_blockchain_verifications_transaction_hash_idx',
      fields: ['transaction_hash']
    },
    {
      name: 'document_blockchain_verifications_verification_status_idx',
      fields: ['verification_status']
    }
  ]
});

export default DocumentBlockchainVerification;