import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';
import { defineModel } from '../utils/modelUtils.js';
import Farm from './Farm.js';
import dotenv from 'dotenv';

dotenv.config();

const CropType = defineModel('CropType', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  farm_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: Farm,
      key: 'id'
    }
  },
  name: {
    type: DataTypes.STRING(255),
    allowNull: false
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  growing_season: {
    type: DataTypes.STRING(50),
    allowNull: true,
    comment: 'Season when this crop type is typically grown (spring, summer, fall, winter, year-round)'
  },
  days_to_maturity: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'Average number of days from planting to harvest'
  },
  planting_depth: {
    type: DataTypes.DECIMAL(5, 2),
    allowNull: true,
    comment: 'Recommended planting depth in inches'
  },
  row_spacing: {
    type: DataTypes.DECIMAL(5, 2),
    allowNull: true,
    comment: 'Recommended row spacing in inches'
  },
  plant_spacing: {
    type: DataTypes.DECIMAL(5, 2),
    allowNull: true,
    comment: 'Recommended spacing between plants in inches'
  },
  ideal_soil_ph: {
    type: DataTypes.DECIMAL(4, 2),
    allowNull: true,
    comment: 'Ideal soil pH for this crop type'
  },
  ideal_temperature: {
    type: DataTypes.DECIMAL(5, 2),
    allowNull: true,
    comment: 'Ideal growing temperature in degrees Fahrenheit'
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'crop_types',
  
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

// Associations are defined in associations.js

export default CropType;
