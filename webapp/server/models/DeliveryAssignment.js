import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';
import DeliveryRoute from './DeliveryRoute.js';
import PurchaseRequest from './PurchaseRequest.js';

const DeliveryAssignment = sequelize.define('delivery_assignments', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  route_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'delivery_routes',
      key: 'id'
    },
    onDelete: 'CASCADE'
  },
  purchase_request_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'purchase_requests',
      key: 'id'
    },
    onDelete: 'CASCADE'
  },
  delivery_order: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: 'Order in the delivery route'
  },
  estimated_arrival_time: {
    type: DataTypes.DATE,
    allowNull: true
  },
  actual_arrival_time: {
    type: DataTypes.DATE,
    allowNull: true
  },
  status: {
    type: DataTypes.STRING(50),
    allowNull: false,
    defaultValue: 'pending',
    validate: {
      isIn: [['pending', 'in_progress', 'completed', 'canceled']]
    }
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  timestamps: false,
  underscored: true,
  tableName: 'delivery_assignments',
  indexes: [
    {
      unique: true,
      fields: ['route_id', 'purchase_request_id']
    }
  ]
});

// Define associations
// Note: Associations are now defined in associations.js to avoid duplicates

export default DeliveryAssignment;