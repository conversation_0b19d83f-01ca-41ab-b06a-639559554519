import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';
import { defineModel } from '../utils/modelUtils.js';

const UserRecoveryKey = defineModel('UserRecoveryKey', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  user_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    },
    unique: true
  },
  encrypted_recovery_key: {
    type: DataTypes.STRING(1024),
    allowNull: false,
    comment: 'Encrypted recovery key for the user'
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'user_recovery_keys',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

// Instance methods
UserRecoveryKey.prototype.toJSON = function() {
  const values = { ...this.get() };
  // Don't include the encrypted recovery key in JSON responses
  delete values.encrypted_recovery_key;
  return values;
};

// Static methods
UserRecoveryKey.findByUser = async function(userId) {
  return await this.findOne({
    where: { user_id: userId }
  });
};

UserRecoveryKey.createOrUpdate = async function(userId, encryptedRecoveryKey) {
  const [recoveryKey, created] = await this.findOrCreate({
    where: { user_id: userId },
    defaults: {
      encrypted_recovery_key: encryptedRecoveryKey
    }
  });

  if (!created) {
    recoveryKey.encrypted_recovery_key = encryptedRecoveryKey;
    await recoveryKey.save();
  }

  return recoveryKey;
};

export default UserRecoveryKey;