import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';
import { defineModel } from '../utils/modelUtils.js';
import Farm from './Farm.js';
import bcrypt from 'bcryptjs';
import dotenv from 'dotenv';

dotenv.config();

const Customer = defineModel('Customer', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  farm_id: {
    type: DataTypes.UUID,
    allowNull: true, // Allow null for global store customers
    references: {
      model: Farm,
      key: 'id'
    }
  },
  name: {
    type: DataTypes.STRING(255),
    allowNull: false
  },
  contact_name: {
    type: DataTypes.STRING(255),
    allowNull: true
  },
  email: {
    type: DataTypes.STRING(255),
    allowNull: true
  },
  phone: {
    type: DataTypes.STRING(20),
    allowNull: true
  },
  address: {
    type: DataTypes.STRING(255),
    allowNull: true
  },
  city: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  state: {
    type: DataTypes.STRING(50),
    allowNull: true
  },
  zip_code: {
    type: DataTypes.STRING(20),
    allowNull: true
  },
  country: {
    type: DataTypes.STRING(100),
    defaultValue: 'USA'
  },
  tax_id: {
    type: DataTypes.STRING(50),
    allowNull: true
  },
  is_tax_exempt: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false,
    comment: 'Whether this customer is exempt from taxes'
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  phone_book_subscription: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false,
    comment: 'Whether the customer is subscribed to phone book updates'
  },
  ios_phone_book_sync: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false,
    comment: 'Whether to sync with iOS phone book'
  },
  android_phone_book_sync: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false,
    comment: 'Whether to sync with Android phone book'
  },
  phone_book_last_sync: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: 'When the customer was last synced with phone books'
  },
  phone_book_sync_id: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: 'ID for tracking sync status'
  },
  external_phone_book_id: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: 'ID of the contact in external phone book systems'
  },
  password_hash: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: 'Hashed password for customer portal access'
  },
  reset_password_token: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: 'Token for password reset'
  },
  reset_password_expires: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: 'Expiration time for password reset token'
  },
  email_verified: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false,
    comment: 'Whether the customer has verified their email address'
  },
  email_verification_token: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: 'Token for email verification'
  },
  email_verification_expires: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: 'Expiration time for email verification token'
  },
  portal_access: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false,
    comment: 'Whether the customer has access to the customer portal'
  },
  origin: {
    type: DataTypes.STRING(50),
    defaultValue: 'farm',
    comment: 'Origin of the customer: farm, marketplace'
  },
  global_customer_id: {
    type: DataTypes.UUID,
    allowNull: true,
    comment: 'ID linking customers across farms for the same user'
  },
  marketplace_user_id: {
    type: DataTypes.UUID,
    allowNull: true,
    comment: 'ID of the user in the marketplace system'
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'customers',

  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  hooks: {
    beforeCreate: async (customer) => {
      if (customer.password_hash) {
        try {
          customer.password_hash = await bcrypt.hash(customer.password_hash, 10);
        } catch (error) {
          console.error('Error hashing customer password:', error);
          throw error;
        }
      }
    },
    beforeUpdate: async (customer) => {
      if (customer.changed('password_hash')) {
        try {
          customer.password_hash = await bcrypt.hash(customer.password_hash, 10);
        } catch (error) {
          console.error('Error hashing customer password:', error);
          throw error;
        }
      }
    }
  }
});

// Define associations
// Note: All associations are now defined in associations.js

// Instance methods
Customer.prototype.validatePassword = async function(password) {
  if (!this.password_hash) return false;
  return await bcrypt.compare(password, this.password_hash);
};

// Static methods
Customer.findByEmail = async function(email) {
  return await this.findOne({ where: { email } });
};

export default Customer;
