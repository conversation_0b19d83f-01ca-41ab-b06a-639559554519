import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';
import { defineModel } from '../utils/modelUtils.js';
import Employee from './Employee.js';

const PayStub = defineModel('PayStub', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  employee_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'employees',
      key: 'id'
    }
  },
  pay_period_start: {
    type: DataTypes.DATE,
    allowNull: false
  },
  pay_period_end: {
    type: DataTypes.DATE,
    allowNull: false
  },
  payment_date: {
    type: DataTypes.DATE,
    allowNull: false
  },
  gross_pay: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false
  },
  net_pay: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false
  },
  regular_hours: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true
  },
  overtime_hours: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true
  },
  regular_pay: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true
  },
  overtime_pay: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true
  },
  deductions: {
    type: DataTypes.JSONB,
    allowNull: true,
    comment: 'JSON object containing deduction details'
  },
  taxes: {
    type: DataTypes.JSONB,
    allowNull: true,
    comment: 'JSON object containing tax details'
  },
  benefits: {
    type: DataTypes.JSONB,
    allowNull: true,
    comment: 'JSON object containing benefits details'
  },
  year_to_date: {
    type: DataTypes.JSONB,
    allowNull: true,
    comment: 'JSON object containing year-to-date totals'
  },
  document_url: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: 'URL to the pay stub document'
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  status: {
    type: DataTypes.STRING(50),
    allowNull: false,
    defaultValue: 'issued',
    comment: 'issued, viewed, etc.'
  }
}, {
  tableName: 'pay_stubs',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

// Define associations
// Note: Associations are now defined in associations.js to avoid duplicates

export default PayStub;