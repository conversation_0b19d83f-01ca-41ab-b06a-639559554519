import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';
import { defineModel } from '../utils/modelUtils.js';
import Farm from './Farm.js';
import dotenv from 'dotenv';

dotenv.config();

const FuturePrice = defineModel('FuturePrice', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  farm_id: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: Farm,
      key: 'id'
    },
    comment: 'Optional farm association, null for global future prices'
  },
  commodity: {
    type: DataTypes.STRING(50),
    allowNull: false,
    comment: 'Name of the commodity'
  },
  price: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    comment: 'Future price of the commodity'
  },
  unit: {
    type: DataTypes.STRING(20),
    allowNull: false,
    comment: 'Unit of measurement (e.g., bushel, cwt, ton)'
  },
  month: {
    type: DataTypes.STRING(3),
    allowNull: false,
    comment: 'Month code (e.g., JAN, FEB)'
  },
  year: {
    type: DataTypes.STRING(4),
    allowNull: false,
    comment: 'Year of the future contract'
  },
  exchange: {
    type: DataTypes.STRING(20),
    allowNull: false,
    comment: 'Exchange where the future is traded (e.g., CME)'
  },
  contract_date: {
    type: DataTypes.DATEONLY,
    allowNull: true,
    comment: 'Contract date'
  },
  source: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: 'Source of the futures data'
  },
  api_source: {
    type: DataTypes.STRING(50),
    allowNull: true,
    comment: 'API source (e.g., AMS, DATA_GOV)'
  },
  is_cached: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: true,
    comment: 'Indicates if this is cached data'
  },
  cache_expiry: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: 'When the cache expires'
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'future_prices',
  
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      name: 'future_prices_commodity_month_year_idx',
      fields: ['commodity', 'month', 'year']
    },
    {
      name: 'future_prices_farm_id_idx',
      fields: ['farm_id']
    }
  ]
});

export default FuturePrice;