// This file is deprecated. Use Farm.js instead.
import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';
import { defineModel } from '../utils/modelUtils.js';
import Farm from './Farm.js';
import dotenv from 'dotenv';

dotenv.config();

// Get the schema from environment variables
const schema = process.env.DB_SCHEMA || 'site';
console.log(`Defining Tenant model (deprecated) with schema: ${schema}`);

// Create a minimal Tenant model that just references Farm
// This is to avoid breaking existing code that imports Tenant
const Tenant = Farm;

console.log(`Tenant model (deprecated) defined as an alias to Farm model`);

export default Tenant;
