import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';
import { defineModel } from '../utils/modelUtils.js';
import Farm from './Farm.js';
import Field from './Field.js';
import Crop from './Crop.js';
import dotenv from 'dotenv';

dotenv.config();

const HarvestSchedule = defineModel('HarvestSchedule', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  farm_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: Farm,
      key: 'id'
    }
  },
  field_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: Field,
      key: 'id'
    }
  },
  crop_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: Crop,
      key: 'id'
    }
  },
  scheduled_date: {
    type: DataTypes.DATEONLY,
    allowNull: false
  },
  status: {
    type: DataTypes.ENUM('planned', 'in_progress', 'completed', 'cancelled'),
    defaultValue: 'planned'
  },
  weather_dependent: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
    comment: 'Whether the harvest is dependent on weather conditions'
  },
  optimal_conditions: {
    type: DataTypes.JSONB,
    allowNull: true,
    comment: 'JSON object with optimal weather conditions for harvest'
  },
  actual_start_date: {
    type: DataTypes.DATEONLY,
    allowNull: true
  },
  actual_end_date: {
    type: DataTypes.DATEONLY,
    allowNull: true
  },
  yield_amount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true
  },
  yield_unit: {
    type: DataTypes.STRING(50),
    allowNull: true
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'harvest_schedules',
  
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

// Define associations
// Note: Associations are now defined in associations.js to avoid duplicates

export default HarvestSchedule;