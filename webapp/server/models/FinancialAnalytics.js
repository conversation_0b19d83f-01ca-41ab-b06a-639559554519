import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';
import { defineModel } from '../utils/modelUtils.js';
import Farm from './Farm.js';
import dotenv from 'dotenv';

dotenv.config();

const FinancialAnalytics = defineModel('FinancialAnalytics', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  farm_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: Farm,
      key: 'id'
    }
  },
  period_type: {
    type: DataTypes.STRING(20),
    allowNull: false,
    comment: 'Type of time period (monthly, quarterly, yearly)'
  },
  period_start: {
    type: DataTypes.DATE,
    allowNull: false,
    comment: 'Start date of the time period'
  },
  period_end: {
    type: DataTypes.DATE,
    allowNull: false,
    comment: 'End date of the time period'
  },
  revenue: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: false,
    defaultValue: 0,
    comment: 'Total revenue for the period'
  },
  expenses: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: false,
    defaultValue: 0,
    comment: 'Total expenses for the period'
  },
  profit: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: false,
    defaultValue: 0,
    comment: 'Total profit for the period (revenue - expenses)'
  },
  cash_flow: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: false,
    defaultValue: 0,
    comment: 'Net cash flow for the period'
  },
  projected_revenue: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: true,
    comment: 'Projected revenue for future periods'
  },
  projected_expenses: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: true,
    comment: 'Projected expenses for future periods'
  },
  projected_profit: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: true,
    comment: 'Projected profit for future periods'
  },
  projected_cash_flow: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: true,
    comment: 'Projected cash flow for future periods'
  },
  is_projection: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false,
    comment: 'Flag indicating if this record is a projection'
  },
  metrics: {
    type: DataTypes.JSONB,
    allowNull: true,
    comment: 'Additional financial metrics stored as JSON'
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'financial_analytics',
  
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      name: 'idx_financial_analytics_farm_id',
      fields: ['farm_id']
    },
    {
      name: 'idx_financial_analytics_period_type',
      fields: ['period_type']
    },
    {
      name: 'idx_financial_analytics_period_start',
      fields: ['period_start']
    },
    {
      name: 'idx_financial_analytics_period_end',
      fields: ['period_end']
    },
    {
      name: 'idx_financial_analytics_is_projection',
      fields: ['is_projection']
    }
  ]
});

// Define associations
// Note: Associations are now defined in associations.js to avoid duplicates

export default FinancialAnalytics;