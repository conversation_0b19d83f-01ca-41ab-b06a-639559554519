import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';
import { defineModel } from '../utils/modelUtils.js';
import dotenv from 'dotenv';

dotenv.config();

const RecurringBill = defineModel('RecurringBill', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  bill_id: {
    type: DataTypes.UUID,
    allowNull: false
  },
  frequency: {
    type: DataTypes.STRING(50),
    allowNull: false
  },
  start_date: {
    type: DataTypes.DATEONLY,
    allowNull: false
  },
  end_date: {
    type: DataTypes.DATEONLY,
    allowNull: true
  },
  day_of_month: {
    type: DataTypes.INTEGER,
    allowNull: true
  },
  day_of_week: {
    type: DataTypes.INTEGER,
    allowNull: true
  },
  week_of_month: {
    type: DataTypes.INTEGER,
    allowNull: true
  },
  month_of_year: {
    type: DataTypes.INTEGER,
    allowNull: true
  },
  last_generated_date: {
    type: DataTypes.DATEONLY,
    allowNull: true
  },
  next_due_date: {
    type: DataTypes.DATEONLY,
    allowNull: false
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'recurring_bills',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

export default RecurringBill;