import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';
import { defineModel } from '../utils/modelUtils.js';

const ShoppingCart = defineModel('ShoppingCart', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  user_id: {
    type: DataTypes.UUID,
    allowNull: true
  },
  session_id: {
    type: DataTypes.STRING(255),
    allowNull: true
  },
  farm_id: {
    type: DataTypes.UUID,
    allowNull: true
  },
  is_saved: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false
  },
  fulfillment_method: {
    type: DataTypes.STRING(50),
    allowNull: true,
    comment: 'Method of fulfillment: delivery, pickup'
  },
  pickup_date: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: 'Scheduled date for pickup (if applicable)'
  },
  delivery_address_id: {
    type: DataTypes.UUID,
    allowNull: true,
    comment: 'Reference to the delivery address (if applicable)'
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'shopping_carts',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

// Associations are defined in associations.js

export default ShoppingCart;
