import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';
import { defineModel } from '../utils/modelUtils.js';
import ApiProvider from './ApiProvider.js';
import ApiEndpoint from './ApiEndpoint.js';
import dotenv from 'dotenv';

dotenv.config();

const ApiAnalytics = defineModel('ApiAnalytics', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  provider_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: ApiProvider,
      key: 'id'
    }
  },
  endpoint_id: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: ApiEndpoint,
      key: 'id'
    }
  },
  date: {
    type: DataTypes.DATEONLY,
    allowNull: false
  },
  request_count: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0
  },
  error_count: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0
  },
  cache_hit_count: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0
  },
  avg_response_time_ms: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'api_analytics',
  
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

// Define associations
// Note: Associations are now defined in associations.js to avoid duplicates

export default ApiAnalytics;