import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';
import DeliveryRoute from './DeliveryRoute.js';

const DeliveryTracking = sequelize.define('delivery_tracking', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  route_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'delivery_routes',
      key: 'id'
    },
    onDelete: 'CASCADE'
  },
  latitude: {
    type: DataTypes.DECIMAL(10, 8),
    allowNull: false,
    validate: {
      min: -90,
      max: 90
    }
  },
  longitude: {
    type: DataTypes.DECIMAL(11, 8),
    allowNull: false,
    validate: {
      min: -180,
      max: 180
    }
  },
  timestamp: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  timestamps: false,
  underscored: true,
  tableName: 'delivery_tracking'
});

// Define associations
// Note: Associations are now defined in associations.js to avoid duplicates

export default DeliveryTracking;