import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';
import { defineModel } from '../utils/modelUtils.js';
import dotenv from 'dotenv';
import Farm from './Farm.js';

dotenv.config();

const Integration = defineModel('Integration', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  name: {
    type: DataTypes.STRING(255),
    allowNull: false
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  version: {
    type: DataTypes.STRING(50),
    allowNull: false
  },
  author: {
    type: DataTypes.STRING(255),
    allowNull: false
  },
  entry_point: {
    type: DataTypes.STRING(255),
    allowNull: false,
    comment: 'Path to the main file of the integration'
  },
  enabled: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  settings: {
    type: DataTypes.JSONB,
    allowNull: true,
    defaultValue: {},
    comment: 'JSON object containing integration settings'
  },
  icon: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: 'URL or path to the integration icon'
  },
  farm_id: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: Farm,
      key: 'id'
    },
    comment: 'If null, the integration is available to all farms'
  },
  is_global: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false,
    comment: 'If true, this is a global system level add-on; if false, it is a per-farm add-on'
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'integrations',
  
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

// Define associations
// Note: Associations are now defined in associations.js to avoid duplicates

export default Integration;
