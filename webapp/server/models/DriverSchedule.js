import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';
import { defineModel } from '../utils/modelUtils.js';
import dotenv from 'dotenv';
import Farm from './Farm.js';
import Driver from './Driver.js';
import Delivery from './Delivery.js';
import Pickup from './Pickup.js';

dotenv.config();

const DriverSchedule = defineModel('DriverSchedule', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  farm_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: Farm,
      key: 'id'
    }
  },
  driver_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: Driver,
      key: 'id'
    }
  },
  delivery_id: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: Delivery,
      key: 'id'
    },
    comment: 'The delivery associated with this schedule (if applicable)'
  },
  pickup_id: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: Pickup,
      key: 'id'
    },
    comment: 'The pickup associated with this schedule (if applicable)'
  },
  schedule_type: {
    type: DataTypes.ENUM('delivery', 'pickup', 'maintenance', 'break', 'other'),
    allowNull: false
  },
  title: {
    type: DataTypes.STRING(255),
    allowNull: false
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  start_time: {
    type: DataTypes.DATE,
    allowNull: false
  },
  end_time: {
    type: DataTypes.DATE,
    allowNull: false
  },
  is_all_day: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  status: {
    type: DataTypes.ENUM('scheduled', 'in_progress', 'completed', 'cancelled'),
    allowNull: false,
    defaultValue: 'scheduled'
  },
  location: {
    type: DataTypes.STRING(255),
    allowNull: true
  },
  location_lat: {
    type: DataTypes.DECIMAL(10, 7),
    allowNull: true
  },
  location_lng: {
    type: DataTypes.DECIMAL(10, 7),
    allowNull: true
  },
  priority: {
    type: DataTypes.ENUM('low', 'medium', 'high'),
    defaultValue: 'medium'
  },
  recurrence_pattern: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: 'Pattern for recurring schedules (e.g., "daily", "weekly", "monthly")'
  },
  recurrence_end_date: {
    type: DataTypes.DATE,
    allowNull: true
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'driver_schedules',
  
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

// Associations are defined in associations.js

export default DriverSchedule;
