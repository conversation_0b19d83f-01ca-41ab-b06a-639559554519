import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';
import { defineModel } from '../utils/modelUtils.js';
import dotenv from 'dotenv';
import Farm from './Farm.js';
import Role from './Role.js';

dotenv.config();

const RolePermission = defineModel('RolePermission', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  farm_id: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: Farm,
      key: 'id'
    }
  },
  role_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: Role,
      key: 'id'
    },
    comment: 'Reference to the role'
  },
  role_name: {
    type: DataTypes.STRING(50),
    allowNull: true,
    comment: 'Name of the role (deprecated, use role_id instead)'
  },
  feature: {
    type: DataTypes.STRING(100),
    allowNull: false,
    comment: 'Name of the feature or module'
  },
  can_view: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  can_create: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  can_edit: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  can_delete: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  can_approve: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Whether the role can approve items in this feature'
  },
  can_reject: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Whether the role can reject items in this feature'
  },
  can_assign: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Whether the role can assign items to users in this feature'
  },
  can_export: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Whether the role can export data from this feature'
  },
  can_import: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Whether the role can import data into this feature'
  },
  can_manage_settings: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Whether the role can manage settings for this feature'
  },
  can_generate_reports: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Whether the role can generate reports for this feature'
  },
  can_view_sensitive: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Whether the role can view sensitive information in this feature'
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'role_permissions',
  
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      name: 'role_permissions_farm_role_feature_idx',
      fields: ['farm_id', 'role_id', 'feature'],
      unique: true
    }
  ]
});

// Define associations
// Note: Associations are now defined in associations.js to avoid duplicates

export default RolePermission;
