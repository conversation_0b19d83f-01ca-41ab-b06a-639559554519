import { DataTypes } from 'sequelize';
import { defineModel } from '../utils/modelUtils.js';
import Invoice from './Invoice.js';
import User from './User.js';
import Farm from './Farm.js';

const InvoiceDispute = defineModel('InvoiceDispute', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  invoice_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: Invoice,
      key: 'id'
    },
    comment: 'The invoice being disputed'
  },
  dispute_number: {
    type: DataTypes.STRING(50),
    allowNull: false,
    unique: true,
    comment: 'Unique dispute reference number'
  },
  raised_by_farm_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: Farm,
      key: 'id'
    },
    comment: 'Farm that raised the dispute'
  },
  raised_by_user_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: User,
      key: 'id'
    },
    comment: 'User who raised the dispute'
  },
  dispute_type: {
    type: DataTypes.ENUM(
      'amount_incorrect',
      'services_not_provided',
      'quality_issue',
      'billing_error',
      'duplicate_charge',
      'unauthorized_charge',
      'other'
    ),
    allowNull: false,
    comment: 'Type of dispute'
  },
  subject: {
    type: DataTypes.STRING(255),
    allowNull: false,
    comment: 'Brief subject of the dispute'
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: false,
    comment: 'Detailed description of the dispute'
  },
  disputed_amount: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: true,
    comment: 'Amount being disputed (if applicable)'
  },
  status: {
    type: DataTypes.ENUM(
      'open',
      'under_review',
      'awaiting_response',
      'resolved',
      'closed',
      'escalated'
    ),
    allowNull: false,
    defaultValue: 'open',
    comment: 'Current status of the dispute'
  },
  priority: {
    type: DataTypes.ENUM('low', 'medium', 'high', 'urgent'),
    allowNull: false,
    defaultValue: 'medium',
    comment: 'Priority level of the dispute'
  },
  resolution: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'Resolution details when dispute is resolved'
  },
  resolved_by_user_id: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: User,
      key: 'id'
    },
    comment: 'User who resolved the dispute'
  },
  resolved_at: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: 'When the dispute was resolved'
  },
  due_date: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: 'Expected resolution date'
  },
  escalated_at: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: 'When the dispute was escalated'
  },
  escalated_to_user_id: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: User,
      key: 'id'
    },
    comment: 'User the dispute was escalated to'
  },
  supporting_documents: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'Array of supporting document URLs and metadata'
  },
  tags: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'Array of tags for categorization'
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'invoice_disputes',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      name: 'invoice_disputes_invoice_id_idx',
      fields: ['invoice_id']
    },
    {
      name: 'invoice_disputes_raised_by_farm_id_idx',
      fields: ['raised_by_farm_id']
    },
    {
      name: 'invoice_disputes_status_idx',
      fields: ['status']
    },
    {
      name: 'invoice_disputes_priority_idx',
      fields: ['priority']
    },
    {
      name: 'invoice_disputes_created_at_idx',
      fields: ['created_at']
    }
  ]
});

export default InvoiceDispute;
