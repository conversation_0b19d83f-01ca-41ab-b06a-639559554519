import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';
import { defineModel } from '../utils/modelUtils.js';
import Farm from './Farm.js';
import dotenv from 'dotenv';

dotenv.config();

const FarmAssociation = defineModel('FarmAssociation', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  initiator_farm_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: Farm,
      key: 'id'
    }
  },
  associated_farm_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: Farm,
      key: 'id'
    }
  },
  status: {
    type: DataTypes.STRING(50),
    allowNull: false,
    defaultValue: 'pending',
    validate: {
      isIn: [['pending', 'active', 'rejected', 'revoked', 'pending_replacement']]
    },
    comment: 'Status of the association: pending (waiting for approval), active (approved), rejected, revoked, or pending_replacement (waiting for a new association to be approved)'
  },
  association_type: {
    type: DataTypes.STRING(50),
    allowNull: false,
    defaultValue: 'general',
    comment: 'Type of association between farms: general, business, supplier, customer, etc.'
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'farm_associations',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

// Static methods
FarmAssociation.areFarmsAssociated = async function(farmId1, farmId2) {
  try {
    const query = `
      SELECT COUNT(*) as count
      FROM farm_associations
      WHERE (
        (initiator_farm_id = :farmId1 AND associated_farm_id = :farmId2)
        OR
        (initiator_farm_id = :farmId2 AND associated_farm_id = :farmId1)
      )
      AND (status = 'active' OR status = 'pending_replacement')
    `;

    const [result] = await sequelize.query(query, {
      replacements: { farmId1, farmId2 },
      type: sequelize.QueryTypes.SELECT
    });

    return result.count > 0;
  } catch (error) {
    console.error('Error checking if farms are associated:', error);
    return false;
  }
};

export default FarmAssociation;
