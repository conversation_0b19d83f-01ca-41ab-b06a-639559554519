import { DataTypes } from 'sequelize';
import { defineModel } from '../utils/modelUtils.js';
import Customer from './Customer.js';
import Farm from './Farm.js';
import InvoiceQuestion from './InvoiceQuestion.js';

const CustomerNotification = defineModel('CustomerNotification', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  title: {
    type: DataTypes.STRING,
    allowNull: false,
    comment: 'Notification title'
  },
  message: {
    type: DataTypes.TEXT,
    allowNull: false,
    comment: 'Notification message content'
  },
  type: {
    type: DataTypes.ENUM('info', 'warning', 'success'),
    allowNull: false,
    defaultValue: 'info',
    comment: 'Notification type'
  },
  read: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false,
    comment: 'Whether the notification has been read'
  },
  farm_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: Farm,
      key: 'id'
    },
    comment: 'Farm this notification belongs to'
  },
  customer_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: Customer,
      key: 'id'
    },
    comment: 'Customer this notification is for'
  },
  related_entity_type: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: 'Type of entity this notification is related to (e.g., invoice_question)'
  },
  related_entity_id: {
    type: DataTypes.UUID,
    allowNull: true,
    comment: 'ID of the entity this notification is related to'
  },
  action_url: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: 'URL for action to take on this notification'
  },
  created_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'customer_notifications',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

// Define associations
// Note: Associations are now defined in associations.js to avoid duplicates

export default CustomerNotification;