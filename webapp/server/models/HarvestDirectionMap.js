import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';
import { defineModel } from '../utils/modelUtils.js';
import Field from './Field.js';
import dotenv from 'dotenv';

dotenv.config();

const HarvestDirectionMap = defineModel('HarvestDirectionMap', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  field_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: Field,
      key: 'id'
    }
  },
  name: {
    type: DataTypes.STRING(255),
    allowNull: false
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  order_index: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 1
  },
  elements: {
    type: DataTypes.JSONB,
    allowNull: false,
    defaultValue: [],
    comment: 'Array of drawing elements (polygons, polylines, markers) with properties like color, label, etc.'
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'harvest_direction_maps',
  
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

// Associations are defined in associations.js

export default HarvestDirectionMap;