import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';
import { defineModel } from '../utils/modelUtils.js';
import Farm from './Farm.js';
import Field from './Field.js';
import dotenv from 'dotenv';

dotenv.config();

const Weather = defineModel('Weather', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  farm_id: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: Farm,
      key: 'id'
    }
  },
  field_id: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: Field,
      key: 'id'
    }
  },
  latitude: {
    type: DataTypes.DECIMAL(10, 6),
    allowNull: false
  },
  longitude: {
    type: DataTypes.DECIMAL(10, 6),
    allowNull: false
  },
  timestamp: {
    type: DataTypes.DATE,
    allowNull: false
  },
  temperature: {
    type: DataTypes.DECIMAL(5, 2),
    allowNull: true
  },
  feels_like: {
    type: DataTypes.DECIMAL(5, 2),
    allowNull: true
  },
  humidity: {
    type: DataTypes.INTEGER,
    allowNull: true
  },
  wind_speed: {
    type: DataTypes.DECIMAL(5, 2),
    allowNull: true
  },
  wind_direction: {
    type: DataTypes.STRING(10),
    allowNull: true
  },
  precipitation: {
    type: DataTypes.DECIMAL(5, 2),
    allowNull: true
  },
  precipitation_chance: {
    type: DataTypes.INTEGER,
    allowNull: true
  },
  condition: {
    type: DataTypes.STRING(50),
    allowNull: true
  },
  icon: {
    type: DataTypes.STRING(20),
    allowNull: true
  },
  forecast_type: {
    type: DataTypes.ENUM('current', 'hourly', 'daily'),
    allowNull: false,
    defaultValue: 'current'
  },
  forecast_hour: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'Hour offset from current time for hourly forecasts'
  },
  forecast_day: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'Day offset from current time for daily forecasts'
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'weather',
  
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

// Define associations
// Note: Associations are now defined in associations.js to avoid duplicates

export default Weather;