import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';
import { defineModel } from '../utils/modelUtils.js';
import dotenv from 'dotenv';

dotenv.config();

const TaxPayment = defineModel('TaxPayment', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  farm_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'farms',
      key: 'id'
    }
  },
  payment_date: {
    type: DataTypes.DATE,
    allowNull: false
  },
  amount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false
  },
  payment_method: {
    type: DataTypes.ENUM('check', 'electronic', 'credit_card', 'other'),
    allowNull: false
  },
  payment_reference: {
    type: DataTypes.STRING,
    allowNull: true
  },
  tax_year: {
    type: DataTypes.INTEGER,
    allowNull: false
  },
  tax_period: {
    type: DataTypes.ENUM('q1', 'q2', 'q3', 'q4', 'annual'),
    allowNull: false
  },
  tax_type: {
    type: DataTypes.ENUM('income', 'property', 'payroll', 'sales', 'other'),
    allowNull: false
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  receipt_document_id: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: 'tax_documents',
      key: 'id'
    }
  },
  created_by: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'tax_payments',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

export default TaxPayment;