import { DataTypes } from 'sequelize';
import { defineModel } from '../utils/modelUtils.js';

const InvoiceDocument = defineModel('InvoiceDocument', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  invoice_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'invoices',
      key: 'id'
    },
    comment: 'The invoice this document belongs to'
  },
  name: {
    type: DataTypes.STRING(255),
    allowNull: false,
    comment: 'Original filename of the uploaded document'
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'Optional description of the document'
  },
  file_path: {
    type: DataTypes.STRING(1024),
    allowNull: false,
    comment: 'Storage path in Digital Ocean Spaces'
  },
  file_size: {
    type: DataTypes.BIGINT,
    allowNull: false,
    comment: 'Size of the document in bytes'
  },
  mime_type: {
    type: DataTypes.STRING(100),
    allowNull: false,
    comment: 'MIME type of the document (e.g., application/pdf, text/plain)'
  },
  uploaded_by: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    },
    comment: 'User who uploaded the document'
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'invoice_documents',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      name: 'invoice_documents_invoice_id_idx',
      fields: ['invoice_id']
    },
    {
      name: 'invoice_documents_uploaded_by_idx',
      fields: ['uploaded_by']
    }
  ]
});

export default InvoiceDocument;
