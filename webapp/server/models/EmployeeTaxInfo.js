import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';
import { defineModel } from '../utils/modelUtils.js';
import dotenv from 'dotenv';

dotenv.config();

const EmployeeTaxInfo = defineModel('EmployeeTaxInfo', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  employee_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'employees',
      key: 'id'
    }
  },
  farm_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'farms',
      key: 'id'
    }
  },
  tax_year: {
    type: DataTypes.INTEGER,
    allowNull: false
  },
  ssn: {
    type: DataTypes.STRING,
    allowNull: true
  },
  filing_status: {
    type: DataTypes.ENUM('single', 'married_joint', 'married_separate', 'head_of_household', 'qualifying_widow'),
    allowNull: true
  },
  withholding_allowances: {
    type: DataTypes.INTEGER,
    allowNull: true,
    defaultValue: 0
  },
  additional_withholding: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    defaultValue: 0.00
  },
  is_exempt: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false
  },
  w2_generated: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false
  },
  w2_document_id: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: 'tax_documents',
      key: 'id'
    }
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'employee_tax_info',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

export default EmployeeTaxInfo;