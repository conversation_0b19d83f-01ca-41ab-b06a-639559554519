import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';
import { defineModel } from '../utils/modelUtils.js';
import User from './User.js';
import dotenv from 'dotenv';

dotenv.config();

const Session = defineModel('Session', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  user_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: User,
      key: 'id'
    }
  },
  token: {
    type: DataTypes.STRING(255),
    allowNull: false,
    unique: true,
    comment: 'JWT token identifier for this session'
  },
  ip_address: {
    type: DataTypes.STRING(45),
    allowNull: true,
    comment: 'IP address of the client'
  },
  user_agent: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'User agent string of the client browser/device'
  },
  device_type: {
    type: DataTypes.STRING(50),
    allowNull: true,
    comment: 'Type of device (desktop, mobile, tablet)'
  },
  browser: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: 'Browser name and version'
  },
  operating_system: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: 'Operating system name and version'
  },
  location: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: 'Approximate location based on IP (city, country)'
  },
  last_active_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
    comment: 'Timestamp of last activity'
  },
  expires_at: {
    type: DataTypes.DATE,
    allowNull: false,
    comment: 'Timestamp when this session expires'
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
    comment: 'Whether this session is currently active'
  },
  device_fingerprint: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: 'Unique fingerprint for this device'
  },
  is_trusted: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Whether this device is trusted (no 2FA required)'
  },
  device_name: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: 'User-friendly name for this device'
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'sessions',
  
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

// Define associations
// Note: Associations are now defined in associations.js to avoid duplicates

export default Session;
