import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';
import { defineModel } from '../utils/modelUtils.js';
import Crop from './Crop.js';
import dotenv from 'dotenv';

dotenv.config();

const CropActivity = defineModel('CropActivity', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  crop_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: Crop,
      key: 'id'
    }
  },
  activity_type: {
    type: DataTypes.STRING(50),
    allowNull: false
  },
  activity_date: {
    type: DataTypes.DATEONLY,
    allowNull: false
  },
  field_location: {
    type: DataTypes.STRING(255),
    allowNull: true
  },
  acres: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'crop_activities',
  
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

// Associations are defined in associations.js

export default CropActivity;
