import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';
import { defineModel } from '../utils/modelUtils.js';
import Invoice from './Invoice.js';
import dotenv from 'dotenv';

dotenv.config();

const RecurringInvoice = defineModel('RecurringInvoice', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  invoice_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: Invoice,
      key: 'id'
    }
  },
  frequency: {
    type: DataTypes.STRING(50),
    allowNull: false
  },
  start_date: {
    type: DataTypes.DATEONLY,
    allowNull: false
  },
  end_date: {
    type: DataTypes.DATEONLY,
    allowNull: true
  },
  day_of_month: {
    type: DataTypes.INTEGER,
    allowNull: true
  },
  day_of_week: {
    type: DataTypes.INTEGER,
    allowNull: true
  },
  week_of_month: {
    type: DataTypes.INTEGER,
    allowNull: true
  },
  month_of_year: {
    type: DataTypes.INTEGER,
    allowNull: true
  },
  last_generated_date: {
    type: DataTypes.DATEONLY,
    allowNull: true
  },
  next_due_date: {
    type: DataTypes.DATEONLY,
    allowNull: false
  },
  auto_send: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    allowNull: false
  },
  additional_recipient_emails: {
    type: DataTypes.ARRAY(DataTypes.STRING),
    allowNull: true
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'recurring_invoices',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

// Note: Associations are defined in associations.js to avoid duplicates

export default RecurringInvoice;
