import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';
import { defineModel } from '../utils/modelUtils.js';
import dotenv from 'dotenv';

dotenv.config();

const TaxFiling = defineModel('TaxFiling', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  farm_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'farms',
      key: 'id'
    }
  },
  tax_year: {
    type: DataTypes.INTEGER,
    allowNull: false
  },
  filing_type: {
    type: DataTypes.ENUM('federal_income', 'state_income', 'property', 'payroll', 'sales', 'other'),
    allowNull: false
  },
  form_number: {
    type: DataTypes.STRING,
    allowNull: true
  },
  due_date: {
    type: DataTypes.DATE,
    allowNull: false
  },
  filing_date: {
    type: DataTypes.DATE,
    allowNull: true
  },
  extension_filed: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    allowNull: false
  },
  extension_date: {
    type: DataTypes.DATE,
    allowNull: true
  },
  status: {
    type: DataTypes.ENUM('not_started', 'in_progress', 'ready_to_file', 'filed', 'accepted', 'rejected', 'amended'),
    defaultValue: 'not_started',
    allowNull: false
  },
  filing_method: {
    type: DataTypes.ENUM('electronic', 'paper', 'tax_professional'),
    allowNull: true
  },
  confirmation_number: {
    type: DataTypes.STRING,
    allowNull: true
  },
  total_tax: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true
  },
  total_paid: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true
  },
  balance_due: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true
  },
  refund_amount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  document_id: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: 'tax_documents',
      key: 'id'
    }
  },
  created_by: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'tax_filings',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

export default TaxFiling;