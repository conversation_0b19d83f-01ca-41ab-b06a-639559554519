import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';
import { defineModel } from '../utils/modelUtils.js';
import dotenv from 'dotenv';
import Farm from './Farm.js';
import Driver from './Driver.js';

dotenv.config();

const DriverLocation = defineModel('DriverLocation', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  farm_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: Farm,
      key: 'id'
    }
  },
  driver_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: Driver,
      key: 'id'
    }
  },
  latitude: {
    type: DataTypes.DECIMAL(10, 7),
    allowNull: false
  },
  longitude: {
    type: DataTypes.DECIMAL(10, 7),
    allowNull: false
  },
  accuracy: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    comment: 'Accuracy of the location in meters'
  },
  altitude: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true
  },
  speed: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    comment: 'Speed in km/h'
  },
  heading: {
    type: DataTypes.DECIMAL(5, 2),
    allowNull: true,
    comment: 'Direction in degrees (0-360)'
  },
  address: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: 'Reverse geocoded address'
  },
  timestamp: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
    comment: 'When the location was recorded'
  },
  device_id: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: 'ID of the device that reported the location'
  },
  battery_level: {
    type: DataTypes.DECIMAL(5, 2),
    allowNull: true,
    comment: 'Battery level of the device (0-100)'
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'driver_locations',
  
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      name: 'idx_driver_locations_driver_id',
      fields: ['driver_id']
    },
    {
      name: 'idx_driver_locations_timestamp',
      fields: ['timestamp']
    }
  ]
});

// Associations are defined in associations.js

// Static methods
DriverLocation.getLatestLocationForDriver = async function(driverId) {
  return await this.findOne({
    where: { driver_id: driverId },
    order: [['timestamp', 'DESC']]
  });
};

export default DriverLocation;
