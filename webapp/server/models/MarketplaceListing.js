import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';
import { defineModel } from '../utils/modelUtils.js';
import Farm from './Farm.js';
import User from './User.js';
import Product from './Product.js';
import dotenv from 'dotenv';

dotenv.config();

const MarketplaceListing = defineModel('MarketplaceListing', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  farm_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: Farm,
      key: 'id'
    }
  },
  created_by: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: User,
      key: 'id'
    }
  },
  product_id: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: Product,
      key: 'id'
    }
  },
  title: {
    type: DataTypes.STRING(100),
    allowNull: false
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  listing_type: {
    type: DataTypes.STRING(20),
    allowNull: false,
    comment: 'Type of listing: sell, buy, service, rent, etc.'
  },
  category: {
    type: DataTypes.STRING(50),
    allowNull: false,
    comment: 'Category of the listing'
  },
  subcategory: {
    type: DataTypes.STRING(50),
    allowNull: true,
    comment: 'Subcategory of the listing'
  },
  price: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    comment: 'Price of the item or service'
  },
  price_type: {
    type: DataTypes.STRING(20),
    allowNull: true,
    comment: 'Type of pricing: fixed, negotiable, auction, etc.'
  },
  currency: {
    type: DataTypes.STRING(3),
    allowNull: true,
    defaultValue: 'USD'
  },
  quantity: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    comment: 'Quantity available'
  },
  unit: {
    type: DataTypes.STRING(20),
    allowNull: true,
    comment: 'Unit of measurement (e.g., kg, lb, each)'
  },
  location: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: 'Location of the item or service'
  },
  latitude: {
    type: DataTypes.DECIMAL(10, 6),
    allowNull: true,
    comment: 'Latitude for location-aware search'
  },
  longitude: {
    type: DataTypes.DECIMAL(10, 6),
    allowNull: true,
    comment: 'Longitude for location-aware search'
  },
  images: {
    type: DataTypes.JSONB,
    allowNull: true,
    comment: 'Array of image URLs'
  },
  tags: {
    type: DataTypes.ARRAY(DataTypes.STRING),
    allowNull: true,
    defaultValue: [],
    comment: 'Tags for the listing'
  },
  status: {
    type: DataTypes.STRING(20),
    allowNull: false,
    defaultValue: 'active',
    comment: 'Status of the listing: active, pending, sold, expired, etc.'
  },
  expiration_date: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: 'Date when the listing expires'
  },
  visibility: {
    type: DataTypes.STRING(20),
    allowNull: false,
    defaultValue: 'public',
    comment: 'Visibility of the listing: public, private, network'
  },
  contact_info: {
    type: DataTypes.JSONB,
    allowNull: true,
    comment: 'Contact information for the listing'
  },
  shipping_options: {
    type: DataTypes.JSONB,
    allowNull: true,
    comment: 'Shipping options for the listing'
  },
  payment_options: {
    type: DataTypes.JSONB,
    allowNull: true,
    comment: 'Payment options for the listing'
  },
  views: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: 'Number of views for the listing'
  },
  favorites: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: 'Number of users who favorited the listing'
  },
  is_featured: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false,
    comment: 'Whether the listing is featured'
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: true
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'marketplace_listings',
  
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

// Associations are defined in associations.js

export default MarketplaceListing;
