// This file is deprecated. Use FarmStorageUsage.js instead.
import { sequelize } from '../config/database.js';
import { defineModel } from '../utils/modelUtils.js';
import FarmStorageUsage from './FarmStorageUsage.js';
import dotenv from 'dotenv';

dotenv.config();

// Get the schema from environment variables
const schema = process.env.DB_SCHEMA || 'site';
console.log(`Defining TenantStorageUsage model (deprecated) with schema: ${schema}`);

// Create a minimal TenantStorageUsage model that just references FarmStorageUsage
// This is to avoid breaking existing code that imports TenantStorageUsage
const TenantStorageUsage = FarmStorageUsage;

console.log(`TenantStorageUsage model (deprecated) defined as an alias to FarmStorageUsage model`);

export default TenantStorageUsage;
