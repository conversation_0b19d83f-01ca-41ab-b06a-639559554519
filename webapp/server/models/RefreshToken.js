import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';
import { defineModel } from '../utils/modelUtils.js';
import User from './User.js';
import { v4 as uuidv4 } from 'uuid';

const RefreshToken = defineModel('RefreshToken', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  token: {
    type: DataTypes.STRING,
    allowNull: false,
    unique: true
  },
  user_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  expires_at: {
    type: DataTypes.DATE,
    allowNull: false
  },
  revoked: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  created_by_ip: {
    type: DataTypes.STRING,
    allowNull: true
  },
  revoked_by_ip: {
    type: DataTypes.STRING,
    allowNull: true
  },
  replaced_by_token: {
    type: DataTypes.STRING,
    allowNull: true
  }
}, {
  tableName: 'refresh_tokens',
  schema: process.env.DB_SCHEMA || 'site',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

// Note: Associations are defined in associations.js to avoid duplicates

// Helper methods
RefreshToken.createToken = async function(userId, ipAddress) {
  // Generate a unique token
  const token = uuidv4();

  // Set expiry time - 7 days
  const expiresAt = new Date();
  expiresAt.setDate(expiresAt.getDate() + 7);

  // Create and save the refresh token
  const refreshToken = await this.create({
    token,
    user_id: userId,
    expires_at: expiresAt,
    created_by_ip: ipAddress
  });

  return refreshToken;
};

// Check if token is active (not expired and not revoked)
RefreshToken.prototype.isActive = function() {
  return !this.revoked && new Date() < this.expires_at;
};

// Revoke token (used when token is rotated or user logs out)
RefreshToken.prototype.revoke = async function(ipAddress, replacedByToken = null) {
  this.revoked = true;
  this.revoked_by_ip = ipAddress;
  this.replaced_by_token = replacedByToken;
  return await this.save();
};

export default RefreshToken;
