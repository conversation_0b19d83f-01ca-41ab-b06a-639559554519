import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';
import { defineModel } from '../utils/modelUtils.js';
import Farm from './Farm.js';
import Crop from './Crop.js';
import Field from './Field.js';
import dotenv from 'dotenv';

dotenv.config();

const YieldPrediction = defineModel('YieldPrediction', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  farm_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: Farm,
      key: 'id'
    }
  },
  crop_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: Crop,
      key: 'id'
    }
  },
  field_id: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: Field,
      key: 'id'
    },
    comment: 'Field ID if the prediction is for a specific field'
  },
  predicted_yield: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    comment: 'Predicted yield amount'
  },
  yield_unit: {
    type: DataTypes.STRING(50),
    allowNull: false,
    comment: 'Unit of measurement for yield (e.g., bushels/acre, tons/acre)'
  },
  confidence_level: {
    type: DataTypes.DECIMAL(5, 4),
    allowNull: false,
    comment: 'Confidence level of the prediction (0-1)'
  },
  factors: {
    type: DataTypes.JSONB,
    allowNull: true,
    comment: 'Factors that influenced the prediction'
  },
  prediction_date: {
    type: DataTypes.DATEONLY,
    allowNull: false,
    defaultValue: DataTypes.NOW,
    comment: 'Date when the prediction was made'
  },
  harvest_year: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: 'Year for which the yield is predicted'
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'yield_predictions',
  
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

// Define associations
// Note: Associations are now defined in associations.js to avoid duplicates

export default YieldPrediction;