import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';
import { defineModel } from '../utils/modelUtils.js';
import Farm from './Farm.js';
import User from './User.js';
import Supplier from './Supplier.js';
import dotenv from 'dotenv';

dotenv.config();

const MarketContract = defineModel('MarketContract', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  farm_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: Farm,
      key: 'id'
    }
  },
  supplier_id: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: Supplier,
      key: 'id'
    }
  },
  created_by: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: User,
      key: 'id'
    }
  },
  title: {
    type: DataTypes.STRING(100),
    allowNull: false
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  contract_type: {
    type: DataTypes.STRING(50),
    allowNull: false,
    comment: 'Type of contract: sale, purchase, service, etc.'
  },
  status: {
    type: DataTypes.STRING(50),
    allowNull: false,
    defaultValue: 'draft',
    comment: 'Status of the contract: draft, pending, active, completed, cancelled, etc.'
  },
  start_date: {
    type: DataTypes.DATE,
    allowNull: true
  },
  end_date: {
    type: DataTypes.DATE,
    allowNull: true
  },
  value: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    comment: 'Total value of the contract'
  },
  currency: {
    type: DataTypes.STRING(3),
    allowNull: true,
    defaultValue: 'USD'
  },
  payment_terms: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  delivery_terms: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  products: {
    type: DataTypes.JSONB,
    allowNull: true,
    comment: 'Products or services covered by the contract'
  },
  attachments: {
    type: DataTypes.JSONB,
    allowNull: true,
    comment: 'Document attachments related to the contract'
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: true
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'market_contracts',
  
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

// Associations are defined in associations.js

export default MarketContract;
