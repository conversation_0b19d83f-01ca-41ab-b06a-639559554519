import { DataTypes } from 'sequelize';
import Farm from './Farm.js';
import { sequelize } from '../config/database.js';
import { defineModel } from '../utils/modelUtils.js';
import dotenv from 'dotenv';

dotenv.config();

const AlertRule = defineModel('AlertRule', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  name: {
    type: DataTypes.STRING,
    allowNull: false,
    comment: 'Name of the alert rule'
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'Description of the alert rule'
  },
  enabled: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: true,
    comment: 'Whether the rule is enabled'
  },
  conditions: {
    type: DataTypes.JSONB,
    allowNull: false,
    comment: 'JSON array of conditions that trigger the alert'
  },
  actions: {
    type: DataTypes.JSONB,
    allowNull: false,
    comment: 'JSON array of actions to take when the alert is triggered'
  },
  farm_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'farms',
      key: 'id'
    },
    comment: 'Farm this alert rule belongs to'
  },
  created_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'alert_rules',
  
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

// Associations are defined in associations.js

export default AlertRule;
