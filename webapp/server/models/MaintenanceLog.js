import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';
import { defineModel } from '../utils/modelUtils.js';
import Equipment from './Equipment.js';
import MaintenanceSchedule from './MaintenanceSchedule.js';
import dotenv from 'dotenv';

dotenv.config();

const MaintenanceLog = defineModel('MaintenanceLog', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  equipment_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: Equipment,
      key: 'id'
    }
  },
  maintenance_schedule_id: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: MaintenanceSchedule,
      key: 'id'
    }
  },
  service_date: {
    type: DataTypes.DATEONLY,
    allowNull: false
  },
  service_hours: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true
  },
  service_type: {
    type: DataTypes.STRING(100),
    allowNull: false
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: false
  },
  performed_by: {
    type: DataTypes.STRING(255),
    allowNull: true
  },
  parts_used: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  labor_hours: {
    type: DataTypes.DECIMAL(5, 2),
    allowNull: true
  },
  labor_cost: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true
  },
  parts_cost: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true
  },
  total_cost: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'maintenance_logs',
  
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

// Define associations
// Note: Associations are now defined in associations.js to avoid duplicates

export default MaintenanceLog;