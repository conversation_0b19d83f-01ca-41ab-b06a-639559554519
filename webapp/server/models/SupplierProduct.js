import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';
import { defineModel } from '../utils/modelUtils.js';
import Supplier from './Supplier.js';
import Product from './Product.js';
import dotenv from 'dotenv';

dotenv.config();

const SupplierProduct = defineModel('SupplierProduct', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  supplier_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: Supplier,
      key: 'id'
    }
  },
  product_id: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: Product,
      key: 'id'
    },
    comment: 'Optional reference to a product in the system'
  },
  name: {
    type: DataTypes.STRING(255),
    allowNull: false,
    comment: 'Name of the product offered by the supplier'
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  sku: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: 'Supplier\'s SKU for the product'
  },
  price: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: true
  },
  unit: {
    type: DataTypes.STRING(50),
    allowNull: true
  },
  category: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  type: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  availability: {
    type: DataTypes.JSONB,
    allowNull: true,
    comment: 'Availability information in JSON format'
  },
  lead_time: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'Lead time in days'
  },
  minimum_order_quantity: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: true
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: true
  },
  external_id: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: 'ID from external system'
  },
  external_data: {
    type: DataTypes.JSONB,
    allowNull: true,
    comment: 'Additional data from external system'
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'supplier_products',
  
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

// Associations are defined in associations.js

export default SupplierProduct;
