import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';
import { defineModel } from '../utils/modelUtils.js';
import SoilSample from './SoilSample.js';
import dotenv from 'dotenv';

dotenv.config();

const SoilRecommendation = defineModel('SoilRecommendation', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  soil_sample_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: SoilSample,
      key: 'id'
    }
  },
  field_id: {
    type: DataTypes.UUID,
    allowNull: true
  },
  nutrient_type: {
    type: DataTypes.STRING(50),
    allowNull: false
  },
  recommended_amount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false
  },
  unit: {
    type: DataTypes.STRING(50),
    allowNull: false
  },
  application_method: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  application_timing: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'soil_recommendations',
  
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

// Define associations
// Note: Associations are now defined in associations.js to avoid duplicates

export default SoilRecommendation;