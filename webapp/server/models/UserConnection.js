import db from '../db.js';
import { v4 as uuidv4 } from 'uuid';

/**
 * Model for user connections (farmer-to-farmer)
 */
class UserConnection {
  /**
   * Create a new connection request
   * @param {string} userId - User ID initiating the connection
   * @param {string} connectedUserId - User ID to connect with
   * @returns {Promise<Object>} Created connection
   */
  static async create(userId, connectedUserId) {
    // Check if users are the same
    if (userId === connectedUserId) {
      throw new Error('Cannot create a connection with yourself');
    }

    // Check if connection already exists
    const existingConnection = await this.getConnection(userId, connectedUserId);
    if (existingConnection) {
      return existingConnection;
    }

    const id = uuidv4();
    const query = `
      INSERT INTO user_connections (id, user_id, connected_user_id, status)
      VALUES ($1, $2, $3, 'pending')
      RETURNING *
    `;

    const result = await db.query(query, [id, userId, connectedUserId]);
    return result.rows[0];
  }

  /**
   * Get a connection between two users
   * @param {string} userId - First user ID
   * @param {string} connectedUserId - Second user ID
   * @returns {Promise<Object>} Connection
   */
  static async getConnection(userId, connectedUserId) {
    const query = `
      SELECT * FROM user_connections
      WHERE (user_id = $1 AND connected_user_id = $2)
      OR (user_id = $2 AND connected_user_id = $1)
    `;

    const result = await db.query(query, [userId, connectedUserId]);
    return result.rows[0];
  }

  /**
   * Get all connections for a user
   * @param {string} userId - User ID
   * @param {string} status - Filter by status (optional)
   * @returns {Promise<Array>} Connections
   */
  static async getByUserId(userId, status = null) {
    let query = `
      SELECT uc.*, 
             u1.first_name as user_first_name, 
             u1.last_name as user_last_name,
             u1.email as user_email,
             u2.first_name as connected_user_first_name, 
             u2.last_name as connected_user_last_name,
             u2.email as connected_user_email
      FROM user_connections uc
      JOIN users u1 ON uc.user_id = u1.id
      JOIN users u2 ON uc.connected_user_id = u2.id
      WHERE (uc.user_id = $1 OR uc.connected_user_id = $1)
    `;

    const params = [userId];

    if (status) {
      query += ` AND uc.status = $2`;
      params.push(status);
    }

    query += ` ORDER BY uc.updated_at DESC`;

    const result = await db.query(query, params);
    return result.rows;
  }

  /**
   * Get all pending connection requests for a user
   * @param {string} userId - User ID
   * @returns {Promise<Array>} Pending connection requests
   */
  static async getPendingRequests(userId) {
    const query = `
      SELECT uc.*, 
             u.first_name as requester_first_name, 
             u.last_name as requester_last_name,
             u.email as requester_email
      FROM user_connections uc
      JOIN users u ON uc.user_id = u.id
      WHERE uc.connected_user_id = $1
      AND uc.status = 'pending'
      ORDER BY uc.created_at DESC
    `;

    const result = await db.query(query, [userId]);
    return result.rows;
  }

  /**
   * Accept a connection request
   * @param {string} connectionId - Connection ID
   * @returns {Promise<Object>} Updated connection
   */
  static async accept(connectionId) {
    const query = `
      UPDATE user_connections
      SET status = 'accepted'
      WHERE id = $1
      RETURNING *
    `;

    const result = await db.query(query, [connectionId]);
    return result.rows[0];
  }

  /**
   * Reject a connection request
   * @param {string} connectionId - Connection ID
   * @returns {Promise<Object>} Updated connection
   */
  static async reject(connectionId) {
    const query = `
      UPDATE user_connections
      SET status = 'rejected'
      WHERE id = $1
      RETURNING *
    `;

    const result = await db.query(query, [connectionId]);
    return result.rows[0];
  }

  /**
   * Block a user connection
   * @param {string} connectionId - Connection ID
   * @returns {Promise<Object>} Updated connection
   */
  static async block(connectionId) {
    const query = `
      UPDATE user_connections
      SET status = 'blocked'
      WHERE id = $1
      RETURNING *
    `;

    const result = await db.query(query, [connectionId]);
    return result.rows[0];
  }

  /**
   * Unblock a user connection
   * @param {string} connectionId - Connection ID
   * @returns {Promise<Object>} Updated connection
   */
  static async unblock(connectionId) {
    const query = `
      UPDATE user_connections
      SET status = 'accepted'
      WHERE id = $1 AND status = 'blocked'
      RETURNING *
    `;

    const result = await db.query(query, [connectionId]);
    return result.rows[0];
  }

  /**
   * Delete a connection
   * @param {string} connectionId - Connection ID
   * @returns {Promise<boolean>} Success
   */
  static async delete(connectionId) {
    const query = `
      DELETE FROM user_connections
      WHERE id = $1
      RETURNING id
    `;

    const result = await db.query(query, [connectionId]);
    return result.rowCount > 0;
  }

  /**
   * Check if two users are connected
   * @param {string} userId - First user ID
   * @param {string} connectedUserId - Second user ID
   * @returns {Promise<boolean>} Are connected
   */
  static async areConnected(userId, connectedUserId) {
    const query = `
      SELECT * FROM user_connections
      WHERE ((user_id = $1 AND connected_user_id = $2) OR (user_id = $2 AND connected_user_id = $1))
      AND status = 'accepted'
    `;

    const result = await db.query(query, [userId, connectedUserId]);
    return result.rowCount > 0;
  }

  /**
   * Check if a user is blocked by another user
   * @param {string} userId - User ID to check
   * @param {string} blockedByUserId - User ID who might have blocked
   * @returns {Promise<boolean>} Is blocked
   */
  static async isBlocked(userId, blockedByUserId) {
    const query = `
      SELECT * FROM user_connections
      WHERE user_id = $1 AND connected_user_id = $2 AND status = 'blocked'
    `;

    const result = await db.query(query, [blockedByUserId, userId]);
    return result.rowCount > 0;
  }

  /**
   * Get all users that can be connected with
   * @param {string} userId - User ID
   * @returns {Promise<Array>} Connectable users
   */
  static async getConnectableUsers(userId) {
    // Get users who are farmers and not already connected or blocked
    const query = `
      SELECT u.id, u.first_name, u.last_name, u.email
      FROM users u
      WHERE u.id != $1
      AND u.user_type = 'farmer'
      AND NOT EXISTS (
        SELECT 1 FROM user_connections uc
        WHERE ((uc.user_id = $1 AND uc.connected_user_id = u.id) 
              OR (uc.user_id = u.id AND uc.connected_user_id = $1))
        AND uc.status IN ('accepted', 'blocked')
      )
      ORDER BY u.first_name, u.last_name
    `;

    const result = await db.query(query, [userId]);
    return result.rows;
  }

  /**
   * Get connection statistics for a user
   * @param {string} userId - User ID
   * @returns {Promise<Object>} Connection statistics
   */
  static async getStats(userId) {
    const query = `
      SELECT
        COUNT(CASE WHEN status = 'accepted' THEN 1 END) as accepted_count,
        COUNT(CASE WHEN status = 'pending' AND connected_user_id = $1 THEN 1 END) as pending_received_count,
        COUNT(CASE WHEN status = 'pending' AND user_id = $1 THEN 1 END) as pending_sent_count,
        COUNT(CASE WHEN status = 'blocked' AND user_id = $1 THEN 1 END) as blocked_count
      FROM user_connections
      WHERE user_id = $1 OR connected_user_id = $1
    `;

    const result = await db.query(query, [userId]);
    return result.rows[0];
  }
}

export default UserConnection;
