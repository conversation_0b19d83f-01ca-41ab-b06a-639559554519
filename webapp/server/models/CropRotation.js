import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';
import { defineModel } from '../utils/modelUtils.js';
import Farm from './Farm.js';
import Field from './Field.js';
import dotenv from 'dotenv';

dotenv.config();

const CropRotation = defineModel('CropRotation', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  farm_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: Farm,
      key: 'id'
    }
  },
  field_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: Field,
      key: 'id'
    }
  },
  current_crop: {
    type: DataTypes.STRING(100),
    allowNull: false,
    comment: 'Current crop in the field'
  },
  recommended_sequence: {
    type: DataTypes.JSONB,
    allowNull: false,
    comment: 'Recommended crop rotation sequence'
  },
  benefits: {
    type: DataTypes.JSONB,
    allowNull: true,
    comment: 'Benefits of the recommended rotation'
  },
  rotation_years: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'Number of years in the rotation cycle'
  },
  soil_health_impact: {
    type: DataTypes.STRING(50),
    allowNull: true,
    comment: 'Impact on soil health (positive, neutral, negative)'
  },
  confidence_score: {
    type: DataTypes.FLOAT,
    allowNull: true,
    comment: 'AI confidence score for the rotation plan (0-100)'
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'crop_rotations',

  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

// Define associations
// Note: Associations are now defined in associations.js to avoid duplicates

export default CropRotation;
