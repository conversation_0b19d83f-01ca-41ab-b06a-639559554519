import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';
import { defineModel } from '../utils/modelUtils.js';

const MigrationSystem = defineModel('MigrationSystem', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  name: {
    type: DataTypes.STRING,
    allowNull: false
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  logo_url: {
    type: DataTypes.STRING,
    allowNull: true
  },
  website: {
    type: DataTypes.STRING,
    allowNull: true
  },
  supported_formats: {
    type: DataTypes.ARRAY(DataTypes.STRING),
    defaultValue: ['csv', 'excel', 'json']
  },
  supported_entities: {
    type: DataTypes.ARRAY(DataTypes.STRING),
    defaultValue: []
  }
}, {
  tableName: 'migration_systems',
  timestamps: true,
  underscored: true
});

// Associations are defined in associations.js

export default MigrationSystem;
