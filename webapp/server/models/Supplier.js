import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';
import { defineModel } from '../utils/modelUtils.js';
import Farm from './Farm.js';
import User from './User.js';
import dotenv from 'dotenv';

dotenv.config();

const Supplier = defineModel('Supplier', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  farm_id: {
    type: DataTypes.UUID,
    allowNull: true, // Changed to allow null for suppliers not associated with a farm
    references: {
      model: Farm,
      key: 'id'
    }
  },
  user_id: {
    type: DataTypes.UUID,
    allowNull: true, // Allow null for backward compatibility
    references: {
      model: User,
      key: 'id'
    },
    comment: 'User account associated with this supplier'
  },
  name: {
    type: DataTypes.STRING(100),
    allowNull: false
  },
  contact_name: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  email: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  phone: {
    type: DataTypes.STRING(20),
    allowNull: true
  },
  address: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  website: {
    type: DataTypes.STRING(255),
    allowNull: true
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  payment_terms: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: true
  },
  latitude: {
    type: DataTypes.DECIMAL(10, 6),
    allowNull: true,
    comment: 'Latitude for location-aware supplier search'
  },
  longitude: {
    type: DataTypes.DECIMAL(10, 6),
    allowNull: true,
    comment: 'Longitude for location-aware supplier search'
  },
  product_types: {
    type: DataTypes.ARRAY(DataTypes.STRING),
    allowNull: true,
    defaultValue: [],
    comment: 'Types of products offered by this supplier'
  },
  availability: {
    type: DataTypes.JSONB,
    allowNull: true,
    comment: 'Availability information in JSON format'
  },
  is_preferred: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false,
    comment: 'Whether this supplier is preferred by the farmer'
  },
  api_integration: {
    type: DataTypes.JSONB,
    allowNull: true,
    comment: 'External API integration information in JSON format'
  },
  api_key: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: 'API key for external integrations'
  },
  api_endpoint: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: 'API endpoint for external integrations'
  },
  business_hours: {
    type: DataTypes.JSONB,
    allowNull: true,
    comment: 'Business hours in JSON format'
  },
  rating: {
    type: DataTypes.DECIMAL(3, 2),
    allowNull: true,
    comment: 'Average rating from reviews'
  },
  review_count: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: 'Number of reviews'
  },
  is_global: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    allowNull: false,
    comment: 'If true, this is a global system level supplier; if false, it is a per-farm supplier'
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'suppliers',
  
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

// Associations are defined in associations.js

export default Supplier;
