import { sequelize } from '../config/database.js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function runRefreshTokensMigration() {
  try {
    console.log('Running Refresh Tokens migration...');

    // Check if users table exists
    let usersTableExists = false;
    const schema = process.env.DB_SCHEMA || 'site';

    try {
      await sequelize.query(`SELECT 1 FROM ${schema}.users LIMIT 1`);
      console.log('Users table exists, proceeding with refresh tokens table creation.');
      usersTableExists = true;
    } catch (error) {
      console.log('Users table does not exist, need to run schema.sql first.');
    }

    // If users table doesn't exist, exit
    if (!usersTableExists) {
      console.error('Please run schema.sql first to create the users table.');
      process.exit(1);
    }

    // Check if uuid-ossp extension is available
    try {
      await sequelize.query(`CREATE EXTENSION IF NOT EXISTS "uuid-ossp";`);
      console.log('uuid-ossp extension is available or has been created.');
    } catch (error) {
      console.warn('Could not create uuid-ossp extension. This might be due to insufficient privileges.');
      console.warn('The migration will continue, but uuid generation might not work properly.');
    }

    // Now run add_refresh_tokens_table.sql
    console.log('Creating refresh tokens table...');
    const migrationPath = path.join(__dirname, '../db/migrations/add_refresh_tokens_table.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');

    // Split the SQL into individual statements
    const statements = migrationSQL
      .split(';')
      .filter(statement => statement.trim() !== '')
      .map(statement => statement.trim() + ';');

    console.log(`Found ${statements.length} SQL statements to execute in add_refresh_tokens_table.sql.`);

    // Set the search_path to use the specified schema before executing migration
    await sequelize.query(`SET search_path TO ${schema};`);
    console.log(`Set search_path to schema: ${schema} for refresh tokens table`);

    // Execute each statement
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      try {
        await sequelize.query(statement);
        console.log(`Executed statement ${i + 1}/${statements.length} from add_refresh_tokens_table.sql`);
      } catch (error) {
        console.error(`Error executing statement ${i + 1}/${statements.length} from add_refresh_tokens_table.sql:`, error.message);
        console.error('Statement:', statement);
        throw error;
      }
    }

    console.log('Refresh tokens table created successfully!');
  } catch (error) {
    console.error('Error running refresh tokens migration:', error);
  } finally {
    // Close the database connection
    await sequelize.close();
  }
}

runRefreshTokensMigration();