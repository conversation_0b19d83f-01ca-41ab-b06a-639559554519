import { sequelize } from '../config/database.js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function runAIRecommendationTablesMigration() {
  try {
    console.log('Running migration to add AI recommendation tables...');
    
    // Read the SQL file
    const sqlPath = path.join(__dirname, '../db/migrations/add_ai_recommendation_tables.sql');
    const sql = fs.readFileSync(sqlPath, 'utf8');
    
    // Execute the SQL
    await sequelize.query(sql);
    
    console.log('Migration completed successfully!');
    console.log('The following tables have been created:');
    console.log('- ai_harvest_recommendations');
    console.log('- ai_field_improvement_recommendations');
    console.log('- ai_financial_recommendations');
    console.log('- ai_yield_profit_recommendations');
    
    console.log('\nYou can now use the AI-powered recommendation features in the NxtAcre platform.');
  } catch (error) {
    console.error('Error running migration:', error);
    console.error('Please check if the tables already exist or if there are any syntax errors in the SQL file.');
  } finally {
    // Close the database connection
    await sequelize.close();
  }
}

runAIRecommendationTablesMigration();