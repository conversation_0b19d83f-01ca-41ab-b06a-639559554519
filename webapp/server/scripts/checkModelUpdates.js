#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the directory name
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Path to models directory
const modelsDir = path.join(__dirname, '..', 'models');

// Get all .js files in the models directory
const modelFiles = fs.readdirSync(modelsDir)
  .filter(file => file.endsWith('.js') && file !== 'index.js');

console.log(`Checking ${modelFiles.length} model files for issues...`);

// Track issues
let issuesFound = 0;

for (const file of modelFiles) {
  const filePath = path.join(modelsDir, file);
  
  try {
    // Read the file content
    const content = fs.readFileSync(filePath, 'utf8');
    
    // Check for issues
    const issues = [];
    
    // Check if the file uses sequelize.define
    if (content.includes('sequelize.define')) {
      issues.push('Still using sequelize.define');
    }
    
    // Check if the file has defineModel but is missing the import
    if (content.includes('defineModel') && !content.includes('import { defineModel }') && !content.includes('import {defineModel}')) {
      issues.push('Using defineModel but missing import');
    }
    
    // Check if the file has schema in options
    if (content.includes('schema: schema') || content.includes('schema,')) {
      issues.push('Still has schema in options');
    }
    
    // Check if the file has schema definition
    if (content.includes('const schema = process.env.DB_SCHEMA')) {
      issues.push('Still has schema definition');
    }
    
    // Check if the file has console.log for schema
    if (content.includes('console.log(`') && content.includes('schema')) {
      issues.push('Still has console.log for schema');
    }
    
    // Report issues
    if (issues.length > 0) {
      console.log(`\nIssues in ${file}:`);
      issues.forEach(issue => console.log(`  - ${issue}`));
      issuesFound++;
    }
    
  } catch (error) {
    console.error(`  Error checking ${file}:`, error.message);
    issuesFound++;
  }
}

if (issuesFound === 0) {
  console.log('\nNo issues found! All model files have been properly updated.');
} else {
  console.log(`\nFound issues in ${issuesFound} model files. Please fix them manually.`);
}