import { sequelize } from '../config/database.js';
import { DataTypes } from 'sequelize';

async function testRoleMigration() {
  try {
    console.log('Testing role migration...');
    
    // Check if the roles table exists
    const rolesTableExists = await sequelize.getQueryInterface().showAllTables()
      .then(tables => tables.includes('roles'));
    
    console.log(`Roles table exists: ${rolesTableExists}`);
    
    if (!rolesTableExists) {
      console.error('Roles table does not exist. Migration may not have been run.');
      return;
    }
    
    // Check if the role_id column exists in the user_farms table
    const userFarmsColumns = await sequelize.getQueryInterface().describeTable('user_farms');
    const roleIdColumnExists = 'role_id' in userFarmsColumns;
    
    console.log(`Role_id column exists in user_farms table: ${roleIdColumnExists}`);
    
    if (!roleIdColumnExists) {
      console.error('role_id column does not exist in user_farms table. Migration may not have been run.');
      return;
    }
    
    // Check if the role_id column is NOT NULL
    const roleIdColumn = userFarmsColumns.role_id;
    console.log(`Role_id column is NOT NULL: ${!roleIdColumn.allowNull}`);
    
    if (roleIdColumn.allowNull) {
      console.error('role_id column is nullable. Migration may not have been fully applied.');
      return;
    }
    
    // Check if there are any user_farms records with NULL role_id
    const [results] = await sequelize.query('SELECT COUNT(*) FROM user_farms WHERE role_id IS NULL');
    const nullRoleIdCount = parseInt(results[0].count);
    
    console.log(`Number of user_farms records with NULL role_id: ${nullRoleIdCount}`);
    
    if (nullRoleIdCount > 0) {
      console.error('There are user_farms records with NULL role_id. Migration may not have been fully applied.');
      return;
    }
    
    console.log('Migration was successful! All checks passed.');
  } catch (error) {
    console.error('Error testing migration:', error);
  } finally {
    // Close the database connection
    await sequelize.close();
  }
}

testRoleMigration();