# Help Tips Scripts

This directory contains scripts for managing help tips in the NxtAcre application.

## addHelpTips.js

This script adds a comprehensive set of help tips to the database to improve the onboarding experience for new users. The tips cover:

- Registration process
- Dashboard navigation and customization
- Farm management
- Equipment management
- Inventory management
- Field management
- Task management

### Prerequisites

Before running the script, make sure you have:

1. Node.js installed
2. Access to the NxtAcre API
3. Admin credentials for the application

### Environment Variables

Create a `.env` file in the server directory with the following variables:

```
API_URL=http://localhost:3000/api
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=your-admin-password
```

### Running the Script

To run the script, navigate to the server directory and execute:

```bash
node scripts/addHelpTips.js
```

### Customizing Help Tips

If you want to add or modify help tips, you can edit the arrays in the `addHelpTips.js` file:

- `registrationTips`: Tips for the registration page
- `dashboardTips`: Tips for the dashboard
- `farmsTips`: Tips for farm management
- `equipmentTips`: Tips for equipment management
- `inventoryTips`: Tips for inventory management
- `fieldsTips`: Tips for field management
- `tasksTips`: Tips for task management

Each tip requires the following properties:

- `title`: The title of the tip
- `content`: The content/body of the tip
- `pagePath`: The path of the page where the tip should be shown
- `elementSelector`: CSS selector for the element the tip should point to
- `position`: Where the tip should be positioned ('top', 'right', 'bottom', 'left')
- `order`: The order in which tips should be displayed
- `isActive`: Whether the tip is active or not

### Example

```javascript
{
  title: 'Welcome to Your Dashboard',
  content: 'This is your personalized dashboard where you can see an overview of your farm operations.',
  pagePath: '/dashboard',
  elementSelector: '.dashboard-page',
  position: 'top',
  order: 1,
  isActive: true
}
```

## testHelpTips.js

This script tests the help tips functionality by automating a browser session that navigates through different pages and verifies that help tips are displayed correctly.

### Prerequisites

Before running the script, make sure you have:

1. Node.js installed
2. Puppeteer installed (`npm install puppeteer`)
3. A test user account in the application

### Environment Variables

Add the following variables to your `.env` file:

```
BASE_URL=http://localhost:5173
TEST_EMAIL=<EMAIL>
TEST_PASSWORD=your-test-password
```

### Running the Script

To run the test script, navigate to the server directory and execute:

```bash
node scripts/testHelpTips.js
```

To reset dismissed help tips before testing:

```bash
node scripts/testHelpTips.js --reset-dismissed
```

### What the Test Does

1. Logs in to the application using the test credentials
2. Navigates to the dashboard and checks for help tips
3. Navigates to the farms page and checks for help tips
4. Tests dismissing a help tip
5. Navigates to the equipment and inventory pages to check for help tips

The test will output the results to the console, indicating whether help tips were found on each page and if dismissal works correctly.

## run_ai_assistant_tables.js

This script creates the AI assistant tables in the database, ensuring that the necessary prerequisite tables (like "users" and "farms") exist first.

### Prerequisites

Before running the script, make sure you have:

1. Node.js installed
2. Access to the PostgreSQL database
3. Proper database credentials in your environment variables

### Environment Variables

Make sure your `.env` file has the database connection details:

```
DB_HOST=localhost
DB_PORT=5432
DB_NAME=your_database_name
DB_USER=your_database_user
DB_PASSWORD=your_database_password
DB_SCHEMA=public
```

### Running the Script

To create the AI assistant tables, navigate to the server directory and execute:

```bash
node scripts/run_ai_assistant_tables.js
```

### What the Script Does

1. Checks if the "users" table exists in the database
2. If the "users" table doesn't exist, it runs schema.sql to create all the necessary tables
3. Then it runs ai_assistant_tables.sql to create the AI assistant tables
4. Handles errors and provides detailed logging

This script resolves the "relation 'users' does not exist" error that occurs when trying to run ai_assistant_tables.sql directly, by ensuring that the schema.sql file is executed first to create the "users" table.
