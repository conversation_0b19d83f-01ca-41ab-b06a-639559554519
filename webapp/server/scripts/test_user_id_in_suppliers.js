import { sequelize } from '../config/database.js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

async function testUserIdInSuppliers() {
  try {
    console.log('Testing if user_id column exists in suppliers table...');
    
    // Get the schema from environment variables or use default
    const schema = process.env.DB_SCHEMA || 'site';
    console.log(`Using schema: ${schema}`);
    
    // Query to check if the column exists
    const query = `
      SELECT column_name, data_type, is_nullable
      FROM information_schema.columns
      WHERE table_schema = '${schema}'
        AND table_name = 'suppliers'
        AND column_name = 'user_id';
    `;
    
    const [results] = await sequelize.query(query);
    
    if (results.length === 0) {
      console.error('user_id column does not exist in suppliers table!');
      process.exit(1);
    }
    
    console.log('user_id column exists in suppliers table:');
    console.table(results);
    
    // Check if the column has the correct data type and constraints
    const column = results[0];
    if (column.data_type !== 'uuid') {
      console.error(`user_id column has incorrect data type: ${column.data_type}, expected: uuid`);
      process.exit(1);
    }
    
    console.log('Migration was successful!');
    
  } catch (error) {
    console.error('Error testing migration:', error);
    process.exit(1);
  }
}

// Run the function
testUserIdInSuppliers()
  .then(() => {
    console.log('Test completed, exiting...');
    process.exit(0);
  })
  .catch(err => {
    console.error('Unhandled error:', err);
    process.exit(1);
  });