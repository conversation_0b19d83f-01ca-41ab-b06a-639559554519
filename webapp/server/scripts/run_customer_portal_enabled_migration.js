import { sequelize } from '../config/database.js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function runCustomerPortalEnabledMigration() {
  try {
    console.log('Running migration to add customer_portal_enabled feature to subscription plans...');
    
    // Read the SQL file
    const sqlPath = path.join(__dirname, '../db/migrations/add_customer_portal_enabled_to_subscription_plans.sql');
    const sql = fs.readFileSync(sqlPath, 'utf8');
    
    // Execute the SQL
    await sequelize.query(sql);
    
    console.log('Migration completed successfully! customer_portal_enabled feature added to subscription plans.');
  } catch (error) {
    console.error('Error running migration:', error);
  } finally {
    // Close the database connection
    await sequelize.close();
  }
}

runCustomerPortalEnabledMigration();