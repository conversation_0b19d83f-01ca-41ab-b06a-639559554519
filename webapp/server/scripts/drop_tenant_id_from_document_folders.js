import { sequelize } from '../config/database.js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function dropTenantIdFromDocumentFolders() {
  try {
    console.log('Starting migration: Dropping tenant_id from document_folders table...');
    
    // Define the migration file
    const migrationFile = 'drop_tenant_id_from_document_folders.sql';
    
    // Read the SQL file
    const sqlFilePath = path.join(__dirname, '../db', migrationFile);
    const sql = fs.readFileSync(sqlFilePath, 'utf8');
    
    // Execute the SQL
    await sequelize.query(sql);
    
    console.log(`Migration ${migrationFile} completed successfully`);
    console.log('tenant_id column has been dropped from document_folders table');
  } catch (error) {
    console.error('Error running migration:', error);
    process.exit(1);
  }
}

// Run the function
dropTenantIdFromDocumentFolders()
  .then(() => {
    console.log('Migration completed, exiting...');
    process.exit(0);
  })
  .catch(err => {
    console.error('Unhandled error:', err);
    process.exit(1);
  });