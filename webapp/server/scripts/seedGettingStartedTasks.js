import { sequelize } from '../config/database.js';
import GettingStartedTask from '../models/GettingStartedTask.js';

/**
 * Seed getting started tasks into the database
 * This script will insert tasks if they don't already exist
 */
const seedGettingStartedTasks = async () => {
  try {
    console.log('Starting to seed getting started tasks...');

    // Check if there are any existing tasks
    const existingTasksCount = await GettingStartedTask.count();
    console.log(`Found ${existingTasksCount} existing tasks`);

    // Only seed if there are no existing tasks
    if (existingTasksCount === 0) {
      console.log('No existing tasks found. Creating new tasks...');

      // Define the tasks
      const tasks = [
        {
          title: 'Complete Your Profile',
          description: 'Update your profile information to personalize your experience and ensure you receive relevant notifications.',
          link_path: '/profile',
          icon: 'user',
          order: 1,
          is_active: true,
          user_type: 'all'
        },
        {
          title: 'Add Your Farm',
          description: 'Set up your farm details including location, size, and type to get started with farm management.',
          link_path: '/farms/new',
          icon: 'farm',
          order: 2,
          is_active: true,
          user_type: 'farmer'
        },
        {
          title: 'Add Your First Field',
          description: 'Add a field to start tracking your crops, activities, and field-specific data. Use the interactive map to define field boundaries.',
          link_path: '/fields/new',
          icon: 'field',
          order: 3,
          is_active: true,
          user_type: 'farmer'
        },
        {
          title: 'Add Equipment',
          description: 'Add your equipment to track maintenance schedules, usage, and costs. This helps optimize equipment utilization and plan for maintenance.',
          link_path: '/equipment/new',
          icon: 'tractor',
          order: 4,
          is_active: true,
          user_type: 'farmer'
        },
        {
          title: 'Connect Your Bank Account',
          description: 'Connect your bank account to automatically import transactions and streamline your financial management.',
          link_path: '/link-account',
          icon: 'bank',
          order: 5,
          is_active: true,
          user_type: 'all'
        },
        {
          title: 'Explore the Dashboard',
          description: 'Familiarize yourself with the dashboard to see key information at a glance. Customize your dashboard widgets to show the information most important to you.',
          link_path: '/dashboard',
          icon: 'dashboard',
          order: 6,
          is_active: true,
          user_type: 'all'
        },
        {
          title: 'Check the Weather',
          description: 'View the weather forecast to plan your farm activities. Access historical weather data and set up weather alerts for your location.',
          link_path: '/weather',
          icon: 'cloud',
          order: 7,
          is_active: true,
          user_type: 'farmer'
        },
        {
          title: 'Set Up Inventory',
          description: 'Add your inventory items to track stock levels, set reorder points, and manage your supplies efficiently.',
          link_path: '/inventory/new',
          icon: 'inventory',
          order: 8,
          is_active: true,
          user_type: 'farmer'
        },
        {
          title: 'Create Your First Task',
          description: 'Create a task to track work that needs to be done. Assign tasks to team members and set due dates to stay organized.',
          link_path: '/tasks/new',
          icon: 'task',
          order: 9,
          is_active: true,
          user_type: 'all'
        },
        {
          title: 'Explore Financial Tools',
          description: 'Discover the financial management tools including expense tracking, budgeting, and financial reporting to keep your farm finances in order.',
          link_path: '/finances',
          icon: 'money',
          order: 10,
          is_active: true,
          user_type: 'all'
        },
        {
          title: 'Set Up Crop Planning',
          description: 'Plan your crops for the season, including planting and harvesting schedules, to optimize your farm operations.',
          link_path: '/crops/planning',
          icon: 'seedling',
          order: 11,
          is_active: true,
          user_type: 'farmer'
        },
        {
          title: 'Explore Transport Management',
          description: 'Manage your transport operations including drivers, deliveries, pickups, and scheduling to streamline logistics.',
          link_path: '/transport',
          icon: 'truck',
          order: 12,
          is_active: true,
          user_type: 'farmer'
        },
        {
          title: 'Set Up Receipt Management',
          description: 'Start uploading and organizing receipts to track expenses and simplify tax preparation. Use OCR to automatically extract receipt data.',
          link_path: '/receipts',
          icon: 'receipt',
          order: 13,
          is_active: true,
          user_type: 'all'
        },
        {
          title: 'Explore Help Resources',
          description: 'Check out the help center to find guides, tutorials, and FAQs to help you make the most of the platform.',
          link_path: '/help',
          icon: 'question-circle',
          order: 14,
          is_active: true,
          user_type: 'all'
        },
        {
          title: 'Invite Team Members',
          description: 'Invite your team members to collaborate on farm management tasks and improve coordination.',
          link_path: '/team/invite',
          icon: 'users',
          order: 15,
          is_active: true,
          user_type: 'all'
        }
      ];

      // Insert the tasks
      await GettingStartedTask.bulkCreate(tasks);
      console.log(`Successfully created ${tasks.length} getting started tasks`);
    } else {
      console.log('Tasks already exist in the database. Skipping seed operation.');
    }

    console.log('Getting started tasks seeding completed successfully');
  } catch (error) {
    console.error('Error seeding getting started tasks:', error);
  } finally {
    // Close the database connection
    await sequelize.close();
  }
};

// Run the seed function
seedGettingStartedTasks()
  .then(() => {
    console.log('Seed script completed');
    process.exit(0);
  })
  .catch(error => {
    console.error('Seed script failed:', error);
    process.exit(1);
  });