import { sequelize } from '../config/database.js';
import Role from '../models/Role.js';
import RolePermission from '../models/RolePermission.js';
import { Op } from 'sequelize';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

async function updateRolesAndPermissions() {
  const transaction = await sequelize.transaction();

  try {
    console.log('Starting update of roles and permissions...');

    // 1. Update role names to be more user-friendly
    console.log('Updating role names to be more user-friendly...');

    // Define the mapping of old role names to new user-friendly names
    const roleNameMapping = {
      'farm_owner': 'Farm Owner',
      'farm_admin': 'Farm Administrator',
      'farm_manager': 'Farm Manager',
      'farm_employee': 'Farm Worker',
      'accountant': 'Financial Manager'
    };

    // Update the roles in the database
    for (const [oldName, newName] of Object.entries(roleNameMapping)) {
      // Find the role with the old name
      const role = await Role.findOne({
        where: {
          name: oldName
        },
        transaction
      });

      // If the role exists, update its name
      if (role) {
        console.log(`Updating role name from "${oldName}" to "${newName}"`);
        role.name = newName;
        await role.save({ transaction });
      } else {
        console.log(`Role "${oldName}" not found, creating it with name "${newName}"`);
        // Create the role if it doesn't exist
        await Role.create({
          name: newName,
          description: `${newName} role with appropriate permissions`,
          is_system_role: true
        }, { transaction });
      }
    }

    // 2. Skip removing legacy role entries since role_id column doesn't exist
    console.log('Skipping legacy role entries update since role_id column does not exist in the database.');

    // Instead, we'll update permissions based on role_name directly

    // 3. Update default permissions for global roles
    console.log('Updating default permissions for global roles...');

    // Define the default permissions for each role and feature
    const defaultPermissions = {
      'Farm Owner': {
        'dashboard': { view: true, create: true, edit: true, delete: true },
        'farms': { view: true, create: true, edit: true, delete: true },
        'fields': { view: true, create: true, edit: true, delete: true },
        'crops': { view: true, create: true, edit: true, delete: true },
        'livestock': { view: true, create: true, edit: true, delete: true },
        'equipment': { view: true, create: true, edit: true, delete: true },
        'inventory': { view: true, create: true, edit: true, delete: true },
        'employees': { view: true, create: true, edit: true, delete: true },
        'finances': { view: true, create: true, edit: true, delete: true },
        'reports': { view: true, create: true, edit: true, delete: true },
        'settings': { view: true, create: true, edit: true, delete: true },
        'roles': { view: true, create: true, edit: true, delete: true },
        'transport': { view: true, create: true, edit: true, delete: true },
        'receipts': { view: true, create: true, edit: true, delete: true }
      },
      'Farm Administrator': {
        'dashboard': { view: true, create: true, edit: true, delete: false },
        'farms': { view: true, create: false, edit: true, delete: false },
        'fields': { view: true, create: true, edit: true, delete: true },
        'crops': { view: true, create: true, edit: true, delete: true },
        'livestock': { view: true, create: true, edit: true, delete: true },
        'equipment': { view: true, create: true, edit: true, delete: true },
        'inventory': { view: true, create: true, edit: true, delete: true },
        'employees': { view: true, create: true, edit: true, delete: true },
        'finances': { view: true, create: true, edit: true, delete: true },
        'reports': { view: true, create: true, edit: true, delete: true },
        'settings': { view: true, create: false, edit: false, delete: false },
        'roles': { view: true, create: false, edit: false, delete: false },
        'transport': { view: true, create: true, edit: true, delete: true },
        'receipts': { view: true, create: true, edit: true, delete: true }
      },
      'Farm Manager': {
        'dashboard': { view: true, create: true, edit: true, delete: false },
        'farms': { view: true, create: false, edit: false, delete: false },
        'fields': { view: true, create: true, edit: true, delete: false },
        'crops': { view: true, create: true, edit: true, delete: false },
        'livestock': { view: true, create: true, edit: true, delete: false },
        'equipment': { view: true, create: true, edit: true, delete: false },
        'inventory': { view: true, create: true, edit: true, delete: false },
        'employees': { view: true, create: false, edit: true, delete: false },
        'finances': { view: true, create: true, edit: true, delete: false },
        'reports': { view: true, create: true, edit: false, delete: false },
        'settings': { view: false, create: false, edit: false, delete: false },
        'roles': { view: false, create: false, edit: false, delete: false },
        'transport': { view: true, create: true, edit: true, delete: false },
        'receipts': { view: true, create: true, edit: true, delete: false }
      },
      'Farm Worker': {
        'dashboard': { view: true, create: false, edit: false, delete: false },
        'farms': { view: true, create: false, edit: false, delete: false },
        'fields': { view: true, create: false, edit: false, delete: false },
        'crops': { view: true, create: false, edit: false, delete: false },
        'livestock': { view: true, create: false, edit: false, delete: false },
        'equipment': { view: true, create: false, edit: false, delete: false },
        'inventory': { view: true, create: false, edit: false, delete: false },
        'employees': { view: false, create: false, edit: false, delete: false },
        'finances': { view: false, create: false, edit: false, delete: false },
        'reports': { view: false, create: false, edit: false, delete: false },
        'settings': { view: false, create: false, edit: false, delete: false },
        'roles': { view: false, create: false, edit: false, delete: false },
        'transport': { view: true, create: false, edit: false, delete: false },
        'receipts': { view: true, create: true, edit: false, delete: false }
      },
      'Financial Manager': {
        'dashboard': { view: true, create: false, edit: false, delete: false },
        'farms': { view: true, create: false, edit: false, delete: false },
        'fields': { view: false, create: false, edit: false, delete: false },
        'crops': { view: false, create: false, edit: false, delete: false },
        'livestock': { view: false, create: false, edit: false, delete: false },
        'equipment': { view: false, create: false, edit: false, delete: false },
        'inventory': { view: true, create: false, edit: false, delete: false },
        'employees': { view: true, create: false, edit: false, delete: false },
        'finances': { view: true, create: true, edit: true, delete: false },
        'reports': { view: true, create: true, edit: false, delete: false },
        'settings': { view: false, create: false, edit: false, delete: false },
        'roles': { view: false, create: false, edit: false, delete: false },
        'transport': { view: true, create: false, edit: false, delete: false },
        'receipts': { view: true, create: false, edit: false, delete: false }
      }
    };

    // For each role, update or create the permissions for each feature
    for (const [roleName, features] of Object.entries(defaultPermissions)) {
      // Find the role
      const role = await Role.findOne({
        where: {
          name: roleName
        },
        transaction
      });

      if (!role) {
        console.log(`Role "${roleName}" not found for permission update`);
        continue;
      }

      // For each feature, update or create the permission
      for (const [featureName, permissions] of Object.entries(features)) {
        // Find existing permission
        let permission = await RolePermission.findOne({
          where: {
            role_name: roleName.toLowerCase().replace(' ', '_'), // Convert to legacy format
            farm_id: null,
            feature: featureName
          },
          transaction
        });

        if (permission) {
          // Update existing permission
          console.log(`Updating permission for role "${roleName}", feature "${featureName}"`);
          permission.can_view = permissions.view;
          permission.can_create = permissions.create;
          permission.can_edit = permissions.edit;
          permission.can_delete = permissions.delete;
          await permission.save({ transaction });
        } else {
          // Create new permission
          console.log(`Creating permission for role "${roleName}", feature "${featureName}"`);
          await RolePermission.create({
            role_name: roleName.toLowerCase().replace(' ', '_'), // Convert to legacy format
            farm_id: null,
            feature: featureName,
            can_view: permissions.view,
            can_create: permissions.create,
            can_edit: permissions.edit,
            can_delete: permissions.delete
          }, { transaction });
        }
      }
    }

    // Commit the transaction
    await transaction.commit();
    console.log('Successfully updated roles and permissions');

  } catch (error) {
    // Rollback the transaction in case of error
    await transaction.rollback();
    console.error('Error updating roles and permissions:', error);
    process.exit(1);
  }
}

// Run the function
updateRolesAndPermissions()
  .then(() => {
    console.log('Update completed, exiting...');
    process.exit(0);
  })
  .catch(err => {
    console.error('Unhandled error:', err);
    process.exit(1);
  });
