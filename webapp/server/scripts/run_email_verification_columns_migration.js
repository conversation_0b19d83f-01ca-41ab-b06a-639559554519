import { sequelize } from '../config/database.js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function runEmailVerificationColumnsMigration() {
  try {
    console.log('Adding email_verification_token and email_verification_expires columns to Users table...');
    
    // Read the SQL file
    const sqlFilePath = path.join(__dirname, '../db/migrations/add_email_verification_columns.sql');
    const sql = fs.readFileSync(sqlFilePath, 'utf8');
    
    // Execute the SQL
    await sequelize.query(sql);
    
    console.log('Migration completed successfully');
  } catch (error) {
    console.error('Error running migration:', error);
    process.exit(1);
  }
}

// Run the function
runEmailVerificationColumnsMigration()
  .then(() => {
    console.log('Migration completed, exiting...');
    process.exit(0);
  })
  .catch(err => {
    console.error('Unhandled error:', err);
    process.exit(1);
  });