import { sequelize } from '../config/database.js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function runDashboardLayoutsFKUpdate() {
  try {
    console.log('Starting dashboard_layouts foreign key update...');
    
    // Define the migration file to run
    const migrationFile = 'model_updates/update_dashboard_layouts_foreign_key.sql';
    
    // Read the SQL file
    const sqlFilePath = path.join(__dirname, '../db', migrationFile);
    const sql = fs.readFileSync(sqlFilePath, 'utf8');
    
    // Execute the SQL
    await sequelize.query(sql);
    
    console.log(`Migration ${migrationFile} completed successfully`);
    console.log('Foreign key constraint updated to include CASCADE option');
  } catch (error) {
    console.error('Error running dashboard_layouts foreign key update:', error);
    process.exit(1);
  }
}

// Run the function
runDashboardLayoutsFKUpdate()
  .then(() => {
    console.log('Migration completed, exiting...');
    process.exit(0);
  })
  .catch(err => {
    console.error('Unhandled error:', err);
    process.exit(1);
  });