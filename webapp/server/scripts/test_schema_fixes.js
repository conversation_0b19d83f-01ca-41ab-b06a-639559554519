import { sequelize } from '../config/database.js';

async function testSchemaMigrations() {
  try {
    console.log('Testing schema migrations...');

    // Get the current schema
    const currentSchema = process.env.DB_SCHEMA || 'site';
    console.log(`Current schema from environment: ${currentSchema}`);

    // Define the columns to check for each table
    const columnsToCheck = {
      farms: [
        'subscription_plan_id', 'subscription_status', 'subscription_start_date', 
        'subscription_end_date', 'billing_email', 'billing_address', 'billing_city', 
        'billing_state', 'billing_zip_code', 'billing_country', 'payment_method_id', 
        'stripe_customer_id'
      ],
      users: [
        'is_business_owner', 'is_approved', 'subscription_plan_id', 'user_type'
      ],
      user_farms: [
        'permissions', 'is_billing_contact', 'role'
      ]
    };

    // Check each table and its columns
    for (const [table, columns] of Object.entries(columnsToCheck)) {
      console.log(`\nChecking columns for table: ${table}`);

      for (const column of columns) {
        try {
          const result = await sequelize.query(
            `SELECT EXISTS (
              SELECT 1 
              FROM information_schema.columns 
              WHERE table_schema = '${currentSchema}' 
              AND table_name = '${table}' 
              AND column_name = '${column}'
            );`,
            { type: sequelize.QueryTypes.SELECT }
          );

          const columnExists = result[0].exists;
          console.log(`  - Column ${column}: ${columnExists ? 'EXISTS' : 'MISSING'}`);

          if (!columnExists) {
            console.error(`    WARNING: Column ${column} is missing from table ${table}`);
          }
        } catch (error) {
          console.error(`  Error checking column ${column} in table ${table}:`, error.message);
        }
      }
    }

    // Check constraints for enum values
    console.log('\nChecking constraints for enum values:');

    // Check user_type constraint
    try {
      const userTypeConstraint = await sequelize.query(
        `SELECT conname FROM pg_constraint 
         WHERE conrelid = '${currentSchema}.users'::regclass 
         AND conname = 'check_user_type';`,
        { type: sequelize.QueryTypes.SELECT }
      );

      console.log(`  - users.check_user_type constraint: ${userTypeConstraint.length > 0 ? 'EXISTS' : 'MISSING'}`);
    } catch (error) {
      console.error('  Error checking user_type constraint:', error.message);
    }

    // Check user_farm_role constraint
    try {
      const roleFarmConstraint = await sequelize.query(
        `SELECT conname FROM pg_constraint 
         WHERE conrelid = '${currentSchema}.user_farms'::regclass 
         AND conname = 'check_user_farm_role';`,
        { type: sequelize.QueryTypes.SELECT }
      );

      console.log(`  - user_farms.check_user_farm_role constraint: ${roleFarmConstraint.length > 0 ? 'EXISTS' : 'MISSING'}`);
    } catch (error) {
      console.error('  Error checking user_farm_role constraint:', error.message);
    }

    console.log('\nSchema migration test completed.');
  } catch (error) {
    console.error('Error testing schema migrations:', error);
  } finally {
    // Close the database connection
    await sequelize.close();
  }
}

// Run the test
testSchemaMigrations()
  .then(() => {
    console.log('Test completed, exiting...');
    process.exit(0);
  })
  .catch(err => {
    console.error('Unhandled error:', err);
    process.exit(1);
  });
