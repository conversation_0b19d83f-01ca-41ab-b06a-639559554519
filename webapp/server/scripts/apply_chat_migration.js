import { sequelize } from '../config/database.js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function applyChatMigration() {
  try {
    console.log('Applying chat system tables migration...');

    // Set the schema
    const schema = process.env.DB_SCHEMA || 'site';
    console.log(`Using schema: ${schema}`);

    // Read the chat system migration SQL file
    const sqlPath = path.join(__dirname, '../db/migrations/add_chat_system_tables.sql');

    if (!fs.existsSync(sqlPath)) {
      throw new Error(`Migration file not found: ${sqlPath}`);
    }

    const sql = fs.readFileSync(sqlPath, 'utf8');
    console.log('Migration file loaded successfully');

    // Check if chat_conversations table already exists
    const checkTableQuery = `
      SELECT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_schema = '${schema}'
        AND table_name = 'chat_conversations'
      );
    `;

    const [results] = await sequelize.query(checkTableQuery);
    const tableExists = results[0].exists;

    if (tableExists) {
      console.log('Chat conversations table already exists. Skipping migration.');
      return;
    }

    // Execute the migration in a transaction
    const transaction = await sequelize.transaction();

    try {
      // Set search path
      await sequelize.query(`SET search_path TO ${schema};`, { transaction });

      // Create the update_timestamp function first
      const createFunctionSQL = `
        CREATE OR REPLACE FUNCTION update_timestamp()
        RETURNS TRIGGER AS $$
        BEGIN
          NEW.updated_at = CURRENT_TIMESTAMP;
          RETURN NEW;
        END;
        $$ LANGUAGE plpgsql;
      `;

      await sequelize.query(createFunctionSQL, { transaction });
      console.log('Created update_timestamp function');

      // Execute the SQL migration
      await sequelize.query(sql, { transaction });

      await transaction.commit();
      console.log('Chat system migration applied successfully!');

      // Verify the tables were created
      const verifyQuery = `
        SELECT table_name
        FROM information_schema.tables
        WHERE table_schema = '${schema}'
        AND table_name LIKE 'chat_%'
        ORDER BY table_name;
      `;

      const [tables] = await sequelize.query(verifyQuery);
      console.log('Created chat tables:');
      tables.forEach(table => console.log(`  - ${table.table_name}`));

    } catch (error) {
      await transaction.rollback();
      throw error;
    }

  } catch (error) {
    console.error('Error applying chat migration:', error);
    process.exit(1);
  } finally {
    // Close the database connection
    await sequelize.close();
  }
}

applyChatMigration();
