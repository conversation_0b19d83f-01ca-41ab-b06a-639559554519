/**
 * <PERSON><PERSON><PERSON> to run the drop_tenant_tables migration
 * 
 * This script executes the SQL migration that drops tenant tables and columns
 * after they have been migrated to farm-based structures.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { sequelize } from '../config/database.js';
import dotenv from 'dotenv';

dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const runDropTenantTablesMigration = async () => {
  const transaction = await sequelize.transaction();

  try {
    console.log('Starting migration: dropping tenant tables and columns');

    // Read the SQL migration file
    const migrationPath = path.join(__dirname, '../db/migrations/drop_tenant_tables.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');

    // Execute the SQL migration
    await sequelize.query(migrationSQL, { transaction });

    // Commit the transaction
    await transaction.commit();
    console.log('Migration completed successfully');
    
    return { success: true, message: 'Migration completed successfully' };
  } catch (error) {
    // Rollback the transaction in case of error
    await transaction.rollback();
    console.error('Migration failed:', error);
    
    return { success: false, message: `Migration failed: ${error.message}` };
  }
};

// Run the migration
runDropTenantTablesMigration()
  .then(result => {
    console.log(result.message);
    process.exit(result.success ? 0 : 1);
  })
  .catch(error => {
    console.error('Unexpected error:', error);
    process.exit(1);
  });