import { sequelize } from '../config/database.js';
import DocumentFolder from '../models/DocumentFolder.js';
import Farm from '../models/Farm.js';
import User from '../models/User.js';
import '../models/associations.js'; // Import associations to ensure they're loaded

/**
 * Test script to verify that the DocumentFolder self-referential association works correctly.
 * This script:
 * 1. Creates a test farm and user if they don't exist
 * 2. Creates a parent folder
 * 3. Creates a child folder with the parent folder as its parent
 * 4. Retrieves the parent folder with its subFolders included
 * 5. Verifies that the child folder is in the parent folder's subFolders array
 */
async function testDocumentFolderAssociation() {
  try {
    console.log('Starting DocumentFolder association test...');

    // Create a test farm if it doesn't exist
    let testFarm = await Farm.findOne({ where: { name: 'Test Farm' } });
    if (!testFarm) {
      console.log('Creating test farm...');
      testFarm = await Farm.create({
        name: 'Test Farm',
        address: '123 Test St',
        city: 'Test City',
        state: 'TS',
        zip: '12345',
        country: 'Test Country',
        phone: '************',
        email: '<EMAIL>'
      });
      console.log('Test farm created with ID:', testFarm.id);
    }

    // Create a test user if it doesn't exist
    let testUser = await User.findOne({ where: { email: '<EMAIL>' } });
    if (!testUser) {
      console.log('Creating test user...');
      testUser = await User.create({
        first_name: 'Test',
        last_name: 'User',
        email: '<EMAIL>',
        password: 'password123',
        is_active: true
      });
      console.log('Test user created with ID:', testUser.id);
    }

    // Create a parent folder
    console.log('Creating parent folder...');
    const parentFolder = await DocumentFolder.create({
      name: 'Parent Folder',
      description: 'Test parent folder',
      parent_folder_id: '00000000-0000-0000-0000-000000000000', // Root folder
      farm_id: testFarm.id,
      created_by: testUser.id
    });
    console.log('Parent folder created with ID:', parentFolder.id);

    // Create a child folder
    console.log('Creating child folder...');
    const childFolder = await DocumentFolder.create({
      name: 'Child Folder',
      description: 'Test child folder',
      parent_folder_id: parentFolder.id,
      farm_id: testFarm.id,
      created_by: testUser.id
    });
    console.log('Child folder created with ID:', childFolder.id);

    // Retrieve the parent folder with its subFolders
    console.log('Retrieving parent folder with subFolders...');
    const retrievedParentFolder = await DocumentFolder.findByPk(parentFolder.id, {
      include: [
        {
          model: DocumentFolder,
          as: 'subFolders'
        }
      ]
    });

    // Verify that the child folder is in the parent folder's subFolders array
    if (retrievedParentFolder.subFolders && retrievedParentFolder.subFolders.length > 0) {
      const foundChildFolder = retrievedParentFolder.subFolders.find(
        folder => folder.id === childFolder.id
      );
      
      if (foundChildFolder) {
        console.log('SUCCESS: Child folder found in parent folder\'s subFolders array!');
        console.log('Child folder details:', {
          id: foundChildFolder.id,
          name: foundChildFolder.name,
          parent_folder_id: foundChildFolder.parent_folder_id
        });
      } else {
        console.error('ERROR: Child folder not found in parent folder\'s subFolders array!');
      }
    } else {
      console.error('ERROR: Parent folder has no subFolders!');
    }

    // Clean up - delete the test folders
    console.log('Cleaning up - deleting test folders...');
    await childFolder.destroy();
    await parentFolder.destroy();
    
    console.log('Test completed successfully!');
  } catch (error) {
    console.error('Error in test:', error);
  } finally {
    // Close the database connection
    await sequelize.close();
  }
}

// Run the test
testDocumentFolderAssociation();