import dotenv from 'dotenv';
import { encrypt, decrypt, encryptCameraCredentials, decryptCameraCredentials } from '../utils/encryption.js';

dotenv.config();

// Test encryption and decryption
const testEncryptionDecryption = () => {
  console.log('Testing encryption and decryption...');
  
  const testText = 'test-password-123';
  console.log(`Original text: ${testText}`);
  
  const encrypted = encrypt(testText);
  console.log(`Encrypted text: ${encrypted}`);
  
  const decrypted = decrypt(encrypted);
  console.log(`Decrypted text: ${decrypted}`);
  
  console.log(`Test ${testText === decrypted ? 'PASSED' : 'FAILED'}`);
  console.log('-----------------------------------');
};

// Test camera credentials encryption and decryption
const testCameraCredentials = () => {
  console.log('Testing camera credentials encryption and decryption...');
  
  const cameraConfig = {
    cameraType: 'amcrest',
    serialNumber: 'ABC123456789',
    username: 'admin',
    password: 'camera-password-123',
    p2pEnabled: true,
    streamUrl: 'p2p://ABC123456789'
  };
  
  console.log('Original config:');
  console.log(JSON.stringify(cameraConfig, null, 2));
  
  const encryptedConfig = encryptCameraCredentials(cameraConfig);
  console.log('\nEncrypted config:');
  console.log(JSON.stringify(encryptedConfig, null, 2));
  
  const decryptedConfig = decryptCameraCredentials(encryptedConfig);
  console.log('\nDecrypted config:');
  console.log(JSON.stringify(decryptedConfig, null, 2));
  
  console.log(`\nTest ${cameraConfig.password === decryptedConfig.password ? 'PASSED' : 'FAILED'}`);
  console.log('-----------------------------------');
};

// Run tests
console.log('Starting camera encryption tests...');
console.log('===================================');

// Check if encryption key is set
if (!process.env.ENCRYPTION_KEY) {
  console.error('ERROR: ENCRYPTION_KEY environment variable is not set.');
  console.error('Please set it in the .env file and try again.');
  process.exit(1);
}

// Run the tests
testEncryptionDecryption();
testCameraCredentials();

console.log('All tests completed.');