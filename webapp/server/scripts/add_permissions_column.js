import { sequelize } from '../config/database.js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function addPermissionsColumn() {
  try {
    console.log('Starting to add permissions column to user_farms table...');
    
    // Read the SQL file
    const sqlFilePath = path.join(__dirname, '../db/add_permissions_column.sql');
    console.log(`Reading SQL file from: ${sqlFilePath}`);
    const sql = fs.readFileSync(sqlFilePath, 'utf8');
    
    // Execute the SQL
    console.log('Executing SQL...');
    await sequelize.query(sql);
    
    console.log('permissions column added successfully to user_farms table');
  } catch (error) {
    console.error('Error adding permissions column:', error);
    process.exit(1);
  }
}

// Run the function
addPermissionsColumn()
  .then(() => {
    console.log('Migration completed, exiting...');
    process.exit(0);
  })
  .catch(err => {
    console.error('Unhandled error:', err);
    process.exit(1);
  });