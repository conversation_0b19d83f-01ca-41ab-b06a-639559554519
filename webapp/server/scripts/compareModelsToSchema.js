import { sequelize } from '../config/database.js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Get the directory name
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Import all models
import '../models/index.js';

// Note: We're not calling setupAssociations() to avoid association errors
// The models should already be registered with Sequelize

// Get all models from sequelize
const models = Object.values(sequelize.models);

async function compareModelsToSchema() {
  try {
    console.log('Comparing Sequelize models to database schema...');

    // Get the schema from environment variables or use default
    const schema = process.env.DB_SCHEMA || 'site';
    console.log(`Using schema: ${schema}`);

    // Create a directory for update files if it doesn't exist
    const updateDir = path.join(__dirname, '..', 'db', 'model_updates');
    if (!fs.existsSync(updateDir)) {
      fs.mkdirSync(updateDir);
    }

    // Process each model
    for (const model of models) {
      const modelName = model.name;
      const tableName = model.tableName;

      console.log(`\nProcessing model: ${modelName} (table: ${tableName})`);

      // Get model attributes
      const modelAttributes = Object.entries(model.rawAttributes).map(([name, attribute]) => {
        // Extract more detailed type information
        let typeInfo = {
          name: attribute.type.constructor.name,
          options: {}
        };

        // Handle DECIMAL type with precision and scale
        if (attribute.type.constructor.name === 'DECIMAL' && attribute.type._precision !== undefined) {
          typeInfo.options.precision = attribute.type._precision;
          typeInfo.options.scale = attribute.type._scale !== undefined ? attribute.type._scale : 0;
        }

        // Handle STRING type with length
        if (attribute.type.constructor.name === 'STRING' && attribute.type.options && attribute.type.options.length) {
          typeInfo.options.length = attribute.type.options.length;
        }

        // Handle ARRAY type with item type
        if (attribute.type.constructor.name === 'ARRAY' && attribute.type.options && attribute.type.options.type) {
          typeInfo.options.itemType = attribute.type.options.type.constructor.name;
        }

        return {
          name,
          type: typeInfo,
          allowNull: attribute.allowNull !== false, // Default to true if not specified
          defaultValue: attribute.defaultValue,
          primaryKey: attribute.primaryKey || false,
          references: attribute.references ? {
            model: attribute.references.model,
            key: attribute.references.key
          } : null,
          comment: attribute.comment
        };
      });

      // Query database schema for this table
      const query = `
        SELECT 
          column_name, 
          data_type, 
          udt_name,
          is_nullable,
          column_default,
          is_identity
        FROM 
          information_schema.columns
        WHERE 
          table_schema = '${schema}'
          AND table_name = '${tableName}';
      `;

      const [dbColumns] = await sequelize.query(query);

      if (dbColumns.length === 0) {
        console.log(`Table ${tableName} does not exist in the database. It will be created by Sequelize sync.`);
        continue;
      }

      console.log(`Found ${dbColumns.length} columns in database table ${tableName}`);

      // Check for missing columns
      const missingColumns = [];

      for (const attr of modelAttributes) {
        const dbColumn = dbColumns.find(col => col.column_name === attr.name);

        if (!dbColumn) {
          console.log(`Column ${attr.name} is missing from database table ${tableName}`);
          missingColumns.push(attr);
        }
      }

      if (missingColumns.length > 0) {
        console.log(`Found ${missingColumns.length} missing columns in table ${tableName}`);

        // Generate SQL update file
        const updateFileName = `add_missing_columns_to_${tableName}.sql`;
        const updateFilePath = path.join(updateDir, updateFileName);

        let updateSQL = `-- Add missing columns to ${tableName} table\n`;
        updateSQL += `-- Generated on ${new Date().toISOString()}\n\n`;
        updateSQL += `-- Get the schema from environment variable or use 'site' as default\n`;
        updateSQL += `DO $$\n`;
        updateSQL += `DECLARE\n`;
        updateSQL += `    schema_name TEXT;\n`;
        updateSQL += `BEGIN\n`;
        updateSQL += `    -- Try to get schema from DB_SCHEMA environment variable, fall back to current_schema() if not available\n`;
        updateSQL += `    BEGIN\n`;
        updateSQL += `        -- Check if DB_SCHEMA is set as a PostgreSQL variable with proper namespace\n`;
        updateSQL += `        SELECT current_setting('app.db_schema') INTO schema_name;\n`;
        updateSQL += `        EXCEPTION WHEN OTHERS THEN\n`;
        updateSQL += `            -- If not set, use current schema or 'site' as default\n`;
        updateSQL += `            SELECT current_schema() INTO schema_name;\n`;
        updateSQL += `    END;\n\n`;

        for (const column of missingColumns) {
          updateSQL += `    -- Add ${column.name} column if it doesn't exist\n`;
          updateSQL += `    EXECUTE format('\n`;
          updateSQL += `        ALTER TABLE %I.${tableName} \n`;
          updateSQL += `        ADD COLUMN IF NOT EXISTS ${column.name} `;

          // Map Sequelize type to PostgreSQL type
          let sqlType;
          const typeName = column.type.name;

          switch (typeName) {
            case 'UUID':
              sqlType = 'UUID';
              break;
            case 'STRING':
              const length = column.type.options.length || 255;
              sqlType = `VARCHAR(${length})`;
              break;
            case 'TEXT':
              sqlType = 'TEXT';
              break;
            case 'BOOLEAN':
              sqlType = 'BOOLEAN';
              break;
            case 'INTEGER':
              sqlType = 'INTEGER';
              break;
            case 'BIGINT':
              sqlType = 'BIGINT';
              break;
            case 'FLOAT':
              sqlType = 'FLOAT';
              break;
            case 'REAL':
              sqlType = 'REAL';
              break;
            case 'DOUBLE':
              sqlType = 'DOUBLE PRECISION';
              break;
            case 'DECIMAL':
              const precision = column.type.options.precision || 15;
              const scale = column.type.options.scale || 2;
              sqlType = `DECIMAL(${precision}, ${scale})`;
              break;
            case 'DATE':
              sqlType = 'TIMESTAMP';
              break;
            case 'DATEONLY':
              sqlType = 'DATE';
              break;
            case 'TIME':
              sqlType = 'TIME';
              break;
            case 'JSONB':
              sqlType = 'JSONB';
              break;
            case 'JSON':
              sqlType = 'JSON';
              break;
            case 'ARRAY':
              // Determine the item type
              let itemType = 'VARCHAR(255)';
              if (column.type.options.itemType) {
                switch (column.type.options.itemType) {
                  case 'STRING':
                    itemType = 'VARCHAR(255)';
                    break;
                  case 'INTEGER':
                    itemType = 'INTEGER';
                    break;
                  case 'FLOAT':
                    itemType = 'FLOAT';
                    break;
                  case 'BOOLEAN':
                    itemType = 'BOOLEAN';
                    break;
                  case 'UUID':
                    itemType = 'UUID';
                    break;
                  default:
                    itemType = 'VARCHAR(255)';
                }
              }
              sqlType = `${itemType}[]`;
              break;
            case 'ENUM':
              // For ENUM types, we'll use VARCHAR as a fallback
              // In a real implementation, you'd want to create a custom ENUM type
              sqlType = 'VARCHAR(255)';
              break;
            default:
              sqlType = 'VARCHAR(255)';
          }

          updateSQL += sqlType;

          // Add constraints
          if (!column.allowNull) {
            updateSQL += ' NOT NULL';
          }

          if (column.defaultValue !== undefined && column.defaultValue !== null) {
            if (typeof column.defaultValue === 'string') {
              updateSQL += ` DEFAULT '${column.defaultValue}'`;
            } else if (column.defaultValue === true || column.defaultValue === false) {
              updateSQL += ` DEFAULT ${column.defaultValue}`;
            } else if (column.type === 'UUID' && column.defaultValue === 'UUIDV4') {
              updateSQL += ` DEFAULT uuid_generate_v4()`;
            } else {
              updateSQL += ` DEFAULT ${column.defaultValue}`;
            }
          }

          updateSQL += `;\n`;
          updateSQL += `    ', schema_name);\n`;

          // Add comment if it exists
          if (column.comment) {
            updateSQL += `    -- Add comment to the column\n`;
            updateSQL += `    EXECUTE format('\n`;
            updateSQL += `        COMMENT ON COLUMN %I.${tableName}.${column.name} IS ''${column.comment.replace(/'/g, "''")}'';\n`;
            updateSQL += `    ', schema_name);\n`;
          }

          updateSQL += `\n`;
        }

        updateSQL += `    RAISE NOTICE 'Successfully added missing columns to ${tableName} table in schema %', schema_name;\n`;
        updateSQL += `END $$;\n`;

        fs.writeFileSync(updateFilePath, updateSQL);
        console.log(`Generated update file: ${updateFilePath}`);
      } else {
        console.log(`No missing columns found in table ${tableName}`);
      }
    }

    console.log('\nComparison completed successfully!');

  } catch (error) {
    console.error('Error comparing models to schema:', error);
    process.exit(1);
  }
}

// Run the function
compareModelsToSchema()
  .then(() => {
    console.log('Script completed, exiting...');
    process.exit(0);
  })
  .catch(err => {
    console.error('Unhandled error:', err);
    process.exit(1);
  });
