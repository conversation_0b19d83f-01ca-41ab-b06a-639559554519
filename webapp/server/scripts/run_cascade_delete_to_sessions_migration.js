import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';
import { sequelize } from '../config/database.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

async function runCascadeDeleteToSessionsMigration() {
  try {
    console.log('Running migration to add CASCADE delete to sessions table foreign key...');

    // Path to the migration SQL file
    const sqlPath = path.join(__dirname, '../db/migrations/add_cascade_delete_to_sessions.sql');
    const migrationSQL = fs.readFileSync(sqlPath, 'utf8');

    // Execute the migration
    await sequelize.query(migrationSQL);

    console.log('Migration completed successfully!');
    console.log('Sessions table foreign key constraint updated with CASCADE option.');
    
    // Close the database connection
    await sequelize.close();
    
    process.exit(0);
  } catch (error) {
    console.error('Error running migration:', error);
    
    // Close the database connection
    await sequelize.close();
    
    process.exit(1);
  }
}

// Run the migration
runCascadeDeleteToSessionsMigration();