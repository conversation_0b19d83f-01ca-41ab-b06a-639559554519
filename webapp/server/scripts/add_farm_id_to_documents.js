import { sequelize } from '../config/database.js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function addFarmIdToDocuments() {
  try {
    console.log('Starting migration: Adding farm_id to documents tables...');

    // Define the migration files
    const migrationFiles = [
      'add_farm_id_to_documents.sql',
      'add_farm_id_to_tenant_storage_usage.sql'
    ];

    // Run each migration file
    for (const file of migrationFiles) {
      console.log(`Running migration: ${file}`);

      // Read the SQL file
      const sqlFilePath = path.join(__dirname, '../db', file);
      const sql = fs.readFileSync(sqlFilePath, 'utf8');

      // Execute the SQL
      await sequelize.query(sql);

      console.log(`Migration ${file} completed successfully`);
    }

    console.log('All tables now have farm_id column properly set up');
  } catch (error) {
    console.error('Error running migration:', error);
    process.exit(1);
  }
}

// Run the function
addFarmIdToDocuments()
  .then(() => {
    console.log('Migration completed, exiting...');
    process.exit(0);
  })
  .catch(err => {
    console.error('Unhandled error:', err);
    process.exit(1);
  });
