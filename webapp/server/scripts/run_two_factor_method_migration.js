import { sequelize } from '../config/database.js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function runMigration() {
  try {
    console.log('Running migration to add two_factor_method column...');
    
    // Read the SQL file
    const sqlPath = path.join(__dirname, '../db/add_two_factor_method_column.sql');
    const sql = fs.readFileSync(sqlPath, 'utf8');
    
    // Execute the SQL
    await sequelize.query(sql);
    
    // Verify the column was added
    const [result] = await sequelize.query(`
      SELECT column_name, data_type, udt_name 
      FROM information_schema.columns 
      WHERE table_schema = 'site' 
      AND table_name = 'users' 
      AND column_name = 'two_factor_method'
    `);
    
    if (result.length === 0) {
      console.error('Error: two_factor_method column was not added!');
    } else {
      console.log('two_factor_method column was added successfully:');
      console.log(JSON.stringify(result[0], null, 2));
    }
    
    console.log('Migration completed successfully!');
  } catch (error) {
    console.error('Error running migration:', error);
  } finally {
    // Close the database connection
    await sequelize.close();
  }
}

runMigration();