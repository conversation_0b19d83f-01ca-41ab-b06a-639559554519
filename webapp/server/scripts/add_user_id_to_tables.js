import { sequelize } from '../config/database.js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function addUserIdToTables() {
  try {
    console.log('Starting to add user_id column to vendors, suppliers, and vets tables...');
    
    // Define the migration files
    const migrationFiles = [
      'add_user_id_to_vendors.sql',
      'add_user_id_to_suppliers.sql',
      'add_user_id_to_vets.sql'
    ];
    
    // Run each migration file
    for (const file of migrationFiles) {
      console.log(`Running migration: ${file}`);
      
      // Read the SQL file
      const sqlFilePath = path.join(__dirname, '../db/migrations', file);
      console.log(`Reading SQL file from: ${sqlFilePath}`);
      const sql = fs.readFileSync(sqlFilePath, 'utf8');
      
      // Execute the SQL
      console.log('Executing SQL...');
      await sequelize.query(sql);
      
      console.log(`Migration ${file} completed successfully`);
    }
    
    console.log('All migrations completed successfully');
  } catch (error) {
    console.error('Error adding user_id column:', error);
    process.exit(1);
  } finally {
    // Close the database connection
    await sequelize.close();
  }
}

// Run the migration
addUserIdToTables();
