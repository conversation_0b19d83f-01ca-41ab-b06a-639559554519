import { sequelize } from '../config/database.js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function runAiAssistantTables() {
  try {
    console.log('Running AI Assistant Tables migration...');

    // Check if users table exists
    let usersTableExists = false;
    const schema = process.env.DB_SCHEMA || 'site';

    try {
      await sequelize.query(`SELECT 1 FROM ${schema}.users LIMIT 1`);
      console.log('Users table exists, proceeding with AI Assistant tables creation.');
      usersTableExists = true;
    } catch (error) {
      console.log('Users table does not exist, need to run schema.sql first.');
    }

    // If users table doesn't exist, run schema.sql first
    if (!usersTableExists) {
      console.log('Initializing database schema with schema.sql...');
      const schemaPath = path.join(__dirname, '../db/schema.sql');
      const schemaSQL = fs.readFileSync(schemaPath, 'utf8');

      // Split the schema into individual statements
      const statements = schemaSQL
        .split(';')
        .filter(statement => statement.trim() !== '')
        .map(statement => statement.trim() + ';');

      console.log(`Found ${statements.length} SQL statements to execute in schema.sql.`);

      // Execute each statement
      for (let i = 0; i < statements.length; i++) {
        const statement = statements[i];
        try {
          // Set the search_path to use the specified schema
          if (i === 0) {
            await sequelize.query(`SET search_path TO ${schema};`);
            console.log(`Set search_path to schema: ${schema}`);
          }
          await sequelize.query(statement);
          console.log(`Executed statement ${i + 1}/${statements.length} from schema.sql`);
        } catch (error) {
          console.error(`Error executing statement ${i + 1}/${statements.length} from schema.sql:`, error.message);
          console.error('Statement:', statement);
          // Continue with next statement even if one fails
        }
      }

      console.log('Database schema initialized successfully.');
    }

    // Now run ai_assistant_tables.sql
    console.log('Creating AI Assistant tables...');
    const aiAssistantTablesPath = path.join(__dirname, '../db/ai_assistant_tables.sql');
    const aiAssistantTablesSQL = fs.readFileSync(aiAssistantTablesPath, 'utf8');

    // Split the SQL into individual statements
    const aiAssistantStatements = aiAssistantTablesSQL
      .split(';')
      .filter(statement => statement.trim() !== '')
      .map(statement => statement.trim() + ';');

    console.log(`Found ${aiAssistantStatements.length} SQL statements to execute in ai_assistant_tables.sql.`);

    // Set the search_path to use the specified schema before executing AI assistant tables
    await sequelize.query(`SET search_path TO ${schema};`);
    console.log(`Set search_path to schema: ${schema} for AI assistant tables`);

    // Execute each statement
    for (let i = 0; i < aiAssistantStatements.length; i++) {
      const statement = aiAssistantStatements[i];
      try {
        await sequelize.query(statement);
        console.log(`Executed statement ${i + 1}/${aiAssistantStatements.length} from ai_assistant_tables.sql`);
      } catch (error) {
        console.error(`Error executing statement ${i + 1}/${aiAssistantStatements.length} from ai_assistant_tables.sql:`, error.message);
        console.error('Statement:', statement);
        throw error;
      }
    }

    console.log('AI Assistant tables created successfully!');
  } catch (error) {
    console.error('Error running AI Assistant tables migration:', error);
  } finally {
    // Close the database connection
    await sequelize.close();
  }
}

runAiAssistantTables();
