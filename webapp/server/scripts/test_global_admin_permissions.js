import axios from 'axios';
import dotenv from 'dotenv';

dotenv.config();

const API_URL = process.env.API_URL || 'http://localhost:3001/api';

// Test function to check if global admin has all permissions
const testGlobalAdminPermissions = async () => {
  try {
    // Replace these with actual values from your system
    const globalAdminUserId = 'your_global_admin_user_id';
    const regularUserId = 'your_regular_user_id';
    const farmId = 'your_farm_id';
    const feature = 'some_feature';
    const permission = 'view';

    console.log('Testing global admin permissions...');

    // Test permission check for global admin
    const globalAdminResponse = await axios.get(
      `${API_URL}/permissions/check?farmId=${farmId}&userId=${globalAdminUserId}&feature=${feature}&permission=${permission}`
    );

    console.log('Global admin permission check result:', globalAdminResponse.data);

    // Test permission check for regular user
    const regularUserResponse = await axios.get(
      `${API_URL}/permissions/check?farmId=${farmId}&userId=${regularUserId}&feature=${feature}&permission=${permission}`
    );

    console.log('Regular user permission check result:', regularUserResponse.data);

    // Test viewable features for global admin
    const globalAdminFeaturesResponse = await axios.get(
      `${API_URL}/permissions/user/${globalAdminUserId}/farm/${farmId}/viewable`
    );

    console.log('Global admin viewable features:', globalAdminFeaturesResponse.data);

    // Test viewable features for regular user
    const regularUserFeaturesResponse = await axios.get(
      `${API_URL}/permissions/user/${regularUserId}/farm/${farmId}/viewable`
    );

    console.log('Regular user viewable features:', regularUserFeaturesResponse.data);

    console.log('Tests completed.');
  } catch (error) {
    console.error('Error testing global admin permissions:', error.message);
    if (error.response) {
      console.error('Response data:', error.response.data);
    }
  }
};

// Run the test
testGlobalAdminPermissions();