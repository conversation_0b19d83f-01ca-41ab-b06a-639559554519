import axios from 'axios';
import dotenv from 'dotenv';
import puppeteer from 'puppeteer';

// Load environment variables
dotenv.config();

const BASE_URL = process.env.BASE_URL || 'http://localhost:5173';
const TEST_EMAIL = process.env.TEST_EMAIL || '<EMAIL>';
const TEST_PASSWORD = process.env.TEST_PASSWORD || 'password123';

/**
 * Test help tips functionality
 */
const testHelpTips = async () => {
  console.log('Starting help tips test...');
  
  // Launch browser
  const browser = await puppeteer.launch({
    headless: false, // Set to true for headless testing
    defaultViewport: null,
    args: ['--window-size=1920,1080']
  });
  
  const page = await browser.newPage();
  
  try {
    // Login
    console.log('Logging in...');
    await page.goto(`${BASE_URL}/login`);
    await page.waitForSelector('#email-address');
    
    await page.type('#email-address', TEST_EMAIL);
    await page.type('#password', TEST_PASSWORD);
    await page.click('button[type="submit"]');
    
    // Wait for dashboard to load
    await page.waitForNavigation();
    console.log('Logged in successfully');
    
    // Check for help tips on dashboard
    console.log('Checking dashboard help tips...');
    await page.waitForSelector('.dashboard-page');
    
    // Wait for help tips to appear
    await page.waitForSelector('.help-tip', { timeout: 5000 })
      .then(() => console.log('✅ Dashboard help tips found'))
      .catch(() => console.log('❌ No help tips found on dashboard'));
    
    // Navigate to farms page
    console.log('Navigating to farms page...');
    await page.goto(`${BASE_URL}/farms`);
    await page.waitForSelector('h1');
    
    // Check for help tips on farms page
    await page.waitForSelector('.help-tip', { timeout: 5000 })
      .then(() => console.log('✅ Farms page help tips found'))
      .catch(() => console.log('❌ No help tips found on farms page'));
    
    // Test dismissing a help tip
    console.log('Testing help tip dismissal...');
    const helpTipCount = await page.$$eval('.help-tip', tips => tips.length);
    console.log(`Found ${helpTipCount} help tips`);
    
    if (helpTipCount > 0) {
      // Click the "Got it" button on the first help tip
      await page.click('.help-tip button:last-child');
      
      // Wait a moment for the dismissal to take effect
      await page.waitForTimeout(1000);
      
      // Check if the number of help tips decreased
      const newHelpTipCount = await page.$$eval('.help-tip', tips => tips.length);
      if (newHelpTipCount < helpTipCount) {
        console.log('✅ Help tip dismissal works correctly');
      } else {
        console.log('❌ Help tip dismissal did not work');
      }
    }
    
    // Navigate to equipment page
    console.log('Navigating to equipment page...');
    await page.goto(`${BASE_URL}/equipment`);
    await page.waitForSelector('h1');
    
    // Check for help tips on equipment page
    await page.waitForSelector('.help-tip', { timeout: 5000 })
      .then(() => console.log('✅ Equipment page help tips found'))
      .catch(() => console.log('❌ No help tips found on equipment page'));
    
    // Navigate to inventory page
    console.log('Navigating to inventory page...');
    await page.goto(`${BASE_URL}/inventory`);
    await page.waitForSelector('h1');
    
    // Check for help tips on inventory page
    await page.waitForSelector('.help-tip', { timeout: 5000 })
      .then(() => console.log('✅ Inventory page help tips found'))
      .catch(() => console.log('❌ No help tips found on inventory page'));
    
    console.log('Help tips test completed successfully');
  } catch (error) {
    console.error('Error during test:', error);
  } finally {
    // Close the browser
    await browser.close();
  }
};

// Add command line argument to reset dismissed tips before testing
if (process.argv.includes('--reset-dismissed')) {
  console.log('Resetting dismissed help tips...');
  
  // Login to get token
  axios.post(`${BASE_URL}/api/auth/login`, {
    email: TEST_EMAIL,
    password: TEST_PASSWORD
  })
  .then(response => {
    const token = response.data.token;
    
    // Reset dismissed help tips
    return axios.post(`${BASE_URL}/api/help/tips/dismissed/reset`, {}, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });
  })
  .then(() => {
    console.log('Dismissed help tips reset successfully');
    testHelpTips();
  })
  .catch(error => {
    console.error('Error resetting dismissed help tips:', error.response?.data || error.message);
    testHelpTips();
  });
} else {
  testHelpTips();
}