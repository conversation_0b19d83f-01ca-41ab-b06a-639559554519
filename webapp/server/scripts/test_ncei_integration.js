/**
 * Test script for NCEI API integration
 * 
 * This script tests the NCEI API integration by making requests to the API endpoints
 * and logging the responses. It can be used to verify that the integration is working
 * as expected once the APIs are available.
 * 
 * Usage: node test_ncei_integration.js
 */

import axios from 'axios';
import dotenv from 'dotenv';

dotenv.config();

const API_URL = process.env.API_URL || 'http://localhost:3002';
const FARM_ID = 1; // Replace with a valid farm ID
const FIELD_ID = 1; // Replace with a valid field ID

// Test parameters
const START_DATE = '2022-01-01';
const END_DATE = '2022-12-31';
const YEAR = 2022;
const BASE_TEMP = 50;

// Helper function to make API requests
const makeRequest = async (url, params = {}) => {
  try {
    console.log(`Making request to ${url} with params:`, params);
    const response = await axios.get(url, { params });
    console.log('Response status:', response.status);
    console.log('Response data:', JSON.stringify(response.data, null, 2));
    return response.data;
  } catch (error) {
    console.error('Error making request:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
    throw error;
  }
};

// Test functions
const testHistoricalTemperature = async () => {
  console.log('\n=== Testing Historical Temperature API ===');
  
  console.log('\n--- Farm Historical Temperature ---');
  await makeRequest(`${API_URL}/api/climate/temperature/farm/${FARM_ID}`, {
    startDate: START_DATE,
    endDate: END_DATE
  });
  
  console.log('\n--- Field Historical Temperature ---');
  await makeRequest(`${API_URL}/api/climate/temperature/field/${FIELD_ID}`, {
    startDate: START_DATE,
    endDate: END_DATE
  });
};

const testHistoricalPrecipitation = async () => {
  console.log('\n=== Testing Historical Precipitation API ===');
  
  console.log('\n--- Farm Historical Precipitation ---');
  await makeRequest(`${API_URL}/api/climate/precipitation/farm/${FARM_ID}`, {
    startDate: START_DATE,
    endDate: END_DATE
  });
  
  console.log('\n--- Field Historical Precipitation ---');
  await makeRequest(`${API_URL}/api/climate/precipitation/field/${FIELD_ID}`, {
    startDate: START_DATE,
    endDate: END_DATE
  });
};

const testClimateNormals = async () => {
  console.log('\n=== Testing Climate Normals API ===');
  
  console.log('\n--- Farm Climate Normals ---');
  await makeRequest(`${API_URL}/api/climate/normals/farm/${FARM_ID}`);
  
  console.log('\n--- Field Climate Normals ---');
  await makeRequest(`${API_URL}/api/climate/normals/field/${FIELD_ID}`);
};

const testGrowingDegreeDays = async () => {
  console.log('\n=== Testing Growing Degree Days API ===');
  
  console.log('\n--- Farm Growing Degree Days ---');
  await makeRequest(`${API_URL}/api/climate/gdd/farm/${FARM_ID}`, {
    startDate: START_DATE,
    endDate: END_DATE,
    baseTemp: BASE_TEMP
  });
  
  console.log('\n--- Field Growing Degree Days ---');
  await makeRequest(`${API_URL}/api/climate/gdd/field/${FIELD_ID}`, {
    startDate: START_DATE,
    endDate: END_DATE,
    baseTemp: BASE_TEMP
  });
};

const testFrostFreezeDates = async () => {
  console.log('\n=== Testing Frost/Freeze Dates API ===');
  
  console.log('\n--- Farm Frost/Freeze Dates ---');
  await makeRequest(`${API_URL}/api/climate/frost-freeze/farm/${FARM_ID}`, {
    year: YEAR
  });
  
  console.log('\n--- Field Frost/Freeze Dates ---');
  await makeRequest(`${API_URL}/api/climate/frost-freeze/field/${FIELD_ID}`, {
    year: YEAR
  });
};

const testExtremeWeatherEvents = async () => {
  console.log('\n=== Testing Extreme Weather Events API ===');
  
  console.log('\n--- Farm Extreme Weather Events ---');
  await makeRequest(`${API_URL}/api/climate/extreme-events/farm/${FARM_ID}`, {
    startDate: START_DATE,
    endDate: END_DATE
  });
};

const testAllClimateData = async () => {
  console.log('\n=== Testing All Climate Data API ===');
  
  console.log('\n--- Farm All Climate Data ---');
  await makeRequest(`${API_URL}/api/climate/all/farm/${FARM_ID}`);
};

// Main function to run all tests
const runTests = async () => {
  try {
    console.log('Starting NCEI API integration tests...');
    
    await testHistoricalTemperature();
    await testHistoricalPrecipitation();
    await testClimateNormals();
    await testGrowingDegreeDays();
    await testFrostFreezeDates();
    await testExtremeWeatherEvents();
    await testAllClimateData();
    
    console.log('\nAll tests completed successfully!');
  } catch (error) {
    console.error('\nTests failed:', error.message);
    process.exit(1);
  }
};

// Run the tests
runTests();