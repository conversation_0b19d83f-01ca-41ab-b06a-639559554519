import { sequelize } from '../config/database.js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function addStorageQuotaColumn() {
  try {
    console.log('Starting storage quota column migration...');
    
    // Define the migration file
    const migrationFile = 'add_storage_quota_column.sql';
    
    // Read the SQL file
    const sqlFilePath = path.join(__dirname, '../db', migrationFile);
    const sql = fs.readFileSync(sqlFilePath, 'utf8');
    
    // Execute the SQL
    await sequelize.query(sql);
    
    console.log(`Migration ${migrationFile} completed successfully`);
  } catch (error) {
    console.error('Error running storage quota column migration:', error);
    process.exit(1);
  }
}

// Run the function
addStorageQuotaColumn()
  .then(() => {
    console.log('Migration completed, exiting...');
    process.exit(0);
  })
  .catch(err => {
    console.error('Unhandled error:', err);
    process.exit(1);
  });