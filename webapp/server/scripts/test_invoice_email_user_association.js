#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to test the association between InvoiceEmail and User
 * This script attempts to find an InvoiceEmail and include its associated User
 * to verify that the association works correctly.
 */

import { sequelize } from '../config/database.js';
import InvoiceEmail from '../models/InvoiceEmail.js';
import User from '../models/User.js';
import { setupAssociations } from '../models/associations.js';

async function testInvoiceEmailUserAssociation() {
  try {
    console.log('Setting up associations...');
    setupAssociations();

    console.log('Testing InvoiceEmail to User association...');
    
    // Find an InvoiceEmail and include its associated User
    const invoiceEmail = await InvoiceEmail.findOne({
      include: [
        {
          model: User,
          as: 'sentByUser',
          attributes: ['id', 'email', 'first_name', 'last_name']
        }
      ]
    });

    if (!invoiceEmail) {
      console.log('No InvoiceEmail found in the database.');
      return;
    }

    console.log('Found InvoiceEmail:', {
      id: invoiceEmail.id,
      invoice_id: invoiceEmail.invoice_id,
      recipient_email: invoiceEmail.recipient_email,
      sent_at: invoiceEmail.sent_at,
      sent_by_user_id: invoiceEmail.sent_by_user_id
    });

    if (invoiceEmail.sentByUser) {
      console.log('Associated User:', {
        id: invoiceEmail.sentByUser.id,
        email: invoiceEmail.sentByUser.email,
        name: `${invoiceEmail.sentByUser.first_name} ${invoiceEmail.sentByUser.last_name}`
      });
      console.log('✅ User is successfully associated to InvoiceEmail!');
    } else {
      console.log('❌ No User associated with this InvoiceEmail.');
      if (invoiceEmail.sent_by_user_id) {
        console.log(`The InvoiceEmail has sent_by_user_id = ${invoiceEmail.sent_by_user_id}, but no User was found.`);
      } else {
        console.log('The InvoiceEmail has sent_by_user_id = NULL.');
      }
    }

    // Test the reverse association (User to InvoiceEmail)
    if (invoiceEmail.sent_by_user_id) {
      console.log('\nTesting User to InvoiceEmail association...');
      const user = await User.findByPk(invoiceEmail.sent_by_user_id, {
        include: [
          {
            model: InvoiceEmail,
            as: 'sentInvoiceEmails'
          }
        ]
      });

      if (user) {
        console.log(`User ${user.email} has sent ${user.sentInvoiceEmails.length} invoice emails.`);
        console.log('✅ InvoiceEmails are successfully associated to User!');
      } else {
        console.log(`❌ No User found with id ${invoiceEmail.sent_by_user_id}.`);
      }
    }

  } catch (error) {
    console.error('Error testing InvoiceEmail-User association:', error);
  } finally {
    // Close the database connection
    await sequelize.close();
  }
}

// Run the script if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  testInvoiceEmailUserAssociation()
    .then(() => {
      console.log('Script completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Script failed:', error);
      process.exit(1);
    });
}

export default testInvoiceEmailUserAssociation;