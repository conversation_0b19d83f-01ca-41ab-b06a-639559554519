import { sequelize } from '../config/database.js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function runMigration() {
  try {
    console.log('Running migration to add is_secure column to document_folders...');
    
    // Read the SQL file
    const sqlPath = path.join(__dirname, '../db/migrations/add_is_secure_to_document_folders.sql');
    const sql = fs.readFileSync(sqlPath, 'utf8');
    
    // Execute the SQL
    await sequelize.query(sql);
    
    console.log('Migration completed successfully!');
    console.log('Added is_secure column to document_folders table');
  } catch (error) {
    console.error('Error running migration:', error);
    if (error.message && error.message.includes('already exists')) {
      console.log('Column may already exist - this is normal if migration was run before');
    }
  } finally {
    // Close the database connection
    await sequelize.close();
  }
}

runMigration();
