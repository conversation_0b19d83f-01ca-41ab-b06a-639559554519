/**
 * Type definitions for the wsWrapper module
 */

export class WebSocket {
  static readonly CONNECTING: number;
  static readonly OPEN: number;
  static readonly CLOSING: number;
  static readonly CLOSED: number;
  
  readyState: number;
  
  constructor(address: string, protocols?: string | string[], options?: any);
  
  close(code?: number, reason?: string): void;
  ping(data?: any, mask?: boolean, cb?: (err: Error) => void): void;
  pong(data?: any, mask?: boolean, cb?: (err: Error) => void): void;
  send(data: any, cb?: (err?: Error) => void): void;
  send(data: any, options: { mask?: boolean; binary?: boolean }, cb?: (err?: Error) => void): void;
  
  on(event: 'close', listener: (code: number, reason: string) => void): this;
  on(event: 'error', listener: (err: Error) => void): this;
  on(event: 'message', listener: (data: any, isBinary: boolean) => void): this;
  on(event: 'open', listener: () => void): this;
  on(event: 'ping' | 'pong', listener: (data: Buffer) => void): this;
  on(event: string, listener: (...args: any[]) => void): this;
}

export class WebSocketServer {
  constructor(options: {
    host?: string;
    port?: number;
    backlog?: number;
    server?: any;
    verifyClient?: (info: { origin: string; secure: boolean; req: any }) => boolean;
    handleProtocols?: (protocols: string[], request: any) => string | false;
    path?: string;
    noServer?: boolean;
    clientTracking?: boolean;
    perMessageDeflate?: boolean | { [key: string]: any };
    maxPayload?: number;
  });
  
  on(event: 'close', listener: () => void): this;
  on(event: 'connection', listener: (socket: WebSocket, request: any) => void): this;
  on(event: 'error', listener: (error: Error) => void): this;
  on(event: 'headers', listener: (headers: string[], request: any) => void): this;
  on(event: 'listening', listener: () => void): this;
  on(event: string, listener: (...args: any[]) => void): this;
  
  close(cb?: (err?: Error) => void): void;
}

export const CONNECTING: number;
export const OPEN: number;
export const CLOSING: number;
export const CLOSED: number;
export function createWebSocketStream(websocket: WebSocket, options?: any): any;

export default WebSocket;
