# Database Configuration

## SSL Configuration for Digital Ocean Managed Database

The database configuration in `database.js` has been updated to include SSL options for connecting to the Digital Ocean managed PostgreSQL database. This change was made to address the following error:

```
error: no pg_hba.conf entry for host "*************", user "doadmin", database
```

### What is pg_hba.conf?

The `pg_hba.conf` file is a PostgreSQL configuration file that controls client authentication. It specifies which hosts are allowed to connect to which databases with which users using which authentication methods.

### The Issue

When deploying to Digital Ocean, the application server's IP address was not included in the pg_hba.conf file's allowed hosts list for the managed PostgreSQL database. This resulted in connection failures.

### The Solution

Instead of modifying the pg_hba.conf file (which is not directly accessible in a managed database service), we've added SSL configuration to the Sequelize connection options. This allows the application to connect to the database securely without requiring an entry in the pg_hba.conf file.

The following changes were made to `database.js`:

```javascript
dialectOptions: {
  ssl: process.env.NODE_ENV === 'production' ? {
    require: true,
    rejectUnauthorized: false
  } : false
}
```

This configuration:
1. Enables SSL in production environments
2. Sets `require: true` to force SSL connections
3. Sets `rejectUnauthorized: false` to allow self-signed certificates
4. Disables SSL in non-production environments

### Security Considerations

Setting `rejectUnauthorized: false` means that the application will not verify the server's SSL certificate. This is generally not recommended for production environments, but it's necessary in this case because Digital Ocean's managed databases use self-signed certificates.

In a more security-sensitive environment, you might want to:
1. Obtain the CA certificate used by Digital Ocean
2. Configure Sequelize to use that CA certificate for verification
3. Set `rejectUnauthorized: true`

### Testing

After making these changes, the application should be able to connect to the Digital Ocean managed PostgreSQL database without any pg_hba.conf errors.