#!/bin/bash
set -e

# Check if admin user already exists
echo "Checking if admin user exists..."
if curl -s -o /dev/null -w "%{http_code}" "http://localhost:8008/_synapse/admin/v2/users/@${MATRIX_ADMIN_USERNAME:-admin}:${MATRIX_DOMAIN:-nxtacre.local}" -H "Authorization: Bearer $ADMIN_TOKEN" | grep -q "200"; then
  echo "Admin user already exists."
  exit 0
fi

echo "Admin user does not exist. Creating admin user..."

# Get a nonce for registration
NONCE_RESPONSE=$(curl -s "http://localhost:8008/_synapse/admin/v1/register")
NONCE=$(echo $NONCE_RESPONSE | grep -o '"nonce":"[^"]*"' | cut -d'"' -f4)

if [ -z "$NONCE" ]; then
  echo "Failed to get nonce. Response: $NONCE_RESPONSE"
  exit 1
fi

echo "Got nonce: $NONCE"

# Create HMAC for admin registration
USERNAME="${MATRIX_ADMIN_USERNAME:-admin}"
PASSWORD="${MATRIX_ADMIN_PASSWORD:-$(openssl rand -hex 16)}"
ADMIN="admin"
SHARED_SECRET="${REGISTRATION_SHARED_SECRET}"

if [ -z "$SHARED_SECRET" ]; then
  echo "REGISTRATION_SHARED_SECRET is not set. Cannot create admin user."
  exit 1
fi

# Create the HMAC
MAC=$(echo -n -e "$NONCE\0$USERNAME\0$PASSWORD\0$ADMIN" | openssl dgst -sha1 -hmac "$SHARED_SECRET" | cut -d' ' -f2)

echo "Created HMAC for admin registration."

# Register the admin user
REGISTER_RESPONSE=$(curl -s -X POST "http://localhost:8008/_synapse/admin/v1/register" \
  -H "Content-Type: application/json" \
  -d "{\"nonce\":\"$NONCE\",\"username\":\"$USERNAME\",\"password\":\"$PASSWORD\",\"admin\":true,\"mac\":\"$MAC\"}")

if echo "$REGISTER_RESPONSE" | grep -q "\"access_token\""; then
  echo "Admin user created successfully."
  
  # Save the admin token to a file for future use
  ACCESS_TOKEN=$(echo $REGISTER_RESPONSE | grep -o '"access_token":"[^"]*"' | cut -d'"' -f4)
  echo "ADMIN_TOKEN=$ACCESS_TOKEN" > /data/admin_token.env
  echo "Admin token saved to /data/admin_token.env"
  
  # If password was auto-generated, save it to a file
  if [ "$PASSWORD" != "$MATRIX_ADMIN_PASSWORD" ]; then
    echo "MATRIX_ADMIN_PASSWORD=$PASSWORD" > /data/admin_password.env
    echo "Auto-generated admin password saved to /data/admin_password.env"
  fi
else
  echo "Failed to create admin user. Response: $REGISTER_RESPONSE"
  exit 1
fi

echo "Admin user setup complete."