# Server name
server_name: "${MATRIX_DOMAIN:-nxtacre.local}"
pid_file: /data/homeserver.pid

# Database configuration
database:
  name: psycopg2
  args:
    user: "${DB_USER:-postgres}"
    password: "${DB_PASSWORD:-postgres}"
    database: "${DB_NAME:-farmbooks}"
    host: "${DB_HOST:-postgres}"
    port: "${DB_PORT:-5432}"
    cp_min: 5
    cp_max: 10
    options: "-c search_path=matrix"

# Media storage
media_store_path: /data/media_store

# Log configuration
log_config: "/data/log.config"

# Registration
enable_registration: false
registration_shared_secret: "${REGISTRATION_SHARED_SECRET}"

# JWT Authentication for integration with NxtAcre
jwt_config:
  enabled: true
  secret: "${JWT_SECRET}"
  algorithm: "HS256"
  subject_claim: "sub"
  issuer: "nxtacre"
  audiences:
    - "nxtacre"

# Listeners
listeners:
  - port: 8008
    tls: false
    type: http
    x_forwarded: true
    resources:
      - names: [client, federation]
        compress: true

# Federation
federation_domain_whitelist:
  - "${MATRIX_DOMAIN:-nxtacre.local}"

# Rate limiting
rc_messages_per_second: 0.2
rc_message_burst_count: 10.0

# Retention policy
retention:
  enabled: true
  default_policy:
    min_lifetime: 1d
    max_lifetime: 365d

# URL previews
url_preview_enabled: true
url_preview_ip_range_blacklist:
  - '*********/8'
  - '10.0.0.0/8'
  - '**********/12'
  - '***********/16'
  - '**********/10'
  - '***********/16'
  - '::1/128'
  - 'fe80::/64'
  - 'fc00::/7'
url_preview_url_blacklist:
  - scheme: 'http'
    netloc: '^127\.0\.0\.1$'
  - scheme: 'http'
    netloc: '^localhost$'

# Encryption
encryption_enabled_by_default_for_room_type: "all"

# Presence
presence:
  enabled: true

# Push notifications
push:
  include_content: true

# Metrics
enable_metrics: false

# TURN server (for voice/video calls)
turn_uris: []
turn_shared_secret: "${TURN_SHARED_SECRET}"
turn_user_lifetime: 86400000
turn_allow_guests: true

# Experimental features
experimental_features:
  spaces_enabled: true