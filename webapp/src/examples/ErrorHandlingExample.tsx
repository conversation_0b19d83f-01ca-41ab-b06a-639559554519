import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';
import { handleApiError, redirectIfMaintenance } from '../utils/errorHandler';
import Layout from '../components/Layout';

const ErrorHandlingExample = () => {
  const [data, setData] = useState<any>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const navigate = useNavigate();

  useEffect(() => {
    const fetchData = async () => {
      try {
        // Check if the server is in maintenance mode
        await redirectIfMaintenance(navigate);

        // Make the API call
        const response = await axios.get('/api/some-endpoint');
        setData(response.data);
      } catch (error) {
        // Handle the error and get the error message
        const errorMessage = handleApiError(error, navigate, 'Failed to fetch data');
        // Ensure we're setting a string value for the error
        setError(typeof errorMessage === 'string' ? errorMessage : 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [navigate]);

  if (loading) {
    return (
      <Layout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
          <p className="ml-3 text-gray-500">Loading...</p>
        </div>
      </Layout>
    );
  }

  if (error) {
    return (
      <Layout>
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-md" role="alert">
          <span className="block sm:inline">{error}</span>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="bg-white p-6 rounded-lg shadow-card">
        <h2 className="text-2xl font-bold mb-4">Data Loaded Successfully</h2>
        <pre className="bg-gray-100 p-4 rounded-md overflow-auto">
          {JSON.stringify(data, null, 2)}
        </pre>
      </div>
    </Layout>
  );
};

export default ErrorHandlingExample;
