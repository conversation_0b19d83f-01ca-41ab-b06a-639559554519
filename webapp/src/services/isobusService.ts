import axios from 'axios';
import { API_URL } from '../config';

export interface IsobusData {
  id: string;
  equipment_id: string;
  timestamp: string;
  message_type: string | null;
  pgn: string | null;
  source_address: string | null;
  priority: number | null;
  data_length: number | null;
  raw_data: string | null;
  parsed_data: any | null;
  implement_type: string | null;
  implement_manufacturer: string | null;
  implement_model: string | null;
  task_id: string | null;
  task_name: string | null;
  field_id: string | null;
  status: string | null;
  error_code: string | null;
  created_at: string;
  updated_at: string;
}

export interface AggregatedIsobusData {
  time_period: string;
  message_count: number;
  implement_types: string[];
  status_summary: {
    [key: string]: number;
  };
  error_count: number;
  data_points: number;
}

/**
 * Get ISOBUS data for a specific equipment item
 */
export const getEquipmentIsobusData = async (
  equipmentId: string,
  startDate?: string,
  endDate?: string,
  limit: number = 100,
  offset: number = 0
): Promise<{ isobusData: IsobusData[]; total: number; limit: number; offset: number }> => {
  try {
    let url = `${API_URL}/isobus/equipment/${equipmentId}?limit=${limit}&offset=${offset}`;

    if (startDate) {
      url += `&startDate=${startDate}`;
    }

    if (endDate) {
      url += `&endDate=${endDate}`;
    }

    const response = await axios.get(url);
    return response.data;
  } catch (error) {
    console.error('Error fetching equipment ISOBUS data:', error);
    throw error;
  }
};

/**
 * Get the latest ISOBUS data for an equipment item
 */
export const getLatestIsobusData = async (equipmentId: string): Promise<IsobusData> => {
  try {
    const response = await axios.get(`${API_URL}/isobus/equipment/${equipmentId}/latest`);
    return response.data.isobusData;
  } catch (error) {
    console.error('Error fetching latest ISOBUS data:', error);
    throw error;
  }
};

/**
 * Get ISOBUS implements for a specific equipment item
 */
export const getEquipmentIsobusImplements = async (equipmentId: string): Promise<any[]> => {
  try {
    const response = await axios.get(`${API_URL}/isobus/equipment/${equipmentId}/implements`);
    return response.data.implements;
  } catch (error) {
    console.error('Error fetching ISOBUS implements:', error);
    throw error;
  }
};

/**
 * Create new ISOBUS data record
 */
export const createIsobusData = async (data: {
  equipmentId: string;
  messageType?: string;
  pgn?: string;
  sourceAddress?: string;
  priority?: number;
  dataLength?: number;
  rawData?: string;
  parsedData?: any;
  implementType?: string;
  implementManufacturer?: string;
  implementModel?: string;
  taskId?: string;
  taskName?: string;
  fieldId?: string;
  status?: string;
  errorCode?: string;
}): Promise<IsobusData> => {
  try {
    const response = await axios.post(`${API_URL}/isobus`, data);
    return response.data.isobusData;
  } catch (error) {
    console.error('Error creating ISOBUS data:', error);
    throw error;
  }
};

/**
 * Send ISOBUS data from external systems
 */
export const sendExternalIsobusData = async (data: {
  apiKey: string;
  equipmentIdentifier: string;
  isobusData: any;
}): Promise<{ isobusDataId: string }> => {
  try {
    const response = await axios.post(`${API_URL}/isobus/external`, data);
    return response.data;
  } catch (error) {
    console.error('Error sending external ISOBUS data:', error);
    throw error;
  }
};
