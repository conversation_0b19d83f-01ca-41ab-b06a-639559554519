import axios from 'axios';
import { API_URL } from '../config';

export interface FinancialItem {
  id: string;
  due_date: string;
  status: string;
  is_projected?: boolean;
}

export interface Invoice extends FinancialItem {
  invoice_number: string;
  issue_date: string;
  total_amount: number;
  payment_amount?: number;
  customer_id?: string;
  recipient_farm_id?: string;
}

export interface Bill extends FinancialItem {
  title: string;
  amount: number;
  vendor_id?: string;
}

export interface FinancialSummary {
  totalIncome: number;
  totalExpenses: number;
  totalProjectedIncome: number;
  totalProjectedExpenses: number;
  netCashFlow: number;
  projectedNetCashFlow: number;
  unpaidInvoicesCount: number;
  unpaidInvoicesAmount: number;
  unpaidBillsCount: number;
  unpaidBillsAmount: number;
}

export interface FinancialPlanningData {
  invoices: Invoice[];
  bills: Bill[];
  projectedInvoices: Invoice[];
  projectedBills: Bill[];
  financialSummary: FinancialSummary;
  dateRange: {
    startDate: string;
    endDate: string;
  };
}

export const getFinancialPlanningData = async (
  farmId: string,
  startDate?: string,
  endDate?: string
): Promise<FinancialPlanningData> => {
  try {
    let url = `${API_URL}/financial-planning/farm/${farmId}`;
    
    // Add query parameters if provided
    const params = new URLSearchParams();
    if (startDate) params.append('startDate', startDate);
    if (endDate) params.append('endDate', endDate);
    
    if (params.toString()) {
      url += `?${params.toString()}`;
    }
    
    const response = await axios.get(url);
    return response.data;
  } catch (error) {
    console.error('Error fetching financial planning data:', error);
    throw error;
  }
};

// Helper function to format currency
export const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 2
  }).format(amount);
};

// Helper function to get status color class
export const getStatusColorClass = (status: string): string => {
  switch (status.toLowerCase()) {
    case 'paid':
      return 'text-green-600 bg-green-100';
    case 'draft':
      return 'text-gray-600 bg-gray-100';
    case 'sent':
      return 'text-blue-600 bg-blue-100';
    case 'overdue':
      return 'text-red-600 bg-red-100';
    case 'unpaid':
      return 'text-orange-600 bg-orange-100';
    case 'projected':
      return 'text-purple-600 bg-purple-100';
    case 'cancelled':
      return 'text-gray-600 bg-gray-100';
    default:
      return 'text-gray-600 bg-gray-100';
  }
};

// Helper function to format date
export const formatDate = (dateString: string): string => {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
    year: 'numeric'
  });
};

// Helper function to group financial items by date
export const groupByDate = (items: FinancialItem[]): Record<string, FinancialItem[]> => {
  return items.reduce((acc, item) => {
    const dateKey = new Date(item.due_date).toISOString().split('T')[0];
    if (!acc[dateKey]) {
      acc[dateKey] = [];
    }
    acc[dateKey].push(item);
    return acc;
  }, {} as Record<string, FinancialItem[]>);
};

// Helper function to merge and sort all financial items by date
export const getAllFinancialItemsByDate = (
  invoices: Invoice[],
  bills: Bill[],
  projectedInvoices: Invoice[],
  projectedBills: Bill[]
): Record<string, (Invoice | Bill)[]> => {
  const allItems = [
    ...invoices,
    ...bills,
    ...projectedInvoices,
    ...projectedBills
  ];
  
  return groupByDate(allItems);
};