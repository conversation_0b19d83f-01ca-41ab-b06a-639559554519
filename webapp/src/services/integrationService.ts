import axios from 'axios';
import { API_URL } from '../config';

export interface Integration {
  id: string;
  name: string;
  description: string | null;
  version: string;
  author: string;
  entry_point: string;
  enabled: boolean;
  settings: any;
  settings_schema?: any;
  icon: string | null;
  farm_id: string | null;
  is_global: boolean;
  created_at: string;
  updated_at: string;
}

export interface AvailablePlugin {
  name: string;
  description: string;
  version: string;
  author: string;
  entry_point: string;
  icon: string;
  default_settings: any;
  settings_schema: any;
  folder: string;
  is_global: boolean;
}

/**
 * Get all integrations
 * @param farmId Optional farm ID to filter integrations
 * @returns Promise with integrations array
 */
export async function getIntegrations(farmId?: string): Promise<Integration[]> {
  try {
    const url = farmId ? `${API_URL}/integrations?farmId=${farmId}` : `${API_URL}/integrations`;
    const response = await axios.get(url);
    return response.data;
  } catch (error) {
    console.error('Error fetching integrations:', error);
    throw error;
  }
}

/**
 * Get a single integration by ID
 * @param id Integration ID
 * @returns Promise with integration object
 */
export async function getIntegration(id: string): Promise<Integration> {
  try {
    const response = await axios.get(`${API_URL}/integrations/${id}`);
    return response.data;
  } catch (error) {
    console.error(`Error fetching integration ${id}:`, error);
    throw error;
  }
}

/**
 * Create a new integration
 * @param data Integration data
 * @returns Promise with created integration
 */
export async function createIntegration(data: Partial<Integration>): Promise<Integration> {
  try {
    const response = await axios.post(`${API_URL}/integrations`, data);
    return response.data;
  } catch (error) {
    console.error('Error creating integration:', error);
    throw error;
  }
}

/**
 * Update an integration
 * @param id Integration ID
 * @param data Updated integration data
 * @returns Promise with updated integration
 */
export async function updateIntegration(id: string, data: Partial<Integration>): Promise<Integration> {
  try {
    const response = await axios.put(`${API_URL}/integrations/${id}`, data);
    return response.data;
  } catch (error) {
    console.error(`Error updating integration ${id}:`, error);
    throw error;
  }
}

/**
 * Delete an integration
 * @param id Integration ID
 * @returns Promise with success message
 */
export async function deleteIntegration(id: string): Promise<{ message: string }> {
  try {
    const response = await axios.delete(`${API_URL}/integrations/${id}`);
    return response.data;
  } catch (error) {
    console.error(`Error deleting integration ${id}:`, error);
    throw error;
  }
}

/**
 * Enable or disable an integration
 * @param id Integration ID
 * @param enabled Whether to enable or disable the integration
 * @returns Promise with updated integration
 */
export async function toggleIntegration(id: string, enabled: boolean): Promise<Integration> {
  try {
    const response = await axios.put(`${API_URL}/integrations/${id}/toggle`, { enabled });
    return response.data;
  } catch (error) {
    console.error(`Error toggling integration ${id}:`, error);
    throw error;
  }
}

/**
 * Update integration settings
 * @param id Integration ID
 * @param settings Updated settings object
 * @returns Promise with updated integration
 */
export async function updateIntegrationSettings(id: string, settings: any): Promise<Integration> {
  try {
    const response = await axios.put(`${API_URL}/integrations/${id}/settings`, { settings });
    return response.data;
  } catch (error) {
    console.error(`Error updating settings for integration ${id}:`, error);
    throw error;
  }
}

/**
 * Get available integration plugins
 * @returns Promise with available plugins array
 */
export async function getAvailablePlugins(): Promise<AvailablePlugin[]> {
  try {
    const response = await axios.get(`${API_URL}/integrations/available`);
    return response.data;
  } catch (error) {
    console.error('Error fetching available plugins:', error);
    throw error;
  }
}

/**
 * Install a plugin
 * @param folder Plugin folder name
 * @returns Promise with installed integration
 */
export async function installPlugin(folder: string): Promise<Integration> {
  try {
    const response = await axios.post(`${API_URL}/integrations/install`, { folder });
    return response.data;
  } catch (error) {
    console.error(`Error installing plugin from folder ${folder}:`, error);
    throw error;
  }
}
