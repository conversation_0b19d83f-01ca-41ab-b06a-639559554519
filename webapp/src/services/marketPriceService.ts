import axios from 'axios';
import { API_URL } from '../config';

// Types for market price data
export interface CommodityPrice {
  commodity: string;
  price: number;
  unit: string;
  date: string;
  location: string;
  trend: 'up' | 'down' | 'stable';
  percentChange: number;
}

export interface FuturePrice {
  commodity: string;
  price: number;
  unit: string;
  month: string;
  year: string;
  exchange: string;
}

export interface MarketPriceData {
  currentPrices: CommodityPrice[];
  futurePrices: FuturePrice[];
  lastUpdated: string;
}

// Create a proxy endpoint on your server to avoid CORS issues and hide API keys
const MARKET_PRICE_API_BASE = `${API_URL}/market-prices`;

/**
 * Get market prices for agricultural commodities
 * @param farmLocation The location of the farm (latitude, longitude)
 * @param commodities Array of commodities to fetch prices for (e.g., ['beef', 'corn', 'hay'])
 * @returns Promise<MarketPriceData> Market price data for the specified commodities
 */
export const getMarketPrices = async (
  farmLocation: { latitude: number; longitude: number },
  commodities: string[] = ['beef', 'corn', 'hay', 'wheat', 'soybeans']
): Promise<MarketPriceData> => {
  try {
    // Use your backend as a proxy to avoid CORS issues and hide API keys
    const response = await axios.get(
      `${MARKET_PRICE_API_BASE}?lat=${farmLocation.latitude}&lon=${farmLocation.longitude}&commodities=${commodities.join(',')}`
    );

    return response.data;
  } catch (error) {
    console.error('Error fetching market price data:', error);

    // Return mock data as fallback
    return generateMockMarketData(commodities);
  }
};

/**
 * Get price predictions for agricultural commodity futures
 * @param farmLocation The location of the farm (latitude, longitude)
 * @param commodities Array of commodities to fetch futures for
 * @returns Promise<FuturePrice[]> Future price predictions for the specified commodities
 */
export const getCommodityFutures = async (
  farmLocation: { latitude: number; longitude: number },
  commodities: string[] = ['beef', 'corn', 'hay', 'wheat', 'soybeans']
): Promise<FuturePrice[]> => {
  try {
    // Use your backend as a proxy to avoid CORS issues and hide API keys
    const response = await axios.get(
      `${MARKET_PRICE_API_BASE}/futures?lat=${farmLocation.latitude}&lon=${farmLocation.longitude}&commodities=${commodities.join(',')}`
    );

    return response.data;
  } catch (error) {
    console.error('Error fetching commodity futures data:', error);

    // Return mock data as fallback
    return generateMockFuturesData(commodities);
  }
};

/**
 * Get historical price data for a specific commodity
 * @param commodity The commodity to fetch historical data for
 * @param startDate The start date for historical data (YYYY-MM-DD)
 * @param endDate The end date for historical data (YYYY-MM-DD)
 * @returns Promise<CommodityPrice[]> Historical price data for the specified commodity
 */
export const getHistoricalPrices = async (
  commodity: string,
  startDate: string,
  endDate: string
): Promise<CommodityPrice[]> => {
  try {
    // Use your backend as a proxy to avoid CORS issues and hide API keys
    const response = await axios.get(
      `${MARKET_PRICE_API_BASE}/historical?commodity=${commodity}&startDate=${startDate}&endDate=${endDate}`
    );

    return response.data;
  } catch (error) {
    console.error('Error fetching historical price data:', error);

    // Return mock data as fallback
    return generateMockHistoricalData(commodity, startDate, endDate);
  }
};

/**
 * Set up price alerts for specific commodities
 * @param commodity The commodity to set an alert for
 * @param targetPrice The target price to trigger the alert
 * @param condition The condition for the alert ('above' or 'below')
 * @returns Promise<{ success: boolean, message: string }> Result of setting up the alert
 */
export const setPriceAlert = async (
  commodity: string,
  targetPrice: number,
  condition: 'above' | 'below'
): Promise<{ success: boolean; message: string }> => {
  try {
    // Use your backend to store the alert
    const response = await axios.post(`${MARKET_PRICE_API_BASE}/alerts`, {
      commodity,
      targetPrice,
      condition
    });

    return response.data;
  } catch (error) {
    console.error('Error setting price alert:', error);
    return {
      success: false,
      message: 'Failed to set price alert. Please try again later.'
    };
  }
};

// Helper functions to generate mock data for development and fallback

const generateMockMarketData = (commodities: string[]): MarketPriceData => {
  const currentPrices: CommodityPrice[] = commodities.map(commodity => {
    const basePrice = getBasePriceForCommodity(commodity);
    const randomChange = (Math.random() * 0.1 - 0.05) * basePrice; // -5% to +5%
    const price = basePrice + randomChange;
    const percentChange = (randomChange / basePrice) * 100;
    
    return {
      commodity,
      price: parseFloat(price.toFixed(2)),
      unit: getCommodityUnit(commodity),
      date: new Date().toISOString().split('T')[0],
      location: 'National Average',
      trend: percentChange > 0 ? 'up' : percentChange < 0 ? 'down' : 'stable',
      percentChange: parseFloat(percentChange.toFixed(2))
    };
  });

  const futurePrices: FuturePrice[] = generateMockFuturesData(commodities);

  return {
    currentPrices,
    futurePrices,
    lastUpdated: new Date().toISOString()
  };
};

const generateMockFuturesData = (commodities: string[]): FuturePrice[] => {
  const futures: FuturePrice[] = [];
  const months = ['JAN', 'FEB', 'MAR', 'APR', 'MAY', 'JUN', 'JUL', 'AUG', 'SEP', 'OCT', 'NOV', 'DEC'];
  const currentDate = new Date();
  const currentYear = currentDate.getFullYear();
  const currentMonth = currentDate.getMonth();

  commodities.forEach(commodity => {
    const basePrice = getBasePriceForCommodity(commodity);
    
    // Generate futures for the next 6 months
    for (let i = 1; i <= 6; i++) {
      const futureMonth = (currentMonth + i) % 12;
      const futureYear = currentYear + Math.floor((currentMonth + i) / 12);
      
      // Prices tend to increase for future months (with some randomness)
      const futurePrice = basePrice * (1 + (i * 0.02) + (Math.random() * 0.04 - 0.02));
      
      futures.push({
        commodity,
        price: parseFloat(futurePrice.toFixed(2)),
        unit: getCommodityUnit(commodity),
        month: months[futureMonth],
        year: futureYear.toString(),
        exchange: 'CME'
      });
    }
  });

  return futures;
};

const generateMockHistoricalData = (commodity: string, startDate: string, endDate: string): CommodityPrice[] => {
  const basePrice = getBasePriceForCommodity(commodity);
  const unit = getCommodityUnit(commodity);
  
  const start = new Date(startDate);
  const end = new Date(endDate);
  const daysDiff = Math.ceil((end.getTime() - start.getTime()) / (1000 * 3600 * 24));
  
  const historicalPrices: CommodityPrice[] = [];
  
  for (let i = 0; i <= daysDiff; i++) {
    const currentDate = new Date(start);
    currentDate.setDate(currentDate.getDate() + i);
    
    // Generate a somewhat realistic price series with some randomness and trend
    const trendFactor = 1 + (i / daysDiff) * 0.1; // 10% increase over the period
    const randomFactor = 1 + (Math.random() * 0.06 - 0.03); // -3% to +3% daily variation
    const price = basePrice * trendFactor * randomFactor;
    
    historicalPrices.push({
      commodity,
      price: parseFloat(price.toFixed(2)),
      unit,
      date: currentDate.toISOString().split('T')[0],
      location: 'National Average',
      trend: Math.random() > 0.5 ? 'up' : 'down',
      percentChange: parseFloat((Math.random() * 2 - 1).toFixed(2)) // -1% to +1%
    });
  }
  
  return historicalPrices;
};

// Helper functions for commodity-specific data

const getBasePriceForCommodity = (commodity: string): number => {
  switch (commodity.toLowerCase()) {
    case 'beef':
      return 175.00; // $/cwt (hundredweight)
    case 'corn':
      return 5.75; // $/bushel
    case 'hay':
      return 200.00; // $/ton
    case 'wheat':
      return 7.25; // $/bushel
    case 'soybeans':
      return 13.50; // $/bushel
    case 'cotton':
      return 0.85; // $/lb
    case 'rice':
      return 18.50; // $/cwt
    case 'oats':
      return 4.25; // $/bushel
    case 'barley':
      return 5.50; // $/bushel
    default:
      return 10.00; // Default price
  }
};

const getCommodityUnit = (commodity: string): string => {
  switch (commodity.toLowerCase()) {
    case 'beef':
      return 'cwt'; // hundredweight (100 lbs)
    case 'corn':
    case 'wheat':
    case 'soybeans':
    case 'oats':
    case 'barley':
      return 'bushel';
    case 'hay':
      return 'ton';
    case 'cotton':
      return 'lb';
    case 'rice':
      return 'cwt';
    default:
      return 'unit';
  }
};