import axios from 'axios';
import { API_URL } from '../config';

/**
 * Interface for all field data
 */
export interface AllFieldData {
  fieldData: FieldDataGov;
  recommendedCrops: string[];
  conservationPractices: string[];
  historicalYieldData: FieldDataGov['historical_yield_data'];
}

/**
 * Get all field data in a single request
 * @param fieldId The field ID
 * @returns Promise<AllFieldData> All field data
 */
export const getAllFieldData = async (fieldId: string): Promise<AllFieldData> => {
  try {
    const response = await axios.get(`${API_URL}/fields/${fieldId}/all-data`, {
      headers: {
        'Content-Type': 'application/json',
      },
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching all field data:', error);
    throw error;
  }
};

/**
 * Interface for field data from data.gov
 */
export interface FieldDataGov {
  field_id: string;
  soil_type: string;
  soil_health_index: number;
  conservation_practices: string[];
  land_capability_class: string;
  erosion_risk: string;
  water_availability: string;
  recommended_crops: string[];
  historical_yield_data: {
    year: number;
    crop: string;
    yield: number;
    unit: string;
  }[];
  last_updated: string;
}

/**
 * Get field data from data.gov for a specific field
 * @param fieldId The field ID
 * @returns Promise<FieldDataGov> Field data from data.gov
 */
export const getFieldDataGov = async (fieldId: string): Promise<FieldDataGov> => {
  try {
    const response = await axios.get(`${API_URL}/fields/${fieldId}/data-gov`, {
      headers: {
        'Content-Type': 'application/json',
      },
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching field data from data.gov:', error);
    throw error;
  }
};

/**
 * Get field data from data.gov for all fields in a farm
 * @param farmId The farm ID
 * @returns Promise<FieldDataGov[]> Field data from data.gov for all fields in the farm
 */
export const getFarmFieldsDataGov = async (farmId: string): Promise<FieldDataGov[]> => {
  try {
    const response = await axios.get(`${API_URL}/fields/farm/${farmId}/data-gov`, {
      headers: {
        'Content-Type': 'application/json',
      },
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching farm fields data from data.gov:', error);
    throw error;
  }
};

/**
 * Get recommended crops for a field based on data.gov information
 * @param fieldId The field ID
 * @returns Promise<string[]> Recommended crops for the field
 */
export const getFieldRecommendedCrops = async (fieldId: string): Promise<string[]> => {
  try {
    const response = await axios.get(`${API_URL}/fields/${fieldId}/recommended-crops`, {
      headers: {
        'Content-Type': 'application/json',
      },
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching field recommended crops:', error);
    throw error;
  }
};

/**
 * Get conservation practices for a field based on data.gov information
 * @param fieldId The field ID
 * @returns Promise<string[]> Conservation practices for the field
 */
export const getFieldConservationPractices = async (fieldId: string): Promise<string[]> => {
  try {
    const response = await axios.get(`${API_URL}/fields/${fieldId}/conservation-practices`, {
      headers: {
        'Content-Type': 'application/json',
      },
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching field conservation practices:', error);
    throw error;
  }
};

/**
 * Get historical yield data for a field based on data.gov information
 * @param fieldId The field ID
 * @returns Promise<FieldDataGov['historical_yield_data']> Historical yield data for the field
 */
export const getFieldHistoricalYieldData = async (fieldId: string): Promise<FieldDataGov['historical_yield_data']> => {
  try {
    const response = await axios.get(`${API_URL}/fields/${fieldId}/historical-yield`, {
      headers: {
        'Content-Type': 'application/json',
      },
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching field historical yield data:', error);
    throw error;
  }
};
