import axios from 'axios';
import { API_URL } from '../config';

const VETS_API_URL = `${API_URL}/vets`;

export interface Vet {
  id: string;
  name: string;
  specialization?: string;
  phone?: string;
  email?: string;
  address?: string;
  city?: string;
  state?: string;
  zip_code?: string;
  license_number?: string;
  is_global: boolean;
  farm_id?: string;
  notes?: string;
  created_at: string;
  updated_at: string;
}

export interface VetFormData {
  name: string;
  specialization?: string;
  phone?: string;
  email?: string;
  address?: string;
  city?: string;
  state?: string;
  zipCode?: string;
  licenseNumber?: string;
  isGlobal: boolean;
  farmId?: string;
  notes?: string;
}

// Get all vets (global and farm-specific)
export const getAllVets = async (): Promise<Vet[]> => {
  try {
    const response = await axios.get(VETS_API_URL);
    return response.data.vets;
  } catch (error) {
    console.error('Error fetching all vets:', error);
    throw error;
  }
};

// Get all global vets (managed by admin)
export const getGlobalVets = async (): Promise<Vet[]> => {
  try {
    const response = await axios.get(`${VETS_API_URL}/global`);
    return response.data.vets;
  } catch (error) {
    console.error('Error fetching global vets:', error);
    throw error;
  }
};

// Get all vets for a specific farm (including global vets)
export const getFarmVets = async (farmId: string): Promise<{ farmVets: Vet[], globalVets: Vet[] }> => {
  try {
    const response = await axios.get(`${VETS_API_URL}/farm/${farmId}`);
    return {
      farmVets: response.data.farmVets || [],
      globalVets: response.data.globalVets || []
    };
  } catch (error) {
    console.error(`Error fetching vets for farm ${farmId}:`, error);
    throw error;
  }
};

// Get a single vet by ID
export const getVetById = async (vetId: string): Promise<Vet> => {
  try {
    const response = await axios.get(`${VETS_API_URL}/${vetId}`);
    return response.data.vet;
  } catch (error) {
    console.error(`Error fetching vet ${vetId}:`, error);
    throw error;
  }
};

// Create a new vet
export const createVet = async (vetData: VetFormData): Promise<Vet> => {
  try {
    const response = await axios.post(VETS_API_URL, vetData);
    return response.data.vet;
  } catch (error) {
    console.error('Error creating vet:', error);
    throw error;
  }
};

// Update a vet
export const updateVet = async (vetId: string, vetData: VetFormData): Promise<Vet> => {
  try {
    const response = await axios.put(`${VETS_API_URL}/${vetId}`, vetData);
    return response.data.vet;
  } catch (error) {
    console.error(`Error updating vet ${vetId}:`, error);
    throw error;
  }
};

// Delete a vet
export const deleteVet = async (vetId: string): Promise<void> => {
  try {
    await axios.delete(`${VETS_API_URL}/${vetId}`);
  } catch (error) {
    console.error(`Error deleting vet ${vetId}:`, error);
    throw error;
  }
};

// Search vets by name, specialization, or location
export const searchVets = async (query: string, farmId?: string): Promise<Vet[]> => {
  try {
    const params: { query: string; farmId?: string } = { query };
    if (farmId) {
      params.farmId = farmId;
    }

    const response = await axios.get(`${VETS_API_URL}/search`, { params });
    return response.data.vets;
  } catch (error) {
    console.error('Error searching vets:', error);
    throw error;
  }
};
