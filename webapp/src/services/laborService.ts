import axios from 'axios';
import { API_URL } from '../config';

// Types
export interface SeasonalWorker {
  id?: number;
  farmId: number;
  firstName: string;
  lastName: string;
  contactInfo: string;
  startDate: string;
  endDate: string;
  position: string;
  status: 'active' | 'inactive' | 'pending';
  documentationStatus: 'complete' | 'incomplete' | 'pending';
  notes: string;
}

export interface LaborCostAnalysis {
  id?: number;
  farmId: number;
  analysisDate: string;
  period: string;
  taskId?: number;
  cropId?: number;
  fieldId?: number;
  laborHours: number;
  laborCost: number;
  productivity: number;
  notes: string;
}

export interface ComplianceRecord {
  id?: number;
  farmId: number;
  recordType: string;
  recordDate: string;
  expirationDate?: string;
  status: 'compliant' | 'non-compliant' | 'pending';
  details: string;
  documentUrl?: string;
  notes: string;
}

export interface WorkerCertification {
  id?: number;
  farmId: number;
  workerId: number;
  certificationType: string;
  issueDate: string;
  expirationDate: string;
  status: 'active' | 'expired' | 'pending';
  documentUrl?: string;
  notes: string;
}

// Seasonal Worker Management
export const createSeasonalWorker = async (data: SeasonalWorker) => {
  const response = await axios.post(`${API_URL}/api/labor/seasonal-workers`, data);
  return response.data;
};

export const getSeasonalWorkers = async (farmId: number) => {
  const response = await axios.get(`${API_URL}/api/labor/seasonal-workers?farmId=${farmId}`);
  return response.data;
};

export const getSeasonalWorker = async (id: number) => {
  const response = await axios.get(`${API_URL}/api/labor/seasonal-workers/${id}`);
  return response.data;
};

export const updateSeasonalWorker = async (id: number, data: SeasonalWorker) => {
  const response = await axios.put(`${API_URL}/api/labor/seasonal-workers/${id}`, data);
  return response.data;
};

export const deleteSeasonalWorker = async (id: number) => {
  const response = await axios.delete(`${API_URL}/api/labor/seasonal-workers/${id}`);
  return response.data;
};

// Labor Cost Analysis
export const createLaborCostAnalysis = async (data: LaborCostAnalysis) => {
  const response = await axios.post(`${API_URL}/api/labor/cost-analysis`, data);
  return response.data;
};

export const getLaborCostAnalyses = async (farmId: number) => {
  const response = await axios.get(`${API_URL}/api/labor/cost-analysis?farmId=${farmId}`);
  return response.data;
};

export const getLaborCostAnalysis = async (id: number) => {
  const response = await axios.get(`${API_URL}/api/labor/cost-analysis/${id}`);
  return response.data;
};

export const updateLaborCostAnalysis = async (id: number, data: LaborCostAnalysis) => {
  const response = await axios.put(`${API_URL}/api/labor/cost-analysis/${id}`, data);
  return response.data;
};

export const deleteLaborCostAnalysis = async (id: number) => {
  const response = await axios.delete(`${API_URL}/api/labor/cost-analysis/${id}`);
  return response.data;
};

// Compliance Tracking
export const createComplianceRecord = async (data: ComplianceRecord) => {
  const response = await axios.post(`${API_URL}/api/labor/compliance`, data);
  return response.data;
};

export const getComplianceRecords = async (farmId: number) => {
  const response = await axios.get(`${API_URL}/api/labor/compliance?farmId=${farmId}`);
  return response.data;
};

export const getComplianceRecord = async (id: number) => {
  const response = await axios.get(`${API_URL}/api/labor/compliance/${id}`);
  return response.data;
};

export const updateComplianceRecord = async (id: number, data: ComplianceRecord) => {
  const response = await axios.put(`${API_URL}/api/labor/compliance/${id}`, data);
  return response.data;
};

export const deleteComplianceRecord = async (id: number) => {
  const response = await axios.delete(`${API_URL}/api/labor/compliance/${id}`);
  return response.data;
};

// Worker Certification Tracking
export const createWorkerCertification = async (data: WorkerCertification) => {
  const response = await axios.post(`${API_URL}/api/labor/certifications`, data);
  return response.data;
};

export const getWorkerCertifications = async (farmId: number, workerId?: number) => {
  const url = workerId 
    ? `${API_URL}/api/labor/certifications?farmId=${farmId}&workerId=${workerId}`
    : `${API_URL}/api/labor/certifications?farmId=${farmId}`;
  const response = await axios.get(url);
  return response.data;
};

export const getWorkerCertification = async (id: number) => {
  const response = await axios.get(`${API_URL}/api/labor/certifications/${id}`);
  return response.data;
};

export const updateWorkerCertification = async (id: number, data: WorkerCertification) => {
  const response = await axios.put(`${API_URL}/api/labor/certifications/${id}`, data);
  return response.data;
};

export const deleteWorkerCertification = async (id: number) => {
  const response = await axios.delete(`${API_URL}/api/labor/certifications/${id}`);
  return response.data;
};

// Document Upload for Compliance Records
export const uploadComplianceDocument = async (farmId: number, file: File) => {
  const formData = new FormData();
  formData.append('file', file);

  const response = await axios.post(
    `${API_URL}/documents/farm/${farmId}/upload`,
    formData,
    {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    }
  );

  return response.data.document;
};

// Document Upload for Worker Certifications
export const uploadCertificationDocument = async (farmId: number, file: File) => {
  const formData = new FormData();
  formData.append('file', file);

  const response = await axios.post(
    `${API_URL}/documents/farm/${farmId}/upload`,
    formData,
    {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    }
  );

  return response.data.document;
};
