import axios from 'axios';
import { API_URL } from '../config';

export interface CartItem {
  id: string;
  cart_id: string;
  product_id: string;
  quantity: number;
  created_at: string;
  updated_at: string;
  product: {
    id: string;
    name: string;
    description: string;
    price: number;
    farm_id: string;
    images?: {
      id: string;
      file_path: string;
      is_primary: boolean;
    }[];
  };
}

export interface ShoppingCart {
  id: string;
  user_id: string | null;
  session_id: string | null;
  farm_id: string;
  is_saved: boolean;
  created_at: string;
  updated_at: string;
  items: CartItem[];
  farm: {
    id: string;
    name: string;
    logo_url: string | null;
    subdomain: string;
  };
  itemCount: number;
  totalPrice: number;
}

// Get current cart
export const getCurrentCart = async (farmId: string): Promise<ShoppingCart> => {
  try {
    const response = await axios.get(`${API_URL}/marketplace/cart`, {
      params: { farmId },
      withCredentials: true
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching cart:', error);
    throw error;
  }
};

// Get all carts for the current user
export const getAllCarts = async (): Promise<ShoppingCart[]> => {
  try {
    const response = await axios.get(`${API_URL}/marketplace/cart/all`, {
      withCredentials: true
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching all carts:', error);
    throw error;
  }
};

// Add item to cart
export const addItemToCart = async (
  productId: string,
  farmId: string,
  quantity: number = 1
): Promise<ShoppingCart> => {
  try {
    const response = await axios.post(
      `${API_URL}/marketplace/cart/items`,
      { productId, farmId, quantity },
      { withCredentials: true }
    );
    return response.data;
  } catch (error) {
    console.error('Error adding item to cart:', error);
    throw error;
  }
};

// Update cart item quantity
export const updateCartItemQuantity = async (
  itemId: string,
  quantity: number
): Promise<ShoppingCart> => {
  try {
    const response = await axios.put(
      `${API_URL}/marketplace/cart/items/${itemId}`,
      { quantity },
      { withCredentials: true }
    );
    return response.data;
  } catch (error) {
    console.error('Error updating cart item:', error);
    throw error;
  }
};

// Remove item from cart
export const removeCartItem = async (itemId: string): Promise<ShoppingCart> => {
  try {
    const response = await axios.delete(
      `${API_URL}/marketplace/cart/items/${itemId}`,
      { withCredentials: true }
    );
    return response.data;
  } catch (error) {
    console.error('Error removing cart item:', error);
    throw error;
  }
};

// Save cart for later
export const saveCartForLater = async (cartId: string): Promise<{ message: string; cart: ShoppingCart }> => {
  try {
    const response = await axios.post(
      `${API_URL}/marketplace/cart/${cartId}/save`,
      {},
      { withCredentials: true }
    );
    return response.data;
  } catch (error) {
    console.error('Error saving cart:', error);
    throw error;
  }
};

// Checkout
export const checkout = async (
  cartId: string,
  notes?: string
): Promise<{ message: string; purchaseRequest: any }> => {
  try {
    const response = await axios.post(
      `${API_URL}/marketplace/cart/checkout`,
      { cartId, notes },
      { withCredentials: true }
    );
    return response.data;
  } catch (error) {
    console.error('Error checking out:', error);
    throw error;
  }
};
