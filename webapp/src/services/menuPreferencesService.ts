import axios from 'axios';
import { API_URL } from '../config';
import { MenuItem, MenuCategory, MenuPreferences, getMockMenuPreferences as getDefaultMenuPreferences } from '../utils/menuUtils';

/**
 * Get default menu structure
 */
export const getDefaultMenuStructure = async (): Promise<MenuPreferences> => {
  try {
    const response = await axios.get(`${API_URL}/menu/defaults`);
    return response.data;
  } catch (error) {
    console.error('Error fetching default menu structure:', error);
    throw error;
  }
};

/**
 * Get user's menu preferences
 */
export const getUserMenuPreferences = async (userId: string): Promise<MenuPreferences> => {
  try {
    const response = await axios.get(`${API_URL}/menu/preferences/${userId}`);
    return response.data;
  } catch (error) {
    console.error('Error fetching user menu preferences:', error);
    throw error;
  }
};

/**
 * Save user's menu preferences
 */
export const saveUserMenuPreferences = async (preferences: MenuPreferences): Promise<MenuPreferences> => {
  try {
    const response = await axios.post(`${API_URL}/menu/preferences`, preferences);
    return response.data;
  } catch (error) {
    console.error('Error saving user menu preferences:', error);
    throw error;
  }
};

/**
 * Reset user's menu preferences to defaults
 */
export const resetUserMenuPreferences = async (userId: string): Promise<MenuPreferences> => {
  try {
    const response = await axios.delete(`${API_URL}/menu/preferences/${userId}`);
    return response.data;
  } catch (error) {
    console.error('Error resetting user menu preferences:', error);
    throw error;
  }
};

/**
 * Get menu preferences for the current user
 * @param userId - The ID of the user to get menu preferences for
 * @returns Promise<MenuPreferences> - The user's menu preferences
 */
export const getMockMenuPreferences = async (userId: string): Promise<MenuPreferences> => {
  try {
    // Call the real API endpoint to get user's menu preferences
    return await getUserMenuPreferences(userId);
  } catch (error) {
    console.error('Error fetching user menu preferences, falling back to defaults:', error);
    // Fallback to default preferences if API call fails
    return getDefaultMenuPreferences();
  }
};
