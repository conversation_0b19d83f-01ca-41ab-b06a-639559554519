import axios from 'axios';
import { API_URL } from '../config';
import { handleApiError } from '../utils/errorHandler';
import { getAuthToken } from '../utils/storageUtils';

// Define Delivery interface
export interface Delivery {
  id: string;
  deliveryDate: string;
  status: string;
  destination: string;
  recipientName: string;
  recipientContact: string;
  driverId: string;
  driverName: string;
  products: Array<{
    id: string;
    name: string;
    quantity: number;
    unit: string;
  }>;
  notes: string;
  trackingNumber: string;
  estimatedArrival: string;
  farmId: string;
  tenantId: string;
  createdAt: string;
  updatedAt: string;
}

// Get all deliveries for a farm
export const getDeliveries = async (
  farmId: string,
  params: {
    search?: string;
    status?: string;
    startDate?: string;
    endDate?: string;
    driverId?: string;
    page?: number;
    limit?: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  } = {}
) => {
  try {
    const token = getAuthToken();

    // Build query string
    const queryParams = new URLSearchParams();
    queryParams.append('farmId', farmId);

    if (params.search) queryParams.append('search', params.search);
    if (params.status) queryParams.append('status', params.status);
    if (params.startDate) queryParams.append('startDate', params.startDate);
    if (params.endDate) queryParams.append('endDate', params.endDate);
    if (params.driverId) queryParams.append('driverId', params.driverId);
    if (params.page) queryParams.append('page', params.page.toString());
    if (params.limit) queryParams.append('limit', params.limit.toString());
    if (params.sortBy) queryParams.append('sortBy', params.sortBy);
    if (params.sortOrder) queryParams.append('sortOrder', params.sortOrder);

    const queryString = queryParams.toString();
    const url = `${API_URL}/deliveries${queryString ? `?${queryString}` : ''}`;

    const response = await axios.get(url, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });

    return Array.isArray(response.data) ? response.data : response.data.deliveries || [];
  } catch (error: unknown) {
    console.error('Error fetching deliveries:', error);

    // Use the error handler to get a structured error
    const structuredError = handleApiError(error, undefined, 'Failed to fetch deliveries');

    // Create a custom error with the structured information
    const customError = new Error(structuredError.message);
    (customError as any).structuredError = structuredError;

    throw customError;
  }
};

// Get delivery by ID
export const getDeliveryById = async (id: string) => {
  try {
    const token = getAuthToken();

    const response = await axios.get(`${API_URL}/deliveries/${id}`, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });

    return response.data;
  } catch (error: unknown) {
    console.error('Error fetching delivery:', error);

    // Use the error handler to get a structured error
    const structuredError = handleApiError(error, undefined, 'Failed to fetch delivery details');

    // Create a custom error with the structured information
    const customError = new Error(structuredError.message);
    (customError as any).structuredError = structuredError;

    throw customError;
  }
};

// Create a new delivery
export const createDelivery = async (
  data: {
    deliveryDate: string;
    status: string;
    destination: string;
    recipientName: string;
    recipientContact?: string;
    driverId?: string;
    products?: Array<{
      id: string;
      name: string;
      quantity: number;
      unit: string;
    }>;
    notes?: string;
    trackingNumber?: string;
    estimatedArrival?: string;
    farmId: string;
  }
) => {
  try {
    const token = getAuthToken();

    const response = await axios.post(`${API_URL}/deliveries`, data, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });

    return response.data;
  } catch (error: unknown) {
    console.error('Error creating delivery:', error);

    // Use the error handler to get a structured error
    const structuredError = handleApiError(error, undefined, 'Failed to create delivery');

    // Create a custom error with the structured information
    const customError = new Error(structuredError.message);
    (customError as any).structuredError = structuredError;

    throw customError;
  }
};

// Update a delivery
export const updateDelivery = async (
  id: string,
  data: {
    deliveryDate?: string;
    status?: string;
    destination?: string;
    recipientName?: string;
    recipientContact?: string;
    driverId?: string;
    products?: Array<{
      id: string;
      name: string;
      quantity: number;
      unit: string;
    }>;
    notes?: string;
    trackingNumber?: string;
    estimatedArrival?: string;
  }
) => {
  try {
    const token = getAuthToken();

    const response = await axios.put(`${API_URL}/deliveries/${id}`, data, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });

    return response.data;
  } catch (error: unknown) {
    console.error('Error updating delivery:', error);

    // Use the error handler to get a structured error
    const structuredError = handleApiError(error, undefined, 'Failed to update delivery');

    // Create a custom error with the structured information
    const customError = new Error(structuredError.message);
    (customError as any).structuredError = structuredError;

    throw customError;
  }
};

// Delete a delivery
export const deleteDelivery = async (id: string) => {
  try {
    const token = getAuthToken();

    const response = await axios.delete(`${API_URL}/deliveries/${id}`, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });

    return response.data;
  } catch (error: unknown) {
    console.error('Error deleting delivery:', error);

    // Use the error handler to get a structured error
    const structuredError = handleApiError(error, undefined, 'Failed to delete delivery');

    // Create a custom error with the structured information
    const customError = new Error(structuredError.message);
    (customError as any).structuredError = structuredError;

    throw customError;
  }
};

// Update delivery status
export const updateDeliveryStatus = async (
  id: string,
  status: string
) => {
  try {
    const token = getAuthToken();

    const response = await axios.patch(`${API_URL}/deliveries/${id}/status`, { status }, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });

    return response.data;
  } catch (error: unknown) {
    console.error('Error updating delivery status:', error);

    // Use the error handler to get a structured error
    const structuredError = handleApiError(error, undefined, 'Failed to update delivery status');

    // Create a custom error with the structured information
    const customError = new Error(structuredError.message);
    (customError as any).structuredError = structuredError;

    throw customError;
  }
};

// Format date for display
export const formatDate = (dateString: string | null) => {
  if (!dateString) return 'N/A';
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
};