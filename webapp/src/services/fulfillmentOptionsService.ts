import axios from 'axios';
import { API_URL } from '../config';

export interface FarmFulfillmentOptions {
  farm_id: string;
  offers_delivery: boolean;
  offers_pickup: boolean;
  delivery_fee: number;
  min_order_for_free_delivery: number | null;
  delivery_radius_miles: number | null;
  pickup_instructions: string | null;
  delivery_instructions: string | null;
}

export interface ProductFulfillmentOptions {
  product_id: string;
  farm_id: string;
  override_farm_fulfillment: boolean;
  offers_delivery: boolean;
  offers_pickup: boolean;
  farm_fulfillment_options: FarmFulfillmentOptions;
}

export const getProductFulfillmentOptions = async (productId: string): Promise<ProductFulfillmentOptions> => {
  try {
    const response = await axios.get(`${API_URL}/products/${productId}/fulfillment-options`);
    return response.data;
  } catch (error) {
    console.error('Error fetching product fulfillment options:', error);
    throw error;
  }
};

export const updateProductFulfillmentOptions = async (
  productId: string,
  options: {
    override_farm_fulfillment: boolean;
    offers_delivery?: boolean;
    offers_pickup?: boolean;
  }
): Promise<ProductFulfillmentOptions> => {
  try {
    const response = await axios.put(`${API_URL}/products/${productId}/fulfillment-options`, options);
    return response.data;
  } catch (error) {
    console.error('Error updating product fulfillment options:', error);
    throw error;
  }
};

export const getFarmFulfillmentOptions = async (farmId: string): Promise<FarmFulfillmentOptions> => {
  try {
    const response = await axios.get(`${API_URL}/farms/${farmId}/fulfillment-options`);
    return response.data;
  } catch (error) {
    console.error('Error fetching farm fulfillment options:', error);
    throw error;
  }
};

export const updateFarmFulfillmentOptions = async (
  farmId: string,
  options: {
    offers_delivery: boolean;
    offers_pickup: boolean;
    delivery_fee: number;
    min_order_for_free_delivery: number | null;
    delivery_radius_miles: number | null;
    pickup_instructions: string | null;
    delivery_instructions: string | null;
  }
): Promise<FarmFulfillmentOptions> => {
  try {
    const response = await axios.put(`${API_URL}/farms/${farmId}/fulfillment-options`, options);
    return response.data;
  } catch (error) {
    console.error('Error updating farm fulfillment options:', error);
    throw error;
  }
};
