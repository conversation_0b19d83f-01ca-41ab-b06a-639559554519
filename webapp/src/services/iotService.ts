import axios from 'axios';
import { API_URL } from '../config';

export interface IoTDevice {
  id: string;
  farm_id: string;
  name: string;
  device_type: string;
  manufacturer: string | null;
  model: string | null;
  serial_number: string | null;
  firmware_version: string | null;
  location_description: string | null;
  latitude: number | null;
  longitude: number | null;
  status: string;
  last_communication: string | null;
  battery_level: number | null;
  signal_strength: number | null;
  configuration: any | null;
  created_at: string;
  updated_at: string;
}

export interface IoTData {
  id: string;
  device_id: string;
  timestamp: string;
  latitude: number | null;
  longitude: number | null;
  altitude: number | null;
  temperature: number | null;
  humidity: number | null;
  pressure: number | null;
  soil_moisture: number | null;
  soil_temperature: number | null;
  light_level: number | null;
  battery_level: number | null;
  signal_strength: number | null;
  status: string | null;
  raw_data: any | null;
  custom_fields: any | null;
  created_at: string;
  updated_at: string;
}

export interface AggregatedIoTData {
  time_period: string;
  avg_value: number;
  min_value: number;
  max_value: number;
  data_points: number;
}

/**
 * Get all IoT devices for a farm
 */
export const getFarmIoTDevices = async (farmId: string): Promise<{ devices: IoTDevice[] }> => {
  try {
    const response = await axios.get(`${API_URL}/iot/farm/${farmId}`);
    return response.data;
  } catch (error) {
    console.error('Error fetching farm IoT devices:', error);
    throw error;
  }
};

/**
 * Get a specific IoT device
 */
export const getIoTDevice = async (deviceId: string): Promise<{ device: IoTDevice }> => {
  try {
    const response = await axios.get(`${API_URL}/iot/device/${deviceId}`);
    return response.data;
  } catch (error) {
    console.error('Error fetching IoT device:', error);
    throw error;
  }
};

/**
 * Create a new IoT device
 */
export const createIoTDevice = async (data: {
  farmId: string;
  name: string;
  deviceType: string;
  manufacturer?: string;
  model?: string;
  serialNumber?: string;
  firmwareVersion?: string;
  locationDescription?: string;
  latitude?: number;
  longitude?: number;
  status?: string;
  configuration?: any;
}): Promise<{ message: string; device: IoTDevice }> => {
  try {
    const response = await axios.post(`${API_URL}/iot/device`, data);
    return response.data;
  } catch (error) {
    console.error('Error creating IoT device:', error);
    throw error;
  }
};

/**
 * Update an IoT device
 */
export const updateIoTDevice = async (
  deviceId: string,
  data: {
    name?: string;
    deviceType?: string;
    manufacturer?: string;
    model?: string;
    serialNumber?: string;
    firmwareVersion?: string;
    locationDescription?: string;
    latitude?: number;
    longitude?: number;
    status?: string;
    configuration?: any;
  }
): Promise<{ message: string; device: IoTDevice }> => {
  try {
    const response = await axios.put(`${API_URL}/iot/device/${deviceId}`, data);
    return response.data;
  } catch (error) {
    console.error('Error updating IoT device:', error);
    throw error;
  }
};

/**
 * Delete an IoT device
 */
export const deleteIoTDevice = async (deviceId: string): Promise<{ message: string }> => {
  try {
    const response = await axios.delete(`${API_URL}/iot/device/${deviceId}`);
    return response.data;
  } catch (error) {
    console.error('Error deleting IoT device:', error);
    throw error;
  }
};

/**
 * Get IoT data for a specific device
 */
export const getDeviceIoTData = async (
  deviceId: string,
  startDate?: string,
  endDate?: string,
  limit: number = 100,
  offset: number = 0
): Promise<{ iotData: IoTData[]; total: number; limit: number; offset: number }> => {
  try {
    let url = `${API_URL}/iot/device/${deviceId}/data?limit=${limit}&offset=${offset}`;

    if (startDate) {
      url += `&startDate=${startDate}`;
    }

    if (endDate) {
      url += `&endDate=${endDate}`;
    }

    const response = await axios.get(url);
    return response.data;
  } catch (error) {
    console.error('Error fetching device IoT data:', error);
    throw error;
  }
};

/**
 * Get the latest IoT data for a device
 */
export const getLatestIoTData = async (deviceId: string): Promise<{ iotData: IoTData }> => {
  try {
    const response = await axios.get(`${API_URL}/iot/device/${deviceId}/data/latest`);
    return response.data;
  } catch (error) {
    console.error('Error fetching latest IoT data:', error);
    throw error;
  }
};

/**
 * Create new IoT data record
 */
export const createIoTData = async (data: {
  deviceId: string;
  timestamp?: string;
  latitude?: number;
  longitude?: number;
  altitude?: number;
  temperature?: number;
  humidity?: number;
  pressure?: number;
  soilMoisture?: number;
  soilTemperature?: number;
  lightLevel?: number;
  batteryLevel?: number;
  signalStrength?: number;
  status?: string;
  rawData?: any;
  customFields?: any;
}): Promise<{ message: string; iotData: IoTData }> => {
  try {
    const response = await axios.post(`${API_URL}/iot/data`, data);
    return response.data;
  } catch (error) {
    console.error('Error creating IoT data:', error);
    throw error;
  }
};

/**
 * Get aggregated IoT data for reporting
 */
export const getAggregatedIoTData = async (
  deviceId: string,
  startDate: string,
  endDate: string,
  metric: string,
  interval: 'hour' | 'day' | 'week' | 'month' = 'day'
): Promise<{ metric: string; aggregatedData: AggregatedIoTData[] }> => {
  try {
    const response = await axios.get(
      `${API_URL}/iot/device/${deviceId}/data/aggregated?startDate=${startDate}&endDate=${endDate}&metric=${metric}&interval=${interval}`
    );
    return response.data;
  } catch (error) {
    console.error('Error fetching aggregated IoT data:', error);
    throw error;
  }
};

/**
 * Send IoT data from external systems
 */
export const sendExternalIoTData = async (data: {
  apiKey: string;
  deviceIdentifier: string;
  iotData: any;
}): Promise<{ message: string; iotDataId: string }> => {
  try {
    const response = await axios.post(`${API_URL}/iot/data/external`, data);
    return response.data;
  } catch (error) {
    console.error('Error sending external IoT data:', error);
    throw error;
  }
};
