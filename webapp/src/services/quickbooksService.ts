import axios from 'axios';
import { API_URL } from '../config';

// Types for QuickBooks data
export interface QuickBooksConnection {
  id: string;
  farm_id: string;
  realm_id: string;
  company_name: string;
  status: 'active' | 'inactive' | 'error';
  last_sync_at: string;
  created_at: string;
  updated_at: string;
}

export interface QuickBooksAccount {
  Id: string;
  Name: string;
  AccountType: string;
  AccountSubType: string;
  Classification: string;
  CurrentBalance: number;
  Active: boolean;
}

export interface QuickBooksCustomer {
  Id: string;
  DisplayName: string;
  CompanyName?: string;
  GivenName?: string;
  FamilyName?: string;
  Active: boolean;
  Balance: number;
}

export interface QuickBooksVendor {
  Id: string;
  DisplayName: string;
  CompanyName?: string;
  GivenName?: string;
  FamilyName?: string;
  Active: boolean;
  Balance: number;
}

export interface QuickBooksItem {
  Id: string;
  Name: string;
  Description?: string;
  Type: string;
  Active: boolean;
  UnitPrice?: number;
  PurchaseCost?: number;
}

export interface QuickBooksInvoice {
  Id: string;
  DocNumber?: string;
  CustomerRef: {
    value: string;
    name: string;
  };
  TxnDate: string;
  DueDate?: string;
  TotalAmt: number;
  Balance: number;
}

export interface QuickBooksBill {
  Id: string;
  DocNumber?: string;
  VendorRef: {
    value: string;
    name: string;
  };
  TxnDate: string;
  DueDate?: string;
  TotalAmt: number;
  Balance: number;
}

/**
 * Get authorization URL for QuickBooks OAuth
 * @param farmId The ID of the farm to connect QuickBooks to
 * @returns Promise<{ authUrl: string }> The authorization URL
 */
export const getAuthorizationUrl = async (farmId: string): Promise<{ authUrl: string }> => {
  try {
    const response = await axios.get(`${API_URL}/quickbooks/auth`, {
      params: { farmId }
    });
    return response.data;
  } catch (error) {
    console.error('Error getting QuickBooks authorization URL:', error);
    throw new Error('Failed to get QuickBooks authorization URL');
  }
};

/**
 * Get QuickBooks connection for a farm
 * @param farmId The ID of the farm
 * @returns Promise<{ connection: QuickBooksConnection }> The QuickBooks connection
 */
export const getConnection = async (farmId: string): Promise<{ connection: QuickBooksConnection }> => {
  try {
    const response = await axios.get(`${API_URL}/quickbooks/connection/${farmId}`);
    return response.data;
  } catch (error) {
    console.error('Error getting QuickBooks connection:', error);
    throw new Error('Failed to get QuickBooks connection');
  }
};

/**
 * Disconnect QuickBooks
 * @param connectionId The ID of the QuickBooks connection
 * @returns Promise<{ success: boolean; message: string }> Success message
 */
export const disconnectQuickBooks = async (connectionId: string): Promise<{ success: boolean; message: string }> => {
  try {
    const response = await axios.delete(`${API_URL}/quickbooks/connection/${connectionId}`);
    return response.data;
  } catch (error) {
    console.error('Error disconnecting QuickBooks:', error);
    throw new Error('Failed to disconnect QuickBooks');
  }
};

/**
 * Sync accounts from QuickBooks
 * @param connectionId The ID of the QuickBooks connection
 * @returns Promise<{ success: boolean; accounts: QuickBooksAccount[] }> The synced accounts
 */
export const syncAccounts = async (connectionId: string): Promise<{ success: boolean; accounts: QuickBooksAccount[] }> => {
  try {
    const response = await axios.get(`${API_URL}/quickbooks/sync/accounts/${connectionId}`);
    return response.data;
  } catch (error) {
    console.error('Error syncing QuickBooks accounts:', error);
    throw new Error('Failed to sync QuickBooks accounts');
  }
};

/**
 * Import data from QuickBooks
 * @param connectionId The ID of the QuickBooks connection
 * @param dataType The type of data to import (accounts, customers, vendors, items, invoices, bills, transactions)
 * @param startDate Optional start date for filtering data
 * @param endDate Optional end date for filtering data
 * @returns Promise<{ success: boolean; data: any[] }> The imported data
 */
export const importData = async (
  connectionId: string,
  dataType: 'accounts' | 'customers' | 'vendors' | 'items' | 'invoices' | 'bills' | 'transactions' | 'payments' | 'taxdocuments',
  startDate?: string,
  endDate?: string,
  itemId?: string
): Promise<{ success: boolean; data: any[] }> => {
  try {
    const params: any = {};
    if (startDate) params.startDate = startDate;
    if (endDate) params.endDate = endDate;
    if (itemId) params.itemId = itemId;

    const response = await axios.get(`${API_URL}/quickbooks/import/${connectionId}/${dataType}`, { params });
    return response.data;
  } catch (error) {
    console.error(`Error importing QuickBooks ${dataType}:`, error);
    throw new Error(`Failed to import QuickBooks ${dataType}`);
  }
};

/**
 * Update an item in QuickBooks
 * @param connectionId The ID of the QuickBooks connection
 * @param dataType The type of data to update (accounts, customers, vendors, items, invoices, bills, payments, taxdocuments)
 * @param itemId The ID of the item to update
 * @param itemData The updated item data
 * @returns Promise<{ success: boolean; data: any }> The updated item
 */
export const updateItem = async (
  connectionId: string,
  dataType: 'accounts' | 'customers' | 'vendors' | 'items' | 'invoices' | 'bills' | 'payments' | 'taxdocuments',
  itemId: string,
  itemData: any
): Promise<{ success: boolean; data: any }> => {
  try {
    const response = await axios.post(`${API_URL}/quickbooks/update/${connectionId}/${dataType}/${itemId}`, itemData);
    return response.data;
  } catch (error) {
    console.error(`Error updating QuickBooks ${dataType}:`, error);
    throw new Error(`Failed to update QuickBooks ${dataType}`);
  }
};
