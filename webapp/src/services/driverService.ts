import axios from 'axios';
import { API_URL } from '../config';
import { handleApiError } from '../utils/errorHandler';
import { getAuthToken } from '../utils/storageUtils';

// Define Driver interface
export interface Driver {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber: string;
  licenseNumber: string;
  licenseExpiry: string;
  vehicleType: string;
  vehiclePlate: string;
  status: string;
  notes: string;
  farmId: string;
  tenantId: string;
  createdAt: string;
  updatedAt: string;
}

// Get all drivers for a farm
export const getDrivers = async (
  farmId: string,
  params: {
    search?: string;
    status?: string;
    page?: number;
    limit?: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  } = {}
) => {
  try {
    const token = getAuthToken();

    // Build query string
    const queryParams = new URLSearchParams();
    queryParams.append('farmId', farmId);

    if (params.search) queryParams.append('search', params.search);
    if (params.status) queryParams.append('status', params.status);
    if (params.page) queryParams.append('page', params.page.toString());
    if (params.limit) queryParams.append('limit', params.limit.toString());
    if (params.sortBy) queryParams.append('sortBy', params.sortBy);
    if (params.sortOrder) queryParams.append('sortOrder', params.sortOrder);

    const queryString = queryParams.toString();
    const url = `${API_URL}/drivers${queryString ? `?${queryString}` : ''}`;

    const response = await axios.get(url, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });

    return Array.isArray(response.data) ? response.data : response.data.drivers || [];
  } catch (error: unknown) {
    console.error('Error fetching drivers:', error);

    // Use the error handler to get a structured error
    const structuredError = handleApiError(error, undefined, 'Failed to fetch drivers');

    // Create a custom error with the structured information
    const customError = new Error(structuredError.message);
    (customError as any).structuredError = structuredError;

    throw customError;
  }
};

// Get driver by ID
export const getDriverById = async (id: string) => {
  try {
    const token = getAuthToken();

    const response = await axios.get(`${API_URL}/drivers/${id}`, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });

    return response.data;
  } catch (error: unknown) {
    console.error('Error fetching driver:', error);

    // Use the error handler to get a structured error
    const structuredError = handleApiError(error, undefined, 'Failed to fetch driver details');

    // Create a custom error with the structured information
    const customError = new Error(structuredError.message);
    (customError as any).structuredError = structuredError;

    throw customError;
  }
};

// Create a new driver
export const createDriver = async (
  data: {
    firstName: string;
    lastName: string;
    email?: string;
    phoneNumber?: string;
    licenseNumber?: string;
    licenseExpiry?: string;
    vehicleType?: string;
    vehiclePlate?: string;
    status?: string;
    notes?: string;
    farmId: string;
  }
) => {
  try {
    const token = getAuthToken();

    const response = await axios.post(`${API_URL}/drivers`, data, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });

    return response.data;
  } catch (error: unknown) {
    console.error('Error creating driver:', error);

    // Use the error handler to get a structured error
    const structuredError = handleApiError(error, undefined, 'Failed to create driver');

    // Create a custom error with the structured information
    const customError = new Error(structuredError.message);
    (customError as any).structuredError = structuredError;

    throw customError;
  }
};

// Update a driver
export const updateDriver = async (
  id: string,
  data: {
    firstName?: string;
    lastName?: string;
    email?: string;
    phoneNumber?: string;
    licenseNumber?: string;
    licenseExpiry?: string;
    vehicleType?: string;
    vehiclePlate?: string;
    status?: string;
    notes?: string;
  }
) => {
  try {
    const token = getAuthToken();

    const response = await axios.put(`${API_URL}/drivers/${id}`, data, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });

    return response.data;
  } catch (error: unknown) {
    console.error('Error updating driver:', error);

    // Use the error handler to get a structured error
    const structuredError = handleApiError(error, undefined, 'Failed to update driver');

    // Create a custom error with the structured information
    const customError = new Error(structuredError.message);
    (customError as any).structuredError = structuredError;

    throw customError;
  }
};

// Delete a driver
export const deleteDriver = async (id: string) => {
  try {
    const token = getAuthToken();

    const response = await axios.delete(`${API_URL}/drivers/${id}`, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });

    return response.data;
  } catch (error: unknown) {
    console.error('Error deleting driver:', error);

    // Use the error handler to get a structured error
    const structuredError = handleApiError(error, undefined, 'Failed to delete driver');

    // Create a custom error with the structured information
    const customError = new Error(structuredError.message);
    (customError as any).structuredError = structuredError;

    throw customError;
  }
};

// Format date for display
export const formatDate = (dateString: string | null) => {
  if (!dateString) return 'N/A';
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
};