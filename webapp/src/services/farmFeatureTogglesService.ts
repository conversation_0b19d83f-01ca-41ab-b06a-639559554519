import axios from 'axios';
import { API_URL } from '../config';

export interface FarmFeatureToggles {
  id: string;
  farm_id: string;
  marketplace_enabled: boolean;
  customer_orders_enabled: boolean;
  delivery_scheduling_enabled: boolean;
  driver_tracking_enabled: boolean;
  customer_messaging_enabled: boolean;
  created_at: string;
  updated_at: string;
}

export interface FarmFeatureSettings {
  id?: string;
  farm_id: string;
  marketplace_default_visibility?: boolean;
  marketplace_default_category?: string;
  customer_order_auto_approval?: boolean;
  delivery_scheduling_advance_days?: number;
  delivery_scheduling_blackout_dates?: string[];
  driver_tracking_update_frequency?: number;
  customer_messaging_auto_response?: string;
  created_at?: string;
  updated_at?: string;
}

/**
 * Get feature toggles for a farm
 * @param farmId - The ID of the farm
 * @returns Promise with the farm feature toggles
 */
export const getFarmFeatureToggles = async (farmId: string): Promise<FarmFeatureToggles> => {
  try {
    const response = await axios.get(`${API_URL}/farms/${farmId}/feature-toggles`);
    return response.data;
  } catch (error) {
    console.error('Error getting farm feature toggles:', error);
    throw error;
  }
};

/**
 * Update feature toggles for a farm
 * @param farmId - The ID of the farm
 * @param featureToggles - The feature toggles to update
 * @returns Promise with the updated farm feature toggles
 */
export const updateFarmFeatureToggles = async (
  farmId: string,
  featureToggles: Partial<FarmFeatureToggles>
): Promise<FarmFeatureToggles> => {
  try {
    const response = await axios.put(`${API_URL}/farms/${farmId}/feature-toggles`, featureToggles);
    return response.data;
  } catch (error) {
    console.error('Error updating farm feature toggles:', error);
    throw error;
  }
};

/**
 * Get feature settings for a farm
 * @param farmId - The ID of the farm
 * @returns Promise with the farm feature settings
 */
export const getFarmFeatureSettings = async (farmId: string): Promise<FarmFeatureSettings> => {
  try {
    const response = await axios.get(`${API_URL}/farms/${farmId}/feature-settings`);
    return response.data;
  } catch (error) {
    console.error('Error getting farm feature settings:', error);
    throw error;
  }
};

/**
 * Update feature settings for a farm
 * @param farmId - The ID of the farm
 * @param featureSettings - The feature settings to update
 * @returns Promise with the updated farm feature settings
 */
export const updateFarmFeatureSettings = async (
  farmId: string,
  featureSettings: Partial<FarmFeatureSettings>
): Promise<FarmFeatureSettings> => {
  try {
    const response = await axios.put(`${API_URL}/farms/${farmId}/feature-settings`, featureSettings);
    return response.data;
  } catch (error) {
    console.error('Error updating farm feature settings:', error);
    throw error;
  }
};
