declare module 'recharts' {
  import { ComponentType, ReactNode } from 'react';

  export interface LineChartProps {
    width?: number;
    height?: number;
    data?: any[];
    margin?: { top?: number; right?: number; bottom?: number; left?: number };
    children?: ReactNode;
    [key: string]: any;
  }

  export interface AreaChartProps {
    width?: number;
    height?: number;
    data?: any[];
    margin?: { top?: number; right?: number; bottom?: number; left?: number };
    children?: ReactNode;
    [key: string]: any;
  }

  export interface XAxisProps {
    dataKey?: string;
    domain?: [number | string, number | string];
    name?: string;
    tickFormatter?: (value: any) => string;
    [key: string]: any;
  }

  export interface YAxisProps {
    dataKey?: string;
    domain?: [number | string, number | string];
    name?: string;
    tickFormatter?: (value: any) => string;
    [key: string]: any;
  }

  export interface TooltipProps {
    formatter?: (value: any, name: string, props: any) => ReactNode;
    labelFormatter?: (label: any) => ReactNode;
    [key: string]: any;
  }

  export interface CartesianGridProps {
    strokeDasharray?: string;
    [key: string]: any;
  }

  export interface LegendProps {
    [key: string]: any;
  }

  export interface AreaProps {
    type?: string;
    dataKey: string;
    stroke?: string;
    fill?: string;
    [key: string]: any;
  }

  export interface LineProps {
    type?: string;
    dataKey: string;
    stroke?: string;
    [key: string]: any;
  }

  export const LineChart: ComponentType<LineChartProps>;
  export const AreaChart: ComponentType<AreaChartProps>;
  export const XAxis: ComponentType<XAxisProps>;
  export const YAxis: ComponentType<YAxisProps>;
  export const Tooltip: ComponentType<TooltipProps>;
  export const CartesianGrid: ComponentType<CartesianGridProps>;
  export const Legend: ComponentType<LegendProps>;
  export const Area: ComponentType<AreaProps>;
  export const Line: ComponentType<LineProps>;
  export const ResponsiveContainer: ComponentType<{ width?: string | number; height?: string | number; children?: ReactNode; [key: string]: any; }>;
}
