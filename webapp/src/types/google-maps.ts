// Google Maps type definitions
declare global {
  interface Window {
    google: {
      maps: {
        places: {
          Autocomplete: new (input: HTMLInputElement, options?: any) => { 
            addListener: (event: string, callback: () => void) => void; 
            getPlace: () => { 
              geometry?: any; 
              formatted_address?: string; 
              address_components?: Array<{
                long_name: string;
                short_name: string;
                types: string[];
              }>; 
            }; 
          };
          PlaceAutocompleteElement: new (options?: any) => HTMLElement & {
            value: string;
            addEventListener: (event: string, callback: (event: { place: any }) => void) => void;
          };
        };
        Geocoder: new () => google.maps.Geocoder;
        GeocoderStatus: {
          OK: google.maps.GeocoderStatus;
        };
      };
    };
  }
}

export {};