// Test script to verify that widgets are correctly loaded from plugins

// Import the dynamic loading function
const loadPluginWidgets = async () => {
  const widgets = {};
  
  try {
    // Load the field health widget
    console.log('Loading Field Health widget...');
    const fieldHealthModule = await import('../plugins/field-health-analytics');
    if (fieldHealthModule && fieldHealthModule.FieldHealthWidget) {
      widgets.FieldHealthWidget = fieldHealthModule.FieldHealthWidget;
      console.log('Field Health widget loaded successfully');
    } else {
      console.log('Field Health widget not found in the module');
    }
  } catch (error) {
    console.error('Error loading Field Health widget:', error);
  }

  try {
    // Load the market price widget
    console.log('Loading Market Price widget...');
    const marketPriceModule = await import('../plugins/market-price-tracker');
    if (marketPriceModule && marketPriceModule.MarketPriceWidget) {
      widgets.MarketPriceWidget = marketPriceModule.MarketPriceWidget;
      console.log('Market Price widget loaded successfully');
    } else {
      console.log('Market Price widget not found in the module');
    }
  } catch (error) {
    console.error('Error loading Market Price widget:', error);
  }
  
  return widgets;
};

// Run the test
const runTest = async () => {
  console.log('Starting plugin widget test...');
  
  // Load the widgets
  const widgets = await loadPluginWidgets();
  
  // Check if widgets were loaded
  console.log('Test results:');
  console.log('Field Health widget:', widgets.FieldHealthWidget ? 'Loaded' : 'Not loaded');
  console.log('Market Price widget:', widgets.MarketPriceWidget ? 'Loaded' : 'Not loaded');
  
  // Summary
  if (widgets.FieldHealthWidget && widgets.MarketPriceWidget) {
    console.log('All plugin widgets loaded successfully!');
  } else {
    console.log('Some plugin widgets failed to load.');
  }
};

// Execute the test
runTest().catch(error => {
  console.error('Test failed with error:', error);
});

// To run this test:
// 1. Make sure the plugins are properly set up
// 2. Run this script with Node.js
// Expected output: Both widgets should be loaded successfully