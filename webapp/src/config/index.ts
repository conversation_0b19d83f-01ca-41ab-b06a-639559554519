// Constants used throughout the application

// Helper function to get environment variables from import.meta.env
const getEnv = (key: string, defaultValue: string = ''): string => {
  return import.meta.env[key] || defaultValue;
};

// API base URL
export const API_URL = getEnv('VITE_API_URL', 'https://api.nxtacre.com');

// Main domain for subdomains
export const MAIN_DOMAIN = getEnv('VITE_MAIN_DOMAIN', 'nxtacre.com');

// Google Maps API key
export const GOOGLE_MAPS_API_KEY = getEnv('VITE_GOOGLE_MAPS_API_KEY', 'AIzaSyAhcNcr9eb9-ghjFFIeo8FRz_iUOJnAUyM');

// Matrix configuration
export const MATRIX_SERVER_URL = getEnv('VITE_MATRIX_SERVER_URL', 'https://chat.nxtacre.com');
export const MATRIX_DOMAIN = getEnv('VITE_MATRIX_DOMAIN', 'chat.nxtacre.com');

// Stripe configuration
export const STRIPE_PUBLISHABLE_KEY = getEnv('VITE_REACT_APP_STRIPE_PUBLISHABLE_KEY', 'pk_live_51RRxn2P6qREjIxD1qOQOmw8WeyF4AlzFUH0YzPSREJQjyo3MJQI7PKRAmwcZ7h4yHmwLni2birDGS33H20YgDvWh00BL0ebo9C');

// Other constants can be added here
