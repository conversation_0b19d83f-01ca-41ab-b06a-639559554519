import { useState, useEffect } from 'react';
import { Line } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  ChartOptions
} from 'chart.js';
import axios from 'axios';
import { API_URL } from '../config';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend
);

interface WeatherData {
  date: string;
  temperature: number;
  precipitation: number;
  humidity: number;
  windSpeed: number;
}

const WeatherTrendsWidget = () => {
  const [weatherData, setWeatherData] = useState<WeatherData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [dataType, setDataType] = useState<'temperature' | 'precipitation' | 'humidity' | 'windSpeed'>('temperature');
  const [timeRange, setTimeRange] = useState<'7days' | '30days' | '90days'>('7days');

  // Fetch weather data
  useEffect(() => {
    const fetchWeatherData = async () => {
      setLoading(true);
      setError(null);

      try {
        const response = await axios.get(`${API_URL}/api/weather/history`, {
          params: { timeRange },
          withCredentials: true
        });
        
        if (response.data && response.data.weatherData) {
          setWeatherData(response.data.weatherData);
        } else {
          setWeatherData([]);
        }
      } catch (err) {
        console.error('Error fetching weather data:', err);
        setError('Failed to load weather data. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchWeatherData();
  }, [timeRange]);

  // Handle data type change
  const handleDataTypeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setDataType(e.target.value as 'temperature' | 'precipitation' | 'humidity' | 'windSpeed');
  };

  // Handle time range change
  const handleTimeRangeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setTimeRange(e.target.value as '7days' | '30days' | '90days');
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
  };

  // Get data type label and unit
  const getDataTypeInfo = () => {
    switch (dataType) {
      case 'temperature':
        return { label: 'Temperature', unit: '°F', color: 'rgb(255, 99, 132)' };
      case 'precipitation':
        return { label: 'Precipitation', unit: 'in', color: 'rgb(54, 162, 235)' };
      case 'humidity':
        return { label: 'Humidity', unit: '%', color: 'rgb(75, 192, 192)' };
      case 'windSpeed':
        return { label: 'Wind Speed', unit: 'mph', color: 'rgb(153, 102, 255)' };
      default:
        return { label: 'Temperature', unit: '°F', color: 'rgb(255, 99, 132)' };
    }
  };

  const dataTypeInfo = getDataTypeInfo();

  // Prepare chart data
  const chartData = {
    labels: weatherData.map(item => formatDate(item.date)),
    datasets: [
      {
        label: `${dataTypeInfo.label} (${dataTypeInfo.unit})`,
        data: weatherData.map(item => item[dataType]),
        borderColor: dataTypeInfo.color,
        backgroundColor: `${dataTypeInfo.color}33`, // Add transparency
        borderWidth: 2,
        fill: true,
        tension: 0.4
      }
    ]
  };

  // Chart options
  const chartOptions: ChartOptions<'line'> = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top',
      },
      title: {
        display: true,
        text: `Weather Trends: ${dataTypeInfo.label}`,
        font: {
          size: 16
        }
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            let label = context.dataset.label || '';
            if (label) {
              label = label.split(' ')[0] + ': '; // Just get the data type name
            }
            if (context.parsed.y !== null) {
              label += context.parsed.y.toFixed(1) + ' ' + dataTypeInfo.unit;
            }
            return label;
          }
        }
      }
    },
    scales: {
      x: {
        title: {
          display: true,
          text: 'Date'
        }
      },
      y: {
        title: {
          display: true,
          text: `${dataTypeInfo.label} (${dataTypeInfo.unit})`
        },
        ticks: {
          callback: function(value) {
            return value + ' ' + dataTypeInfo.unit;
          }
        }
      }
    }
  };

  // Calculate statistics
  const calculateStats = () => {
    if (weatherData.length === 0) return { avg: 0, min: 0, max: 0 };
    
    const values = weatherData.map(item => item[dataType]);
    const sum = values.reduce((a, b) => a + b, 0);
    const avg = sum / values.length;
    const min = Math.min(...values);
    const max = Math.max(...values);
    
    return { avg, min, max };
  };

  const stats = calculateStats();

  return (
    <div className="h-full flex flex-col">
      <div className="mb-4 flex flex-wrap justify-between items-center gap-2">
        <div className="text-sm font-medium text-gray-500 flex items-center">
          <span>Data Type:</span>
          <select
            value={dataType}
            onChange={handleDataTypeChange}
            className="ml-2 p-1 border border-gray-300 rounded"
          >
            <option value="temperature">Temperature</option>
            <option value="precipitation">Precipitation</option>
            <option value="humidity">Humidity</option>
            <option value="windSpeed">Wind Speed</option>
          </select>
          
          <span className="ml-4">Time Range:</span>
          <select
            value={timeRange}
            onChange={handleTimeRangeChange}
            className="ml-2 p-1 border border-gray-300 rounded"
          >
            <option value="7days">Last 7 Days</option>
            <option value="30days">Last 30 Days</option>
            <option value="90days">Last 90 Days</option>
          </select>
        </div>
        
        <div className="flex space-x-4">
          <div className="text-sm">
            <span className="font-medium text-gray-500">Avg:</span>
            <span className="ml-1">
              {stats.avg.toFixed(1)} {dataTypeInfo.unit}
            </span>
          </div>
          <div className="text-sm">
            <span className="font-medium text-gray-500">Min:</span>
            <span className="ml-1">
              {stats.min.toFixed(1)} {dataTypeInfo.unit}
            </span>
          </div>
          <div className="text-sm">
            <span className="font-medium text-gray-500">Max:</span>
            <span className="ml-1">
              {stats.max.toFixed(1)} {dataTypeInfo.unit}
            </span>
          </div>
        </div>
      </div>

      <div className="flex-grow relative">
        {loading ? (
          <div className="absolute inset-0 flex items-center justify-center">
            <p className="text-sm text-gray-500">Loading weather data...</p>
          </div>
        ) : error ? (
          <div className="absolute inset-0 flex items-center justify-center">
            <p className="text-sm text-red-500">{error}</p>
          </div>
        ) : weatherData.length === 0 ? (
          <div className="absolute inset-0 flex items-center justify-center">
            <p className="text-sm text-gray-500">No weather data available for the selected time range.</p>
          </div>
        ) : (
          <Line data={chartData} options={chartOptions} />
        )}
      </div>
    </div>
  );
};

export default WeatherTrendsWidget;