import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { toast } from 'react-hot-toast';
import axios from 'axios';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/Card';
import { Button } from '../ui/Button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../ui/Table';
import { Badge } from '../ui/Badge';
import { Loader2, Check, X, Settings } from 'lucide-react';
import { Switch } from '../ui/Switch';
import { Label } from '../ui/Label';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '../ui/Tabs';

interface Farm {
  id: string;
  name: string;
}

interface FarmAssociation {
  id: string;
  initiator_farm_id: string;
  associated_farm_id: string;
  status: 'pending' | 'active' | 'rejected' | 'revoked';
  association_type: string;
  created_at: string;
  updated_at: string;
  initiatorFarm?: Farm;
  associatedFarm?: Farm;
}

interface Permission {
  id: string;
  farm_association_id: string;
  permission_type: string;
  status: 'pending' | 'active' | 'rejected';
  initiator_farm_id: string;
  last_updated_at: string;
  created_at: string;
  updated_at: string;
  initiatorFarm?: Farm;
  farmAssociation?: FarmAssociation;
}

const permissionTypeLabels: Record<string, string> = {
  'invoices': 'Shared Invoices',
  'chat': 'Chat',
  'customers': 'Shared Customers',
  'products': 'Shared Products',
  'equipment': 'Shared Equipment',
  'tasks': 'Shared Tasks'
};

const FarmAssociationPermissions: React.FC<{ association: FarmAssociation; currentFarmId: string; onPermissionsUpdated: () => void }> = ({ 
  association, 
  currentFarmId,
  onPermissionsUpdated
}) => {
  const [loading, setLoading] = useState<boolean>(true);
  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [pendingPermissions, setPendingPermissions] = useState<Permission[]>([]);
  const [permissionStates, setPermissionStates] = useState<Record<string, boolean>>({});
  const [submitting, setSubmitting] = useState<boolean>(false);

  // Get the other farm in the association
  const otherFarm = association.initiator_farm_id === currentFarmId 
    ? association.associatedFarm 
    : association.initiatorFarm;

  // Fetch permissions for this association
  const fetchPermissions = async () => {
    try {
      setLoading(true);
      const response = await axios.get(`/api/farm-association-permissions/association/${association.id}`);
      setPermissions(response.data);
      
      // Initialize permission states
      const states: Record<string, boolean> = {};
      response.data.forEach((perm: Permission) => {
        states[perm.permission_type] = perm.status === 'active';
      });
      setPermissionStates(states);
      
      setLoading(false);
    } catch (error) {
      console.error('Error fetching permissions:', error);
      toast.error('Error fetching permissions');
      setLoading(false);
    }
  };

  // Fetch pending permissions for this association
  const fetchPendingPermissions = async () => {
    try {
      const response = await axios.get(`/api/farm-association-permissions/farm/${currentFarmId}/pending`);
      // Filter to only include permissions for this association
      const associationPendingPermissions = response.data.filter(
        (perm: Permission) => perm.farm_association_id === association.id
      );
      setPendingPermissions(associationPendingPermissions);
    } catch (error) {
      console.error('Error fetching pending permissions:', error);
      toast.error('Error fetching pending permissions');
    }
  };

  // Update permissions
  const updatePermissions = async () => {
    try {
      setSubmitting(true);
      
      // Create array of permissions to update
      const permissionsToUpdate = Object.entries(permissionStates).map(([permission_type, enabled]) => ({
        permission_type,
        status: enabled ? 'active' : 'rejected'
      }));
      
      await axios.post(`/api/farm-association-permissions/association/${association.id}`, {
        permissions: permissionsToUpdate,
        initiatorFarmId: currentFarmId
      });
      
      toast.success('Permissions updated successfully');
      fetchPermissions();
      onPermissionsUpdated();
      setSubmitting(false);
    } catch (error) {
      console.error('Error updating permissions:', error);
      toast.error('Error updating permissions');
      setSubmitting(false);
    }
  };

  // Update a permission status (accept or reject)
  const updatePermissionStatus = async (permissionId: string, status: 'active' | 'rejected') => {
    try {
      await axios.put(`/api/farm-association-permissions/${permissionId}/status`, {
        status,
        farmId: currentFarmId
      });
      
      toast.success(`Permission ${status === 'active' ? 'approved' : 'rejected'}`);
      fetchPermissions();
      fetchPendingPermissions();
      onPermissionsUpdated();
    } catch (error) {
      console.error('Error updating permission status:', error);
      toast.error('Error updating permission status');
    }
  };

  // Handle permission toggle
  const handlePermissionToggle = (permissionType: string, enabled: boolean) => {
    setPermissionStates(prev => ({
      ...prev,
      [permissionType]: enabled
    }));
  };

  useEffect(() => {
    if (association.id) {
      fetchPermissions();
      fetchPendingPermissions();
    }
  }, [association.id]);

  // Status badge component
  const StatusBadge: React.FC<{ status: string }> = ({ status }) => {
    let color = '';
    switch (status) {
      case 'active':
        color = 'bg-green-500';
        break;
      case 'pending':
        color = 'bg-yellow-500';
        break;
      case 'rejected':
        color = 'bg-red-500';
        break;
      default:
        color = 'bg-blue-500';
    }

    return <Badge className={color}>{status}</Badge>;
  };

  return (
    <Card className="mt-4">
      <CardHeader>
        <CardTitle>Permissions with {otherFarm?.name}</CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="manage">
          <TabsList>
            <TabsTrigger value="manage">Manage Permissions</TabsTrigger>
            <TabsTrigger value="pending">Pending Requests {pendingPermissions.length > 0 && `(${pendingPermissions.length})`}</TabsTrigger>
          </TabsList>

          <TabsContent value="manage" className="space-y-4">
            {loading ? (
              <div className="flex justify-center p-4">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
              </div>
            ) : (
              <div className="space-y-6">
                <div className="space-y-4">
                  <p className="text-sm text-gray-500">
                    Enable or disable features you want to share with {otherFarm?.name}. 
                    Changes will require approval from the other farm.
                  </p>
                  
                  <div className="space-y-2">
                    {Object.entries(permissionTypeLabels).map(([permType, label]) => (
                      <div key={permType} className="flex items-center justify-between py-2 border-b">
                        <div className="flex items-center space-x-2">
                          <Label htmlFor={`permission-${permType}`} className="font-medium">
                            {label}
                          </Label>
                          {permissions.find(p => p.permission_type === permType)?.status === 'pending' && (
                            <Badge className="bg-yellow-500">Pending Approval</Badge>
                          )}
                        </div>
                        <Switch 
                          id={`permission-${permType}`}
                          checked={permissionStates[permType] || false}
                          onCheckedChange={(checked) => handlePermissionToggle(permType, checked)}
                        />
                      </div>
                    ))}
                  </div>
                  
                  <Button 
                    onClick={updatePermissions} 
                    disabled={submitting || loading}
                    className="w-full mt-4"
                  >
                    {submitting ? (
                      <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    ) : (
                      <Settings className="h-4 w-4 mr-2" />
                    )}
                    Update Permissions
                  </Button>
                </div>
              </div>
            )}
          </TabsContent>

          <TabsContent value="pending" className="space-y-4">
            {pendingPermissions.length > 0 ? (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Permission</TableHead>
                    <TableHead>Requested By</TableHead>
                    <TableHead>Requested</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {pendingPermissions.map((permission) => (
                    <TableRow key={permission.id}>
                      <TableCell>{permissionTypeLabels[permission.permission_type] || permission.permission_type}</TableCell>
                      <TableCell>{permission.initiatorFarm?.name}</TableCell>
                      <TableCell>{new Date(permission.created_at).toLocaleDateString()}</TableCell>
                      <TableCell className="flex space-x-2">
                        <Button
                          variant="default"
                          size="sm"
                          onClick={() => updatePermissionStatus(permission.id, 'active')}
                        >
                          <Check className="h-4 w-4 mr-1" /> Approve
                        </Button>
                        <Button
                          variant="destructive"
                          size="sm"
                          onClick={() => updatePermissionStatus(permission.id, 'rejected')}
                        >
                          <X className="h-4 w-4 mr-1" /> Reject
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            ) : (
              <div className="text-center p-4">
                <p>No pending permission requests.</p>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

export default FarmAssociationPermissions;