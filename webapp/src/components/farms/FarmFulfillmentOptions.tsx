import { useState, useEffect } from 'react';
import { toast } from 'react-toastify';
import {
  getFarmFulfillmentOptions,
  updateFarmFulfillmentOptions,
  FarmFulfillmentOptions as FarmFulfillmentOptionsType
} from '../../services/fulfillmentOptionsService';

interface FarmFulfillmentOptionsProps {
  farmId: string;
  onFulfillmentOptionsChange?: (options: FarmFulfillmentOptionsType) => void;
}

const FarmFulfillmentOptions: React.FC<FarmFulfillmentOptionsProps> = ({
  farmId,
  onFulfillmentOptionsChange
}) => {
  const [fulfillmentOptions, setFulfillmentOptions] = useState<FarmFulfillmentOptionsType | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [saving, setSaving] = useState<boolean>(false);
  const [offersDelivery, setOffersDelivery] = useState<boolean>(true);
  const [offersPickup, setOffersPickup] = useState<boolean>(true);
  const [deliveryFee, setDeliveryFee] = useState<string>('0.00');
  const [minOrderForFreeDelivery, setMinOrderForFreeDelivery] = useState<string>('');
  const [deliveryRadius, setDeliveryRadius] = useState<string>('');
  const [pickupInstructions, setPickupInstructions] = useState<string>('');
  const [deliveryInstructions, setDeliveryInstructions] = useState<string>('');

  // Fetch fulfillment options when component mounts
  useEffect(() => {
    const fetchFulfillmentOptions = async () => {
      if (!farmId) return;

      try {
        setLoading(true);
        const options = await getFarmFulfillmentOptions(farmId);
        setFulfillmentOptions(options);
        
        // Initialize form values
        setOffersDelivery(options.offers_delivery);
        setOffersPickup(options.offers_pickup);
        setDeliveryFee(options.delivery_fee.toString());
        setMinOrderForFreeDelivery(options.min_order_for_free_delivery?.toString() || '');
        setDeliveryRadius(options.delivery_radius_miles?.toString() || '');
        setPickupInstructions(options.pickup_instructions || '');
        setDeliveryInstructions(options.delivery_instructions || '');
      } catch (error) {
        console.error('Error fetching farm fulfillment options:', error);
        toast.error('Failed to load fulfillment options');
      } finally {
        setLoading(false);
      }
    };

    fetchFulfillmentOptions();
  }, [farmId]);

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      setSaving(true);
      
      const updatedOptions = await updateFarmFulfillmentOptions(farmId, {
        offers_delivery: offersDelivery,
        offers_pickup: offersPickup,
        delivery_fee: parseFloat(deliveryFee) || 0,
        min_order_for_free_delivery: minOrderForFreeDelivery ? parseFloat(minOrderForFreeDelivery) : null,
        delivery_radius_miles: deliveryRadius ? parseInt(deliveryRadius) : null,
        pickup_instructions: pickupInstructions,
        delivery_instructions: deliveryInstructions
      });
      
      setFulfillmentOptions(updatedOptions);
      
      if (onFulfillmentOptionsChange) {
        onFulfillmentOptionsChange(updatedOptions);
      }
      
      toast.success('Fulfillment options updated successfully');
    } catch (error) {
      console.error('Error updating farm fulfillment options:', error);
      toast.error('Failed to update fulfillment options');
    } finally {
      setSaving(false);
    }
  };

  if (loading && !fulfillmentOptions) {
    return (
      <div className="p-4 flex justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <form onSubmit={handleSubmit}>
        <div className="space-y-4">
          <div>
            <h3 className="text-lg font-medium text-gray-900">Fulfillment Methods</h3>
            <p className="mt-1 text-sm text-gray-500">
              Configure how customers can receive their orders from your farm.
            </p>
          </div>
          
          <div className="flex flex-col space-y-4 sm:flex-row sm:space-y-0 sm:space-x-6">
            <div className="flex items-center">
              <input
                id="offers-delivery"
                name="offers-delivery"
                type="checkbox"
                checked={offersDelivery}
                onChange={(e) => setOffersDelivery(e.target.checked)}
                className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
              />
              <label htmlFor="offers-delivery" className="ml-2 block text-sm text-gray-700">
                Offer Delivery
              </label>
            </div>
            
            <div className="flex items-center">
              <input
                id="offers-pickup"
                name="offers-pickup"
                type="checkbox"
                checked={offersPickup}
                onChange={(e) => setOffersPickup(e.target.checked)}
                className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
              />
              <label htmlFor="offers-pickup" className="ml-2 block text-sm text-gray-700">
                Offer Pickup
              </label>
            </div>
          </div>
          
          {offersDelivery && (
            <div className="bg-gray-50 p-4 rounded-md space-y-4">
              <h4 className="text-md font-medium text-gray-900">Delivery Settings</h4>
              
              <div className="grid grid-cols-1 gap-y-4 gap-x-6 sm:grid-cols-2">
                <div>
                  <label htmlFor="delivery-fee" className="block text-sm font-medium text-gray-700">
                    Delivery Fee ($)
                  </label>
                  <div className="mt-1">
                    <input
                      type="number"
                      step="0.01"
                      min="0"
                      id="delivery-fee"
                      name="delivery-fee"
                      value={deliveryFee}
                      onChange={(e) => setDeliveryFee(e.target.value)}
                      className="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 rounded-md"
                    />
                  </div>
                </div>
                
                <div>
                  <label htmlFor="min-order" className="block text-sm font-medium text-gray-700">
                    Minimum Order for Free Delivery ($)
                  </label>
                  <div className="mt-1">
                    <input
                      type="number"
                      step="0.01"
                      min="0"
                      id="min-order"
                      name="min-order"
                      value={minOrderForFreeDelivery}
                      onChange={(e) => setMinOrderForFreeDelivery(e.target.value)}
                      placeholder="Leave blank for no free delivery"
                      className="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 rounded-md"
                    />
                  </div>
                </div>
                
                <div>
                  <label htmlFor="delivery-radius" className="block text-sm font-medium text-gray-700">
                    Delivery Radius (miles)
                  </label>
                  <div className="mt-1">
                    <input
                      type="number"
                      min="0"
                      id="delivery-radius"
                      name="delivery-radius"
                      value={deliveryRadius}
                      onChange={(e) => setDeliveryRadius(e.target.value)}
                      placeholder="Leave blank for no limit"
                      className="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 rounded-md"
                    />
                  </div>
                </div>
                
                <div className="sm:col-span-2">
                  <label htmlFor="delivery-instructions" className="block text-sm font-medium text-gray-700">
                    Delivery Instructions
                  </label>
                  <div className="mt-1">
                    <textarea
                      id="delivery-instructions"
                      name="delivery-instructions"
                      rows={3}
                      value={deliveryInstructions}
                      onChange={(e) => setDeliveryInstructions(e.target.value)}
                      placeholder="Instructions for customers about delivery (e.g., delivery days, times, etc.)"
                      className="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 rounded-md"
                    />
                  </div>
                </div>
              </div>
            </div>
          )}
          
          {offersPickup && (
            <div className="bg-gray-50 p-4 rounded-md space-y-4">
              <h4 className="text-md font-medium text-gray-900">Pickup Settings</h4>
              
              <div>
                <label htmlFor="pickup-instructions" className="block text-sm font-medium text-gray-700">
                  Pickup Instructions
                </label>
                <div className="mt-1">
                  <textarea
                    id="pickup-instructions"
                    name="pickup-instructions"
                    rows={3}
                    value={pickupInstructions}
                    onChange={(e) => setPickupInstructions(e.target.value)}
                    placeholder="Instructions for customers about pickup (e.g., pickup location, hours, etc.)"
                    className="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 rounded-md"
                  />
                </div>
              </div>
            </div>
          )}
          
          <div className="pt-4">
            <button
              type="submit"
              disabled={saving}
              className={`inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 ${saving ? 'opacity-70 cursor-not-allowed' : ''}`}
            >
              {saving ? 'Saving...' : 'Save Fulfillment Options'}
            </button>
          </div>
        </div>
      </form>
    </div>
  );
};

export default FarmFulfillmentOptions;