import { useState, useEffect } from 'react';
import { toast } from 'react-toastify';
import {
  getFarmFeatureToggles,
  updateFarmFeatureToggles,
  FarmFeatureToggles as FarmFeatureTogglesType
} from '../../services/farmFeatureTogglesService';

interface FarmFeatureTogglesProps {
  farmId: string;
  onFeatureTogglesChange?: (toggles: FarmFeatureTogglesType) => void;
}

const FarmFeatureToggles: React.FC<FarmFeatureTogglesProps> = ({
  farmId,
  onFeatureTogglesChange
}) => {
  const [featureToggles, setFeatureToggles] = useState<FarmFeatureTogglesType | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [saving, setSaving] = useState<boolean>(false);
  const [marketplaceEnabled, setMarketplaceEnabled] = useState<boolean>(false);
  const [customerOrdersEnabled, setCustomerOrdersEnabled] = useState<boolean>(false);
  const [deliverySchedulingEnabled, setDeliverySchedulingEnabled] = useState<boolean>(false);
  const [driverTrackingEnabled, setDriverTrackingEnabled] = useState<boolean>(false);
  const [customerMessagingEnabled, setCustomerMessagingEnabled] = useState<boolean>(false);

  // Fetch feature toggles when component mounts
  useEffect(() => {
    const fetchFeatureToggles = async () => {
      if (!farmId) return;

      try {
        setLoading(true);
        const toggles = await getFarmFeatureToggles(farmId);
        setFeatureToggles(toggles);
        
        // Initialize form values
        setMarketplaceEnabled(toggles.marketplace_enabled);
        setCustomerOrdersEnabled(toggles.customer_orders_enabled);
        setDeliverySchedulingEnabled(toggles.delivery_scheduling_enabled);
        setDriverTrackingEnabled(toggles.driver_tracking_enabled);
        setCustomerMessagingEnabled(toggles.customer_messaging_enabled);
      } catch (error) {
        console.error('Error fetching farm feature toggles:', error);
        toast.error('Failed to load feature toggles');
      } finally {
        setLoading(false);
      }
    };

    fetchFeatureToggles();
  }, [farmId]);

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      setSaving(true);
      
      const updatedToggles = await updateFarmFeatureToggles(farmId, {
        marketplace_enabled: marketplaceEnabled,
        customer_orders_enabled: customerOrdersEnabled,
        delivery_scheduling_enabled: deliverySchedulingEnabled,
        driver_tracking_enabled: driverTrackingEnabled,
        customer_messaging_enabled: customerMessagingEnabled
      });
      
      setFeatureToggles(updatedToggles);
      
      if (onFeatureTogglesChange) {
        onFeatureTogglesChange(updatedToggles);
      }
      
      toast.success('Feature toggles updated successfully');
    } catch (error) {
      console.error('Error updating farm feature toggles:', error);
      toast.error('Failed to update feature toggles');
    } finally {
      setSaving(false);
    }
  };

  if (loading && !featureToggles) {
    return (
      <div className="p-4 flex justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <form onSubmit={handleSubmit}>
        <div className="space-y-4">
          <div>
            <h3 className="text-lg font-medium text-gray-900">Marketplace Features</h3>
            <p className="mt-1 text-sm text-gray-500">
              Enable or disable marketplace features for your farm.
            </p>
          </div>
          
          <div className="bg-gray-50 p-4 rounded-md space-y-4">
            <div className="flex items-center">
              <input
                id="marketplace-enabled"
                name="marketplace-enabled"
                type="checkbox"
                checked={marketplaceEnabled}
                onChange={(e) => setMarketplaceEnabled(e.target.checked)}
                className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
              />
              <label htmlFor="marketplace-enabled" className="ml-2 block text-sm text-gray-700">
                Marketplace Visibility
              </label>
              <span className="ml-2 text-xs text-gray-500">
                (Make your farm visible in the public marketplace)
              </span>
            </div>
            
            <div className="flex items-center">
              <input
                id="customer-orders-enabled"
                name="customer-orders-enabled"
                type="checkbox"
                checked={customerOrdersEnabled}
                onChange={(e) => setCustomerOrdersEnabled(e.target.checked)}
                className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
              />
              <label htmlFor="customer-orders-enabled" className="ml-2 block text-sm text-gray-700">
                Customer Order Requests
              </label>
              <span className="ml-2 text-xs text-gray-500">
                (Allow customers to submit order requests)
              </span>
            </div>
            
            <div className="flex items-center">
              <input
                id="delivery-scheduling-enabled"
                name="delivery-scheduling-enabled"
                type="checkbox"
                checked={deliverySchedulingEnabled}
                onChange={(e) => setDeliverySchedulingEnabled(e.target.checked)}
                className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
              />
              <label htmlFor="delivery-scheduling-enabled" className="ml-2 block text-sm text-gray-700">
                Delivery Scheduling
              </label>
              <span className="ml-2 text-xs text-gray-500">
                (Enable delivery scheduling for customers)
              </span>
            </div>
            
            <div className="flex items-center">
              <input
                id="driver-tracking-enabled"
                name="driver-tracking-enabled"
                type="checkbox"
                checked={driverTrackingEnabled}
                onChange={(e) => setDriverTrackingEnabled(e.target.checked)}
                className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
              />
              <label htmlFor="driver-tracking-enabled" className="ml-2 block text-sm text-gray-700">
                Driver Location Tracking
              </label>
              <span className="ml-2 text-xs text-gray-500">
                (Allow customers to track driver location for deliveries)
              </span>
            </div>
            
            <div className="flex items-center">
              <input
                id="customer-messaging-enabled"
                name="customer-messaging-enabled"
                type="checkbox"
                checked={customerMessagingEnabled}
                onChange={(e) => setCustomerMessagingEnabled(e.target.checked)}
                className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
              />
              <label htmlFor="customer-messaging-enabled" className="ml-2 block text-sm text-gray-700">
                Customer Messaging
              </label>
              <span className="ml-2 text-xs text-gray-500">
                (Allow customers to send messages to your farm)
              </span>
            </div>
          </div>
          
          <div className="pt-4">
            <button
              type="submit"
              disabled={saving}
              className={`inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 ${saving ? 'opacity-70 cursor-not-allowed' : ''}`}
            >
              {saving ? 'Saving...' : 'Save Feature Settings'}
            </button>
          </div>
        </div>
      </form>
    </div>
  );
};

export default FarmFeatureToggles;