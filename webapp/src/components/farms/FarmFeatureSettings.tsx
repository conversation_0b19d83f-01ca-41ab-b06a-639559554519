import { useState, useEffect } from 'react';
import { toast } from 'react-toastify';
import {
  getFarmFeatureSettings,
  updateFarmFeatureSettings,
  FarmFeatureSettings as FarmFeatureSettingsType
} from '../../services/farmFeatureTogglesService';

interface FarmFeatureSettingsProps {
  farmId: string;
  onFeatureSettingsChange?: (settings: FarmFeatureSettingsType) => void;
}

const FarmFeatureSettings: React.FC<FarmFeatureSettingsProps> = ({
  farmId,
  onFeatureSettingsChange
}) => {
  const [featureSettings, setFeatureSettings] = useState<FarmFeatureSettingsType | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [saving, setSaving] = useState<boolean>(false);
  
  // State for form fields
  const [marketplaceDefaultVisibility, setMarketplaceDefaultVisibility] = useState<boolean>(false);
  const [marketplaceDefaultCategory, setMarketplaceDefaultCategory] = useState<string>('');
  const [customerOrderAutoApproval, setCustomerOrderAutoApproval] = useState<boolean>(false);
  const [deliverySchedulingAdvanceDays, setDeliverySchedulingAdvanceDays] = useState<string>('7');
  const [deliverySchedulingBlackoutDates, setDeliverySchedulingBlackoutDates] = useState<string>('');
  const [driverTrackingUpdateFrequency, setDriverTrackingUpdateFrequency] = useState<string>('60');
  const [customerMessagingAutoResponse, setCustomerMessagingAutoResponse] = useState<string>('');

  // Fetch feature settings when component mounts
  useEffect(() => {
    const fetchFeatureSettings = async () => {
      if (!farmId) return;

      try {
        setLoading(true);
        const settings = await getFarmFeatureSettings(farmId);
        setFeatureSettings(settings);
        
        // Initialize form values
        setMarketplaceDefaultVisibility(settings.marketplace_default_visibility || false);
        setMarketplaceDefaultCategory(settings.marketplace_default_category || '');
        setCustomerOrderAutoApproval(settings.customer_order_auto_approval || false);
        setDeliverySchedulingAdvanceDays((settings.delivery_scheduling_advance_days || 7).toString());
        setDeliverySchedulingBlackoutDates((settings.delivery_scheduling_blackout_dates || []).join(', '));
        setDriverTrackingUpdateFrequency((settings.driver_tracking_update_frequency || 60).toString());
        setCustomerMessagingAutoResponse(settings.customer_messaging_auto_response || '');
      } catch (error) {
        console.error('Error fetching farm feature settings:', error);
        toast.error('Failed to load feature settings');
      } finally {
        setLoading(false);
      }
    };

    fetchFeatureSettings();
  }, [farmId]);

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      setSaving(true);
      
      const updatedSettings = await updateFarmFeatureSettings(farmId, {
        farm_id: farmId,
        marketplace_default_visibility: marketplaceDefaultVisibility,
        marketplace_default_category: marketplaceDefaultCategory,
        customer_order_auto_approval: customerOrderAutoApproval,
        delivery_scheduling_advance_days: parseInt(deliverySchedulingAdvanceDays) || 7,
        delivery_scheduling_blackout_dates: deliverySchedulingBlackoutDates.split(',').map(date => date.trim()).filter(date => date),
        driver_tracking_update_frequency: parseInt(driverTrackingUpdateFrequency) || 60,
        customer_messaging_auto_response: customerMessagingAutoResponse
      });
      
      setFeatureSettings(updatedSettings);
      
      if (onFeatureSettingsChange) {
        onFeatureSettingsChange(updatedSettings);
      }
      
      toast.success('Feature settings updated successfully');
    } catch (error) {
      console.error('Error updating farm feature settings:', error);
      toast.error('Failed to update feature settings');
    } finally {
      setSaving(false);
    }
  };

  if (loading && !featureSettings) {
    return (
      <div className="p-4 flex justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <form onSubmit={handleSubmit}>
        <div className="space-y-4">
          <div>
            <h3 className="text-lg font-medium text-gray-900">Feature Settings Configuration</h3>
            <p className="mt-1 text-sm text-gray-500">
              Configure default settings for your farm's marketplace features.
            </p>
          </div>
          
          <div className="bg-gray-50 p-4 rounded-md space-y-4">
            <h4 className="text-md font-medium text-gray-900">Marketplace Settings</h4>
            
            <div className="flex items-center">
              <input
                id="marketplace-default-visibility"
                name="marketplace-default-visibility"
                type="checkbox"
                checked={marketplaceDefaultVisibility}
                onChange={(e) => setMarketplaceDefaultVisibility(e.target.checked)}
                className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
              />
              <label htmlFor="marketplace-default-visibility" className="ml-2 block text-sm text-gray-700">
                Default Product Visibility in Marketplace
              </label>
              <span className="ml-2 text-xs text-gray-500">
                (New products will be visible in marketplace by default)
              </span>
            </div>
            
            <div>
              <label htmlFor="marketplace-default-category" className="block text-sm font-medium text-gray-700">
                Default Product Category
              </label>
              <div className="mt-1">
                <input
                  type="text"
                  id="marketplace-default-category"
                  name="marketplace-default-category"
                  value={marketplaceDefaultCategory}
                  onChange={(e) => setMarketplaceDefaultCategory(e.target.value)}
                  placeholder="e.g., Vegetables, Fruits, Dairy"
                  className="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 rounded-md"
                />
              </div>
            </div>
          </div>
          
          <div className="bg-gray-50 p-4 rounded-md space-y-4">
            <h4 className="text-md font-medium text-gray-900">Customer Order Settings</h4>
            
            <div className="flex items-center">
              <input
                id="customer-order-auto-approval"
                name="customer-order-auto-approval"
                type="checkbox"
                checked={customerOrderAutoApproval}
                onChange={(e) => setCustomerOrderAutoApproval(e.target.checked)}
                className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
              />
              <label htmlFor="customer-order-auto-approval" className="ml-2 block text-sm text-gray-700">
                Auto-Approve Customer Orders
              </label>
              <span className="ml-2 text-xs text-gray-500">
                (Customer orders will be automatically approved)
              </span>
            </div>
          </div>
          
          <div className="bg-gray-50 p-4 rounded-md space-y-4">
            <h4 className="text-md font-medium text-gray-900">Delivery Scheduling Settings</h4>
            
            <div>
              <label htmlFor="delivery-scheduling-advance-days" className="block text-sm font-medium text-gray-700">
                Advance Booking Days
              </label>
              <div className="mt-1">
                <input
                  type="number"
                  min="1"
                  id="delivery-scheduling-advance-days"
                  name="delivery-scheduling-advance-days"
                  value={deliverySchedulingAdvanceDays}
                  onChange={(e) => setDeliverySchedulingAdvanceDays(e.target.value)}
                  className="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 rounded-md"
                />
              </div>
              <p className="mt-1 text-xs text-gray-500">
                Number of days in advance customers can schedule deliveries
              </p>
            </div>
            
            <div>
              <label htmlFor="delivery-scheduling-blackout-dates" className="block text-sm font-medium text-gray-700">
                Blackout Dates
              </label>
              <div className="mt-1">
                <input
                  type="text"
                  id="delivery-scheduling-blackout-dates"
                  name="delivery-scheduling-blackout-dates"
                  value={deliverySchedulingBlackoutDates}
                  onChange={(e) => setDeliverySchedulingBlackoutDates(e.target.value)}
                  placeholder="e.g., 2023-12-25, 2024-01-01"
                  className="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 rounded-md"
                />
              </div>
              <p className="mt-1 text-xs text-gray-500">
                Comma-separated list of dates when deliveries are not available (YYYY-MM-DD format)
              </p>
            </div>
          </div>
          
          <div className="bg-gray-50 p-4 rounded-md space-y-4">
            <h4 className="text-md font-medium text-gray-900">Driver Tracking Settings</h4>
            
            <div>
              <label htmlFor="driver-tracking-update-frequency" className="block text-sm font-medium text-gray-700">
                Location Update Frequency (seconds)
              </label>
              <div className="mt-1">
                <input
                  type="number"
                  min="10"
                  id="driver-tracking-update-frequency"
                  name="driver-tracking-update-frequency"
                  value={driverTrackingUpdateFrequency}
                  onChange={(e) => setDriverTrackingUpdateFrequency(e.target.value)}
                  className="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 rounded-md"
                />
              </div>
              <p className="mt-1 text-xs text-gray-500">
                How often driver location is updated (in seconds)
              </p>
            </div>
          </div>
          
          <div className="bg-gray-50 p-4 rounded-md space-y-4">
            <h4 className="text-md font-medium text-gray-900">Customer Messaging Settings</h4>
            
            <div>
              <label htmlFor="customer-messaging-auto-response" className="block text-sm font-medium text-gray-700">
                Auto-Response Message
              </label>
              <div className="mt-1">
                <textarea
                  id="customer-messaging-auto-response"
                  name="customer-messaging-auto-response"
                  rows={3}
                  value={customerMessagingAutoResponse}
                  onChange={(e) => setCustomerMessagingAutoResponse(e.target.value)}
                  placeholder="Automatic response sent to customers when they send a message"
                  className="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 rounded-md"
                />
              </div>
            </div>
          </div>
          
          <div className="pt-4">
            <button
              type="submit"
              disabled={saving}
              className={`inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 ${saving ? 'opacity-70 cursor-not-allowed' : ''}`}
            >
              {saving ? 'Saving...' : 'Save Feature Settings'}
            </button>
          </div>
        </div>
      </form>
    </div>
  );
};

export default FarmFeatureSettings;