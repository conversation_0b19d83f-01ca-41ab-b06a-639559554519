import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { toast } from 'react-hot-toast';
import { useAuth } from '../../context/AuthContext';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/Card';
import { Button } from '../ui/Button';
import { Input } from '../ui/Input';
import { Label } from '../ui/Label';
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '../ui/Tabs';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../ui/Table';
import { Badge } from '../ui/Badge';
import { Loader2, Plus, X, Check, Settings, Edit } from 'lucide-react';
import axios from 'axios';
import FarmAssociationPermissions from './FarmAssociationPermissions';
import { <PERSON><PERSON>, DialogContent, DialogHeader, <PERSON><PERSON><PERSON><PERSON><PERSON>, Di<PERSON>Footer } from '../ui/Dialog';

interface Farm {
  id: string;
  name: string;
}

interface FarmAssociation {
  id: string;
  initiator_farm_id: string;
  associated_farm_id: string;
  status: 'pending' | 'active' | 'rejected' | 'revoked';
  association_type: string;
  created_at: string;
  updated_at: string;
  initiatorFarm?: Farm;
  associatedFarm?: Farm;
}

const FarmAssociations: React.FC = () => {
  const { farmId } = useParams<{ farmId: string }>();
  const { user } = useAuth();

  const [loading, setLoading] = useState<boolean>(true);
  const [associations, setAssociations] = useState<FarmAssociation[]>([]);
  const [pendingAssociations, setPendingAssociations] = useState<FarmAssociation[]>([]);
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [searchResults, setSearchResults] = useState<Farm[]>([]);
  const [searching, setSearching] = useState<boolean>(false);
  const [selectedFarm, setSelectedFarm] = useState<Farm | null>(null);
  const [associationType, setAssociationType] = useState<string>('general');
  const [submitting, setSubmitting] = useState<boolean>(false);
  const [permissionsUpdated, setPermissionsUpdated] = useState<boolean>(false);
  const [editModalOpen, setEditModalOpen] = useState<boolean>(false);
  const [selectedAssociation, setSelectedAssociation] = useState<FarmAssociation | null>(null);
  const [editAssociationType, setEditAssociationType] = useState<string>('');
  const [updatingAssociation, setUpdatingAssociation] = useState<boolean>(false);

  // Fetch farm associations
  const fetchAssociations = async () => {
    try {
      setLoading(true);
      const response = await axios.get(`/api/farm-associations/farm/${farmId}`);
      setAssociations(response.data);
      setLoading(false);
    } catch (error) {
      console.error('Error fetching farm associations:', error);
      toast.error('Error fetching farm associations');
      setLoading(false);
    }
  };

  // Fetch pending associations
  const fetchPendingAssociations = async () => {
    try {
      const response = await axios.get(`/api/farm-associations/farm/${farmId}/pending`);
      setPendingAssociations(response.data);
    } catch (error) {
      console.error('Error fetching pending associations:', error);
      toast.error('Error fetching pending associations');
    }
  };

  // Search for farms
  const searchFarms = async () => {
    if (!searchTerm) return;

    try {
      setSearching(true);
      const response = await axios.get(`/api/farms/search?q=${searchTerm}`);
      // Filter out the current farm and already associated farms
      const filteredResults = response.data.farms.filter((farm: Farm) => {
        // Exclude current farm
        if (farm.id === farmId) return false;

        // Exclude farms that are already associated
        const isAlreadyAssociated = associations.some(
          assoc => 
            (assoc.initiator_farm_id === farmId && assoc.associated_farm_id === farm.id) ||
            (assoc.associated_farm_id === farmId && assoc.initiator_farm_id === farm.id)
        );

        return !isAlreadyAssociated;
      });

      setSearchResults(filteredResults);
      setSearching(false);
    } catch (error) {
      console.error('Error searching farms:', error);
      toast.error('Error searching farms');
      setSearching(false);
    }
  };

  // Create a farm association
  const createAssociation = async () => {
    if (!selectedFarm) return;

    try {
      setSubmitting(true);
      await axios.post('/api/farm-associations', {
        initiator_farm_id: farmId,
        associated_farm_id: selectedFarm.id,
        association_type: associationType
      });

      toast.success('Association request sent successfully');
      setSelectedFarm(null);
      setSearchTerm('');
      setSearchResults([]);
      fetchAssociations();
      setSubmitting(false);
    } catch (error) {
      console.error('Error creating farm association:', error);
      toast.error('Error creating farm association');
      setSubmitting(false);
    }
  };

  // Update association status
  const updateAssociationStatus = async (associationId: string, status: string) => {
    try {
      await axios.put(`/api/farm-associations/${associationId}`, { status });
      toast.success(`Association ${status === 'active' ? 'approved' : status}`);
      fetchAssociations();
      fetchPendingAssociations();
    } catch (error) {
      console.error('Error updating association status:', error);
      toast.error('Error updating association status');
    }
  };

  // Update association type
  const updateAssociationType = async () => {
    if (!selectedAssociation) return;

    try {
      setUpdatingAssociation(true);
      await axios.put(`/api/farm-associations/${selectedAssociation.id}/type`, { 
        association_type: editAssociationType,
        initiator_farm_id: farmId
      });

      toast.success('Association type update request sent successfully');
      fetchAssociations();
      setEditModalOpen(false);
      setSelectedAssociation(null);
      setUpdatingAssociation(false);
    } catch (error) {
      console.error('Error updating association type:', error);
      toast.error('Error updating association type');
      setUpdatingAssociation(false);
    }
  };

  // Remove association
  const removeAssociation = async (associationId: string) => {
    try {
      await axios.delete(`/api/farm-associations/${associationId}`);
      toast.success('Association removed successfully');
      fetchAssociations();
    } catch (error) {
      console.error('Error removing farm association:', error);
      toast.error('Error removing farm association');
    }
  };

  useEffect(() => {
    if (farmId) {
      fetchAssociations();
      fetchPendingAssociations();
    }
  }, [farmId, permissionsUpdated]);

  // Status badge component
  const StatusBadge: React.FC<{ status: string }> = ({ status }) => {
    let color = '';
    switch (status) {
      case 'active':
        color = 'bg-green-500';
        break;
      case 'pending':
        color = 'bg-yellow-500';
        break;
      case 'rejected':
        color = 'bg-red-500';
        break;
      case 'revoked':
        color = 'bg-gray-500';
        break;
      default:
        color = 'bg-blue-500';
    }

    return <Badge className={color}>{status}</Badge>;
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Farm Associations</CardTitle>
        </CardHeader>
        <CardContent>
          {/* Edit Association Modal */}
          <Dialog open={editModalOpen} onOpenChange={setEditModalOpen}>
            <DialogContent className="max-w-3xl">
              <DialogHeader>
                <DialogTitle>Edit Farm Association</DialogTitle>
              </DialogHeader>

              {selectedAssociation && (
                <div className="space-y-6">
                  <div className="space-y-2">
                    <Label htmlFor="editAssociationType">Association Type</Label>
                    <select
                      id="editAssociationType"
                      className="w-full p-2 border rounded-md"
                      value={editAssociationType}
                      onChange={(e) => setEditAssociationType(e.target.value)}
                    >
                      <option value="general">General</option>
                      <option value="business">Business</option>
                      <option value="supplier">Supplier</option>
                      <option value="customer">Customer</option>
                    </select>
                    <p className="text-sm text-gray-500">
                      Changing the association type will require approval from the other farm.
                    </p>
                  </div>

                  {/* Include the permissions component inside the modal */}
                  {selectedAssociation.status === 'active' && (
                    <FarmAssociationPermissions 
                      association={selectedAssociation} 
                      currentFarmId={farmId || ''} 
                      onPermissionsUpdated={() => setPermissionsUpdated(prev => !prev)}
                    />
                  )}

                  <DialogFooter>
                    <Button
                      variant="outline"
                      onClick={() => setEditModalOpen(false)}
                    >
                      Cancel
                    </Button>
                    <Button
                      onClick={updateAssociationType}
                      disabled={updatingAssociation || selectedAssociation.association_type === editAssociationType}
                    >
                      {updatingAssociation ? (
                        <Loader2 className="h-4 w-4 animate-spin mr-2" />
                      ) : (
                        <Settings className="h-4 w-4 mr-2" />
                      )}
                      Update Association
                    </Button>
                  </DialogFooter>
                </div>
              )}
            </DialogContent>
          </Dialog>
          <Tabs defaultValue="active">
            <TabsList>
              <TabsTrigger value="active">Active Associations</TabsTrigger>
              <TabsTrigger value="pending">Pending Requests {pendingAssociations.length > 0 && `(${pendingAssociations.length})`}</TabsTrigger>
              <TabsTrigger value="add">Add Association</TabsTrigger>
            </TabsList>

            <TabsContent value="active" className="space-y-4">
              {loading ? (
                <div className="flex justify-center p-4">
                  <Loader2 className="h-8 w-8 animate-spin text-primary" />
                </div>
              ) : associations.length > 0 ? (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Farm Name</TableHead>
                      <TableHead>Association Type</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Created</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {associations.map((association) => {
                      // Determine which farm to display (the one that's not the current farm)
                      const otherFarm = 
                        association.initiator_farm_id === farmId 
                          ? association.associatedFarm 
                          : association.initiatorFarm;

                      return (
                        <React.Fragment key={association.id}>
                          <TableRow>
                            <TableCell>{otherFarm?.name}</TableCell>
                            <TableCell>{association.association_type}</TableCell>
                            <TableCell>
                              <StatusBadge status={association.status} />
                            </TableCell>
                            <TableCell>{new Date(association.created_at).toLocaleDateString()}</TableCell>
                            <TableCell>
                              <div className="flex space-x-2">
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => {
                                    setSelectedAssociation(association);
                                    setEditAssociationType(association.association_type);
                                    setEditModalOpen(true);
                                  }}
                                >
                                  <Edit className="h-4 w-4 mr-1" /> Edit
                                </Button>
                                <Button
                                  variant="destructive"
                                  size="sm"
                                  onClick={() => removeAssociation(association.id)}
                                >
                                  <X className="h-4 w-4 mr-1" /> Remove
                                </Button>
                              </div>
                            </TableCell>
                          </TableRow>
                        </React.Fragment>
                      );
                    })}
                  </TableBody>
                </Table>
              ) : (
                <div className="text-center p-4">
                  <p>No active associations found.</p>
                </div>
              )}
            </TabsContent>

            <TabsContent value="pending" className="space-y-4">
              {pendingAssociations.length > 0 ? (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Farm Name</TableHead>
                      <TableHead>Association Type</TableHead>
                      <TableHead>Requested</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {pendingAssociations.map((association) => (
                      <TableRow key={association.id}>
                        <TableCell>{association.initiatorFarm?.name}</TableCell>
                        <TableCell>{association.association_type}</TableCell>
                        <TableCell>{new Date(association.created_at).toLocaleDateString()}</TableCell>
                        <TableCell className="flex space-x-2">
                          <Button
                            variant="default"
                            size="sm"
                            onClick={() => updateAssociationStatus(association.id, 'active')}
                          >
                            <Check className="h-4 w-4 mr-1" /> Approve
                          </Button>
                          <Button
                            variant="destructive"
                            size="sm"
                            onClick={() => updateAssociationStatus(association.id, 'rejected')}
                          >
                            <X className="h-4 w-4 mr-1" /> Reject
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              ) : (
                <div className="text-center p-4">
                  <p>No pending association requests.</p>
                </div>
              )}
            </TabsContent>

            <TabsContent value="add" className="space-y-4">
              <div className="space-y-4">
                <div>
                  <Label htmlFor="search">Search for farms</Label>
                  <div className="flex space-x-2">
                    <Input
                      id="search"
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      placeholder="Enter farm name"
                    />
                    <Button onClick={searchFarms} disabled={searching || !searchTerm}>
                      {searching ? <Loader2 className="h-4 w-4 animate-spin" /> : 'Search'}
                    </Button>
                  </div>
                </div>

                {searchResults.length > 0 && (
                  <div className="space-y-2">
                    <Label>Search Results</Label>
                    <div className="border rounded-md p-2 max-h-60 overflow-y-auto">
                      {searchResults.map((farm) => (
                        <div
                          key={farm.id}
                          className={`p-2 rounded-md cursor-pointer hover:bg-gray-100 ${
                            selectedFarm?.id === farm.id ? 'bg-gray-100' : ''
                          }`}
                          onClick={() => setSelectedFarm(farm)}
                        >
                          {farm.name}
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {selectedFarm && (
                  <div className="space-y-2">
                    <Label htmlFor="associationType">Association Type</Label>
                    <select
                      id="associationType"
                      className="w-full p-2 border rounded-md"
                      value={associationType}
                      onChange={(e) => setAssociationType(e.target.value)}
                    >
                      <option value="general">General</option>
                      <option value="business">Business</option>
                      <option value="supplier">Supplier</option>
                      <option value="customer">Customer</option>
                    </select>

                    <Button
                      className="w-full"
                      onClick={createAssociation}
                      disabled={submitting}
                    >
                      {submitting ? (
                        <Loader2 className="h-4 w-4 animate-spin mr-2" />
                      ) : (
                        <Plus className="h-4 w-4 mr-2" />
                      )}
                      Request Association with {selectedFarm.name}
                    </Button>
                  </div>
                )}
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
};

export default FarmAssociations;
