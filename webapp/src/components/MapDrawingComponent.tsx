import React, { useState, useEffect, useRef, useCallback } from 'react';
import { GoogleMap, DrawingManager, Polygon } from '@react-google-maps/api';
import { GOOGLE_MAPS_API_KEY } from '../config';
import { loadGoogleMapsApi, isGoogleMapsLoaded } from '../utils/googleMapsLoader';

// Define the shape of the coordinates for a polygon
interface PolygonCoordinates {
  lat: number;
  lng: number;
}

interface MapDrawingComponentProps {
  value: string; // GeoJSON string
  onChange: (value: string) => void;
  center?: { lat: number; lng: number };
  height?: string;
  width?: string;
}

// Default map center (can be overridden by props)
const defaultCenter = { lat: 39.8283, lng: -98.5795 }; // Center of the US


const MapDrawingComponent: React.FC<MapDrawingComponentProps> = ({
  value,
  onChange,
  center,
  height = '400px',
  width = '100%',
}) => {
  // Reference to the map instance
  const mapRef = useRef<google.maps.Map | null>(null);

  // State for the drawn polygon paths
  const [paths, setPaths] = useState<PolygonCoordinates[][]>([]);

  // State for the map center
  const [mapCenter, setMapCenter] = useState(center || defaultCenter);

  // Custom map container style based on props
  const customMapContainerStyle = {
    width,
    height,
  };

  // Parse GeoJSON from value prop when component mounts or value changes
  useEffect(() => {
    // Clear paths if value is empty
    if (!value || value.trim() === '') {
      setPaths([]);
      return;
    }

    try {
      // Check if value is a valid JSON string
      const geoJson = JSON.parse(value);

      // Check if the parsed JSON is a valid GeoJSON FeatureCollection
      if (geoJson && geoJson.type === 'FeatureCollection') {
        const polygons: PolygonCoordinates[][] = [];

        // Handle case where features array might be empty
        if (geoJson.features && geoJson.features.length > 0) {
          geoJson.features.forEach((feature: any) => {
            if (feature.geometry && feature.geometry.type === 'Polygon' && 
                feature.geometry.coordinates && feature.geometry.coordinates.length > 0) {
              // Convert GeoJSON coordinates to Google Maps LatLng format
              // GeoJSON format is [longitude, latitude], but Google Maps expects {lat, lng}
              const polygon = feature.geometry.coordinates[0].map((coord: number[]) => ({
                lat: coord[1],
                lng: coord[0],
              }));

              polygons.push(polygon);
            }
          });
        }

        setPaths(polygons);
      } else {
        console.warn('Invalid GeoJSON format:', geoJson);
        setPaths([]);
      }
    } catch (error) {
      console.error('Error parsing GeoJSON:', error);
      setPaths([]);
    }
  }, [value]);

  // Update map center when center prop changes
  useEffect(() => {
    if (center) {
      setMapCenter(center);

      // If we have a map instance, pan to the new center and zoom in
      if (mapRef.current) {
        mapRef.current.panTo(center);
        mapRef.current.setZoom(15); // Zoom in to a reasonable level
      }
    }
  }, [center]);

  // Handle map load
  const onMapLoad = useCallback((map: google.maps.Map) => {
    mapRef.current = map;
  }, []);

  // Handle polygon complete event from the drawing manager
  const onPolygonComplete = useCallback((polygon: google.maps.Polygon) => {
    // Get the path of the polygon
    const polygonPath = polygon.getPath();
    const coordinates: PolygonCoordinates[] = [];

    // Convert the path to an array of coordinates
    for (let i = 0; i < polygonPath.getLength(); i++) {
      const point = polygonPath.getAt(i);
      coordinates.push({
        lat: point.lat(),
        lng: point.lng(),
      });
    }

    // Add the first point again to close the polygon (GeoJSON requirement)
    if (coordinates.length > 0) {
      coordinates.push({ ...coordinates[0] });
    }

    // Add the new polygon to the paths state
    const updatedPaths = [...paths, coordinates];
    setPaths(updatedPaths);

    // Convert the updated paths to GeoJSON and call onChange
    const geoJson = pathsToGeoJson(updatedPaths);
    onChange(JSON.stringify(geoJson));

    // Remove the drawn polygon since we're managing our own polygons
    polygon.setMap(null);
  }, [paths, onChange]);

  // Convert polygon paths to GeoJSON format
  const pathsToGeoJson = (polygonPaths: PolygonCoordinates[][]) => {
    const features = polygonPaths.map(path => {
      // Convert Google Maps LatLng format to GeoJSON format
      // GeoJSON format is [longitude, latitude], but Google Maps gives {lat, lng}
      const coordinates = [path.map(point => [point.lng, point.lat])];

      return {
        type: 'Feature',
        geometry: {
          type: 'Polygon',
          coordinates,
        },
        properties: {},
      };
    });

    return {
      type: 'FeatureCollection',
      features,
    };
  };

  // Clear all drawn polygons
  const clearPolygons = useCallback(() => {
    setPaths([]);
    // Create an empty GeoJSON FeatureCollection
    const emptyGeoJson = {
      type: 'FeatureCollection',
      features: []
    };
    onChange(JSON.stringify(emptyGeoJson));
  }, [onChange]);

  // State for Google Maps API loading
  const [isLoaded, setIsLoaded] = useState(false);
  const [loadError, setLoadError] = useState<Error | null>(null);

  // Load the Google Maps API using the googleMapsLoader utility
  useEffect(() => {
    const loadMapsApi = async () => {
      try {
        await loadGoogleMapsApi(['drawing', 'places']);
        setIsLoaded(true);
      } catch (error) {
        console.error('Failed to load Google Maps API:', error);
        setLoadError(error instanceof Error ? error : new Error('Failed to load Google Maps API'));
      }
    };

    if (!isGoogleMapsLoaded()) {
      loadMapsApi();
    } else {
      setIsLoaded(true);
    }
  }, []);

  // Handle loading error
  if (loadError) {
    return (
      <div className="map-drawing-component">
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">Error loading Google Maps API. Please try again later.</span>
        </div>
      </div>
    );
  }

  // Show loading indicator while the API is loading
  if (!isLoaded) {
    return (
      <div className="map-drawing-component">
        <div className="flex justify-center items-center" style={{ height: height }}>
          <div className="text-gray-500">Loading map...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="map-drawing-component">
      <div className="relative">
        <GoogleMap
          mapContainerStyle={customMapContainerStyle}
          center={mapCenter}
          zoom={5}
          onLoad={onMapLoad}
        >
          {/* Drawing Manager for polygon drawing */}
          <DrawingManager
            onPolygonComplete={onPolygonComplete}
            options={{
              drawingControl: true,
              drawingControlOptions: {
                position: google.maps.ControlPosition.TOP_CENTER,
                drawingModes: [google.maps.drawing.OverlayType.POLYGON],
              },
              polygonOptions: {
                fillColor: '#22c55e',
                fillOpacity: 0.3,
                strokeWeight: 2,
                strokeColor: '#22c55e',
                clickable: true,
                editable: true,
                zIndex: 1,
              },
            }}
          />

          {/* Render existing polygons */}
          {paths.map((path, index) => (
            <Polygon
              key={index}
              paths={path}
              options={{
                fillColor: '#22c55e',
                fillOpacity: 0.3,
                strokeWeight: 2,
                strokeColor: '#22c55e',
                clickable: true,
                editable: true,
                zIndex: 1,
              }}
            />
          ))}
        </GoogleMap>

        {/* Clear button */}
        <button
          type="button"
          onClick={clearPolygons}
          className="absolute bottom-2 right-2 bg-white px-3 py-1 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
        >
          Clear All
        </button>
      </div>

      <p className="mt-1 text-sm text-gray-500">
        Draw field boundaries by clicking the polygon tool and then clicking on the map to create points. 
        Click the first point again to complete the polygon.
      </p>
    </div>
  );
};

export default MapDrawingComponent;
