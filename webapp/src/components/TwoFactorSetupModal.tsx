import { useState, useEffect } from 'react';

interface TwoFactorSetupModalProps {
  isOpen: boolean;
  onClose: () => void;
  onCancel?: () => void;
  qrCode: string | null;
  secret: string | null;
  onVerify: (code1: string, code2: string) => Promise<void>;
}

const TwoFactorSetupModal = ({ isOpen, onClose, onCancel, qrCode, secret, onVerify }: TwoFactorSetupModalProps) => {
  const [step, setStep] = useState<'scan' | 'verify'>('scan');
  const [code1, setCode1] = useState('');
  const [code2, setCode2] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);

  // Reset state when modal is opened or closed
  useEffect(() => {
    console.log('TwoFactorSetupModal: isOpen changed to', isOpen);
    if (isOpen) {
      console.log('TwoFactorSetupModal: Resetting state');
      setStep('scan');
      setCode1('');
      setCode2('');
      setError(null);
      setLoading(false);

      // Log the QR code and secret
      console.log('TwoFactorSetupModal: QR code provided:', qrCode ? `${qrCode.substring(0, 50)}...` : 'null');
      console.log('TwoFactorSetupModal: Secret provided:', secret || 'null');
    }
  }, [isOpen, qrCode, secret]);

  const handleNextStep = () => {
    console.log('TwoFactorSetupModal: Moving to verify step');
    setStep('verify');
  };

  const handleVerify = async (e: React.FormEvent) => {
    e.preventDefault();
    console.log('TwoFactorSetupModal: Verifying codes');
    setError(null);
    setLoading(true);

    try {
      await onVerify(code1, code2);
      console.log('TwoFactorSetupModal: Verification successful');
      onClose();
    } catch (err: any) {
      console.error('TwoFactorSetupModal: Verification error', err);
      setError(err.message || 'Failed to verify codes. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  console.log('TwoFactorSetupModal: Rendering with isOpen =', isOpen);
  if (!isOpen) {
    console.log('TwoFactorSetupModal: Not rendering because isOpen is false');
    return null;
  }

  // Check if we have the required data
  if (!qrCode || !secret) {
    console.error('TwoFactorSetupModal: Missing required data - qrCode:', !!qrCode, 'secret:', !!secret);
    // Instead of returning null, show a loading state
    return (
      <div className="fixed inset-0 overflow-y-auto z-50" aria-labelledby="modal-title" role="dialog" aria-modal="true">
        <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
          <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>
          <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-y-auto max-h-[90vh] shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
            <div className="bg-white px-3 pt-4 pb-3 sm:p-6 sm:pb-4">
              <div className="sm:flex sm:items-start">
                <div className="mt-2 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                  <h3 className="text-lg leading-6 font-medium text-gray-900" id="modal-title">
                    Setting Up Two-Factor Authentication
                  </h3>
                  <div className="mt-6 flex justify-center">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-4 border-primary-600"></div>
                  </div>
                  <p className="mt-4 text-base text-gray-500 pb-2">Loading QR code...</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  console.log('TwoFactorSetupModal: Rendering modal content');
  return (
    <div className="fixed inset-0 overflow-y-auto z-50" aria-labelledby="modal-title" role="dialog" aria-modal="true">
      <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        {/* Background overlay */}
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>

        {/* Modal panel */}
        <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-y-auto max-h-[90vh] shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full" style={{border: '2px solid #4f46e5'}}>
          <div className="bg-white px-3 pt-4 pb-3 sm:p-6 sm:pb-4">
            <div className="sm:flex sm:items-start">
              <div className="mt-2 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                <h3 className="text-lg leading-6 font-medium text-gray-900" id="modal-title">
                  Set Up Two-Factor Authentication
                </h3>

                {step === 'scan' ? (
                  <div className="mt-4">
                    <p className="text-sm text-gray-500 mb-4">
                      Scan this QR code with your authenticator app (like Google Authenticator, Authy, or Microsoft Authenticator).
                    </p>

                    {qrCode ? (
                      <div className="flex justify-center mb-4">
                        <img
                          src={qrCode}
                          alt="QR Code for 2FA"
                          className="border border-gray-300 rounded-md"
                          onLoad={() => console.log('QR code image loaded successfully')}
                          onError={(e) => console.error('QR code image failed to load:', e)}
                        />
                      </div>
                    ) : (
                      <div className="flex justify-center mb-4 p-4 bg-gray-100 rounded-md">
                        <p className="text-gray-500">QR code not available. Please try again.</p>
                      </div>
                    )}

                    {secret ? (
                      <div className="mb-6">
                        <p className="text-sm text-gray-500 mb-2">
                          If you can't scan the QR code, enter this code manually in your app:
                        </p>
                        <div className="bg-gray-100 p-2 rounded-md font-mono text-center break-all">
                          {secret}
                        </div>
                      </div>
                    ) : (
                      <div className="mb-6">
                        <p className="text-sm text-gray-500 mb-2">
                          Manual entry code is not available. Please try again.
                        </p>
                      </div>
                    )}

                    <div className="bg-blue-50 border-l-4 border-blue-400 p-4 rounded mb-4">
                      <div className="flex">
                        <div className="flex-shrink-0">
                          <svg className="h-5 w-5 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                        </div>
                        <div className="ml-3">
                          <p className="text-sm text-blue-700">
                            After scanning, you'll need to enter two consecutive codes from your authenticator app to verify the setup.
                          </p>
                        </div>
                      </div>
                    </div>

                    <button
                      type="button"
                      onClick={handleNextStep}
                      className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-3 sm:py-2 bg-primary-600 text-base font-medium text-white hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:w-auto"
                    >
                      Next: Verify Codes
                    </button>
                  </div>
                ) : (
                  <div className="mt-4">
                    <p className="text-sm text-gray-500 mb-4">
                      To verify your setup, please enter two consecutive codes from your authenticator app.
                    </p>

                    {error && (
                      <div className="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 rounded-md mb-4" role="alert">
                        <div className="flex">
                          <div className="flex-shrink-0">
                            <svg className="h-5 w-5 text-red-500" viewBox="0 0 20 20" fill="currentColor">
                              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                            </svg>
                          </div>
                          <div className="ml-3">
                            <p className="text-sm text-red-700">{error}</p>
                          </div>
                        </div>
                      </div>
                    )}

                    <form onSubmit={handleVerify}>
                      <div className="mb-4">
                        <label htmlFor="code1" className="block text-sm font-medium text-gray-700 mb-1">First Code</label>
                        <input
                          id="code1"
                          name="code1"
                          type="text"
                          required
                          className="appearance-none block w-full px-4 py-3 text-base border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                          placeholder="Enter first 6-digit code"
                          value={code1}
                          onChange={(e) => setCode1(e.target.value)}
                          autoComplete="off"
                          maxLength={6}
                          pattern="[0-9]*"
                          inputMode="numeric"
                        />
                      </div>

                      <div className="mb-4">
                        <label htmlFor="code2" className="block text-sm font-medium text-gray-700 mb-1">Second Code (wait for the first code to change)</label>
                        <input
                          id="code2"
                          name="code2"
                          type="text"
                          required
                          className="appearance-none block w-full px-4 py-3 text-base border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                          placeholder="Enter second 6-digit code"
                          value={code2}
                          onChange={(e) => setCode2(e.target.value)}
                          autoComplete="off"
                          maxLength={6}
                          pattern="[0-9]*"
                          inputMode="numeric"
                        />
                      </div>

                      <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 rounded mb-4">
                        <div className="flex">
                          <div className="flex-shrink-0">
                            <svg className="h-5 w-5 text-yellow-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                            </svg>
                          </div>
                          <div className="ml-3">
                            <p className="text-sm text-yellow-700">
                              Wait for the first code to change in your authenticator app before entering the second code. This ensures the codes are consecutive.
                            </p>
                          </div>
                        </div>
                      </div>

                      <div className="flex flex-col sm:flex-row sm:justify-end space-y-2 sm:space-y-0 sm:space-x-3">
                        <button
                          type="button"
                          onClick={() => setStep('scan')}
                          className="inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-3 sm:py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                        >
                          Back
                        </button>
                        <button
                          type="submit"
                          disabled={loading || code1.length !== 6 || code2.length !== 6}
                          className={`inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-3 sm:py-2 bg-primary-600 text-base font-medium text-white hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 ${(loading || code1.length !== 6 || code2.length !== 6) ? 'opacity-70 cursor-not-allowed' : ''}`}
                        >
                          {loading ? 'Verifying...' : 'Verify and Enable 2FA'}
                        </button>
                      </div>
                    </form>
                  </div>
                )}
              </div>
            </div>
          </div>
          <div className="bg-gray-50 px-4 py-4 sm:px-6 flex justify-center sm:justify-end">
            <button
              type="button"
              onClick={() => {
                // If onCancel is provided, call it (this will clear localStorage)
                // Otherwise just close the modal
                if (onCancel) {
                  onCancel();
                } else {
                  onClose();
                }
              }}
              className="w-full sm:w-auto inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-3 sm:py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              Cancel
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TwoFactorSetupModal;
