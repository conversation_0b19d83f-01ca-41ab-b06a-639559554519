import { useState, useEffect } from 'react';
import { Line } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  ChartOptions
} from 'chart.js';
import axios from 'axios';
import { API_URL } from '../config';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend
);

interface InventoryData {
  date: string;
  productId: string;
  productName: string;
  quantity: number;
}

interface Product {
  id: string;
  name: string;
}

const InventoryLevelsWidget = () => {
  const [inventoryData, setInventoryData] = useState<InventoryData[]>([]);
  const [products, setProducts] = useState<Product[]>([]);
  const [selectedProducts, setSelectedProducts] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [timeRange, setTimeRange] = useState<'7days' | '30days' | '90days'>('30days');

  // Fetch products
  useEffect(() => {
    const fetchProducts = async () => {
      try {
        const response = await axios.get(`${API_URL}/api/inventory/products`, {
          withCredentials: true
        });
        
        if (response.data && response.data.products) {
          setProducts(response.data.products);
          // Select first 5 products by default (or all if less than 5)
          const initialSelection = response.data.products
            .slice(0, 5)
            .map((product: Product) => product.id);
          setSelectedProducts(initialSelection);
        }
      } catch (err) {
        console.error('Error fetching products:', err);
        setError('Failed to load products. Please try again later.');
      }
    };

    fetchProducts();
  }, []);

  // Fetch inventory data when selected products or time range changes
  useEffect(() => {
    const fetchInventoryData = async () => {
      if (selectedProducts.length === 0) {
        setInventoryData([]);
        setLoading(false);
        return;
      }

      setLoading(true);
      setError(null);

      try {
        const response = await axios.get(`${API_URL}/api/inventory/history`, {
          params: { 
            productIds: selectedProducts.join(','),
            timeRange 
          },
          withCredentials: true
        });
        
        if (response.data && response.data.history) {
          setInventoryData(response.data.history);
        } else {
          setInventoryData([]);
        }
      } catch (err) {
        console.error('Error fetching inventory data:', err);
        setError('Failed to load inventory data. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchInventoryData();
  }, [selectedProducts, timeRange]);

  // Handle product selection change
  const handleProductChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const options = e.target.options;
    const selectedValues: string[] = [];
    
    for (let i = 0; i < options.length; i++) {
      if (options[i].selected) {
        selectedValues.push(options[i].value);
      }
    }
    
    setSelectedProducts(selectedValues);
  };

  // Handle time range change
  const handleTimeRangeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setTimeRange(e.target.value as '7days' | '30days' | '90days');
  };

  // Group data by product and date
  const groupedData = inventoryData.reduce((acc, item) => {
    if (!acc[item.productId]) {
      acc[item.productId] = {
        name: item.productName,
        data: {}
      };
    }
    acc[item.productId].data[item.date] = item.quantity;
    return acc;
  }, {} as Record<string, { name: string; data: Record<string, number> }>);

  // Get unique dates and sort them
  const dates = [...new Set(inventoryData.map(item => item.date))].sort();

  // Prepare chart data
  const chartData = {
    labels: dates,
    datasets: Object.entries(groupedData).map(([productId, product], index) => {
      // Generate a color based on index
      const hue = (index * 137) % 360; // Use golden ratio to spread colors
      const color = `hsl(${hue}, 70%, 60%)`;
      
      return {
        label: product.name,
        data: dates.map(date => product.data[date] || null),
        borderColor: color,
        backgroundColor: `hsla(${hue}, 70%, 60%, 0.2)`,
        borderWidth: 2,
        pointRadius: 3,
        tension: 0.1
      };
    })
  };

  // Chart options
  const chartOptions: ChartOptions<'line'> = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top',
      },
      title: {
        display: true,
        text: 'Inventory Levels Over Time',
        font: {
          size: 16
        }
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            let label = context.dataset.label || '';
            if (label) {
              label += ': ';
            }
            if (context.parsed.y !== null) {
              label += context.parsed.y.toFixed(0) + ' units';
            }
            return label;
          }
        }
      }
    },
    scales: {
      x: {
        title: {
          display: true,
          text: 'Date'
        }
      },
      y: {
        title: {
          display: true,
          text: 'Quantity'
        },
        beginAtZero: true
      }
    }
  };

  return (
    <div className="h-full flex flex-col">
      <div className="mb-4 flex flex-wrap justify-between items-center gap-2">
        <div className="text-sm font-medium text-gray-500 flex items-center">
          <span>Time Range:</span>
          <select
            value={timeRange}
            onChange={handleTimeRangeChange}
            className="ml-2 p-1 border border-gray-300 rounded"
          >
            <option value="7days">Last 7 Days</option>
            <option value="30days">Last 30 Days</option>
            <option value="90days">Last 90 Days</option>
          </select>
        </div>
        
        <div className="text-sm font-medium text-gray-500 flex items-center">
          <span>Products:</span>
          <select
            multiple
            value={selectedProducts}
            onChange={handleProductChange}
            className="ml-2 p-1 border border-gray-300 rounded h-24"
            style={{ minWidth: '200px' }}
          >
            {products.map(product => (
              <option key={product.id} value={product.id}>{product.name}</option>
            ))}
          </select>
          <span className="ml-2 text-xs text-gray-400">(Hold Ctrl/Cmd to select multiple)</span>
        </div>
      </div>

      <div className="flex-grow relative">
        {loading ? (
          <div className="absolute inset-0 flex items-center justify-center">
            <p className="text-sm text-gray-500">Loading inventory data...</p>
          </div>
        ) : error ? (
          <div className="absolute inset-0 flex items-center justify-center">
            <p className="text-sm text-red-500">{error}</p>
          </div>
        ) : inventoryData.length === 0 ? (
          <div className="absolute inset-0 flex items-center justify-center">
            <p className="text-sm text-gray-500">
              {selectedProducts.length === 0 
                ? 'Please select at least one product to view inventory data.' 
                : 'No inventory data available for the selected products and time range.'}
            </p>
          </div>
        ) : (
          <Line data={chartData} options={chartOptions} />
        )}
      </div>
    </div>
  );
};

export default InventoryLevelsWidget;