import React from 'react';
import { analyzePasswordStrength, getPasswordStrengthColor } from '../utils/passwordStrength';

interface PasswordStrengthMeterProps {
  password: string;
  className?: string;
}

/**
 * A component that displays a password strength meter and feedback
 */
const PasswordStrengthMeter: React.FC<PasswordStrengthMeterProps> = ({ 
  password, 
  className = '' 
}) => {
  const { score, level, feedback } = analyzePasswordStrength(password);
  const color = getPasswordStrengthColor(score);
  
  // Don't show anything if password is empty
  if (!password) {
    return null;
  }

  return (
    <div className={`mt-2 ${className}`}>
      {/* Strength meter bar */}
      <div className="w-full h-2 bg-gray-200 rounded-full overflow-hidden">
        <div 
          className="h-full transition-all duration-300 ease-in-out"
          style={{ 
            width: `${score}%`, 
            backgroundColor: color 
          }}
        />
      </div>
      
      {/* Strength level text */}
      <div className="flex justify-between items-center mt-1">
        <span 
          className="text-xs font-medium capitalize"
          style={{ color }}
        >
          {level}
        </span>
        <span className="text-xs text-gray-500">
          {score}/100
        </span>
      </div>
      
      {/* Feedback messages */}
      {feedback.length > 0 && (
        <ul className="mt-2 text-xs text-gray-600 space-y-1">
          {feedback.map((item, index) => (
            <li key={index} className="flex items-start">
              <span className="mr-1">•</span>
              <span>{item}</span>
            </li>
          ))}
        </ul>
      )}
    </div>
  );
};

export default PasswordStrengthMeter;