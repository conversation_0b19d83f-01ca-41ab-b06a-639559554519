import React, { useState, useEffect } from 'react';
import { ShoppingCart } from 'lucide-react';
import { Button } from '../ui/Button';
import { useCart } from '../../context/CartContext';
import CartSidebar from './CartSidebar';

interface CartButtonProps {
  farmId: string;
}

const CartButton: React.FC<CartButtonProps> = ({ farmId }) => {
  const [isCartOpen, setIsCartOpen] = useState(false);
  const { cart, fetchCart } = useCart();

  useEffect(() => {
    // Fetch cart on initial load
    fetchCart(farmId);

    // Set up interval to refresh cart every minute
    const intervalId = setInterval(() => {
      fetchCart(farmId);
    }, 60000); // 1 minute

    return () => clearInterval(intervalId);
  }, [farmId, fetchCart]);

  const openCart = () => {
    setIsCartOpen(true);
  };

  const closeCart = () => {
    setIsCartOpen(false);
  };

  const itemCount = cart?.itemCount || 0;

  return (
    <>
      <Button
        variant="outline"
        className="relative"
        onClick={openCart}
        aria-label={`Open cart with ${itemCount} items`}
      >
        <ShoppingCart className="h-5 w-5 mr-2" />
        <span>Cart</span>
        {itemCount > 0 && (
          <span className="absolute -top-2 -right-2 bg-primary text-primary-foreground text-xs rounded-full h-5 w-5 flex items-center justify-center">
            {itemCount}
          </span>
        )}
      </Button>

      <CartSidebar 
        isOpen={isCartOpen} 
        onClose={closeCart} 
        farmId={farmId} 
      />
    </>
  );
};

export default CartButton;
