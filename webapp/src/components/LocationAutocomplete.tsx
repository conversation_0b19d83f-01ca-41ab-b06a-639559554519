import React, { useState, useEffect, useRef } from 'react';
import { loadGoogleMapsApi, isGoogleMapsPlacesLoaded } from '../utils/googleMapsLoader';

interface LocationAutocompleteProps {
  value: string;
  onChange: (value: string, coordinates?: { lat: number; lng: number }, addressComponents?: any) => void;
  placeholder?: string;
  className?: string;
  onCoordinatesChange?: (coordinates: { lat: number; lng: number }) => void;
}

interface AddressComponent {
  long_name: string;
  short_name: string;
  types: string[];
}

interface AddressComponents {
  streetNumber?: string;
  street?: string;
  city?: string;
  state?: string;
  zipCode?: string;
  country?: string;
  streetAddress?: string;
}

const LocationAutocomplete: React.FC<LocationAutocompleteProps> = ({
  value,
  onChange,
  placeholder = "Enter a location",
  className = "",
  onCoordinatesChange
}) => {
  const [inputValue, setInputValue] = useState(value);
  const inputRef = useRef<HTMLInputElement>(null);
  const autocompleteRef = useRef<google.maps.places.Autocomplete | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // Update input value when prop changes
  useEffect(() => {
    setInputValue(value);
  }, [value]);

  // Load Google Maps API
  useEffect(() => {
    const loadMapsApi = async () => {
      try {
        await loadGoogleMapsApi(['places']);
        setIsLoading(false);
      } catch (error) {
        console.error('Failed to load Google Maps API:', error);
        setIsLoading(false);
      }
    };

    loadMapsApi();
  }, []);

  // Initialize Google Places Autocomplete
  useEffect(() => {
    if (isInitialized || isLoading || !inputRef.current) return;

    if (!isGoogleMapsPlacesLoaded()) {
      console.error('Google Maps Places API not loaded correctly');
      return;
    }

    try {
      // Create the Autocomplete instance
      autocompleteRef.current = new google.maps.places.Autocomplete(inputRef.current, {
        types: ['address'],
      });

      // Add event listener for place selection
      autocompleteRef.current.addListener('place_changed', () => {
        const place = autocompleteRef.current?.getPlace();

        if (!place) {
          console.error('No place data available');
          return;
        }

        // Get the formatted address
        const address = place.formatted_address || '';
        setInputValue(address);

        // Extract address components
        let addressComponents: AddressComponents = {};
        let streetNumber = '';
        let street = '';

        if (place.address_components) {
          // First pass: extract all components by their primary type
          place.address_components.forEach((component: AddressComponent) => {
            const componentType = component.types[0];

            switch (componentType) {
              case 'street_number':
                streetNumber = component.long_name;
                addressComponents = { ...addressComponents, streetNumber: component.long_name };
                break;
              case 'route':
                street = component.long_name;
                addressComponents = { ...addressComponents, street: component.long_name };
                break;
              case 'locality':
                addressComponents = { ...addressComponents, city: component.long_name };
                break;
              case 'sublocality':
              case 'sublocality_level_1':
                // Use sublocality as city if locality is not available
                if (!addressComponents.city) {
                  addressComponents = { ...addressComponents, city: component.long_name };
                }
                break;
              case 'administrative_area_level_1':
                addressComponents = { ...addressComponents, state: component.short_name };
                break;
              case 'administrative_area_level_2':
                // Use as state fallback if level_1 is not available
                if (!addressComponents.state) {
                  addressComponents = { ...addressComponents, state: component.short_name };
                }
                break;
              case 'postal_code':
                addressComponents = { ...addressComponents, zipCode: component.long_name };
                break;
              case 'postal_code_prefix':
                // Use as zipCode fallback if postal_code is not available
                if (!addressComponents.zipCode) {
                  addressComponents = { ...addressComponents, zipCode: component.long_name };
                }
                break;
              case 'country':
                // Use short_name for country code (e.g., "US" instead of "United States")
                addressComponents = { ...addressComponents, country: component.short_name };
                break;
            }
          });

          // Construct street address from street number and street
          if (streetNumber && street) {
            addressComponents = { ...addressComponents, streetAddress: `${streetNumber} ${street}` };
          } else if (street) {
            // If we only have the street name
            addressComponents = { ...addressComponents, streetAddress: street };
          } else if (streetNumber) {
            // If we only have the street number (unlikely but possible)
            addressComponents = { ...addressComponents, streetAddress: streetNumber };
          } else {
            // If we don't have either, use the formatted address as fallback for the street address
            addressComponents = { ...addressComponents, streetAddress: address };
          }

          // Additional fallback for city using neighborhood if still not found
          if (!addressComponents.city) {
            const neighborhood = place.address_components.find(comp => comp.types.includes('neighborhood'));
            if (neighborhood) {
              addressComponents = { ...addressComponents, city: neighborhood.long_name };
            }
          }

          // Ensure state is always a two-letter code for US addresses
          if ('country' in addressComponents && addressComponents.country &&
              (addressComponents.country === 'US' || addressComponents.country === 'USA')) {
            // If we have a state but it's not a two-letter code, try to convert it
            if ('state' in addressComponents && addressComponents.state && addressComponents.state.length > 2) {
              // Find the state component again and use short_name
              const stateComponent = place.address_components.find(comp =>
                comp.types.includes('administrative_area_level_1'));
              if (stateComponent) {
                addressComponents = { ...addressComponents, state: stateComponent.short_name };
              }
            }
          }

          // Log missing components for debugging
          const missingComponents = [];
          if (!addressComponents.city) missingComponents.push('city');
          if (!addressComponents.state) missingComponents.push('state');
          if (!addressComponents.zipCode) missingComponents.push('zipCode');

          if (missingComponents.length > 0) {
            console.log('Missing address components:', missingComponents.join(', '));
          }
        }

        // Get coordinates if available
        if (place.geometry && place.geometry.location) {
          const lat = place.geometry.location.lat();
          const lng = place.geometry.location.lng();
          const coordinates = { lat, lng };

          // Log the extracted address components for debugging
          console.log('Extracted address components:', addressComponents);

          // Call onChange with the street address, coordinates, and address components
          onChange(addressComponents.streetAddress ? addressComponents.streetAddress : address, coordinates, addressComponents);

          // Call onCoordinatesChange if provided
          if (onCoordinatesChange) {
            onCoordinatesChange(coordinates);
          }
        } else {
          // Log the extracted address components for debugging
          console.log('Extracted address components (no coordinates):', addressComponents);

          // Call onChange with just the street address and components if no coordinates
          onChange(addressComponents.streetAddress ? addressComponents.streetAddress : address, undefined, addressComponents);
        }
      });

      setIsInitialized(true);
    } catch (error) {
      console.error('Error initializing Google Places Autocomplete:', error);
    }

    return () => {
      // Clean up when component unmounts
      if (autocompleteRef.current && window.google && window.google.maps) {
        google.maps.event.clearInstanceListeners(autocompleteRef.current);
      }
    };
  }, [isInitialized, isLoading, onChange, onCoordinatesChange]);

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setInputValue(newValue);

    // Call onChange with the new value
    // This ensures that manual typing also updates the parent component
    onChange(newValue);
  };

  return (
    <input
      ref={inputRef}
      type="text"
      value={inputValue}
      onChange={handleInputChange}
      placeholder={isLoading ? "Loading..." : placeholder}
      disabled={isLoading}
      autoComplete="off"
      className={`block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm ${className}`}
    />
  );
};

export default LocationAutocomplete;
