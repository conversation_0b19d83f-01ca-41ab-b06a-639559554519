import React, { useState, useEffect } from 'react';
import { 
  generatePassword, 
  DEFAULT_PASSWORD_OPTIONS, 
  PasswordGeneratorOptions 
} from '../utils/passwordGenerator';
import { Button } from './ui/Button';
import { Input } from './ui/Input';
import { <PERSON>lide<PERSON> } from './ui/Slider';
import { Checkbox } from './ui/Checkbox';
import PasswordStrengthMeter from './PasswordStrengthMeter';
import { FaRandom, FaCopy, FaCheck } from 'react-icons/fa';

interface PasswordGeneratorProps {
  onSelectPassword?: (password: string) => void;
  className?: string;
}

/**
 * A component that generates secure passwords with configurable options
 */
const PasswordGenerator: React.FC<PasswordGeneratorProps> = ({
  onSelectPassword,
  className = ''
}) => {
  const [options, setOptions] = useState<PasswordGeneratorOptions>(DEFAULT_PASSWORD_OPTIONS);
  const [generatedPassword, setGeneratedPassword] = useState('');
  const [copied, setCopied] = useState(false);

  // Generate a password on initial render
  useEffect(() => {
    handleGeneratePassword();
  }, []);

  // Generate a new password when options change
  useEffect(() => {
    try {
      const newPassword = generatePassword(options);
      setGeneratedPassword(newPassword);
    } catch (error) {
      console.error('Error generating password:', error);
    }
  }, [options]);

  const handleGeneratePassword = () => {
    try {
      const newPassword = generatePassword(options);
      setGeneratedPassword(newPassword);
    } catch (error) {
      console.error('Error generating password:', error);
    }
  };

  const handleCopyPassword = () => {
    if (generatedPassword) {
      navigator.clipboard.writeText(generatedPassword);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    }
  };

  const handleUsePassword = () => {
    if (onSelectPassword && generatedPassword) {
      onSelectPassword(generatedPassword);
    }
  };

  const handleOptionChange = (name: keyof PasswordGeneratorOptions, value: boolean | number) => {
    setOptions(prev => ({
      ...prev,
      [name]: value
    }));
  };

  return (
    <div className={`bg-white rounded-lg shadow-md p-4 ${className}`}>
      <h3 className="text-lg font-semibold mb-4">Password Generator</h3>

      {/* Generated password display */}
      <div className="mb-4">
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Generated Password
        </label>
        <div className="relative">
          <Input
            type="text"
            value={generatedPassword}
            readOnly
            className="pr-20 font-mono"
          />
          <div className="absolute inset-y-0 right-0 flex items-center pr-2">
            <button
              type="button"
              onClick={handleCopyPassword}
              className="p-1 text-gray-500 hover:text-gray-700 focus:outline-none"
              title="Copy to clipboard"
            >
              {copied ? <FaCheck className="text-green-500" /> : <FaCopy />}
            </button>
            <button
              type="button"
              onClick={handleGeneratePassword}
              className="p-1 ml-1 text-gray-500 hover:text-gray-700 focus:outline-none"
              title="Generate new password"
            >
              <FaRandom />
            </button>
          </div>
        </div>

        {/* Password strength meter */}
        <PasswordStrengthMeter password={generatedPassword} />
      </div>

      {/* Password length slider */}
      <div className="mb-4">
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Password Length: {options.length}
        </label>
        <Slider
          min={8}
          max={32}
          value={options.length}
          onChange={(value) => handleOptionChange('length', value)}
        />
      </div>

      {/* Character type options */}
      <div className="mb-4 space-y-2">
        <Checkbox
          id="includeUppercase"
          label="Include Uppercase Letters (A-Z)"
          checked={options.includeUppercase}
          onChange={(e) => handleOptionChange('includeUppercase', e.target.checked)}
        />
        <Checkbox
          id="includeLowercase"
          label="Include Lowercase Letters (a-z)"
          checked={options.includeLowercase}
          onChange={(e) => handleOptionChange('includeLowercase', e.target.checked)}
        />
        <Checkbox
          id="includeNumbers"
          label="Include Numbers (0-9)"
          checked={options.includeNumbers}
          onChange={(e) => handleOptionChange('includeNumbers', e.target.checked)}
        />
        <Checkbox
          id="includeSpecial"
          label="Include Special Characters (!@#$%^&*)"
          checked={options.includeSpecial}
          onChange={(e) => handleOptionChange('includeSpecial', e.target.checked)}
        />
      </div>

      {/* Advanced options */}
      <div className="mb-4 space-y-2">
        <Checkbox
          id="excludeSimilar"
          label="Exclude Similar Characters (i, l, 1, L, o, 0, O)"
          checked={options.excludeSimilar}
          onChange={(e) => handleOptionChange('excludeSimilar', e.target.checked)}
        />
        <Checkbox
          id="excludeAmbiguous"
          label="Exclude Ambiguous Characters ({}, [], (), /, \, etc.)"
          checked={options.excludeAmbiguous}
          onChange={(e) => handleOptionChange('excludeAmbiguous', e.target.checked)}
        />
      </div>

      {/* Action buttons */}
      <div className="flex justify-end space-x-2">
        <Button
          variant="secondary"
          onClick={handleGeneratePassword}
        >
          <FaRandom className="mr-2" /> Generate New
        </Button>
        {onSelectPassword && (
          <Button
            variant="primary"
            onClick={handleUsePassword}
          >
            Use This Password
          </Button>
        )}
      </div>
    </div>
  );
};

export default PasswordGenerator;
