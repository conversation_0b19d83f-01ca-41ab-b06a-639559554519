import { ReactNode, useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useMenuPreferences } from '../hooks/useMenuPreferences';
import Footer from './Footer';
import Header from './Header';
import ImpersonationBanner from './ImpersonationBanner';
import { ChevronDownIcon, ChevronRightIcon } from '@heroicons/react/24/outline';

interface LayoutProps {
  children: ReactNode;
}

// NavGroup component for collapsible sidebar sections
interface NavGroupProps {
  title: string;
  children: ReactNode;
  defaultOpen?: boolean;
  isActive: boolean;
}

const NavGroup = ({ title, children, defaultOpen = false, isActive }: NavGroupProps) => {
  const [isOpen, setIsOpen] = useState(defaultOpen || isActive);

  return (
    <div className="mb-2">
      <button
        className={`w-full flex items-center px-4 py-2 text-left rounded-md ${
          isActive ? 'bg-primary-50 text-primary-600' : 'text-gray-700 hover:bg-gray-100'
        }`}
        onClick={() => setIsOpen(!isOpen)}
      >
        <span className="flex-1">{title}</span>
        {isOpen ? (
          <ChevronDownIcon className="h-4 w-4" />
        ) : (
          <ChevronRightIcon className="h-4 w-4" />
        )}
      </button>
      {isOpen && (
        <div className="ml-4 pl-2 border-l border-gray-200">
          {children}
        </div>
      )}
    </div>
  );
};

// Sidebar link component for consistent styling
const SidebarLink = ({ to, icon, children, isActive }: { to: string; icon: React.ReactNode; children: React.ReactNode; isActive: boolean }) => (
  <Link 
    to={to} 
    className={`flex items-center px-4 py-2 mb-2 rounded-md ${
      isActive 
        ? 'bg-primary-50 text-primary-600' 
        : 'text-gray-700 hover:bg-gray-100'
    }`}
  >
    {icon}
    {children}
  </Link>
);

const Layout = ({ children }: LayoutProps) => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [layoutError, setLayoutError] = useState<string | null>(null);

  // Safely get menu preferences with error handling
  let sidebarCategories: any[] = [];
  let menuLoading = true;

  try {
    const menuPrefs = useMenuPreferences();
    sidebarCategories = menuPrefs.sidebarCategories || [];
    menuLoading = menuPrefs.loading;
  } catch (error) {
    console.error('Layout: Error loading menu preferences:', error);
    setLayoutError('Failed to load menu preferences');
    sidebarCategories = [];
    menuLoading = false;
  }

  const location = useLocation();

  // Check if the current route is active
  const isActive = (path: string) => {
    return location.pathname === path;
  };

  // Check if any path in a group is active
  const isGroupActive = (paths: string[]) => {
    return paths.some(path => 
      location.pathname === path || location.pathname.startsWith(`${path}/`)
    );
  };

  // Check if we're in the support section
  const isSupport = location.pathname.startsWith('/support');

  return (
    <div className="min-h-screen flex flex-col bg-gray-50 overflow-x-hidden">
      <ImpersonationBanner />
      <div className="flex flex-1">
        {/* Mobile sidebar backdrop - hide for support pages */}
        {!isSupport && sidebarOpen && (
          <div 
            className="fixed inset-0 z-20 bg-black bg-opacity-50 transition-opacity lg:hidden"
            onClick={() => setSidebarOpen(false)}
          ></div>
        )}

        {/* Sidebar - hide for support pages */}
        {!isSupport && (
          <div className={`fixed inset-y-0 left-0 z-30 w-64 transform bg-white shadow-lg transition duration-300 lg:translate-x-0 lg:static lg:inset-0 ${
            sidebarOpen ? 'translate-x-0' : '-translate-x-full'
          }`}>
            <div className="flex h-full flex-col overflow-y-auto">
              {/* Sidebar header */}
              <div className="relative h-16 flex items-center justify-center px-4 border-b border-gray-200">
                <Link to="/dashboard" className="flex items-center justify-center">
                  <img 
                    src="/logo.svg" 
                    alt="Logo"
                    className="h-8 w-auto"
                    onError={(e) => {
                      // Fallback if logo doesn't exist
                      const target = e.currentTarget;
                      target.onerror = null;
                      target.style.display = 'none';
                      const parent = target.parentElement;
                      if (parent) {
                        const span = document.createElement('span');
                        span.className = 'text-xl font-semibold text-primary-600';
                        span.textContent = 'nxtAcre';
                        parent.appendChild(span);
                      }
                    }}
                  />
                </Link>
                <button 
                  className="absolute right-4 lg:hidden"
                  onClick={() => setSidebarOpen(false)}
                >
                  <svg className="h-6 w-6 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              {/* Sidebar content */}
              <nav className="flex-1 px-2 py-4">
                {!menuLoading && sidebarCategories.map(category => {
                  // Only proceed if the category has at least one visible item
                  const visibleItems = category.items.filter(item => item.isVisible);
                  if (visibleItems.length === 0) return null;

                  // Get all paths for this category to check if any are active
                  const categoryPaths = visibleItems.map(item => item.path);

                  return (
                    <NavGroup 
                      key={category.id}
                      title={category.title} 
                      isActive={isGroupActive(categoryPaths)}
                      defaultOpen={false}
                    >
                      {visibleItems.map(item => (
                        <SidebarLink 
                          key={item.id}
                          to={item.path} 
                          icon={
                            <svg className="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                            </svg>
                          }
                          isActive={isActive(item.path) || location.pathname.startsWith(`${item.path}/`)}
                        >
                          {item.title}
                        </SidebarLink>
                      ))}
                    </NavGroup>
                  );
                })}
              </nav>
            </div>
          </div>
        )}

        {/* Main content */}
        <div className="flex flex-col flex-1">
          {/* Header */}
            <Header />

          {/* Mobile menu button - hide for support pages */}
          {!isSupport && (
            <div className="sticky top-0 z-10 flex h-10 bg-white shadow-sm lg:hidden">
              <button
                type="button"
                className="px-4 text-gray-500 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500"
                onClick={() => setSidebarOpen(true)}
              >
                <span className="sr-only">Open sidebar</span>
                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                </svg>
              </button>
              <div className="flex flex-1 items-center px-4">
                <h1 className="text-lg font-semibold text-gray-900">
                  {/* Dashboard & Analytics */}
                  {location.pathname === '/dashboard' && 'Dashboard & Analytics'}
                  {location.pathname === '/reports' && 'Dashboard & Analytics'}

                  {/* Financial Management */}
                  {location.pathname === '/transactions' && 'Financial Management'}
                  {location.pathname === '/invoices' && 'Financial Management'}
                  {location.pathname === '/customers' && 'Financial Management'}
                  {location.pathname === '/products' && 'Financial Management'}
                  {location.pathname === '/link-account' && 'Financial Management'}
                  {location.pathname === '/farms' && 'Financial Management'}
                  {location.pathname === '/bills' && 'Financial Management'}
                  {location.pathname.startsWith('/bills/') && 'Financial Management'}

                  {/* Farm Operations */}
                  {location.pathname === '/fields' && 'Farm Operations'}
                  {location.pathname === '/crops' && 'Farm Operations'}
                  {location.pathname === '/livestock' && 'Farm Operations'}

                  {/* Resource Management */}
                  {location.pathname === '/inventory' && 'Resource Management'}
                  {location.pathname === '/equipment' && 'Resource Management'}
                  {location.pathname === '/employees' && 'Resource Management'}

                  {/* Document Management */}
                  {location.pathname === '/documents' && 'Document Management'}
                  {location.pathname.startsWith('/documents/view/') && 'Document Management'}
                  {location.pathname === '/documents/folders' && 'Document Management'}
                  {location.pathname.startsWith('/documents/folders/') && 'Document Management'}
                  {location.pathname === '/documents/external' && 'Document Management'}
                  {location.pathname.startsWith('/documents/external/') && 'Document Management'}

                  {/* Tasks */}
                  {location.pathname === '/tasks' && 'Tasks'}

                  {/* User Settings */}
                  {location.pathname === '/profile' && 'User Settings'}
                  {location.pathname === '/setup-2fa' && 'User Settings'}
                </h1>
              </div>
            </div>
          )}

          {/* Page content */}
          <main className="flex-1 overflow-x-hidden">
            <div className="w-full px-4 py-6 overflow-x-auto">
              {children}
            </div>
          </main>

          {/* Footer */}
          <Footer />
        </div>
      </div>
    </div>
  );
};

export default Layout;
