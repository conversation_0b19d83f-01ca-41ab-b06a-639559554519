import { useEffect, useContext } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { AuthContext } from '../context/AuthContext';
import axios from 'axios';
import { API_URL } from '../config';

/**
 * Component that checks for the checkAuth query parameter and authenticates the user if needed
 * This is used to maintain authentication when navigating between subdomains
 */
const SubdomainAuthCheck: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { user, loading, updateUser } = useContext(AuthContext);
  
  useEffect(() => {
    // Parse the query parameters
    const searchParams = new URLSearchParams(location.search);
    const checkAuth = searchParams.get('checkAuth');
    
    // If checkAuth parameter is present and user is not already logged in
    if (checkAuth === 'true' && !loading && !user) {
      const checkAuthCookies = async () => {
        try {
          // Call the checkAuth endpoint to validate auth cookies
          const response = await axios.get(`${API_URL}/auth/check-auth`);
          
          if (response.data && response.data.user) {
            // Update the user state with the authenticated user
            updateUser(response.data.user);
            
            // Remove the checkAuth parameter from the URL
            searchParams.delete('checkAuth');
            const newSearch = searchParams.toString();
            const newPath = location.pathname + (newSearch ? `?${newSearch}` : '');
            
            // Replace the current URL without the checkAuth parameter
            navigate(newPath, { replace: true });
          }
        } catch (error) {
          console.error('Error checking auth cookies:', error);
          // If there's an error, remove the checkAuth parameter from the URL
          searchParams.delete('checkAuth');
          const newSearch = searchParams.toString();
          const newPath = location.pathname + (newSearch ? `?${newSearch}` : '');
          
          // Replace the current URL without the checkAuth parameter
          navigate(newPath, { replace: true });
        }
      };
      
      checkAuthCookies();
    } else if (checkAuth === 'true' && (loading || user)) {
      // If user is already logged in or loading, just remove the checkAuth parameter
      searchParams.delete('checkAuth');
      const newSearch = searchParams.toString();
      const newPath = location.pathname + (newSearch ? `?${newSearch}` : '');
      
      // Replace the current URL without the checkAuth parameter
      navigate(newPath, { replace: true });
    }
  }, [location, user, loading, navigate, updateUser]);
  
  // This component doesn't render anything
  return null;
};

export default SubdomainAuthCheck;