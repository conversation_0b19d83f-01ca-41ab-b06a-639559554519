import React, { forwardRef } from 'react';

export interface SliderProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'value' | 'defaultValue' | 'onChange'> {
  min?: number;
  max?: number;
  value?: number | number[];
  defaultValue?: number | number[];
  onChange?: (value: number) => void;
  onValueChange?: (value: number[]) => void;
}

export const Slider = forwardRef<HTMLInputElement, SliderProps>(
  ({ className = '', min = 0, max = 100, value, defaultValue, onChange, onValueChange, ...props }, ref) => {
    // Handle single value
    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const newValue = parseInt(e.target.value, 10);
      if (onChange) {
        onChange(newValue);
      }
    };

    // If we have an array for value or defaultValue, we're in range mode
    const isRange = Array.isArray(value) || Array.isArray(defaultValue);

    // For range slider, we would need two inputs, but since we're using a custom implementation
    // in the actual components, we'll just pass through the props and let the component handle it
    if (isRange) {
      // In range mode, we'll just render a basic input that will be styled by the parent component
      return (
        <input
          ref={ref}
          type="range"
          min={min}
          max={max}
          // We can't actually use an array for HTML input value, but the parent component
          // will handle this through the onValueChange prop
          className={`w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer focus:outline-none focus:ring-2 focus:ring-primary-500 ${className}`}
          {...props}
        />
      );
    }

    // Single value mode
    return (
      <input
        ref={ref}
        type="range"
        min={min}
        max={max}
        value={value as number}
        onChange={handleChange}
        className={`w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer focus:outline-none focus:ring-2 focus:ring-primary-500 ${className}`}
        {...props}
      />
    );
  }
);

Slider.displayName = 'Slider';
