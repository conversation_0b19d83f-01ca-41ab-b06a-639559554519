import React, { HTMLAttributes } from 'react';

export interface TextProps extends HTMLAttributes<HTMLParagraphElement> {
  variant?: 'default' | 'muted' | 'small';
  as?: 'p' | 'span' | 'div';
}

export const Text = React.forwardRef<HTMLParagraphElement, TextProps>(
  ({ className = '', variant = 'default', as: Component = 'p', children, ...props }, ref) => {
    // Simple class merging function
    const getClassName = () => {
      let baseClass = 'text-base leading-7';

      if (variant === 'muted') {
        baseClass += ' text-gray-500';
      } else if (variant === 'small') {
        baseClass += ' text-sm leading-6';
      }

      return className ? `${baseClass} ${className}` : baseClass;
    };

    return (
      <Component
        className={getClassName()}
        ref={ref}
        {...props}
      >
        {children}
      </Component>
    );
  }
);

Text.displayName = 'Text';
