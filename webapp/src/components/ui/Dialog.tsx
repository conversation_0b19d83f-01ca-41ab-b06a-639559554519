import React, { Fragment } from 'react';
import { Dialog as HeadlessDialog, Transition } from '@headlessui/react';

interface DialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  children: React.ReactNode;
}

const Dialog = ({ open, onOpenChange, children }: DialogProps) => {
  return (
    <Transition appear show={open} as={Fragment}>
      <HeadlessDialog
        as="div"
        className="relative z-50"
        onClose={() => onOpenChange(false)}
      >
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black bg-opacity-25" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <HeadlessDialog.Panel className="w-full max-w-md transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
                {children}
              </HeadlessDialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </HeadlessDialog>
    </Transition>
  );
};

const DialogContent = ({ children, className = '' }: { children: React.ReactNode; className?: string }) => {
  return <div className={`mt-2 ${className}`}>{children}</div>;
};

const DialogHeader = ({ children, className = '' }: { children: React.ReactNode; className?: string }) => {
  return <div className={`mb-4 ${className}`}>{children}</div>;
};

const DialogTitle = ({ children, className = '' }: { children: React.ReactNode; className?: string }) => {
  return (
    <HeadlessDialog.Title as="h3" className={`text-lg font-medium leading-6 text-gray-900 ${className}`}>
      {children}
    </HeadlessDialog.Title>
  );
};

const DialogFooter = ({ children, className = '' }: { children: React.ReactNode; className?: string }) => {
  return <div className={`mt-4 flex justify-end space-x-2 ${className}`}>{children}</div>;
};

const DialogDescription = ({ children, className = '' }: { children: React.ReactNode; className?: string }) => {
  return <div className={`mt-2 text-sm text-gray-500 ${className}`}>{children}</div>;
};

export { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter, DialogDescription };
