import React, { HTMLAttributes, forwardRef } from 'react';

export interface HeadingProps extends HTMLAttributes<HTMLHeadingElement> {
  level?: 1 | 2 | 3 | 4 | 5 | 6;
}

const Heading = forwardRef<HTMLHeadingElement, HeadingProps>(
  ({ className, level = 1, children, ...props }, ref) => {
    // Base styles for different heading levels
    let levelClasses = '';

    switch (level) {
      case 1:
        levelClasses = 'text-2xl md:text-3xl font-bold';
        break;
      case 2:
        levelClasses = 'text-xl md:text-2xl font-semibold';
        break;
      case 3:
        levelClasses = 'text-lg md:text-xl font-medium';
        break;
      case 4:
        levelClasses = 'text-base md:text-lg font-medium';
        break;
      case 5:
        levelClasses = 'text-sm md:text-base font-medium';
        break;
      case 6:
        levelClasses = 'text-xs md:text-sm font-medium';
        break;
      default:
        levelClasses = 'text-2xl md:text-3xl font-bold';
    }

    const combinedClassName = `${levelClasses} ${className || ''}`;

    // Render the appropriate heading element based on level
    switch (level) {
      case 1:
        return <h1 ref={ref} className={combinedClassName} {...props}>{children}</h1>;
      case 2:
        return <h2 ref={ref} className={combinedClassName} {...props}>{children}</h2>;
      case 3:
        return <h3 ref={ref} className={combinedClassName} {...props}>{children}</h3>;
      case 4:
        return <h4 ref={ref} className={combinedClassName} {...props}>{children}</h4>;
      case 5:
        return <h5 ref={ref} className={combinedClassName} {...props}>{children}</h5>;
      case 6:
        return <h6 ref={ref} className={combinedClassName} {...props}>{children}</h6>;
      default:
        return <h1 ref={ref} className={combinedClassName} {...props}>{children}</h1>;
    }
  }
);

Heading.displayName = "Heading";

export { Heading };
