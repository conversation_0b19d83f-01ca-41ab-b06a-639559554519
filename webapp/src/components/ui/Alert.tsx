import React, { HTMLAttributes } from 'react';

export interface AlertProps extends HTMLAttributes<HTMLDivElement> {
  variant?: 'default' | 'destructive' | 'warning' | 'success';
}

export const Alert = React.forwardRef<HTMLDivElement, AlertProps>(
  ({ className = '', variant = 'default', children, ...props }, ref) => {
    // Class merging function
    const getClassName = () => {
      let baseClass = 'relative w-full rounded-lg border p-4 mb-4';

      if (variant === 'destructive') {
        baseClass += ' bg-red-100 border-red-400 text-red-700';
      } else if (variant === 'warning') {
        baseClass += ' bg-yellow-100 border-yellow-400 text-yellow-700';
      } else if (variant === 'success') {
        baseClass += ' bg-green-100 border-green-400 text-green-700';
      } else {
        baseClass += ' bg-gray-100 border-gray-200 text-gray-700';
      }

      return className ? `${baseClass} ${className}` : baseClass;
    };

    return (
      <div
        className={getClassName()}
        ref={ref}
        role="alert"
        {...props}
      >
        {children}
      </div>
    );
  }
);

Alert.displayName = 'Alert';

export interface AlertTitleProps extends HTMLAttributes<HTMLHeadingElement> {}

export const AlertTitle = React.forwardRef<HTMLHeadingElement, AlertTitleProps>(
  ({ className = '', children, ...props }, ref) => {
    return (
      <h5
        className={`font-medium text-lg mb-1 ${className}`}
        ref={ref}
        {...props}
      >
        {children}
      </h5>
    );
  }
);

AlertTitle.displayName = 'AlertTitle';

export interface AlertDescriptionProps extends HTMLAttributes<HTMLParagraphElement> {}

export const AlertDescription = React.forwardRef<HTMLParagraphElement, AlertDescriptionProps>(
  ({ className = '', children, ...props }, ref) => {
    return (
      <p
        className={`text-sm ${className}`}
        ref={ref}
        {...props}
      >
        {children}
      </p>
    );
  }
);

AlertDescription.displayName = 'AlertDescription';