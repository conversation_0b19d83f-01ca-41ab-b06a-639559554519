import React, { ReactNode } from 'react';
import { Link } from 'react-router-dom';
import { ArrowLeftIcon } from '@heroicons/react/24/outline';
import { Heading } from './Heading';

export interface PageHeaderProps {
  title: string;
  description?: string;
  icon?: ReactNode;
  backLink?: string;
  onBack?: () => void;
  actions?: ReactNode;
  className?: string;
}

const PageHeader: React.FC<PageHeaderProps> = ({
  title,
  description,
  icon,
  backLink,
  onBack,
  actions,
  className = '',
}) => {
  const handleBack = () => {
    if (onBack) {
      onBack();
    }
  };

  return (
    <div className={`mb-6 ${className}`}>
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          {(backLink || onBack) && (
            <button
              onClick={handleBack}
              className="mr-3 p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
              aria-label="Go back"
            >
              <ArrowLeftIcon className="h-5 w-5 text-gray-500 dark:text-gray-400" />
            </button>
          )}
          <div className="flex items-center">
            {icon && <div className="mr-3 text-xl">{icon}</div>}
            <Heading level={1} className="text-gray-900 dark:text-white">
              {title}
            </Heading>
          </div>
        </div>
        {actions && <div className="flex items-center space-x-2">{actions}</div>}
      </div>
      {description && (
        <p className="mt-1 text-sm text-gray-500 dark:text-gray-400 max-w-3xl">
          {description}
        </p>
      )}
    </div>
  );
};

export default PageHeader;