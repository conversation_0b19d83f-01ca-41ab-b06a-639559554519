import React from 'react';
import { StructuredError } from '../../utils/errorHandler';
import { Alert, AlertTitle, AlertDescription } from './Alert';

export interface ErrorDisplayProps {
  error: string | StructuredError | null;
  className?: string;
  onRetry?: () => void;
  onDismiss?: () => void;
}

/**
 * A component for displaying errors in a consistent way
 */
export const ErrorDisplay: React.FC<ErrorDisplayProps> = ({
  error,
  className = '',
  onRetry,
  onDismiss
}) => {
  if (!error) return null;

  // Convert string errors to a simple structured error
  const structuredError: StructuredError = typeof error === 'string'
    ? {
        message: error,
        type: 'Error',
        code: 'UNKNOWN'
      }
    : error;

  // Determine the alert variant based on error type
  const getVariant = () => {
    const errorType = structuredError.type.toLowerCase();
    
    if (errorType.includes('validation') || errorType.includes('input')) {
      return 'warning';
    } else if (errorType.includes('notfound') || errorType.includes('404')) {
      return 'default';
    } else if (errorType.includes('forbidden') || errorType.includes('unauthorized') || 
               errorType.includes('403') || errorType.includes('401')) {
      return 'warning';
    } else {
      return 'destructive';
    }
  };

  // Get a user-friendly title based on error type
  const getTitle = () => {
    const errorType = structuredError.type.toLowerCase();
    
    if (errorType.includes('validation') || errorType.includes('input')) {
      return 'Validation Error';
    } else if (errorType.includes('notfound') || errorType.includes('404')) {
      return 'Not Found';
    } else if (errorType.includes('forbidden') || errorType.includes('403')) {
      return 'Access Denied';
    } else if (errorType.includes('unauthorized') || errorType.includes('401')) {
      return 'Authentication Required';
    } else if (errorType.includes('timeout') || errorType.includes('network')) {
      return 'Connection Error';
    } else {
      return 'Error';
    }
  };

  return (
    <Alert variant={getVariant()} className={className}>
      <AlertTitle>{getTitle()}</AlertTitle>
      <AlertDescription>{structuredError.message}</AlertDescription>
      
      {(onRetry || onDismiss) && (
        <div className="mt-3 flex gap-3">
          {onRetry && (
            <button 
              onClick={onRetry}
              className="px-3 py-1 text-sm bg-gray-200 hover:bg-gray-300 rounded"
            >
              Try Again
            </button>
          )}
          {onDismiss && (
            <button 
              onClick={onDismiss}
              className="px-3 py-1 text-sm bg-transparent hover:bg-gray-100 rounded"
            >
              Dismiss
            </button>
          )}
        </div>
      )}
    </Alert>
  );
};

export default ErrorDisplay;