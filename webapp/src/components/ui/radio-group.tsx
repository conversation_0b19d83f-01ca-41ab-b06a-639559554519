import React, { HTMLAttributes, forwardRef } from 'react';

export interface RadioGroupProps extends HTMLAttributes<HTMLDivElement> {
  value?: string;
  onValueChange?: (value: string) => void;
}

export const RadioGroup = forwardRef<HTMLDivElement, RadioGroupProps>(
  ({ className = '', children, value, onValueChange, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={`${className}`}
        role="radiogroup"
        {...props}
      >
        {React.Children.map(children, child => {
          if (React.isValidElement(child)) {
            return React.cloneElement(child, {
              checked: child.props.value === value,
              onChange: () => onValueChange?.(child.props.value),
            } as any);
          }
          return child;
        })}
      </div>
    );
  }
);

RadioGroup.displayName = 'RadioGroup';

export interface RadioGroupItemProps extends HTMLAttributes<HTMLInputElement> {
  value: string;
  id: string;
}

export const RadioGroupItem = forwardRef<HTMLInputElement, RadioGroupItemProps>(
  ({ className = '', value, id, checked, onChange, ...props }, ref) => {
    return (
      <input
        ref={ref}
        type="radio"
        className={`h-4 w-4 text-primary-600 border-gray-300 focus:ring-primary-500 ${className}`}
        id={id}
        value={value}
        checked={checked}
        onChange={onChange}
        {...props}
      />
    );
  }
);

RadioGroupItem.displayName = 'RadioGroupItem';