import React, { useState, useEffect } from 'react';
import { 
  getEquipmentOemData, 
  getOemSystems,
  getAvailableOemSystems,
  linkEquipmentToOem,
  syncEquipmentWithOem,
  OemSystem,
  OemEquipmentData
} from '../services/oemService';

interface EquipmentOemProps {
  equipmentId: string;
  manufacturer: string;
}

const EquipmentOem: React.FC<EquipmentOemProps> = ({ equipmentId, manufacturer }) => {
  const [oemData, setOemData] = useState<OemEquipmentData[]>([]);
  const [availableSystems, setAvailableSystems] = useState<OemSystem[]>([]);
  const [allSystems, setAllSystems] = useState<OemSystem[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [showLinkForm, setShowLinkForm] = useState<boolean>(false);
  const [linkFormData, setLinkFormData] = useState({
    oem_system_id: '',
    oem_equipment_id: '',
  });
  const [syncingId, setSyncingId] = useState<string | null>(null);

  // Fetch OEM data for the equipment
  useEffect(() => {
    const fetchOemData = async () => {
      try {
        setLoading(true);
        setError(null);
        
        // Fetch OEM data for this equipment
        const data = await getEquipmentOemData(equipmentId);
        setOemData(data);
        
        // Fetch all OEM systems
        const systems = await getOemSystems();
        setAllSystems(systems);
        
        // Fetch available OEM systems for this manufacturer
        const available = await getAvailableOemSystems(manufacturer);
        setAvailableSystems(available);
      } catch (err: any) {
        console.error('Error fetching OEM data:', err);
        setError('Failed to load OEM integration data. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchOemData();
  }, [equipmentId, manufacturer]);

  // Format date for display
  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleString();
  };

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setLinkFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle linking equipment to OEM system
  const handleLinkEquipment = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!linkFormData.oem_system_id || !linkFormData.oem_equipment_id) {
      setError('Please select an OEM system and enter an equipment ID.');
      return;
    }
    
    try {
      setLoading(true);
      setError(null);
      
      const newOemData = await linkEquipmentToOem({
        equipment_id: equipmentId,
        oem_system_id: linkFormData.oem_system_id,
        oem_equipment_id: linkFormData.oem_equipment_id
      });
      
      setOemData([...oemData, newOemData]);
      setShowLinkForm(false);
      setLinkFormData({
        oem_system_id: '',
        oem_equipment_id: ''
      });
    } catch (err: any) {
      console.error('Error linking equipment to OEM system:', err);
      setError('Failed to link equipment to OEM system. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  // Handle syncing equipment data with OEM system
  const handleSyncData = async (oemSystemId: string) => {
    try {
      setSyncingId(oemSystemId);
      setError(null);
      
      const updatedData = await syncEquipmentWithOem(equipmentId, oemSystemId);
      
      // Update the OEM data in the state
      setOemData(oemData.map(item => 
        item.oem_system_id === oemSystemId ? updatedData : item
      ));
      
      alert('Equipment data synced successfully with OEM system.');
    } catch (err: any) {
      console.error('Error syncing equipment data:', err);
      setError('Failed to sync equipment data with OEM system. Please try again later.');
    } finally {
      setSyncingId(null);
    }
  };

  // Get OEM system name by ID
  const getOemSystemName = (systemId: string) => {
    const system = allSystems.find(s => s.id === systemId);
    return system ? system.name : 'Unknown System';
  };

  if (loading && !oemData.length) {
    return (
      <div className="bg-white shadow overflow-hidden sm:rounded-lg mb-6">
        <div className="px-4 py-5 sm:px-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900">OEM Integrations</h3>
          <p className="mt-1 max-w-2xl text-sm text-gray-500">Connect with manufacturer systems.</p>
        </div>
        <div className="border-t border-gray-200 p-6 flex justify-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
          <p className="ml-3 text-gray-500">Loading OEM integration data...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white shadow overflow-hidden sm:rounded-lg mb-6">
      <div className="px-4 py-5 sm:px-6 flex justify-between items-center">
        <div>
          <h3 className="text-lg leading-6 font-medium text-gray-900">OEM Integrations</h3>
          <p className="mt-1 max-w-2xl text-sm text-gray-500">Connect with manufacturer systems.</p>
        </div>
        {!showLinkForm && (
          <button
            onClick={() => setShowLinkForm(true)}
            className="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Link to OEM System
          </button>
        )}
      </div>
      
      {error && (
        <div className="border-t border-gray-200">
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 m-4 rounded relative" role="alert">
            <span className="block sm:inline">{error}</span>
          </div>
        </div>
      )}
      
      {/* Link Equipment Form */}
      {showLinkForm && (
        <div className="border-t border-gray-200 p-4 bg-gray-50">
          <h4 className="text-md font-medium text-gray-700 mb-3">Link Equipment to OEM System</h4>
          <form onSubmit={handleLinkEquipment}>
            <div className="grid grid-cols-1 gap-y-4 sm:grid-cols-2 sm:gap-x-4">
              <div>
                <label htmlFor="oem_system_id" className="block text-sm font-medium text-gray-700">
                  OEM System
                </label>
                <select
                  id="oem_system_id"
                  name="oem_system_id"
                  value={linkFormData.oem_system_id}
                  onChange={handleInputChange}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
                  required
                >
                  <option value="">Select OEM System</option>
                  {availableSystems.map(system => (
                    <option key={system.id} value={system.id}>
                      {system.name} ({system.manufacturer})
                    </option>
                  ))}
                </select>
              </div>
              <div>
                <label htmlFor="oem_equipment_id" className="block text-sm font-medium text-gray-700">
                  OEM Equipment ID
                </label>
                <input
                  type="text"
                  id="oem_equipment_id"
                  name="oem_equipment_id"
                  value={linkFormData.oem_equipment_id}
                  onChange={handleInputChange}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
                  placeholder="Enter the equipment ID from the OEM system"
                  required
                />
              </div>
            </div>
            <div className="mt-4 flex justify-end space-x-2">
              <button
                type="button"
                onClick={() => setShowLinkForm(false)}
                className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                disabled={loading}
              >
                {loading ? 'Linking...' : 'Link Equipment'}
              </button>
            </div>
          </form>
        </div>
      )}
      
      {/* OEM Integrations List */}
      <div className="border-t border-gray-200">
        {oemData.length === 0 ? (
          <div className="px-4 py-5 sm:px-6 text-center">
            <p className="text-sm text-gray-500">This equipment is not linked to any OEM systems yet.</p>
            {!showLinkForm && (
              <button
                onClick={() => setShowLinkForm(true)}
                className="mt-3 inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                Link to OEM System
              </button>
            )}
          </div>
        ) : (
          <ul className="divide-y divide-gray-200">
            {oemData.map(data => (
              <li key={data.id} className="px-4 py-4 sm:px-6">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="text-md font-medium text-primary-600">
                      {getOemSystemName(data.oem_system_id)}
                    </h4>
                    <p className="text-sm text-gray-500">
                      OEM Equipment ID: {data.oem_equipment_id}
                    </p>
                    <p className="text-xs text-gray-400">
                      Last Synced: {formatDate(data.last_sync)}
                    </p>
                  </div>
                  <button
                    onClick={() => handleSyncData(data.oem_system_id)}
                    disabled={syncingId === data.oem_system_id}
                    className={`inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md ${
                      syncingId === data.oem_system_id
                        ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                        : 'text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500'
                    }`}
                  >
                    {syncingId === data.oem_system_id ? (
                      <>
                        <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Syncing...
                      </>
                    ) : (
                      'Sync Data'
                    )}
                  </button>
                </div>
                
                {/* OEM Data Details */}
                {(data.model_details || data.warranty_info || data.service_history || data.diagnostic_data || data.firmware_version) && (
                  <div className="mt-3 grid grid-cols-1 gap-y-2 sm:grid-cols-2 sm:gap-x-4 text-sm">
                    {data.model_details && (
                      <div className="col-span-2">
                        <h5 className="text-xs font-medium text-gray-500 uppercase tracking-wider">Model Details</h5>
                        <pre className="mt-1 text-gray-900 font-mono bg-gray-50 p-2 rounded overflow-auto text-xs">
                          {JSON.stringify(data.model_details, null, 2)}
                        </pre>
                      </div>
                    )}
                    
                    {data.warranty_info && (
                      <div className="col-span-2">
                        <h5 className="text-xs font-medium text-gray-500 uppercase tracking-wider">Warranty Information</h5>
                        <pre className="mt-1 text-gray-900 font-mono bg-gray-50 p-2 rounded overflow-auto text-xs">
                          {JSON.stringify(data.warranty_info, null, 2)}
                        </pre>
                      </div>
                    )}
                    
                    {data.service_history && (
                      <div className="col-span-2">
                        <h5 className="text-xs font-medium text-gray-500 uppercase tracking-wider">Service History</h5>
                        <pre className="mt-1 text-gray-900 font-mono bg-gray-50 p-2 rounded overflow-auto text-xs">
                          {JSON.stringify(data.service_history, null, 2)}
                        </pre>
                      </div>
                    )}
                    
                    {data.diagnostic_data && (
                      <div className="col-span-2">
                        <h5 className="text-xs font-medium text-gray-500 uppercase tracking-wider">Diagnostic Data</h5>
                        <pre className="mt-1 text-gray-900 font-mono bg-gray-50 p-2 rounded overflow-auto text-xs">
                          {JSON.stringify(data.diagnostic_data, null, 2)}
                        </pre>
                      </div>
                    )}
                    
                    {data.firmware_version && (
                      <div>
                        <h5 className="text-xs font-medium text-gray-500 uppercase tracking-wider">Firmware Version</h5>
                        <p className="mt-1 text-gray-900">{data.firmware_version}</p>
                      </div>
                    )}
                  </div>
                )}
              </li>
            ))}
          </ul>
        )}
      </div>
    </div>
  );
};

export default EquipmentOem;