import React, { useState, useEffect, useRef, useCallback } from 'react';
import { GoogleMap, Polygon, DrawingManager, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, InfoWindow } from '@react-google-maps/api';
import { loadGoogleMapsApi, isGoogleMapsLoaded } from '../utils/googleMapsLoader';
import axios from 'axios';
import { API_URL } from '../config';

// Define the shape of the coordinates for a polygon
interface PolygonCoordinates {
  lat: number;
  lng: number;
}

// Define the shape of a harvest direction map
interface HarvestDirectionMap {
  id: string;
  field_id: string;
  name: string;
  description: string | null;
  order_index: number;
  elements: HarvestMapElement[];
  created_at: string;
  updated_at: string;
}

// Define the shape of a harvest map element
interface HarvestMapElement {
  id: string;
  type: 'polyline' | 'marker';
  coordinates: PolygonCoordinates[] | PolygonCoordinates;
  color: string;
  label?: string;
  symbol?: string; // Symbol type for polylines (e.g., 'arrow', 'none')
  markerType?: string; // Type of marker for field dangers (e.g., 'rock', 'tree', 'hole')
}

// Define the shape of the label input
interface LabelInput {
  position: PolygonCoordinates;
  text: string;
  elementId?: string;
}

interface HarvestDirectionMapComponentProps {
  fieldId: string; // Field ID for fetching harvest direction maps
  fieldBoundaries: PolygonCoordinates[][]; // Field boundaries from the parent component
  center?: { lat: number; lng: number };
  height?: string;
  width?: string;
}

// Default map center (can be overridden by props)
const defaultCenter = { lat: 39.8283, lng: -98.5795 }; // Center of the US

// Helper function to get marker icon based on marker type
const getMarkerIcon = (markerType: string, color: string) => {
  let path = google.maps.SymbolPath.CIRCLE;
  let scale = 8;

  switch (markerType) {
    case 'rock':
      path = google.maps.SymbolPath.CIRCLE;
      scale = 10;
      break;
    case 'tree':
      // Triangle pointing up
      path = google.maps.SymbolPath.FORWARD_CLOSED_ARROW;
      scale = 10;
      break;
    case 'hole':
      // Circle with a dot in the middle
      path = google.maps.SymbolPath.CIRCLE;
      scale = 12;
      break;
    case 'water':
      // Wave-like symbol
      path = google.maps.SymbolPath.BACKWARD_OPEN_ARROW;
      scale = 10;
      break;
    case 'equipment':
      // Square
      path = google.maps.SymbolPath.BACKWARD_CLOSED_ARROW;
      scale = 10;
      break;
    default:
      path = google.maps.SymbolPath.CIRCLE;
      scale = 8;
  }

  return {
    path,
    scale,
    fillColor: color,
    fillOpacity: 1,
    strokeWeight: 1,
    strokeColor: '#ffffff',
  };
};

// Available colors for drawing
const colorOptions = [
  '#22c55e', // Green
  '#ef4444', // Red
  '#3b82f6', // Blue
  '#f59e0b', // Yellow
  '#8b5cf6', // Purple
  '#ec4899', // Pink
  '#14b8a6', // Teal
  '#f97316', // Orange
];

const HarvestDirectionMapComponent: React.FC<HarvestDirectionMapComponentProps> = ({
  fieldId,
  fieldBoundaries,
  center,
  height = '400px',
  width = '100%',
}) => {
  // Reference to the map instance
  const harvestMapRef = useRef<google.maps.Map | null>(null);

  // State for the map center
  const [mapCenter, setMapCenter] = useState(center || defaultCenter);

  // State for harvest direction maps
  const [harvestMaps, setHarvestMaps] = useState<HarvestDirectionMap[]>([]);
  const [activeHarvestMapIndex, setActiveHarvestMapIndex] = useState<number>(-1);

  // State to track unsaved changes
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  // State to store pending changes that haven't been saved to the backend yet
  const [pendingChanges, setPendingChanges] = useState<{
    mapId: string;
    changes: Partial<HarvestDirectionMap>;
  } | null>(null);

  // State for drawing mode
  const [drawingMode, setDrawingMode] = useState<any>(null);

  // State for selected color
  const [selectedColor, setSelectedColor] = useState<string>(colorOptions[0]);

  // State for selected symbol type
  const [selectedSymbol, setSelectedSymbol] = useState<string>('none');

  // State for selected marker type
  const [selectedMarkerType, setSelectedMarkerType] = useState<string>('default');

  // Define marker types for field dangers
  const markerTypes = [
    { id: 'default', name: 'Default Marker', icon: 'circle' },
    { id: 'rock', name: 'Rock', icon: 'square' },
    { id: 'tree', name: 'Tree', icon: 'triangle' },
    { id: 'hole', name: 'Hole', icon: 'reverse_triangle' },
    { id: 'water', name: 'Water Hazard', icon: 'water' },
    { id: 'equipment', name: 'Equipment', icon: 'equipment' }
  ];

  // State for label input
  const [activeLabelInput, setActiveLabelInput] = useState<LabelInput | null>(null);

  // State for map dialog
  const [mapDialogOpen, setMapDialogOpen] = useState<boolean>(false);
  const [mapDialogMode, setMapDialogMode] = useState<'add' | 'edit'>('add');
  const [mapDialogData, setMapDialogData] = useState<{ name: string; description: string }>({
    name: '',
    description: ''
  });

  // Custom map container style based on props
  const customMapContainerStyle = {
    width,
    height,
  };

  // Fetch harvest direction maps when component mounts or fieldId changes
  useEffect(() => {
    if (fieldId) {
      fetchHarvestDirectionMaps();
    }
  }, [fieldId]);

  // Fetch harvest direction maps from the API
  const fetchHarvestDirectionMaps = async () => {
    try {
      const response = await axios.get(`${API_URL}/fields/${fieldId}/harvest-direction-maps`);

      if (response.data && Array.isArray(response.data)) {
        // Sort maps by order_index
        const sortedMaps = response.data.sort((a: HarvestDirectionMap, b: HarvestDirectionMap) => 
          (a.order_index || 0) - (b.order_index || 0)
        );

        setHarvestMaps(sortedMaps);

        // Set the first map as active if there are any maps
        if (sortedMaps.length > 0) {
          setActiveHarvestMapIndex(0);
        }
      }
    } catch (error) {
      console.error('Error fetching harvest direction maps:', error);
    }
  };

  // Create a new harvest direction map
  const createHarvestDirectionMap = async (data: { name: string; description: string }) => {
    try {
      const response = await axios.post(`${API_URL}/fields/${fieldId}/harvest-direction-maps`, {
        ...data,
        order_index: harvestMaps.length + 1,
        elements: []
      });

      if (response.data) {
        // Add the new map to the state
        setHarvestMaps([...harvestMaps, response.data]);

        // Set the new map as active
        setActiveHarvestMapIndex(harvestMaps.length);

        return response.data;
      }
    } catch (error) {
      console.error('Error creating harvest direction map:', error);
      throw error;
    }
  };

  // Update an existing harvest direction map
  const updateHarvestDirectionMap = async (mapId: string, data: Partial<HarvestDirectionMap>) => {
    try {
      const response = await axios.put(`${API_URL}/fields/harvest-direction-maps/${mapId}`, data);

      if (response.data) {
        // Update the map in the state
        const updatedMaps = harvestMaps.map(map => 
          map.id === mapId ? { ...map, ...response.data } : map
        );

        setHarvestMaps(updatedMaps);

        return response.data;
      }
    } catch (error) {
      console.error('Error updating harvest direction map:', error);
      throw error;
    }
  };

  // Delete a harvest direction map
  const deleteHarvestDirectionMap = async (mapId: string) => {
    try {
      await axios.delete(`${API_URL}/fields/harvest-direction-maps/${mapId}`);

      // Remove the map from the state
      const updatedMaps = harvestMaps.filter(map => map.id !== mapId);
      setHarvestMaps(updatedMaps);

      // Update active map index
      if (activeHarvestMapIndex >= updatedMaps.length) {
        setActiveHarvestMapIndex(updatedMaps.length - 1);
      }

      return true;
    } catch (error) {
      console.error('Error deleting harvest direction map:', error);
      throw error;
    }
  };

  // Handle drawing mode change
  const handleDrawingModeChange = useCallback((mode: any) => {
    setDrawingMode(mode);
  }, []);

  // Helper function to safely check drawing mode
  const isDrawingMode = useCallback((mode: string): boolean => {
    if (!window.google || !window.google.maps || !window.google.maps.drawing) {
      return false;
    }

    if (mode === 'POLYLINE') {
      return drawingMode === google.maps.drawing.OverlayType.POLYLINE;
    } else if (mode === 'MARKER') {
      return drawingMode === google.maps.drawing.OverlayType.MARKER;
    } else if (mode === 'null') {
      return drawingMode === null;
    }

    return false;
  }, [drawingMode]);

  // Handle polyline complete event from the drawing manager
  const onPolylineComplete = useCallback((polyline: google.maps.Polyline) => {
    if (activeHarvestMapIndex < 0 || activeHarvestMapIndex >= harvestMaps.length) {
      alert('Please select or create a harvest direction map first.');
      polyline.setMap(null);
      return;
    }

    // Get the path of the polyline
    const polylinePath = polyline.getPath();
    const coordinates: PolygonCoordinates[] = [];

    // Convert the path to an array of coordinates
    for (let i = 0; i < polylinePath.getLength(); i++) {
      const point = polylinePath.getAt(i);
      coordinates.push({
        lat: point.lat(),
        lng: point.lng(),
      });
    }

    // Create a new element
    const newElement: HarvestMapElement = {
      id: `polyline-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      type: 'polyline',
      coordinates,
      color: selectedColor,
      symbol: selectedSymbol !== 'none' ? selectedSymbol : undefined
    };

    // Add the new element to the active harvest map
    const updatedHarvestMaps = [...harvestMaps];
    updatedHarvestMaps[activeHarvestMapIndex].elements.push(newElement);

    // Store the changes locally instead of updating the backend immediately
    setPendingChanges({
      mapId: updatedHarvestMaps[activeHarvestMapIndex].id,
      changes: {
        elements: updatedHarvestMaps[activeHarvestMapIndex].elements
      }
    });
    setHasUnsavedChanges(true);

    setHarvestMaps(updatedHarvestMaps);

    // Remove the drawn polyline since we're managing our own polylines
    polyline.setMap(null);

    // Reset drawing mode
    handleDrawingModeChange(null);
  }, [activeHarvestMapIndex, harvestMaps, selectedColor]);

  // Handle marker complete event from the drawing manager
  const onMarkerComplete = useCallback((marker: google.maps.Marker) => {
    if (activeHarvestMapIndex < 0 || activeHarvestMapIndex >= harvestMaps.length) {
      alert('Please select or create a harvest direction map first.');
      marker.setMap(null);
      return;
    }

    // Get the position of the marker
    const position = marker.getPosition();
    if (!position) return;

    const coordinates: PolygonCoordinates = {
      lat: position.lat(),
      lng: position.lng(),
    };

    // Create a new element
    const newElement: HarvestMapElement = {
      id: `marker-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      type: 'marker',
      coordinates,
      color: selectedColor,
      markerType: selectedMarkerType
    };

    // Add the new element to the active harvest map
    const updatedHarvestMaps = [...harvestMaps];
    updatedHarvestMaps[activeHarvestMapIndex].elements.push(newElement);

    // Store the changes locally instead of updating the backend immediately
    setPendingChanges({
      mapId: updatedHarvestMaps[activeHarvestMapIndex].id,
      changes: {
        elements: updatedHarvestMaps[activeHarvestMapIndex].elements
      }
    });
    setHasUnsavedChanges(true);

    setHarvestMaps(updatedHarvestMaps);

    // Remove the drawn marker since we're managing our own markers
    marker.setMap(null);

    // Open label input for the new marker
    setActiveLabelInput({
      position: coordinates,
      text: '',
      elementId: newElement.id
    });

    // Reset drawing mode
    handleDrawingModeChange(null);
  }, [activeHarvestMapIndex, harvestMaps, selectedColor, selectedMarkerType]);

  // Handle label submit
  const handleLabelSubmit = useCallback(() => {
    if (!activeLabelInput || activeHarvestMapIndex < 0 || activeHarvestMapIndex >= harvestMaps.length) {
      setActiveLabelInput(null);
      return;
    }

    // Find the element to update
    const updatedHarvestMaps = [...harvestMaps];
    const elementIndex = updatedHarvestMaps[activeHarvestMapIndex].elements.findIndex(
      element => element.id === activeLabelInput.elementId
    );

    if (elementIndex !== -1) {
      // Update the element label
      updatedHarvestMaps[activeHarvestMapIndex].elements[elementIndex].label = activeLabelInput.text;

      // Store the changes locally instead of updating the backend immediately
      setPendingChanges({
        mapId: updatedHarvestMaps[activeHarvestMapIndex].id,
        changes: {
          elements: updatedHarvestMaps[activeHarvestMapIndex].elements
        }
      });
      setHasUnsavedChanges(true);

      setHarvestMaps(updatedHarvestMaps);
    }

    // Close the label input
    setActiveLabelInput(null);
  }, [activeLabelInput, activeHarvestMapIndex, harvestMaps]);

  // Open add map dialog
  const openAddMapDialog = useCallback(() => {
    setMapDialogMode('add');
    setMapDialogData({ name: '', description: '' });
    setMapDialogOpen(true);
  }, []);

  // Open edit map dialog
  const openEditMapDialog = useCallback((index: number) => {
    if (index < 0 || index >= harvestMaps.length) return;

    setMapDialogMode('edit');
    setMapDialogData({
      name: harvestMaps[index].name,
      description: harvestMaps[index].description || ''
    });
    setMapDialogOpen(true);
  }, [harvestMaps]);

  // Handle map dialog submit
  const handleMapDialogSubmit = useCallback(async () => {
    try {
      if (!mapDialogData.name.trim()) {
        alert('Map name is required.');
        return;
      }

      if (mapDialogMode === 'add') {
        // Create a new map
        await createHarvestDirectionMap(mapDialogData);
      } else {
        // Update existing map
        if (activeHarvestMapIndex < 0 || activeHarvestMapIndex >= harvestMaps.length) return;

        await updateHarvestDirectionMap(harvestMaps[activeHarvestMapIndex].id, {
          name: mapDialogData.name,
          description: mapDialogData.description
        });
      }

      // Close the dialog
      setMapDialogOpen(false);
    } catch (error) {
      console.error('Error handling map dialog submit:', error);
      alert('Failed to save map. Please try again.');
    }
  }, [mapDialogMode, mapDialogData, activeHarvestMapIndex, harvestMaps, createHarvestDirectionMap, updateHarvestDirectionMap]);

  // Move map up in order
  const moveMapUp = useCallback(() => {
    if (activeHarvestMapIndex <= 0 || activeHarvestMapIndex >= harvestMaps.length) return;

    const updatedMaps = [...harvestMaps];
    const currentMap = updatedMaps[activeHarvestMapIndex];
    const prevMap = updatedMaps[activeHarvestMapIndex - 1];

    // Swap order_index values
    const tempOrderIndex = currentMap.order_index;
    currentMap.order_index = prevMap.order_index;
    prevMap.order_index = tempOrderIndex;

    // Swap positions in array
    updatedMaps[activeHarvestMapIndex] = prevMap;
    updatedMaps[activeHarvestMapIndex - 1] = currentMap;

    // Update both maps in the backend
    Promise.all([
      updateHarvestDirectionMap(currentMap.id, { order_index: currentMap.order_index }),
      updateHarvestDirectionMap(prevMap.id, { order_index: prevMap.order_index })
    ]).catch(error => {
      console.error('Error moving map up:', error);
      alert('Failed to reorder maps. Please try again.');
    });

    setHarvestMaps(updatedMaps);
    setActiveHarvestMapIndex(activeHarvestMapIndex - 1);
  }, [activeHarvestMapIndex, harvestMaps, updateHarvestDirectionMap]);

  // Move map down in order
  const moveMapDown = useCallback(() => {
    if (activeHarvestMapIndex < 0 || activeHarvestMapIndex >= harvestMaps.length - 1) return;

    const updatedMaps = [...harvestMaps];
    const currentMap = updatedMaps[activeHarvestMapIndex];
    const nextMap = updatedMaps[activeHarvestMapIndex + 1];

    // Swap order_index values
    const tempOrderIndex = currentMap.order_index;
    currentMap.order_index = nextMap.order_index;
    nextMap.order_index = tempOrderIndex;

    // Swap positions in array
    updatedMaps[activeHarvestMapIndex] = nextMap;
    updatedMaps[activeHarvestMapIndex + 1] = currentMap;

    // Update both maps in the backend
    Promise.all([
      updateHarvestDirectionMap(currentMap.id, { order_index: currentMap.order_index }),
      updateHarvestDirectionMap(nextMap.id, { order_index: nextMap.order_index })
    ]).catch(error => {
      console.error('Error moving map down:', error);
      alert('Failed to reorder maps. Please try again.');
    });

    setHarvestMaps(updatedMaps);
    setActiveHarvestMapIndex(activeHarvestMapIndex + 1);
  }, [activeHarvestMapIndex, harvestMaps, updateHarvestDirectionMap]);

  // Clear all elements from the current harvest map
  const clearHarvestElements = useCallback(() => {
    if (activeHarvestMapIndex < 0 || activeHarvestMapIndex >= harvestMaps.length) return;

    if (!confirm('Are you sure you want to clear all elements from this map?')) return;

    const updatedMaps = [...harvestMaps];
    updatedMaps[activeHarvestMapIndex].elements = [];

    // Store the changes locally instead of updating the backend immediately
    setPendingChanges({
      mapId: updatedMaps[activeHarvestMapIndex].id,
      changes: {
        elements: []
      }
    });
    setHasUnsavedChanges(true);

    setHarvestMaps(updatedMaps);
  }, [activeHarvestMapIndex, harvestMaps]);

  // Save changes to the backend
  const saveChanges = useCallback(async () => {
    if (!hasUnsavedChanges || !pendingChanges) return;

    try {
      await updateHarvestDirectionMap(pendingChanges.mapId, pendingChanges.changes);
      setHasUnsavedChanges(false);
      setPendingChanges(null);
    } catch (error) {
      console.error('Error saving changes to harvest direction map:', error);
      alert('Failed to save changes. Please try again.');
    }
  }, [hasUnsavedChanges, pendingChanges, updateHarvestDirectionMap]);

  // Delete the current harvest map
  const deleteCurrentMap = useCallback(() => {
    if (activeHarvestMapIndex < 0 || activeHarvestMapIndex >= harvestMaps.length) return;

    if (!confirm('Are you sure you want to delete this map? This action cannot be undone.')) return;

    deleteHarvestDirectionMap(harvestMaps[activeHarvestMapIndex].id).catch(error => {
      console.error('Error deleting harvest direction map:', error);
      alert('Failed to delete map. Please try again.');
    });
  }, [activeHarvestMapIndex, harvestMaps, deleteHarvestDirectionMap]);

  // Update map center when center prop changes
  useEffect(() => {
    if (center) {
      setMapCenter(center);

      if (harvestMapRef.current) {
        harvestMapRef.current.panTo(center);
        harvestMapRef.current.setZoom(15); // Zoom in to a reasonable level
        // Set map type to satellite
        harvestMapRef.current.setMapTypeId('satellite');
      }
    }
  }, [center]);

  // Handle harvest map load
  const onHarvestMapLoad = useCallback((map: google.maps.Map) => {
    harvestMapRef.current = map;

    // Set map type to satellite
    map.setMapTypeId('satellite');

    // If we have field boundaries, fit the map to them
    if (fieldBoundaries.length > 0) {
      const bounds = new google.maps.LatLngBounds();

      // Add all points from all field paths to the bounds
      fieldBoundaries.forEach(path => {
        path.forEach(point => {
          bounds.extend(new google.maps.LatLng(point.lat, point.lng));
        });
      });

      // Center the map on the field bounds
      map.fitBounds(bounds);
    }
  }, [fieldBoundaries]);

  // Auto-reposition harvest map when field boundaries change
  useEffect(() => {
    if (harvestMapRef.current && fieldBoundaries.length > 0) {
      // Calculate the center of the field
      const bounds = new google.maps.LatLngBounds();

      // Add all points from all field paths to the bounds
      fieldBoundaries.forEach(path => {
        path.forEach(point => {
          bounds.extend(new google.maps.LatLng(point.lat, point.lng));
        });
      });

      // Center the map on the field bounds
      harvestMapRef.current.fitBounds(bounds);

      // Set map type to satellite
      harvestMapRef.current.setMapTypeId('satellite');
    }
  }, [fieldBoundaries]);


  // State for Google Maps API loading
  const [isLoaded, setIsLoaded] = useState(false);
  const [loadError, setLoadError] = useState<Error | null>(null);

  // Load the Google Maps API using the googleMapsLoader utility
  useEffect(() => {
    const loadMapsApi = async () => {
      try {
        await loadGoogleMapsApi(['drawing', 'places']);
        setIsLoaded(true);
      } catch (error) {
        console.error('Failed to load Google Maps API:', error);
        setLoadError(error instanceof Error ? error : new Error('Failed to load Google Maps API'));
      }
    };

    if (!isGoogleMapsLoaded()) {
      loadMapsApi();
    } else {
      setIsLoaded(true);
    }
  }, []);

  // Handle loading error
  if (loadError) {
    return (
      <div className="harvest-direction-map-component">
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">Error loading Google Maps API. Please try again later.</span>
        </div>
      </div>
    );
  }

  // Show loading indicator while the API is loading
  if (!isLoaded) {
    return (
      <div className="harvest-direction-map-component">
        <div className="flex justify-center items-center" style={{ height: height }}>
          <div className="text-gray-500">Loading map...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="harvest-direction-map-component">
      <div className="mt-8">
        <h3 className="text-lg font-medium text-gray-900 mb-2">Harvest Direction Maps</h3>
        <div className="bg-white p-4 mb-4 rounded-md shadow-sm border border-gray-200">
          <p className="text-sm text-gray-600">
            This is a separate component for harvest direction maps. It displays field boundaries in satellite view
            and allows you to create and manage harvest direction maps.
          </p>

          {/* Add button to create a new map */}
          <div className="mt-3">
            <button
              className="inline-flex items-center px-3 py-2 border border-primary-300 shadow-sm text-sm leading-4 font-medium rounded-md text-primary-700 bg-white hover:bg-primary-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              onClick={openAddMapDialog}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd" />
              </svg>
              Create New Map
            </button>
          </div>
        </div>

        {/* Harvest Map Tabs - Only show if we have maps */}
        {harvestMaps.length > 0 && (
          <div className="mb-4">
            <div className="flex border-b border-gray-200">
              {harvestMaps
                .sort((a, b) => (a.order_index || 0) - (b.order_index || 0))
                .map((map, index) => (
                  <button
                    key={map.id}
                    className={`px-4 py-2 text-sm font-medium ${
                      index === activeHarvestMapIndex
                        ? 'text-primary-600 border-b-2 border-primary-600'
                        : 'text-gray-500 hover:text-gray-700'
                    }`}
                    onClick={() => setActiveHarvestMapIndex(index)}
                  >
                    <span className="inline-flex items-center">
                      <span className="w-5 h-5 flex items-center justify-center rounded-full bg-gray-200 text-xs mr-2">
                        {map.order_index || index + 1}
                      </span>
                      {map.name}
                    </span>
                  </button>
                ))}
            </div>

            {/* Active Map Info */}
            {activeHarvestMapIndex >= 0 && activeHarvestMapIndex < harvestMaps.length && (
              <div className="mt-2 p-3 bg-gray-50 rounded-md">
                <div className="flex justify-between items-start">
                  <div>
                    <h4 className="text-md font-medium text-gray-900">
                      {harvestMaps[activeHarvestMapIndex].name}
                    </h4>
                    {harvestMaps[activeHarvestMapIndex].description && (
                      <p className="text-sm text-gray-600 mt-1">
                        {harvestMaps[activeHarvestMapIndex].description}
                      </p>
                    )}
                  </div>
                  <div className="flex space-x-2">
                    <button
                      type="button"
                      onClick={() => openEditMapDialog(activeHarvestMapIndex)}
                      className="p-1 text-gray-500 hover:text-gray-700"
                      title="Edit Map Details"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
                      </svg>
                    </button>
                    <button
                      type="button"
                      onClick={moveMapUp}
                      disabled={activeHarvestMapIndex <= 0}
                      className={`p-1 ${activeHarvestMapIndex <= 0 ? 'text-gray-300 cursor-not-allowed' : 'text-gray-500 hover:text-gray-700'}`}
                      title="Move Up"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M5.293 9.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L11 7.414V15a1 1 0 11-2 0V7.414L6.707 9.707a1 1 0 01-1.414 0z" clipRule="evenodd" />
                      </svg>
                    </button>
                    <button
                      type="button"
                      onClick={moveMapDown}
                      disabled={activeHarvestMapIndex >= harvestMaps.length - 1}
                      className={`p-1 ${activeHarvestMapIndex >= harvestMaps.length - 1 ? 'text-gray-300 cursor-not-allowed' : 'text-gray-500 hover:text-gray-700'}`}
                      title="Move Down"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M14.707 10.293a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 111.414-1.414L9 12.586V5a1 1 0 012 0v7.586l2.293-2.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    </button>
                    <button
                      type="button"
                      onClick={deleteCurrentMap}
                      className="p-1 text-red-500 hover:text-red-700"
                      title="Delete Map"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd" />
                      </svg>
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}

        {/* Drawing Tools - Only show if we have an active map */}
        {activeHarvestMapIndex >= 0 && activeHarvestMapIndex < harvestMaps.length && (
          <div className="bg-white p-4 mb-4 rounded-md shadow-sm border border-gray-200">
            <h4 className="text-md font-medium text-gray-900 mb-2">Harvest Direction Drawing Tools</h4>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-3">
              {/* Drawing Tools */}
              <div className="bg-gray-50 p-3 rounded-md">
                <h5 className="text-sm font-medium text-gray-700 mb-2">Drawing Tools</h5>
                <div className="flex flex-wrap gap-2">
                  <button
                    type="button"
                    className={`inline-flex items-center px-3 py-2 border ${
                      isDrawingMode('POLYLINE')
                        ? 'border-primary-500 bg-primary-50 text-primary-700'
                        : 'border-gray-300 bg-white text-gray-700'
                    } shadow-sm text-sm leading-4 font-medium rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500`}
                    onClick={() => window.google && window.google.maps && window.google.maps.drawing && handleDrawingModeChange(google.maps.drawing.OverlayType.POLYLINE)}
                    title="Draw directional lines to show harvest paths"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                      <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
                    </svg>
                    Draw Path
                  </button>
                  <button
                    type="button"
                    className={`inline-flex items-center px-3 py-2 border ${
                      isDrawingMode('MARKER')
                        ? 'border-primary-500 bg-primary-50 text-primary-700'
                        : 'border-gray-300 bg-white text-gray-700'
                    } shadow-sm text-sm leading-4 font-medium rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500`}
                    onClick={() => window.google && window.google.maps && window.google.maps.drawing && handleDrawingModeChange(google.maps.drawing.OverlayType.MARKER)}
                    title="Add markers with labels for important points"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                    </svg>
                    Add Marker
                  </button>
                  <button
                    type="button"
                    className={`inline-flex items-center px-3 py-2 border ${
                      isDrawingMode('null')
                        ? 'border-primary-500 bg-primary-50 text-primary-700'
                        : 'border-gray-300 bg-white text-gray-700'
                    } shadow-sm text-sm leading-4 font-medium rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500`}
                    onClick={() => handleDrawingModeChange(null)}
                    title="Select and edit existing elements"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M6.672 1.911a1 1 0 10-1.932.518l.259.966a1 1 0 001.932-.518l-.26-.966zM2.429 4.74a1 1 0 10-.517 1.932l.966.259a1 1 0 00.517-1.932l-.966-.26zm8.814-.569a1 1 0 00-1.415-1.414l-.707.707a1 1 0 101.415 1.415l.707-.708zm-7.071 7.072l.707-.707A1 1 0 003.465 9.12l-.708.707a1 1 0 001.415 1.415zm3.2-5.171a1 1 0 00-1.3 1.3l4 10a1 1 0 001.823.075l1.38-2.759 3.018 3.02a1 1 0 001.414-1.415l-3.019-3.02 2.76-1.379a1 1 0 00-.076-1.822l-10-4z" clipRule="evenodd" />
                    </svg>
                    Select/Edit
                  </button>
                </div>
              </div>

              {/* Color Selector */}
              <div className="bg-gray-50 p-3 rounded-md">
                <h5 className="text-sm font-medium text-gray-700 mb-2">Element Color</h5>
                <div className="grid grid-cols-4 gap-2">
                  {colorOptions.map(color => (
                    <button
                      key={color}
                      type="button"
                      className={`w-full h-8 rounded-md flex items-center justify-center ${
                        selectedColor === color ? 'ring-2 ring-offset-2 ring-gray-500' : 'ring-1 ring-gray-200'
                      }`}
                      style={{ backgroundColor: color }}
                      onClick={() => setSelectedColor(color)}
                      title={`Select ${color} color`}
                    >
                      {selectedColor === color && (
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                      )}
                    </button>
                  ))}
                </div>
              </div>

              {/* Symbol Selector */}
              <div className="bg-gray-50 p-3 rounded-md">
                <h5 className="text-sm font-medium text-gray-700 mb-2">Line Style</h5>
                <div className="grid grid-cols-3 gap-2">
                  <button
                    type="button"
                    className={`w-full h-8 rounded-md flex items-center justify-center border ${
                      selectedSymbol === 'none' ? 'bg-primary-100 border-primary-500' : 'bg-white border-gray-300'
                    }`}
                    onClick={() => setSelectedSymbol('none')}
                    title="No symbols"
                  >
                    <span className="text-sm">Plain Line</span>
                  </button>
                  <button
                    type="button"
                    className={`w-full h-8 rounded-md flex items-center justify-center border ${
                      selectedSymbol === 'arrow' ? 'bg-primary-100 border-primary-500' : 'bg-white border-gray-300'
                    }`}
                    onClick={() => setSelectedSymbol('arrow')}
                    title="Arrow symbols"
                  >
                    <span className="text-sm">→ Arrows</span>
                  </button>
                  <button
                    type="button"
                    className={`w-full h-8 rounded-md flex items-center justify-center border ${
                      selectedSymbol === 'dot' ? 'bg-primary-100 border-primary-500' : 'bg-white border-gray-300'
                    }`}
                    onClick={() => setSelectedSymbol('dot')}
                    title="Dot symbols"
                  >
                    <span className="text-sm">• Dots</span>
                  </button>
                </div>
              </div>

              {/* Marker Type Selector */}
              <div className="bg-gray-50 p-3 rounded-md">
                <h5 className="text-sm font-medium text-gray-700 mb-2">Field Danger Markers</h5>
                <div className="grid grid-cols-3 gap-2">
                  {markerTypes.map(markerType => (
                    <button
                      key={markerType.id}
                      type="button"
                      className={`w-full h-8 rounded-md flex items-center justify-center border ${
                        selectedMarkerType === markerType.id ? 'bg-primary-100 border-primary-500' : 'bg-white border-gray-300'
                      }`}
                      onClick={() => setSelectedMarkerType(markerType.id)}
                      title={markerType.name}
                    >
                      <span className="text-sm">{markerType.name}</span>
                    </button>
                  ))}
                </div>
              </div>

              {/* Map Actions */}
              <div className="bg-gray-50 p-3 rounded-md">
                <h5 className="text-sm font-medium text-gray-700 mb-2">Map Actions</h5>
                <div className="flex flex-col space-y-2">
                  <button
                    type="button"
                    onClick={saveChanges}
                    disabled={!hasUnsavedChanges}
                    className={`inline-flex items-center px-3 py-2 border ${
                      hasUnsavedChanges 
                        ? 'border-primary-500 bg-primary-50 text-primary-700 hover:bg-primary-100' 
                        : 'border-gray-300 bg-gray-50 text-gray-400 cursor-not-allowed'
                    } shadow-sm text-sm leading-4 font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500`}
                    title={hasUnsavedChanges ? "Save your changes" : "No changes to save"}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    Save Changes
                  </button>
                  <button
                    type="button"
                    onClick={clearHarvestElements}
                    className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                    title="Clear all elements from the current map"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                    Clear Current Map
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        <div className="relative">
          <GoogleMap
            mapContainerStyle={customMapContainerStyle}
            center={mapCenter}
            zoom={15}
            onLoad={onHarvestMapLoad}
            mapTypeId="satellite"
            options={{
              mapTypeControl: true,
              streetViewControl: false,
              fullscreenControl: true,
              mapTypeControlOptions: {
                mapTypeIds: ["roadmap", "satellite", "hybrid", "terrain"],
                position: window.google?.maps?.ControlPosition?.TOP_RIGHT
              }
            }}
          >
            {/* Render field boundaries as reference (non-editable) */}
            {fieldBoundaries.map((path, index) => (
              <Polygon
                key={`field-${index}`}
                paths={path}
                options={{
                  fillColor: '#22c55e',
                  fillOpacity: 0.1,
                  strokeWeight: 1,
                  strokeColor: '#22c55e',
                  clickable: false,
                  editable: false,
                  zIndex: 1,
                }}
              />
            ))}

            {/* Drawing Manager for harvest directions */}
            {activeHarvestMapIndex >= 0 && activeHarvestMapIndex < harvestMaps.length && (
              <DrawingManager
                onPolylineComplete={onPolylineComplete}
                onMarkerComplete={onMarkerComplete}
                drawingMode={drawingMode}
                options={{
                  drawingControl: false, // We're using our own UI controls
                  polylineOptions: {
                    strokeColor: selectedColor,
                    strokeWeight: 3,
                    strokeOpacity: 1,
                    clickable: true,
                    editable: true,
                    zIndex: 2,
                    icons: selectedSymbol !== 'none' ? [
                      {
                        icon: {
                          path: selectedSymbol === 'arrow' 
                            ? google.maps.SymbolPath.FORWARD_CLOSED_ARROW 
                            : google.maps.SymbolPath.CIRCLE,
                          scale: selectedSymbol === 'arrow' ? 5 : 3,
                          fillColor: selectedColor,
                          fillOpacity: 1,
                          strokeWeight: 1,
                          strokeColor: '#ffffff',
                        },
                        offset: '0%',
                        repeat: selectedSymbol === 'arrow' ? '100px' : '50px'
                      }
                    ] : undefined,
                  },
                  markerOptions: {
                    icon: getMarkerIcon(selectedMarkerType, selectedColor),
                    clickable: true,
                    draggable: true,
                    zIndex: 2,
                  },
                }}
              />
            )}

            {/* Render harvest direction elements */}
            {activeHarvestMapIndex >= 0 && activeHarvestMapIndex < harvestMaps.length && 
              harvestMaps[activeHarvestMapIndex].elements.map(element => {
                if (element.type === 'polyline') {
                  return (
                    <Polyline
                      key={element.id}
                      path={element.coordinates as PolygonCoordinates[]}
                      options={{
                        strokeColor: element.color,
                        strokeWeight: 3,
                        strokeOpacity: 1,
                        clickable: true,
                        editable: true,
                        draggable: true,
                        zIndex: 2,
                        icons: element.symbol ? [
                          {
                            icon: {
                              path: element.symbol === 'arrow' 
                                ? google.maps.SymbolPath.FORWARD_CLOSED_ARROW 
                                : google.maps.SymbolPath.CIRCLE,
                              scale: element.symbol === 'arrow' ? 5 : 3,
                              fillColor: element.color,
                              fillOpacity: 1,
                              strokeWeight: 1,
                              strokeColor: '#ffffff',
                            },
                            offset: '0%',
                            repeat: element.symbol === 'arrow' ? '100px' : '50px'
                          }
                        ] : undefined,
                      }}
                      onMouseUp={(e) => {
                        // Update the element when the polyline is edited
                        // Access the polyline directly from the event target
                        const polyline = e.latLng ? (e as any).latLng.polyline : null;
                        if (!polyline || !polyline.getPath) return;

                        const path = polyline.getPath();
                        const updatedCoordinates: PolygonCoordinates[] = [];

                        for (let i = 0; i < path.getLength(); i++) {
                          const point = path.getAt(i);
                          updatedCoordinates.push({
                            lat: point.lat(),
                            lng: point.lng()
                          });
                        }

                        // Update the element in the harvest map
                        const updatedHarvestMaps = [...harvestMaps];
                        const elementIndex = updatedHarvestMaps[activeHarvestMapIndex].elements.findIndex(
                          e => e.id === element.id
                        );

                        if (elementIndex !== -1) {
                          // Preserve the existing element properties and update only the coordinates
                          updatedHarvestMaps[activeHarvestMapIndex].elements[elementIndex] = {
                            ...updatedHarvestMaps[activeHarvestMapIndex].elements[elementIndex],
                            coordinates: updatedCoordinates
                          };

                          // Store the changes locally instead of updating the backend immediately
                          setPendingChanges({
                            mapId: updatedHarvestMaps[activeHarvestMapIndex].id,
                            changes: {
                              elements: updatedHarvestMaps[activeHarvestMapIndex].elements
                            }
                          });
                          setHasUnsavedChanges(true);

                          setHarvestMaps(updatedHarvestMaps);
                        }
                      }}
                    />
                  );
                } else if (element.type === 'marker') {
                  return (
                    <Marker
                      key={element.id}
                      position={element.coordinates as PolygonCoordinates}
                      icon={getMarkerIcon(element.markerType || 'default', element.color)}
                      clickable={true}
                      draggable={true}
                      onDragEnd={(e) => {
                        // Update the element when the marker is dragged
                        if (!e || !e.latLng) return;

                        const updatedPosition = {
                          lat: e.latLng.lat(),
                          lng: e.latLng.lng()
                        };

                        // Update the element in the harvest map
                        const updatedHarvestMaps = [...harvestMaps];
                        const elementIndex = updatedHarvestMaps[activeHarvestMapIndex].elements.findIndex(
                          el => el.id === element.id
                        );

                        if (elementIndex !== -1) {
                          updatedHarvestMaps[activeHarvestMapIndex].elements[elementIndex].coordinates = updatedPosition;

                          // Store the changes locally instead of updating the backend immediately
                          setPendingChanges({
                            mapId: updatedHarvestMaps[activeHarvestMapIndex].id,
                            changes: {
                              elements: updatedHarvestMaps[activeHarvestMapIndex].elements
                            }
                          });
                          setHasUnsavedChanges(true);

                          setHarvestMaps(updatedHarvestMaps);
                        }
                      }}
                      onClick={() => {
                        // Open label input when marker is clicked
                        if (element.coordinates && typeof element.coordinates !== 'string') {
                          const position = element.coordinates as PolygonCoordinates;
                          setActiveLabelInput({
                            position,
                            text: element.label || '',
                            elementId: element.id
                          });
                        }
                      }}
                    >
                      {element.label && (
                        <InfoWindow
                          position={element.coordinates as PolygonCoordinates}
                          options={{
                            pixelOffset: new google.maps.Size(0, -30),
                          }}
                        >
                          <div className="p-1">
                            <p className="text-sm">{element.label}</p>
                          </div>
                        </InfoWindow>
                      )}
                    </Marker>
                  );
                }
                return null;
              })
            }

            {/* Label input for markers */}
            {activeLabelInput && (
              <InfoWindow
                position={activeLabelInput.position}
                onCloseClick={() => setActiveLabelInput(null)}
              >
                <div className="p-2">
                  <input
                    type="text"
                    value={activeLabelInput.text}
                    onChange={(e) => setActiveLabelInput({
                      ...activeLabelInput,
                      text: e.target.value,
                    })}
                    placeholder="Enter label text"
                    className="w-full px-2 py-1 border border-gray-300 rounded-md text-sm"
                  />
                  <div className="flex justify-end mt-2">
                    <button
                      type="button"
                      onClick={handleLabelSubmit}
                      className="px-2 py-1 bg-primary-600 text-white rounded-md text-xs"
                    >
                      Save
                    </button>
                  </div>
                </div>
              </InfoWindow>
            )}
          </GoogleMap>
        </div>

        <p className="mt-1 text-sm text-gray-500">
          The harvest direction maps are now in a separate component from the field boundary map.
        </p>
      </div>

      {/* Map Dialog */}
      {mapDialogOpen && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl p-6 w-full max-w-md">
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              {mapDialogMode === 'add' ? 'Add New Harvest Direction Map' : 'Edit Harvest Direction Map'}
            </h3>

            <div className="space-y-4">
              <div>
                <label htmlFor="mapName" className="block text-sm font-medium text-gray-700 mb-1">
                  Map Name <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  id="mapName"
                  value={mapDialogData.name}
                  onChange={(e) => setMapDialogData({ ...mapDialogData, name: e.target.value })}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                  placeholder="e.g., Initial Mowing Direction"
                  required
                />
              </div>

              <div>
                <label htmlFor="mapDescription" className="block text-sm font-medium text-gray-700 mb-1">
                  Description
                </label>
                <textarea
                  id="mapDescription"
                  value={mapDialogData.description}
                  onChange={(e) => setMapDialogData({ ...mapDialogData, description: e.target.value })}
                  rows={3}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                  placeholder="Describe the purpose of this map (e.g., Direction for initial mowing of hay)"
                />
              </div>
            </div>

            <div className="mt-6 flex justify-end space-x-3">
              <button
                type="button"
                onClick={() => setMapDialogOpen(false)}
                className="px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                Cancel
              </button>
              <button
                type="button"
                onClick={handleMapDialogSubmit}
                className="px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                {mapDialogMode === 'add' ? 'Add Map' : 'Save Changes'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default HarvestDirectionMapComponent;
