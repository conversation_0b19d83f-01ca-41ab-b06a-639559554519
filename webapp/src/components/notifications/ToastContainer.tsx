import React from 'react';
import { useNotification } from '../../context/NotificationContext';
import Toast from './Toast';

const ToastContainer: React.FC = () => {
  const { notifications, dismissNotification } = useNotification();

  if (notifications.length === 0) {
    return null;
  }

  return (
    <div className="fixed top-4 right-4 z-50 flex flex-col items-end space-y-2 max-h-screen overflow-hidden">
      {notifications.map((notification) => (
        <Toast
          key={notification.id}
          notification={notification}
          onDismiss={dismissNotification}
        />
      ))}
    </div>
  );
};

export default ToastContainer;