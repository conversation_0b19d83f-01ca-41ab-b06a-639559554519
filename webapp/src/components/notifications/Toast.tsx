import React from 'react';
import { useNotification, Notification } from '../../context/NotificationContext';

interface ToastProps {
  notification: Notification;
  onDismiss: (id: string) => void;
}

const Toast: React.FC<ToastProps> = ({ notification, onDismiss }) => {
  const { id, type, title, message, onClick } = notification;

  // Define background color based on notification type
  const getBgColor = () => {
    switch (type) {
      case 'success':
        return 'bg-green-500';
      case 'error':
        return 'bg-red-500';
      case 'warning':
        return 'bg-yellow-500';
      default:
        return 'bg-blue-500';
    }
  };

  // Handle click on the toast
  const handleClick = () => {
    if (onClick) {
      onClick();
    }
    onDismiss(id);
  };

  return (
    <div
      className={`${getBgColor()} text-white p-4 rounded-lg shadow-lg mb-2 max-w-md w-full flex items-start transition-all duration-300 ease-in-out transform hover:scale-105`}
      onClick={handleClick}
      role="alert"
    >
      <div className="flex-grow">
        {title && <h4 className="font-bold">{title}</h4>}
        <p>{message}</p>
      </div>
      <button
        className="ml-4 text-white hover:text-gray-200 focus:outline-none"
        onClick={(e) => {
          e.stopPropagation();
          onDismiss(id);
        }}
        aria-label="Close notification"
      >
        <svg
          className="w-5 h-5"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="2"
            d="M6 18L18 6M6 6l12 12"
          ></path>
        </svg>
      </button>
    </div>
  );
};

export default Toast;