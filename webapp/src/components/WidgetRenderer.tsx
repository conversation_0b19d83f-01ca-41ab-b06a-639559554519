import React from 'react';
import { Widget } from '../context/DashboardContext';
import WeatherWidget from './WeatherWidget';
import TaskStatusWidget from './TaskStatusWidget';
import InventoryAlertWidget from './InventoryAlertWidget';
import FinancialSummaryWidget from './FinancialSummaryWidget';
import InventoryLevelsWidget from './InventoryLevelsWidget';
import TaskCompletionWidget from './TaskCompletionWidget';
import WeatherTrendsWidget from './WeatherTrendsWidget';
import GettingStartedWidget from './GettingStartedWidget';

import CropCalendarWidget from './CropCalendarWidget';
import FieldStatusWidget from './FieldStatusWidget';

// Dynamic imports for plugin widgets
// This will hold the dynamically loaded widget components
const PluginWidgets: {
  FieldHealthWidget: React.ComponentType<any> | null;
  MarketPriceWidget: React.ComponentType<any> | null;
} = {
  FieldHealthWidget: null,
  MarketPriceWidget: null
};

// Function to load plugin widgets
// const loadPluginWidgets = async () => {
//   try {
//     // Load the field health widget
//     const fieldHealthModule = await import('../../plugins/field-health-analytics');
//     if (fieldHealthModule && fieldHealthModule.FieldHealthWidget) {
//       PluginWidgets.FieldHealthWidget = fieldHealthModule.FieldHealthWidget;
//     }
//   } catch (error) {
//     console.error('Error loading Field Health widget:', error);
//   }
//
//   try {
//     // Load the market price widget
//     const marketPriceModule = await import('../../plugins/market-price-tracker');
//     if (marketPriceModule && marketPriceModule.MarketPriceWidget) {
//       PluginWidgets.MarketPriceWidget = marketPriceModule.MarketPriceWidget;
//     }
//   } catch (error) {
//     console.error('Error loading Market Price widget:', error);
//   }
// };

// Load the plugin widgets
//loadPluginWidgets();

interface WidgetRendererProps {
  widget: Widget;
  editMode: boolean;
  onRemove?: (widgetId: string) => void;
}

const WidgetRenderer: React.FC<WidgetRendererProps> = ({ widget, editMode, onRemove }) => {
  const [isLoading, setIsLoading] = React.useState<boolean>(true);

  // Effect to update loading state when plugin widgets are loaded
  React.useEffect(() => {
    // Check if the required plugin widget is loaded
    const checkWidgetLoaded = () => {
      if (widget.type === 'field-health' || widget.type === 'FieldHealthWidget') {
        return !!PluginWidgets.FieldHealthWidget;
      }
      if (widget.type === 'market-prices' || widget.type === 'MarketPricesWidget') {
        return !!PluginWidgets.MarketPriceWidget;
      }
      return true; // Not a plugin widget, so it's "loaded"
    };

    // If the widget is already loaded, set loading to false
    if (checkWidgetLoaded()) {
      setIsLoading(false);
      return;
    }

    // Otherwise, set up a timer to check periodically
    const timer = setInterval(() => {
      if (checkWidgetLoaded()) {
        setIsLoading(false);
        clearInterval(timer);
      }
    }, 100);

    return () => clearInterval(timer);
  }, [widget.type]);
  // Render the widget header with title and controls
  const renderWidgetHeader = () => (
    <div className={`widget-header p-3 border-b border-gray-200 bg-gray-50 flex justify-between items-center ${editMode ? 'cursor-move' : ''}`}>
      <h2 className="text-md font-medium text-gray-900">{widget.title}</h2>
      {editMode && onRemove && (
        <button
          onClick={() => onRemove(widget.id)}
          className="p-2 rounded-full bg-red-100 text-red-500 hover:bg-red-200 hover:text-red-700 transition-colors cursor-pointer"
          title="Remove widget"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
          </svg>
        </button>
      )}
    </div>
  );

  // Render the widget content based on its type
  const renderWidgetContent = () => {
    switch (widget.type) {
      case 'getting-started':
        return <GettingStartedWidget />;

      case 'weather':
        return <WeatherWidget />;

      case 'accounts':
        return (
          <div className="p-4">
            <div className="divide-y divide-gray-200">
              {widget.accounts && Array.isArray(widget.accounts) && widget.accounts.length > 0 ? (
                widget.accounts.map((account: { id: string; name: string; balance: number; type: string; institution?: string }) => (
                  <div key={account.id} className="py-3 flex justify-between items-center">
                    <div>
                      <h3 className="text-sm font-medium text-gray-900">{account.name}</h3>
                      <p className="text-xs text-gray-500">{account.institution} • {account.type}</p>
                    </div>
                    <div className={`text-sm font-medium ${account.balance < 0 ? 'text-red-600' : 'text-green-600'}`}>
                      {new Intl.NumberFormat('en-US', {
                        style: 'currency',
                        currency: 'USD'
                      }).format(account.balance)}
                    </div>
                  </div>
                ))
              ) : (
                <div className="py-3 text-center text-gray-500">
                  No accounts connected
                </div>
              )}
            </div>
          </div>
        );

      case 'quick-actions':
        return (
          <div className="p-4 space-y-2">
            {widget.actions && Array.isArray(widget.actions) && widget.actions.length > 0 ? (
              widget.actions.map((action: { id: string; label: string; link: string }) => (
                <a
                  key={action.id}
                  href={action.link}
                  className="btn btn-outline block w-full text-center"
                >
                  {action.label}
                </a>
              ))
            ) : (
              <div className="text-center text-gray-500">
                No actions available
              </div>
            )}
          </div>
        );

      case 'recent-transactions':
        return (
          <div className="p-4">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th scope="col" className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Date
                    </th>
                    <th scope="col" className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Description
                    </th>
                    <th scope="col" className="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Amount
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {widget.transactions && Array.isArray(widget.transactions) && widget.transactions.length > 0 ? (
                    widget.transactions.map((transaction: { id: string; transaction_date: string; description: string; amount: number }) => (
                      <tr key={transaction.id} className="hover:bg-gray-50">
                        <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-500">
                          {new Date(transaction.transaction_date).toLocaleDateString()}
                        </td>
                        <td className="px-4 py-2 whitespace-nowrap text-sm font-medium text-gray-900">
                          {transaction.description}
                        </td>
                        <td className={`px-4 py-2 whitespace-nowrap text-sm text-right font-medium ${transaction.amount < 0 ? 'text-red-600' : 'text-green-600'}`}>
                          {new Intl.NumberFormat('en-US', {
                            style: 'currency',
                            currency: 'USD'
                          }).format(transaction.amount)}
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan={3} className="px-4 py-2 text-center text-gray-500">
                        No transactions available
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </div>
        );

      case 'task-status':
        return <TaskStatusWidget />;

      case 'inventory-alerts':
        return <InventoryAlertWidget />;

      // Plugin widgets
      case 'field-health':
      case 'FieldHealthWidget':
        if (isLoading) {
          return (
            <div className="flex justify-center items-center h-full">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary-600"></div>
              <span className="ml-2 text-gray-500">Loading Field Health widget...</span>
            </div>
          );
        }
        if (PluginWidgets.FieldHealthWidget) {
          return <PluginWidgets.FieldHealthWidget />;
        }
        return (
          <div className="p-4 text-center text-gray-500">
            Field Health widget not available. Plugin may not be enabled.
          </div>
        );

      case 'market-prices':
      case 'MarketPricesWidget':
        if (isLoading) {
          return (
            <div className="flex justify-center items-center h-full">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary-600"></div>
              <span className="ml-2 text-gray-500">Loading Market Prices widget...</span>
            </div>
          );
        }
        if (PluginWidgets.MarketPriceWidget) {
          return <PluginWidgets.MarketPriceWidget />;
        }
        return (
          <div className="p-4 text-center text-gray-500">
            Market Prices widget not available. Plugin may not be enabled.
          </div>
        );

      // Chart widgets
      case 'financial-summary':
        return <FinancialSummaryWidget />;

      case 'inventory-levels':
        return <InventoryLevelsWidget />;

      case 'task-completion':
        return <TaskCompletionWidget />;

      case 'weather-trends':
        return <WeatherTrendsWidget />;

      case 'crop-calendar':
        return <CropCalendarWidget />;

      case 'field-status':
        return <FieldStatusWidget />;

      default:
        return (
          <div className="p-4 text-center text-gray-500">
            Unknown widget type: {widget.type}
          </div>
        );
    }
  };

  return (
    <div className="widget-container flex flex-col">
      {renderWidgetHeader()}
      <div className="widget-content-container">
        {renderWidgetContent()}
      </div>
    </div>
  );
};

export default WidgetRenderer;
