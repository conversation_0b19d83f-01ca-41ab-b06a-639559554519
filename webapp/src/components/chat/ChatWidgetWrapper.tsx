import React, { useState, useEffect } from 'react';
import { ChatProvider } from '../../context/ChatContext';
import ChatWidget from './ChatWidget';
import { useAuth } from '../../context/AuthContext';
import { useFarm } from '../../context/FarmContext';
import { checkPermission } from '../../services/rolePermissionService';
import axios from 'axios';
import { API_URL } from '../../config';

/**
 * Wrapper component that provides the ChatProvider context to the ChatWidget
 * and checks for chat permissions before rendering the chat widget
 */
const ChatWidgetWrapper: React.FC = () => {
  const { user } = useAuth();
  const { currentFarm } = useFarm();
  const [hasPermission, setHasPermission] = useState<boolean>(false);
  const [hasFarmChatPermission, setHasFarmChatPermission] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  // Check if the user has permission to view the chat feature
  useEffect(() => {
    const checkChatPermission = async () => {
      if (!user || !currentFarm) {
        setHasPermission(false);
        setHasFarmChatPermission(false);
        setIsLoading(false);
        return;
      }

      try {
        // Check if the user has permission to view the chat feature
        const canViewChat = await checkPermission(
          currentFarm.id,
          user.id,
          'chat',
          'view'
        );

        setHasPermission(canViewChat);

        // Check if the farm has any active chat permissions with associated farms
        try {
          const permissionsResponse = await axios.get(`${API_URL}/farm-association-permissions/farm/${currentFarm.id}`);
          const permissions = Array.isArray(permissionsResponse.data) ? permissionsResponse.data : [];

          // Check if there are any active chat permissions
          const hasChatPermission = permissions.some(
            (permission: any) => permission.permission_type === 'chat' && permission.status === 'active'
          );

          setHasFarmChatPermission(hasChatPermission);
        } catch (permError) {
          console.error('Error checking farm chat permissions:', permError);
          // Default to false if there's an error
          setHasFarmChatPermission(false);
        }
      } catch (error) {
        console.error('Error checking chat permission:', error);
        // Default to allowing access if there's an error
        setHasPermission(true);
        setHasFarmChatPermission(false);
      } finally {
        setIsLoading(false);
      }
    };

    checkChatPermission();
  }, [user, currentFarm]);

  // Don't render anything while loading
  if (isLoading) {
    return null;
  }

  // Only show the chat widget if the user has permission and the farm has active chat permissions with associated farms
  if (!hasPermission || !hasFarmChatPermission) {
    return null;
  }

  return (
    <ChatWidget position="bottom-left" />
  );
};

export default ChatWidgetWrapper;
