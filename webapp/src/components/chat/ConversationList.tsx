import React, { useEffect, useState } from 'react';
import { useChat, Conversation } from '../../context/ChatContext';
import { formatDistanceToNow } from 'date-fns';
import SearchIcon from './icons/SearchIcon';
import PlusIcon from './icons/PlusIcon';
import NewConversationModal from './NewConversationModal';
import NotificationBadge from './NotificationBadge';

interface ConversationListProps {
  onSelectConversation: (id: string) => void;
}

/**
 * Conversation list component that displays a list of conversations
 */
const ConversationList: React.FC<ConversationListProps> = ({ onSelectConversation }) => {
  const { conversations, loadConversations, isLoading } = useChat();
  const [searchTerm, setSearchTerm] = useState('');
  const [isNewConversationModalOpen, setIsNewConversationModalOpen] = useState(false);

  // Load conversations on mount
  useEffect(() => {
    loadConversations();
  }, [loadConversations]);

  // Filter conversations by search term
  const filteredConversations = conversations.filter(conversation => {
    const name = conversation.name || 'Direct Message';
    return name.toLowerCase().includes(searchTerm.toLowerCase());
  });

  // Get conversation name and last message preview
  const getConversationDetails = (conversation: Conversation) => {
    const name = conversation.name || 'Direct Message';

    // If it's a direct message, use the other participant's name
    if (conversation.type === 'direct' && conversation.participants && conversation.participants.length > 0) {
      const otherParticipant = conversation.participants[0];
      return `${otherParticipant.first_name} ${otherParticipant.last_name}`;
    }

    return name;
  };

  // Format the last message time
  const formatLastMessageTime = (time: string) => {
    return formatDistanceToNow(new Date(time), { addSuffix: true });
  };

  return (
    <div className="flex flex-col w-full">
      {/* Search and new conversation */}
      <div className="p-3 border-b">
        <div className="flex items-center bg-gray-100 rounded-md p-2">
          <SearchIcon size={18} color="#6B7280" />
          <input
            type="text"
            placeholder="Search conversations..."
            className="bg-transparent border-none focus:outline-none ml-2 w-full"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <button
          onClick={() => setIsNewConversationModalOpen(true)}
          className="mt-2 w-full bg-blue-600 hover:bg-blue-700 text-white rounded-md p-2 flex items-center justify-center"
        >
          <PlusIcon size={18} />
          <span className="ml-2">New Conversation</span>
        </button>
      </div>

      {/* Conversations list */}
      <div className="flex-1 overflow-y-auto">
        {isLoading ? (
          <div className="flex items-center justify-center h-32">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        ) : filteredConversations.length === 0 ? (
          <div className="text-center p-4 text-gray-500">
            {searchTerm ? 'No conversations found' : 'No conversations yet'}
          </div>
        ) : (
          filteredConversations.map(conversation => (
            <div
              key={conversation.id}
              onClick={() => onSelectConversation(conversation.id)}
              className="p-3 border-b hover:bg-gray-50 cursor-pointer flex items-center"
            >
              {/* Avatar or icon */}
              <div className="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 font-semibold">
                {getConversationDetails(conversation).charAt(0)}
              </div>

              {/* Conversation details */}
              <div className="ml-3 flex-1 min-w-0">
                <div className="flex justify-between items-center">
                  <h3 className="text-sm font-semibold truncate">
                    {getConversationDetails(conversation)}
                  </h3>
                  {conversation.updated_at && (
                    <span className="text-xs text-gray-500">
                      {formatLastMessageTime(conversation.updated_at)}
                    </span>
                  )}
                </div>

                {/* Last message preview */}
                <p className="text-xs text-gray-500 truncate">
                  {conversation.last_message ? conversation.last_message.content : 
                   conversation.type === 'direct' ? 'Start a conversation' : 'Group conversation'}
                </p>
              </div>

              {/* Unread count */}
              <div className="ml-2 relative">
                <NotificationBadge 
                  count={conversation.unread_count || 0} 
                  className="static" 
                />
              </div>
            </div>
          ))
        )}
      </div>

      {/* New conversation modal */}
      {isNewConversationModalOpen && (
        <NewConversationModal
          onClose={() => setIsNewConversationModalOpen(false)}
          onConversationCreated={(conversationId) => {
            setIsNewConversationModalOpen(false);
            onSelectConversation(conversationId);
          }}
        />
      )}
    </div>
  );
};

export default ConversationList;
