import React from 'react';

interface UnpinIconProps {
  size?: number;
  color?: string;
}

/**
 * Unpin icon component
 */
const UnpinIcon: React.FC<UnpinIconProps> = ({ 
  size = 24, 
  color = 'currentColor' 
}) => {
  return (
    <svg 
      xmlns="http://www.w3.org/2000/svg" 
      width={size} 
      height={size} 
      viewBox="0 0 24 24" 
      fill="none" 
      stroke={color} 
      strokeWidth="2" 
      strokeLinecap="round" 
      strokeLinejoin="round"
    >
      <path d="M12 22L12 18"></path>
      <path d="M5 5H19L18 10H6L5 5Z"></path>
      <path d="M8 10V16L6 19L18 19L16 16V10"></path>
      <line x1="4" y1="2" x2="20" y2="2"></line>
    </svg>
  );
};

export default UnpinIcon;