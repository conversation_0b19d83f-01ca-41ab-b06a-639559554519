import React, { useState } from 'react';
import ConversationList from './ConversationList';
import MessageArea from './MessageArea';
import PinIcon from './icons/PinIcon';
import CloseIcon from './icons/CloseIcon';

interface ChatWindowProps {
  onClose: () => void;
  onPin: () => void;
  isPinned: boolean;
}

/**
 * Chat window component that displays the conversation list and message area
 */
const ChatWindow: React.FC<ChatWindowProps> = ({ onClose, onPin, isPinned }) => {
  const [activeConversationId, setActiveConversationId] = useState<string | null>(null);
  
  return (
    <div className="bg-white rounded-lg shadow-xl flex flex-col w-96 h-[500px] overflow-hidden">
      {/* Header */}
      <div className="bg-blue-600 text-white p-3 flex justify-between items-center">
        <h2 className="text-lg font-semibold">Chat</h2>
        <div className="flex space-x-2">
          <button
            onClick={onPin}
            className="text-white hover:text-blue-200 focus:outline-none"
            aria-label="Pin chat"
          >
            <PinIcon size={18} />
          </button>
          <button
            onClick={onClose}
            className="text-white hover:text-blue-200 focus:outline-none"
            aria-label="Close chat"
          >
            <CloseIcon size={18} />
          </button>
        </div>
      </div>
      
      {/* Content */}
      <div className="flex flex-1 overflow-hidden">
        {!activeConversationId ? (
          <ConversationList 
            onSelectConversation={(id) => setActiveConversationId(id)} 
          />
        ) : (
          <MessageArea 
            conversationId={activeConversationId}
            onBack={() => setActiveConversationId(null)}
          />
        )}
      </div>
    </div>
  );
};

export default ChatWindow;