/* Editor container */
.lexical-editor-container {
  background-color: white;
  position: relative;
}

/* Editor input area */
.editor-input {
  font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  color: #374151;
  position: relative;
  resize: none;
  font-size: 15px;
  caret-color: rgb(5, 5, 5);
  display: block;
}

/* Placeholder text */
.editor-placeholder {
  color: #999;
  overflow: hidden;
  position: absolute;
  text-overflow: ellipsis;
  top: 15px;
  left: 15px;
  user-select: none;
  display: inline-block;
  pointer-events: none;
}

/* Paragraph styling */
.editor-paragraph {
  margin: 0 0 15px 0;
  position: relative;
}

/* Headings */
.editor-heading-h1 {
  font-size: 24px;
  font-weight: 700;
  margin: 0 0 15px 0;
}

.editor-heading-h2 {
  font-size: 20px;
  font-weight: 700;
  margin: 0 0 15px 0;
}

.editor-heading-h3 {
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 15px 0;
}

.editor-heading-h4 {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 15px 0;
}

.editor-heading-h5 {
  font-size: 15px;
  font-weight: 600;
  margin: 0 0 15px 0;
}

/* Lists */
.editor-list-ol {
  padding: 0;
  margin: 0 0 15px 16px;
}

.editor-list-ul {
  padding: 0;
  margin: 0 0 15px 16px;
}

.editor-listitem {
  margin: 8px 32px;
}

.editor-nested-listitem {
  list-style-type: none;
}

/* Quote */
.editor-quote {
  margin: 0 0 15px 0;
  padding-left: 16px;
  border-left: 4px solid #ccc;
  color: #666;
}

/* Text formatting */
.editor-text-bold {
  font-weight: bold;
}

.editor-text-italic {
  font-style: italic;
}

.editor-text-underline {
  text-decoration: underline;
}

.editor-text-strikethrough {
  text-decoration: line-through;
}

.editor-text-underlineStrikethrough {
  text-decoration: underline line-through;
}

.editor-text-code {
  background-color: rgb(240, 242, 245);
  padding: 1px 0.25rem;
  font-family: Menlo, Consolas, Monaco, monospace;
  font-size: 94%;
  border-radius: 0.25rem;
}

.editor-link {
  color: rgb(33, 111, 219);
  text-decoration: none;
}

.editor-code {
  background-color: rgb(240, 242, 245);
  font-family: Menlo, Consolas, Monaco, monospace;
  display: block;
  padding: 8px 8px 8px 52px;
  line-height: 1.53;
  font-size: 13px;
  margin: 0 0 15px 0;
  tab-size: 2;
  overflow-x: auto;
  position: relative;
  border-radius: 0.25rem;
}

/* Code highlighting */
.editor-tokenComment {
  color: slategray;
}

.editor-tokenPunctuation {
  color: #999;
}

.editor-tokenProperty {
  color: #905;
}

.editor-tokenSelector {
  color: #690;
}

.editor-tokenOperator {
  color: #9a6e3a;
}

.editor-tokenAttr {
  color: #07a;
}

.editor-tokenVariable {
  color: #e90;
}

.editor-tokenFunction {
  color: #dd4a68;
}