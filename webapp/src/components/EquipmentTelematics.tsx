import React, { useState, useEffect } from 'react';
import { 
  getLatestTelematics, 
  getEquipmentTelematics, 
  getAggregatedTelematics,
  TelematicsData,
  AggregatedTelematicsData
} from '../services/telematicsService';

interface EquipmentTelematicsProps {
  equipmentId: string;
}

const EquipmentTelematics: React.FC<EquipmentTelematicsProps> = ({ equipmentId }) => {
  const [latestData, setLatestData] = useState<TelematicsData | null>(null);
  const [historicalData, setHistoricalData] = useState<TelematicsData[]>([]);
  const [aggregatedData, setAggregatedData] = useState<AggregatedTelematicsData[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'latest' | 'historical' | 'aggregated'>('latest');
  const [dateRange, setDateRange] = useState<{
    startDate: string;
    endDate: string;
  }>({
    startDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 7 days ago
    endDate: new Date().toISOString().split('T')[0] // today
  });
  const [aggregationInterval, setAggregationInterval] = useState<'hour' | 'day' | 'week' | 'month'>('day');

  // Fetch latest telematics data
  useEffect(() => {
    const fetchLatestData = async () => {
      try {
        setLoading(true);
        setError(null);
        const data = await getLatestTelematics(equipmentId);
        setLatestData(data);
      } catch (err: any) {
        console.error('Error fetching latest telematics data:', err);
        setError('Failed to load latest telematics data. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchLatestData();
  }, [equipmentId]);

  // Fetch historical data when tab changes to historical or date range changes
  useEffect(() => {
    if (activeTab === 'historical') {
      const fetchHistoricalData = async () => {
        try {
          setLoading(true);
          setError(null);
          const response = await getEquipmentTelematics(
            equipmentId,
            dateRange.startDate,
            dateRange.endDate
          );
          setHistoricalData(response.telematics);
        } catch (err: any) {
          console.error('Error fetching historical telematics data:', err);
          setError('Failed to load historical telematics data. Please try again later.');
        } finally {
          setLoading(false);
        }
      };

      fetchHistoricalData();
    }
  }, [equipmentId, activeTab, dateRange]);

  // Fetch aggregated data when tab changes to aggregated or date range/interval changes
  useEffect(() => {
    if (activeTab === 'aggregated') {
      const fetchAggregatedData = async () => {
        try {
          setLoading(true);
          setError(null);
          const data = await getAggregatedTelematics(
            equipmentId,
            dateRange.startDate,
            dateRange.endDate,
            aggregationInterval
          );
          setAggregatedData(data);
        } catch (err: any) {
          console.error('Error fetching aggregated telematics data:', err);
          setError('Failed to load aggregated telematics data. Please try again later.');
        } finally {
          setLoading(false);
        }
      };

      fetchAggregatedData();
    }
  }, [equipmentId, activeTab, dateRange, aggregationInterval]);

  // Format date for display
  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleString();
  };

  // Handle date range change
  const handleDateRangeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setDateRange(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle aggregation interval change
  const handleIntervalChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setAggregationInterval(e.target.value as 'hour' | 'day' | 'week' | 'month');
  };

  if (loading && !latestData && !historicalData.length && !aggregatedData.length) {
    return (
      <div className="bg-white shadow overflow-hidden sm:rounded-lg mb-6">
        <div className="px-4 py-5 sm:px-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900">Equipment Telematics</h3>
          <p className="mt-1 max-w-2xl text-sm text-gray-500">Real-time and historical telematics data.</p>
        </div>
        <div className="border-t border-gray-200 p-6 flex justify-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
          <p className="ml-3 text-gray-500">Loading telematics data...</p>
        </div>
      </div>
    );
  }

  if (error && !latestData && !historicalData.length && !aggregatedData.length) {
    return (
      <div className="bg-white shadow overflow-hidden sm:rounded-lg mb-6">
        <div className="px-4 py-5 sm:px-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900">Equipment Telematics</h3>
          <p className="mt-1 max-w-2xl text-sm text-gray-500">Real-time and historical telematics data.</p>
        </div>
        <div className="border-t border-gray-200">
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 m-4 rounded relative" role="alert">
            <span className="block sm:inline">{error}</span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white shadow overflow-hidden sm:rounded-lg mb-6">
      <div className="px-4 py-5 sm:px-6">
        <h3 className="text-lg leading-6 font-medium text-gray-900">Equipment Telematics</h3>
        <p className="mt-1 max-w-2xl text-sm text-gray-500">Real-time and historical telematics data.</p>
      </div>

      {/* Tabs */}
      <div className="border-t border-gray-200">
        <div className="flex border-b">
          <button
            className={`px-4 py-2 text-sm font-medium ${
              activeTab === 'latest'
                ? 'border-b-2 border-primary-500 text-primary-600'
                : 'text-gray-500 hover:text-gray-700'
            }`}
            onClick={() => setActiveTab('latest')}
          >
            Latest Data
          </button>
          <button
            className={`px-4 py-2 text-sm font-medium ${
              activeTab === 'historical'
                ? 'border-b-2 border-primary-500 text-primary-600'
                : 'text-gray-500 hover:text-gray-700'
            }`}
            onClick={() => setActiveTab('historical')}
          >
            Historical Data
          </button>
          <button
            className={`px-4 py-2 text-sm font-medium ${
              activeTab === 'aggregated'
                ? 'border-b-2 border-primary-500 text-primary-600'
                : 'text-gray-500 hover:text-gray-700'
            }`}
            onClick={() => setActiveTab('aggregated')}
          >
            Aggregated Data
          </button>
        </div>

        {/* Date range selector for historical and aggregated tabs */}
        {(activeTab === 'historical' || activeTab === 'aggregated') && (
          <div className="p-4 bg-gray-50 border-b border-gray-200">
            <div className="flex flex-wrap items-center gap-4">
              <div>
                <label htmlFor="startDate" className="block text-sm font-medium text-gray-700">
                  Start Date
                </label>
                <input
                  type="date"
                  id="startDate"
                  name="startDate"
                  value={dateRange.startDate}
                  onChange={handleDateRangeChange}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
                />
              </div>
              <div>
                <label htmlFor="endDate" className="block text-sm font-medium text-gray-700">
                  End Date
                </label>
                <input
                  type="date"
                  id="endDate"
                  name="endDate"
                  value={dateRange.endDate}
                  onChange={handleDateRangeChange}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
                />
              </div>

              {/* Interval selector for aggregated tab */}
              {activeTab === 'aggregated' && (
                <div>
                  <label htmlFor="interval" className="block text-sm font-medium text-gray-700">
                    Interval
                  </label>
                  <select
                    id="interval"
                    name="interval"
                    value={aggregationInterval}
                    onChange={handleIntervalChange}
                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
                  >
                    <option value="hour">Hourly</option>
                    <option value="day">Daily</option>
                    <option value="week">Weekly</option>
                    <option value="month">Monthly</option>
                  </select>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Tab content */}
        <div className="p-4">
          {/* Latest Data Tab */}
          {activeTab === 'latest' && (
            <div>
              {loading && !latestData ? (
                <div className="flex justify-center py-4">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
                  <p className="ml-3 text-gray-500">Loading latest data...</p>
                </div>
              ) : latestData ? (
                <dl className="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2 lg:grid-cols-3">
                  <div className="sm:col-span-2 lg:col-span-3">
                    <dt className="text-sm font-medium text-gray-500">Last Updated</dt>
                    <dd className="mt-1 text-sm text-gray-900">{formatDate(latestData.timestamp)}</dd>
                  </div>

                  {latestData.latitude && latestData.longitude && (
                    <div>
                      <dt className="text-sm font-medium text-gray-500">Location</dt>
                      <dd className="mt-1 text-sm text-gray-900">
                        {latestData.latitude.toFixed(6)}, {latestData.longitude.toFixed(6)}
                        {latestData.altitude && `, Alt: ${latestData.altitude.toFixed(2)}m`}
                      </dd>
                    </div>
                  )}

                  {latestData.engine_hours !== null && (
                    <div>
                      <dt className="text-sm font-medium text-gray-500">Engine Hours</dt>
                      <dd className="mt-1 text-sm text-gray-900">{latestData.engine_hours.toFixed(2)} hrs</dd>
                    </div>
                  )}

                  {latestData.odometer !== null && (
                    <div>
                      <dt className="text-sm font-medium text-gray-500">Odometer</dt>
                      <dd className="mt-1 text-sm text-gray-900">{latestData.odometer.toFixed(2)} km</dd>
                    </div>
                  )}

                  {latestData.fuel_level !== null && (
                    <div>
                      <dt className="text-sm font-medium text-gray-500">Fuel Level</dt>
                      <dd className="mt-1 text-sm text-gray-900">{latestData.fuel_level.toFixed(2)}%</dd>
                    </div>
                  )}

                  {latestData.fuel_consumption_rate !== null && (
                    <div>
                      <dt className="text-sm font-medium text-gray-500">Fuel Consumption</dt>
                      <dd className="mt-1 text-sm text-gray-900">{latestData.fuel_consumption_rate.toFixed(2)} L/h</dd>
                    </div>
                  )}

                  {latestData.engine_rpm !== null && (
                    <div>
                      <dt className="text-sm font-medium text-gray-500">Engine RPM</dt>
                      <dd className="mt-1 text-sm text-gray-900">{latestData.engine_rpm} RPM</dd>
                    </div>
                  )}

                  {latestData.engine_load !== null && (
                    <div>
                      <dt className="text-sm font-medium text-gray-500">Engine Load</dt>
                      <dd className="mt-1 text-sm text-gray-900">{latestData.engine_load.toFixed(2)}%</dd>
                    </div>
                  )}

                  {latestData.engine_temperature !== null && (
                    <div>
                      <dt className="text-sm font-medium text-gray-500">Engine Temperature</dt>
                      <dd className="mt-1 text-sm text-gray-900">{latestData.engine_temperature.toFixed(2)}°C</dd>
                    </div>
                  )}

                  {latestData.operational_status && (
                    <div>
                      <dt className="text-sm font-medium text-gray-500">Status</dt>
                      <dd className="mt-1 text-sm text-gray-900">
                        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                          latestData.operational_status === 'running' ? 'bg-green-100 text-green-800' : 
                          latestData.operational_status === 'idle' ? 'bg-yellow-100 text-yellow-800' : 
                          latestData.operational_status === 'off' ? 'bg-gray-100 text-gray-800' : 
                          latestData.operational_status === 'error' ? 'bg-red-100 text-red-800' : 
                          'bg-blue-100 text-blue-800'
                        }`}>
                          {latestData.operational_status.charAt(0).toUpperCase() + latestData.operational_status.slice(1)}
                        </span>
                      </dd>
                    </div>
                  )}

                  {latestData.diagnostic_codes && Object.keys(latestData.diagnostic_codes).length > 0 && (
                    <div className="sm:col-span-2 lg:col-span-3">
                      <dt className="text-sm font-medium text-gray-500">Diagnostic Codes</dt>
                      <dd className="mt-1 text-sm text-gray-900">
                        <ul className="list-disc pl-5">
                          {Object.entries(latestData.diagnostic_codes).map(([code, description]) => (
                            <li key={code}>
                              <span className="font-medium">{code}</span>: {String(description)}
                            </li>
                          ))}
                        </ul>
                      </dd>
                    </div>
                  )}
                </dl>
              ) : (
                <p className="text-sm text-gray-500 py-4">No telematics data available for this equipment.</p>
              )}
            </div>
          )}

          {/* Historical Data Tab */}
          {activeTab === 'historical' && (
            <div>
              {loading ? (
                <div className="flex justify-center py-4">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
                  <p className="ml-3 text-gray-500">Loading historical data...</p>
                </div>
              ) : historicalData.length > 0 ? (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Timestamp
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Location
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Engine Hours
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Odometer
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Fuel Level
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Status
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {historicalData.map((data) => (
                        <tr key={data.id}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {formatDate(data.timestamp)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {data.latitude && data.longitude
                              ? `${data.latitude.toFixed(6)}, ${data.longitude.toFixed(6)}`
                              : 'N/A'}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {data.engine_hours !== null ? `${data.engine_hours.toFixed(2)} hrs` : 'N/A'}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {data.odometer !== null ? `${data.odometer.toFixed(2)} km` : 'N/A'}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {data.fuel_level !== null ? `${data.fuel_level.toFixed(2)}%` : 'N/A'}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {data.operational_status ? (
                              <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                                data.operational_status === 'running' ? 'bg-green-100 text-green-800' : 
                                data.operational_status === 'idle' ? 'bg-yellow-100 text-yellow-800' : 
                                data.operational_status === 'off' ? 'bg-gray-100 text-gray-800' : 
                                data.operational_status === 'error' ? 'bg-red-100 text-red-800' : 
                                'bg-blue-100 text-blue-800'
                              }`}>
                                {data.operational_status.charAt(0).toUpperCase() + data.operational_status.slice(1)}
                              </span>
                            ) : 'N/A'}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <p className="text-sm text-gray-500 py-4">No historical telematics data available for this equipment in the selected date range.</p>
              )}
            </div>
          )}

          {/* Aggregated Data Tab */}
          {activeTab === 'aggregated' && (
            <div>
              {loading ? (
                <div className="flex justify-center py-4">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
                  <p className="ml-3 text-gray-500">Loading aggregated data...</p>
                </div>
              ) : aggregatedData.length > 0 ? (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Time Period
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Engine Hours
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Odometer
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Avg. Fuel Level
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Avg. Fuel Consumption
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Data Points
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {aggregatedData.map((data, index) => (
                        <tr key={index}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {data.time_period}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {data.avg_engine_hours !== null ? (
                              <div>
                                <div>Avg: {data.avg_engine_hours.toFixed(2)} hrs</div>
                                <div className="text-xs text-gray-400">Change: {data.engine_hours_change.toFixed(2)} hrs</div>
                              </div>
                            ) : 'N/A'}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {data.avg_odometer !== null ? (
                              <div>
                                <div>Avg: {data.avg_odometer.toFixed(2)} km</div>
                                <div className="text-xs text-gray-400">Change: {data.odometer_change.toFixed(2)} km</div>
                              </div>
                            ) : 'N/A'}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {data.avg_fuel_level !== null ? `${data.avg_fuel_level.toFixed(2)}%` : 'N/A'}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {data.avg_fuel_consumption !== null ? `${data.avg_fuel_consumption.toFixed(2)} L/h` : 'N/A'}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {data.data_points}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <p className="text-sm text-gray-500 py-4">No aggregated telematics data available for this equipment in the selected date range.</p>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default EquipmentTelematics;
