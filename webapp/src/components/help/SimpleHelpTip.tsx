import React from 'react';
import HelpTip from './HelpTip';
import { HelpTip as HelpTipType } from '../../context/HelpContext';

interface SimpleHelpTipProps {
  content: string;
  position: 'top' | 'right' | 'bottom' | 'left';
}

const SimpleHelpTip: React.FC<SimpleHelpTipProps> = ({ content, position }) => {
  // Create a HelpTipType object from the props
  const tip: HelpTipType = {
    id: 'simple-help-tip',
    title: 'Help',
    content,
    pagePath: '',
    position,
    order: 0,
    isActive: true,
    createdAt: '',
    updatedAt: ''
  };

  return <HelpTip tip={tip} />;
};

export default SimpleHelpTip;