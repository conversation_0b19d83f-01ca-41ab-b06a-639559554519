import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, Link, useNavigate } from 'react-router-dom';
import { useHelp, HelpGuide } from '../../context/HelpContext';
import { ChevronLeftIcon } from '@heroicons/react/24/outline';
import { markdownToHtml } from '../../utils/markdownUtils';
import Layout from '../Layout';

const HelpGuideDetail: React.FC = () => {
  const { slug } = useParams<{ slug: string }>();
  const { getGuideBySlug, guides, loading, error } = useHelp();
  const [guide, setGuide] = useState<HelpGuide | null>(null);
  const [relatedGuides, setRelatedGuides] = useState<HelpGuide[]>([]);
  const navigate = useNavigate();

  useEffect(() => {
    const fetchGuide = async () => {
      if (slug) {
        const fetchedGuide = await getGuideBySlug(slug);
        if (fetchedGuide) {
          setGuide(fetchedGuide);

          // Find related guides (same category or tags)
          const related = guides.filter(g => 
            g.id !== fetchedGuide.id && (
              g.category === fetchedGuide.category ||
              g.tags.some(tag => fetchedGuide.tags.includes(tag))
            )
          ).slice(0, 3);

          setRelatedGuides(related);
        }
      }
    };

    fetchGuide();
  }, [slug, getGuideBySlug, guides]);

  if (loading) {
    return (
      <Layout>
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
        </div>
      </Layout>
    );
  }

  if (error) {
    return (
      <Layout>
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
          <strong className="font-bold">Error!</strong>
          <span className="block sm:inline"> {error}</span>
        </div>
      </Layout>
    );
  }

  if (!guide) {
    return (
      <Layout>
        <div className="text-center p-8">
          <h2 className="text-2xl font-semibold text-gray-900 mb-4">Guide Not Found</h2>
          <p className="text-gray-600 mb-6">
            The help guide you're looking for doesn't exist or has been moved.
          </p>
          <button
            onClick={() => navigate('/help')}
            className="inline-flex items-center px-4 py-2 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700"
          >
            <ChevronLeftIcon className="h-5 w-5 mr-2" />
            Back to Help Center
          </button>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex justify-between items-center mb-6">
          <div>
            <button
              onClick={() => navigate('/help')}
              className="inline-flex items-center text-gray-600 hover:text-gray-900"
            >
              <ChevronLeftIcon className="h-5 w-5 mr-1" />
              Back to Help Center
            </button>
          </div>
          <div>
            <Link to="/support" className="text-primary-600 hover:text-primary-800 flex items-center">
              Back to Support
            </Link>
          </div>
        </div>

        <div className="flex flex-col lg:flex-row gap-8">
          {/* Main content */}
          <div className="lg:w-3/4">
            <div className="bg-white shadow rounded-lg overflow-hidden">
              <div className="px-6 py-4 bg-gray-50 border-b border-gray-200">
                <div className="flex flex-wrap items-center text-sm text-gray-500 mb-2">
                  <span>{guide.category}</span>
                  {guide.subcategory && (
                    <>
                      <span className="mx-2">›</span>
                      <span>{guide.subcategory}</span>
                    </>
                  )}
                </div>
                <h1 className="text-2xl font-bold text-gray-900">{guide.title}</h1>
              </div>

              <div className="p-6">
                {/* Render Markdown content as HTML */}
                <div 
                  className="prose prose-headings:text-primary-800 prose-a:text-primary-600 prose-a:no-underline hover:prose-a:underline prose-strong:text-primary-700 prose-code:text-primary-800 prose-code:bg-primary-50 prose-code:px-1 prose-code:py-0.5 prose-code:rounded prose-code:before:content-none prose-code:after:content-none prose-li:marker:text-primary-500 max-w-none"
                  dangerouslySetInnerHTML={{ __html: markdownToHtml(guide.content) }}
                />

                {/* Tags */}
                {guide.tags.length > 0 && (
                  <div className="mt-8 pt-6 border-t border-gray-200">
                    <h3 className="text-sm font-medium text-gray-500 mb-3">Related Topics</h3>
                    <div className="flex flex-wrap gap-2">
                      {guide.tags.map(tag => (
                        <span 
                          key={tag} 
                          className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800"
                        >
                          {tag}
                        </span>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="lg:w-1/4">
            {/* Related guides */}
            {relatedGuides.length > 0 && (
              <div className="bg-white shadow rounded-lg overflow-hidden mb-6">
                <div className="px-4 py-3 bg-gray-50 border-b border-gray-200">
                  <h2 className="text-lg font-medium text-gray-900">Related Guides</h2>
                </div>
                <div className="p-4">
                  <ul className="space-y-3">
                    {relatedGuides.map(relatedGuide => (
                      <li key={relatedGuide.id}>
                        <Link 
                          to={`/help/guides/${relatedGuide.slug}`}
                          className="block p-3 hover:bg-gray-50 rounded-md"
                        >
                          <h3 className="text-sm font-medium text-gray-900 mb-1">{relatedGuide.title}</h3>
                          <p className="text-xs text-gray-500">
                            {relatedGuide.category} {relatedGuide.subcategory ? `› ${relatedGuide.subcategory}` : ''}
                          </p>
                        </Link>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            )}

            {/* Help box */}
            <div className="bg-blue-50 rounded-lg p-4 border border-blue-100">
              <h3 className="text-lg font-medium text-blue-800 mb-2">Need more help?</h3>
              <p className="text-sm text-blue-700 mb-4">
                If you couldn't find what you're looking for, our support team is here to help.
              </p>
              <Link
                to="/support"
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700"
              >
                Contact Support
              </Link>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default HelpGuideDetail;