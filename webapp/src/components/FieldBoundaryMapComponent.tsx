import React, { useState, useEffect, useRef, useCallback } from 'react';
import { GoogleMap, DrawingManager, Polygon } from '@react-google-maps/api';
import { loadGoogleMapsApi, isGoogleMapsLoaded } from '../utils/googleMapsLoader';

// Define the shape of the coordinates for a polygon
interface PolygonCoordinates {
  lat: number;
  lng: number;
}

interface FieldBoundaryMapComponentProps {
  value: string; // GeoJSON string for field boundaries
  onChange: (value: string) => void;
  center?: { lat: number; lng: number };
  height?: string;
  width?: string;
}

// Default map center (can be overridden by props)
const defaultCenter = { lat: 39.8283, lng: -98.5795 }; // Center of the US

const FieldBoundaryMapComponent: React.FC<FieldBoundaryMapComponentProps> = ({
  value,
  onChange,
  center,
  height = '400px',
  width = '100%',
}) => {
  // Reference to the map instance
  const fieldMapRef = useRef<google.maps.Map | null>(null);

  // State for the drawn polygon paths (field boundaries)
  const [fieldPaths, setFieldPaths] = useState<PolygonCoordinates[][]>([]);

  // State to track if there are unsaved changes
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  // State to store the current GeoJSON (local changes that haven't been saved yet)
  const [localGeoJson, setLocalGeoJson] = useState<string>(value);

  // References to the polygon instances
  const polygonRefs = useRef<(google.maps.Polygon | null)[]>([]);

  // State for the map center
  const [mapCenter, setMapCenter] = useState(center || defaultCenter);

  // State for the drawing mode
  const [drawingMode, setDrawingMode] = useState<any>(null);

  // Custom map container style based on props
  const customMapContainerStyle = {
    width,
    height,
  };

  // Parse GeoJSON from value prop when component mounts or value changes
  useEffect(() => {
    // Clear paths if value is empty
    if (!value || value.trim() === '') {
      setFieldPaths([]);
      setLocalGeoJson('');
      setHasUnsavedChanges(false);
      return;
    }

    try {
      // Check if value is a valid JSON string
      const geoJson = JSON.parse(value);

      // Check if the parsed JSON is a valid GeoJSON FeatureCollection
      if (geoJson && geoJson.type === 'FeatureCollection') {
        const polygons: PolygonCoordinates[][] = [];

        // Handle case where features array might be empty
        if (geoJson.features && geoJson.features.length > 0) {
          geoJson.features.forEach((feature: any) => {
            if (feature.geometry && feature.geometry.type === 'Polygon' && 
                feature.geometry.coordinates && feature.geometry.coordinates.length > 0) {
              // Convert GeoJSON coordinates to Google Maps LatLng format
              // GeoJSON format is [longitude, latitude], but Google Maps expects {lat, lng}
              const polygon = feature.geometry.coordinates[0].map((coord: number[]) => ({
                lat: coord[1],
                lng: coord[0],
              }));

              polygons.push(polygon);
            }
          });
        }

        setFieldPaths(polygons);
        setLocalGeoJson(value);
        setHasUnsavedChanges(false);
      } else {
        console.warn('Invalid GeoJSON format:', geoJson);
        setFieldPaths([]);
        setLocalGeoJson('');
        setHasUnsavedChanges(false);
      }
    } catch (error) {
      console.error('Error parsing GeoJSON:', error);
      setFieldPaths([]);
      setLocalGeoJson('');
      setHasUnsavedChanges(false);
    }
  }, [value]);

  // Function to save changes
  const saveChanges = useCallback(() => {
    if (hasUnsavedChanges && localGeoJson) {
      onChange(localGeoJson);
      setHasUnsavedChanges(false);
    }
  }, [hasUnsavedChanges, localGeoJson, onChange]);

  // Update map center when center prop changes
  useEffect(() => {
    if (center) {
      setMapCenter(center);

      // If we have map instance, pan to the new center and zoom in
      if (fieldMapRef.current) {
        fieldMapRef.current.panTo(center);
        fieldMapRef.current.setZoom(15); // Zoom in to a reasonable level
      }
    }
  }, [center]);

  // Handle field map load
  const onFieldMapLoad = useCallback((map: google.maps.Map) => {
    fieldMapRef.current = map;
  }, []);

  // Handle polygon complete event from the drawing manager
  const onPolygonComplete = useCallback((polygon: google.maps.Polygon) => {
    // Get the path of the polygon
    const polygonPath = polygon.getPath();
    const coordinates: PolygonCoordinates[] = [];

    // Convert the path to an array of coordinates
    for (let i = 0; i < polygonPath.getLength(); i++) {
      const point = polygonPath.getAt(i);
      coordinates.push({
        lat: point.lat(),
        lng: point.lng(),
      });
    }

    // Add the first point again to close the polygon (GeoJSON requirement)
    if (coordinates.length > 0) {
      coordinates.push({ ...coordinates[0] });
    }

    // Add the new polygon to the paths state
    const updatedPaths = [...fieldPaths, coordinates];
    setFieldPaths(updatedPaths);

    // Convert the updated paths to GeoJSON and store locally
    const geoJson = pathsToGeoJson(updatedPaths);
    const geoJsonString = JSON.stringify(geoJson);
    setLocalGeoJson(geoJsonString);
    setHasUnsavedChanges(true);

    // Remove the drawn polygon since we're managing our own polygons
    polygon.setMap(null);
  }, [fieldPaths]);

  // Convert polygon paths to GeoJSON format
  const pathsToGeoJson = (polygonPaths: PolygonCoordinates[][]) => {
    const features = polygonPaths.map(path => {
      // Convert Google Maps LatLng format to GeoJSON format
      // GeoJSON format is [longitude, latitude], but Google Maps gives {lat, lng}
      const coordinates = [path.map(point => [point.lng, point.lat])];

      return {
        type: 'Feature',
        geometry: {
          type: 'Polygon',
          coordinates,
        },
        properties: {},
      };
    });

    return {
      type: 'FeatureCollection',
      features,
    };
  };

  // Clear all drawn polygons from the field map
  const clearFieldPolygons = useCallback(() => {
    // Clear polygon references
    polygonRefs.current = [];

    // Clear field paths
    setFieldPaths([]);

    // Create an empty GeoJSON FeatureCollection
    const emptyGeoJson = {
      type: 'FeatureCollection',
      features: []
    };
    const emptyGeoJsonString = JSON.stringify(emptyGeoJson);
    setLocalGeoJson(emptyGeoJsonString);
    setHasUnsavedChanges(true);

    // Reset drawing mode
    if (window.google && window.google.maps && window.google.maps.drawing) {
      handleDrawingModeChange(null);
    }
  }, []);

  // Handle drawing mode change
  const handleDrawingModeChange = useCallback((mode: any) => {
    setDrawingMode(mode);
  }, []);

  // Helper function to safely check drawing mode
  const isDrawingMode = useCallback((mode: string): boolean => {
    if (!window.google || !window.google.maps || !window.google.maps.drawing) {
      return false;
    }

    if (mode === 'POLYGON') {
      return drawingMode === google.maps.drawing.OverlayType.POLYGON;
    } else if (mode === 'null') {
      return drawingMode === null;
    }

    return false;
  }, [drawingMode]);

  // State for Google Maps API loading
  const [isLoaded, setIsLoaded] = useState(false);
  const [loadError, setLoadError] = useState<Error | null>(null);

  // Load the Google Maps API using the googleMapsLoader utility
  useEffect(() => {
    const loadMapsApi = async () => {
      try {
        await loadGoogleMapsApi(['drawing', 'places']);
        setIsLoaded(true);
        // Initialize drawing mode after Google Maps API is loaded
        if (window.google && window.google.maps && window.google.maps.drawing) {
          setDrawingMode(google.maps.drawing.OverlayType.POLYGON);
        }
      } catch (error) {
        console.error('Failed to load Google Maps API:', error);
        setLoadError(error instanceof Error ? error : new Error('Failed to load Google Maps API'));
      }
    };

    if (!isGoogleMapsLoaded()) {
      loadMapsApi();
    } else {
      setIsLoaded(true);
      // Initialize drawing mode if Google Maps API is already loaded
      if (window.google && window.google.maps && window.google.maps.drawing) {
        setDrawingMode(google.maps.drawing.OverlayType.POLYGON);
      }
    }
  }, []);

  // Handle loading error
  if (loadError) {
    return (
      <div className="field-boundary-map-component">
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">Error loading Google Maps API. Please try again later.</span>
        </div>
      </div>
    );
  }

  // Show loading indicator while the API is loading
  if (!isLoaded) {
    return (
      <div className="field-boundary-map-component">
        <div className="flex justify-center items-center" style={{ height: height }}>
          <div className="text-gray-500">Loading map...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="field-boundary-map-component">
      <div className="mb-6">
        <h3 className="text-lg font-medium text-gray-900 mb-2">Field Boundaries</h3>

        {/* Custom Drawing Toolbar */}
        <div className="bg-white p-3 mb-2 rounded-md shadow-sm border border-gray-200">
          <div className="flex items-center">
            <div className="mr-4">
              <span className="text-sm font-medium text-gray-700 mr-2">Drawing Tools:</span>
              <button
                type="button"
                className={`inline-flex items-center px-3 py-2 border ${
                  isDrawingMode('POLYGON')
                    ? 'border-primary-500 bg-primary-50 text-primary-700'
                    : 'border-gray-300 bg-white text-gray-700'
                } shadow-sm text-sm leading-4 font-medium rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500`}
                onClick={() => window.google && window.google.maps && window.google.maps.drawing && handleDrawingModeChange(isDrawingMode('POLYGON') ? null : google.maps.drawing.OverlayType.POLYGON)}
                title="Click to toggle drawing mode. When active, click points on the map to create a shape, then click the first point again to complete."
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM14 11a1 1 0 011 1v1h1a1 1 0 110 2h-1v1a1 1 0 11-2 0v-1h-1a1 1 0 110-2h1v-1a1 1 0 011-1z" />
                </svg>
                Draw Field
              </button>
            </div>
            <div className="flex space-x-2">
              <button
                type="button"
                onClick={saveChanges}
                disabled={!hasUnsavedChanges}
                className={`inline-flex items-center px-3 py-2 border ${
                  hasUnsavedChanges 
                    ? 'border-primary-500 bg-primary-50 text-primary-700 hover:bg-primary-100' 
                    : 'border-gray-300 bg-gray-50 text-gray-400 cursor-not-allowed'
                } shadow-sm text-sm leading-4 font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500`}
                title={hasUnsavedChanges ? "Save your changes" : "No changes to save"}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
                Save Changes
              </button>
              <button
                type="button"
                onClick={clearFieldPolygons}
                className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                title="Clear all drawn field boundaries"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
                Clear All
              </button>
            </div>
          </div>
          <div className="mt-2 text-sm text-gray-600">
            <p><strong>How to draw:</strong> Click the "Draw Field" button, then click on the map to create points. Connect back to the first point to complete the field boundary.</p>
            <p><strong>Edit existing:</strong> Click and drag any point to adjust the shape. Click and drag a line to add a new point.</p>
          </div>
        </div>

        <div className="relative">
          <GoogleMap
            mapContainerStyle={customMapContainerStyle}
            center={mapCenter}
            zoom={5}
            onLoad={onFieldMapLoad}
          >
            {/* Drawing Manager for polygon drawing */}
            <DrawingManager
              drawingMode={drawingMode}
              onPolygonComplete={onPolygonComplete}
              options={{
                drawingControl: false, // Hide default controls, we're using our own
                polygonOptions: {
                  fillColor: '#22c55e',
                  fillOpacity: 0.3,
                  strokeWeight: 2,
                  strokeColor: '#22c55e',
                  clickable: true,
                  editable: true,
                  zIndex: 1,
                },
              }}
            />

            {/* Render existing polygons */}
            {fieldPaths.map((path, index) => (
              <Polygon
                key={index}
                paths={path}
                options={{
                  fillColor: '#22c55e',
                  fillOpacity: 0.3,
                  strokeWeight: 2,
                  strokeColor: '#22c55e',
                  clickable: true,
                  editable: true,
                  zIndex: 1,
                  draggable: true
                }}
                onLoad={(polygon) => {
                  // Store reference to the polygon instance
                  polygonRefs.current[index] = polygon;
                }}
                onMouseUp={() => {
                  // Update the paths when the polygon is edited
                  if (!window.google || !window.google.maps) return;

                  const polygon = polygonRefs.current[index];
                  if (!polygon) return;

                  // Get the updated path from the polygon
                  const path = polygon.getPath();
                  const updatedPath: PolygonCoordinates[] = [];

                  for (let i = 0; i < path.getLength(); i++) {
                    const point = path.getAt(i);
                    updatedPath.push({
                      lat: point.lat(),
                      lng: point.lng()
                    });
                  }

                  // Add the first point again to close the polygon (GeoJSON requirement)
                  if (updatedPath.length > 0) {
                    updatedPath.push({ ...updatedPath[0] });
                  }

                  // Update the fieldPaths state
                  const updatedPaths = [...fieldPaths];
                  updatedPaths[index] = updatedPath;
                  setFieldPaths(updatedPaths);

                  // Convert the updated paths to GeoJSON and store locally
                  const geoJson = pathsToGeoJson(updatedPaths);
                  const geoJsonString = JSON.stringify(geoJson);
                  setLocalGeoJson(geoJsonString);
                  setHasUnsavedChanges(true);
                }}
                onDragEnd={() => {
                  // Update the paths when the polygon is dragged
                  if (!window.google || !window.google.maps) return;

                  const polygon = polygonRefs.current[index];
                  if (!polygon) return;

                  // Get the updated path from the polygon
                  const path = polygon.getPath();
                  const updatedPath: PolygonCoordinates[] = [];

                  for (let i = 0; i < path.getLength(); i++) {
                    const point = path.getAt(i);
                    updatedPath.push({
                      lat: point.lat(),
                      lng: point.lng()
                    });
                  }

                  // Add the first point again to close the polygon (GeoJSON requirement)
                  if (updatedPath.length > 0) {
                    updatedPath.push({ ...updatedPath[0] });
                  }

                  // Update the fieldPaths state
                  const updatedPaths = [...fieldPaths];
                  updatedPaths[index] = updatedPath;
                  setFieldPaths(updatedPaths);

                  // Convert the updated paths to GeoJSON and store locally
                  const geoJson = pathsToGeoJson(updatedPaths);
                  const geoJsonString = JSON.stringify(geoJson);
                  setLocalGeoJson(geoJsonString);
                  setHasUnsavedChanges(true);
                }}
              />
            ))}
          </GoogleMap>

          {/* Clear button */}
          <button
            type="button"
            onClick={clearFieldPolygons}
            className="absolute bottom-2 right-2 bg-white px-3 py-1 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Clear All
          </button>
        </div>

        <p className="mt-1 text-sm text-gray-500">
          Draw field boundaries by clicking the polygon tool and then clicking on the map to create points. 
          Click the first point again to complete the polygon.
        </p>
      </div>
    </div>
  );
};

export default FieldBoundaryMapComponent;
