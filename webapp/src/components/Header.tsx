import {useContext, useEffect} from 'react';
import {Link, useLocation, useNavigate} from 'react-router-dom';
import {AuthContext} from '../context/AuthContext';
import {useFarm} from '../context/FarmContext';
import {useMenuPreferences} from '../hooks/useMenuPreferences';
import {Menu} from '@headlessui/react';
import {ChevronDownIcon} from '@heroicons/react/24/outline';
import GlobalSearch from './GlobalSearch';
import ThemeToggleButton from './theme/ThemeToggleButton';
import { redirectToFarmSubdomain } from '../utils/redirectUtils';

const Header = () => {
  const { user, logout, isImpersonating, endImpersonation } = useContext(AuthContext);
  const { farms, currentFarm, setCurrentFarm, fetchFarms } = useFarm();
  const { headerItems, quickLinksItems, loading: menuLoading } = useMenuPreferences();
  const navigate = useNavigate();
  const location = useLocation();

  // Fetch farms when component mounts
  useEffect(() => {
    if (user) {
      fetchFarms();
    }
  }, [user, fetchFarms]);

  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  // Check if the current route is active
  const isActive = (path: string) => {
    return location.pathname === path;
  };


  return (
    <header className="bg-white dark:bg-gray-900 shadow-sm sticky top-0 z-20">
      <div className="w-full px-2 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          {/* Logo and brand */}
          <div className="flex items-center lg:hidden">
            <Link to="/dashboard" className="flex items-center">
              <img 
                src="/logo.svg" 
                alt="Logo"
                className="h-8 w-auto mr-2"
                onError={(e) => {
                  // Fallback if logo doesn't exist
                  const target = e.currentTarget;
                  target.onerror = null;
                  target.style.display = 'none';
                  const parent = target.parentElement;
                  if (parent) {
                    const span = document.createElement('span');
                    span.className = 'text-xl font-semibold text-primary-600';
                    span.textContent = 'nxtAcre';
                    parent.appendChild(span);
                  }
                }}
              />
            </Link>
          </div>

          {/* Main navigation - simplified with only key sections */}
          <div className="hidden md:flex items-center justify-center">
            <nav className="flex space-x-6 items-center h-full">
              {/* Render header items based on user preferences */}
              {!menuLoading && headerItems.filter(item => item.isVisible).map(item => (
                <Link
                  key={item.id}
                  to={item.path}
                  className={`inline-flex items-center px-1 py-2 border-b-2 text-sm font-medium ${
                    isActive(item.path) || location.pathname.startsWith(`${item.path}/`)
                      ? 'border-primary-500 text-gray-900 dark:text-white'
                      : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 dark:text-gray-300 dark:hover:text-white dark:hover:border-gray-500'
                  }`}
                >
                  <span className="flex items-center">
                    {item.title}
                  </span>
                </Link>
              ))}


              {/* Quick Actions Menu */}
              <Menu as="div" className="relative">
                <Menu.Button 
                  className={`inline-flex items-center px-1 py-2 border-b-2 text-sm font-medium border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 dark:text-gray-300 dark:hover:text-white dark:hover:border-gray-500`}
                >
                  <span className="flex items-center">
                    Quick Actions
                    <ChevronDownIcon className="ml-1 h-4 w-4" aria-hidden="true" />
                  </span>
                </Menu.Button>
                <Menu.Items className="absolute z-10 mt-2 w-48 origin-top-left rounded-md bg-white dark:bg-gray-800 py-1 shadow-lg ring-1 ring-black ring-opacity-5 dark:ring-gray-700 focus:outline-none transition-all duration-100 ease-in-out">
                  {!menuLoading && quickLinksItems.filter(item => item.isVisible).map(item => (
                    <Menu.Item key={item.id}>
                      {({ active }) => (
                        <Link
                          to={item.path}
                          className={`${
                            active ? 'bg-gray-100 dark:bg-gray-700' : ''
                          } block px-4 py-2 text-sm text-gray-700 dark:text-gray-200`}
                        >
                          {item.title}
                        </Link>
                      )}
                    </Menu.Item>
                  ))}
                </Menu.Items>
              </Menu>

              {/* Global Admin - Only visible to global admins */}
              {user?.is_global_admin && (
                <Menu as="div" className="relative">
                  <Menu.Button 
                    className={`inline-flex items-center px-1 py-2 border-b-2 text-sm font-medium ${
                      isActive('/admin') || location.pathname.startsWith('/admin/')
                        ? 'border-primary-500 text-gray-900'
                        : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'
                    }`}
                  >
                    <span className="flex items-center">
                      Admin
                      <ChevronDownIcon className="ml-1 h-4 w-4" aria-hidden="true" />
                    </span>
                  </Menu.Button>
                  <Menu.Items className="absolute z-10 mt-2 w-56 origin-top-left rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none transition-all duration-100 ease-in-out">
                    {/* System Management Group */}
                    <div className="px-3 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wider">
                      System Management
                    </div>
                    <Menu.Item>
                      {({ active }) => (
                        <Link
                          to="/admin"
                          className={`${
                            active ? 'bg-gray-100' : ''
                          } block px-4 py-2 text-sm text-gray-700`}
                        >
                          Dashboard
                        </Link>
                      )}
                    </Menu.Item>
                    <Menu.Item>
                      {({ active }) => (
                        <Link
                          to="/admin/environment"
                          className={`${
                            active ? 'bg-gray-100' : ''
                          } block px-4 py-2 text-sm text-gray-700`}
                        >
                          Environment Variables
                        </Link>
                      )}
                    </Menu.Item>
                    <Menu.Item>
                      {({ active }) => (
                        <Link
                          to="/admin/db-migrations"
                          className={`${
                            active ? 'bg-gray-100' : ''
                          } block px-4 py-2 text-sm text-gray-700`}
                        >
                          Database Migrations
                        </Link>
                      )}
                    </Menu.Item>
                    <Menu.Item>
                      {({ active }) => (
                        <Link
                          to="/admin/logs"
                          className={`${
                            active ? 'bg-gray-100' : ''
                          } block px-4 py-2 text-sm text-gray-700`}
                        >
                          Error Logs
                        </Link>
                      )}
                    </Menu.Item>

                    {/* User Management Group */}
                    <div className="px-3 py-2 mt-2 text-xs font-semibold text-gray-500 uppercase tracking-wider border-t border-gray-100">
                      User Management
                    </div>
                    <Menu.Item>
                      {({ active }) => (
                        <Link
                          to="/admin/users"
                          className={`${
                            active ? 'bg-gray-100' : ''
                          } block px-4 py-2 text-sm text-gray-700`}
                        >
                          User Management
                        </Link>
                      )}
                    </Menu.Item>
                    <Menu.Item>
                      {({ active }) => (
                        <Link
                          to="/admin/roles"
                          className={`${
                            active ? 'bg-gray-100' : ''
                          } block px-4 py-2 text-sm text-gray-700`}
                        >
                          Role Management
                        </Link>
                      )}
                    </Menu.Item>

                    {/* Business Management Group */}
                    <div className="px-3 py-2 mt-2 text-xs font-semibold text-gray-500 uppercase tracking-wider border-t border-gray-100">
                      Business Management
                    </div>
                    <Menu.Item>
                      {({ active }) => (
                        <Link
                          to="/admin/farms"
                          className={`${
                            active ? 'bg-gray-100' : ''
                          } block px-4 py-2 text-sm text-gray-700`}
                        >
                          Farm Management
                        </Link>
                      )}
                    </Menu.Item>
                    <Menu.Item>
                      {({ active }) => (
                        <Link
                          to="/admin/subscriptions"
                          className={`${
                            active ? 'bg-gray-100' : ''
                          } block px-4 py-2 text-sm text-gray-700`}
                        >
                          Subscription Management
                        </Link>
                      )}
                    </Menu.Item>
                    <Menu.Item>
                      {({ active }) => (
                        <Link
                          to="/admin/suppliers"
                          className={`${
                            active ? 'bg-gray-100' : ''
                          } block px-4 py-2 text-sm text-gray-700`}
                        >
                          Supplier Management
                        </Link>
                      )}
                    </Menu.Item>
                    <Menu.Item>
                      {({ active }) => (
                        <Link
                          to="/admin/integrations"
                          className={`${
                            active ? 'bg-gray-100' : ''
                          } block px-4 py-2 text-sm text-gray-700`}
                        >
                          Integration Management
                        </Link>
                      )}
                    </Menu.Item>

                    {/* Support & Help Group */}
                    <div className="px-3 py-2 mt-2 text-xs font-semibold text-gray-500 uppercase tracking-wider border-t border-gray-100">
                      Support & Help
                    </div>
                    <Menu.Item>
                      {({ active }) => (
                        <Link
                          to="/admin/help-tips"
                          className={`${
                            active ? 'bg-gray-100' : ''
                          } block px-4 py-2 text-sm text-gray-700`}
                        >
                          Help Tips Management
                        </Link>
                      )}
                    </Menu.Item>
                    <Menu.Item>
                      {({ active }) => (
                        <Link
                          to="/admin/support-tickets"
                          className={`${
                            active ? 'bg-gray-100' : ''
                          } block px-4 py-2 text-sm text-gray-700`}
                        >
                          Support Ticket Management
                        </Link>
                      )}
                    </Menu.Item>
                    <Menu.Item>
                      {({ active }) => (
                        <Link
                          to="/admin/faqs"
                          className={`${
                            active ? 'bg-gray-100' : ''
                          } block px-4 py-2 text-sm text-gray-700`}
                        >
                          FAQ Management
                        </Link>
                      )}
                    </Menu.Item>

                  </Menu.Items>
                </Menu>
              )}
            </nav>
          </div>

          {/* User profile dropdown */}
          <div className="flex items-center">
            {/* Theme Toggle Button */}
            <div className="mr-2">
              <ThemeToggleButton />
            </div>

            {/* Global Search */}
            <div className="mr-2">
              <GlobalSearch />
            </div>

            {/* Support Link */}
            <div className="mr-2">
              <Link
                to="/support"
                className="inline-flex items-center justify-center p-2 rounded-md text-gray-500 hover:text-gray-600 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500"
                aria-label="Support"
                style={{ height: 'fit-content' }}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192l-3.536 3.536M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v3m0 0v3m0-3h3m-3 0H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </Link>
            </div>

            {/* Help Link */}
            <div className="mr-2">
              <Link
                to="/help"
                className="inline-flex items-center justify-center p-2 rounded-md text-gray-500 hover:text-gray-600 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500"
                aria-label="Help Center"
                style={{ height: 'fit-content' }}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </Link>
            </div>

            {/* Farm Selector */}
            <div className="mr-4 flex items-center">
              <span className="hidden sm:inline text-sm font-medium text-gray-500 mr-2">Farm:</span>
              <select
                id="farm-selector-nav"
                value={currentFarm?.id || ''}
                onChange={(e) => {
                  const selectedFarm = farms.find(farm => farm.id === e.target.value);
                  if (selectedFarm) {
                    setCurrentFarm(selectedFarm);

                    // Redirect to farm subdomain if available
                    if (selectedFarm.subdomain) {
                      // Use the redirectUtils function to handle the redirect logic
                      redirectToFarmSubdomain(selectedFarm.subdomain, location.pathname, user);
                    }
                  }
                }}
                className="block w-28 sm:w-40 rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 text-xs sm:text-sm"
                aria-label="Select Farm"
              >
                <option value="" disabled>Select Farm</option>
                {farms.map((farm) => (
                  <option key={farm.id} value={farm.id}>
                    {farm.name}
                  </option>
                ))}
              </select>
            </div>
            {user && (
              <Menu as="div" className="ml-3 relative">
                <div>
                  <Menu.Button className="max-w-xs bg-white flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                    <span className="sr-only">Open user menu</span>
                    <div className="h-8 w-8 rounded-full bg-primary-100 flex items-center justify-center text-primary-700 font-semibold">
                      {user.firstName?.charAt(0)}{user.lastName?.charAt(0)}
                    </div>
                  </Menu.Button>
                </div>
                <Menu.Items className="origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg py-1 bg-white ring-1 ring-black ring-opacity-5 focus:outline-none transition-all duration-100 ease-in-out">
                  <div className="px-4 py-2 border-b border-gray-100">
                    <p className="text-sm font-medium text-gray-900 truncate">
                      {user.firstName} {user.lastName}
                    </p>
                    <p className="text-xs text-gray-500 truncate">
                      {user.email}
                    </p>
                  </div>
                  <Menu.Item>
                    {({ active }) => (
                      <Link
                        to="/profile"
                        className={`${
                          active ? 'bg-gray-100' : ''
                        } block px-4 py-2 text-sm text-gray-700`}
                      >
                        Profile Settings
                      </Link>
                    )}
                  </Menu.Item>
                  {user.isBusinessOwner && (user.userType === 'supplier' || user.userType === 'vet') && (
                    <Menu.Item>
                      {({ active }) => (
                        <Link
                          to="/business-profile"
                          className={`${
                            active ? 'bg-gray-100' : ''
                          } block px-4 py-2 text-sm text-gray-700`}
                        >
                          Business Listing
                        </Link>
                      )}
                    </Menu.Item>
                  )}
                  {isImpersonating && (
                    <Menu.Item>
                      {({ active }) => (
                        <button
                          onClick={() => {
                            endImpersonation();
                            navigate('/admin/users');
                          }}
                          className={`${
                            active ? 'bg-gray-100' : ''
                          } block w-full text-left px-4 py-2 text-sm text-red-600 font-medium`}
                        >
                          End Impersonation
                        </button>
                      )}
                    </Menu.Item>
                  )}
                  <Menu.Item>
                    {({ active }) => (
                      <button
                        onClick={handleLogout}
                        className={`${
                          active ? 'bg-gray-100' : ''
                        } block w-full text-left px-4 py-2 text-sm text-gray-700`}
                      >
                        Sign out
                      </button>
                    )}
                  </Menu.Item>
                </Menu.Items>
              </Menu>
            )}
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;
