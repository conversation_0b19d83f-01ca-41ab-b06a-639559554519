import React, { useState, useEffect } from 'react';
import { getMarketplaceCategories } from '../../services/productMarketplaceService';

interface ProductMarketplaceSettingsProps {
  isMarketplaceVisible: boolean;
  marketplaceDescription: string;
  marketplaceCategory: string;
  onVisibilityChange: (isVisible: boolean) => void;
  onDescriptionChange: (description: string) => void;
  onCategoryChange: (category: string) => void;
  readOnly?: boolean;
}

const ProductMarketplaceSettings: React.FC<ProductMarketplaceSettingsProps> = ({
  isMarketplaceVisible,
  marketplaceDescription,
  marketplaceCategory,
  onVisibilityChange,
  onDescriptionChange,
  onCategoryChange,
  readOnly = false
}) => {
  const [categories, setCategories] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch marketplace categories
  useEffect(() => {
    const fetchCategories = async () => {
      setLoading(true);
      try {
        const fetchedCategories = await getMarketplaceCategories();
        setCategories(fetchedCategories);
      } catch (err) {
        console.error('Error fetching marketplace categories:', err);
        setError('Failed to load marketplace categories. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchCategories();
  }, []);

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-medium text-gray-900">Marketplace Settings</h3>
      
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
          <span className="block sm:inline">{error}</span>
        </div>
      )}

      <div className="bg-blue-50 border-l-4 border-blue-400 p-4 mb-4">
        <div className="flex">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-blue-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="ml-3">
            <p className="text-sm text-blue-700">
              Making this product visible in the marketplace will allow customers to view and purchase it through the NxtAcre Marketplace.
            </p>
          </div>
        </div>
      </div>

      {/* Marketplace Visibility Toggle */}
      <div className="flex items-center">
        <input
          id="marketplace-visible"
          name="marketplace-visible"
          type="checkbox"
          checked={isMarketplaceVisible}
          onChange={(e) => onVisibilityChange(e.target.checked)}
          disabled={readOnly}
          className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
        />
        <label htmlFor="marketplace-visible" className="ml-2 block text-sm text-gray-900">
          Show in Marketplace
        </label>
      </div>

      {/* Marketplace Category */}
      <div>
        <label htmlFor="marketplace-category" className="block text-sm font-medium text-gray-700 mb-1">
          Marketplace Category
        </label>
        <select
          id="marketplace-category"
          name="marketplace-category"
          value={marketplaceCategory}
          onChange={(e) => onCategoryChange(e.target.value)}
          disabled={readOnly || loading}
          className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
        >
          <option value="">Select a category</option>
          {categories.map((category) => (
            <option key={category} value={category}>
              {category}
            </option>
          ))}
        </select>
        {loading && (
          <div className="mt-1 flex items-center">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary-600"></div>
            <p className="ml-2 text-xs text-gray-500">Loading categories...</p>
          </div>
        )}
      </div>

      {/* Marketplace Description */}
      <div>
        <label htmlFor="marketplace-description" className="block text-sm font-medium text-gray-700 mb-1">
          Marketplace Description
        </label>
        <div className="mt-1">
          <textarea
            id="marketplace-description"
            name="marketplace-description"
            rows={4}
            value={marketplaceDescription}
            onChange={(e) => onDescriptionChange(e.target.value)}
            disabled={readOnly}
            placeholder="Provide a detailed description for customers in the marketplace. If left empty, the standard product description will be used."
            className="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 rounded-md"
          />
        </div>
        <p className="mt-2 text-sm text-gray-500">
          This description will be shown to customers in the marketplace. It can be different from your internal product description.
        </p>
      </div>

      {isMarketplaceVisible && (
        <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded-md">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-green-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-green-700">
                This product will be visible in the NxtAcre Marketplace.
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ProductMarketplaceSettings;