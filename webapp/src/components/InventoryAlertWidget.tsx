import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { useAuth } from '../context/AuthContext';
import { useFarm } from '../context/FarmContext';
import { Link } from 'react-router-dom';
import { API_URL } from '../config';

interface InventoryItem {
  id: string;
  name: string;
  category: string;
  currentStock: number;
  unit: string;
  minStockLevel: number;
  reorderLevel: number;
  lastUpdated: string;
}

const InventoryAlertWidget: React.FC = () => {
  const { token } = useAuth();
  const { currentFarm } = useFarm();
  const [inventoryItems, setInventoryItems] = useState<InventoryItem[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchLowInventory = async () => {
      if (!currentFarm || !token) return;

      try {
        setLoading(true);
        setError(null);

        // Call the API endpoint to get low inventory items
        const response = await axios.get(`${API_URL}/inventory`, {
          headers: { Authorization: `Bearer ${token}` },
          params: {
            farmId: currentFarm.id,
            lowStock: true
          }
        });

        // Map the backend model to the frontend interface
        const inventoryData: InventoryItem[] = response.data.map((item: any) => ({
          id: item.id,
          name: item.name,
          category: item.InventoryCategory ? item.InventoryCategory.name : 'Uncategorized',
          currentStock: parseFloat(item.quantity),
          unit: item.unit || '',
          minStockLevel: item.reorder_point ? parseFloat(item.reorder_point) * 0.5 : 0, // Using half of reorder point as min stock level
          reorderLevel: parseFloat(item.reorder_point) || 0,
          lastUpdated: item.updated_at
        }));

        setInventoryItems(inventoryData);
        setLoading(false);
      } catch (err: any) {
        console.error('Error fetching low inventory items:', err);
        setError(err.response?.data?.error || 'Failed to load inventory data');
        setLoading(false);

        // Fallback to empty array if API call fails
        setInventoryItems([]);
      }
    };

    fetchLowInventory();
  }, [currentFarm, token]);

  const getStockLevelClass = (currentStock: number, minStockLevel: number, reorderLevel: number) => {
    if (currentStock <= minStockLevel) {
      return 'text-red-600';
    } else if (currentStock <= reorderLevel) {
      return 'text-yellow-600';
    } else {
      return 'text-green-600';
    }
  };

  const getStockPercentage = (currentStock: number, reorderLevel: number) => {
    const percentage = (currentStock / reorderLevel) * 100;
    return Math.min(percentage, 100); // Cap at 100%
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-full">
        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-indigo-600"></div>
        <span className="ml-2 text-gray-500">Loading inventory data...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4 text-center text-red-500">
        <p>Error: {error}</p>
      </div>
    );
  }

  if (inventoryItems.length === 0) {
    return (
      <div className="p-4 text-center text-gray-500">
        <p>No low inventory items.</p>
      </div>
    );
  }

  return (
    <div className="p-4">
      <div className="space-y-4">
        {inventoryItems.map((item) => (
          <div key={item.id} className="border rounded-lg p-3">
            <div className="flex justify-between items-center mb-2">
              <div>
                <Link to={`/inventory/${item.id}`} className="text-sm font-medium text-indigo-600 hover:text-indigo-800">
                  {item.name}
                </Link>
                <p className="text-xs text-gray-500">{item.category}</p>
              </div>
              <div className={`text-sm font-medium ${getStockLevelClass(item.currentStock, item.minStockLevel, item.reorderLevel)}`}>
                {item.currentStock} {item.unit}
              </div>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2.5 mb-1">
              <div 
                className={`h-2.5 rounded-full ${
                  item.currentStock <= item.minStockLevel ? 'bg-red-600' : 
                  item.currentStock <= item.reorderLevel ? 'bg-yellow-500' : 'bg-green-600'
                }`} 
                style={{ width: `${getStockPercentage(item.currentStock, item.reorderLevel)}%` }}
              ></div>
            </div>
            <div className="flex justify-between text-xs text-gray-500">
              <span>Min: {item.minStockLevel} {item.unit}</span>
              <span>Reorder: {item.reorderLevel} {item.unit}</span>
            </div>
          </div>
        ))}
      </div>
      <div className="mt-3 text-right">
        <Link to="/inventory" className="text-sm text-indigo-600 hover:text-indigo-800">
          View all inventory →
        </Link>
      </div>
    </div>
  );
};

export default InventoryAlertWidget;
