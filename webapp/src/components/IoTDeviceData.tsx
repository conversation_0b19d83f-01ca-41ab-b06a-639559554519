import { useState, useEffect } from 'react';
import { getDeviceIoTData, getAggregatedIoTData, IoTData, AggregatedIoTData } from '../services/iotService';
import { Line } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  ChartOptions
} from 'chart.js';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend
);

interface IoTDeviceDataProps {
  deviceId: string;
}

const IoTDeviceData = ({ deviceId }: IoTDeviceDataProps) => {
  const [iotData, setIotData] = useState<IoTData[]>([]);
  const [aggregatedData, setAggregatedData] = useState<AggregatedIoTData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [metric, setMetric] = useState<string>('temperature');
  const [interval, setInterval] = useState<'hour' | 'day' | 'week' | 'month'>('day');
  const [dateRange, setDateRange] = useState({
    startDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 7 days ago
    endDate: new Date().toISOString().split('T')[0] // today
  });

  // Fetch IoT data
  useEffect(() => {
    const fetchIoTData = async () => {
      setLoading(true);
      setError(null);

      try {
        // Fetch the latest IoT data points
        const response = await getDeviceIoTData(deviceId, undefined, undefined, 10, 0);
        setIotData(response.iotData || []);
      } catch (err: any) {
        console.error('Error fetching IoT data:', err);
        setError('Failed to load IoT data. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchIoTData();
  }, [deviceId]);

  // Fetch aggregated data when metric, interval, or date range changes
  useEffect(() => {
    const fetchAggregatedData = async () => {
      setLoading(true);
      setError(null);

      try {
        const response = await getAggregatedIoTData(
          deviceId,
          dateRange.startDate,
          dateRange.endDate,
          metric,
          interval
        );
        setAggregatedData(response.aggregatedData || []);
      } catch (err: any) {
        console.error('Error fetching aggregated IoT data:', err);
        setError('Failed to load aggregated data. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchAggregatedData();
  }, [deviceId, metric, interval, dateRange.startDate, dateRange.endDate]);

  // Handle metric change
  const handleMetricChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setMetric(e.target.value);
  };

  // Handle interval change
  const handleIntervalChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setInterval(e.target.value as 'hour' | 'day' | 'week' | 'month');
  };

  // Handle date range change
  const handleDateRangeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setDateRange(prev => ({ ...prev, [name]: value }));
  };

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Prepare chart data
  const chartData = {
    labels: aggregatedData.map(data => data.time_period),
    datasets: [
      {
        label: `Average ${metric.replace('_', ' ')}`,
        data: aggregatedData.map(data => data.avg_value),
        borderColor: 'rgb(75, 192, 192)',
        backgroundColor: 'rgba(75, 192, 192, 0.5)',
      },
      {
        label: `Min ${metric.replace('_', ' ')}`,
        data: aggregatedData.map(data => data.min_value),
        borderColor: 'rgb(53, 162, 235)',
        backgroundColor: 'rgba(53, 162, 235, 0.5)',
      },
      {
        label: `Max ${metric.replace('_', ' ')}`,
        data: aggregatedData.map(data => data.max_value),
        borderColor: 'rgb(255, 99, 132)',
        backgroundColor: 'rgba(255, 99, 132, 0.5)',
      }
    ]
  };

  const chartOptions: ChartOptions<'line'> = {
    responsive: true,
    plugins: {
      legend: {
        position: 'top' as const,
      },
      title: {
        display: true,
        text: `${metric.replace('_', ' ').charAt(0).toUpperCase() + metric.replace('_', ' ').slice(1)} Over Time`,
      },
    },
  };

  // Get unit for metric
  const getMetricUnit = (metricName: string) => {
    switch (metricName) {
      case 'temperature':
      case 'soil_temperature':
        return '°C';
      case 'humidity':
      case 'soil_moisture':
        return '%';
      case 'pressure':
        return 'hPa';
      case 'light_level':
        return 'lux';
      case 'battery_level':
      case 'signal_strength':
        return '%';
      default:
        return '';
    }
  };

  // Format metric value
  const formatMetricValue = (value: number | null, metricName: string) => {
    if (value === null) return 'N/A';
    return `${value}${getMetricUnit(metricName)}`;
  };

  return (
    <div className="bg-white shadow overflow-hidden sm:rounded-lg mt-6">
      <div className="px-4 py-5 sm:px-6">
        <h3 className="text-lg leading-6 font-medium text-gray-900">IoT Device Data</h3>
        <p className="mt-1 max-w-2xl text-sm text-gray-500">Sensor readings and data collected from this device.</p>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">{error}</span>
        </div>
      )}

      {/* Latest Data Section */}
      <div className="border-t border-gray-200">
        <div className="px-4 py-5 sm:px-6">
          <h4 className="text-md leading-6 font-medium text-gray-900">Latest Readings</h4>
        </div>
        {loading && iotData.length === 0 ? (
          <div className="px-4 py-5 sm:px-6 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto"></div>
            <p className="mt-2 text-sm text-gray-500">Loading data...</p>
          </div>
        ) : iotData.length === 0 ? (
          <div className="px-4 py-5 sm:px-6 text-center">
            <p className="text-sm text-gray-500">No data available for this device.</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Timestamp
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Temperature
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Humidity
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Soil Moisture
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Battery
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Signal
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {iotData.map((data) => (
                  <tr key={data.id}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {formatDate(data.timestamp)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {formatMetricValue(data.temperature, 'temperature')}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {formatMetricValue(data.humidity, 'humidity')}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {formatMetricValue(data.soil_moisture, 'soil_moisture')}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {formatMetricValue(data.battery_level, 'battery_level')}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {formatMetricValue(data.signal_strength, 'signal_strength')}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Data Visualization Section */}
      <div className="border-t border-gray-200">
        <div className="px-4 py-5 sm:px-6">
          <h4 className="text-md leading-6 font-medium text-gray-900">Data Visualization</h4>
          <div className="mt-4 grid grid-cols-1 gap-4 sm:grid-cols-4">
            <div>
              <label htmlFor="metric" className="block text-sm font-medium text-gray-700">
                Metric
              </label>
              <select
                id="metric"
                name="metric"
                value={metric}
                onChange={handleMetricChange}
                className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md"
              >
                <option value="temperature">Temperature</option>
                <option value="humidity">Humidity</option>
                <option value="pressure">Pressure</option>
                <option value="soil_moisture">Soil Moisture</option>
                <option value="soil_temperature">Soil Temperature</option>
                <option value="light_level">Light Level</option>
                <option value="battery_level">Battery Level</option>
                <option value="signal_strength">Signal Strength</option>
              </select>
            </div>
            <div>
              <label htmlFor="interval" className="block text-sm font-medium text-gray-700">
                Interval
              </label>
              <select
                id="interval"
                name="interval"
                value={interval}
                onChange={handleIntervalChange}
                className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md"
              >
                <option value="hour">Hourly</option>
                <option value="day">Daily</option>
                <option value="week">Weekly</option>
                <option value="month">Monthly</option>
              </select>
            </div>
            <div>
              <label htmlFor="startDate" className="block text-sm font-medium text-gray-700">
                Start Date
              </label>
              <input
                type="date"
                id="startDate"
                name="startDate"
                value={dateRange.startDate}
                onChange={handleDateRangeChange}
                className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md"
              />
            </div>
            <div>
              <label htmlFor="endDate" className="block text-sm font-medium text-gray-700">
                End Date
              </label>
              <input
                type="date"
                id="endDate"
                name="endDate"
                value={dateRange.endDate}
                onChange={handleDateRangeChange}
                className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md"
              />
            </div>
          </div>
        </div>
        <div className="px-4 py-5 sm:px-6">
          {loading && aggregatedData.length === 0 ? (
            <div className="text-center py-10">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
              <p className="mt-2 text-sm text-gray-500">Loading chart data...</p>
            </div>
          ) : aggregatedData.length === 0 ? (
            <div className="text-center py-10">
              <p className="text-sm text-gray-500">No data available for the selected parameters.</p>
            </div>
          ) : (
            <div className="h-80">
              <Line data={chartData} options={chartOptions} />
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default IoTDeviceData;