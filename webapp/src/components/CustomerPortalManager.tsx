import { useState, useEffect } from 'react';
import axios from 'axios';
import { API_URL } from '../config';
import { getAuthToken } from '../utils/storageUtils';

interface CustomerPortalManagerProps {
  farmId: string;
  featureEnabled: boolean;
}

const CustomerPortalManager: React.FC<CustomerPortalManagerProps> = ({ farmId, featureEnabled }) => {
  const [isEnabled, setIsEnabled] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [processing, setProcessing] = useState<boolean>(false);

  useEffect(() => {
    const fetchCustomerPortalStatus = async () => {
      try {
        setLoading(true);
        setError(null);
        
        const response = await axios.get(`${API_URL}/farms/${farmId}`, {
          headers: {
            Authorization: `Bearer ${getAuthToken()}`
          }
        });
        
        setIsEnabled(response.data.farm.customer_portal_enabled || false);
      } catch (err: any) {
        console.error('Error fetching customer portal status:', err);
        setError(err.response?.data?.error || 'Failed to fetch customer portal information');
      } finally {
        setLoading(false);
      }
    };

    fetchCustomerPortalStatus();
  }, [farmId]);

  const handleToggleCustomerPortal = async () => {
    try {
      setProcessing(true);
      setError(null);
      setSuccess(null);
      
      const response = await axios.put(
        `${API_URL}/farms/${farmId}`,
        { customer_portal_enabled: !isEnabled },
        {
          headers: {
            Authorization: `Bearer ${getAuthToken()}`
          }
        }
      );
      
      setIsEnabled(response.data.farm.customer_portal_enabled);
      setSuccess(`Customer portal ${isEnabled ? 'disabled' : 'enabled'} successfully.`);
    } catch (err: any) {
      console.error('Error toggling customer portal:', err);
      setError(err.response?.data?.error || 'Failed to update customer portal settings');
    } finally {
      setProcessing(false);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center py-4">
        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary-600"></div>
        <span className="ml-2 text-sm text-gray-500">Loading customer portal information...</span>
      </div>
    );
  }

  if (!featureEnabled) {
    return (
      <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-4">
        <div className="flex">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="ml-3">
            <p className="text-sm text-yellow-700">
              Customer portal feature is not available with your current subscription plan.
              <a href="#" className="font-medium underline text-yellow-700 hover:text-yellow-600 ml-1">
                Upgrade your plan to access this feature.
              </a>
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {error && (
        <div className="bg-red-50 border-l-4 border-red-400 p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-700">{error}</p>
            </div>
          </div>
        </div>
      )}

      {success && (
        <div className="bg-green-50 border-l-4 border-green-400 p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-green-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-green-700">{success}</p>
            </div>
          </div>
        </div>
      )}

      <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900">Customer Portal</h3>
          <div className="mt-2 max-w-xl text-sm text-gray-500">
            <p>
              The customer portal allows your customers to view their invoices, orders, and other information.
              {isEnabled 
                ? ' Your customer portal is currently enabled.' 
                : ' Your customer portal is currently disabled.'}
            </p>
          </div>
          <div className="mt-5">
            <button
              type="button"
              onClick={handleToggleCustomerPortal}
              disabled={processing}
              className={`inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white ${
                isEnabled 
                  ? 'bg-red-600 hover:bg-red-700 focus:ring-red-500' 
                  : 'bg-primary-600 hover:bg-primary-700 focus:ring-primary-500'
              } focus:outline-none focus:ring-2 focus:ring-offset-2 ${
                processing ? 'opacity-70 cursor-not-allowed' : ''
              }`}
            >
              {processing 
                ? 'Processing...' 
                : isEnabled 
                  ? 'Disable Customer Portal' 
                  : 'Enable Customer Portal'}
            </button>
          </div>
        </div>
      </div>

      <div className="bg-gray-50 rounded-lg border border-gray-200 overflow-hidden">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900">About Customer Portal</h3>
          <div className="mt-2 max-w-xl text-sm text-gray-500">
            <p>The customer portal provides the following features:</p>
            <ul className="list-disc list-inside mt-2 space-y-1">
              <li>Customers can view their invoices and payment history</li>
              <li>Customers can view and track their orders</li>
              <li>Customers can update their contact information</li>
              <li>Customers can communicate with your team</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CustomerPortalManager;