import React, {useState, useEffect} from 'react';
import axios from 'axios';
import {API_URL} from '../../config';
import {useAuth} from '../../context/AuthContext';
import ContractorTaxInfo from "../../../server/models/ContractorTaxInfo";

interface TaxDocument {
    id: string;
    title: string;
    fileName?: string;
    status: string;
}

interface ContractorTaxInfo {
    id?: string;
    contractorName: string;
    businessName?: string;
    taxId?: string;
    address?: string;
    city?: string;
    state?: string;
    zipCode?: string;
    taxYear: number;
    totalPayments: number;
    form1099Generated?: boolean;
    form1099DocumentId?: string;
    form1099Document?: TaxDocument;
}

interface ContractorTaxInfoFormProps {
    farmId: string,
    taxInfoId?: string,
    onSave?: (taxInfo: ContractorTaxInfo) => void,
    onCancel?: () => void,
    onGenerate1099?: (taxInfoId: string) => void,
    year?: number,
    onInfoAdded?: () => Promise<void>
}

const ContractorTaxInfoForm: React.FC<ContractorTaxInfoFormProps> = ({
                                                                         farmId,
                                                                         taxInfoId,
                                                                         onSave,
                                                                         onCancel,
                                                                         onGenerate1099,
                                                                         year,
                                                                         onInfoAdded
                                                                     }) => {
    const {user, token} = useAuth();
    const [taxInfo, setTaxInfo] = useState<ContractorTaxInfo>({
        contractorName: '',
        businessName: '',
        taxId: '',
        address: '',
        city: '',
        state: '',
        zipCode: '',
        taxYear: new Date().getFullYear(),
        totalPayments: 0,
        form1099Generated: false
    });
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    // US States for dropdown
    const states = [
        {value: 'AL', label: 'Alabama'},
        {value: 'AK', label: 'Alaska'},
        {value: 'AZ', label: 'Arizona'},
        {value: 'AR', label: 'Arkansas'},
        {value: 'CA', label: 'California'},
        {value: 'CO', label: 'Colorado'},
        {value: 'CT', label: 'Connecticut'},
        {value: 'DE', label: 'Delaware'},
        {value: 'FL', label: 'Florida'},
        {value: 'GA', label: 'Georgia'},
        {value: 'HI', label: 'Hawaii'},
        {value: 'ID', label: 'Idaho'},
        {value: 'IL', label: 'Illinois'},
        {value: 'IN', label: 'Indiana'},
        {value: 'IA', label: 'Iowa'},
        {value: 'KS', label: 'Kansas'},
        {value: 'KY', label: 'Kentucky'},
        {value: 'LA', label: 'Louisiana'},
        {value: 'ME', label: 'Maine'},
        {value: 'MD', label: 'Maryland'},
        {value: 'MA', label: 'Massachusetts'},
        {value: 'MI', label: 'Michigan'},
        {value: 'MN', label: 'Minnesota'},
        {value: 'MS', label: 'Mississippi'},
        {value: 'MO', label: 'Missouri'},
        {value: 'MT', label: 'Montana'},
        {value: 'NE', label: 'Nebraska'},
        {value: 'NV', label: 'Nevada'},
        {value: 'NH', label: 'New Hampshire'},
        {value: 'NJ', label: 'New Jersey'},
        {value: 'NM', label: 'New Mexico'},
        {value: 'NY', label: 'New York'},
        {value: 'NC', label: 'North Carolina'},
        {value: 'ND', label: 'North Dakota'},
        {value: 'OH', label: 'Ohio'},
        {value: 'OK', label: 'Oklahoma'},
        {value: 'OR', label: 'Oregon'},
        {value: 'PA', label: 'Pennsylvania'},
        {value: 'RI', label: 'Rhode Island'},
        {value: 'SC', label: 'South Carolina'},
        {value: 'SD', label: 'South Dakota'},
        {value: 'TN', label: 'Tennessee'},
        {value: 'TX', label: 'Texas'},
        {value: 'UT', label: 'Utah'},
        {value: 'VT', label: 'Vermont'},
        {value: 'VA', label: 'Virginia'},
        {value: 'WA', label: 'Washington'},
        {value: 'WV', label: 'West Virginia'},
        {value: 'WI', label: 'Wisconsin'},
        {value: 'WY', label: 'Wyoming'},
        {value: 'DC', label: 'District of Columbia'}
    ];

    // Fetch tax info if editing
    useEffect(() => {
        const fetchTaxInfo = async () => {
            if (!taxInfoId) return;

            setLoading(true);
            setError(null);

            try {
                const response = await axios.get(`${API_URL}/api/tax/contractor-tax-info/${taxInfoId}`, {
                    headers: {Authorization: `Bearer ${token}`}
                });

                if (response.data && response.data.contractorTaxInfo) {
                    const fetchedTaxInfo = response.data.contractorTaxInfo;
                    setTaxInfo({
                        id: fetchedTaxInfo.id,
                        contractorName: fetchedTaxInfo.contractorName,
                        businessName: fetchedTaxInfo.businessName || '',
                        taxId: fetchedTaxInfo.taxId || '',
                        address: fetchedTaxInfo.address || '',
                        city: fetchedTaxInfo.city || '',
                        state: fetchedTaxInfo.state || '',
                        zipCode: fetchedTaxInfo.zipCode || '',
                        taxYear: fetchedTaxInfo.taxYear,
                        totalPayments: fetchedTaxInfo.totalPayments || 0,
                        form1099Generated: fetchedTaxInfo.form1099Generated || false,
                        form1099DocumentId: fetchedTaxInfo.form1099DocumentId,
                        form1099Document: fetchedTaxInfo.form1099Document
                    });
                } else {
                    throw new Error('Invalid response format from server');
                }
            } catch (err) {
                console.error('Error fetching contractor tax info:', err);
                setError('Failed to load contractor tax information. Please try again later.');
            } finally {
                setLoading(false);
            }
        };

        if (taxInfoId) {
            fetchTaxInfo();
        }
    }, [taxInfoId, user]);

    // Handle form input changes
    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
        const {name, value, type} = e.target;

        if (name === 'taxYear') {
            setTaxInfo(prev => ({...prev, [name]: parseInt(value) || new Date().getFullYear()}));
        } else if (name === 'totalPayments') {
            setTaxInfo(prev => ({...prev, [name]: parseFloat(value) || 0}));
        } else {
            setTaxInfo(prev => ({...prev, [name]: value}));
        }
    };

    // Format Tax ID for display
    const formatTaxId = (taxId: string): string => {
        if (!taxId) return '';

        // Only show last 4 digits
        if (taxId.length >= 4) {
            return `XXX-XX-${taxId.slice(-4)}`;
        }

        return taxId;
    };

    // Handle form submission
    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        if (!taxInfo.contractorName || !taxInfo.taxYear) {
            setError('Please fill in all required fields.');
            return;
        }

        if (onSave) {
            onSave(taxInfo);
        }

        if (onInfoAdded) {
            await onInfoAdded();
        }
    };

    // Handle 1099 generation
    const handleGenerate1099 = () => {
        if (taxInfoId && onGenerate1099) {
            onGenerate1099(taxInfoId);
        }
    };

    // Format currency for display
    const formatCurrency = (amount: number): string => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
            minimumFractionDigits: 2
        }).format(amount);
    };

    return (
        <div className="bg-white shadow overflow-hidden sm:rounded-lg">
            <div className="px-4 py-5 sm:px-6">
                <h3 className="text-lg leading-6 font-medium text-gray-900">
                    {taxInfoId ? 'Edit Contractor Tax Information' : 'Add Contractor Tax Information'}
                </h3>
                <p className="mt-1 max-w-2xl text-sm text-gray-500">
                    {taxInfoId
                        ? 'Update tax information for this contractor.'
                        : 'Add tax information for a contractor for the selected tax year.'}
                </p>
            </div>

            {error && (
                <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mx-6 mb-4"
                     role="alert">
                    <span className="block sm:inline">{error}</span>
                </div>
            )}

            <form onSubmit={handleSubmit} className="border-t border-gray-200 px-4 py-5 sm:px-6">
                <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                    {/* Contractor Name */}
                    <div>
                        <label htmlFor="contractorName" className="block text-sm font-medium text-gray-700 mb-1">
                            Contractor Name <span className="text-red-500">*</span>
                        </label>
                        <input
                            type="text"
                            id="contractorName"
                            name="contractorName"
                            value={taxInfo.contractorName}
                            onChange={handleChange}
                            className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md"
                            required
                        />
                    </div>

                    {/* Business Name */}
                    <div>
                        <label htmlFor="businessName" className="block text-sm font-medium text-gray-700 mb-1">
                            Business Name
                        </label>
                        <input
                            type="text"
                            id="businessName"
                            name="businessName"
                            value={taxInfo.businessName}
                            onChange={handleChange}
                            className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md"
                        />
                    </div>

                    {/* Tax ID */}
                    <div>
                        <label htmlFor="taxId" className="block text-sm font-medium text-gray-700 mb-1">
                            Tax ID (SSN/EIN)
                        </label>
                        <input
                            type="text"
                            id="taxId"
                            name="taxId"
                            value={taxInfo.taxId}
                            onChange={handleChange}
                            placeholder="XXX-XX-XXXX or XX-XXXXXXX"
                            className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md"
                        />
                        {taxInfo.taxId && (
                            <p className="mt-1 text-xs text-gray-500">
                                For security, only the last 4 digits will be displayed after saving.
                            </p>
                        )}
                    </div>

                    {/* Tax Year */}
                    <div>
                        <label htmlFor="taxYear" className="block text-sm font-medium text-gray-700 mb-1">
                            Tax Year <span className="text-red-500">*</span>
                        </label>
                        <select
                            id="taxYear"
                            name="taxYear"
                            value={taxInfo.taxYear}
                            onChange={handleChange}
                            className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md"
                            required
                            disabled={!!taxInfoId} // Disable if editing existing record
                        >
                            {[...Array(5)].map((_, i) => {
                                const year = new Date().getFullYear() - 2 + i;
                                return (
                                    <option key={year} value={year}>
                                        {year}
                                    </option>
                                );
                            })}
                        </select>
                    </div>

                    {/* Total Payments */}
                    <div>
                        <label htmlFor="totalPayments" className="block text-sm font-medium text-gray-700 mb-1">
                            Total Payments ($) <span className="text-red-500">*</span>
                        </label>
                        <input
                            type="number"
                            id="totalPayments"
                            name="totalPayments"
                            value={taxInfo.totalPayments}
                            onChange={handleChange}
                            min="0"
                            step="0.01"
                            className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md"
                            required
                        />
                    </div>
                </div>

                {/* Address Information */}
                <div className="mt-6">
                    <h4 className="text-sm font-medium text-gray-900 mb-2">Address Information</h4>
                    <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                        {/* Address */}
                        <div>
                            <label htmlFor="address" className="block text-sm font-medium text-gray-700 mb-1">
                                Street Address
                            </label>
                            <input
                                type="text"
                                id="address"
                                name="address"
                                value={taxInfo.address}
                                onChange={handleChange}
                                className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md"
                            />
                        </div>

                        {/* City */}
                        <div>
                            <label htmlFor="city" className="block text-sm font-medium text-gray-700 mb-1">
                                City
                            </label>
                            <input
                                type="text"
                                id="city"
                                name="city"
                                value={taxInfo.city}
                                onChange={handleChange}
                                className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md"
                            />
                        </div>

                        {/* State */}
                        <div>
                            <label htmlFor="state" className="block text-sm font-medium text-gray-700 mb-1">
                                State
                            </label>
                            <select
                                id="state"
                                name="state"
                                value={taxInfo.state}
                                onChange={handleChange}
                                className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md"
                            >
                                <option value="">Select a state</option>
                                {states.map(state => (
                                    <option key={state.value} value={state.value}>
                                        {state.label}
                                    </option>
                                ))}
                            </select>
                        </div>

                        {/* Zip Code */}
                        <div>
                            <label htmlFor="zipCode" className="block text-sm font-medium text-gray-700 mb-1">
                                Zip Code
                            </label>
                            <input
                                type="text"
                                id="zipCode"
                                name="zipCode"
                                value={taxInfo.zipCode}
                                onChange={handleChange}
                                className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md"
                            />
                        </div>
                    </div>
                </div>

                {/* 1099 Information (only for editing) */}
                {taxInfoId && (
                    <div className="mt-6 bg-gray-50 p-4 rounded-md">
                        <h4 className="text-sm font-medium text-gray-900 mb-2">1099 Information</h4>

                        {taxInfo.form1099Generated ? (
                            <div>
                                <div className="flex items-center">
                  <span
                      className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 mr-2">
                    Generated
                  </span>
                                    {taxInfo.form1099Document && (
                                        <span className="text-sm text-gray-700">
                      Document: {taxInfo.form1099Document.title}
                    </span>
                                    )}
                                </div>
                            </div>
                        ) : (
                            <div>
                <span
                    className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 mr-2">
                  Not Generated
                </span>
                                {onGenerate1099 && (
                                    <button
                                        type="button"
                                        onClick={handleGenerate1099}
                                        className="mt-2 inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                                    >
                                        Generate 1099
                                    </button>
                                )}
                            </div>
                        )}
                    </div>
                )}

                {/* Form Actions */}
                <div className="mt-8 flex justify-end space-x-3">
                    <button
                        type="button"
                        onClick={() => onCancel && onCancel()}
                        className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                    >
                        Cancel
                    </button>
                    <button
                        type="submit"
                        disabled={loading}
                        className={`inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 ${
                            loading ? 'opacity-70 cursor-not-allowed' : ''
                        }`}
                    >
                        {loading ? 'Saving...' : taxInfoId ? 'Update Tax Info' : 'Save Tax Info'}
                    </button>
                </div>
            </form>
        </div>
    );
};

export default ContractorTaxInfoForm;
