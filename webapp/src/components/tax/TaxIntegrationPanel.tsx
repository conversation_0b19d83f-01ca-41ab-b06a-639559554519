import React, { useState, useEffect } from 'react';
import { useAuth } from '../../context/AuthContext';
import { useFarm } from '../../context/FarmContext';
import axios from 'axios';
import { API_URL } from '../../config';
import LoadingSpinner from '../LoadingSpinner';

interface FinancialData {
  id: string;
  category: string;
  amount: number;
  date: string;
  description: string;
  taxRelevant: boolean;
  taxCategory?: string;
}

interface InventoryItem {
  id: string;
  name: string;
  category: string;
  purchasePrice: number;
  purchaseDate: string;
  depreciable: boolean;
  depreciationMethod?: string;
  usefulLife?: number;
  salvageValue?: number;
}

interface EmployeeTaxData {
  id: string;
  employeeId: string;
  employeeName: string;
  taxYear: number;
  w2Generated: boolean;
  taxableWages: number;
  federalWithholding: number;
  socialSecurity: number;
  medicare: number;
  stateWithholding: number;
}

interface ExpenseData {
  id: string;
  category: string;
  amount: number;
  date: string;
  description: string;
  vendor: string;
  taxDeductible: boolean;
  taxCategory?: string;
}

interface TaxExportOptions {
  format: 'csv' | 'pdf' | 'quickbooks' | 'turbotax' | 'excel';
  year: number;
  includeFinancial: boolean;
  includeInventory: boolean;
  includeEmployees: boolean;
  includeExpenses: boolean;
}

interface TaxProfessionalAccess {
  id: string;
  name: string;
  email: string;
  accessLevel: 'view' | 'edit' | 'admin';
  expirationDate: string;
  lastAccess?: string;
}

interface TaxIntegrationPanelProps {
  year: number;
}

const TaxIntegrationPanel: React.FC<TaxIntegrationPanelProps> = ({ year }) => {
  const { user, token } = useAuth();
  const { currentFarm } = useFarm();
  const [financialData, setFinancialData] = useState<FinancialData[]>([]);
  const [inventoryItems, setInventoryItems] = useState<InventoryItem[]>([]);
  const [employeeTaxData, setEmployeeTaxData] = useState<EmployeeTaxData[]>([]);
  const [expenseData, setExpenseData] = useState<ExpenseData[]>([]);
  const [taxProfessionals, setTaxProfessionals] = useState<TaxProfessionalAccess[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'financial' | 'inventory' | 'employees' | 'expenses' | 'export' | 'professionals'>('financial');
  const [exportOptions, setExportOptions] = useState<TaxExportOptions>({
    format: 'csv',
    year: year,
    includeFinancial: true,
    includeInventory: true,
    includeEmployees: true,
    includeExpenses: true
  });
  const [exportStatus, setExportStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle');

  useEffect(() => {
    if (user && currentFarm) {
      fetchIntegrationData();
    }
  }, [user, currentFarm, year, activeTab]);

  const fetchIntegrationData = async () => {
    try {
      setLoading(true);
      setError(null);

      if (!currentFarm?.id) {
        throw new Error('No farm selected');
      }

      // Only fetch data for the active tab to improve performance
      if (activeTab === 'financial' || activeTab === 'export') {
        try {
          const financialResponse = await axios.get(`${API_URL}/api/financial/farms/${currentFarm.id}/tax-relevant`, {
            headers: { Authorization: `Bearer ${token}` },
            params: { year }
          });

          if (financialResponse.data && financialResponse.data.financialData) {
            setFinancialData(financialResponse.data.financialData);
          }
        } catch (financialErr) {
          console.error('Error fetching financial data:', financialErr);
          // Don't set the global error, just log it
          // This prevents the entire component from showing an error
        }
      }

      if (activeTab === 'inventory' || activeTab === 'export') {
        try {
          const inventoryResponse = await axios.get(`${API_URL}/api/inventory/farms/${currentFarm.id}/depreciable-assets`, {
            headers: { Authorization: `Bearer ${token}` },
            params: { year }
          });

          if (inventoryResponse.data && inventoryResponse.data.inventoryItems) {
            setInventoryItems(inventoryResponse.data.inventoryItems);
          }
        } catch (inventoryErr) {
          console.error('Error fetching inventory data:', inventoryErr);
          // Don't set the global error, just log it
        }
      }

      if (activeTab === 'employees' || activeTab === 'export') {
        try {
          const employeeResponse = await axios.get(`${API_URL}/api/tax/farms/${currentFarm.id}/employee-tax-data`, {
            headers: { Authorization: `Bearer ${token}` },
            params: { year }
          });

          if (employeeResponse.data && employeeResponse.data.employeeTaxData) {
            setEmployeeTaxData(employeeResponse.data.employeeTaxData);
          }
        } catch (employeeErr) {
          console.error('Error fetching employee data:', employeeErr);
          // Don't set the global error, just log it
        }
      }

      if (activeTab === 'expenses' || activeTab === 'export') {
        try {
          const expenseResponse = await axios.get(`${API_URL}/api/expenses/farms/${currentFarm.id}/tax-deductible`, {
            headers: { Authorization: `Bearer ${token}` },
            params: { year }
          });

          if (expenseResponse.data && expenseResponse.data.expenses) {
            setExpenseData(expenseResponse.data.expenses);
          }
        } catch (expenseErr) {
          console.error('Error fetching expense data:', expenseErr);
          // Don't set the global error, just log it
        }
      }

    } catch (err) {
      console.error('Error fetching tax integration data:', err);
      setError('Failed to load tax integration data. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(value);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  if (loading && activeTab !== 'export' && activeTab !== 'professionals') {
    return (
      <div className="flex justify-center items-center h-64">
        <LoadingSpinner />
      </div>
    );
  }

  return (
    <div className="bg-white shadow overflow-hidden sm:rounded-lg">
      <div className="px-4 py-5 sm:px-6">
        <h3 className="text-lg leading-6 font-medium text-gray-900">Tax System Integration</h3>
        <p className="mt-1 max-w-2xl text-sm text-gray-500">
          Integrate tax information with other farm management systems.
        </p>
      </div>

      {error && (
        <div className="mx-4 my-2 bg-red-50 border-l-4 border-red-400 p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-700">{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* Tabs - Displayed as pills instead of tabs to avoid confusion with main tabs */}
      <div className="px-4 py-4 bg-gray-50 border-b border-gray-200">
        <div className="text-sm font-medium text-gray-700 mb-3">Integration Options:</div>
        <div className="flex flex-wrap gap-2">
          <button
            onClick={() => setActiveTab('financial')}
            className={`${
              activeTab === 'financial'
                ? 'bg-primary-100 text-primary-800 border-primary-200'
                : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
            } px-3 py-1.5 border rounded-full text-sm font-medium`}
          >
            Financial
          </button>
          <button
            onClick={() => setActiveTab('inventory')}
            className={`${
              activeTab === 'inventory'
                ? 'bg-primary-100 text-primary-800 border-primary-200'
                : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
            } px-3 py-1.5 border rounded-full text-sm font-medium`}
          >
            Inventory
          </button>
          <button
            onClick={() => setActiveTab('employees')}
            className={`${
              activeTab === 'employees'
                ? 'bg-primary-100 text-primary-800 border-primary-200'
                : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
            } px-3 py-1.5 border rounded-full text-sm font-medium`}
          >
            Employees
          </button>
          <button
            onClick={() => setActiveTab('expenses')}
            className={`${
              activeTab === 'expenses'
                ? 'bg-primary-100 text-primary-800 border-primary-200'
                : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
            } px-3 py-1.5 border rounded-full text-sm font-medium`}
          >
            Expenses
          </button>
          <button
            onClick={() => setActiveTab('export')}
            className={`${
              activeTab === 'export'
                ? 'bg-primary-100 text-primary-800 border-primary-200'
                : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
            } px-3 py-1.5 border rounded-full text-sm font-medium`}
          >
            Export
          </button>
          <button
            onClick={() => setActiveTab('professionals')}
            className={`${
              activeTab === 'professionals'
                ? 'bg-primary-100 text-primary-800 border-primary-200'
                : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
            } px-3 py-1.5 border rounded-full text-sm font-medium`}
          >
            Tax Professionals
          </button>
        </div>
      </div>

      {/* Tab content will be implemented in the next step */}
      <div className="px-4 py-5 sm:p-6">
        {activeTab === 'financial' && (
          <div>
            <h4 className="text-md font-medium text-gray-900 mb-4">Financial Management Integration</h4>
            <p className="mb-4 text-sm text-gray-500">
              View and manage tax-relevant financial transactions from your farm's financial records.
            </p>
            {/* Financial integration content will be added here */}
          </div>
        )}

        {activeTab === 'inventory' && (
          <div>
            <h4 className="text-md font-medium text-gray-900 mb-4">Inventory Management Integration</h4>
            <p className="mb-4 text-sm text-gray-500">
              View and manage depreciable assets from your farm's inventory for tax purposes.
            </p>
            {/* Inventory integration content will be added here */}
          </div>
        )}

        {activeTab === 'employees' && (
          <div>
            <h4 className="text-md font-medium text-gray-900 mb-4">Employee Management Integration</h4>
            <p className="mb-4 text-sm text-gray-500">
              View and manage employee tax information from your farm's employee records.
            </p>
            {/* Employee integration content will be added here */}
          </div>
        )}

        {activeTab === 'expenses' && (
          <div>
            <h4 className="text-md font-medium text-gray-900 mb-4">Expense Tracking Integration</h4>
            <p className="mb-4 text-sm text-gray-500">
              View and manage tax-deductible expenses from your farm's expense records.
            </p>
            {/* Expense integration content will be added here */}
          </div>
        )}

        {activeTab === 'export' && (
          <div>
            <h4 className="text-md font-medium text-gray-900 mb-4">Export Tax Data</h4>
            <p className="mb-4 text-sm text-gray-500">
              Export tax data to various formats including QuickBooks and TurboTax.
            </p>
            {/* Export options content will be added here */}
          </div>
        )}

        {activeTab === 'professionals' && (
          <div>
            <h4 className="text-md font-medium text-gray-900 mb-4">Tax Professional Access</h4>
            <p className="mb-4 text-sm text-gray-500">
              Manage access for tax professionals to your farm's tax data.
            </p>
            {/* Tax professional access content will be added here */}
          </div>
        )}
      </div>
    </div>
  );
};

export default TaxIntegrationPanel;
