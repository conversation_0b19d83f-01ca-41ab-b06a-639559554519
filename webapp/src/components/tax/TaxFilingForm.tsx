import React, {useState, useEffect, useRef} from 'react';
import axios from 'axios';
import {API_URL} from '../../config';
import {useAuth} from '../../context/AuthContext';
import TaxFiling from "../../../server/models/TaxFiling";

interface TaxDocument {
    id: string;
    title: string;
    fileName?: string;
    status: string;
}

interface TaxFiling {
    id?: string;
    taxYear: number;
    filingType: string;
    formNumber?: string;
    dueDate: string;
    filingDate?: string;
    extensionFiled: boolean;
    extensionDate?: string;
    status: string;
    filingMethod?: string;
    confirmationNumber?: string;
    totalTax?: number;
    totalPaid?: number;
    balanceDue?: number;
    refundAmount?: number;
    notes?: string;
    documentId?: string;
    document?: TaxDocument;
}

interface TaxFilingFormProps {
    farmId: string,
    filingId?: string,
    onSave: (filing: TaxFiling, filingDocument?: File) => void,
    onCancel: () => void,
    year?: number
}

const TaxFilingForm: React.FC<TaxFilingFormProps> = ({
                                                         farmId,
                                                         filingId,
                                                         onSave,
                                                         onCancel,
                                                         year
                                                     }) => {
    const {user, token} = useAuth();
    const fileInputRef = useRef<HTMLInputElement>(null);

    const [filing, setFiling] = useState<TaxFiling>({
        taxYear: new Date().getFullYear(),
        filingType: 'federal_income',
        formNumber: '',
        dueDate: getDefaultDueDate(),
        filingDate: '',
        extensionFiled: false,
        extensionDate: '',
        status: 'not_started',
        filingMethod: '',
        confirmationNumber: '',
        totalTax: undefined,
        totalPaid: undefined,
        balanceDue: undefined,
        refundAmount: undefined,
        notes: ''
    });

    const [filingDocument, setFilingDocument] = useState<File | null>(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    const filingTypes = [
        {value: 'federal_income', label: 'Federal Income Tax'},
        {value: 'state_income', label: 'State Income Tax'},
        {value: 'property', label: 'Property Tax'},
        {value: 'payroll', label: 'Payroll Tax'},
        {value: 'sales', label: 'Sales Tax'},
        {value: 'other', label: 'Other'}
    ];

    const statusOptions = [
        {value: 'not_started', label: 'Not Started'},
        {value: 'in_progress', label: 'In Progress'},
        {value: 'ready_to_file', label: 'Ready to File'},
        {value: 'filed', label: 'Filed'},
        {value: 'accepted', label: 'Accepted'},
        {value: 'rejected', label: 'Rejected'},
        {value: 'amended', label: 'Amended'}
    ];

    const filingMethods = [
        {value: 'electronic', label: 'Electronic Filing'},
        {value: 'paper', label: 'Paper Filing'},
        {value: 'tax_professional', label: 'Tax Professional'}
    ];

    // Helper function to get default due date (April 15 of next year)
    function getDefaultDueDate(): string {
        const currentYear = new Date().getFullYear();
        const month = new Date().getMonth(); // 0-indexed, 0 = January

        // If we're past April, set due date to April 15 of next year
        const dueYear = month >= 3 ? currentYear + 1 : currentYear;

        return `${dueYear}-04-15`;
    }

    // Fetch tax filing if editing
    useEffect(() => {
        const fetchFiling = async () => {
            if (!filingId) return;

            setLoading(true);
            setError(null);

            try {
                const response = await axios.get(`${API_URL}/api/tax/tax-filings/${filingId}`, {
                    headers: {Authorization: `Bearer ${token}`}
                });

                if (response.data && response.data.taxFiling) {
                    const fetchedFiling = response.data.taxFiling;
                    setFiling({
                        id: fetchedFiling.id,
                        taxYear: fetchedFiling.taxYear,
                        filingType: fetchedFiling.filingType,
                        formNumber: fetchedFiling.formNumber || '',
                        dueDate: new Date(fetchedFiling.dueDate).toISOString().split('T')[0],
                        filingDate: fetchedFiling.filingDate ? new Date(fetchedFiling.filingDate).toISOString().split('T')[0] : '',
                        extensionFiled: fetchedFiling.extensionFiled || false,
                        extensionDate: fetchedFiling.extensionDate ? new Date(fetchedFiling.extensionDate).toISOString().split('T')[0] : '',
                        status: fetchedFiling.status,
                        filingMethod: fetchedFiling.filingMethod || '',
                        confirmationNumber: fetchedFiling.confirmationNumber || '',
                        totalTax: fetchedFiling.totalTax,
                        totalPaid: fetchedFiling.totalPaid,
                        balanceDue: fetchedFiling.balanceDue,
                        refundAmount: fetchedFiling.refundAmount,
                        notes: fetchedFiling.notes || '',
                        documentId: fetchedFiling.documentId,
                        document: fetchedFiling.filingDocument
                    });
                } else {
                    throw new Error('Invalid response format from server');
                }
            } catch (err) {
                console.error('Error fetching tax filing:', err);
                setError('Failed to load tax filing. Please try again later.');
            } finally {
                setLoading(false);
            }
        };

        if (filingId) {
            fetchFiling();
        }
    }, [filingId, user]);

    // Handle form input changes
    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
        const {name, value, type} = e.target;

        if (type === 'checkbox') {
            const checked = (e.target as HTMLInputElement).checked;
            setFiling(prev => ({...prev, [name]: checked}));
        } else if (name === 'taxYear') {
            setFiling(prev => ({...prev, [name]: parseInt(value) || new Date().getFullYear()}));
        } else if (['totalTax', 'totalPaid', 'balanceDue', 'refundAmount'].includes(name)) {
            const numValue = value === '' ? undefined : parseFloat(value) || 0;
            setFiling(prev => ({...prev, [name]: numValue}));
        } else {
            setFiling(prev => ({...prev, [name]: value}));
        }
    };

    // Handle file input changes
    const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        if (e.target.files && e.target.files.length > 0) {
            setFilingDocument(e.target.files[0]);
        }
    };

    // Handle form submission
    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();

        if (!filing.taxYear || !filing.filingType || !filing.dueDate || !filing.status) {
            setError('Please fill in all required fields.');
            return;
        }

        onSave(filing, filingDocument || undefined);
    };

    // Format file size for display
    const formatFileSize = (bytes?: number): string => {
        if (!bytes) return 'N/A';

        if (bytes < 1024) return bytes + ' bytes';
        if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(1) + ' KB';
        return (bytes / (1024 * 1024)).toFixed(1) + ' MB';
    };

    // Format currency for display
    const formatCurrency = (amount?: number): string => {
        if (amount === undefined) return '';

        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
            minimumFractionDigits: 2
        }).format(amount);
    };

    // Calculate balance due automatically
    useEffect(() => {
        if (filing.totalTax !== undefined && filing.totalPaid !== undefined) {
            const balance = filing.totalTax - filing.totalPaid;
            setFiling(prev => ({
                ...prev,
                balanceDue: balance > 0 ? balance : 0,
                refundAmount: balance < 0 ? Math.abs(balance) : 0
            }));
        }
    }, [filing.totalTax, filing.totalPaid]);

    return (
        <div className="bg-white shadow overflow-hidden sm:rounded-lg">
            <div className="px-4 py-5 sm:px-6">
                <h3 className="text-lg leading-6 font-medium text-gray-900">
                    {filingId ? 'Edit Tax Filing' : 'Create Tax Filing'}
                </h3>
                <p className="mt-1 max-w-2xl text-sm text-gray-500">
                    {filingId
                        ? 'Update the details of this tax filing.'
                        : 'Create a new tax filing record for your farm business.'}
                </p>
            </div>

            {error && (
                <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mx-6 mb-4"
                     role="alert">
                    <span className="block sm:inline">{error}</span>
                </div>
            )}

            <form onSubmit={handleSubmit} className="border-t border-gray-200 px-4 py-5 sm:px-6">
                <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                    {/* Filing Type */}
                    <div>
                        <label htmlFor="filingType" className="block text-sm font-medium text-gray-700 mb-1">
                            Filing Type <span className="text-red-500">*</span>
                        </label>
                        <select
                            id="filingType"
                            name="filingType"
                            value={filing.filingType}
                            onChange={handleChange}
                            className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md"
                            required
                        >
                            {filingTypes.map(type => (
                                <option key={type.value} value={type.value}>
                                    {type.label}
                                </option>
                            ))}
                        </select>
                    </div>

                    {/* Tax Year */}
                    <div>
                        <label htmlFor="taxYear" className="block text-sm font-medium text-gray-700 mb-1">
                            Tax Year <span className="text-red-500">*</span>
                        </label>
                        <select
                            id="taxYear"
                            name="taxYear"
                            value={filing.taxYear}
                            onChange={handleChange}
                            className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md"
                            required
                        >
                            {[...Array(5)].map((_, i) => {
                                const year = new Date().getFullYear() - 2 + i;
                                return (
                                    <option key={year} value={year}>
                                        {year}
                                    </option>
                                );
                            })}
                        </select>
                    </div>

                    {/* Form Number */}
                    <div>
                        <label htmlFor="formNumber" className="block text-sm font-medium text-gray-700 mb-1">
                            Form Number
                        </label>
                        <input
                            type="text"
                            id="formNumber"
                            name="formNumber"
                            value={filing.formNumber}
                            onChange={handleChange}
                            placeholder="e.g., 1040, Schedule F"
                            className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md"
                        />
                    </div>

                    {/* Status */}
                    <div>
                        <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">
                            Status <span className="text-red-500">*</span>
                        </label>
                        <select
                            id="status"
                            name="status"
                            value={filing.status}
                            onChange={handleChange}
                            className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md"
                            required
                        >
                            {statusOptions.map(status => (
                                <option key={status.value} value={status.value}>
                                    {status.label}
                                </option>
                            ))}
                        </select>
                    </div>

                    {/* Due Date */}
                    <div>
                        <label htmlFor="dueDate" className="block text-sm font-medium text-gray-700 mb-1">
                            Due Date <span className="text-red-500">*</span>
                        </label>
                        <input
                            type="date"
                            id="dueDate"
                            name="dueDate"
                            value={filing.dueDate}
                            onChange={handleChange}
                            className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md"
                            required
                        />
                    </div>

                    {/* Filing Method */}
                    <div>
                        <label htmlFor="filingMethod" className="block text-sm font-medium text-gray-700 mb-1">
                            Filing Method
                        </label>
                        <select
                            id="filingMethod"
                            name="filingMethod"
                            value={filing.filingMethod}
                            onChange={handleChange}
                            className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md"
                        >
                            <option value="">Select a filing method</option>
                            {filingMethods.map(method => (
                                <option key={method.value} value={method.value}>
                                    {method.label}
                                </option>
                            ))}
                        </select>
                    </div>
                </div>

                {/* Extension Information */}
                <div className="mt-6">
                    <div className="relative flex items-start">
                        <div className="flex items-center h-5">
                            <input
                                id="extensionFiled"
                                name="extensionFiled"
                                type="checkbox"
                                checked={filing.extensionFiled}
                                onChange={handleChange}
                                className="focus:ring-primary-500 h-4 w-4 text-primary-600 border-gray-300 rounded"
                            />
                        </div>
                        <div className="ml-3 text-sm">
                            <label htmlFor="extensionFiled" className="font-medium text-gray-700">
                                Extension Filed
                            </label>
                            <p className="text-gray-500">Check if you've filed for an extension</p>
                        </div>
                    </div>

                    {filing.extensionFiled && (
                        <div className="mt-3 ml-7">
                            <label htmlFor="extensionDate" className="block text-sm font-medium text-gray-700 mb-1">
                                Extension Date
                            </label>
                            <input
                                type="date"
                                id="extensionDate"
                                name="extensionDate"
                                value={filing.extensionDate}
                                onChange={handleChange}
                                className="mt-1 block w-full sm:w-1/2 pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md"
                            />
                        </div>
                    )}
                </div>

                {/* Filing Date and Confirmation */}
                <div className="mt-6 grid grid-cols-1 gap-6 sm:grid-cols-2">
                    {/* Filing Date */}
                    <div>
                        <label htmlFor="filingDate" className="block text-sm font-medium text-gray-700 mb-1">
                            Filing Date
                        </label>
                        <input
                            type="date"
                            id="filingDate"
                            name="filingDate"
                            value={filing.filingDate}
                            onChange={handleChange}
                            className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md"
                        />
                    </div>

                    {/* Confirmation Number */}
                    <div>
                        <label htmlFor="confirmationNumber" className="block text-sm font-medium text-gray-700 mb-1">
                            Confirmation Number
                        </label>
                        <input
                            type="text"
                            id="confirmationNumber"
                            name="confirmationNumber"
                            value={filing.confirmationNumber}
                            onChange={handleChange}
                            placeholder="e.g., IRS confirmation number"
                            className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md"
                        />
                    </div>
                </div>

                {/* Financial Information */}
                <div className="mt-6">
                    <h4 className="text-sm font-medium text-gray-900 mb-2">Financial Information</h4>
                    <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                        {/* Total Tax */}
                        <div>
                            <label htmlFor="totalTax" className="block text-sm font-medium text-gray-700 mb-1">
                                Total Tax ($)
                            </label>
                            <input
                                type="number"
                                id="totalTax"
                                name="totalTax"
                                value={filing.totalTax === undefined ? '' : filing.totalTax}
                                onChange={handleChange}
                                min="0"
                                step="0.01"
                                className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md"
                            />
                        </div>

                        {/* Total Paid */}
                        <div>
                            <label htmlFor="totalPaid" className="block text-sm font-medium text-gray-700 mb-1">
                                Total Paid ($)
                            </label>
                            <input
                                type="number"
                                id="totalPaid"
                                name="totalPaid"
                                value={filing.totalPaid === undefined ? '' : filing.totalPaid}
                                onChange={handleChange}
                                min="0"
                                step="0.01"
                                className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md"
                            />
                        </div>

                        {/* Balance Due */}
                        <div>
                            <label htmlFor="balanceDue" className="block text-sm font-medium text-gray-700 mb-1">
                                Balance Due ($)
                            </label>
                            <input
                                type="number"
                                id="balanceDue"
                                name="balanceDue"
                                value={filing.balanceDue === undefined ? '' : filing.balanceDue}
                                onChange={handleChange}
                                min="0"
                                step="0.01"
                                className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md"
                                readOnly
                            />
                            <p className="mt-1 text-xs text-gray-500">
                                Calculated automatically from Total Tax - Total Paid
                            </p>
                        </div>

                        {/* Refund Amount */}
                        <div>
                            <label htmlFor="refundAmount" className="block text-sm font-medium text-gray-700 mb-1">
                                Refund Amount ($)
                            </label>
                            <input
                                type="number"
                                id="refundAmount"
                                name="refundAmount"
                                value={filing.refundAmount === undefined ? '' : filing.refundAmount}
                                onChange={handleChange}
                                min="0"
                                step="0.01"
                                className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md"
                                readOnly
                            />
                            <p className="mt-1 text-xs text-gray-500">
                                Calculated automatically if Total Paid exceeds Total Tax
                            </p>
                        </div>
                    </div>
                </div>

                {/* Notes */}
                <div className="mt-6">
                    <label htmlFor="notes" className="block text-sm font-medium text-gray-700 mb-1">
                        Notes
                    </label>
                    <textarea
                        id="notes"
                        name="notes"
                        value={filing.notes}
                        onChange={handleChange}
                        rows={3}
                        className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md"
                    ></textarea>
                </div>

                {/* Filing Document Upload */}
                <div className="mt-6">
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                        Filing Document
                    </label>

                    {filing.document ? (
                        <div className="mt-2 flex items-center space-x-2">
                            <div className="flex-1 bg-gray-100 rounded-md p-3">
                                <p className="text-sm font-medium text-gray-900">{filing.document.title}</p>
                                {filing.document.fileName && (
                                    <p className="text-xs text-gray-500">{filing.document.fileName}</p>
                                )}
                            </div>
                            <button
                                type="button"
                                onClick={() => fileInputRef.current?.click()}
                                className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                            >
                                Replace
                            </button>
                        </div>
                    ) : (
                        <div
                            className="mt-2 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md">
                            <div className="space-y-1 text-center">
                                <svg
                                    className="mx-auto h-12 w-12 text-gray-400"
                                    stroke="currentColor"
                                    fill="none"
                                    viewBox="0 0 48 48"
                                    aria-hidden="true"
                                >
                                    <path
                                        d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
                                        strokeWidth={2}
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                    />
                                </svg>
                                <div className="flex text-sm text-gray-600">
                                    <label
                                        htmlFor="file-upload"
                                        className="relative cursor-pointer bg-white rounded-md font-medium text-primary-600 hover:text-primary-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-primary-500"
                                    >
                                        <span>Upload a document</span>
                                        <input
                                            id="file-upload"
                                            name="file-upload"
                                            type="file"
                                            className="sr-only"
                                            ref={fileInputRef}
                                            onChange={handleFileChange}
                                            accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                                        />
                                    </label>
                                    <p className="pl-1">or drag and drop</p>
                                </div>
                                <p className="text-xs text-gray-500">PDF, Word, or image files up to 10MB</p>
                            </div>
                        </div>
                    )}
                    <input
                        type="file"
                        className="hidden"
                        ref={fileInputRef}
                        onChange={handleFileChange}
                        accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                    />
                    {filingDocument && (
                        <div className="mt-2 text-sm text-gray-500">
                            Selected file: {filingDocument.name} ({formatFileSize(filingDocument.size)})
                        </div>
                    )}
                </div>

                {/* Form Actions */}
                <div className="mt-8 flex justify-end space-x-3">
                    <button
                        type="button"
                        onClick={onCancel}
                        className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                    >
                        Cancel
                    </button>
                    <button
                        type="submit"
                        disabled={loading}
                        className={`inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 ${
                            loading ? 'opacity-70 cursor-not-allowed' : ''
                        }`}
                    >
                        {loading ? 'Saving...' : filingId ? 'Update Filing' : 'Create Filing'}
                    </button>
                </div>
            </form>
        </div>
    );
};

export default TaxFilingForm;
