import React, {useState, useEffect} from 'react';
import axios from 'axios';
import {API_URL} from '../../config';
import {useAuth} from '../../context/AuthContext';
import TaxDeduction from "../../../server/models/TaxDeduction";

interface TaxDeduction {
    id?: string;
    name: string;
    description: string;
    amount: number;
    category: string;
    taxYear: number;
    status?: 'pending' | 'approved' | 'rejected';
}

interface TaxDeductionFormProps {
    farmId: string,
    deductionId?: string,
    onSave?: (deduction: TaxDeduction) => void,
    onCancel?: () => void,
    year?: number,
    onDeductionAdded?: () => Promise<void>
}

const TaxDeductionForm: React.FC<TaxDeductionFormProps> = ({
                                                               farmId,
                                                               deductionId,
                                                               onSave,
                                                               onCancel,
                                                               year,
                                                               onDeductionAdded
                                                           }) => {
    const {user, token} = useAuth();
    const [deduction, setDeduction] = useState<TaxDeduction>({
        name: '',
        description: '',
        amount: 0,
        category: '',
        taxYear: new Date().getFullYear()
    });
    const [categories, setCategories] = useState<string[]>([
        'Equipment', 'Seed', 'Fertilizer', 'Chemicals', 'Fuel', 'Labor',
        'Repairs', 'Insurance', 'Utilities', 'Office', 'Professional Fees', 'Other'
    ]);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    // Fetch tax deduction if editing
    useEffect(() => {
        const fetchDeduction = async () => {
            if (!deductionId) return;

            setLoading(true);
            setError(null);

            try {
                const response = await axios.get(`${API_URL}/api/tax/tax-deductions/${deductionId}`, {
                    headers: {Authorization: `Bearer ${token}`}
                });

                if (response.data && response.data.taxDeduction) {
                    const fetchedDeduction = response.data.taxDeduction;
                    setDeduction({
                        id: fetchedDeduction.id,
                        name: fetchedDeduction.name,
                        description: fetchedDeduction.description || '',
                        amount: fetchedDeduction.amount,
                        category: fetchedDeduction.category,
                        taxYear: fetchedDeduction.taxYear,
                        status: fetchedDeduction.status
                    });
                } else {
                    throw new Error('Invalid response format from server');
                }
            } catch (err) {
                console.error('Error fetching tax deduction:', err);
                setError('Failed to load tax deduction. Please try again later.');
            } finally {
                setLoading(false);
            }
        };

        if (deductionId) {
            fetchDeduction();
        }
    }, [deductionId, user]);

    // Handle form input changes
    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
        const {name, value} = e.target;

        // Handle numeric values
        if (name === 'amount') {
            setDeduction(prev => ({...prev, [name]: parseFloat(value) || 0}));
        } else if (name === 'taxYear') {
            setDeduction(prev => ({...prev, [name]: parseInt(value) || new Date().getFullYear()}));
        } else {
            setDeduction(prev => ({...prev, [name]: value}));
        }
    };

    // Handle form submission
    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        if (!deduction.name || !deduction.category || deduction.amount <= 0) {
            setError('Please fill in all required fields with valid values.');
            return;
        }

        // If onSave is provided, call it with the deduction
        if (onSave) {
            onSave(deduction);
        }

        // If onDeductionAdded is provided, call it after saving
        if (onDeductionAdded) {
            try {
                setLoading(true);

                // Create or update the deduction via API if onSave is not provided
                if (!onSave) {
                    const url = deductionId 
                        ? `${API_URL}/api/tax/tax-deductions/${deductionId}`
                        : `${API_URL}/api/tax/farms/${farmId}/tax-deductions`;

                    const method = deductionId ? 'put' : 'post';

                    await axios({
                        method,
                        url,
                        data: deduction,
                        headers: { Authorization: `Bearer ${token}` }
                    });
                }

                // Call the callback to refresh the data
                await onDeductionAdded();

                // Reset form if it's a new deduction
                if (!deductionId) {
                    setDeduction({
                        name: '',
                        description: '',
                        amount: 0,
                        category: '',
                        taxYear: year || new Date().getFullYear()
                    });
                }
            } catch (err) {
                console.error('Error saving tax deduction:', err);
                setError('Failed to save tax deduction. Please try again later.');
            } finally {
                setLoading(false);
            }
        }
    };

    return (
        <div className="bg-white shadow overflow-hidden sm:rounded-lg">
            <div className="px-4 py-5 sm:px-6">
                <h3 className="text-lg leading-6 font-medium text-gray-900">
                    {deductionId ? 'Edit Tax Deduction' : 'Create Tax Deduction'}
                </h3>
                <p className="mt-1 max-w-2xl text-sm text-gray-500">
                    {deductionId
                        ? 'Update the details of this tax deduction.'
                        : 'Add a new tax deduction for your farm business.'}
                </p>
            </div>

            {error && (
                <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mx-6 mb-4"
                     role="alert">
                    <span className="block sm:inline">{error}</span>
                </div>
            )}

            <form onSubmit={handleSubmit} className="border-t border-gray-200 px-4 py-5 sm:px-6">
                <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                    {/* Deduction Name */}
                    <div>
                        <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                            Deduction Name <span className="text-red-500">*</span>
                        </label>
                        <input
                            type="text"
                            id="name"
                            name="name"
                            value={deduction.name}
                            onChange={handleChange}
                            className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md"
                            required
                        />
                    </div>

                    {/* Category */}
                    <div>
                        <label htmlFor="category" className="block text-sm font-medium text-gray-700 mb-1">
                            Category <span className="text-red-500">*</span>
                        </label>
                        <select
                            id="category"
                            name="category"
                            value={deduction.category}
                            onChange={handleChange}
                            className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md"
                            required
                        >
                            <option value="">Select a category</option>
                            {categories.map(category => (
                                <option key={category} value={category}>
                                    {category}
                                </option>
                            ))}
                        </select>
                    </div>

                    {/* Amount */}
                    <div>
                        <label htmlFor="amount" className="block text-sm font-medium text-gray-700 mb-1">
                            Amount ($) <span className="text-red-500">*</span>
                        </label>
                        <input
                            type="number"
                            id="amount"
                            name="amount"
                            value={deduction.amount}
                            onChange={handleChange}
                            min="0"
                            step="0.01"
                            className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md"
                            required
                        />
                    </div>

                    {/* Tax Year */}
                    <div>
                        <label htmlFor="taxYear" className="block text-sm font-medium text-gray-700 mb-1">
                            Tax Year <span className="text-red-500">*</span>
                        </label>
                        <select
                            id="taxYear"
                            name="taxYear"
                            value={deduction.taxYear}
                            onChange={handleChange}
                            className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md"
                            required
                        >
                            {[...Array(5)].map((_, i) => {
                                const year = new Date().getFullYear() - 2 + i;
                                return (
                                    <option key={year} value={year}>
                                        {year}
                                    </option>
                                );
                            })}
                        </select>
                    </div>
                </div>

                {/* Description */}
                <div className="mt-6">
                    <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
                        Description
                    </label>
                    <textarea
                        id="description"
                        name="description"
                        value={deduction.description}
                        onChange={handleChange}
                        rows={4}
                        className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md"
                    ></textarea>
                </div>

                {/* Status (only for editing) */}
                {deductionId && (
                    <div className="mt-6">
                        <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">
                            Status
                        </label>
                        <select
                            id="status"
                            name="status"
                            value={deduction.status}
                            onChange={handleChange}
                            className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md"
                        >
                            <option value="pending">Pending</option>
                            <option value="approved">Approved</option>
                            <option value="rejected">Rejected</option>
                        </select>
                    </div>
                )}

                {/* Form Actions */}
                <div className="mt-8 flex justify-end space-x-3">
                    {onCancel && (
                        <button
                            type="button"
                            onClick={onCancel}
                            className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                        >
                            Cancel
                        </button>
                    )}
                    <button
                        type="submit"
                        disabled={loading}
                        className={`inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 ${
                            loading ? 'opacity-70 cursor-not-allowed' : ''
                        }`}
                    >
                        {loading ? 'Saving...' : deductionId ? 'Update Deduction' : 'Create Deduction'}
                    </button>
                </div>
            </form>
        </div>
    );
};

export default TaxDeductionForm;
