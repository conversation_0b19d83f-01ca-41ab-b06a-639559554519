import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { API_URL } from '../../config';
import { useAuth } from '../../context/AuthContext';
import { useFarm } from '../../context/FarmContext';
import LoadingSpinner from '../LoadingSpinner';

interface Employee {
  id: string;
  name: string;
  position: string;
}

interface TaxInfo {
  id: string;
  employeeId: string;
  taxYear: number;
  filingStatus?: string;
  withholdingAllowances: number;
  additionalWithholding: number;
  isExempt: boolean;
  w2Generated: boolean;
}

interface EmployeePayrollTax {
  employee: Employee;
  taxInfo: TaxInfo | null;
  payPeriods: number;
  totalGrossPay: number;
  totalNetPay: number;
  federalIncomeTax: number;
  socialSecurityTax: number;
  medicareTax: number;
  stateTax: number;
  totalTaxes: number;
  effectiveTaxRate: number;
}

interface FarmTotals {
  totalGrossPay: number;
  totalNetPay: number;
  federalIncomeTax: number;
  socialSecurityTax: number;
  medicareTax: number;
  stateTax: number;
  totalTaxes: number;
  effectiveTaxRate: number;
}

interface Period {
  year: number;
  quarter: number | null;
  month: number | null;
  startDate: string;
  endDate: string;
}

interface PayrollTaxReport {
  period: Period;
  employees: EmployeePayrollTax[];
  farmTotals: FarmTotals;
}

interface PayrollTaxReportProps {
  farmId: string;
  year?: number;
  quarter?: number;
  month?: number;
  employeeId?: string;
  onClose?: () => void;
}

const PayrollTaxReport: React.FC<PayrollTaxReportProps> = ({
  farmId,
  year = new Date().getFullYear(),
  quarter,
  month,
  employeeId,
  onClose
}) => {
  const { user, token } = useAuth();
  const { currentFarm } = useFarm();
  const [report, setReport] = useState<PayrollTaxReport | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedYear, setSelectedYear] = useState<number>(year);
  const [selectedQuarter, setSelectedQuarter] = useState<number | null>(quarter || null);
  const [selectedMonth, setSelectedMonth] = useState<number | null>(month || null);
  const [selectedEmployeeId, setSelectedEmployeeId] = useState<string | undefined>(employeeId);

  useEffect(() => {
    if (user && farmId) {
      fetchPayrollTaxReport();
    }
  }, [user, farmId, selectedYear, selectedQuarter, selectedMonth, selectedEmployeeId]);

  const fetchPayrollTaxReport = async () => {
    try {
      setLoading(true);
      setError(null);

      // Build query parameters
      const params: Record<string, string> = {
        year: selectedYear.toString()
      };

      if (selectedQuarter !== null) {
        params.quarter = selectedQuarter.toString();
      }

      if (selectedMonth !== null) {
        params.month = selectedMonth.toString();
      }

      if (selectedEmployeeId) {
        params.employeeId = selectedEmployeeId;
      }

      // Fetch payroll tax report
      const response = await axios.get(`${API_URL}/api/tax/farms/${farmId}/payroll-taxes`, {
        headers: { Authorization: `Bearer ${token}` },
        params
      });

      if (response.data) {
        setReport(response.data);
      } else {
        throw new Error('Invalid response format from server');
      }
    } catch (err: any) {
      console.error('Error fetching payroll tax report:', err);

      // Handle authentication errors without causing logout
      if (err.response && err.response.status === 401) {
        setError('Authentication error. Please refresh the page and try again.');
      } else {
        setError('Failed to load payroll tax report. Please try again later.');
      }
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(value);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const formatPercentage = (value: number) => {
    return `${value.toFixed(2)}%`;
  };

  const handleYearChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedYear(parseInt(e.target.value));
  };

  const handleQuarterChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const value = e.target.value;
    if (value === '') {
      setSelectedQuarter(null);
    } else {
      setSelectedQuarter(parseInt(value));
      setSelectedMonth(null); // Reset month when quarter is selected
    }
  };

  const handleMonthChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const value = e.target.value;
    if (value === '') {
      setSelectedMonth(null);
    } else {
      setSelectedMonth(parseInt(value));
      setSelectedQuarter(null); // Reset quarter when month is selected
    }
  };

  const handleEmployeeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const value = e.target.value;
    setSelectedEmployeeId(value === '' ? undefined : value);
  };

  const getPeriodLabel = () => {
    if (report) {
      if (report.period.quarter) {
        return `Q${report.period.quarter} ${report.period.year}`;
      } else if (report.period.month) {
        return new Date(report.period.year, report.period.month - 1, 1).toLocaleDateString('en-US', { month: 'long', year: 'numeric' });
      } else {
        return `Year ${report.period.year}`;
      }
    }
    return '';
  };

  if (loading && !report) {
    return (
      <div className="flex justify-center items-center h-64">
        <LoadingSpinner />
      </div>
    );
  }

  return (
    <div className="bg-white shadow overflow-hidden sm:rounded-lg">
      <div className="px-4 py-5 sm:px-6 flex justify-between items-center">
        <div>
          <h3 className="text-lg leading-6 font-medium text-gray-900">Payroll Tax Report</h3>
          <p className="mt-1 max-w-2xl text-sm text-gray-500">
            {report && `${formatDate(report.period.startDate)} - ${formatDate(report.period.endDate)}`}
          </p>
        </div>
        {onClose && (
          <button
            type="button"
            onClick={onClose}
            className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Close
          </button>
        )}
      </div>

      {error && (
        <div className="bg-red-100 border-l-4 border-red-400 p-4 mb-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-700">{error}</p>
            </div>
          </div>
        </div>
      )}

      <div className="border-t border-gray-200 px-4 py-5 sm:p-6">
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-4 mb-6">
          <div>
            <label htmlFor="year-select" className="block text-sm font-medium text-gray-700 mb-1">
              Year
            </label>
            <select
              id="year-select"
              name="year-select"
              className="block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-base focus:border-primary-500 focus:outline-none focus:ring-primary-500 sm:text-sm"
              value={selectedYear}
              onChange={handleYearChange}
            >
              {[...Array(5)].map((_, i) => {
                const year = new Date().getFullYear() - 2 + i;
                return (
                  <option key={year} value={year}>
                    {year}
                  </option>
                );
              })}
            </select>
          </div>

          <div>
            <label htmlFor="quarter-select" className="block text-sm font-medium text-gray-700 mb-1">
              Quarter
            </label>
            <select
              id="quarter-select"
              name="quarter-select"
              className="block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-base focus:border-primary-500 focus:outline-none focus:ring-primary-500 sm:text-sm"
              value={selectedQuarter === null ? '' : selectedQuarter}
              onChange={handleQuarterChange}
            >
              <option value="">All Quarters</option>
              <option value="1">Q1 (Jan-Mar)</option>
              <option value="2">Q2 (Apr-Jun)</option>
              <option value="3">Q3 (Jul-Sep)</option>
              <option value="4">Q4 (Oct-Dec)</option>
            </select>
          </div>

          <div>
            <label htmlFor="month-select" className="block text-sm font-medium text-gray-700 mb-1">
              Month
            </label>
            <select
              id="month-select"
              name="month-select"
              className="block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-base focus:border-primary-500 focus:outline-none focus:ring-primary-500 sm:text-sm"
              value={selectedMonth === null ? '' : selectedMonth}
              onChange={handleMonthChange}
            >
              <option value="">All Months</option>
              <option value="1">January</option>
              <option value="2">February</option>
              <option value="3">March</option>
              <option value="4">April</option>
              <option value="5">May</option>
              <option value="6">June</option>
              <option value="7">July</option>
              <option value="8">August</option>
              <option value="9">September</option>
              <option value="10">October</option>
              <option value="11">November</option>
              <option value="12">December</option>
            </select>
          </div>

          <div>
            <label htmlFor="employee-select" className="block text-sm font-medium text-gray-700 mb-1">
              Employee
            </label>
            <select
              id="employee-select"
              name="employee-select"
              className="block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-base focus:border-primary-500 focus:outline-none focus:ring-primary-500 sm:text-sm"
              value={selectedEmployeeId || ''}
              onChange={handleEmployeeChange}
            >
              <option value="">All Employees</option>
              {report?.employees.map(item => (
                <option key={item.employee.id} value={item.employee.id}>
                  {item.employee.name}
                </option>
              ))}
            </select>
          </div>
        </div>

        {report && (
          <>
            {/* Farm Totals Summary */}
            <div className="mb-8">
              <h4 className="text-lg font-medium text-gray-900 mb-4">
                Farm Payroll Tax Summary - {getPeriodLabel()}
              </h4>
              <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
                <div className="bg-white overflow-hidden shadow rounded-lg">
                  <div className="px-4 py-5 sm:p-6">
                    <dt className="text-sm font-medium text-gray-500 truncate">Total Gross Pay</dt>
                    <dd className="mt-1 text-3xl font-semibold text-gray-900">{formatCurrency(report.farmTotals.totalGrossPay)}</dd>
                  </div>
                </div>
                <div className="bg-white overflow-hidden shadow rounded-lg">
                  <div className="px-4 py-5 sm:p-6">
                    <dt className="text-sm font-medium text-gray-500 truncate">Total Net Pay</dt>
                    <dd className="mt-1 text-3xl font-semibold text-gray-900">{formatCurrency(report.farmTotals.totalNetPay)}</dd>
                  </div>
                </div>
                <div className="bg-white overflow-hidden shadow rounded-lg">
                  <div className="px-4 py-5 sm:p-6">
                    <dt className="text-sm font-medium text-gray-500 truncate">Total Taxes</dt>
                    <dd className="mt-1 text-3xl font-semibold text-gray-900">{formatCurrency(report.farmTotals.totalTaxes)}</dd>
                  </div>
                </div>
                <div className="bg-white overflow-hidden shadow rounded-lg">
                  <div className="px-4 py-5 sm:p-6">
                    <dt className="text-sm font-medium text-gray-500 truncate">Effective Tax Rate</dt>
                    <dd className="mt-1 text-3xl font-semibold text-gray-900">{formatPercentage(report.farmTotals.effectiveTaxRate)}</dd>
                  </div>
                </div>
              </div>
            </div>

            {/* Tax Breakdown */}
            <div className="mb-8">
              <h4 className="text-lg font-medium text-gray-900 mb-4">Tax Breakdown</h4>
              <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
                <div className="bg-white overflow-hidden shadow rounded-lg">
                  <div className="px-4 py-5 sm:p-6">
                    <dt className="text-sm font-medium text-gray-500 truncate">Federal Income Tax</dt>
                    <dd className="mt-1 text-2xl font-semibold text-gray-900">{formatCurrency(report.farmTotals.federalIncomeTax)}</dd>
                  </div>
                </div>
                <div className="bg-white overflow-hidden shadow rounded-lg">
                  <div className="px-4 py-5 sm:p-6">
                    <dt className="text-sm font-medium text-gray-500 truncate">Social Security Tax</dt>
                    <dd className="mt-1 text-2xl font-semibold text-gray-900">{formatCurrency(report.farmTotals.socialSecurityTax)}</dd>
                  </div>
                </div>
                <div className="bg-white overflow-hidden shadow rounded-lg">
                  <div className="px-4 py-5 sm:p-6">
                    <dt className="text-sm font-medium text-gray-500 truncate">Medicare Tax</dt>
                    <dd className="mt-1 text-2xl font-semibold text-gray-900">{formatCurrency(report.farmTotals.medicareTax)}</dd>
                  </div>
                </div>
                <div className="bg-white overflow-hidden shadow rounded-lg">
                  <div className="px-4 py-5 sm:p-6">
                    <dt className="text-sm font-medium text-gray-500 truncate">State Tax</dt>
                    <dd className="mt-1 text-2xl font-semibold text-gray-900">{formatCurrency(report.farmTotals.stateTax)}</dd>
                  </div>
                </div>
              </div>
            </div>

            {/* Employee Details Table */}
            <div>
              <h4 className="text-lg font-medium text-gray-900 mb-4">Employee Details</h4>
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Employee
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Pay Periods
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Gross Pay
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Federal Tax
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Social Security
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Medicare
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        State Tax
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Total Tax
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Net Pay
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Tax Rate
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {report.employees.map((employee) => (
                      <tr key={employee.employee.id}>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900">{employee.employee.name}</div>
                          <div className="text-sm text-gray-500">{employee.employee.position}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {employee.payPeriods}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {formatCurrency(employee.totalGrossPay)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {formatCurrency(employee.federalIncomeTax)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {formatCurrency(employee.socialSecurityTax)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {formatCurrency(employee.medicareTax)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {formatCurrency(employee.stateTax)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {formatCurrency(employee.totalTaxes)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {formatCurrency(employee.totalNetPay)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {formatPercentage(employee.effectiveTaxRate)}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default PayrollTaxReport;
