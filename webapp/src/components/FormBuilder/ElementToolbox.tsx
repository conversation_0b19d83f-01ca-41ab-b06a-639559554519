import React from 'react';

interface ElementToolboxProps {
  onAddElement: (elementType: string, defaultProps?: any) => void;
}

const ElementToolbox: React.FC<ElementToolboxProps> = ({ onAddElement }) => {
  // Define element categories and their items
  const elementCategories = [
    {
      name: 'Basic Elements',
      items: [
        { type: 'text', label: 'Text', icon: 'text-size' },
        { type: 'heading', label: 'Heading', icon: 'type-h1' },
        { type: 'paragraph', label: 'Paragraph', icon: 'paragraph' },
        { type: 'image', label: 'Image', icon: 'image' },
        { type: 'divider', label: 'Divider', icon: 'hr' },
        { type: 'spacer', label: 'Spacer', icon: 'arrows-expand' }
      ]
    },
    {
      name: 'Form Fields',
      items: [
        { 
          type: 'field', 
          label: 'Text Field', 
          icon: 'input-cursor-text',
          defaultProps: { field_type: 'text', field_name: 'Text Field' } 
        },
        { 
          type: 'field', 
          label: 'Checkbox', 
          icon: 'check-square',
          defaultProps: { field_type: 'checkbox', field_name: 'Checkbox' } 
        },
        { 
          type: 'field', 
          label: 'Radio Button', 
          icon: 'record-circle',
          defaultProps: { field_type: 'radio', field_name: 'Radio Button' } 
        },
        { 
          type: 'field', 
          label: 'Dropdown', 
          icon: 'menu-button-wide',
          defaultProps: { field_type: 'dropdown', field_name: 'Dropdown', options: [] } 
        },
        { 
          type: 'field', 
          label: 'Date', 
          icon: 'calendar',
          defaultProps: { field_type: 'date', field_name: 'Date' } 
        }
      ]
    },
    {
      name: 'Signature Fields',
      items: [
        { 
          type: 'field', 
          label: 'Signature', 
          icon: 'pen',
          defaultProps: { field_type: 'signature', field_name: 'Signature', is_required: true } 
        },
        { 
          type: 'field', 
          label: 'Initial', 
          icon: 'pencil',
          defaultProps: { field_type: 'initial', field_name: 'Initial' } 
        }
      ]
    }
  ];

  // Helper function to render an icon
  const renderIcon = (iconName: string) => {
    // This is a placeholder. In a real implementation, you would use your icon library
    return <span className={`bi bi-${iconName}`}></span>;
  };

  return (
    <div className="element-toolbox">
      <h3 className="text-lg font-medium mb-4">Elements</h3>
      
      {elementCategories.map((category, categoryIndex) => (
        <div key={categoryIndex} className="mb-6">
          <h4 className="text-sm font-medium text-gray-500 mb-2">{category.name}</h4>
          <div className="grid grid-cols-2 gap-2">
            {category.items.map((item, itemIndex) => (
              <button
                key={itemIndex}
                className="flex flex-col items-center justify-center p-2 bg-white border border-gray-200 rounded hover:bg-gray-50 hover:border-gray-300 transition-colors"
                onClick={() => onAddElement(item.type, item.defaultProps)}
              >
                <div className="w-8 h-8 flex items-center justify-center text-gray-600 mb-1">
                  {renderIcon(item.icon)}
                </div>
                <span className="text-xs">{item.label}</span>
              </button>
            ))}
          </div>
        </div>
      ))}
      
      <div className="mt-4 text-xs text-gray-500">
        <p>Drag elements onto the canvas or click to add them.</p>
      </div>
    </div>
  );
};

export default ElementToolbox;