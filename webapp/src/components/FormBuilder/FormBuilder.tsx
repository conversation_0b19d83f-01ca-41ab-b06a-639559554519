import React, { useState, useRef, useEffect } from 'react';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import ElementToolbox from './ElementToolbox';
import FormCanvas from './FormCanvas';
import ElementProperties from './ElementProperties';
import { v4 as uuidv4 } from 'uuid';

export interface FormElement {
  id: string;
  element_type: string;
  content?: string;
  image_path?: string;
  field_type?: string;
  field_name?: string;
  is_required?: boolean;
  placeholder?: string;
  options?: any;
  validation_rules?: any;
  style_properties?: any;
  page_number: number;
  x_position: number;
  y_position: number;
  width: number;
  height: number;
  z_index: number;
  font_family?: string;
  font_size?: number;
  font_color?: string;
  background_color?: string;
  border_style?: string;
  border_width?: number;
  border_color?: string;
  signer_id?: string;
}

interface FormBuilderProps {
  initialElements?: FormElement[];
  signers?: Array<{ id: string; name: string; email: string; role?: string }>;
  readOnly?: boolean;
  onSave?: (elements: FormElement[]) => void;
}

const FormBuilder: React.FC<FormBuilderProps> = ({
  initialElements = [],
  signers = [],
  readOnly = false,
  onSave
}) => {
  const [elements, setElements] = useState<FormElement[]>(initialElements);
  const [selectedElement, setSelectedElement] = useState<FormElement | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const canvasRef = useRef<HTMLDivElement>(null);

  // Initialize with initial elements or empty array
  useEffect(() => {
    if (initialElements.length > 0) {
      setElements(initialElements);

      // Find the highest page number to set total pages
      const maxPage = Math.max(...initialElements.map(el => el.page_number));
      setTotalPages(maxPage);
    }
  }, [initialElements]);

  // Find a non-overlapping position for a new element
  const findNonOverlappingPosition = (
    width: number,
    height: number,
    pageNumber: number
  ): { x: number, y: number } => {
    // Start position
    let x = 100;
    let y = 100;

    // Grid size for snapping
    const GRID_SIZE = 10;

    // Check if position overlaps with any existing element
    const checkOverlap = (posX: number, posY: number): boolean => {
      return elements.some(el => {
        if (el.page_number !== pageNumber) return false;

        return (
          posX < el.x_position + el.width &&
          posX + width > el.x_position &&
          posY < el.y_position + el.height &&
          posY + height > el.y_position
        );
      });
    };

    // If no overlap at initial position, return it
    if (!checkOverlap(x, y)) {
      return { x, y };
    }

    // Try positions in a grid pattern
    for (let offsetY = 0; offsetY < 500; offsetY += GRID_SIZE) {
      for (let offsetX = 0; offsetX < 500; offsetX += GRID_SIZE) {
        x = 100 + offsetX;
        y = 100 + offsetY;

        if (!checkOverlap(x, y)) {
          return { x, y };
        }
      }
    }

    // If we couldn't find a non-overlapping position, return a position far from the origin
    return { x: 100, y: elements.length * 50 + 100 };
  };

  // Add a new element to the canvas
  const handleAddElement = (elementType: string, defaultProps: Partial<FormElement> = {}) => {
    if (readOnly) return;

    // Determine element size based on type
    const width = elementType === 'text' ? 200 : 100;
    const height = elementType === 'text' ? 100 : 40;

    // Find a non-overlapping position
    const { x, y } = findNonOverlappingPosition(width, height, currentPage);

    const newElement: FormElement = {
      id: uuidv4(),
      element_type: elementType,
      page_number: currentPage,
      x_position: x,
      y_position: y,
      width: width,
      height: height,
      z_index: elements.length,
      ...defaultProps
    };

    setElements([...elements, newElement]);
    setSelectedElement(newElement);
  };

  // Update an element's properties
  const handleUpdateElement = (updatedElement: FormElement) => {
    if (readOnly) return;

    setElements(elements.map(el => 
      el.id === updatedElement.id ? updatedElement : el
    ));
    setSelectedElement(updatedElement);
  };

  // Delete an element
  const handleDeleteElement = (elementId: string) => {
    if (readOnly) return;

    setElements(elements.filter(el => el.id !== elementId));
    if (selectedElement && selectedElement.id === elementId) {
      setSelectedElement(null);
    }
  };

  // Handle element selection
  const handleSelectElement = (element: FormElement | null) => {
    setSelectedElement(element);
  };

  // Add a new page
  const handleAddPage = () => {
    if (readOnly) return;
    setTotalPages(totalPages + 1);
  };

  // Change current page
  const handleChangePage = (pageNumber: number) => {
    if (pageNumber < 1 || pageNumber > totalPages) return;
    setCurrentPage(pageNumber);
  };

  // Save the form
  const handleSave = () => {
    if (onSave) {
      onSave(elements);
    }
  };

  return (
    <DndProvider backend={HTML5Backend}>
      <div className="flex flex-col h-full">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold">Form Builder</h2>
          {!readOnly && (
            <button
              onClick={handleSave}
              className="px-4 py-2 bg-primary-600 text-white rounded hover:bg-primary-700"
            >
              Save Form
            </button>
          )}
        </div>

        <div className="flex flex-1 gap-4">
          {!readOnly && (
            <div className="w-64 bg-white p-4 rounded shadow">
              <ElementToolbox onAddElement={handleAddElement} />
            </div>
          )}

          <div className="flex-1 bg-gray-100 rounded shadow">
            <div className="p-4 bg-white border-b">
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => handleChangePage(currentPage - 1)}
                  disabled={currentPage === 1}
                  className="px-2 py-1 bg-gray-200 rounded disabled:opacity-50"
                >
                  Previous
                </button>
                <span>
                  Page {currentPage} of {totalPages}
                </span>
                <button
                  onClick={() => handleChangePage(currentPage + 1)}
                  disabled={currentPage === totalPages}
                  className="px-2 py-1 bg-gray-200 rounded disabled:opacity-50"
                >
                  Next
                </button>
                {!readOnly && (
                  <button
                    onClick={handleAddPage}
                    className="px-2 py-1 bg-gray-200 rounded ml-4"
                  >
                    Add Page
                  </button>
                )}
              </div>
            </div>
            <div className="p-8 overflow-auto">
              <FormCanvas
                ref={canvasRef}
                elements={elements.filter(el => el.page_number === currentPage)}
                selectedElement={selectedElement}
                onSelectElement={handleSelectElement}
                onUpdateElement={handleUpdateElement}
                onDeleteElement={handleDeleteElement}
                readOnly={readOnly}
              />
            </div>
          </div>

          {!readOnly && selectedElement && (
            <div className="w-80 bg-white p-4 rounded shadow z-10">
              <ElementProperties
                element={selectedElement}
                signers={signers}
                onUpdateElement={handleUpdateElement}
                onDeleteElement={handleDeleteElement}
              />
            </div>
          )}
        </div>
      </div>
    </DndProvider>
  );
};

export default FormBuilder;
