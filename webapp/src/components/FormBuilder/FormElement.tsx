import React, { useRef } from 'react';
import { useDrag } from 'react-dnd';
import { FormElement as FormElementType } from './FormBuilder';

interface FormElementProps {
  element: FormElementType;
  isSelected: boolean;
  onSelect: () => void;
  onMove: (elementId: string, deltaX: number, deltaY: number) => void;
  onResize: (elementId: string, width: number, height: number) => void;
  onDelete: () => void;
  readOnly?: boolean;
}

const FormElement: React.FC<FormElementProps> = ({
  element,
  isSelected,
  onSelect,
  onMove,
  onResize,
  onDelete,
  readOnly = false
}) => {
  const elementRef = useRef<HTMLDivElement>(null);
  const resizeStartPos = useRef({ x: 0, y: 0 });
  const originalSize = useRef({ width: 0, height: 0 });
  const isResizing = useRef(false);

  // Set up drag and drop
  const [{ isDragging }, drag] = useDrag({
    type: 'FORM_ELEMENT',
    item: { id: element.id, type: element.element_type },
    collect: (monitor) => ({
      isDragging: !!monitor.isDragging()
    }),
    canDrag: !readOnly
  });

  // Connect the drag ref to the element ref
  drag(elementRef);

  // Handle mouse down for dragging or resizing
  const handleMouseDown = (e: React.MouseEvent) => {
    if (readOnly) return;

    e.stopPropagation();
    onSelect();

    // Check if we're clicking on a resize handle
    const target = e.target as HTMLElement;
    if (target.classList.contains('resize-handle')) {
      isResizing.current = true;
      resizeStartPos.current = { x: e.clientX, y: e.clientY };
      originalSize.current = {
        width: element.width,
        height: element.height
      };

      // Add event listeners for resize
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
    }
  };

  // Handle mouse move for resizing
  const handleMouseMove = (e: MouseEvent) => {
    if (!isResizing.current) return;

    const deltaX = e.clientX - resizeStartPos.current.x;
    const deltaY = e.clientY - resizeStartPos.current.y;

    const newWidth = Math.max(20, originalSize.current.width + deltaX);
    const newHeight = Math.max(20, originalSize.current.height + deltaY);

    onResize(element.id, newWidth, newHeight);
  };

  // Handle mouse up to end resizing
  const handleMouseUp = () => {
    isResizing.current = false;
    document.removeEventListener('mousemove', handleMouseMove);
    document.removeEventListener('mouseup', handleMouseUp);
  };

  // Handle key press for deleting elements
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (readOnly) return;

    if (isSelected && (e.key === 'Delete' || e.key === 'Backspace')) {
      onDelete();
    }
  };

  // Render the appropriate content based on element type
  const renderElementContent = () => {
    switch (element.element_type) {
      case 'text':
        return (
          <div 
            className="w-full h-full p-2 overflow-hidden"
            style={{
              fontFamily: element.font_family || 'inherit',
              fontSize: element.font_size ? `${element.font_size}px` : 'inherit',
              color: element.font_color || 'inherit'
            }}
          >
            {element.content || 'Text content'}
          </div>
        );

      case 'heading':
        return (
          <h2 
            className="w-full h-full p-2 font-bold overflow-hidden"
            style={{
              fontFamily: element.font_family || 'inherit',
              fontSize: element.font_size ? `${element.font_size}px` : '24px',
              color: element.font_color || 'inherit'
            }}
          >
            {element.content || 'Heading'}
          </h2>
        );

      case 'paragraph':
        return (
          <p 
            className="w-full h-full p-2 overflow-hidden"
            style={{
              fontFamily: element.font_family || 'inherit',
              fontSize: element.font_size ? `${element.font_size}px` : 'inherit',
              color: element.font_color || 'inherit'
            }}
          >
            {element.content || 'Paragraph text'}
          </p>
        );

      case 'image':
        return (
          <div className="w-full h-full flex items-center justify-center bg-gray-100">
            {element.image_path ? (
              <img 
                src={element.image_path} 
                alt="Form element" 
                className="max-w-full max-h-full object-contain"
              />
            ) : (
              <div className="text-gray-400">Image placeholder</div>
            )}
          </div>
        );

      case 'divider':
        return (
          <hr className="w-full my-2 border-t border-gray-300" />
        );

      case 'spacer':
        return (
          <div className="w-full h-full" />
        );

      case 'field':
        return renderFieldElement();

      default:
        return (
          <div className="w-full h-full p-2 bg-gray-100 flex items-center justify-center">
            Unknown element type: {element.element_type}
          </div>
        );
    }
  };

  // Render field elements (signature, text field, etc.)
  const renderFieldElement = () => {
    if (!element.field_type) return null;

    switch (element.field_type) {
      case 'signature':
        return (
          <div className="w-full h-full border-2 border-dashed border-gray-400 flex items-center justify-center bg-gray-50">
            <span className="text-gray-500">Signature</span>
          </div>
        );

      case 'initial':
        return (
          <div className="w-full h-full border-2 border-dashed border-gray-400 flex items-center justify-center bg-gray-50">
            <span className="text-gray-500">Initial</span>
          </div>
        );

      case 'text':
        return (
          <div className="w-full h-full">
            <input
              type="text"
              className="w-full h-full border border-gray-300 rounded px-2 py-1"
              placeholder={element.placeholder || 'Text field'}
              disabled={readOnly}
              onClick={(e) => e.stopPropagation()}
            />
          </div>
        );

      case 'checkbox':
        return (
          <div className="w-full h-full flex items-center">
            <input
              type="checkbox"
              className="mr-2"
              disabled={readOnly}
              onClick={(e) => e.stopPropagation()}
            />
            <span>{element.field_name || 'Checkbox'}</span>
          </div>
        );

      case 'radio':
        return (
          <div className="w-full h-full flex items-center">
            <input
              type="radio"
              className="mr-2"
              disabled={readOnly}
              onClick={(e) => e.stopPropagation()}
            />
            <span>{element.field_name || 'Radio button'}</span>
          </div>
        );

      case 'dropdown':
        return (
          <div className="w-full h-full">
            <select
              className="w-full h-full border border-gray-300 rounded px-2 py-1"
              disabled={readOnly}
              onClick={(e) => e.stopPropagation()}
            >
              <option value="">Select an option</option>
              {element.options && Array.isArray(element.options) && element.options.map((option, index) => (
                <option key={index} value={option.value}>{option.label}</option>
              ))}
            </select>
          </div>
        );

      case 'date':
        return (
          <div className="w-full h-full">
            <input
              type="date"
              className="w-full h-full border border-gray-300 rounded px-2 py-1"
              disabled={readOnly}
              onClick={(e) => e.stopPropagation()}
            />
          </div>
        );

      default:
        return (
          <div className="w-full h-full p-2 bg-gray-100 flex items-center justify-center">
            Unknown field type: {element.field_type}
          </div>
        );
    }
  };

  return (
    <div
      ref={elementRef}
      className={`absolute ${isSelected ? 'z-10' : ''} ${isDragging ? 'opacity-50' : ''}`}
      style={{
        left: `${element.x_position}px`,
        top: `${element.y_position}px`,
        width: `${element.width}px`,
        height: `${element.height}px`,
        backgroundColor: element.background_color || 'transparent',
        border: isSelected ? '2px solid #3b82f6' : element.border_style ? `${element.border_width || 1}px ${element.border_style} ${element.border_color || '#000'}` : '1px dashed #e5e7eb',
        boxShadow: isSelected ? '0 0 0 2px rgba(59, 130, 246, 0.5)' : 'none',
        cursor: readOnly ? 'default' : 'move',
        zIndex: element.z_index
      }}
      onClick={(e) => {
        e.stopPropagation();
        onSelect();
      }}
      onMouseDown={handleMouseDown}
      onKeyDown={handleKeyDown}
      tabIndex={0}
    >
      {renderElementContent()}

      {/* Resize handle */}
      {isSelected && !readOnly && (
        <div
          className="resize-handle absolute bottom-0 right-0 w-4 h-4 bg-blue-500 cursor-se-resize"
          onMouseDown={(e) => {
            e.stopPropagation();
            isResizing.current = true;
            resizeStartPos.current = { x: e.clientX, y: e.clientY };
            originalSize.current = {
              width: element.width,
              height: element.height
            };
            document.addEventListener('mousemove', handleMouseMove);
            document.addEventListener('mouseup', handleMouseUp);
          }}
        />
      )}
    </div>
  );
};

export default FormElement;
