import React, { useState, useEffect } from 'react';
import { Link, useNavigate, useParams } from 'react-router-dom';
import axios from 'axios';
import { API_URL } from '../../../config';
import { getFarmIdFromSubdomain, getSubdomain } from '../../../utils/subdomainUtils';

const CustomerLogin: React.FC = () => {
  const { farmId: urlFarmId } = useParams<{ farmId: string }>();
  const [actualFarmId, setActualFarmId] = useState<string | null>(urlFarmId || null);
  const [email, setEmail] = useState('');
  const [phoneNumber, setPhoneNumber] = useState('');
  const [loginMethod, setLoginMethod] = useState<'email' | 'phone'>('email');
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const navigate = useNavigate();

  useEffect(() => {
    const fetchFarmId = async () => {
      // If we already have a farm ID from the URL, use that
      if (urlFarmId) {
        setActualFarmId(urlFarmId);
        return;
      }

      // Otherwise, try to get it from the subdomain
      try {
        const subdomainFarmId = await getFarmIdFromSubdomain();
        if (subdomainFarmId) {
          setActualFarmId(subdomainFarmId);
        } else {
          setError('Farm ID could not be determined. Please access this page through the proper URL or subdomain.');
        }
      } catch (err) {
        console.error('Error getting farm ID from subdomain:', err);
        setError('Error determining farm ID. Please try again or contact support.');
      }
    };

    fetchFarmId();
  }, [urlFarmId]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!actualFarmId) {
      setError('Farm ID is missing. Please access this page through the proper URL or subdomain.');
      return;
    }

    // Validate input based on login method
    if (loginMethod === 'email' && !email) {
      setError('Please enter your email address.');
      return;
    }

    if (loginMethod === 'phone' && !phoneNumber) {
      setError('Please enter your phone number.');
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // Prepare request data based on login method
      const requestData = {
        farmId: actualFarmId,
        ...(loginMethod === 'email' ? { email } : { phoneNumber })
      };

      // Send request to get one-time login link
      await axios.post(`${API_URL}/customer/auth/send-login-link`, requestData);

      // Show success message (we don't redirect here, user needs to check email/phone)
      setSuccess(true);
    } catch (err: any) {
      console.error('Login link request error:', err);
      setError(err.response?.data?.error || 'Failed to send login link. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
          Customer Login
        </h2>
        <p className="mt-2 text-center text-sm text-gray-600">
          Access your invoices and payments
        </p>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
          {error && !success && (
            <div className="bg-red-50 border-l-4 border-red-400 p-4 mb-4">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <p className="text-sm text-red-700">{error}</p>
                </div>
              </div>
            </div>
          )}

          {success ? (
            <div className="bg-green-50 border-l-4 border-green-400 p-4 mb-4">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-green-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <p className="text-sm text-green-700">
                    {loginMethod === 'email' 
                      ? `Login link sent to ${email}. Please check your email.` 
                      : `Login link sent to ${phoneNumber}. Please check your messages.`}
                  </p>
                  <p className="mt-2 text-sm text-green-700">
                    The link will expire in 24 hours. Click the link to log in.
                  </p>
                </div>
              </div>
            </div>
          ) : (
            <form className="space-y-6" onSubmit={handleSubmit}>
              {/* Login method toggle */}
              <div className="flex justify-center space-x-4 mb-4">
                <button
                  type="button"
                  onClick={() => setLoginMethod('email')}
                  className={`px-4 py-2 text-sm font-medium rounded-md ${
                    loginMethod === 'email'
                      ? 'bg-primary-100 text-primary-700'
                      : 'bg-white text-gray-500 hover:bg-gray-50'
                  }`}
                >
                  Email
                </button>
                <button
                  type="button"
                  onClick={() => setLoginMethod('phone')}
                  className={`px-4 py-2 text-sm font-medium rounded-md ${
                    loginMethod === 'phone'
                      ? 'bg-primary-100 text-primary-700'
                      : 'bg-white text-gray-500 hover:bg-gray-50'
                  }`}
                >
                  Phone
                </button>
              </div>

              {loginMethod === 'email' ? (
                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                    Email address
                  </label>
                  <div className="mt-1">
                    <input
                      id="email"
                      name="email"
                      type="email"
                      autoComplete="email"
                      required
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                    />
                  </div>
                </div>
              ) : (
                <div>
                  <label htmlFor="phoneNumber" className="block text-sm font-medium text-gray-700">
                    Phone number
                  </label>
                  <div className="mt-1">
                    <input
                      id="phoneNumber"
                      name="phoneNumber"
                      type="tel"
                      autoComplete="tel"
                      required
                      value={phoneNumber}
                      onChange={(e) => setPhoneNumber(e.target.value)}
                      className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                      placeholder="(*************"
                    />
                  </div>
                </div>
              )}

              <div>
                <p className="text-sm text-gray-500 mb-4">
                  We'll send you a secure login link to access your account. No password needed.
                </p>
                <button
                  type="submit"
                  disabled={loading}
                  className={`w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 ${
                    loading ? 'opacity-70 cursor-not-allowed' : ''
                  }`}
                >
                  {loading ? 'Sending login link...' : 'Send login link'}
                </button>
              </div>
            </form>
          )}

          <div className="mt-6">
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-gray-300" />
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="px-2 bg-white text-gray-500">
                  {success ? 'Need help?' : 'New customer?'}
                </span>
              </div>
            </div>

            <div className="mt-6">
              {success ? (
                <button
                  onClick={() => setSuccess(false)}
                  className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-primary-600 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                >
                  Try again
                </button>
              ) : (
                <Link
                  to={(!urlFarmId && getSubdomain()) ? "/customer/register" : (actualFarmId ? `/customer/register/${actualFarmId}` : "/customer/register")}
                  className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-primary-600 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                >
                  Create an account
                </Link>
              )}
            </div>

            {!success && (
              <p className="mt-4 text-center text-xs text-gray-500">
                No password needed. We'll send you a secure login link.
              </p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default CustomerLogin;
