import { useState, useEffect, useContext } from 'react';
import { use<PERSON>ara<PERSON>, Link } from 'react-router-dom';
import axios from 'axios';
import { AuthContext } from '../../context/AuthContext';
import Layout from '../../components/Layout';
import { 
  ChartBarIcon, 
  MapIcon, 
  TruckIcon, 
  CubeIcon, 
  CurrencyDollarIcon, 
  UserGroupIcon 
} from '@heroicons/react/24/outline';

// Mock data for charts
const mockChartData = {
  'farm-performance': {
    title: 'Farm Performance',
    description: 'Overall farm performance metrics including revenue, expenses, and profitability.',
    metrics: [
      { name: 'Total Revenue', value: '$125,000', change: '+12%', trend: 'up' },
      { name: 'Total Expenses', value: '$78,500', change: '+8%', trend: 'up' },
      { name: 'Net Profit', value: '$46,500', change: '+18%', trend: 'up' },
      { name: 'Profit Margin', value: '37.2%', change: '+2.5%', trend: 'up' }
    ],
    chartDescription: 'Revenue and expenses by month for the current year.',
    tableData: [
      { period: 'January', revenue: '$9,200', expenses: '$5,800', profit: '$3,400' },
      { period: 'February', revenue: '$8,500', expenses: '$5,200', profit: '$3,300' },
      { period: 'March', revenue: '$10,300', expenses: '$6,100', profit: '$4,200' },
      { period: 'April', revenue: '$11,500', expenses: '$7,200', profit: '$4,300' },
      { period: 'May', revenue: '$12,800', expenses: '$7,800', profit: '$5,000' },
    ]
  },
  'field-production': {
    title: 'Field Production',
    description: 'Yields and production metrics by field, crop type, and season.',
    metrics: [
      { name: 'Total Yield', value: '1,250 tons', change: '+5%', trend: 'up' },
      { name: 'Yield per Acre', value: '4.2 tons', change: '+3%', trend: 'up' },
      { name: 'Planted Area', value: '298 acres', change: '+2%', trend: 'up' },
      { name: 'Harvest Efficiency', value: '92%', change: '+1%', trend: 'up' }
    ],
    chartDescription: 'Yield comparison by field for the current season.',
    tableData: [
      { field: 'North Field', crop: 'Corn', area: '45 acres', yield: '198 tons', yieldPerAcre: '4.4 tons' },
      { field: 'South Field', crop: 'Soybeans', area: '38 acres', yield: '152 tons', yieldPerAcre: '4.0 tons' },
      { field: 'East Field', crop: 'Wheat', area: '42 acres', yield: '168 tons', yieldPerAcre: '4.0 tons' },
      { field: 'West Field', crop: 'Corn', area: '40 acres', yield: '180 tons', yieldPerAcre: '4.5 tons' },
      { field: 'Central Field', crop: 'Alfalfa', area: '35 acres', yield: '140 tons', yieldPerAcre: '4.0 tons' },
    ]
  },
  'equipment-utilization': {
    title: 'Equipment Utilization',
    description: 'Usage and efficiency metrics for farm equipment.',
    metrics: [
      { name: 'Total Usage', value: '1,850 hours', change: '+8%', trend: 'up' },
      { name: 'Maintenance Cost', value: '$12,500', change: '-5%', trend: 'down' },
      { name: 'Fuel Consumption', value: '4,200 gal', change: '+3%', trend: 'up' },
      { name: 'Downtime', value: '48 hours', change: '-15%', trend: 'down' }
    ],
    chartDescription: 'Equipment usage hours by type for the current year.',
    tableData: [
      { equipment: 'Tractor A', type: 'Tractor', hours: '420', fuelUsed: '980 gal', maintenanceCost: '$2,800' },
      { equipment: 'Harvester B', type: 'Harvester', hours: '280', fuelUsed: '850 gal', maintenanceCost: '$3,200' },
      { equipment: 'Tractor C', type: 'Tractor', hours: '390', fuelUsed: '920 gal', maintenanceCost: '$2,100' },
      { equipment: 'Sprayer D', type: 'Sprayer', hours: '210', fuelUsed: '480 gal', maintenanceCost: '$1,500' },
      { equipment: 'Plow E', type: 'Plow', hours: '180', fuelUsed: '420 gal', maintenanceCost: '$900' },
    ]
  },
  'inventory-status': {
    title: 'Inventory Status',
    description: 'Current inventory levels, valuation, and reorder alerts.',
    metrics: [
      { name: 'Total Items', value: '128', change: '+12', trend: 'up' },
      { name: 'Total Value', value: '$85,200', change: '+15%', trend: 'up' },
      { name: 'Low Stock Items', value: '8', change: '-2', trend: 'down' },
      { name: 'Expired Items', value: '3', change: '-1', trend: 'down' }
    ],
    chartDescription: 'Inventory value by category.',
    tableData: [
      { item: 'Corn Seed', category: 'Seed', quantity: '250 bags', value: '$12,500', status: 'Adequate' },
      { item: 'Fertilizer A', category: 'Fertilizer', quantity: '180 bags', value: '$9,000', status: 'Low Stock' },
      { item: 'Herbicide B', category: 'Herbicide', quantity: '85 gal', value: '$8,500', status: 'Adequate' },
      { item: 'Diesel Fuel', category: 'Fuel', quantity: '1,200 gal', value: '$4,800', status: 'Adequate' },
      { item: 'Animal Feed', category: 'Feed', quantity: '320 bags', value: '$6,400', status: 'Low Stock' },
    ]
  },
  'financial-summary': {
    title: 'Financial Summary',
    description: 'Income, expenses, and profitability by time period and category.',
    metrics: [
      { name: 'Total Revenue', value: '$125,000', change: '+12%', trend: 'up' },
      { name: 'Total Expenses', value: '$78,500', change: '+8%', trend: 'up' },
      { name: 'Net Profit', value: '$46,500', change: '+18%', trend: 'up' },
      { name: 'Cash on Hand', value: '$32,800', change: '+22%', trend: 'up' }
    ],
    chartDescription: 'Revenue and expenses by category.',
    tableData: [
      { category: 'Crop Sales', revenue: '$85,000', expenses: '$45,000', profit: '$40,000' },
      { category: 'Livestock', revenue: '$25,000', expenses: '$18,000', profit: '$7,000' },
      { category: 'Equipment Rental', revenue: '$8,000', expenses: '$2,500', profit: '$5,500' },
      { category: 'Government Subsidies', revenue: '$7,000', expenses: '$0', profit: '$7,000' },
      { category: 'Other', revenue: '$0', expenses: '$13,000', profit: '-$13,000' },
    ]
  },
  'employee-productivity': {
    title: 'Employee Productivity',
    description: 'Work hours, task completion, and productivity metrics by employee.',
    metrics: [
      { name: 'Total Hours', value: '2,850 hours', change: '+5%', trend: 'up' },
      { name: 'Tasks Completed', value: '385', change: '+12%', trend: 'up' },
      { name: 'Avg. Completion Time', value: '3.2 hours', change: '-8%', trend: 'down' },
      { name: 'Overtime Hours', value: '120 hours', change: '-15%', trend: 'down' }
    ],
    chartDescription: 'Hours worked and tasks completed by employee.',
    tableData: [
      { employee: 'John Smith', role: 'Manager', hoursWorked: '180', tasksCompleted: '45', efficiency: '98%' },
      { employee: 'Jane Doe', role: 'Field Worker', hoursWorked: '165', tasksCompleted: '38', efficiency: '92%' },
      { employee: 'Bob Johnson', role: 'Equipment Operator', hoursWorked: '175', tasksCompleted: '42', efficiency: '95%' },
      { employee: 'Alice Brown', role: 'Administrative', hoursWorked: '160', tasksCompleted: '52', efficiency: '97%' },
      { employee: 'Charlie Davis', role: 'Field Worker', hoursWorked: '170', tasksCompleted: '40', efficiency: '94%' },
    ]
  }
};

// Get icon for report type
const getReportIcon = (reportId: string) => {
  switch (reportId) {
    case 'farm-performance':
      return <ChartBarIcon className="h-8 w-8 text-primary-500" />;
    case 'field-production':
      return <MapIcon className="h-8 w-8 text-green-500" />;
    case 'equipment-utilization':
      return <TruckIcon className="h-8 w-8 text-blue-500" />;
    case 'inventory-status':
      return <CubeIcon className="h-8 w-8 text-yellow-500" />;
    case 'financial-summary':
      return <CurrencyDollarIcon className="h-8 w-8 text-red-500" />;
    case 'employee-productivity':
      return <UserGroupIcon className="h-8 w-8 text-purple-500" />;
    default:
      return <ChartBarIcon className="h-8 w-8 text-gray-500" />;
  }
};

// Get trend icon and color
const getTrendIndicator = (trend: string) => {
  if (trend === 'up') {
    return (
      <span className="inline-flex items-center text-green-600">
        <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 15l7-7 7 7"></path>
        </svg>
      </span>
    );
  } else {
    return (
      <span className="inline-flex items-center text-red-600">
        <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7"></path>
        </svg>
      </span>
    );
  }
};

const ReportDetail = () => {
  const { reportId } = useParams<{ reportId: string }>();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { user, token } = useContext(AuthContext);

  // State for API data
  const [reportData, setReportData] = useState<any>(null);
  const [apiError, setApiError] = useState<string | null>(null);

  // Fetch report data from API
  useEffect(() => {
    const fetchReportData = async () => {
      if (!reportId || !user) return;
      // Don't check for token here, as it might be available after the component mounts

      try {
        setLoading(true);
        setError(null);
        setApiError(null);

        // Make sure we have a token before making the API call
        if (!token) {
          throw new Error('Authentication token not available');
        }

        // Try to get report data from the API
        const response = await axios.get(`${import.meta.env.VITE_API_URL}/reports/type/${reportId}`, {
          headers: { Authorization: `Bearer ${token}` },
          params: { farmId: (user as any).currentFarmId }
        });

        if (response.data && response.data.success && response.data.data) {
          setReportData(response.data.data);
        } else {
          throw new Error('Invalid response format from server');
        }
      } catch (err: any) {
        console.error('Error fetching report data:', err);
        setApiError(err.response?.data?.error || 'Failed to load report data');

        // Fallback to mock data if API call fails
        if (reportId && mockChartData[reportId as keyof typeof mockChartData]) {
          console.log('Falling back to mock data');
          setReportData(mockChartData[reportId as keyof typeof mockChartData]);
        }
      } finally {
        setLoading(false);
      }
    };

    fetchReportData();
  }, [reportId, user, token]);

  // Show loading state
  if (loading) {
    return (
      <Layout>
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500"></div>
          <span className="ml-3 text-gray-600">Loading report data...</span>
        </div>
      </Layout>
    );
  }

  // Show error state
  if (error || (apiError && !reportData)) {
    return (
      <Layout>
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">{error || apiError || 'An error occurred while loading the report.'}</span>
        </div>
        <Link
          to="/reports"
          className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
        >
          Back to Reports
        </Link>
      </Layout>
    );
  }

  // Show not found state
  if (!reportId || !reportData) {
    return (
      <Layout>
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">Report not found or invalid report ID.</span>
        </div>
        <Link
          to="/reports"
          className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
        >
          Back to Reports
        </Link>
      </Layout>
    );
  }

  return (
    <Layout>
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center">
          <div className="mr-4">
            {getReportIcon(reportId)}
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">{reportData.title}</h1>
            <p className="text-sm text-gray-500">{reportData.description}</p>
          </div>
        </div>
        <Link
          to="/reports"
          className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
        >
          Back to Reports
        </Link>
      </div>

      {/* Key Metrics */}
      <div className="mb-6 grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        {reportData.metrics.map((metric, index) => (
          <div key={index} className="bg-white overflow-hidden shadow rounded-lg">
            <div className="px-4 py-5 sm:p-6">
              <dt className="text-sm font-medium text-gray-500 truncate">{metric.name}</dt>
              <dd className="mt-1 text-3xl font-semibold text-gray-900">{metric.value}</dd>
              <dd className="mt-2 flex items-center text-sm font-medium">
                {getTrendIndicator(metric.trend)}
                <span className={metric.trend === 'up' ? 'text-green-600' : 'text-red-600'}>
                  {metric.change}
                </span>
                <span className="text-gray-500 ml-1">from previous period</span>
              </dd>
            </div>
          </div>
        ))}
      </div>

      {/* Chart Placeholder */}
      <div className="mb-6 bg-white shadow rounded-lg p-6">
        <h2 className="text-lg font-medium text-gray-900 mb-4">Chart Visualization</h2>
        <p className="text-sm text-gray-500 mb-4">{reportData.chartDescription}</p>
        <div className="bg-gray-100 rounded-lg p-6 flex items-center justify-center" style={{ height: '300px' }}>
          <div className="text-center">
            <ChartBarIcon className="h-12 w-12 text-gray-400 mx-auto" />
            <p className="mt-2 text-sm text-gray-500">Chart visualization would appear here.</p>
            <p className="text-xs text-gray-400">This is a placeholder for actual chart implementation.</p>
          </div>
        </div>
      </div>

      {/* Data Table */}
      <div className="bg-white shadow rounded-lg overflow-hidden">
        <div className="px-4 py-5 sm:px-6">
          <h2 className="text-lg font-medium text-gray-900">Detailed Data</h2>
        </div>
        <div className="border-t border-gray-200">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  {Object.keys(reportData.tableData[0]).map((key, index) => (
                    <th
                      key={index}
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      {key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {reportData.tableData.map((row, rowIndex) => (
                  <tr key={rowIndex}>
                    {Object.values(row).map((value, cellIndex) => (
                      <td key={cellIndex} className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {typeof value === 'object' 
                          ? JSON.stringify(value) 
                          : value !== null && value !== undefined 
                            ? String(value) 
                            : ''}
                      </td>
                    ))}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {/* Export Options */}
      <div className="mt-6 flex justify-end space-x-3">
        <button
          type="button"
          className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
        >
          Export PDF
        </button>
        <button
          type="button"
          className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
        >
          Export CSV
        </button>
        <button
          type="button"
          className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
        >
          Print Report
        </button>
      </div>
    </Layout>
  );
};

export default ReportDetail;
