import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useFarm } from '../../context/FarmContext';
import { toast } from 'react-hot-toast';
import { 
  generateEnvironmentalImpactReport, 
  getEnvironmentalImpactReports, 
  deleteEnvironmentalImpactReport,
  updateEnvironmentalImpactReport
} from '../../services/sustainabilityService';
import Layout from '../../components/Layout';
import { format } from 'date-fns';

const EnvironmentalImpactReports: React.FC = () => {
  const navigate = useNavigate();
  const { currentFarm } = useFarm();
  const [isLoading, setIsLoading] = useState(false);
  const [reports, setReports] = useState<any[]>([]);
  const [editingId, setEditingId] = useState<number | null>(null);
  const [formData, setFormData] = useState({
    reportDate: format(new Date(), 'yyyy-MM-dd'),
    reportType: 'annual',
    metrics: {
      waterUsage: 0,
      soilHealth: 0,
      biodiversity: 0,
      wasteReduction: 0,
      energyEfficiency: 0
    },
    summary: '',
    recommendations: ''
  });

  useEffect(() => {
    if (currentFarm?.id) {
      fetchReports();
    }
  }, [currentFarm]);

  const fetchReports = async () => {
    try {
      setIsLoading(true);
      const data = await getEnvironmentalImpactReports(Number(currentFarm!.id));
      setReports(data);
    } catch (error) {
      console.error('Error fetching environmental impact reports:', error);
      toast.error('Failed to fetch environmental impact reports');
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleMetricChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      metrics: {
        ...prev.metrics,
        [name]: parseFloat(value) || 0
      }
    }));
  };

  const handleEdit = (report: any) => {
    setEditingId(report.id);
    setFormData({
      reportDate: format(new Date(report.report_date), 'yyyy-MM-dd'),
      reportType: report.report_type,
      metrics: report.metrics,
      summary: report.summary,
      recommendations: report.recommendations
    });
  };

  const handleCancelEdit = () => {
    setEditingId(null);
    setFormData({
      reportDate: format(new Date(), 'yyyy-MM-dd'),
      reportType: 'annual',
      metrics: {
        waterUsage: 0,
        soilHealth: 0,
        biodiversity: 0,
        wasteReduction: 0,
        energyEfficiency: 0
      },
      summary: '',
      recommendations: ''
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!currentFarm?.id) {
      toast.error('Please select a farm first');
      return;
    }

    const reportData = {
      farmId: Number(currentFarm.id),
      ...formData
    };

    try {
      setIsLoading(true);

      if (editingId) {
        // Update existing report
        await updateEnvironmentalImpactReport(editingId, reportData);
        toast.success('Environmental impact report updated successfully');
        setEditingId(null);
      } else {
        // Generate new report
        await generateEnvironmentalImpactReport(reportData);
        toast.success('Environmental impact report generated successfully');
      }

      setFormData({
        reportDate: format(new Date(), 'yyyy-MM-dd'),
        reportType: 'annual',
        metrics: {
          waterUsage: 0,
          soilHealth: 0,
          biodiversity: 0,
          wasteReduction: 0,
          energyEfficiency: 0
        },
        summary: '',
        recommendations: ''
      });
      fetchReports();
    } catch (error) {
      console.error('Error with environmental impact report:', error);
      toast.error(editingId ? 'Failed to update environmental impact report' : 'Failed to generate environmental impact report');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = async (id: number) => {
    if (window.confirm('Are you sure you want to delete this environmental impact report?')) {
      try {
        setIsLoading(true);
        await deleteEnvironmentalImpactReport(id);
        toast.success('Environmental impact report deleted successfully');
        fetchReports();
      } catch (error) {
        console.error('Error deleting environmental impact report:', error);
        toast.error('Failed to delete environmental impact report');
      } finally {
        setIsLoading(false);
      }
    }
  };

  const getReportTypeLabel = (reportType: string) => {
    const types: Record<string, string> = {
      'annual': 'Annual Report',
      'quarterly': 'Quarterly Report',
      'monthly': 'Monthly Report',
      'special': 'Special Assessment'
    };
    return types[reportType] || reportType;
  };

  const getMetricLabel = (metric: string) => {
    const metrics: Record<string, string> = {
      'waterUsage': 'Water Usage Efficiency',
      'soilHealth': 'Soil Health Index',
      'biodiversity': 'Biodiversity Score',
      'wasteReduction': 'Waste Reduction Rate',
      'energyEfficiency': 'Energy Efficiency Rating'
    };
    return metrics[metric] || metric;
  };

  const getMetricDescription = (metric: string) => {
    const descriptions: Record<string, string> = {
      'waterUsage': 'Measures water conservation practices (0-100)',
      'soilHealth': 'Indicates soil quality and fertility (0-100)',
      'biodiversity': 'Reflects species diversity on the farm (0-100)',
      'wasteReduction': 'Measures waste management effectiveness (0-100)',
      'energyEfficiency': 'Rates energy usage optimization (0-100)'
    };
    return descriptions[metric] || '';
  };

  const getAverageScore = (metrics: any): string => {
    const values = Object.values(metrics) as number[];
    const sum = values.reduce((acc, val) => acc + val, 0);
    return (sum / values.length).toFixed(1);
  };

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-blue-600';
    if (score >= 40) return 'text-yellow-600';
    return 'text-red-600';
  };

  return (
    <Layout>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Environmental Impact Reports</h1>
        <button
          onClick={() => navigate('/sustainability')}
          className="px-4 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300"
        >
          Back to Dashboard
        </button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white shadow rounded-lg p-6">
          <h2 className="text-lg font-medium text-gray-900 mb-4">
            {editingId ? 'Edit Environmental Impact Report' : 'Generate Environmental Impact Report'}
          </h2>
          <form onSubmit={handleSubmit}>
            <div className="mb-4">
              <label htmlFor="reportDate" className="block text-sm font-medium text-gray-700 mb-1">
                Report Date
              </label>
              <input
                type="date"
                id="reportDate"
                name="reportDate"
                value={formData.reportDate}
                onChange={handleInputChange}
                className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary-500"
                required
              />
            </div>

            <div className="mb-4">
              <label htmlFor="reportType" className="block text-sm font-medium text-gray-700 mb-1">
                Report Type
              </label>
              <select
                id="reportType"
                name="reportType"
                value={formData.reportType}
                onChange={handleInputChange}
                className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary-500"
                required
              >
                <option value="annual">Annual Report</option>
                <option value="quarterly">Quarterly Report</option>
                <option value="monthly">Monthly Report</option>
                <option value="special">Special Assessment</option>
              </select>
            </div>

            <div className="mb-6">
              <h3 className="text-md font-medium text-gray-700 mb-3">Environmental Metrics</h3>

              {Object.keys(formData.metrics).map((metric) => (
                <div key={metric} className="mb-4">
                  <label htmlFor={metric} className="block text-sm font-medium text-gray-700 mb-1">
                    {getMetricLabel(metric)}
                  </label>
                  <div className="flex items-center">
                    <input
                      type="range"
                      id={metric}
                      name={metric}
                      min="0"
                      max="100"
                      step="1"
                      value={formData.metrics[metric as keyof typeof formData.metrics]}
                      onChange={handleMetricChange}
                      className="w-full mr-3"
                    />
                    <span className="text-sm font-medium w-8">
                      {formData.metrics[metric as keyof typeof formData.metrics]}
                    </span>
                  </div>
                  <p className="text-xs text-gray-500 mt-1">{getMetricDescription(metric)}</p>
                </div>
              ))}
            </div>

            <div className="mb-4">
              <label htmlFor="summary" className="block text-sm font-medium text-gray-700 mb-1">
                Summary
              </label>
              <textarea
                id="summary"
                name="summary"
                value={formData.summary}
                onChange={handleInputChange}
                rows={4}
                className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary-500"
                required
                placeholder="Provide a summary of the environmental impact assessment..."
              />
            </div>

            <div className="mb-4">
              <label htmlFor="recommendations" className="block text-sm font-medium text-gray-700 mb-1">
                Recommendations
              </label>
              <textarea
                id="recommendations"
                name="recommendations"
                value={formData.recommendations}
                onChange={handleInputChange}
                rows={4}
                className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary-500"
                required
                placeholder="Provide recommendations for improving environmental sustainability..."
              />
            </div>

            <div className="flex space-x-2">
              <button
                type="submit"
                disabled={isLoading}
                className="flex-1 px-4 py-2 bg-primary-600 text-white rounded hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 disabled:bg-gray-400"
              >
                {isLoading 
                  ? (editingId ? 'Updating...' : 'Generating...') 
                  : (editingId ? 'Update Report' : 'Generate Report')
                }
              </button>
              {editingId && (
                <button
                  type="button"
                  onClick={handleCancelEdit}
                  className="px-4 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500"
                >
                  Cancel
                </button>
              )}
            </div>
          </form>
        </div>

        <div className="bg-white shadow rounded-lg p-6">
          <h2 className="text-lg font-medium text-gray-900 mb-4">Environmental Impact Reports</h2>
          {isLoading && <p className="text-gray-500">Loading...</p>}

          {!isLoading && reports.length === 0 && (
            <p className="text-gray-500">No environmental impact reports found.</p>
          )}

          {!isLoading && reports.length > 0 && (
            <div className="space-y-6">
              {reports.map((report) => (
                <div key={report.id} className="border rounded-lg p-4">
                  <div className="flex justify-between items-start">
                    <div>
                      <h3 className="text-lg font-medium text-gray-900">
                        {getReportTypeLabel(report.report_type)}
                      </h3>
                      <p className="text-sm text-gray-500 mb-2">
                        Date: {new Date(report.report_date).toLocaleDateString()}
                      </p>
                      <div className="flex items-center mb-2">
                        <span className="text-sm font-medium text-gray-700 mr-2">Overall Score:</span>
                        <span className={`text-lg font-bold ${getScoreColor(parseFloat(getAverageScore(report.metrics)))}`}>
                          {getAverageScore(report.metrics)}/100
                        </span>
                      </div>
                    </div>
                    <div className="flex space-x-3">
                      <button
                        onClick={() => handleEdit(report)}
                        className="text-blue-600 hover:text-blue-900"
                      >
                        Edit
                      </button>
                      <button
                        onClick={() => handleDelete(report.id)}
                        className="text-red-600 hover:text-red-900"
                      >
                        Delete
                      </button>
                    </div>
                  </div>

                  <div className="mt-3 grid grid-cols-2 gap-2 mb-4">
                    {Object.entries(report.metrics).map(([key, value]) => (
                      <div key={key} className="bg-gray-50 p-2 rounded">
                        <p className="text-xs font-medium text-gray-700">{getMetricLabel(key)}</p>
                        <div className="w-full bg-gray-200 rounded-full h-2.5 mt-1">
                          <div 
                            className="bg-primary-600 h-2.5 rounded-full" 
                            style={{ width: `${value}%` }}
                          ></div>
                        </div>
                        <p className="text-xs text-right mt-1">{value as number}/100</p>
                      </div>
                    ))}
                  </div>

                  <div className="mt-3">
                    <h4 className="text-sm font-medium text-gray-700 mb-1">Summary</h4>
                    <p className="text-sm text-gray-600 mb-3">{report.summary}</p>

                    <h4 className="text-sm font-medium text-gray-700 mb-1">Recommendations</h4>
                    <p className="text-sm text-gray-600">{report.recommendations}</p>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </Layout>
  );
};

export default EnvironmentalImpactReports;
