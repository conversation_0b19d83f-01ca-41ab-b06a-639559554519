import { useState, useEffect, useContext } from 'react';
import { Link } from 'react-router-dom';
import { useFarm } from '../../context/FarmContext';
import { AuthContext } from '../../context/AuthContext';
import Layout from '../../components/Layout';
import { Vet, getFarmVets, searchVets, deleteVet } from '../../services/vetService';

const VetList = () => {
  const [farmVets, setFarmVets] = useState<Vet[]>([]);
  const [globalVets, setGlobalVets] = useState<Vet[]>([]);
  const [searchResults, setSearchResults] = useState<Vet[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const { user } = useContext(AuthContext);
  const { selectedFarm } = useFarm();
  const isAdmin = false; // Global admins should not have special privileges on the frontend

  // Fetch vets for the selected farm
  useEffect(() => {
    const fetchVets = async () => {
      if (!selectedFarm) return;

      setLoading(true);
      setError(null);
      setIsSearching(false);
      setSearchQuery('');

      try {
        const response = await getFarmVets(selectedFarm.id);
        setFarmVets(response.farmVets || []);
        setGlobalVets(response.globalVets || []);
      } catch (err: any) {
        console.error('Error fetching vets:', err);
        setError('Failed to load vets. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchVets();
  }, [selectedFarm]);

  // Handle vet deletion
  const handleDeleteVet = async (vetId: string, isGlobal: boolean) => {
    // Only admins can delete global vets
    if (isGlobal && !isAdmin) {
      alert('Only administrators can delete global vets.');
      return;
    }

    if (!window.confirm('Are you sure you want to delete this vet?')) {
      return;
    }

    try {
      await deleteVet(vetId);

      if (isSearching) {
        setSearchResults(searchResults.filter(vet => vet.id !== vetId));
      } else {
        if (isGlobal) {
          setGlobalVets(globalVets.filter(vet => vet.id !== vetId));
        } else {
          setFarmVets(farmVets.filter(vet => vet.id !== vetId));
        }
      }
    } catch (err: any) {
      console.error('Error deleting vet:', err);
      setError('Failed to delete vet. Please try again later.');
    }
  };

  // Handle search
  const handleSearch = async () => {
    if (!searchQuery.trim()) {
      setIsSearching(false);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const results = await searchVets(searchQuery, selectedFarm?.id);
      setSearchResults(results);
      setIsSearching(true);
    } catch (err: any) {
      console.error('Error searching vets:', err);
      setError('Failed to search vets. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  // Clear search
  const clearSearch = () => {
    setSearchQuery('');
    setIsSearching(false);
  };

  // Render vet list item
  const renderVetItem = (vet: Vet, isGlobal: boolean) => (
    <li key={vet.id} className="border-b border-gray-200 last:border-b-0">
      <div className="px-4 py-4 sm:px-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <div className="ml-3">
              <p className="text-sm font-medium text-primary-600 truncate">
                {vet.name}
              </p>
              <p className="text-sm text-gray-500">
                {vet.specialization && <span className="mr-2">Specialization: {vet.specialization}</span>}
                {vet.phone && <span className="mr-2">Phone: {vet.phone}</span>}
                {vet.email && <span>Email: {vet.email}</span>}
              </p>
              {(vet.city || vet.state) && (
                <p className="text-sm text-gray-500">
                  Location: {[vet.city, vet.state].filter(Boolean).join(', ')}
                </p>
              )}
            </div>
          </div>
          <div className="flex space-x-2">
            <Link
              to={`/vets/${vet.id}`}
              className="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              View
            </Link>
            {(!isGlobal || isAdmin) && (
              <Link
                to={`/vets/${vet.id}/edit`}
                className="inline-flex items-center px-3 py-1 border border-gray-300 text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                Edit
              </Link>
            )}
            {(!isGlobal || isAdmin) && (
              <button
                onClick={() => handleDeleteVet(vet.id, isGlobal)}
                className="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
              >
                Delete
              </button>
            )}
          </div>
        </div>
        {isGlobal && (
          <div className="mt-2">
            <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
              Global Database
            </span>
          </div>
        )}
        {vet.notes && (
          <div className="mt-2 text-sm text-gray-500">
            <p>{vet.notes}</p>
          </div>
        )}
      </div>
    </li>
  );

  return (
    <Layout>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Veterinary Management</h1>
        <Link
          to="/vets/new"
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
        >
          Add Vet
        </Link>
      </div>

      {/* Display selected farm */}
      <div className="mb-6">
        <p className="text-sm text-gray-500">
          Showing vets for farm: <span className="font-medium text-gray-700">{selectedFarm ? selectedFarm.name : 'No farm selected'}</span>
        </p>
        {!selectedFarm && (
          <p className="text-sm text-red-500 mt-1">
            Please select a farm from the header dropdown to view and manage vets.
          </p>
        )}
      </div>

      {/* Search box */}
      <div className="mb-6">
        <div className="flex space-x-2">
          <div className="flex-grow">
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder="Search vets by name, specialization, or location"
              className="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 rounded-md"
              onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
            />
          </div>
          <button
            onClick={handleSearch}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Search
          </button>
          {isSearching && (
            <button
              onClick={clearSearch}
              className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              Clear
            </button>
          )}
        </div>
        <p className="text-xs text-gray-500 mt-1">
          Search the global database of vets or add your own custom vets to your farm.
        </p>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">{error}</span>
        </div>
      )}

      {loading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
          <p className="ml-3 text-gray-500">Loading vets...</p>
        </div>
      ) : isSearching ? (
        // Search results
        <div>
          <h2 className="text-lg font-medium text-gray-900 mb-4">Search Results</h2>
          {searchResults.length === 0 ? (
            <div className="bg-white shadow rounded-lg p-6 text-center">
              <p className="text-gray-500">No vets found matching your search criteria.</p>
            </div>
          ) : (
            <div className="bg-white shadow overflow-hidden sm:rounded-md">
              <ul className="divide-y divide-gray-200">
                {searchResults.map(vet => renderVetItem(vet, vet.is_global))}
              </ul>
            </div>
          )}
        </div>
      ) : (
        // Regular view (farm vets and global vets)
        <div>
          {/* Farm-specific vets */}
          <h2 className="text-lg font-medium text-gray-900 mb-4">Farm Vets</h2>
          {farmVets.length === 0 ? (
            <div className="bg-white shadow rounded-lg p-6 text-center mb-8">
              <p className="text-gray-500 mb-4">No farm-specific vets found.</p>
              <p className="text-sm text-gray-400 mb-6">
                Add your own vets to keep track of veterinary professionals who work with your farm.
              </p>
              <Link
                to="/vets/new"
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                Add Farm Vet
              </Link>
            </div>
          ) : (
            <div className="bg-white shadow overflow-hidden sm:rounded-md mb-8">
              <ul className="divide-y divide-gray-200">
                {farmVets.map(vet => renderVetItem(vet, false))}
              </ul>
            </div>
          )}

          {/* Global vets */}
          <h2 className="text-lg font-medium text-gray-900 mb-4">Global Vet Database</h2>
          {globalVets.length === 0 ? (
            <div className="bg-white shadow rounded-lg p-6 text-center">
              <p className="text-gray-500">No global vets available.</p>
              {isAdmin && (
                <div className="mt-4">
                  <p className="text-sm text-gray-400 mb-4">
                    As an administrator, you can add vets to the global database.
                  </p>
                  <Link
                    to="/vets/new"
                    className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                  >
                    Add Global Vet
                  </Link>
                </div>
              )}
            </div>
          ) : (
            <div className="bg-white shadow overflow-hidden sm:rounded-md">
              <ul className="divide-y divide-gray-200">
                {globalVets.map(vet => renderVetItem(vet, true))}
              </ul>
            </div>
          )}
        </div>
      )}
    </Layout>
  );
};

export default VetList;
