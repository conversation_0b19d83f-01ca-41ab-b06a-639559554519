import React, { useState, useEffect } from 'react';
import { useFarm } from '../../context/FarmContext';
import { Card, CardContent, CardHeader } from '../../components/ui/Card';
import { Button } from '../../components/ui/Button';
import { Heading } from '../../components/ui/Heading';
import { Text } from '../../components/ui/Text';
import { Spinner } from '../../components/ui/Spinner';
import { Alert, AlertDescription, AlertTitle } from '../../components/ui/Alert';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../../components/ui/Table';
import { Badge } from '../../components/ui/Badge';
import { Scales } from 'phosphor-react';

const MarketContracts: React.FC = () => {
  const { currentFarm } = useFarm();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Simulated data - in a real implementation, this would be fetched from the API
  const [contracts, setContracts] = useState<any[]>([]);

  useEffect(() => {
    const fetchData = async () => {
      if (!currentFarm) return;

      setLoading(true);
      setError(null);

      try {
        // In a real implementation, this would be an actual API call
        // For now, we'll just simulate some data
        
        // Simulate API call delay
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Set simulated data
        setContracts([
          {
            id: '1',
            title: 'Corn Sale Contract',
            contract_type: 'sale',
            status: 'active',
            supplier: 'Midwest Grain Co.',
            start_date: '2023-05-15',
            end_date: '2023-12-31',
            value: 25000,
            currency: 'USD'
          },
          {
            id: '2',
            title: 'Soybean Purchase Agreement',
            contract_type: 'purchase',
            status: 'pending',
            supplier: 'AgriSeeds Inc.',
            start_date: '2023-06-01',
            end_date: '2023-08-31',
            value: 12500,
            currency: 'USD'
          },
          {
            id: '3',
            title: 'Equipment Rental Contract',
            contract_type: 'service',
            status: 'active',
            supplier: 'Farm Equipment Rentals',
            start_date: '2023-04-01',
            end_date: '2023-10-31',
            value: 8750,
            currency: 'USD'
          },
          {
            id: '4',
            title: 'Wheat Sale Contract',
            contract_type: 'sale',
            status: 'completed',
            supplier: 'Global Grain Traders',
            start_date: '2023-01-15',
            end_date: '2023-03-31',
            value: 18500,
            currency: 'USD'
          },
          {
            id: '5',
            title: 'Fertilizer Purchase Agreement',
            contract_type: 'purchase',
            status: 'active',
            supplier: 'AgriChem Solutions',
            start_date: '2023-03-01',
            end_date: '2023-09-30',
            value: 7200,
            currency: 'USD'
          }
        ]);
      } catch (err) {
        console.error('Error fetching contracts:', err);
        setError('Failed to load contracts. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [currentFarm]);

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge variant="success">Active</Badge>;
      case 'pending':
        return <Badge variant="warning">Pending</Badge>;
      case 'completed':
        return <Badge variant="default">Completed</Badge>;
      case 'cancelled':
        return <Badge variant="destructive">Cancelled</Badge>;
      default:
        return <Badge>{status}</Badge>;
    }
  };

  const getContractTypeBadge = (type: string) => {
    switch (type) {
      case 'sale':
        return <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">Sale</Badge>;
      case 'purchase':
        return <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">Purchase</Badge>;
      case 'service':
        return <Badge variant="outline" className="bg-purple-50 text-purple-700 border-purple-200">Service</Badge>;
      default:
        return <Badge variant="outline">{type}</Badge>;
    }
  };

  if (!currentFarm) {
    return (
      <Alert variant="warning">
        <AlertTitle>No Farm Selected</AlertTitle>
        <AlertDescription>
          Please select a farm to view contracts.
        </AlertDescription>
      </Alert>
    );
  }

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spinner size="lg" />
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-2">
          <Scales size={24} className="text-primary-500" />
          <Heading level={1}>Contracts</Heading>
        </div>
        <Button>New Contract</Button>
      </div>

      <Card>
        <CardHeader>
          <Heading level={2}>Contract Management</Heading>
          <Text>Manage your sales, purchase, and service contracts in one place.</Text>
        </CardHeader>
        <CardContent>
          {contracts.length === 0 ? (
            <div className="text-center py-8">
              <Text className="text-gray-500">No contracts found. Create your first contract to get started.</Text>
              <div className="mt-4">
                <Button>Create Contract</Button>
              </div>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Title</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Supplier</TableHead>
                  <TableHead>Start Date</TableHead>
                  <TableHead>End Date</TableHead>
                  <TableHead className="text-right">Value</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {contracts.map((contract) => (
                  <TableRow key={contract.id}>
                    <TableCell className="font-medium">{contract.title}</TableCell>
                    <TableCell>{getContractTypeBadge(contract.contract_type)}</TableCell>
                    <TableCell>{getStatusBadge(contract.status)}</TableCell>
                    <TableCell>{contract.supplier}</TableCell>
                    <TableCell>{new Date(contract.start_date).toLocaleDateString()}</TableCell>
                    <TableCell>{new Date(contract.end_date).toLocaleDateString()}</TableCell>
                    <TableCell className="text-right">${contract.value.toLocaleString()}</TableCell>
                    <TableCell className="text-right">
                      <Button variant="ghost" size="sm">View</Button>
                      <Button variant="ghost" size="sm">Edit</Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default MarketContracts;