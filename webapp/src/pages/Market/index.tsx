import { Routes, Route } from 'react-router-dom';
import Layout from '../../components/Layout';
import MarketDashboard from './MarketDashboard';
import MarketContracts from './MarketContracts';
import PriceComparison from './PriceComparison';
import MarketTrends from './MarketTrends';
import Marketplace from './Marketplace';
import NotFound from '../NotFound';

const MarketRoutes = () => {
  return (
    <Layout>
      <Routes>
        <Route index element={<MarketDashboard />} />
        <Route path="contracts" element={<MarketContracts />} />
        <Route path="price-comparison" element={<PriceComparison />} />
        <Route path="trends" element={<MarketTrends />} />
        <Route path="marketplace" element={<Marketplace />} />
        <Route path="*" element={<NotFound />} />
      </Routes>
    </Layout>
  );
};

export default MarketRoutes;