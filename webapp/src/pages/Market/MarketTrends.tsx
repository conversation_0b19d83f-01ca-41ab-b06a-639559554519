import React, { useState, useEffect } from 'react';
import { useFarm } from '../../context/FarmContext';
import { Card, CardContent, CardHeader } from '../../components/ui/Card';
import { Button } from '../../components/ui/Button';
import { Heading } from '../../components/ui/Heading';
import { Text } from '../../components/ui/Text';
import { Spinner } from '../../components/ui/Spinner';
import { Alert, AlertDescription, AlertTitle } from '../../components/ui/Alert';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../../components/ui/Table';
import { Badge } from '../../components/ui/Badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../components/ui/Select';
import { Input } from '../../components/ui/Input';
import { TrendUp, MagnifyingGlass, ArrowUp, ArrowDown, Minus } from 'phosphor-react';
import { 
  getMarketTrends, 
  getMarketTrendById, 
  getMarketTrendForecast, 
  getProductMarketTrends,
  MarketTrend
} from '../../services/marketTrendService';

// TODO: Replace with a real chart component using a charting library like recharts
const TrendChart: React.FC<{ data: any[] }> = ({ data }) => {
  return (
    <div className="h-64 bg-gray-50 rounded-md border border-gray-200 p-4 flex items-center justify-center">
      <Text className="text-gray-500">
        [Chart Placeholder] - In a real implementation, this would be a line chart showing price trends over time.
      </Text>
    </div>
  );
};

const MarketTrends: React.FC = () => {
  const { currentFarm } = useFarm();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedTimeRange, setSelectedTimeRange] = useState('30d');

  // State for market trend data
  const [marketTrends, setMarketTrends] = useState<MarketTrend[]>([]);
  const [categories, setCategories] = useState<string[]>([]);
  const [selectedTrend, setSelectedTrend] = useState<MarketTrend | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      if (!currentFarm) return;

      setLoading(true);
      setError(null);

      try {
        // Get start and end dates based on selected time range
        const endDate = new Date();
        let startDate = new Date();

        switch (selectedTimeRange) {
          case '7d':
            startDate.setDate(endDate.getDate() - 7);
            break;
          case '30d':
            startDate.setDate(endDate.getDate() - 30);
            break;
          case '90d':
            startDate.setDate(endDate.getDate() - 90);
            break;
          case '1y':
            startDate.setFullYear(endDate.getFullYear() - 1);
            break;
          default:
            startDate.setDate(endDate.getDate() - 30);
        }

        // Format dates for API
        const startDateStr = startDate.toISOString().split('T')[0];
        const endDateStr = endDate.toISOString().split('T')[0];

        // Fetch market trends from API
        const trends = await getMarketTrends(
          currentFarm.id,
          selectedCategory !== 'all' ? selectedCategory : undefined,
          startDateStr,
          endDateStr
        );

        if (trends.length > 0) {
          setMarketTrends(trends);
          setSelectedTrend(trends[0]); // Select the first trend by default

          // Extract unique categories
          const uniqueCategories = Array.from(new Set(trends.map(item => item.product_category)));
          setCategories(uniqueCategories);
        } else {
          // If no trends were returned, show an error
          setError('No market trends found for the selected criteria.');
        }
      } catch (err) {
        console.error('Error fetching market trends:', err);
        setError('Failed to load market trends. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [currentFarm, selectedCategory, selectedTimeRange]);

  // Filter market trends based on search term and selected category
  const filteredTrends = marketTrends.filter(trend => {
    const matchesSearch = searchTerm === '' || 
      trend.product_name.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesCategory = selectedCategory === 'all' || 
      trend.product_category === selectedCategory;

    return matchesSearch && matchesCategory;
  });

  const getTrendIcon = (change: number) => {
    if (change > 0) {
      return <ArrowUp size={16} className="text-green-600" />;
    } else if (change < 0) {
      return <ArrowDown size={16} className="text-red-600" />;
    } else {
      return <Minus size={16} className="text-gray-600" />;
    }
  };

  const getTrendBadge = (change: number) => {
    if (change > 0) {
      return <Badge variant="success">Up {change.toFixed(2)}%</Badge>;
    } else if (change < 0) {
      return <Badge variant="destructive">Down {Math.abs(change).toFixed(2)}%</Badge>;
    } else {
      return <Badge variant="outline">Stable</Badge>;
    }
  };

  const getForecastBadge = (forecast: any) => {
    if (forecast.trend === 'up') {
      return <Badge variant="success">Uptrend</Badge>;
    } else if (forecast.trend === 'down') {
      return <Badge variant="destructive">Downtrend</Badge>;
    } else {
      return <Badge variant="outline">Stable</Badge>;
    }
  };

  if (!currentFarm) {
    return (
      <Alert variant="warning">
        <AlertTitle>No Farm Selected</AlertTitle>
        <AlertDescription>
          Please select a farm to view market trends.
        </AlertDescription>
      </Alert>
    );
  }

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spinner size="lg" />
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-2">
          <TrendUp size={24} className="text-primary-500" />
          <Heading level={1}>Market Trends</Heading>
        </div>
        <Button>Add Custom Trend</Button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-1 space-y-6">
          <Card>
            <CardHeader>
              <Heading level={2}>Market Trend Analysis</Heading>
              <Text>Track price trends for agricultural products and commodities to make informed decisions.</Text>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="relative">
                  <MagnifyingGlass size={18} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500" />
                  <Input
                    placeholder="Search products..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
                <div>
                  <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Categories</SelectItem>
                      {categories.map(category => (
                        <SelectItem key={category} value={category}>{category}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Select value={selectedTimeRange} onValueChange={setSelectedTimeRange}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select time range" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="7d">Last 7 Days</SelectItem>
                      <SelectItem value="30d">Last 30 Days</SelectItem>
                      <SelectItem value="90d">Last 90 Days</SelectItem>
                      <SelectItem value="1y">Last Year</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="mt-6">
                <Heading level={3}>Trends</Heading>
                <div className="mt-2 space-y-2">
                  {filteredTrends.length === 0 ? (
                    <Text className="text-gray-500">No trends found matching your criteria.</Text>
                  ) : (
                    filteredTrends.map(trend => (
                      <div 
                        key={trend.id}
                        className={`p-3 rounded-md border cursor-pointer hover:bg-gray-50 ${selectedTrend?.id === trend.id ? 'border-primary-500 bg-primary-50' : 'border-gray-200'}`}
                        onClick={() => setSelectedTrend(trend)}
                      >
                        <div className="flex justify-between items-center">
                          <div>
                            <Text className="font-medium">{trend.product_name}</Text>
                            <Text className="text-sm text-gray-500">{trend.product_category}</Text>
                          </div>
                          <div className="flex items-center gap-1">
                            {getTrendIcon(trend.price_change_percentage)}
                            <Text className={`font-medium ${
                              trend.price_change_percentage > 0 ? 'text-green-600' : 
                              trend.price_change_percentage < 0 ? 'text-red-600' : 'text-gray-600'
                            }`}>
                              {trend.price_change_percentage > 0 ? '+' : ''}
                              {trend.price_change_percentage.toFixed(2)}%
                            </Text>
                          </div>
                        </div>
                        <div className="flex justify-between items-center mt-1">
                          <Text className="text-sm">${trend.price.toFixed(2)} / {trend.unit}</Text>
                          <Text className="text-xs text-gray-500">{new Date(trend.trend_date).toLocaleDateString()}</Text>
                        </div>
                      </div>
                    ))
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="lg:col-span-2 space-y-6">
          {selectedTrend ? (
            <>
              <Card>
                <CardHeader>
                  <div className="flex justify-between items-center">
                    <Heading level={2}>{selectedTrend.product_name} Price Trend</Heading>
                    <div className="flex items-center gap-2">
                      <Text className="text-sm text-gray-500">Source: {selectedTrend.source}</Text>
                      <Text className="text-sm text-gray-500">Location: {selectedTrend.market_location}</Text>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="flex justify-between items-center mb-4">
                    <div>
                      <Text className="text-sm text-gray-500">Current Price</Text>
                      <Text className="text-2xl font-bold">${selectedTrend.price.toFixed(2)} / {selectedTrend.unit}</Text>
                    </div>
                    <div>
                      <Text className="text-sm text-gray-500">Change</Text>
                      <div className="flex items-center gap-1">
                        {getTrendIcon(selectedTrend.price_change_percentage)}
                        <Text className={`font-bold ${
                          selectedTrend.price_change_percentage > 0 ? 'text-green-600' : 
                          selectedTrend.price_change_percentage < 0 ? 'text-red-600' : 'text-gray-600'
                        }`}>
                          {selectedTrend.price_change > 0 ? '+' : ''}
                          ${Math.abs(selectedTrend.price_change).toFixed(2)} ({selectedTrend.price_change_percentage > 0 ? '+' : ''}
                          {selectedTrend.price_change_percentage.toFixed(2)}%)
                        </Text>
                      </div>
                    </div>
                    <div>
                      <Text className="text-sm text-gray-500">Forecast</Text>
                      <div className="flex items-center gap-1">
                        {getForecastBadge(selectedTrend.forecast)}
                      </div>
                    </div>
                  </div>

                  <TrendChart data={selectedTrend.historical_data} />

                  <div className="mt-6">
                    <Heading level={3}>Price Forecast (Next 30 Days)</Heading>
                    <div className="mt-2">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Date</TableHead>
                            <TableHead>Forecasted Price</TableHead>
                            <TableHead>Change from Current</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {selectedTrend.forecast.next_30_days.map((forecast: any) => {
                            const change = forecast.price - selectedTrend.price;
                            const changePercentage = (change / selectedTrend.price) * 100;

                            return (
                              <TableRow key={forecast.date}>
                                <TableCell>{new Date(forecast.date).toLocaleDateString()}</TableCell>
                                <TableCell>${forecast.price.toFixed(2)}</TableCell>
                                <TableCell>
                                  <div className="flex items-center gap-1">
                                    {getTrendIcon(changePercentage)}
                                    <Text className={`${
                                      changePercentage > 0 ? 'text-green-600' : 
                                      changePercentage < 0 ? 'text-red-600' : 'text-gray-600'
                                    }`}>
                                      {change > 0 ? '+' : ''}${change.toFixed(2)} ({changePercentage > 0 ? '+' : ''}
                                      {changePercentage.toFixed(2)}%)
                                    </Text>
                                  </div>
                                </TableCell>
                              </TableRow>
                            );
                          })}
                        </TableBody>
                      </Table>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </>
          ) : (
            <Card>
              <CardContent className="p-8 text-center">
                <Text className="text-gray-500">Select a market trend from the list to view details.</Text>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
};

export default MarketTrends;
