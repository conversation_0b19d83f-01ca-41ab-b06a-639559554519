import React, { useState, useEffect } from 'react';
import { useFarm } from '../../context/FarmContext';
import { useAuth } from '../../context/AuthContext';
import { Card, CardContent, CardHeader } from '../../components/ui/Card';
import { Button } from '../../components/ui/Button';
import { Heading } from '../../components/ui/Heading';
import { Text } from '../../components/ui/Text';
import { Spinner } from '../../components/ui/Spinner';
import { Alert, AlertDescription, AlertTitle } from '../../components/ui/Alert';
import { Badge } from '../../components/ui/Badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../components/ui/Select';
import { Input } from '../../components/ui/Input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../../components/ui/Tabs';
import { ShoppingCart, MagnifyingGlass, Heart, Tag, MapPin } from 'phosphor-react';
import { 
  getMarketplaceListings, 
  getFarmListings, 
  getMarketplaceCategories, 
  toggleFavorite,
  MarketplaceListing,
  Category
} from '../../services/marketplaceService';

// Listing card component
const ListingCard: React.FC<{ listing: MarketplaceListing, onFavorite: (id: string) => void }> = ({ listing, onFavorite }) => {
  return (
    <Card className="h-full flex flex-col">
      <div className="relative">
        {listing.images && listing.images.length > 0 ? (
          <img 
            src={listing.images[0]} 
            alt={listing.title} 
            className="w-full h-48 object-cover rounded-t-md"
          />
        ) : (
          <div className="w-full h-48 bg-gray-200 flex items-center justify-center rounded-t-md">
            <Text className="text-gray-500">No Image</Text>
          </div>
        )}
        <Button 
          variant="ghost" 
          size="sm" 
          className="absolute top-2 right-2 bg-white/80 rounded-full p-1 hover:bg-white"
          onClick={() => onFavorite(listing.id)}
        >
          <Heart size={20} weight={listing.isFavorited ? "fill" : "regular"} className={listing.isFavorited ? "text-red-500" : "text-gray-500"} />
        </Button>
        {listing.is_featured && (
          <Badge variant="success" className="absolute top-2 left-2">
            Featured
          </Badge>
        )}
      </div>
      <CardContent className="flex-1 flex flex-col p-4">
        <div className="mb-2 flex justify-between items-start">
          <Heading level={3} className="text-lg">{listing.title}</Heading>
          <Badge variant="outline" className="ml-2">
            {listing.listing_type}
          </Badge>
        </div>
        <Text className="text-sm text-gray-500 mb-2 line-clamp-2">{listing.description}</Text>
        <div className="flex items-center gap-1 mb-2">
          <Tag size={16} className="text-gray-500" />
          <Text className="text-sm text-gray-500">{listing.category}</Text>
        </div>
        {listing.location && (
          <div className="flex items-center gap-1 mb-2">
            <MapPin size={16} className="text-gray-500" />
            <Text className="text-sm text-gray-500">{listing.location}</Text>
          </div>
        )}
        <div className="mt-auto pt-4">
          <div className="flex justify-between items-center">
            <Text className="font-bold text-lg">
              {listing.price ? `$${listing.price.toLocaleString()}` : 'Contact for Price'}
              {listing.unit && ` / ${listing.unit}`}
            </Text>
            <Button size="sm">View</Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

const Marketplace: React.FC = () => {
  const { currentFarm } = useFarm();
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedListingType, setSelectedListingType] = useState('all');
  const [activeTab, setActiveTab] = useState('browse');

  // State for marketplace data
  const [listings, setListings] = useState<MarketplaceListing[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [myListings, setMyListings] = useState<MarketplaceListing[]>([]);

  useEffect(() => {
    const fetchData = async () => {
      if (!currentFarm && activeTab === 'my-listings') {
        setActiveTab('browse');
        return;
      }

      setLoading(true);
      setError(null);

      try {
        // Fetch marketplace categories
        const categoriesData = await getMarketplaceCategories();
        setCategories(categoriesData);

        // Fetch listings based on active tab
        if (activeTab === 'my-listings' && currentFarm) {
          // Fetch listings for the current farm
          const farmListings = await getFarmListings(currentFarm.id);
          setMyListings(farmListings);
        } else {
          // Fetch all listings with filters
          const allListings = await getMarketplaceListings(
            selectedCategory !== 'all' ? selectedCategory : undefined,
            undefined, // subcategory
            selectedListingType !== 'all' ? selectedListingType : undefined
          );
          setListings(allListings);

          // If we're on the favorites tab, filter to only show favorited listings
          if (activeTab === 'favorites') {
            const favoritedListings = allListings.filter(listing => listing.isFavorited);
            setListings(favoritedListings);
          }
        }
      } catch (err) {
        console.error('Error fetching marketplace listings:', err);
        setError('Failed to load marketplace listings. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [currentFarm, activeTab, selectedCategory, selectedListingType]);

  // Filter listings based on search term, selected category, and listing type
  const filteredListings = listings.filter(listing => {
    const matchesSearch = searchTerm === '' || 
      listing.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      listing.description.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesCategory = selectedCategory === 'all' || 
      listing.category === selectedCategory;

    const matchesListingType = selectedListingType === 'all' || 
      listing.listing_type === selectedListingType;

    return matchesSearch && matchesCategory && matchesListingType;
  });

  const handleFavorite = async (id: string) => {
    if (!user) return;

    try {
      // Find the listing to toggle
      const listing = listings.find(l => l.id === id) || myListings.find(l => l.id === id);
      if (!listing) return;

      // Toggle favorite status
      const isFavorite = !listing.isFavorited;

      // Update UI immediately for better user experience
      const updateListing = (list: MarketplaceListing[]) => 
        list.map(l => 
          l.id === id 
            ? { ...l, isFavorited: isFavorite, favorites: isFavorite ? l.favorites + 1 : l.favorites - 1 } 
            : l
        );

      setListings(updateListing(listings));
      setMyListings(updateListing(myListings));

      // Call API to update favorite status
      const result = await toggleFavorite(id, user.id, isFavorite);

      if (!result.success) {
        // If API call fails, revert the UI change
        setListings(updateListing(listings));
        setMyListings(updateListing(myListings));
        console.error('Failed to update favorite status');
      }
    } catch (err) {
      console.error('Error toggling favorite status:', err);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spinner size="lg" />
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-2">
          <ShoppingCart size={24} className="text-primary-500" />
          <Heading level={1}>Marketplace</Heading>
        </div>
        {currentFarm && (
          <Button>Create Listing</Button>
        )}
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="browse">Browse Listings</TabsTrigger>
          {currentFarm && (
            <TabsTrigger value="my-listings">My Listings</TabsTrigger>
          )}
          {currentFarm && (
            <TabsTrigger value="favorites">Favorites</TabsTrigger>
          )}
        </TabsList>

        <TabsContent value="browse" className="space-y-6 mt-6">
          <Card>
            <CardHeader>
              <Heading level={2}>Browse Marketplace Listings</Heading>
              <Text>Find agricultural products, equipment, services, and more from farms across the country.</Text>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col md:flex-row gap-4 mb-6">
                <div className="flex-1">
                  <div className="relative">
                    <MagnifyingGlass size={18} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500" />
                    <Input
                      placeholder="Search listings..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
                <div className="w-full md:w-48">
                  <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                    <SelectTrigger>
                      <SelectValue placeholder="Category" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Categories</SelectItem>
                      {categories.map(category => (
                        <SelectItem key={category.id} value={category.id}>{category.name}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="w-full md:w-48">
                  <Select value={selectedListingType} onValueChange={setSelectedListingType}>
                    <SelectTrigger>
                      <SelectValue placeholder="Listing Type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Types</SelectItem>
                      <SelectItem value="sell">For Sale</SelectItem>
                      <SelectItem value="buy">Wanted</SelectItem>
                      <SelectItem value="service">Services</SelectItem>
                      <SelectItem value="rent">For Rent</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {filteredListings.length === 0 ? (
                <div className="text-center py-8">
                  <Text className="text-gray-500">No listings found matching your criteria.</Text>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {filteredListings.map(listing => (
                    <ListingCard 
                      key={listing.id} 
                      listing={listing} 
                      onFavorite={handleFavorite}
                    />
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="my-listings" className="space-y-6 mt-6">
          <Card>
            <CardHeader>
              <Heading level={2}>My Listings</Heading>
              <Text>Manage your marketplace listings.</Text>
            </CardHeader>
            <CardContent>
              {myListings.length === 0 ? (
                <div className="text-center py-8">
                  <Text className="text-gray-500">You don't have any listings yet.</Text>
                  <div className="mt-4">
                    <Button>Create Your First Listing</Button>
                  </div>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {myListings.map(listing => (
                    <ListingCard 
                      key={listing.id} 
                      listing={listing} 
                      onFavorite={handleFavorite}
                    />
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="favorites" className="space-y-6 mt-6">
          <Card>
            <CardHeader>
              <Heading level={2}>Favorite Listings</Heading>
              <Text>View and manage your favorite listings.</Text>
            </CardHeader>
            <CardContent>
              {listings.filter(listing => listing.isFavorited).length === 0 ? (
                <div className="text-center py-8">
                  <Text className="text-gray-500">You don't have any favorite listings yet.</Text>
                  <div className="mt-4">
                    <Button variant="outline" onClick={() => setActiveTab('browse')}>Browse Listings</Button>
                  </div>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {listings.filter(listing => listing.isFavorited).map(listing => (
                    <ListingCard 
                      key={listing.id} 
                      listing={listing} 
                      onFavorite={handleFavorite}
                    />
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default Marketplace;
