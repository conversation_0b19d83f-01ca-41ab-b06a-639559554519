import React, { useState, useEffect } from 'react';
import { useFarm } from '../../context/FarmContext';
import { 
  getFinancialPlanningData, 
  formatCurrency, 
  formatDate, 
  getStatusColorClass,
  type FinancialPlanningData,
  type Invoice,
  type Bill
} from '../../services/financialPlanningService';
import { Calendar, ChevronLeft, ChevronRight, DollarSign, AlertCircle } from 'lucide-react';

const FinancialPlanningPage: React.FC = () => {
  const { selectedFarm } = useFarm();
  const [financialData, setFinancialData] = useState<FinancialPlanningData | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [currentMonth, setCurrentMonth] = useState<Date>(new Date());
  const [viewMode, setViewMode] = useState<'calendar' | 'list'>('calendar');
  
  // Fetch financial planning data when farm or month changes
  useEffect(() => {
    const fetchData = async () => {
      if (!selectedFarm) {
        setFinancialData(null);
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        setError(null);
        
        // Calculate start and end dates based on current month
        const startDate = new Date(currentMonth.getFullYear(), currentMonth.getMonth(), 1);
        const endDate = new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 3, 0);
        
        const data = await getFinancialPlanningData(
          selectedFarm.id,
          startDate.toISOString(),
          endDate.toISOString()
        );
        
        setFinancialData(data);
      } catch (error) {
        console.error('Error fetching financial planning data:', error);
        setError('Failed to load financial planning data. Please try again later.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [selectedFarm, currentMonth]);

  // Navigate to previous month
  const goToPreviousMonth = () => {
    setCurrentMonth(new Date(currentMonth.getFullYear(), currentMonth.getMonth() - 1, 1));
  };

  // Navigate to next month
  const goToNextMonth = () => {
    setCurrentMonth(new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1, 1));
  };

  // Get month name
  const getMonthName = (date: Date) => {
    return date.toLocaleDateString('en-US', { month: 'long', year: 'numeric' });
  };

  // Group financial items by date
  const getFinancialItemsByDate = () => {
    if (!financialData) return {};
    
    const { invoices, bills, projectedInvoices, projectedBills } = financialData;
    
    // Create a map of dates to financial items
    const itemsByDate: Record<string, { invoices: Invoice[], bills: Bill[] }> = {};
    
    // Helper function to add items to the map
    const addItemsToMap = (items: Invoice[] | Bill[], type: 'invoices' | 'bills') => {
      items.forEach(item => {
        const dateKey = new Date(item.due_date).toISOString().split('T')[0];
        if (!itemsByDate[dateKey]) {
          itemsByDate[dateKey] = { invoices: [], bills: [] };
        }
        itemsByDate[dateKey][type].push(item as any);
      });
    };
    
    // Add all items to the map
    addItemsToMap(invoices, 'invoices');
    addItemsToMap(bills, 'bills');
    addItemsToMap(projectedInvoices, 'invoices');
    addItemsToMap(projectedBills, 'bills');
    
    return itemsByDate;
  };

  // Generate calendar days for the current month
  const generateCalendarDays = () => {
    const year = currentMonth.getFullYear();
    const month = currentMonth.getMonth();
    
    // Get the first day of the month
    const firstDay = new Date(year, month, 1);
    // Get the last day of the month
    const lastDay = new Date(year, month + 1, 0);
    
    // Get the day of the week for the first day (0 = Sunday, 1 = Monday, etc.)
    const firstDayOfWeek = firstDay.getDay();
    
    // Calculate the number of days in the month
    const daysInMonth = lastDay.getDate();
    
    // Create an array of day objects
    const days = [];
    
    // Add empty cells for days before the first day of the month
    for (let i = 0; i < firstDayOfWeek; i++) {
      days.push({ day: 0, date: null });
    }
    
    // Add cells for each day of the month
    for (let i = 1; i <= daysInMonth; i++) {
      const date = new Date(year, month, i);
      days.push({ day: i, date });
    }
    
    return days;
  };

  // Render the financial summary
  const renderFinancialSummary = () => {
    if (!financialData) return null;
    
    const { financialSummary } = financialData;
    
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <div className="bg-white rounded-lg shadow-sm p-4">
          <h3 className="text-sm font-medium text-gray-500">Unpaid Invoices</h3>
          <div className="mt-2 flex justify-between items-center">
            <p className="text-2xl font-bold text-gray-900">
              {financialSummary.unpaidInvoicesCount}
            </p>
            <p className="text-lg font-semibold text-blue-600">
              {formatCurrency(financialSummary.unpaidInvoicesAmount)}
            </p>
          </div>
          {financialSummary.unpaidInvoicesCount > 0 && (
            <div className="mt-2 text-sm text-red-600 flex items-center">
              <AlertCircle size={16} className="mr-1" />
              Action needed
            </div>
          )}
        </div>
        
        <div className="bg-white rounded-lg shadow-sm p-4">
          <h3 className="text-sm font-medium text-gray-500">Unpaid Bills</h3>
          <div className="mt-2 flex justify-between items-center">
            <p className="text-2xl font-bold text-gray-900">
              {financialSummary.unpaidBillsCount}
            </p>
            <p className="text-lg font-semibold text-red-600">
              {formatCurrency(financialSummary.unpaidBillsAmount)}
            </p>
          </div>
          {financialSummary.unpaidBillsCount > 0 && (
            <div className="mt-2 text-sm text-red-600 flex items-center">
              <AlertCircle size={16} className="mr-1" />
              Action needed
            </div>
          )}
        </div>
        
        <div className="bg-white rounded-lg shadow-sm p-4">
          <h3 className="text-sm font-medium text-gray-500">Current Cash Flow</h3>
          <div className="mt-2 flex justify-between items-center">
            <p className="text-2xl font-bold text-gray-900">
              {formatCurrency(financialSummary.netCashFlow)}
            </p>
            <DollarSign size={20} className={financialSummary.netCashFlow >= 0 ? "text-green-600" : "text-red-600"} />
          </div>
          <div className="mt-2 text-sm text-gray-500">
            Income: {formatCurrency(financialSummary.totalIncome)}
          </div>
          <div className="text-sm text-gray-500">
            Expenses: {formatCurrency(financialSummary.totalExpenses)}
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow-sm p-4">
          <h3 className="text-sm font-medium text-gray-500">Projected Cash Flow</h3>
          <div className="mt-2 flex justify-between items-center">
            <p className="text-2xl font-bold text-gray-900">
              {formatCurrency(financialSummary.projectedNetCashFlow)}
            </p>
            <DollarSign size={20} className={financialSummary.projectedNetCashFlow >= 0 ? "text-green-600" : "text-red-600"} />
          </div>
          <div className="mt-2 text-sm text-gray-500">
            Projected Income: {formatCurrency(financialSummary.totalProjectedIncome)}
          </div>
          <div className="text-sm text-gray-500">
            Projected Expenses: {formatCurrency(financialSummary.totalProjectedExpenses)}
          </div>
        </div>
      </div>
    );
  };

  // Render a calendar view of financial items
  const renderCalendarView = () => {
    const days = generateCalendarDays();
    const itemsByDate = getFinancialItemsByDate();
    
    return (
      <div className="bg-white rounded-lg shadow-sm overflow-hidden">
        <div className="grid grid-cols-7 gap-px bg-gray-200">
          {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((day, index) => (
            <div key={index} className="bg-gray-50 p-2 text-center text-sm font-medium text-gray-500">
              {day}
            </div>
          ))}
        </div>
        
        <div className="grid grid-cols-7 gap-px bg-gray-200">
          {days.map((day, index) => {
            if (!day.date) {
              return (
                <div key={`empty-${index}`} className="bg-gray-50 p-2 min-h-[120px]"></div>
              );
            }
            
            const dateKey = day.date.toISOString().split('T')[0];
            const dayItems = itemsByDate[dateKey] || { invoices: [], bills: [] };
            const hasItems = dayItems.invoices.length > 0 || dayItems.bills.length > 0;
            
            return (
              <div 
                key={`day-${day.day}`} 
                className={`bg-white p-2 min-h-[120px] ${
                  hasItems ? 'border-l-4 border-blue-500' : ''
                }`}
              >
                <div className="font-medium text-sm text-gray-700 mb-1">{day.day}</div>
                
                <div className="space-y-1 overflow-y-auto max-h-[80px]">
                  {dayItems.invoices.map((invoice) => (
                    <div 
                      key={invoice.id} 
                      className={`text-xs p-1 rounded ${getStatusColorClass(invoice.status)}`}
                    >
                      <div className="font-medium truncate">{invoice.invoice_number}</div>
                      <div className="flex justify-between">
                        <span>{invoice.is_projected ? 'Projected' : invoice.status}</span>
                        <span>{formatCurrency(invoice.total_amount)}</span>
                      </div>
                    </div>
                  ))}
                  
                  {dayItems.bills.map((bill) => (
                    <div 
                      key={bill.id} 
                      className={`text-xs p-1 rounded ${getStatusColorClass(bill.status)}`}
                    >
                      <div className="font-medium truncate">{bill.title}</div>
                      <div className="flex justify-between">
                        <span>{bill.is_projected ? 'Projected' : bill.status}</span>
                        <span>{formatCurrency(bill.amount)}</span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  // Render a list view of financial items
  const renderListView = () => {
    if (!financialData) return null;
    
    const { invoices, bills, projectedInvoices, projectedBills } = financialData;
    
    // Combine all items and sort by due date
    const allItems = [
      ...invoices.map(item => ({ ...item, type: 'invoice' as const })),
      ...bills.map(item => ({ ...item, type: 'bill' as const })),
      ...projectedInvoices.map(item => ({ ...item, type: 'invoice' as const })),
      ...projectedBills.map(item => ({ ...item, type: 'bill' as const }))
    ].sort((a, b) => new Date(a.due_date).getTime() - new Date(b.due_date).getTime());
    
    // Group items by month and day
    const itemsByMonth: Record<string, typeof allItems> = {};
    
    allItems.forEach(item => {
      const date = new Date(item.due_date);
      const monthKey = date.toLocaleDateString('en-US', { month: 'long', year: 'numeric' });
      
      if (!itemsByMonth[monthKey]) {
        itemsByMonth[monthKey] = [];
      }
      
      itemsByMonth[monthKey].push(item);
    });
    
    return (
      <div className="space-y-6">
        {Object.entries(itemsByMonth).map(([month, items]) => (
          <div key={month} className="bg-white rounded-lg shadow-sm overflow-hidden">
            <div className="bg-gray-50 p-3 border-b">
              <h3 className="text-lg font-medium text-gray-700">{month}</h3>
            </div>
            
            <div className="divide-y divide-gray-200">
              {items.map(item => {
                const isInvoice = item.type === 'invoice';
                const amount = isInvoice 
                  ? (item as Invoice).total_amount 
                  : (item as Bill).amount;
                
                return (
                  <div key={item.id} className="p-4 hover:bg-gray-50">
                    <div className="flex justify-between items-start">
                      <div>
                        <div className="font-medium text-gray-900">
                          {isInvoice 
                            ? (item as Invoice).invoice_number 
                            : (item as Bill).title
                          }
                        </div>
                        <div className="text-sm text-gray-500">
                          Due: {formatDate(item.due_date)}
                        </div>
                      </div>
                      
                      <div className="text-right">
                        <div className="font-medium text-gray-900">
                          {formatCurrency(amount)}
                        </div>
                        <div className={`text-sm px-2 py-1 rounded-full inline-block ${getStatusColorClass(item.status)}`}>
                          {item.is_projected ? 'Projected' : item.status}
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        ))}
      </div>
    );
  };

  return (
    <div className="container mx-auto px-4 py-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Financial Planning & Outlook</h1>
        
        <div className="flex space-x-2">
          <button
            onClick={() => setViewMode('calendar')}
            className={`px-3 py-2 rounded-md ${
              viewMode === 'calendar' 
                ? 'bg-blue-600 text-white' 
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            }`}
          >
            <Calendar size={18} className="inline-block mr-1" />
            Calendar
          </button>
          
          <button
            onClick={() => setViewMode('list')}
            className={`px-3 py-2 rounded-md ${
              viewMode === 'list' 
                ? 'bg-blue-600 text-white' 
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            }`}
          >
            <DollarSign size={18} className="inline-block mr-1" />
            List
          </button>
        </div>
      </div>
      
      {renderFinancialSummary()}
      
      <div className="mb-6">
        <div className="flex justify-between items-center mb-4">
          <button 
            onClick={goToPreviousMonth}
            className="p-2 rounded-full hover:bg-gray-100"
          >
            <ChevronLeft size={20} />
          </button>
          
          <h2 className="text-xl font-medium text-gray-800">
            {getMonthName(currentMonth)}
          </h2>
          
          <button 
            onClick={goToNextMonth}
            className="p-2 rounded-full hover:bg-gray-100"
          >
            <ChevronRight size={20} />
          </button>
        </div>
        
        {isLoading ? (
          <div className="flex justify-center items-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            <p className="ml-3 text-gray-600">Loading financial data...</p>
          </div>
        ) : error ? (
          <div className="bg-red-100 text-red-700 p-4 rounded-md">
            {error}
          </div>
        ) : !selectedFarm ? (
          <div className="bg-yellow-100 text-yellow-700 p-4 rounded-md">
            Please select a farm to view financial planning data.
          </div>
        ) : !financialData ? (
          <div className="bg-gray-100 text-gray-700 p-4 rounded-md">
            No financial data available.
          </div>
        ) : (
          viewMode === 'calendar' ? renderCalendarView() : renderListView()
        )}
      </div>
    </div>
  );
};

export default FinancialPlanningPage;