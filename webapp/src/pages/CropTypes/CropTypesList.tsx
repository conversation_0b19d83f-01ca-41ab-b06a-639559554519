import { useState, useEffect, useContext } from 'react';
import { Link } from 'react-router-dom';
import axios from 'axios';
import { AuthContext } from '../../context/AuthContext';
import { useFarm } from '../../context/FarmContext';
import Layout from '../../components/Layout';
import { API_URL } from '../../config';

interface CropType {
  id: string;
  name: string;
  description: string;
  growing_season: string;
  days_to_maturity: number;
  planting_depth: number;
  row_spacing: number;
  plant_spacing: number;
  ideal_soil_ph: number;
  ideal_temperature: number;
}

const CropTypesList = () => {
  const [cropTypes, setCropTypes] = useState<CropType[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const { user } = useContext(AuthContext);
  const { selectedFarm } = useFarm();

  // Fetch crop types for the selected farm
  useEffect(() => {
    const fetchCropTypes = async () => {
      if (!selectedFarm) return;

      setLoading(true);
      setError(null);

      try {
        const response = await axios.get(`${API_URL}/crop-types/farm/${selectedFarm.id}`);
        // Check if response.data is an array or has a cropTypes property
        setCropTypes(Array.isArray(response.data) ? response.data : response.data.cropTypes || []);
      } catch (err: any) {
        console.error('Error fetching crop types:', err);
        setError('Failed to load crop types. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchCropTypes();
  }, [selectedFarm]);

  // Handle crop type deletion
  const handleDeleteCropType = async (cropTypeId: string) => {
    if (!window.confirm('Are you sure you want to delete this crop type?')) {
      return;
    }

    try {
      await axios.delete(`${API_URL}/crop-types/${cropTypeId}`);
      setCropTypes(cropTypes.filter(cropType => cropType.id !== cropTypeId));
    } catch (err: any) {
      console.error('Error deleting crop type:', err);
      setError('Failed to delete crop type. Please try again later.');
    }
  };

  return (
    <Layout>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Crop Type Management</h1>
        <div className="flex space-x-2">
          <Link
            to="/crop-types/new"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Add New Crop Type
          </Link>
        </div>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">{error}</span>
        </div>
      )}

      {loading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
          <p className="ml-3 text-gray-500">Loading crop types...</p>
        </div>
      ) : cropTypes.length === 0 ? (
        <div className="bg-white shadow rounded-lg p-8 text-center">
          <p className="text-gray-500 mb-4">No crop types found for this farm.</p>
          <p className="text-sm text-gray-400 mb-6">
            Add your first crop type to define the types of crops you grow and their characteristics.
          </p>
          <Link
            to="/crop-types/new"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Add New Crop Type
          </Link>
        </div>
      ) : (
        <div className="bg-white shadow overflow-hidden sm:rounded-md">
          <ul className="divide-y divide-gray-200">
            {cropTypes.map((cropType) => (
              <li key={cropType.id}>
                <div className="px-4 py-4 sm:px-6">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className="ml-3">
                        <p className="text-sm font-medium text-primary-600 truncate">{cropType.name}</p>
                        <p className="text-sm text-gray-500">
                          {cropType.growing_season && <span className="mr-2">Season: {cropType.growing_season}</span>}
                          {cropType.days_to_maturity && <span className="mr-2">Days to Maturity: {cropType.days_to_maturity}</span>}
                        </p>
                      </div>
                    </div>
                    <div className="flex space-x-2">
                      <Link
                        to={`/crop-types/${cropType.id}`}
                        className="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                      >
                        View
                      </Link>
                      <Link
                        to={`/crop-types/${cropType.id}/edit`}
                        className="inline-flex items-center px-3 py-1 border border-gray-300 text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                      >
                        Edit
                      </Link>
                      <button
                        onClick={() => handleDeleteCropType(cropType.id)}
                        className="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                      >
                        Delete
                      </button>
                    </div>
                  </div>
                  {cropType.description && (
                    <div className="mt-2 text-sm text-gray-500">
                      <p>{cropType.description}</p>
                    </div>
                  )}
                  <div className="mt-2 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-2">
                    {cropType.planting_depth && (
                      <div className="text-xs text-gray-500">
                        <span className="font-medium">Planting Depth:</span> {cropType.planting_depth} inches
                      </div>
                    )}
                    {cropType.row_spacing && (
                      <div className="text-xs text-gray-500">
                        <span className="font-medium">Row Spacing:</span> {cropType.row_spacing} inches
                      </div>
                    )}
                    {cropType.plant_spacing && (
                      <div className="text-xs text-gray-500">
                        <span className="font-medium">Plant Spacing:</span> {cropType.plant_spacing} inches
                      </div>
                    )}
                    {cropType.ideal_soil_ph && (
                      <div className="text-xs text-gray-500">
                        <span className="font-medium">Ideal Soil pH:</span> {cropType.ideal_soil_ph}
                      </div>
                    )}
                    {cropType.ideal_temperature && (
                      <div className="text-xs text-gray-500">
                        <span className="font-medium">Ideal Temperature:</span> {cropType.ideal_temperature}°F
                      </div>
                    )}
                  </div>
                </div>
              </li>
            ))}
          </ul>
        </div>
      )}
    </Layout>
  );
};

export default CropTypesList;