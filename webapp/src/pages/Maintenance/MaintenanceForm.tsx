import { useState, useEffect } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import axios from 'axios';
import Layout from '../../components/Layout';
import { API_URL } from '../../config';

interface MaintenanceSchedule {
  id?: string;
  equipment_id: string;
  maintenance_type: string;
  frequency_type: string;
  frequency_value: number;
  last_performed_date: string;
  last_performed_hours: number | null;
  next_due_date: string | null;
  next_due_hours: number | null;
  alert_threshold: number | null;
  notes: string;
}

interface Equipment {
  id: string;
  name: string;
}

const MaintenanceForm = () => {
  const { scheduleId } = useParams<{ scheduleId: string }>();
  const navigate = useNavigate();
  const isEditMode = !!scheduleId;

  const [schedule, setSchedule] = useState<MaintenanceSchedule>({
    equipment_id: '',
    maintenance_type: '',
    frequency_type: 'days',
    frequency_value: 30,
    last_performed_date: new Date().toISOString().split('T')[0],
    last_performed_hours: null,
    next_due_date: null,
    next_due_hours: null,
    alert_threshold: null,
    notes: ''
  });

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [equipment, setEquipment] = useState<Equipment[]>([]);

  // Fetch equipment
  useEffect(() => {
    const fetchEquipment = async () => {
      try {
        const response = await axios.get(`${API_URL}/equipment`);
        const equipmentList = Array.isArray(response.data) ? response.data : response.data.equipment || [];
        setEquipment(equipmentList);

        // Set the first equipment as selected by default for new schedule
        if (equipmentList.length > 0 && !isEditMode) {
          setSchedule(prev => ({ ...prev, equipment_id: equipmentList[0].id }));
        }
      } catch (err: any) {
        console.error('Error fetching equipment:', err);
        setError('Failed to load equipment. Please try again later.');
      }
    };

    fetchEquipment();
  }, [isEditMode]);

  // Fetch maintenance schedule data if in edit mode
  useEffect(() => {
    const fetchMaintenanceSchedule = async () => {
      if (!scheduleId) return;

      setLoading(true);
      setError(null);

      try {
        const response = await axios.get(`${API_URL}/maintenance/schedules/${scheduleId}`);
        const scheduleData = response.data.maintenanceSchedule;

        // Format dates for the input fields
        const formattedData = {
          ...scheduleData,
          last_performed_date: scheduleData.last_performed_date ? 
            new Date(scheduleData.last_performed_date).toISOString().split('T')[0] : 
            new Date().toISOString().split('T')[0],
          next_due_date: scheduleData.next_due_date ? 
            new Date(scheduleData.next_due_date).toISOString().split('T')[0] : 
            null
        };

        setSchedule(formattedData);
      } catch (err: any) {
        console.error('Error fetching maintenance schedule:', err);
        setError('Failed to load maintenance schedule data. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    if (isEditMode) {
      fetchMaintenanceSchedule();
    }
  }, [scheduleId, isEditMode]);

  // Calculate next due date based on frequency
  const calculateNextDueDate = (lastDate: string, frequencyType: string, frequencyValue: number) => {
    if (!lastDate) return null;

    const date = new Date(lastDate);

    switch (frequencyType) {
      case 'days':
        date.setDate(date.getDate() + frequencyValue);
        break;
      case 'weeks':
        date.setDate(date.getDate() + (frequencyValue * 7));
        break;
      case 'months':
        date.setMonth(date.getMonth() + frequencyValue);
        break;
      case 'years':
        date.setFullYear(date.getFullYear() + frequencyValue);
        break;
      default:
        return null;
    }

    return date.toISOString().split('T')[0];
  };

  // Calculate next due hours based on frequency
  const calculateNextDueHours = (lastHours: number | null, frequencyType: string, frequencyValue: number) => {
    if (frequencyType !== 'hours' || lastHours === null) return null;
    return lastHours + frequencyValue;
  };

  // Handle form input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;

    // Special handling for numeric fields
    if (['frequency_value', 'last_performed_hours', 'next_due_hours', 'alert_threshold'].includes(name)) {
      const numValue = value === '' ? null : parseFloat(value);

      setSchedule(prev => {
        const updated = { ...prev, [name]: numValue };

        // Recalculate next_due_date and next_due_hours when relevant fields change
        if (['frequency_type', 'frequency_value', 'last_performed_date'].includes(name)) {
          updated.next_due_date = calculateNextDueDate(
            updated.last_performed_date, 
            name === 'frequency_type' ? value : updated.frequency_type, 
            name === 'frequency_value' ? (numValue as number) : updated.frequency_value
          );
        }

        if (['frequency_type', 'frequency_value', 'last_performed_hours'].includes(name)) {
          updated.next_due_hours = calculateNextDueHours(
            updated.last_performed_hours, 
            name === 'frequency_type' ? value : updated.frequency_type, 
            name === 'frequency_value' ? (numValue as number) : updated.frequency_value
          );
        }

        return updated;
      });
    } else {
      setSchedule(prev => {
        const updated = { ...prev, [name]: value };

        // Recalculate next_due_date when last_performed_date changes
        if (name === 'last_performed_date') {
          updated.next_due_date = calculateNextDueDate(value, updated.frequency_type, updated.frequency_value);
        }

        // Recalculate next_due_hours when frequency_type changes
        if (name === 'frequency_type') {
          updated.next_due_hours = calculateNextDueHours(updated.last_performed_hours, value, updated.frequency_value);
        }

        return updated;
      });
    }
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    setLoading(true);
    setError(null);

    try {
      // Validate required fields
      if (!schedule.equipment_id || !schedule.maintenance_type || !schedule.frequency_type || !schedule.frequency_value) {
        setError('Equipment, maintenance type, and frequency are required.');
        setLoading(false);
        return;
      }

      if (isEditMode) {
        // Update existing maintenance schedule
        await axios.put(`${API_URL}/maintenance/schedules/${scheduleId}`, schedule);
      } else {
        // Create new maintenance schedule
        await axios.post(`${API_URL}/maintenance/schedules`, schedule);
      }

      // Redirect to maintenance schedules list
      navigate('/maintenance');
    } catch (err: any) {
      console.error('Error saving maintenance schedule:', err);
      setError('Failed to save maintenance schedule. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Layout>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">
          {isEditMode ? 'Edit Maintenance Schedule' : 'Add New Maintenance Schedule'}
        </h1>
        <Link
          to="/maintenance"
          className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
        >
          Back to Maintenance Schedules
        </Link>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">{error}</span>
        </div>
      )}

      <div className="bg-white shadow rounded-lg overflow-hidden">
        <form onSubmit={handleSubmit} className="p-6">
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
            {/* Equipment Selection */}
            <div>
              <label htmlFor="equipment_id" className="block text-sm font-medium text-gray-700 mb-1">
                Equipment <span className="text-red-500">*</span>
              </label>
              <select
                id="equipment_id"
                name="equipment_id"
                value={schedule.equipment_id}
                onChange={handleChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                required
              >
                <option value="" disabled>Select equipment</option>
                {equipment.map(item => (
                  <option key={item.id} value={item.id}>{item.name}</option>
                ))}
              </select>
            </div>

            {/* Maintenance Type */}
            <div>
              <label htmlFor="maintenance_type" className="block text-sm font-medium text-gray-700 mb-1">
                Maintenance Type <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                id="maintenance_type"
                name="maintenance_type"
                value={schedule.maintenance_type}
                onChange={handleChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                placeholder="e.g., Oil Change, Filter Replacement"
                required
              />
            </div>

            {/* Frequency Type */}
            <div>
              <label htmlFor="frequency_type" className="block text-sm font-medium text-gray-700 mb-1">
                Frequency Type <span className="text-red-500">*</span>
              </label>
              <select
                id="frequency_type"
                name="frequency_type"
                value={schedule.frequency_type}
                onChange={handleChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                required
              >
                <option value="hours">Hours</option>
                <option value="days">Days</option>
                <option value="weeks">Weeks</option>
                <option value="months">Months</option>
                <option value="years">Years</option>
              </select>
            </div>

            {/* Frequency Value */}
            <div>
              <label htmlFor="frequency_value" className="block text-sm font-medium text-gray-700 mb-1">
                Frequency Value <span className="text-red-500">*</span>
              </label>
              <input
                type="number"
                id="frequency_value"
                name="frequency_value"
                value={schedule.frequency_value}
                onChange={handleChange}
                min="1"
                step="1"
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                required
              />
              <p className="mt-1 text-xs text-gray-500">
                How often maintenance should be performed (in {schedule.frequency_type})
              </p>
            </div>

            {/* Last Performed Date */}
            <div>
              <label htmlFor="last_performed_date" className="block text-sm font-medium text-gray-700 mb-1">
                Last Performed Date <span className="text-red-500">*</span>
              </label>
              <input
                type="date"
                id="last_performed_date"
                name="last_performed_date"
                value={schedule.last_performed_date}
                onChange={handleChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                required
              />
            </div>

            {/* Last Performed Hours */}
            <div>
              <label htmlFor="last_performed_hours" className="block text-sm font-medium text-gray-700 mb-1">
                Last Performed Hours
              </label>
              <input
                type="number"
                id="last_performed_hours"
                name="last_performed_hours"
                value={schedule.last_performed_hours === null ? '' : schedule.last_performed_hours}
                onChange={handleChange}
                min="0"
                step="0.1"
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                placeholder="Equipment hours at last service"
              />
              <p className="mt-1 text-xs text-gray-500">
                Only needed for hour-based maintenance
              </p>
            </div>

            {/* Next Due Date (calculated, read-only) */}
            <div>
              <label htmlFor="next_due_date" className="block text-sm font-medium text-gray-700 mb-1">
                Next Due Date
              </label>
              <input
                type="date"
                id="next_due_date"
                name="next_due_date"
                value={schedule.next_due_date || ''}
                readOnly
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-gray-50 text-gray-500 sm:text-sm"
              />
              <p className="mt-1 text-xs text-gray-500">
                Calculated based on last performed date and frequency
              </p>
            </div>

            {/* Next Due Hours (calculated, read-only) */}
            {schedule.frequency_type === 'hours' && (
              <div>
                <label htmlFor="next_due_hours" className="block text-sm font-medium text-gray-700 mb-1">
                  Next Due Hours
                </label>
                <input
                  type="number"
                  id="next_due_hours"
                  name="next_due_hours"
                  value={schedule.next_due_hours === null ? '' : schedule.next_due_hours}
                  readOnly
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-gray-50 text-gray-500 sm:text-sm"
                />
                <p className="mt-1 text-xs text-gray-500">
                  Calculated based on last performed hours and frequency
                </p>
              </div>
            )}

            {/* Alert Threshold */}
            <div>
              <label htmlFor="alert_threshold" className="block text-sm font-medium text-gray-700 mb-1">
                Alert Threshold
              </label>
              <input
                type="number"
                id="alert_threshold"
                name="alert_threshold"
                value={schedule.alert_threshold === null ? '' : schedule.alert_threshold}
                onChange={handleChange}
                min="0"
                step="1"
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                placeholder="e.g., 7"
              />
              <p className="mt-1 text-xs text-gray-500">
                How many {schedule.frequency_type} before due date to send alerts
              </p>
            </div>
          </div>

          {/* Notes */}
          <div className="mt-6">
            <label htmlFor="notes" className="block text-sm font-medium text-gray-700 mb-1">
              Notes
            </label>
            <textarea
              id="notes"
              name="notes"
              value={schedule.notes}
              onChange={handleChange}
              rows={4}
              className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              placeholder="Additional information about the maintenance schedule"
            ></textarea>
          </div>

          {/* Form Actions */}
          <div className="mt-6 flex justify-end space-x-3">
            <Link
              to="/maintenance"
              className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              Cancel
            </Link>
            <button
              type="submit"
              disabled={loading}
              className={`inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 ${loading ? 'opacity-70 cursor-not-allowed' : ''}`}
            >
              {loading ? 'Saving...' : 'Save Schedule'}
            </button>
          </div>
        </form>
      </div>
    </Layout>
  );
};

export default MaintenanceForm;
