import { useState, useEffect, useContext } from 'react';
import { Link } from 'react-router-dom';
import axios from 'axios';
import { AuthContext } from '../../context/AuthContext';
import Layout from '../../components/Layout';
import { API_URL } from '../../config';

interface Equipment {
  id: string;
  name: string;
}

interface MaintenanceSchedule {
  id: string;
  equipment_id: string;
  equipment_name: string;
  maintenance_type: string;
  frequency_type: string;
  frequency_value: number;
  last_performed_date: string | null;
  next_due_date: string | null;
  status: string;
  created_at: string;
  updated_at: string;
}

const MaintenanceList = () => {
  const [maintenanceSchedules, setMaintenanceSchedules] = useState<MaintenanceSchedule[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedEquipment, setSelectedEquipment] = useState<string>('all');
  const [equipment, setEquipment] = useState<Equipment[]>([]);

  const { user } = useContext(AuthContext);

  // Fetch equipment
  useEffect(() => {
    const fetchEquipment = async () => {
      try {
        const response = await axios.get(`${API_URL}/equipment`);
        const equipmentList = Array.isArray(response.data) ? response.data : response.data.equipment || [];
        setEquipment(equipmentList);
      } catch (err: any) {
        console.error('Error fetching equipment:', err);
        setError('Failed to load equipment. Please try again later.');
      }
    };

    if (user) {
      fetchEquipment();
    }
  }, [user]);

  // Fetch maintenance schedules
  useEffect(() => {
    const fetchMaintenanceSchedules = async () => {
      setLoading(true);
      setError(null);

      try {
        let url = `${API_URL}/maintenance/schedules`;
        if (selectedEquipment !== 'all') {
          url = `${API_URL}/maintenance/schedules/equipment/${selectedEquipment}`;
        }

        const response = await axios.get(url);
        setMaintenanceSchedules(response.data.maintenanceSchedules || []);
      } catch (err: any) {
        console.error('Error fetching maintenance schedules:', err);
        setError('Failed to load maintenance schedules. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchMaintenanceSchedules();
  }, [selectedEquipment]);

  // Handle equipment filter change
  const handleEquipmentChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedEquipment(e.target.value);
  };

  // Format date for display
  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'Not set';
    const date = new Date(dateString);
    return date.toLocaleDateString();
  };

  // Get status badge class
  const getStatusBadgeClass = (schedule: MaintenanceSchedule) => {
    if (!schedule.next_due_date) return 'bg-gray-100 text-gray-800';

    const today = new Date();
    const dueDate = new Date(schedule.next_due_date);

    // If due date is in the past
    if (dueDate < today) {
      return 'bg-red-100 text-red-800';
    }

    // If due date is within 7 days
    const sevenDaysFromNow = new Date();
    sevenDaysFromNow.setDate(today.getDate() + 7);
    if (dueDate <= sevenDaysFromNow) {
      return 'bg-yellow-100 text-yellow-800';
    }

    return 'bg-green-100 text-green-800';
  };

  // Get status text
  const getStatusText = (schedule: MaintenanceSchedule) => {
    if (!schedule.next_due_date) return 'Not scheduled';

    const today = new Date();
    const dueDate = new Date(schedule.next_due_date);

    // If due date is in the past
    if (dueDate < today) {
      return 'Overdue';
    }

    // If due date is within 7 days
    const sevenDaysFromNow = new Date();
    sevenDaysFromNow.setDate(today.getDate() + 7);
    if (dueDate <= sevenDaysFromNow) {
      return 'Due soon';
    }

    return 'Scheduled';
  };

  return (
    <Layout>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Maintenance Schedules</h1>
        <Link
          to="/maintenance/schedules/new"
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
        >
          Add New Schedule
        </Link>
      </div>

      {/* Equipment Filter */}
      <div className="mb-6">
        <label htmlFor="equipment-filter" className="block text-sm font-medium text-gray-700 mb-1">
          Filter by Equipment
        </label>
        <select
          id="equipment-filter"
          value={selectedEquipment}
          onChange={handleEquipmentChange}
          className="block w-full md:w-64 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
        >
          <option value="all">All Equipment</option>
          {equipment.map(item => (
            <option key={item.id} value={item.id}>{item.name}</option>
          ))}
        </select>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">{error}</span>
        </div>
      )}

      {loading ? (
        <div className="flex justify-center items-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500"></div>
        </div>
      ) : maintenanceSchedules.length === 0 ? (
        <div className="bg-white shadow rounded-lg p-6 text-center">
          <p className="text-gray-500">No maintenance schedules found. Add your first maintenance schedule to get started.</p>
          <Link
            to="/maintenance/schedules/new"
            className="inline-flex items-center px-4 py-2 mt-4 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Add New Schedule
          </Link>
        </div>
      ) : (
        <div className="bg-white shadow overflow-hidden sm:rounded-md">
          <ul className="divide-y divide-gray-200">
            {maintenanceSchedules.map(schedule => (
              <li key={schedule.id}>
                <Link to={`/maintenance/schedules/${schedule.id}`} className="block hover:bg-gray-50">
                  <div className="px-4 py-4 sm:px-6">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <p className="text-sm font-medium text-primary-600 truncate">
                          {schedule.maintenance_type} - {schedule.equipment_name}
                        </p>
                        <span className={`ml-2 px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusBadgeClass(schedule)}`}>
                          {getStatusText(schedule)}
                        </span>
                      </div>
                      <div className="ml-2 flex-shrink-0 flex">
                        <Link
                          to={`/maintenance/logs/new?scheduleId=${schedule.id}`}
                          className="mr-2 inline-flex items-center px-2.5 py-1.5 border border-transparent text-xs font-medium rounded text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                          onClick={(e) => e.stopPropagation()}
                        >
                          Log Service
                        </Link>
                        <Link
                          to={`/maintenance/schedules/${schedule.id}/edit`}
                          className="inline-flex items-center px-2.5 py-1.5 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                          onClick={(e) => e.stopPropagation()}
                        >
                          Edit
                        </Link>
                      </div>
                    </div>
                    <div className="mt-2 sm:flex sm:justify-between">
                      <div className="sm:flex">
                        <p className="flex items-center text-sm text-gray-500">
                          Frequency: {schedule.frequency_value} {schedule.frequency_type}
                        </p>
                        <p className="mt-2 flex items-center text-sm text-gray-500 sm:mt-0 sm:ml-6">
                          Last performed: {formatDate(schedule.last_performed_date)}
                        </p>
                      </div>
                      <div className="mt-2 flex items-center text-sm text-gray-500 sm:mt-0">
                        <p>
                          Next due: {formatDate(schedule.next_due_date)}
                        </p>
                      </div>
                    </div>
                  </div>
                </Link>
              </li>
            ))}
          </ul>
        </div>
      )}
    </Layout>
  );
};

export default MaintenanceList;
