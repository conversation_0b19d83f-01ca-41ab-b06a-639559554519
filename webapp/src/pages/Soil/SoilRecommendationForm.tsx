import { useState, useEffect } from 'react';
import { useParams, useNavigate, Link, useLocation } from 'react-router-dom';
import axios from 'axios';
import Layout from '../../components/Layout';
import { API_URL } from '../../config';

interface SoilRecommendation {
  id?: string;
  soil_sample_id: string;
  field_id?: string;
  nutrient_type: string;
  recommended_amount: number;
  unit: string;
  application_method?: string;
  application_timing?: string;
  notes?: string;
}

interface SoilSample {
  id: string;
  farm_name: string;
  field_name: string | null;
  sample_date: string;
}

const SoilRecommendationForm = () => {
  const { recommendationId } = useParams<{ recommendationId: string }>();
  const navigate = useNavigate();
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const sampleId = queryParams.get('sampleId');

  const isEditMode = !!recommendationId;

  const [recommendation, setRecommendation] = useState<SoilRecommendation>({
    soil_sample_id: sampleId || '',
    nutrient_type: '',
    recommended_amount: 0,
    unit: 'lbs/acre',
    application_method: '',
    application_timing: '',
    notes: ''
  });

  const [soilSample, setSoilSample] = useState<SoilSample | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Fetch soil sample data and recommendation data if in edit mode
  useEffect(() => {
    const fetchData = async () => {
      if (!sampleId && !isEditMode) return;

      try {
        if (isEditMode) {
          // If editing, first get the recommendation to get the soil_sample_id
          const recommendationResponse = await axios.get(`${API_URL}/soil/recommendations/${recommendationId}`);
          const sampleId = recommendationResponse.data.soilRecommendation.soil_sample_id;
          setRecommendation(recommendationResponse.data.soilRecommendation);

          // Then fetch the soil sample
          const sampleResponse = await axios.get(`${API_URL}/soil/samples/${sampleId}`);
          setSoilSample(sampleResponse.data.soilSample);
        } else if (sampleId) {
          // If creating new recommendation, fetch the soil sample directly
          const response = await axios.get(`${API_URL}/soil/samples/${sampleId}`);
          setSoilSample(response.data.soilSample);
        }
      } catch (err: any) {
        console.error('Error fetching data:', err);
        setError('Failed to load data. Please try again later.');
      }
    };

    fetchData();
  }, [sampleId, recommendationId, isEditMode]);

  // Handle form input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target as HTMLInputElement;
    setRecommendation(prev => ({ 
      ...prev, 
      [name]: type === 'number' ? (value === '' ? 0 : parseFloat(value)) : value 
    }));
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      // Validate soil_sample_id
      if (!recommendation.soil_sample_id) {
        setError('Soil sample ID is required.');
        setLoading(false);
        return;
      }

      // Validate nutrient_type
      if (!recommendation.nutrient_type) {
        setError('Nutrient type is required.');
        setLoading(false);
        return;
      }

      // Validate recommended_amount
      if (recommendation.recommended_amount <= 0) {
        setError('Recommended amount must be greater than zero.');
        setLoading(false);
        return;
      }

      // Validate unit
      if (!recommendation.unit) {
        setError('Unit is required.');
        setLoading(false);
        return;
      }

      if (isEditMode) {
        // Update existing recommendation
        await axios.put(`${API_URL}/soil/recommendations/${recommendationId}`, recommendation);
        setSuccess('Soil recommendation updated successfully!');
      } else {
        // Create new recommendation
        await axios.post(`${API_URL}/soil/recommendations`, recommendation);
        setSuccess('Soil recommendation created successfully!');
      }

      // Redirect to soil sample detail page after a short delay to show success message
      setTimeout(() => {
        navigate(`/soil/samples/${recommendation.soil_sample_id}`);
      }, 1500);
    } catch (err: any) {
      console.error('Error saving soil recommendation:', err);
      setError('Failed to save soil recommendation. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  if (!sampleId && !isEditMode) {
    return (
      <Layout>
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">No soil sample specified. Please select a soil sample first.</span>
        </div>
        <Link
          to="/soil"
          className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
        >
          Back to Soil Samples
        </Link>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">
          {isEditMode ? 'Edit Soil Recommendation' : 'Add Soil Recommendation'}
        </h1>
        <Link
          to={soilSample ? `/soil/samples/${soilSample.id}` : '/soil'}
          className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
        >
          Back to Soil Sample
        </Link>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">{error}</span>
        </div>
      )}

      {success && (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">{success}</span>
        </div>
      )}

      {soilSample && (
        <div className="bg-white shadow rounded-lg p-4 mb-6">
          <h2 className="text-lg font-medium text-gray-900">Soil Sample Information</h2>
          <div className="mt-2 grid grid-cols-1 gap-2 sm:grid-cols-2">
            <div>
              <span className="text-sm font-medium text-gray-500">Farm:</span>
              <span className="ml-2 text-sm text-gray-900">{soilSample.farm_name}</span>
            </div>
            {soilSample.field_name && (
              <div>
                <span className="text-sm font-medium text-gray-500">Field:</span>
                <span className="ml-2 text-sm text-gray-900">{soilSample.field_name}</span>
              </div>
            )}
            <div>
              <span className="text-sm font-medium text-gray-500">Sample Date:</span>
              <span className="ml-2 text-sm text-gray-900">
                {new Date(soilSample.sample_date).toLocaleDateString()}
              </span>
            </div>
          </div>
        </div>
      )}

      <div className="bg-white shadow rounded-lg overflow-hidden">
        <form onSubmit={handleSubmit} className="p-6">
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
            {/* Nutrient Type */}
            <div>
              <label htmlFor="nutrient_type" className="block text-sm font-medium text-gray-700 mb-1">
                Nutrient Type *
              </label>
              <select
                id="nutrient_type"
                name="nutrient_type"
                value={recommendation.nutrient_type}
                onChange={handleChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                required
              >
                <option value="">Select Nutrient Type</option>
                <option value="nitrogen">Nitrogen (N)</option>
                <option value="phosphorus">Phosphorus (P)</option>
                <option value="potassium">Potassium (K)</option>
                <option value="calcium">Calcium (Ca)</option>
                <option value="magnesium">Magnesium (Mg)</option>
                <option value="sulfur">Sulfur (S)</option>
                <option value="zinc">Zinc (Zn)</option>
                <option value="manganese">Manganese (Mn)</option>
                <option value="copper">Copper (Cu)</option>
                <option value="iron">Iron (Fe)</option>
                <option value="boron">Boron (B)</option>
                <option value="lime">Lime</option>
                <option value="organic_matter">Organic Matter</option>
                <option value="other">Other</option>
              </select>
            </div>

            {/* Recommended Amount */}
            <div>
              <label htmlFor="recommended_amount" className="block text-sm font-medium text-gray-700 mb-1">
                Recommended Amount *
              </label>
              <input
                type="number"
                id="recommended_amount"
                name="recommended_amount"
                value={recommendation.recommended_amount === 0 ? '' : recommendation.recommended_amount}
                onChange={handleChange}
                step="0.01"
                min="0"
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                placeholder="e.g., 150"
                required
              />
            </div>

            {/* Unit */}
            <div>
              <label htmlFor="unit" className="block text-sm font-medium text-gray-700 mb-1">
                Unit *
              </label>
              <select
                id="unit"
                name="unit"
                value={recommendation.unit}
                onChange={handleChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                required
              >
                <option value="lbs/acre">lbs/acre</option>
                <option value="tons/acre">tons/acre</option>
                <option value="kg/hectare">kg/hectare</option>
                <option value="tonnes/hectare">tonnes/hectare</option>
                <option value="ppm">ppm</option>
                <option value="percent">percent (%)</option>
                <option value="other">other</option>
              </select>
            </div>

            {/* Application Method */}
            <div>
              <label htmlFor="application_method" className="block text-sm font-medium text-gray-700 mb-1">
                Application Method
              </label>
              <input
                type="text"
                id="application_method"
                name="application_method"
                value={recommendation.application_method || ''}
                onChange={handleChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                placeholder="e.g., Broadcast, Banded, Foliar"
              />
            </div>

            {/* Application Timing */}
            <div>
              <label htmlFor="application_timing" className="block text-sm font-medium text-gray-700 mb-1">
                Application Timing
              </label>
              <input
                type="text"
                id="application_timing"
                name="application_timing"
                value={recommendation.application_timing || ''}
                onChange={handleChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                placeholder="e.g., Pre-plant, At planting, Post-emergence"
              />
            </div>

            {/* Notes */}
            <div className="sm:col-span-2">
              <label htmlFor="notes" className="block text-sm font-medium text-gray-700 mb-1">
                Notes
              </label>
              <textarea
                id="notes"
                name="notes"
                value={recommendation.notes || ''}
                onChange={handleChange}
                rows={3}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                placeholder="Additional notes or instructions"
              />
            </div>
          </div>

          {/* Form Actions */}
          <div className="mt-6 flex justify-end space-x-3">
            <Link
              to={soilSample ? `/soil/samples/${soilSample.id}` : '/soil'}
              className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              Cancel
            </Link>
            <button
              type="submit"
              disabled={loading}
              className={`inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 ${loading ? 'opacity-70 cursor-not-allowed' : ''}`}
            >
              {loading ? 'Saving...' : 'Save Recommendation'}
            </button>
          </div>
        </form>
      </div>
    </Layout>
  );
};

export default SoilRecommendationForm;