import { useState, useEffect, useContext } from 'react';
import { Link } from 'react-router-dom';
import axios from 'axios';
import { AuthContext } from '../../context/AuthContext';
import { useFarm } from '../../context/FarmContext';
import Layout from '../../components/Layout';
import { API_URL } from '../../config';

interface SoilSample {
  id: string;
  farm_id: string;
  farm_name: string;
  field_id: string | null;
  field_name: string | null;
  sample_date: string;
  location: string | null;
  depth: string | null;
  lab_name: string | null;
  lab_reference: string | null;
  status: string;
  notes: string | null;
  created_at: string;
  updated_at: string;
}

const SoilList = () => {
  const [soilSamples, setSoilSamples] = useState<SoilSample[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const { user } = useContext(AuthContext);
  const { selectedFarm } = useFarm();

  // Fetch soil samples
  useEffect(() => {
    const fetchSoilSamples = async () => {
      setLoading(true);
      setError(null);

      try {
        let url = `${API_URL}/soil/samples`;
        if (selectedFarm) {
          url = `${API_URL}/soil/samples/farm/${selectedFarm.id}`;
        }

        const response = await axios.get(url);
        setSoilSamples(response.data.soilSamples || []);
      } catch (err: any) {
        console.error('Error fetching soil samples:', err);
        setError('Failed to load soil samples. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchSoilSamples();
  }, [selectedFarm]);

  // Format date for display
  const formatDate = (dateString: string) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleDateString();
  };

  // Get status badge class
  const getStatusBadgeClass = (status: string) => {
    switch (status.toLowerCase()) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'received':
        return 'bg-blue-100 text-blue-800';
      case 'analyzed':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <Layout>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Soil Samples</h1>
        <Link
          to="/soil/samples/new"
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
        >
          Add New Sample
        </Link>
      </div>

      {/* Display selected farm */}
      <div className="mb-6">
        <p className="text-sm text-gray-500">
          Showing soil samples for farm: <span className="font-medium text-gray-700">{selectedFarm ? selectedFarm.name : 'All Farms'}</span>
        </p>
        {!selectedFarm && (
          <p className="text-sm text-gray-500 mt-1">
            Select a farm from the header dropdown to view soil samples for a specific farm.
          </p>
        )}
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">{error}</span>
        </div>
      )}

      {loading ? (
        <div className="flex justify-center items-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500"></div>
        </div>
      ) : soilSamples.length === 0 ? (
        <div className="bg-white shadow rounded-lg p-6 text-center">
          <p className="text-gray-500">No soil samples found. Add your first soil sample to get started.</p>
          <Link
            to="/soil/samples/new"
            className="inline-flex items-center px-4 py-2 mt-4 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Add New Sample
          </Link>
        </div>
      ) : (
        <div className="bg-white shadow overflow-hidden sm:rounded-md">
          <ul className="divide-y divide-gray-200">
            {soilSamples.map(sample => (
              <li key={sample.id}>
                <Link to={`/soil/samples/${sample.id}`} className="block hover:bg-gray-50">
                  <div className="px-4 py-4 sm:px-6">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <p className="text-sm font-medium text-primary-600 truncate">
                          {sample.field_name ? `${sample.field_name} Sample` : `Sample from ${formatDate(sample.sample_date)}`}
                        </p>
                        <span className={`ml-2 px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusBadgeClass(sample.status)}`}>
                          {sample.status.charAt(0).toUpperCase() + sample.status.slice(1)}
                        </span>
                      </div>
                      <div className="ml-2 flex-shrink-0 flex">
                        <Link
                          to={`/soil/samples/${sample.id}/edit`}
                          className="ml-2 inline-flex items-center px-2.5 py-1.5 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                          onClick={(e) => e.stopPropagation()}
                        >
                          Edit
                        </Link>
                      </div>
                    </div>
                    <div className="mt-2 sm:flex sm:justify-between">
                      <div className="sm:flex">
                        <p className="flex items-center text-sm text-gray-500">
                          Farm: {sample.farm_name}
                        </p>
                        <p className="mt-2 flex items-center text-sm text-gray-500 sm:mt-0 sm:ml-6">
                          Date: {formatDate(sample.sample_date)}
                        </p>
                      </div>
                      <div className="mt-2 flex items-center text-sm text-gray-500 sm:mt-0">
                        <p>
                          {sample.lab_name ? `Lab: ${sample.lab_name}` : 'No lab specified'}
                        </p>
                      </div>
                    </div>
                  </div>
                </Link>
              </li>
            ))}
          </ul>
        </div>
      )}
    </Layout>
  );
};

export default SoilList;
