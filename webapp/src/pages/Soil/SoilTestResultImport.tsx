import { useState, useEffect } from 'react';
import { useParams, useNavigate, Link, useLocation } from 'react-router-dom';
import axios from 'axios';
import Layout from '../../components/Layout';
import { API_URL } from '../../config';

interface SoilSample {
  id: string;
  farm_name: string;
  field_name: string | null;
  sample_date: string;
  lab_name: string | null;
  lab_reference: string | null;
}

const SoilTestResultImport = () => {
  const { sampleId } = useParams<{ sampleId: string }>();
  const navigate = useNavigate();
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const sampleIdFromQuery = queryParams.get('sampleId') || sampleId;

  const [soilSample, setSoilSample] = useState<SoilSample | null>(null);
  const [file, setFile] = useState<File | null>(null);
  const [fileFormat, setFileFormat] = useState<string>('csv');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [previewData, setPreviewData] = useState<any | null>(null);

  // Fetch soil sample data
  useEffect(() => {
    const fetchSoilSample = async () => {
      if (!sampleIdFromQuery) return;

      try {
        const response = await axios.get(`${API_URL}/soil/samples/${sampleIdFromQuery}`);
        setSoilSample(response.data.soilSample);
      } catch (err: any) {
        console.error('Error fetching soil sample:', err);
        setError('Failed to load soil sample data. Please try again later.');
      }
    };

    fetchSoilSample();
  }, [sampleIdFromQuery]);

  // Handle file selection
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setFile(e.target.files[0]);
      setError(null);
      setSuccess(null);
      setPreviewData(null);
    }
  };

  // Handle file format selection
  const handleFormatChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setFileFormat(e.target.value);
    setPreviewData(null);
  };

  // Preview file contents
  const handlePreview = async () => {
    if (!file) {
      setError('Please select a file to preview');
      return;
    }

    setLoading(true);
    setError(null);

    const formData = new FormData();
    formData.append('file', file);
    formData.append('format', fileFormat);

    try {
      const response = await axios.post(`${API_URL}/soil/test-results/preview-import`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });

      setPreviewData(response.data.preview);
      setSuccess('File preview successful. Please review the data before importing.');
    } catch (err: any) {
      console.error('Error previewing file:', err);
      setError(err.response?.data?.error || 'Failed to preview file. Please check the file format and try again.');
    } finally {
      setLoading(false);
    }
  };

  // Import file
  const handleImport = async () => {
    if (!file) {
      setError('Please select a file to import');
      return;
    }

    if (!sampleIdFromQuery) {
      setError('No soil sample specified');
      return;
    }

    setLoading(true);
    setError(null);

    const formData = new FormData();
    formData.append('file', file);
    formData.append('format', fileFormat);
    formData.append('sampleId', sampleIdFromQuery);

    try {
      const response = await axios.post(`${API_URL}/soil/test-results/import`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });

      setSuccess('Soil test results imported successfully!');

      // Redirect to soil sample detail page after a short delay
      setTimeout(() => {
        navigate(`/soil/samples/${sampleIdFromQuery}`);
      }, 2000);
    } catch (err: any) {
      console.error('Error importing file:', err);
      setError(err.response?.data?.error || 'Failed to import file. Please check the file format and try again.');
    } finally {
      setLoading(false);
    }
  };

  if (!sampleIdFromQuery) {
    return (
      <Layout>
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">No soil sample specified. Please select a soil sample first.</span>
        </div>
        <Link
          to="/soil"
          className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
        >
          Back to Soil Samples
        </Link>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">
          Import Soil Test Results
        </h1>
        <Link
          to={soilSample ? `/soil/samples/${soilSample.id}` : '/soil'}
          className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
        >
          Back to Soil Sample
        </Link>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">{error}</span>
        </div>
      )}

      {success && (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">{success}</span>
        </div>
      )}

      {soilSample && (
        <div className="bg-white shadow rounded-lg p-4 mb-6">
          <h2 className="text-lg font-medium text-gray-900">Soil Sample Information</h2>
          <div className="mt-2 grid grid-cols-1 gap-2 sm:grid-cols-2">
            <div>
              <span className="text-sm font-medium text-gray-500">Farm:</span>
              <span className="ml-2 text-sm text-gray-900">{soilSample.farm_name}</span>
            </div>
            {soilSample.field_name && (
              <div>
                <span className="text-sm font-medium text-gray-500">Field:</span>
                <span className="ml-2 text-sm text-gray-900">{soilSample.field_name}</span>
              </div>
            )}
            <div>
              <span className="text-sm font-medium text-gray-500">Sample Date:</span>
              <span className="ml-2 text-sm text-gray-900">
                {new Date(soilSample.sample_date).toLocaleDateString()}
              </span>
            </div>
            {soilSample.lab_name && (
              <div>
                <span className="text-sm font-medium text-gray-500">Lab:</span>
                <span className="ml-2 text-sm text-gray-900">{soilSample.lab_name}</span>
              </div>
            )}
            {soilSample.lab_reference && (
              <div>
                <span className="text-sm font-medium text-gray-500">Lab Reference:</span>
                <span className="ml-2 text-sm text-gray-900">{soilSample.lab_reference}</span>
              </div>
            )}
          </div>
        </div>
      )}

      <div className="bg-white shadow rounded-lg overflow-hidden">
        <div className="p-6">
          <h2 className="text-lg font-medium text-gray-900 mb-4">Import Lab Results</h2>

          <div className="mb-6">
            <label htmlFor="fileFormat" className="block text-sm font-medium text-gray-700 mb-1">
              File Format
            </label>
            <select
              id="fileFormat"
              name="fileFormat"
              value={fileFormat}
              onChange={handleFormatChange}
              className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
            >
              <option value="csv">CSV</option>
              <option value="excel">Excel</option>
              <option value="json">JSON</option>
            </select>
            <p className="mt-1 text-sm text-gray-500">
              Select the format of your lab results file.
            </p>
          </div>

          <div className="mb-6">
            <label htmlFor="file" className="block text-sm font-medium text-gray-700 mb-1">
              Lab Results File
            </label>
            <input
              type="file"
              id="file"
              name="file"
              onChange={handleFileChange}
              accept={fileFormat === 'csv' ? '.csv' : fileFormat === 'excel' ? '.xlsx,.xls' : '.json'}
              className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
            />
            <p className="mt-1 text-sm text-gray-500">
              Upload a file containing soil test results from the lab.
            </p>
          </div>

          <div className="flex space-x-3 mb-6">
            <button
              type="button"
              onClick={handlePreview}
              disabled={loading || !file}
              className={`inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 ${(loading || !file) ? 'opacity-70 cursor-not-allowed' : ''}`}
            >
              {loading ? 'Processing...' : 'Preview Data'}
            </button>

            <button
              type="button"
              onClick={handleImport}
              disabled={loading || !file}
              className={`inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 ${(loading || !file) ? 'opacity-70 cursor-not-allowed' : ''}`}
            >
              {loading ? 'Importing...' : 'Import Results'}
            </button>
          </div>

          {previewData && (
            <div className="mt-6">
              <h3 className="text-lg font-medium text-gray-900 mb-2">Data Preview</h3>
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      {Object.keys(previewData).map((key) => (
                        <th
                          key={key}
                          scope="col"
                          className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                        >
                          {key}
                        </th>
                      ))}
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    <tr>
                      {Object.values(previewData).map((value: any, index) => (
                        <td key={index} className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {value !== null && value !== undefined ? value.toString() : 'N/A'}
                        </td>
                      ))}
                    </tr>
                  </tbody>
                </table>
              </div>
              <p className="mt-2 text-sm text-gray-500">
                This is a preview of the data that will be imported. Please verify that the data is correct before proceeding.
              </p>
            </div>
          )}
        </div>
      </div>
    </Layout>
  );
};

export default SoilTestResultImport;
