import { useState, useEffect, useContext } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import axios from 'axios';
import { AuthContext } from '../../context/AuthContext';
import { useFarm } from '../../context/FarmContext';
import Layout from '../../components/Layout';
import { API_URL } from '../../config';

interface SoilSample {
  id?: string;
  farm_id: string;
  field_id: string | null;
  sample_date: string;
  location: string;
  depth: string;
  lab_name: string;
  lab_reference: string;
  status: string;
  notes: string;
}

const SoilForm = () => {
  const { sampleId } = useParams<{ sampleId: string }>();
  const navigate = useNavigate();
  const isEditMode = !!sampleId;

  const [soilSample, setSoilSample] = useState<SoilSample>({
    farm_id: '',
    field_id: null,
    sample_date: new Date().toISOString().split('T')[0],
    location: '',
    depth: '',
    lab_name: '',
    lab_reference: '',
    status: 'pending',
    notes: ''
  });

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [fields, setFields] = useState<{ id: string; name: string }[]>([]);

  const { user } = useContext(AuthContext);
  const { selectedFarm } = useFarm();

  // Set the selected farm when component mounts or selectedFarm changes
  useEffect(() => {
    if (selectedFarm && !isEditMode) {
      setSoilSample(prev => ({ ...prev, farm_id: selectedFarm.id }));
      // Fetch fields for the selected farm
      fetchFields(selectedFarm.id);
    }
  }, [selectedFarm, isEditMode]);

  // Fetch fields for a farm
  const fetchFields = async (farmId: string) => {
    try {
      const response = await axios.get(`${API_URL}/fields/farm/${farmId}`);
      const farmFields = Array.isArray(response.data) ? response.data : response.data.fields || [];
      setFields(farmFields);
    } catch (err: any) {
      console.error('Error fetching fields:', err);
      setError('Failed to load fields. Please try again later.');
    }
  };

  // We no longer need handleFarmChange as we're using the global farm selector

  // Fetch soil sample data if in edit mode
  useEffect(() => {
    const fetchSoilSample = async () => {
      if (!sampleId) return;

      setLoading(true);
      setError(null);

      try {
        const response = await axios.get(`${API_URL}/soil/samples/${sampleId}`);
        const sampleData = response.data.soilSample;

        // Format the date for the input field
        const formattedData = {
          ...sampleData,
          sample_date: sampleData.sample_date ? 
            new Date(sampleData.sample_date).toISOString().split('T')[0] : 
            new Date().toISOString().split('T')[0]
        };

        setSoilSample(formattedData);

        // Fetch fields for the farm
        if (formattedData.farm_id) {
          fetchFields(formattedData.farm_id);
        }
      } catch (err: any) {
        console.error('Error fetching soil sample:', err);
        setError('Failed to load soil sample data. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    if (isEditMode) {
      fetchSoilSample();
    }
  }, [sampleId, isEditMode]);

  // Handle form input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setSoilSample(prev => ({ ...prev, [name]: value }));
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    setLoading(true);
    setError(null);

    try {
      // Validate required fields
      if (!selectedFarm) {
        setError('Please select a farm from the header dropdown.');
        setLoading(false);
        return;
      }

      if (!soilSample.sample_date || !soilSample.status) {
        setError('Sample date and status are required.');
        setLoading(false);
        return;
      }

      // Ensure farm_id is set from selectedFarm
      const sampleData = {
        ...soilSample,
        farm_id: selectedFarm.id
      };

      if (isEditMode) {
        // Update existing soil sample
        await axios.put(`${API_URL}/soil/samples/${sampleId}`, sampleData);
      } else {
        // Create new soil sample
        await axios.post(`${API_URL}/soil/samples`, sampleData);
      }

      // Redirect to soil samples list
      navigate('/soil');
    } catch (err: any) {
      console.error('Error saving soil sample:', err);
      setError('Failed to save soil sample. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Layout>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">
          {isEditMode ? 'Edit Soil Sample' : 'Add New Soil Sample'}
        </h1>
        <Link
          to="/soil"
          className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
        >
          Back to Soil Samples
        </Link>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">{error}</span>
        </div>
      )}

      <div className="bg-white shadow rounded-lg overflow-hidden">
        <form onSubmit={handleSubmit} className="p-6">
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
            {/* Farm Selection */}
            <div>
              <label htmlFor="farm_id" className="block text-sm font-medium text-gray-700 mb-1">
                Farm <span className="text-red-500">*</span>
              </label>
              <div className="p-2 border rounded bg-gray-50">
                {selectedFarm ? (
                  <span className="text-gray-700">{selectedFarm.name}</span>
                ) : (
                  <span className="text-gray-500 italic">Please select a farm from the header dropdown</span>
                )}
              </div>
              <p className="mt-2 text-sm text-gray-500">
                To change the farm, use the farm selector in the header.
              </p>
              <input
                type="hidden"
                id="farm_id"
                name="farm_id"
                value={selectedFarm?.id || ''}
              />
            </div>

            {/* Field Selection */}
            <div>
              <label htmlFor="field_id" className="block text-sm font-medium text-gray-700 mb-1">
                Field
              </label>
              <select
                id="field_id"
                name="field_id"
                value={soilSample.field_id || ''}
                onChange={handleChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              >
                <option value="">Select a field (optional)</option>
                {fields.map(field => (
                  <option key={field.id} value={field.id}>{field.name}</option>
                ))}
              </select>
            </div>

            {/* Sample Date */}
            <div>
              <label htmlFor="sample_date" className="block text-sm font-medium text-gray-700 mb-1">
                Sample Date <span className="text-red-500">*</span>
              </label>
              <input
                type="date"
                id="sample_date"
                name="sample_date"
                value={soilSample.sample_date}
                onChange={handleChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                required
              />
            </div>

            {/* Status */}
            <div>
              <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">
                Status <span className="text-red-500">*</span>
              </label>
              <select
                id="status"
                name="status"
                value={soilSample.status}
                onChange={handleChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                required
              >
                <option value="pending">Pending</option>
                <option value="received">Received</option>
                <option value="analyzed">Analyzed</option>
              </select>
            </div>

            {/* Location */}
            <div>
              <label htmlFor="location" className="block text-sm font-medium text-gray-700 mb-1">
                Location
              </label>
              <input
                type="text"
                id="location"
                name="location"
                value={soilSample.location}
                onChange={handleChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                placeholder="e.g., North corner, GPS coordinates"
              />
            </div>

            {/* Depth */}
            <div>
              <label htmlFor="depth" className="block text-sm font-medium text-gray-700 mb-1">
                Depth
              </label>
              <input
                type="text"
                id="depth"
                name="depth"
                value={soilSample.depth}
                onChange={handleChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                placeholder="e.g., 0-6 inches, 15-30 cm"
              />
            </div>

            {/* Lab Name */}
            <div>
              <label htmlFor="lab_name" className="block text-sm font-medium text-gray-700 mb-1">
                Lab Name
              </label>
              <input
                type="text"
                id="lab_name"
                name="lab_name"
                value={soilSample.lab_name}
                onChange={handleChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                placeholder="e.g., AgriLab, State University Extension"
              />
            </div>

            {/* Lab Reference */}
            <div>
              <label htmlFor="lab_reference" className="block text-sm font-medium text-gray-700 mb-1">
                Lab Reference
              </label>
              <input
                type="text"
                id="lab_reference"
                name="lab_reference"
                value={soilSample.lab_reference}
                onChange={handleChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                placeholder="e.g., Sample ID, Lab reference number"
              />
            </div>
          </div>

          {/* Notes */}
          <div className="mt-6">
            <label htmlFor="notes" className="block text-sm font-medium text-gray-700 mb-1">
              Notes
            </label>
            <textarea
              id="notes"
              name="notes"
              value={soilSample.notes}
              onChange={handleChange}
              rows={4}
              className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              placeholder="Additional information about the soil sample"
            ></textarea>
          </div>

          {/* Form Actions */}
          <div className="mt-6 flex justify-end space-x-3">
            <Link
              to="/soil"
              className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              Cancel
            </Link>
            <button
              type="submit"
              disabled={loading}
              className={`inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 ${loading ? 'opacity-70 cursor-not-allowed' : ''}`}
            >
              {loading ? 'Saving...' : 'Save Soil Sample'}
            </button>
          </div>
        </form>
      </div>
    </Layout>
  );
};

export default SoilForm;
