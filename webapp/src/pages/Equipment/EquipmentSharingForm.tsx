import { useState, useEffect, useContext } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import axios from 'axios';
import { AuthContext } from '../../context/AuthContext';
import { useFarm } from '../../context/FarmContext';
import Layout from '../../components/Layout';
import { API_URL } from '../../config';

interface Farm {
  id: string;
  name: string;
}

interface Equipment {
  id: string;
  name: string;
  type: string;
  manufacturer: string;
  model: string;
  year: number;
}

interface EquipmentSharing {
  id: string;
  equipment_id: string;
  owner_farm_id: string;
  renter_farm_id: string;
  start_date: string;
  end_date: string;
  rental_cost: number | null;
  rental_cost_type: string | null;
  status: string;
  notes: string | null;
}

const EquipmentSharingForm = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const isEditMode = !!id;

  const [formData, setFormData] = useState<Partial<EquipmentSharing>>({
    equipment_id: '',
    owner_farm_id: '',
    renter_farm_id: '',
    start_date: '',
    end_date: '',
    rental_cost: null,
    rental_cost_type: 'flat',
    notes: ''
  });

  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [farms, setFarms] = useState<Farm[]>([]);
  const [equipment, setEquipment] = useState<Equipment[]>([]);
  const [sharingMode, setSharingMode] = useState<'share' | 'rent'>('share');

  const { user } = useContext(AuthContext);
  const { selectedFarm } = useFarm();

  // Fetch farms
  useEffect(() => {
    const fetchFarms = async () => {
      try {
        const response = await axios.get(`${API_URL}/farms/user/${user?.id}`);
        // Check if response.data is an array or has a farms property
        const farmsData = Array.isArray(response.data) ? response.data : response.data.farms || [];
        setFarms(farmsData);

        // Use the global selectedFarm if not in edit mode
        if (selectedFarm && !isEditMode) {
          if (sharingMode === 'share') {
            setFormData(prev => ({ ...prev, owner_farm_id: selectedFarm.id }));
          } else {
            setFormData(prev => ({ ...prev, renter_farm_id: selectedFarm.id }));
          }
        }
      } catch (err: any) {
        console.error('Error fetching farms:', err);
        setError('Failed to load farms. Please try again later.');
      }
    };

    if (user) {
      fetchFarms();
    }
  }, [user, selectedFarm, isEditMode, sharingMode]);

  // Fetch equipment for the selected farm if in share mode
  useEffect(() => {
    const fetchEquipment = async () => {
      if (!selectedFarm || sharingMode !== 'share') return;

      try {
        const response = await axios.get(`${API_URL}/equipment/farm/${selectedFarm.id}`);
        setEquipment(Array.isArray(response.data) ? response.data : response.data.equipment || []);
      } catch (err: any) {
        console.error('Error fetching equipment:', err);
        setError('Failed to load equipment. Please try again later.');
      }
    };

    fetchEquipment();
  }, [selectedFarm, sharingMode]);

  // Fetch sharing record if in edit mode
  useEffect(() => {
    const fetchSharingRecord = async () => {
      if (!id) {
        setLoading(false);
        return;
      }

      try {
        const response = await axios.get(`${API_URL}/equipment-sharing/${id}`);
        const record = response.data.sharingRecord;

        setFormData({
          equipment_id: record.equipment_id,
          owner_farm_id: record.owner_farm_id,
          renter_farm_id: record.renter_farm_id,
          start_date: record.start_date.split('T')[0], // Format date for input
          end_date: record.end_date.split('T')[0], // Format date for input
          rental_cost: record.rental_cost,
          rental_cost_type: record.rental_cost_type || 'flat',
          notes: record.notes
        });

        // Determine sharing mode
        const userFarms = farms.map(farm => farm.id);
        if (userFarms.includes(record.owner_farm_id)) {
          setSharingMode('share');
        } else {
          setSharingMode('rent');
        }
        // Note: We're using the global farm selector, so we don't need to set selectedFarm
      } catch (err: any) {
        console.error('Error fetching sharing record:', err);
        setError('Failed to load sharing record. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    if (isEditMode) {
      fetchSharingRecord();
    } else {
      setLoading(false);
    }
  }, [id, isEditMode, farms]);

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;

    if (type === 'number') {
      setFormData({ ...formData, [name]: value === '' ? null : parseFloat(value) });
    } else {
      setFormData({ ...formData, [name]: value });
    }
  };

  // No longer need handleFarmChange as we're using the global farm selector

  // Handle sharing mode change
  const handleSharingModeChange = (mode: 'share' | 'rent') => {
    setSharingMode(mode);

    // Reset form data based on mode
    if (mode === 'share' && selectedFarm) {
      setFormData({
        ...formData,
        owner_farm_id: selectedFarm.id,
        renter_farm_id: '',
        equipment_id: ''
      });
    } else if (mode === 'rent' && selectedFarm) {
      setFormData({
        ...formData,
        owner_farm_id: '',
        renter_farm_id: selectedFarm.id,
        equipment_id: ''
      });
    }
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate form
    if (!formData.equipment_id) {
      setError('Please select equipment');
      return;
    }

    if (!formData.owner_farm_id) {
      setError('Please select owner farm');
      return;
    }

    if (!formData.renter_farm_id) {
      setError('Please select renter farm');
      return;
    }

    if (formData.owner_farm_id === formData.renter_farm_id) {
      setError('Owner and renter farms must be different');
      return;
    }

    if (!formData.start_date) {
      setError('Please select start date');
      return;
    }

    if (!formData.end_date) {
      setError('Please select end date');
      return;
    }

    // Validate dates
    const startDate = new Date(formData.start_date);
    const endDate = new Date(formData.end_date);
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    if (startDate < today) {
      setError('Start date cannot be in the past');
      return;
    }

    if (endDate < startDate) {
      setError('End date must be after start date');
      return;
    }

    setSubmitting(true);
    setError(null);

    try {
      const payload = {
        equipmentId: formData.equipment_id,
        ownerFarmId: formData.owner_farm_id,
        renterFarmId: formData.renter_farm_id,
        startDate: formData.start_date,
        endDate: formData.end_date,
        rentalCost: formData.rental_cost,
        rentalCostType: formData.rental_cost_type,
        notes: formData.notes
      };

      if (isEditMode) {
        await axios.put(`${API_URL}/equipment-sharing/${id}`, payload);
      } else {
        await axios.post(`${API_URL}/equipment-sharing`, payload);
      }

      navigate('/equipment-sharing');
    } catch (err: any) {
      console.error('Error saving equipment sharing:', err);

      if (err.response && err.response.data && err.response.data.error) {
        setError(err.response.data.error);
      } else {
        setError('Failed to save equipment sharing. Please try again later.');
      }
    } finally {
      setSubmitting(false);
    }
  };

  if (loading) {
    return (
      <Layout>
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
          <p className="ml-3 text-gray-500">Loading...</p>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">
          {isEditMode ? 'Edit Equipment Sharing' : 'Share Equipment'}
        </h1>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">{error}</span>
        </div>
      )}

      {!isEditMode && (
        <div className="mb-6">
          <div className="flex border-b border-gray-200">
            <button
              className={`py-2 px-4 text-sm font-medium ${sharingMode === 'share' ? 'border-b-2 border-primary-500 text-primary-600' : 'text-gray-500 hover:text-gray-700'}`}
              onClick={() => handleSharingModeChange('share')}
            >
              I want to share my equipment
            </button>
            <button
              className={`py-2 px-4 text-sm font-medium ${sharingMode === 'rent' ? 'border-b-2 border-primary-500 text-primary-600' : 'text-gray-500 hover:text-gray-700'}`}
              onClick={() => handleSharingModeChange('rent')}
            >
              I want to rent equipment
            </button>
          </div>
        </div>
      )}

      <form onSubmit={handleSubmit} className="bg-white shadow-md rounded px-8 pt-6 pb-8 mb-4">
        <div className="mb-6">
          <label className="block text-gray-700 text-sm font-bold mb-2">
            {sharingMode === 'share' ? 'Your Farm (Owner)' : 'Your Farm (Renter)'}
          </label>
          <div className="p-2 border rounded bg-gray-50">
            {selectedFarm ? (
              <span className="text-gray-700">{selectedFarm.name}</span>
            ) : (
              <span className="text-gray-500 italic">Please select a farm from the header dropdown</span>
            )}
          </div>
          <p className="mt-2 text-sm text-gray-500">
            To change the farm, use the farm selector in the header.
          </p>
        </div>

        {sharingMode === 'share' && (
          <div className="mb-6">
            <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="equipment_id">
              Equipment to Share
            </label>
            <select
              id="equipment_id"
              name="equipment_id"
              className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
              value={formData.equipment_id || ''}
              onChange={handleInputChange}
              disabled={isEditMode}
            >
              <option value="" disabled>Select equipment</option>
              {equipment.map(item => (
                <option key={item.id} value={item.id}>
                  {item.name} {item.manufacturer && `- ${item.manufacturer}`} {item.model && `(${item.model})`} {item.year && `(${item.year})`}
                </option>
              ))}
            </select>
          </div>
        )}

        {sharingMode === 'share' && (
          <div className="mb-6">
            <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="renter_farm_id">
              Renter Farm
            </label>
            <select
              id="renter_farm_id"
              name="renter_farm_id"
              className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
              value={formData.renter_farm_id || ''}
              onChange={handleInputChange}
            >
              <option value="" disabled>Select renter farm</option>
              {farms
                .filter(farm => selectedFarm && farm.id !== selectedFarm.id)
                .map(farm => (
                  <option key={farm.id} value={farm.id}>{farm.name}</option>
                ))
              }
            </select>
          </div>
        )}

        {sharingMode === 'rent' && (
          <div className="mb-6">
            <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="owner_farm_id">
              Owner Farm
            </label>
            <select
              id="owner_farm_id"
              name="owner_farm_id"
              className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
              value={formData.owner_farm_id || ''}
              onChange={handleInputChange}
            >
              <option value="" disabled>Select owner farm</option>
              {farms
                .filter(farm => selectedFarm && farm.id !== selectedFarm.id)
                .map(farm => (
                  <option key={farm.id} value={farm.id}>{farm.name}</option>
                ))
              }
            </select>
          </div>
        )}

        {sharingMode === 'rent' && formData.owner_farm_id && (
          <div className="mb-6">
            <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="equipment_id">
              Equipment to Rent
            </label>
            <select
              id="equipment_id"
              name="equipment_id"
              className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
              value={formData.equipment_id || ''}
              onChange={handleInputChange}
              disabled={isEditMode}
            >
              <option value="" disabled>Select equipment</option>
              {equipment.map(item => (
                <option key={item.id} value={item.id}>
                  {item.name} {item.manufacturer && `- ${item.manufacturer}`} {item.model && `(${item.model})`} {item.year && `(${item.year})`}
                </option>
              ))}
            </select>
            {sharingMode === 'rent' && formData.owner_farm_id && equipment.length === 0 && (
              <p className="text-sm text-gray-500 mt-2">
                No equipment available from this farm. Please select another farm.
              </p>
            )}
          </div>
        )}

        <div className="mb-6 flex space-x-4">
          <div className="w-1/2">
            <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="start_date">
              Start Date
            </label>
            <input
              id="start_date"
              name="start_date"
              type="date"
              className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
              value={formData.start_date || ''}
              onChange={handleInputChange}
              min={new Date().toISOString().split('T')[0]} // Set min to today
            />
          </div>
          <div className="w-1/2">
            <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="end_date">
              End Date
            </label>
            <input
              id="end_date"
              name="end_date"
              type="date"
              className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
              value={formData.end_date || ''}
              onChange={handleInputChange}
              min={formData.start_date || new Date().toISOString().split('T')[0]} // Set min to start date or today
            />
          </div>
        </div>

        <div className="mb-6 flex space-x-4">
          <div className="w-1/2">
            <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="rental_cost">
              Rental Cost
            </label>
            <input
              id="rental_cost"
              name="rental_cost"
              type="number"
              step="0.01"
              min="0"
              className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
              value={formData.rental_cost === null ? '' : formData.rental_cost}
              onChange={handleInputChange}
              placeholder="0.00"
            />
          </div>
          <div className="w-1/2">
            <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="rental_cost_type">
              Cost Type
            </label>
            <select
              id="rental_cost_type"
              name="rental_cost_type"
              className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
              value={formData.rental_cost_type || 'flat'}
              onChange={handleInputChange}
            >
              <option value="flat">Flat Rate</option>
              <option value="daily">Per Day</option>
              <option value="hourly">Per Hour</option>
              <option value="per_acre">Per Acre</option>
            </select>
          </div>
        </div>

        <div className="mb-6">
          <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="notes">
            Notes
          </label>
          <textarea
            id="notes"
            name="notes"
            className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
            value={formData.notes || ''}
            onChange={handleInputChange}
            rows={4}
            placeholder="Add any additional details or requirements..."
          ></textarea>
        </div>

        <div className="flex items-center justify-between">
          <button
            type="button"
            className="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
            onClick={() => navigate('/equipment-sharing')}
          >
            Cancel
          </button>
          <button
            type="submit"
            className="bg-primary-500 hover:bg-primary-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
            disabled={submitting}
          >
            {submitting ? 'Saving...' : isEditMode ? 'Update' : 'Create'}
          </button>
        </div>
      </form>
    </Layout>
  );
};

export default EquipmentSharingForm;
