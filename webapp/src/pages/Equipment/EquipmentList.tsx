import { useState, useEffect, useContext } from 'react';
import { Link } from 'react-router-dom';
import axios from 'axios';
import { AuthContext } from '../../context/AuthContext';
import { useFarm } from '../../context/FarmContext';
import Layout from '../../components/Layout';
import { API_URL } from '../../config';

interface Equipment {
  id: string;
  name: string;
  type: string;
  manufacturer: string;
  model: string;
  year: number;
  purchase_date: string;
  purchase_cost: number;
  current_value: number;
  status: string;
  notes: string;
}

const EquipmentList = () => {
  const [equipment, setEquipment] = useState<Equipment[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const { user } = useContext(AuthContext);
  const { currentFarm } = useFarm();

  // Fetch equipment for the selected farm
  useEffect(() => {
    const fetchEquipment = async () => {
      if (!currentFarm) return;

      setLoading(true);
      setError(null);

      try {
        const response = await axios.get(`${API_URL}/equipment/farm/${currentFarm.id}`);
        setEquipment(Array.isArray(response.data) ? response.data : response.data.equipment || []);
      } catch (err: any) {
        console.error('Error fetching equipment:', err);
        setError('Failed to load equipment. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchEquipment();
  }, [currentFarm]);

  // Farm selection is now handled by the global farm selector in the header

  // Handle equipment deletion
  const handleDeleteEquipment = async (equipmentId: string) => {
    if (!window.confirm('Are you sure you want to delete this equipment?')) {
      return;
    }

    try {
      await axios.delete(`${API_URL}/equipment/${equipmentId}`);
      setEquipment(equipment.filter(item => item.id !== equipmentId));
    } catch (err: any) {
      console.error('Error deleting equipment:', err);
      setError('Failed to delete equipment. Please try again later.');
    }
  };

  // Create a product from equipment
  const handleCreateProduct = async (equipmentId: string) => {
    try {
      const response = await axios.post(`${API_URL}/products/from-equipment/${equipmentId}`, {
        price: 0, // Default price, can be updated later
        unit: 'unit' // Default unit for equipment
      });

      alert('Product created successfully!');
    } catch (err: any) {
      console.error('Error creating product from equipment:', err);
      setError('Failed to create product. Please try again later.');
    }
  };

  // Format currency
  const formatCurrency = (amount: number | null) => {
    if (amount === null) return 'N/A';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  // Format date
  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <Layout>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Equipment Management</h1>
        <div>
          {/* Note: Farm selector removed - using global farm selector in header */}
          <Link
            to="/equipment/new"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Add Equipment
          </Link>
        </div>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">{error}</span>
        </div>
      )}

      {loading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
          <p className="ml-3 text-gray-500">Loading equipment...</p>
        </div>
      ) : equipment.length === 0 ? (
        <div className="bg-white shadow rounded-lg p-8 text-center">
          <p className="text-gray-500 mb-4">No equipment found for this farm.</p>
          <p className="text-sm text-gray-400 mb-6">
            Add your first equipment to start tracking your machinery, tools, and other assets.
          </p>
          <Link
            to="/equipment/new"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Add Equipment
          </Link>
        </div>
      ) : (
        <div className="bg-white shadow overflow-hidden sm:rounded-md">
          <ul className="divide-y divide-gray-200">
            {equipment.map((item) => (
              <li key={item.id}>
                <div className="px-4 py-4 sm:px-6">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className="ml-3">
                        <p className="text-sm font-medium text-primary-600 truncate">
                          {item.name}
                        </p>
                        <p className="text-sm text-gray-500">
                          {item.manufacturer && <span className="mr-2">{item.manufacturer}</span>}
                          {item.model && <span className="mr-2">Model: {item.model}</span>}
                          {item.year && <span className="mr-2">Year: {item.year}</span>}
                        </p>
                      </div>
                    </div>
                    <div className="flex space-x-2">
                      <button
                        onClick={() => handleCreateProduct(item.id)}
                        className="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                      >
                        Create Product
                      </button>
                      <Link
                        to={`/equipment/${item.id}`}
                        className="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                      >
                        View
                      </Link>
                      <Link
                        to={`/equipment/${item.id}/edit`}
                        className="inline-flex items-center px-3 py-1 border border-gray-300 text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                      >
                        Edit
                      </Link>
                      <button
                        onClick={() => handleDeleteEquipment(item.id)}
                        className="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                      >
                        Delete
                      </button>
                    </div>
                  </div>
                  <div className="mt-2 sm:flex sm:justify-between">
                    <div className="sm:flex">
                      {item.purchase_date && (
                        <p className="flex items-center text-sm text-gray-500 mr-6">
                          <span>Purchased: {formatDate(item.purchase_date)}</span>
                        </p>
                      )}
                      {item.purchase_cost && (
                        <p className="mt-2 flex items-center text-sm text-gray-500 sm:mt-0 mr-6">
                          <span>Cost: {formatCurrency(item.purchase_cost)}</span>
                        </p>
                      )}
                      {item.current_value && (
                        <p className="mt-2 flex items-center text-sm text-gray-500 sm:mt-0">
                          <span>Current Value: {formatCurrency(item.current_value)}</span>
                        </p>
                      )}
                    </div>
                    {item.status && (
                      <div className="mt-2 flex items-center text-sm text-gray-500 sm:mt-0">
                        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                          item.status === 'active' ? 'bg-green-100 text-green-800' : 
                          item.status === 'maintenance' ? 'bg-yellow-100 text-yellow-800' : 
                          item.status === 'retired' ? 'bg-red-100 text-red-800' : 
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {item.status.charAt(0).toUpperCase() + item.status.slice(1)}
                        </span>
                      </div>
                    )}
                  </div>
                  {item.notes && (
                    <div className="mt-2 text-sm text-gray-500">
                      <p>{item.notes}</p>
                    </div>
                  )}
                </div>
              </li>
            ))}
          </ul>
        </div>
      )}
    </Layout>
  );
};

export default EquipmentList;
