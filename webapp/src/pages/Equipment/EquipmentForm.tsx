import { useState, useEffect, useContext } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import axios from 'axios';
import { AuthContext } from '../../context/AuthContext';
import { useFarm } from '../../context/FarmContext';
import Layout from '../../components/Layout';
import { API_URL } from '../../config';

interface Equipment {
  id?: string;
  farm_id: string;
  name: string;
  type: string;
  manufacturer: string;
  model: string;
  year: number | null;
  purchase_date: string;
  purchase_cost: number | null;
  current_value: number | null;
  status: string;
  notes: string;
}

const EquipmentForm = () => {
  const { equipmentId } = useParams<{ equipmentId: string }>();
  const navigate = useNavigate();
  const isEditMode = !!equipmentId;

  const [equipment, setEquipment] = useState<Equipment>({
    farm_id: '',
    name: '',
    type: '',
    manufacturer: '',
    model: '',
    year: null,
    purchase_date: new Date().toISOString().split('T')[0],
    purchase_cost: null,
    current_value: null,
    status: 'active',
    notes: ''
  });

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { user } = useContext(AuthContext);
  const { currentFarm } = useFarm();

  // Console log for debugging
  useEffect(() => {
    console.log("EquipmentForm rendered", { user, currentFarm, equipment });
  }, [user, currentFarm, equipment]);

  // Set farm_id from currentFarm
  useEffect(() => {
    if (currentFarm && !isEditMode) {
      setEquipment(prev => ({ ...prev, farm_id: currentFarm.id }));
    }
  }, [currentFarm, isEditMode]);

  // Fetch equipment data if in edit mode
  useEffect(() => {
    const fetchEquipment = async () => {
      if (!equipmentId) return;

      setLoading(true);
      setError(null);

      try {
        const response = await axios.get(`${API_URL}/equipment/${equipmentId}`);

        // Format the date for the input field
        const formattedData = {
          ...response.data,
          purchase_date: response.data.purchase_date ? 
            new Date(response.data.purchase_date).toISOString().split('T')[0] : 
            ''
        };

        setEquipment(formattedData);
      } catch (err: any) {
        console.error('Error fetching equipment:', err);
        setError('Failed to load equipment data. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    if (isEditMode) {
      fetchEquipment();
    }
  }, [equipmentId, isEditMode]);

  // Handle form input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setEquipment(prev => ({ ...prev, [name]: value }));
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    setLoading(true);
    setError(null);

    try {
      // Validate required fields
      if (!currentFarm) {
        setError('Please select a farm from the header dropdown.');
        setLoading(false);
        return;
      }

      if (!equipment.name) {
        setError('Equipment name is required.');
        setLoading(false);
        return;
      }

      if (isEditMode) {
        // Update existing equipment
        await axios.put(`${API_URL}/equipment/${equipmentId}`, equipment);
      } else {
        // Create new equipment
        await axios.post(`${API_URL}/equipment`, equipment);
      }

      // Redirect to equipment list
      navigate('/equipment');
    } catch (err: any) {
      console.error('Error saving equipment:', err);
      setError('Failed to save equipment. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Layout>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">
          {isEditMode ? 'Edit Equipment' : 'Add New Equipment'}
        </h1>
        <Link
          to="/equipment"
          className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
        >
          Back to Equipment
        </Link>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">{error}</span>
        </div>
      )}

      <div className="bg-white shadow rounded-lg overflow-hidden">
        <form onSubmit={handleSubmit} className="p-6">
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
            {/* Farm information - using global farm selector */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Farm <span className="text-red-500">*</span>
              </label>
              <div className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-gray-50 text-gray-700 sm:text-sm">
                {currentFarm ? currentFarm.name : 'Please select a farm from the header dropdown'}
              </div>
              <input type="hidden" name="farm_id" value={equipment.farm_id} />
            </div>

            {/* Equipment Name */}
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                Equipment Name <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                id="name"
                name="name"
                value={equipment.name}
                onChange={handleChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                required
              />
            </div>

            {/* Equipment Type */}
            <div>
              <label htmlFor="type" className="block text-sm font-medium text-gray-700 mb-1">
                Type
              </label>
              <select
                id="type"
                name="type"
                value={equipment.type}
                onChange={handleChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              >
                <option value="">Select a type</option>
                <option value="tractor">Tractor</option>
                <option value="harvester">Harvester</option>
                <option value="plow">Plow</option>
                <option value="seeder">Seeder</option>
                <option value="sprayer">Sprayer</option>
                <option value="trailer">Trailer</option>
                <option value="truck">Truck</option>
                <option value="tool">Tool</option>
                <option value="other">Other</option>
              </select>
            </div>

            {/* Manufacturer */}
            <div>
              <label htmlFor="manufacturer" className="block text-sm font-medium text-gray-700 mb-1">
                Manufacturer
              </label>
              <input
                type="text"
                id="manufacturer"
                name="manufacturer"
                value={equipment.manufacturer}
                onChange={handleChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              />
            </div>

            {/* Model */}
            <div>
              <label htmlFor="model" className="block text-sm font-medium text-gray-700 mb-1">
                Model
              </label>
              <input
                type="text"
                id="model"
                name="model"
                value={equipment.model}
                onChange={handleChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              />
            </div>

            {/* Year */}
            <div>
              <label htmlFor="year" className="block text-sm font-medium text-gray-700 mb-1">
                Year
              </label>
              <input
                type="number"
                id="year"
                name="year"
                value={equipment.year === null ? '' : equipment.year}
                onChange={handleChange}
                min="1900"
                max={new Date().getFullYear()}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              />
            </div>

            {/* Purchase Date */}
            <div>
              <label htmlFor="purchase_date" className="block text-sm font-medium text-gray-700 mb-1">
                Purchase Date
              </label>
              <input
                type="date"
                id="purchase_date"
                name="purchase_date"
                value={equipment.purchase_date}
                onChange={handleChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              />
            </div>

            {/* Purchase Cost */}
            <div>
              <label htmlFor="purchase_cost" className="block text-sm font-medium text-gray-700 mb-1">
                Purchase Cost
              </label>
              <div className="relative rounded-md shadow-sm">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <span className="text-gray-500 sm:text-sm">$</span>
                </div>
                <input
                  type="number"
                  id="purchase_cost"
                  name="purchase_cost"
                  value={equipment.purchase_cost === null ? '' : equipment.purchase_cost}
                  onChange={handleChange}
                  step="0.01"
                  min="0"
                  className="block w-full pl-7 pr-12 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                  placeholder="0.00"
                />
                <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                  <span className="text-gray-500 sm:text-sm">USD</span>
                </div>
              </div>
            </div>

            {/* Current Value */}
            <div>
              <label htmlFor="current_value" className="block text-sm font-medium text-gray-700 mb-1">
                Current Value
              </label>
              <div className="relative rounded-md shadow-sm">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <span className="text-gray-500 sm:text-sm">$</span>
                </div>
                <input
                  type="number"
                  id="current_value"
                  name="current_value"
                  value={equipment.current_value === null ? '' : equipment.current_value}
                  onChange={handleChange}
                  step="0.01"
                  min="0"
                  className="block w-full pl-7 pr-12 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                  placeholder="0.00"
                />
                <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                  <span className="text-gray-500 sm:text-sm">USD</span>
                </div>
              </div>
            </div>

            {/* Status */}
            <div>
              <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">
                Status
              </label>
              <select
                id="status"
                name="status"
                value={equipment.status}
                onChange={handleChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              >
                <option value="active">Active</option>
                <option value="maintenance">Maintenance</option>
                <option value="retired">Retired</option>
                <option value="sold">Sold</option>
                <option value="inactive">Inactive</option>
              </select>
            </div>
          </div>

          {/* Notes */}
          <div className="mt-6">
            <label htmlFor="notes" className="block text-sm font-medium text-gray-700 mb-1">
              Notes
            </label>
            <textarea
              id="notes"
              name="notes"
              value={equipment.notes}
              onChange={handleChange}
              rows={4}
              className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
            ></textarea>
          </div>

          {/* Form Actions */}
          <div className="mt-6 flex justify-end space-x-3">
            <Link
              to="/equipment"
              className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              Cancel
            </Link>
            <button
              type="submit"
              disabled={loading}
              className={`inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 ${loading ? 'opacity-70 cursor-not-allowed' : ''}`}
            >
              {loading ? 'Saving...' : 'Save Equipment'}
            </button>
          </div>
        </form>
      </div>
    </Layout>
  );
};

export default EquipmentForm;
