import { Link } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';

const MaintenancePage = () => {
  const { user } = useAuth();

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8 bg-white p-8 rounded-lg shadow-card text-center">
        <div className="flex flex-col items-center">
          <img 
            src="/logo.svg" 
            alt="nxtAcre Logo" 
            className="h-16 mb-6" 
            onError={(e) => {
              e.currentTarget.style.display = 'none';
            }}
          />
          <svg 
            xmlns="http://www.w3.org/2000/svg" 
            className="h-24 w-24 text-primary-600" 
            fill="none" 
            viewBox="0 0 24 24" 
            stroke="currentColor"
          >
            <path 
              strokeLinecap="round" 
              strokeLinejoin="round" 
              strokeWidth={2} 
              d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" 
            />
            <path 
              strokeLinecap="round" 
              strokeLinejoin="round" 
              strokeWidth={2} 
              d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" 
            />
          </svg>
          <h2 className="mt-4 text-3xl font-display font-bold text-gray-900">
            System Maintenance
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600">
            We're currently performing scheduled maintenance on our system. We'll be back shortly.
          </p>
          <p className="mt-4 text-center text-sm text-gray-500">
            Estimated completion time: <span className="font-semibold">2 hours</span>
          </p>
        </div>

        <div className="mt-8">
          <Link
            to={user ? "/dashboard" : "/login"}
            className="btn btn-primary w-full"
          >
            {user ? "Go to Dashboard" : "Go to Login"}
          </Link>
        </div>
      </div>
    </div>
  );
};

export default MaintenancePage;
