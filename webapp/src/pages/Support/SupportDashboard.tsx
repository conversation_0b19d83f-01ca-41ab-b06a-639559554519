import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import axios from 'axios';
import { API_URL } from '../../config';
import { useAuth } from '../../context/AuthContext';
import Layout from '../../components/Layout';

interface SupportTicket {
  id: string;
  subject: string;
  status: string;
  priority: string;
  createdAt: string;
  updatedAt: string;
}

interface HelpGuide {
  id: string;
  title: string;
  slug: string;
  category: string;
}

const SupportDashboard: React.FC = () => {
  const { token } = useAuth();
  const [recentTickets, setRecentTickets] = useState<SupportTicket[]>([]);
  const [popularGuides, setPopularGuides] = useState<HelpGuide[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Fetch recent tickets
        const ticketsResponse = await axios.get(`${API_URL}/support?limit=5`, {
          headers: { Authorization: `Bearer ${token}` }
        });

        // Fetch popular help guides
        const guidesResponse = await axios.get(`${API_URL}/help/guides?limit=5`);

        setRecentTickets(ticketsResponse.data.tickets || []);
        setPopularGuides(guidesResponse.data.guides || []);
      } catch (err: any) {
        console.error('Error fetching support dashboard data:', err);
        setError(err.response?.data?.error || 'Failed to load support dashboard data');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [token]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'open':
        return 'bg-blue-100 text-blue-800';
      case 'in_progress':
        return 'bg-yellow-100 text-yellow-800';
      case 'resolved':
        return 'bg-green-100 text-green-800';
      case 'closed':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'low':
        return 'bg-green-100 text-green-800';
      case 'medium':
        return 'bg-blue-100 text-blue-800';
      case 'high':
        return 'bg-orange-100 text-orange-800';
      case 'urgent':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <Layout>
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-between items-center mb-8">
          <h1 className="text-2xl font-bold">Support & Help Center</h1>
          <Link 
            to="/support/tickets/new" 
            className="btn btn-primary"
          >
            Create Support Ticket
          </Link>
        </div>

        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            {error}
          </div>
        )}

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {/* Support Tickets Section */}
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold">Recent Support Tickets</h2>
              <Link to="/support/tickets" className="text-primary-600 hover:text-primary-800">
                View All
              </Link>
            </div>

            {loading ? (
              <div className="flex justify-center items-center h-40">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
              </div>
            ) : recentTickets.length > 0 ? (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Subject
                      </th>
                      <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                      <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Priority
                      </th>
                      <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Date
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {recentTickets.map((ticket) => (
                      <tr key={ticket.id} className="hover:bg-gray-50">
                        <td className="px-4 py-3 whitespace-nowrap">
                          <Link to={`/support/tickets/${ticket.id}`} className="text-primary-600 hover:text-primary-800">
                            {ticket.subject}
                          </Link>
                        </td>
                        <td className="px-4 py-3 whitespace-nowrap">
                          <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(ticket.status)}`}>
                            {ticket.status.replace('_', ' ')}
                          </span>
                        </td>
                        <td className="px-4 py-3 whitespace-nowrap">
                          <span className={`px-2 py-1 text-xs rounded-full ${getPriorityColor(ticket.priority)}`}>
                            {ticket.priority}
                          </span>
                        </td>
                        <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                          {new Date(ticket.createdAt).toLocaleDateString()}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <div className="text-center py-8">
                <p className="text-gray-500">No support tickets found.</p>
                <Link 
                  to="/support/tickets/new" 
                  className="mt-4 inline-block text-primary-600 hover:text-primary-800"
                >
                  Create your first ticket
                </Link>
              </div>
            )}
          </div>

          {/* Help Guides Section */}
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold">Popular Help Guides</h2>
              <Link to="/help" className="text-primary-600 hover:text-primary-800">
                View All
              </Link>
            </div>

            {loading ? (
              <div className="flex justify-center items-center h-40">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
              </div>
            ) : popularGuides.length > 0 ? (
              <ul className="space-y-4">
                {popularGuides.map((guide) => (
                  <li key={guide.id} className="border-b border-gray-200 pb-4 last:border-b-0">
                    <Link 
                      to={`/help/guides/${guide.slug}`} 
                      className="block hover:bg-gray-50 p-2 -mx-2 rounded"
                    >
                      <h3 className="font-medium text-primary-600">{guide.title}</h3>
                      <p className="text-sm text-gray-500 mt-1">{guide.category}</p>
                    </Link>
                  </li>
                ))}
              </ul>
            ) : (
              <div className="text-center py-8">
                <p className="text-gray-500">No help guides found.</p>
              </div>
            )}
          </div>
        </div>

        {/* Quick Links Section */}
        <div className="mt-8 bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold mb-4">Quick Links</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Link 
              to="/help/guides/getting-started" 
              className="bg-blue-50 hover:bg-blue-100 p-4 rounded-lg flex flex-col items-center text-center"
            >
              <svg className="w-8 h-8 text-blue-500 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
              <h3 className="font-medium">Getting Started</h3>
              <p className="text-sm text-gray-500 mt-1">New to nxtAcre? Start here!</p>
            </Link>

            <Link 
              to="/help/guides/using-support-system" 
              className="bg-green-50 hover:bg-green-100 p-4 rounded-lg flex flex-col items-center text-center"
            >
              <svg className="w-8 h-8 text-green-500 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
              </svg>
              <h3 className="font-medium">Using Support</h3>
              <p className="text-sm text-gray-500 mt-1">Learn how to use the support system</p>
            </Link>

            <Link 
              to="/faq" 
              className="bg-purple-50 hover:bg-purple-100 p-4 rounded-lg flex flex-col items-center text-center"
            >
              <svg className="w-8 h-8 text-purple-500 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <h3 className="font-medium">FAQ</h3>
              <p className="text-sm text-gray-500 mt-1">Frequently asked questions</p>
            </Link>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default SupportDashboard;
