import React, { useState, useEffect } from 'react';
import { Link, useParams, useNavigate } from 'react-router-dom';
import axios from 'axios';
import { API_URL } from '../../config';
import { useAuth } from '../../context/AuthContext';
import Layout from '../../components/Layout';

interface Comment {
  id: string;
  content: string;
  author: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
  };
  isInternal: boolean;
  createdAt: string;
  updatedAt: string;
}

interface Attachment {
  id: string;
  filename: string;
  fileSize: number;
  fileType: string;
  uploader: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
  };
  createdAt: string;
}

interface SupportTicket {
  id: string;
  subject: string;
  description: string;
  status: string;
  priority: string;
  category: string;
  creator: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
  };
  assignee?: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
  };
  farm?: {
    id: string;
    name: string;
  };
  comments: Comment[];
  attachments: Attachment[];
  createdAt: string;
  updatedAt: string;
  resolvedAt?: string;
}

const SupportTicketDetail: React.FC = () => {
  const { ticketId } = useParams<{ ticketId: string }>();
  const { token, user } = useAuth();
  const navigate = useNavigate();

  const [ticket, setTicket] = useState<SupportTicket | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [newComment, setNewComment] = useState<string>('');
  const [isInternalComment, setIsInternalComment] = useState<boolean>(false);
  const [submittingComment, setSubmittingComment] = useState<boolean>(false);
  const [updatingStatus, setUpdatingStatus] = useState<boolean>(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [uploadProgress, setUploadProgress] = useState<number>(0);
  const [uploadError, setUploadError] = useState<string | null>(null);
  const [uploading, setUploading] = useState<boolean>(false);

  useEffect(() => {
    fetchTicket();
  }, [ticketId, token]);

  const fetchTicket = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await axios.get(`${API_URL}/support/${ticketId}`, {
        headers: { Authorization: `Bearer ${token}` }
      });

      setTicket(response.data.ticket);
    } catch (err: any) {
      console.error('Error fetching support ticket:', err);
      setError(err.response?.data?.error || 'Failed to load support ticket');
    } finally {
      setLoading(false);
    }
  };

  const handleAddComment = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!newComment.trim()) return;

    try {
      setSubmittingComment(true);
      setError(null);

      await axios.post(
        `${API_URL}/support/${ticketId}/comments`,
        { content: newComment, isInternal: isInternalComment },
        { headers: { Authorization: `Bearer ${token}` } }
      );

      setNewComment('');
      setIsInternalComment(false);
      fetchTicket(); // Refresh ticket to show new comment
    } catch (err: any) {
      console.error('Error adding comment:', err);
      setError(err.response?.data?.error || 'Failed to add comment');
    } finally {
      setSubmittingComment(false);
    }
  };

  const handleStatusChange = async (newStatus: string) => {
    try {
      setUpdatingStatus(true);
      setError(null);

      await axios.put(
        `${API_URL}/support/${ticketId}`,
        { status: newStatus },
        { headers: { Authorization: `Bearer ${token}` } }
      );

      fetchTicket(); // Refresh ticket to show new status
    } catch (err: any) {
      console.error('Error updating ticket status:', err);
      setError(err.response?.data?.error || 'Failed to update ticket status');
    } finally {
      setUpdatingStatus(false);
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      const file = files[0];

      // Validate file size (10MB max)
      if (file.size > 10 * 1024 * 1024) {
        setUploadError('File size exceeds the limit of 10MB.');
        setSelectedFile(null);
        return;
      }

      // Validate file type
      const allowedTypes = [
        'image/jpeg', 'image/png', 'image/gif', 'image/webp',
        'application/pdf', 'application/msword', 
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.ms-excel', 
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'text/plain', 'text/csv'
      ];

      if (!allowedTypes.includes(file.type)) {
        setUploadError('Invalid file type. Only images, documents, and PDFs are allowed.');
        setSelectedFile(null);
        return;
      }

      setSelectedFile(file);
      setUploadError(null);
    }
  };

  const handleUploadFile = async () => {
    if (!selectedFile) return;

    try {
      setUploading(true);
      setUploadError(null);
      setUploadProgress(0);

      const formData = new FormData();
      formData.append('file', selectedFile);

      const response = await axios.post(
        `${API_URL}/support/${ticketId}/attachments`, 
        formData,
        {
          headers: { 
            'Content-Type': 'multipart/form-data',
            Authorization: `Bearer ${token}`
          },
          onUploadProgress: (progressEvent) => {
            if (progressEvent.total) {
              const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
              setUploadProgress(progress);
            }
          }
        }
      );

      // Clear the file input and refresh ticket to show new attachment
      setSelectedFile(null);
      setUploadProgress(0);
      fetchTicket();
    } catch (err: any) {
      console.error('Error uploading file:', err);
      setUploadError(err.response?.data?.error || 'Failed to upload file');
      setUploadProgress(0);
    } finally {
      setUploading(false);
    }
  };

  const handleDownloadAttachment = async (attachmentId: string) => {
    try {
      setError(null);
      // Use window.open to download the file
      window.open(`${API_URL}/support/attachments/${attachmentId}/download`, '_blank');
    } catch (err: any) {
      console.error('Error downloading attachment:', err);
      setError(err.response?.data?.error || 'Failed to download attachment');
    }
  };

  const handleDeleteAttachment = async (attachmentId: string) => {
    try {
      setError(null);
      await axios.delete(`${API_URL}/support/attachments/${attachmentId}`, {
        headers: { Authorization: `Bearer ${token}` }
      });

      // Refresh ticket to update attachments list
      fetchTicket();
    } catch (err: any) {
      console.error('Error deleting attachment:', err);
      setError(err.response?.data?.error || 'Failed to delete attachment');
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'open':
        return 'bg-blue-100 text-blue-800';
      case 'in_progress':
        return 'bg-yellow-100 text-yellow-800';
      case 'resolved':
        return 'bg-green-100 text-green-800';
      case 'closed':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'low':
        return 'bg-green-100 text-green-800';
      case 'medium':
        return 'bg-blue-100 text-blue-800';
      case 'high':
        return 'bg-orange-100 text-orange-800';
      case 'urgent':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString();
  };

  // Check if user is admin or support staff based on their role
  const isAdminOrSupport = false; // Global admins should not have special privileges on the frontend

  if (loading) {
    return (
      <Layout>
        <div className="container mx-auto px-4 py-8 flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
        </div>
      </Layout>
    );
  }

  if (error) {
    return (
      <Layout>
        <div className="container mx-auto px-4 py-8">
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            {error}
          </div>
          <div className="text-center">
            <Link to="/support/tickets" className="text-primary-600 hover:text-primary-800">
              Back to Support Tickets
            </Link>
          </div>
        </div>
      </Layout>
    );
  }

  if (!ticket) {
    return (
      <Layout>
        <div className="container mx-auto px-4 py-8">
          <div className="text-center">
            <h2 className="text-xl font-semibold mb-4">Support Ticket Not Found</h2>
            <p className="mb-4">The support ticket you're looking for doesn't exist or you don't have permission to view it.</p>
            <Link to="/support/tickets" className="text-primary-600 hover:text-primary-800">
              Back to Support Tickets
            </Link>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="container mx-auto px-4 py-8">
        <div className="mb-6">
          <Link to="/support/tickets" className="text-primary-600 hover:text-primary-800 flex items-center">
            <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
            </svg>
            Back to Support Tickets
          </Link>
        </div>

        <div className="bg-white rounded-lg shadow overflow-hidden">
          {/* Ticket Header */}
          <div className="border-b border-gray-200 bg-gray-50 px-6 py-4">
            <div className="flex flex-col md:flex-row md:justify-between md:items-center">
              <h1 className="text-2xl font-bold text-gray-900 mb-2 md:mb-0">{ticket.subject}</h1>
              <div className="flex items-center space-x-2">
                <span className={`px-3 py-1 text-sm rounded-full ${getStatusColor(ticket.status)}`}>
                  {ticket.status.replace('_', ' ')}
                </span>
                <span className={`px-3 py-1 text-sm rounded-full ${getPriorityColor(ticket.priority)}`}>
                  {ticket.priority}
                </span>
              </div>
            </div>
            <div className="mt-2 text-sm text-gray-500">
              <span>Ticket #{ticket.id.substring(0, 8)}</span>
              <span className="mx-2">•</span>
              <span>Created {formatDate(ticket.createdAt)}</span>
              {ticket.resolvedAt && (
                <>
                  <span className="mx-2">•</span>
                  <span>Resolved {formatDate(ticket.resolvedAt)}</span>
                </>
              )}
            </div>
          </div>

          {/* Ticket Details */}
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="md:col-span-2">
                {/* Description */}
                <div className="mb-8">
                  <h2 className="text-lg font-semibold mb-2">Description</h2>
                  <div className="bg-gray-50 p-4 rounded-lg whitespace-pre-wrap">
                    {ticket.description}
                  </div>
                </div>

                {/* Comments */}
                <div>
                  <h2 className="text-lg font-semibold mb-4">Comments</h2>
                  {ticket.comments && ticket.comments.length > 0 ? (
                    <div className="space-y-4">
                      {ticket.comments.map((comment) => (
                        <div 
                          key={comment.id} 
                          className={`p-4 rounded-lg ${
                            comment.isInternal 
                              ? 'bg-yellow-50 border border-yellow-200' 
                              : 'bg-gray-50 border border-gray-200'
                          }`}
                        >
                          <div className="flex justify-between items-start mb-2">
                            <div className="flex items-center">
                              <div className="font-medium text-gray-900">
                                {comment.author.firstName} {comment.author.lastName}
                              </div>
                              {comment.isInternal && (
                                <span className="ml-2 px-2 py-0.5 text-xs bg-yellow-100 text-yellow-800 rounded-full">
                                  Internal
                                </span>
                              )}
                            </div>
                            <div className="text-sm text-gray-500">
                              {formatDate(comment.createdAt)}
                            </div>
                          </div>
                          <div className="whitespace-pre-wrap">
                            {comment.content}
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-gray-500 italic">No comments yet.</p>
                  )}

                  {/* Attachments Section */}
                  <div className="mt-6 mb-6">
                    <h3 className="text-md font-medium mb-2">Attachments</h3>
                    {ticket.attachments && ticket.attachments.length > 0 ? (
                      <div className="space-y-2 mb-4">
                        {ticket.attachments.map(attachment => (
                          <div 
                            key={attachment.id} 
                            className="p-3 rounded-md bg-gray-50 flex justify-between items-center"
                          >
                            <div>
                              <p className="text-sm font-medium">{attachment.filename}</p>
                              <p className="text-xs text-gray-500">
                                {(attachment.fileSize / 1024).toFixed(2)} KB • 
                                Uploaded by {attachment.uploader ? `${attachment.uploader.firstName} ${attachment.uploader.lastName}` : 'Unknown'} • 
                                {formatDate(attachment.createdAt)}
                              </p>
                            </div>
                            <div className="flex space-x-2">
                              <button
                                onClick={() => handleDownloadAttachment(attachment.id)}
                                className="text-primary-600 hover:text-primary-900 text-sm"
                              >
                                Download
                              </button>
                              {(user?.id === attachment.uploader.id || isAdminOrSupport) && (
                                <button
                                  onClick={() => handleDeleteAttachment(attachment.id)}
                                  className="text-red-600 hover:text-red-900 text-sm"
                                >
                                  Delete
                                </button>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <p className="text-sm text-gray-500 mb-4">No attachments yet.</p>
                    )}

                    {/* File Upload */}
                    <div className="mb-4">
                      <div className="flex items-center space-x-2 mb-2">
                        <input
                          type="file"
                          id="file-upload"
                          onChange={handleFileChange}
                          className="hidden"
                        />
                        <label
                          htmlFor="file-upload"
                          className="bg-gray-200 hover:bg-gray-300 text-gray-800 font-medium py-2 px-4 rounded cursor-pointer text-sm"
                        >
                          Select File
                        </label>
                        {selectedFile && (
                          <span className="text-sm text-gray-600">
                            {selectedFile.name} ({(selectedFile.size / 1024).toFixed(2)} KB)
                          </span>
                        )}
                      </div>

                      {uploadError && (
                        <div className="text-sm text-red-600 mb-2">
                          {uploadError}
                        </div>
                      )}

                      {selectedFile && (
                        <div className="flex items-center space-x-2">
                          <button
                            onClick={handleUploadFile}
                            disabled={uploading}
                            className="bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded text-sm disabled:opacity-50 disabled:cursor-not-allowed"
                          >
                            {uploading ? 'Uploading...' : 'Upload File'}
                          </button>
                          <button
                            onClick={() => setSelectedFile(null)}
                            disabled={uploading}
                            className="bg-gray-500 hover:bg-gray-600 text-white font-medium py-2 px-4 rounded text-sm disabled:opacity-50 disabled:cursor-not-allowed"
                          >
                            Cancel
                          </button>
                        </div>
                      )}

                      {uploading && (
                        <div className="mt-2">
                          <div className="w-full bg-gray-200 rounded-full h-2.5">
                            <div 
                              className="bg-primary-600 h-2.5 rounded-full" 
                              style={{ width: `${uploadProgress}%` }}
                            ></div>
                          </div>
                          <p className="text-xs text-gray-500 mt-1">
                            Uploading: {uploadProgress}%
                          </p>
                        </div>
                      )}
                    </div>
                  </div>

                {/* Add Comment Form */}
                  <div className="mt-6">
                    <h3 className="text-md font-medium mb-2">Add a Comment</h3>
                    <form onSubmit={handleAddComment}>
                      <textarea
                        value={newComment}
                        onChange={(e) => setNewComment(e.target.value)}
                        placeholder="Type your comment here..."
                        className="block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 mb-2"
                        rows={4}
                        required
                      />

                      {isAdminOrSupport && (
                        <div className="flex items-center mb-4">
                          <input
                            type="checkbox"
                            id="internalComment"
                            checked={isInternalComment}
                            onChange={(e) => setIsInternalComment(e.target.checked)}
                            className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                          />
                          <label htmlFor="internalComment" className="ml-2 block text-sm text-gray-900">
                            Internal comment (only visible to support staff)
                          </label>
                        </div>
                      )}

                      <div className="flex justify-end">
                        <button
                          type="submit"
                          disabled={submittingComment || !newComment.trim()}
                          className="px-4 py-2 bg-primary-600 border border-transparent rounded-md text-sm font-medium text-white hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          {submittingComment ? 'Submitting...' : 'Add Comment'}
                        </button>
                      </div>
                    </form>
                  </div>
                </div>
              </div>

              {/* Sidebar */}
              <div>
                <div className="bg-gray-50 p-4 rounded-lg">
                  <h3 className="text-md font-medium mb-4">Ticket Information</h3>

                  <div className="space-y-3">
                    <div>
                      <div className="text-sm font-medium text-gray-500">Status</div>
                      <div className="mt-1">
                        {updatingStatus ? (
                          <div className="animate-pulse h-6 bg-gray-200 rounded"></div>
                        ) : (
                          <div className="flex items-center">
                            <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(ticket.status)}`}>
                              {ticket.status.replace('_', ' ')}
                            </span>

                            {/* Status change dropdown for admins/support */}
                            {isAdminOrSupport && (
                              <div className="ml-2">
                                <select
                                  value={ticket.status}
                                  onChange={(e) => handleStatusChange(e.target.value)}
                                  className="text-sm border-gray-300 rounded-md shadow-sm focus:border-primary-500 focus:ring-primary-500"
                                >
                                  <option value="open">Open</option>
                                  <option value="in_progress">In Progress</option>
                                  <option value="resolved">Resolved</option>
                                  <option value="closed">Closed</option>
                                </select>
                              </div>
                            )}
                          </div>
                        )}
                      </div>
                    </div>

                    <div>
                      <div className="text-sm font-medium text-gray-500">Priority</div>
                      <div className="mt-1">
                        <span className={`px-2 py-1 text-xs rounded-full ${getPriorityColor(ticket.priority)}`}>
                          {ticket.priority}
                        </span>
                      </div>
                    </div>

                    {ticket.category && (
                      <div>
                        <div className="text-sm font-medium text-gray-500">Category</div>
                        <div className="mt-1 text-sm text-gray-900">{ticket.category}</div>
                      </div>
                    )}

                    {ticket.farm && (
                      <div>
                        <div className="text-sm font-medium text-gray-500">Farm</div>
                        <div className="mt-1 text-sm text-gray-900">{ticket.farm.name}</div>
                      </div>
                    )}

                    <div>
                      <div className="text-sm font-medium text-gray-500">Created By</div>
                      <div className="mt-1 text-sm text-gray-900">
                        {ticket.creator.firstName} {ticket.creator.lastName}
                        <div className="text-xs text-gray-500">{ticket.creator.email}</div>
                      </div>
                    </div>

                    {ticket.assignee && (
                      <div>
                        <div className="text-sm font-medium text-gray-500">Assigned To</div>
                        <div className="mt-1 text-sm text-gray-900">
                          {ticket.assignee.firstName} {ticket.assignee.lastName}
                          <div className="text-xs text-gray-500">{ticket.assignee.email}</div>
                        </div>
                      </div>
                    )}

                    <div>
                      <div className="text-sm font-medium text-gray-500">Created</div>
                      <div className="mt-1 text-sm text-gray-900">{formatDate(ticket.createdAt)}</div>
                    </div>

                    <div>
                      <div className="text-sm font-medium text-gray-500">Last Updated</div>
                      <div className="mt-1 text-sm text-gray-900">{formatDate(ticket.updatedAt)}</div>
                    </div>

                    {ticket.resolvedAt && (
                      <div>
                        <div className="text-sm font-medium text-gray-500">Resolved</div>
                        <div className="mt-1 text-sm text-gray-900">{formatDate(ticket.resolvedAt)}</div>
                      </div>
                    )}
                  </div>

                  {/* Admin Actions */}
                  {isAdminOrSupport && (
                    <div className="mt-6 pt-6 border-t border-gray-200">
                      <h3 className="text-md font-medium mb-4">Admin Actions</h3>
                      <div className="space-y-2">
                        <Link
                          to={`/support/tickets/${ticket.id}/edit`}
                          className="block w-full text-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                        >
                          Edit Ticket
                        </Link>

                        {ticket.status !== 'resolved' && (
                          <button
                            onClick={() => handleStatusChange('resolved')}
                            className="block w-full text-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700"
                            disabled={updatingStatus}
                          >
                            {updatingStatus ? 'Updating...' : 'Mark as Resolved'}
                          </button>
                        )}

                        {ticket.status !== 'closed' && (
                          <button
                            onClick={() => handleStatusChange('closed')}
                            className="block w-full text-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-gray-600 hover:bg-gray-700"
                            disabled={updatingStatus}
                          >
                            {updatingStatus ? 'Updating...' : 'Close Ticket'}
                          </button>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default SupportTicketDetail;
