import { useState, useEffect, useContext } from 'react';
import { Link } from 'react-router-dom';
import axios from 'axios';
import { AuthContext } from '../../context/AuthContext';
import { useFarm } from '../../context/FarmContext';
import Layout from '../../components/Layout';
import { API_URL } from '../../config';

interface Crop {
  id: string;
  name: string;
  variety: string;
  season: string;
  year: number;
  status: string;
  notes: string;
}

const CropsList = () => {
  const [crops, setCrops] = useState<Crop[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const { user } = useContext(AuthContext);
  const { selectedFarm } = useFarm();

  // Fetch crops for the selected farm
  useEffect(() => {
    const fetchCrops = async () => {
      if (!selectedFarm) return;

      setLoading(true);
      setError(null);

      try {
        const response = await axios.get(`${API_URL}/crops/farm/${selectedFarm.id}`);
        // Check if response.data is an array or has a crops property
        setCrops(Array.isArray(response.data) ? response.data : response.data.crops || []);
      } catch (err: any) {
        console.error('Error fetching crops:', err);
        setError('Failed to load crops. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchCrops();
  }, [selectedFarm]);

  // Handle crop deletion
  const handleDeleteCrop = async (cropId: string) => {
    if (!window.confirm('Are you sure you want to delete this crop?')) {
      return;
    }

    try {
      await axios.delete(`${API_URL}/crops/${cropId}`);
      setCrops(crops.filter(crop => crop.id !== cropId));
    } catch (err: any) {
      console.error('Error deleting crop:', err);
      setError('Failed to delete crop. Please try again later.');
    }
  };

  // Create a product from a crop
  const handleCreateProduct = async (cropId: string) => {
    try {
      const response = await axios.post(`${API_URL}/products/from-crop/${cropId}`, {
        price: 0, // Default price, can be updated later
        unit: 'unit' // Default unit, can be updated later
      });

      alert('Product created successfully!');
    } catch (err: any) {
      console.error('Error creating product from crop:', err);
      setError('Failed to create product. Please try again later.');
    }
  };

  return (
    <Layout>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Crop Management</h1>
        <div className="flex space-x-2">
          <Link
            to="/crops/new"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Add New Crop
          </Link>
        </div>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">{error}</span>
        </div>
      )}

      {loading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
          <p className="ml-3 text-gray-500">Loading crops...</p>
        </div>
      ) : crops.length === 0 ? (
        <div className="bg-white shadow rounded-lg p-8 text-center">
          <p className="text-gray-500 mb-4">No crops found for this farm.</p>
          <p className="text-sm text-gray-400 mb-6">
            Add your first crop to start tracking your planting, harvests, and yields.
          </p>
          <Link
            to="/crops/new"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Add New Crop
          </Link>
        </div>
      ) : (
        <div className="bg-white shadow overflow-hidden sm:rounded-md">
          <ul className="divide-y divide-gray-200">
            {crops.map((crop) => (
              <li key={crop.id}>
                <div className="px-4 py-4 sm:px-6">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className="ml-3">
                        <p className="text-sm font-medium text-primary-600 truncate">{crop.name}</p>
                        <p className="text-sm text-gray-500">
                          {crop.variety && <span className="mr-2">Variety: {crop.variety}</span>}
                          {crop.year && <span className="mr-2">Year: {crop.year}</span>}
                          {crop.season && <span>Season: {crop.season}</span>}
                        </p>
                      </div>
                    </div>
                    <div className="flex space-x-2">
                      <button
                        onClick={() => handleCreateProduct(crop.id)}
                        className="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                      >
                        Create Product
                      </button>
                      <Link
                        to={`/crops/${crop.id}`}
                        className="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                      >
                        View
                      </Link>
                      <Link
                        to={`/crops/${crop.id}/edit`}
                        className="inline-flex items-center px-3 py-1 border border-gray-300 text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                      >
                        Edit
                      </Link>
                      <button
                        onClick={() => handleDeleteCrop(crop.id)}
                        className="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                      >
                        Delete
                      </button>
                    </div>
                  </div>
                  {crop.status && (
                    <div className="mt-2">
                      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                        crop.status === 'active' ? 'bg-green-100 text-green-800' : 
                        crop.status === 'planned' ? 'bg-blue-100 text-blue-800' : 
                        crop.status === 'harvested' ? 'bg-yellow-100 text-yellow-800' : 
                        'bg-gray-100 text-gray-800'
                      }`}>
                        {crop.status}
                      </span>
                    </div>
                  )}
                  {crop.notes && (
                    <div className="mt-2 text-sm text-gray-500">
                      <p>{crop.notes}</p>
                    </div>
                  )}
                </div>
              </li>
            ))}
          </ul>
        </div>
      )}
    </Layout>
  );
};

export default CropsList;
