import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { API_URL } from '../../config';

interface DatabaseMigration {
  id: string;
  name: string;
  description: string;
  file_path: string;
  applied: boolean;
  skipped: boolean;
  applied_at: string | null;
  applied_by: string | null;
  order: number;
  status: 'pending' | 'in_progress' | 'completed' | 'failed' | 'skipped';
  error_message: string | null;
  dependencies: string[];
  created_at: string;
  updated_at: string;
  appliedBy?: {
    id: string;
    first_name: string;
    last_name: string;
    email: string;
  };
}

interface MigrationStatus {
  pendingMigrations: number;
  allMigrations: number;
  appliedMigrations: number;
  skippedMigrations: number;
  allApplied: boolean;
}

const DatabaseMigrations: React.FC = () => {
  const [migrations, setMigrations] = useState<DatabaseMigration[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [migrationStatus, setMigrationStatus] = useState<MigrationStatus | null>(null);
  const [scanningForMigrations, setScanningForMigrations] = useState<boolean>(false);
  const [applyingMigration, setApplyingMigration] = useState<string | null>(null);
  const [skippingMigration, setSkippingMigration] = useState<string | null>(null);
  const [retryingMigration, setRetryingMigration] = useState<string | null>(null);
  const [showConfirmModal, setShowConfirmModal] = useState<boolean>(false);
  const [showSkipConfirmModal, setShowSkipConfirmModal] = useState<boolean>(false);
  const [showRetryConfirmModal, setShowRetryConfirmModal] = useState<boolean>(false);
  const [selectedMigration, setSelectedMigration] = useState<DatabaseMigration | null>(null);
  const [activeFilter, setActiveFilter] = useState<'pending' | 'applied' | 'skipped'>('pending');

  useEffect(() => {
    fetchMigrations();
    checkMigrationStatus();
  }, [activeFilter]);

  const fetchMigrations = async () => {
    try {
      setLoading(true);
      setError(null);

      // For skipped, we need to fetch all migrations and filter in the frontend
      const filter = activeFilter === 'skipped' ? 'all' : activeFilter;

      const response = await axios.get(`${API_URL}/db-migrations`, {
        params: { filter }
      });

      let filteredMigrations = response.data.migrations || [];

      // If the filter is skipped, filter the migrations in the frontend
      if (activeFilter === 'skipped') {
        // A migration is considered "skipped after applied" if it has been applied and then skipped
        // Since the backend doesn't support this concept directly, we'll look for migrations with status='skipped'
        // This is a placeholder implementation - in reality, we would need backend support to track migrations that were applied and then skipped
        filteredMigrations = filteredMigrations.filter(migration => 
          migration.status === 'skipped' && migration.applied_at !== null
        );
      }

      setMigrations(filteredMigrations);
      setLoading(false);
    } catch (err: any) {
      console.error('Error fetching migrations:', err);
      setError(err.response?.data?.error || 'Failed to load migrations');
      setLoading(false);
    }
  };

  const checkMigrationStatus = async () => {
    try {
      const response = await axios.get(`${API_URL}/db-migrations/status/check`);
      setMigrationStatus(response.data);
    } catch (err: any) {
      console.error('Error checking migration status:', err);
    }
  };

  const handleScanForMigrations = async () => {
    try {
      setScanningForMigrations(true);
      setError(null);

      const response = await axios.post(`${API_URL}/db-migrations/scan`);

      // Refresh migrations with current filter
      fetchMigrations();
      await checkMigrationStatus();
      setScanningForMigrations(false);
    } catch (err: any) {
      console.error('Error scanning for migrations:', err);
      setError(err.response?.data?.error || 'Failed to scan for migrations');
      setScanningForMigrations(false);
    }
  };

  const confirmApplyMigration = (migration: DatabaseMigration) => {
    setSelectedMigration(migration);
    setShowConfirmModal(true);
  };

  const confirmSkipMigration = (migration: DatabaseMigration) => {
    setSelectedMigration(migration);
    setShowSkipConfirmModal(true);
  };

  const handleSkipMigration = async () => {
    if (!selectedMigration) return;

    try {
      setSkippingMigration(selectedMigration.id);
      setError(null);
      setShowSkipConfirmModal(false);

      const response = await axios.post(`${API_URL}/db-migrations/${selectedMigration.id}/skip`);

      // Refresh migrations with current filter
      fetchMigrations();
      await checkMigrationStatus();
      setSkippingMigration(null);
    } catch (err: any) {
      console.error('Error skipping migration:', err);
      setError(err.response?.data?.error || 'Failed to skip migration');
      setSkippingMigration(null);

      // Refresh migrations to get updated status
      fetchMigrations();
    }
  };

  const confirmRetryMigration = (migration: DatabaseMigration) => {
    setSelectedMigration(migration);
    setShowRetryConfirmModal(true);
  };

  const handleRetryMigration = async () => {
    if (!selectedMigration) return;

    try {
      setRetryingMigration(selectedMigration.id);
      setError(null);
      setShowRetryConfirmModal(false);

      const response = await axios.post(`${API_URL}/db-migrations/${selectedMigration.id}/retry`);

      // Refresh migrations with current filter
      fetchMigrations();
      await checkMigrationStatus();
      setRetryingMigration(null);
    } catch (err: any) {
      console.error('Error retrying migration:', err);
      setError(err.response?.data?.error || 'Failed to retry migration');
      setRetryingMigration(null);

      // Refresh migrations to get updated status
      fetchMigrations();
    }
  };

  const handleApplyMigration = async () => {
    if (!selectedMigration) return;

    try {
      setApplyingMigration(selectedMigration.id);
      setError(null);
      setShowConfirmModal(false);

      const response = await axios.post(`${API_URL}/db-migrations/${selectedMigration.id}/apply`);

      // Refresh migrations with current filter
      fetchMigrations();
      await checkMigrationStatus();
      setApplyingMigration(null);
    } catch (err: any) {
      console.error('Error applying migration:', err);
      setError(err.response?.data?.error || 'Failed to apply migration');
      setApplyingMigration(null);

      // Refresh migrations to get updated status
      fetchMigrations();
    }
  };

  const getStatusBadgeClass = (status: string, applied: boolean, skipped: boolean) => {
    if (status === 'skipped' || skipped) return 'bg-yellow-100 text-yellow-800';
    if (status === 'completed' || applied) return 'bg-green-100 text-green-800';
    if (status === 'in_progress') return 'bg-blue-100 text-blue-800';
    if (status === 'failed') return 'bg-red-100 text-red-800';
    return 'bg-gray-100 text-gray-800';
  };

  const getStatusText = (status: string, applied: boolean, skipped: boolean) => {
    if (status === 'skipped' || skipped) return 'Skipped';
    if (status === 'completed' || applied) return 'Applied';
    if (status === 'in_progress') return 'In Progress';
    if (status === 'failed') return 'Failed';
    return 'Pending';
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
      </div>
    );
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <div>
          <h2 className="text-xl font-semibold">Database Migrations</h2>
          {migrationStatus && (
            <p className="text-sm text-gray-600 mt-1">
              {migrationStatus.pendingMigrations} pending, {migrationStatus.appliedMigrations} applied, {migrationStatus.skippedMigrations} skipped out of {migrationStatus.allMigrations} total migrations
            </p>
          )}
        </div>
        <button 
          onClick={handleScanForMigrations}
          disabled={scanningForMigrations}
          className={`${
            scanningForMigrations 
              ? 'bg-gray-400 cursor-not-allowed' 
              : 'bg-primary-600 hover:bg-primary-700'
          } text-white font-bold py-2 px-4 rounded`}
        >
          {scanningForMigrations ? 'Scanning...' : 'Scan for New Migrations'}
        </button>
      </div>

      <div className="flex space-x-4 mb-4">
        <div className="flex items-center">
          <input
            type="radio"
            id="filter-pending"
            checked={activeFilter === 'pending'}
            onChange={() => setActiveFilter('pending')}
            className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300"
          />
          <label htmlFor="filter-pending" className="ml-2 block text-sm text-gray-700">
            Pending Migrations
          </label>
        </div>
        <div className="flex items-center">
          <input
            type="radio"
            id="filter-applied"
            checked={activeFilter === 'applied'}
            onChange={() => setActiveFilter('applied')}
            className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300"
          />
          <label htmlFor="filter-applied" className="ml-2 block text-sm text-gray-700">
            Applied Migrations
          </label>
        </div>
        <div className="flex items-center">
          <input
            type="radio"
            id="filter-skipped-after-applied"
            checked={activeFilter === 'skipped'}
            onChange={() => setActiveFilter('skipped')}
            className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300"
          />
          <label htmlFor="filter-skipped" className="ml-2 block text-sm text-gray-700">
            Skipped
          </label>
        </div>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
          <strong className="font-bold">Error!</strong>
          <span className="block sm:inline"> {error}</span>
        </div>
      )}

      <div className="bg-white shadow-md rounded-lg overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Order
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Name
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Description
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Applied By
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Applied At
              </th>
              <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {migrations.length === 0 ? (
              <tr>
                <td colSpan={7} className="px-6 py-4 text-center text-sm text-gray-500">
                  No migrations found. Click "Scan for New Migrations" to detect available migrations.
                </td>
              </tr>
            ) : (
              migrations.map(migration => (
                <tr key={migration.id}>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {migration.order}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">{migration.name}</div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm text-gray-500">{migration.description}</div>
                    {migration.dependencies && migration.dependencies.length > 0 && (
                      <div className="text-sm text-blue-500 mt-1">
                        Depends on: {migration.dependencies.join(', ')}
                      </div>
                    )}
                    {migration.error_message && (
                      <div className="text-sm text-red-500 mt-1">Error: {migration.error_message}</div>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                      getStatusBadgeClass(migration.status, migration.applied, migration.skipped)
                    }`}>
                      {getStatusText(migration.status, migration.applied, migration.skipped)}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {migration.appliedBy ? `${migration.appliedBy.first_name} ${migration.appliedBy.last_name}` : '-'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {migration.applied_at ? new Date(migration.applied_at).toLocaleString() : '-'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    {!migration.applied && migration.status === 'skipped' && (
                      <button 
                        onClick={() => confirmRetryMigration(migration)}
                        disabled={retryingMigration === migration.id}
                        className={`${
                          retryingMigration === migration.id
                            ? 'text-gray-400 cursor-not-allowed'
                            : 'text-blue-600 hover:text-blue-900'
                        }`}
                      >
                        {retryingMigration === migration.id ? 'Retrying...' : 'Retry'}
                      </button>
                    )}
                    {!migration.applied && migration.status !== 'skipped' && migration.status !== 'in_progress' && (
                      <>
                        <button 
                          onClick={() => confirmApplyMigration(migration)}
                          disabled={applyingMigration === migration.id || skippingMigration === migration.id}
                          className={`${
                            applyingMigration === migration.id
                              ? 'text-gray-400 cursor-not-allowed'
                              : 'text-primary-600 hover:text-primary-900'
                          } mr-4`}
                        >
                          {applyingMigration === migration.id ? 'Applying...' : 'Apply'}
                        </button>
                        <button 
                          onClick={() => confirmSkipMigration(migration)}
                          disabled={applyingMigration === migration.id || skippingMigration === migration.id}
                          className={`${
                            skippingMigration === migration.id
                              ? 'text-gray-400 cursor-not-allowed'
                              : 'text-yellow-600 hover:text-yellow-900'
                          }`}
                        >
                          {skippingMigration === migration.id ? 'Skipping...' : 'Skip'}
                        </button>
                      </>
                    )}
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {/* Apply Confirmation Modal */}
      {showConfirmModal && selectedMigration && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full flex items-center justify-center">
          <div className="relative bg-white rounded-lg shadow-xl max-w-md w-full">
            <div className="flex justify-between items-center p-5 border-b">
              <h3 className="text-lg font-semibold">Confirm Migration</h3>
              <button 
                onClick={() => setShowConfirmModal(false)}
                className="text-gray-400 hover:text-gray-500"
              >
                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <div className="p-5">
              <p className="mb-4">
                Are you sure you want to apply the migration <strong>{selectedMigration.name}</strong>?
              </p>
              <p className="mb-4 text-sm text-gray-600">
                This will execute the SQL in the migration file and cannot be undone.
              </p>
              <div className="flex justify-end">
                <button
                  type="button"
                  onClick={() => setShowConfirmModal(false)}
                  className="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded mr-2"
                >
                  Cancel
                </button>
                <button
                  type="button"
                  onClick={handleApplyMigration}
                  className="bg-primary-600 hover:bg-primary-700 text-white font-bold py-2 px-4 rounded"
                >
                  Apply Migration
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Skip Confirmation Modal */}
      {showSkipConfirmModal && selectedMigration && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full flex items-center justify-center">
          <div className="relative bg-white rounded-lg shadow-xl max-w-md w-full">
            <div className="flex justify-between items-center p-5 border-b">
              <h3 className="text-lg font-semibold">Confirm Skip Migration</h3>
              <button 
                onClick={() => setShowSkipConfirmModal(false)}
                className="text-gray-400 hover:text-gray-500"
              >
                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <div className="p-5">
              <p className="mb-4">
                Are you sure you want to skip the migration <strong>{selectedMigration.name}</strong>?
              </p>
              <p className="mb-4 text-sm text-gray-600">
                This will mark the migration as skipped without executing the SQL.
              </p>
              <div className="flex justify-end">
                <button
                  type="button"
                  onClick={() => setShowSkipConfirmModal(false)}
                  className="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded mr-2"
                >
                  Cancel
                </button>
                <button
                  type="button"
                  onClick={handleSkipMigration}
                  className="bg-yellow-600 hover:bg-yellow-700 text-white font-bold py-2 px-4 rounded"
                >
                  Skip Migration
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Retry Confirmation Modal */}
      {showRetryConfirmModal && selectedMigration && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full flex items-center justify-center">
          <div className="relative bg-white rounded-lg shadow-xl max-w-md w-full">
            <div className="flex justify-between items-center p-5 border-b">
              <h3 className="text-lg font-semibold">Confirm Retry Migration</h3>
              <button 
                onClick={() => setShowRetryConfirmModal(false)}
                className="text-gray-400 hover:text-gray-500"
              >
                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <div className="p-5">
              <p className="mb-4">
                Are you sure you want to retry the migration <strong>{selectedMigration.name}</strong>?
              </p>
              <p className="mb-4 text-sm text-gray-600">
                This will reset the migration status to pending, allowing you to apply or skip it again.
              </p>
              <div className="flex justify-end">
                <button
                  type="button"
                  onClick={() => setShowRetryConfirmModal(false)}
                  className="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded mr-2"
                >
                  Cancel
                </button>
                <button
                  type="button"
                  onClick={handleRetryMigration}
                  className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
                >
                  Retry Migration
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default DatabaseMigrations;
