import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { API_URL } from '../../config';
import { getAuthToken } from '../../utils/storageUtils';

interface Integration {
  id: string;
  name: string;
  description: string | null;
  version: string;
  author: string;
  entry_point: string;
  enabled: boolean;
  settings: any;
  icon: string | null;
  is_default: boolean;
  is_global: boolean;
  created_at: string;
  updated_at: string;
}

interface AvailablePlugin {
  name: string;
  description: string;
  version: string;
  author: string;
  entry_point: string;
  icon: string;
  default_settings: any;
  settings_schema: any;
  folder: string;
  is_global: boolean;
}

const IntegrationManagement: React.FC = () => {
  const [integrations, setIntegrations] = useState<Integration[]>([]);
  const [availablePlugins, setAvailablePlugins] = useState<AvailablePlugin[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [showAvailable, setShowAvailable] = useState<boolean>(false);
  const [installing, setInstalling] = useState<boolean>(false);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Fetch integrations
        const integrationsResponse = await axios.get(`${API_URL}/api/integrations`, {
          headers: {
            Authorization: `Bearer ${getAuthToken()}`
          }
        });
        setIntegrations(integrationsResponse.data || []);

        // Only fetch available plugins if the tab is active
        if (showAvailable) {
          const pluginsResponse = await axios.get(`${API_URL}/api/integrations/available`, {
            headers: {
              Authorization: `Bearer ${getAuthToken()}`
            }
          });
          setAvailablePlugins(pluginsResponse.data || []);
        }

        setLoading(false);
      } catch (err: any) {
        console.error('Error fetching integration data:', err);
        setError(err.response?.data?.error || 'Failed to load integration data');
        setLoading(false);
      }
    };

    fetchData();
  }, [showAvailable]);

  const handleToggleDefault = async (id: string, isDefault: boolean) => {
    try {
      setError(null);

      const response = await axios.put(`${API_URL}/api/integrations/${id}/settings`, { 
        is_default: isDefault 
      }, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`
        }
      });

      setIntegrations(prev => 
        prev.map(integration => 
          integration.id === id ? response.data : integration
        )
      );
    } catch (err: any) {
      console.error('Error toggling default status:', err);
      setError(err.response?.data?.error || 'Failed to update integration default status');
    }
  };

  const handleToggleGlobal = async (id: string, isGlobal: boolean) => {
    try {
      setError(null);

      const response = await axios.put(`${API_URL}/api/integrations/${id}`, { 
        is_global: isGlobal 
      }, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`
        }
      });

      setIntegrations(prev => 
        prev.map(integration => 
          integration.id === id ? response.data : integration
        )
      );
    } catch (err: any) {
      console.error('Error toggling global status:', err);
      setError(err.response?.data?.error || 'Failed to update integration global status');
    }
  };

  const handleToggleEnabled = async (id: string, enabled: boolean) => {
    try {
      setError(null);

      const response = await axios.put(`${API_URL}/api/integrations/${id}/toggle`, { 
        enabled: enabled 
      }, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`
        }
      });

      setIntegrations(prev => 
        prev.map(integration => 
          integration.id === id ? response.data : integration
        )
      );
    } catch (err: any) {
      console.error('Error toggling enabled status:', err);
      setError(err.response?.data?.error || 'Failed to update integration enabled status');
    }
  };

  const handleInstallPlugin = async (folder: string) => {
    setInstalling(true);
    setError(null);

    try {
      const response = await axios.post(`${API_URL}/api/integrations/install`, { folder }, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`
        }
      });

      setIntegrations(prev => [...prev, response.data]);
      setShowAvailable(false);
    } catch (err: any) {
      console.error('Error installing plugin:', err);
      setError(err.response?.data?.error || 'Failed to install plugin. Please try again later.');
    } finally {
      setInstalling(false);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
        <strong className="font-bold">Error!</strong>
        <span className="block sm:inline"> {error}</span>
      </div>
    );
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold">Manage Integrations</h2>
        <div className="flex space-x-2">
          <button
            className={`px-4 py-2 rounded-md ${!showAvailable ? 'bg-primary-600 text-white' : 'bg-gray-200 text-gray-800'}`}
            onClick={() => setShowAvailable(false)}
          >
            Installed
          </button>
          <button
            className={`px-4 py-2 rounded-md ${showAvailable ? 'bg-primary-600 text-white' : 'bg-gray-200 text-gray-800'}`}
            onClick={() => setShowAvailable(true)}
          >
            Available
          </button>
        </div>
      </div>

      {showAvailable ? (
        // Available plugins
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {availablePlugins.length === 0 ? (
            <div className="col-span-full text-center py-12 bg-gray-50 rounded-lg">
              <p className="text-gray-500">No available plugins found.</p>
            </div>
          ) : (
            availablePlugins.map(plugin => (
              <div key={plugin.folder} className="bg-white rounded-lg shadow-md overflow-hidden border border-gray-200">
                <div className="p-6">
                  <div className="flex items-center mb-4">
                    {plugin.icon ? (
                      <img src={plugin.icon} alt={plugin.name} className="w-10 h-10 mr-3" />
                    ) : (
                      <div className="w-10 h-10 bg-gray-200 rounded-full mr-3 flex items-center justify-center">
                        <span className="text-gray-500 text-xl">{plugin.name.charAt(0)}</span>
                      </div>
                    )}
                    <div>
                      <h2 className="text-xl font-semibold">{plugin.name}</h2>
                      <p className="text-sm text-gray-500">v{plugin.version} by {plugin.author}</p>
                    </div>
                  </div>
                  <p className="text-gray-600 mb-4">{plugin.description}</p>
                  <div className="flex items-center mb-4">
                    <span className={`px-2 py-1 text-xs font-semibold rounded-full mr-2 ${
                      plugin.is_global 
                        ? 'bg-purple-100 text-purple-800' 
                        : 'bg-orange-100 text-orange-800'
                    }`}>
                      {plugin.is_global ? 'Global System' : 'Per Farm'}
                    </span>
                  </div>
                  <button
                    className="w-full bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 transition-colors"
                    onClick={() => handleInstallPlugin(plugin.folder)}
                    disabled={installing}
                  >
                    {installing ? 'Installing...' : 'Install'}
                  </button>
                </div>
              </div>
            ))
          )}
        </div>
      ) : (
        // Installed integrations
        <div className="bg-white shadow-md rounded-lg overflow-hidden">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Integration
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Version
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Default
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Type
                </th>
                <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {integrations.map(integration => (
                <tr key={integration.id}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      {integration.icon ? (
                        <img src={integration.icon} alt={integration.name} className="w-8 h-8 mr-3" />
                      ) : (
                        <div className="w-8 h-8 bg-gray-200 rounded-full mr-3 flex items-center justify-center">
                          <span className="text-gray-500">{integration.name.charAt(0)}</span>
                        </div>
                      )}
                      <div>
                        <div className="font-medium text-gray-900">{integration.name}</div>
                        {integration.description && (
                          <div className="text-sm text-gray-500">{integration.description}</div>
                        )}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {integration.version}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                      integration.enabled 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-gray-100 text-gray-800'
                    }`}>
                      {integration.enabled ? 'Enabled' : 'Disabled'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                      integration.is_default 
                        ? 'bg-blue-100 text-blue-800' 
                        : 'bg-gray-100 text-gray-800'
                    }`}>
                      {integration.is_default ? 'Default' : 'Optional'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                      integration.is_global 
                        ? 'bg-purple-100 text-purple-800' 
                        : 'bg-orange-100 text-orange-800'
                    }`}>
                      {integration.is_global ? 'Global System' : 'Per Farm'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex justify-end space-x-2">
                      <button
                        className={`px-3 py-1 rounded ${
                          integration.enabled 
                            ? 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200' 
                            : 'bg-green-100 text-green-800 hover:bg-green-200'
                        }`}
                        onClick={() => handleToggleEnabled(integration.id, !integration.enabled)}
                      >
                        {integration.enabled ? 'Disable' : 'Enable'}
                      </button>
                      <button
                        className={`px-3 py-1 rounded ${
                          integration.is_default 
                            ? 'bg-red-100 text-red-800 hover:bg-red-200' 
                            : 'bg-blue-100 text-blue-800 hover:bg-blue-200'
                        }`}
                        onClick={() => handleToggleDefault(integration.id, !integration.is_default)}
                      >
                        {integration.is_default ? 'Remove Default' : 'Make Default'}
                      </button>
                      <button
                        className={`px-3 py-1 rounded ${
                          integration.is_global 
                            ? 'bg-purple-100 text-purple-800 hover:bg-purple-200' 
                            : 'bg-orange-100 text-orange-800 hover:bg-orange-200'
                        }`}
                        onClick={() => handleToggleGlobal(integration.id, !integration.is_global)}
                      >
                        {integration.is_global ? 'Make Per Farm' : 'Make Global'}
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
};

export default IntegrationManagement;
