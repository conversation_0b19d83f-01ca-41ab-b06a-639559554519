import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { API_URL } from '../../config';
import { getAuthToken } from '../../utils/storageUtils';

interface ApiProvider {
  id: string;
  name: string;
  category: string;
  base_url: string | null;
  requires_key: boolean;
  description: string | null;
  created_at: string;
  updated_at: string;
}

interface ApiEndpoint {
  id: string;
  provider_id: string;
  name: string;
  path: string;
  method: string;
  description: string | null;
  request_format: any | null;
  response_format: any | null;
  cache_duration_seconds: number;
  is_enabled: boolean;
  created_at: string;
  updated_at: string;
  ApiProvider?: ApiProvider;
}

const ApiEndpointManagement: React.FC = () => {
  const [providers, setProviders] = useState<ApiProvider[]>([]);
  const [endpoints, setEndpoints] = useState<ApiEndpoint[]>([]);
  const [selectedProvider, setSelectedProvider] = useState<string>('all');
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [editingEndpoint, setEditingEndpoint] = useState<ApiEndpoint | null>(null);
  const [isCreating, setIsCreating] = useState<boolean>(false);
  const [formData, setFormData] = useState({
    provider_id: '',
    name: '',
    path: '',
    method: 'GET',
    description: '',
    request_format: null,
    response_format: null,
    cache_duration_seconds: 3600,
    is_enabled: true
  });

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Fetch API providers
        const providersResponse = await axios.get(`${API_URL}/api/admin/api-providers`, {
          headers: {
            Authorization: `Bearer ${getAuthToken()}`
          }
        });
        setProviders(providersResponse.data || []);

        // Fetch API endpoints
        const endpointsResponse = await axios.get(`${API_URL}/api/admin/api-endpoints`, {
          headers: {
            Authorization: `Bearer ${getAuthToken()}`
          }
        });
        setEndpoints(endpointsResponse.data || []);

        setLoading(false);
      } catch (err: any) {
        console.error('Error fetching API data:', err);
        setError(err.response?.data?.error || 'Failed to load API data');
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const handleProviderChange = async (providerId: string) => {
    setSelectedProvider(providerId);
    setLoading(true);
    setError(null);

    try {
      const url = providerId === 'all' 
        ? `${API_URL}/api/admin/api-endpoints`
        : `${API_URL}/api/admin/api-endpoints?providerId=${providerId}`;

      const response = await axios.get(url, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`
        }
      });
      setEndpoints(response.data || []);
      setLoading(false);
    } catch (err: any) {
      console.error('Error fetching endpoints:', err);
      setError(err.response?.data?.error || 'Failed to load endpoints');
      setLoading(false);
    }
  };

  const handleEditClick = (endpoint: ApiEndpoint) => {
    setEditingEndpoint(endpoint);
    setFormData({
      provider_id: endpoint.provider_id,
      name: endpoint.name,
      path: endpoint.path,
      method: endpoint.method,
      description: endpoint.description || '',
      request_format: endpoint.request_format,
      response_format: endpoint.response_format,
      cache_duration_seconds: endpoint.cache_duration_seconds,
      is_enabled: endpoint.is_enabled
    });
  };

  const handleCancelEdit = () => {
    setEditingEndpoint(null);
    setIsCreating(false);
    resetForm();
  };

  const resetForm = () => {
    setFormData({
      provider_id: '',
      name: '',
      path: '',
      method: 'GET',
      description: '',
      request_format: null,
      response_format: null,
      cache_duration_seconds: 3600,
      is_enabled: true
    });
  };

  const handleCreateClick = () => {
    resetForm();
    if (providers.length > 0) {
      setFormData(prev => ({ ...prev, provider_id: providers[0].id }));
    }
    setIsCreating(true);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: name === 'cache_duration_seconds' ? parseInt(value) : value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    setLoading(true);
    setError(null);

    try {
      let response;

      if (isCreating) {
        // Creating a new endpoint
        response = await axios.post(
          `${API_URL}/api/admin/api-endpoints`,
          formData,
          {
            headers: {
              Authorization: `Bearer ${getAuthToken()}`
            }
          }
        );

        // Add the new endpoint to the list
        setEndpoints(prev => [...prev, response.data]);

        // Reset the form and exit create mode
        setIsCreating(false);
      } else if (editingEndpoint) {
        // Updating an existing endpoint
        response = await axios.put(
          `${API_URL}/api/admin/api-endpoints/${editingEndpoint.id}`,
          formData,
          {
            headers: {
              Authorization: `Bearer ${getAuthToken()}`
            }
          }
        );

        // Update the endpoints list with the updated endpoint
        setEndpoints(prev => 
          prev.map(endpoint => 
            endpoint.id === editingEndpoint.id ? response.data : endpoint
          )
        );

        // Exit edit mode
        setEditingEndpoint(null);
      }

      setLoading(false);
      resetForm();
    } catch (err: any) {
      console.error('Error saving endpoint:', err);
      setError(err.response?.data?.error || 'Failed to save endpoint');
      setLoading(false);
    }
  };

  const handleToggleStatus = async (endpoint: ApiEndpoint) => {
    setLoading(true);
    setError(null);

    try {
      const response = await axios.put(
        `${API_URL}/api/admin/api-endpoints/${endpoint.id}/status`,
        { is_enabled: !endpoint.is_enabled },
        {
          headers: {
            Authorization: `Bearer ${getAuthToken()}`
          }
        }
      );

      // Update the endpoints list with the updated endpoint
      setEndpoints(prev => 
        prev.map(ep => 
          ep.id === endpoint.id ? response.data.endpoint : ep
        )
      );

      setLoading(false);
    } catch (err: any) {
      console.error('Error toggling endpoint status:', err);
      setError(err.response?.data?.error || 'Failed to toggle endpoint status');
      setLoading(false);
    }
  };

  const getProviderName = (providerId: string) => {
    const provider = providers.find(p => p.id === providerId);
    return provider ? provider.name : 'Unknown Provider';
  };

  const formatCacheDuration = (seconds: number) => {
    if (seconds < 60) return `${seconds} seconds`;
    if (seconds < 3600) return `${Math.floor(seconds / 60)} minutes`;
    if (seconds < 86400) return `${Math.floor(seconds / 3600)} hours`;
    return `${Math.floor(seconds / 86400)} days`;
  };

  if (loading && providers.length === 0) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
      </div>
    );
  }

  if (error && providers.length === 0) {
    return (
      <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
        <strong className="font-bold">Error!</strong>
        <span className="block sm:inline"> {error}</span>
      </div>
    );
  }

  return (
    <div>
      <div className="mb-6">
        <h2 className="text-xl font-semibold mb-4">API Endpoint Management</h2>
        <p className="text-gray-600 mb-4">
          View and manage API endpoints for external services. You can create new endpoints, update settings, and enable or disable endpoints.
        </p>

        {/* Create button */}
        <div className="mb-6">
          <button
            onClick={handleCreateClick}
            className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Create New Endpoint
          </button>
        </div>

        {/* Provider filter */}
        <div className="mb-6">
          <label htmlFor="provider-filter" className="block text-sm font-medium text-gray-700 mb-2">
            Filter by Provider:
          </label>
          <select
            id="provider-filter"
            className="w-full md:w-1/3 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
            value={selectedProvider}
            onChange={(e) => handleProviderChange(e.target.value)}
          >
            <option value="all">All Providers</option>
            {providers.map(provider => (
              <option key={provider.id} value={provider.id}>
                {provider.name} ({provider.category})
              </option>
            ))}
          </select>
        </div>

        {/* Edit/Create form */}
        {(editingEndpoint || isCreating) && (
          <div className="bg-white p-6 rounded-lg shadow-md mb-6">
            <h3 className="text-lg font-semibold mb-4">
              {isCreating ? 'Create New Endpoint' : `Edit Endpoint: ${editingEndpoint?.name}`}
            </h3>
            <form onSubmit={handleSubmit}>
              {isCreating && (
                <div className="mb-4">
                  <label htmlFor="provider_id" className="block text-sm font-medium text-gray-700 mb-1">
                    Provider:
                  </label>
                  <select
                    id="provider_id"
                    name="provider_id"
                    value={formData.provider_id}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                    required
                  >
                    <option value="">Select a provider</option>
                    {providers.map(provider => (
                      <option key={provider.id} value={provider.id}>
                        {provider.name} ({provider.category})
                      </option>
                    ))}
                  </select>
                </div>
              )}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div>
                  <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                    Name:
                  </label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                    required
                  />
                </div>
                <div>
                  <label htmlFor="path" className="block text-sm font-medium text-gray-700 mb-1">
                    Path:
                  </label>
                  <input
                    type="text"
                    id="path"
                    name="path"
                    value={formData.path}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                    required
                  />
                </div>
                <div>
                  <label htmlFor="method" className="block text-sm font-medium text-gray-700 mb-1">
                    Method:
                  </label>
                  <select
                    id="method"
                    name="method"
                    value={formData.method}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                  >
                    <option value="GET">GET</option>
                    <option value="POST">POST</option>
                    <option value="PUT">PUT</option>
                    <option value="DELETE">DELETE</option>
                    <option value="PATCH">PATCH</option>
                  </select>
                </div>
                <div>
                  <label htmlFor="cache_duration_seconds" className="block text-sm font-medium text-gray-700 mb-1">
                    Cache Duration (seconds):
                  </label>
                  <input
                    type="number"
                    id="cache_duration_seconds"
                    name="cache_duration_seconds"
                    value={formData.cache_duration_seconds}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                    min="0"
                    required
                  />
                  <p className="text-sm text-gray-500 mt-1">
                    {formatCacheDuration(formData.cache_duration_seconds)}
                  </p>
                </div>
              </div>
              <div className="mb-4">
                <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
                  Description:
                </label>
                <textarea
                  id="description"
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                />
              </div>
              <div className="mb-4">
                <label htmlFor="request_format" className="block text-sm font-medium text-gray-700 mb-1">
                  Request Format (JSON):
                </label>
                <textarea
                  id="request_format"
                  name="request_format"
                  value={formData.request_format ? JSON.stringify(formData.request_format, null, 2) : ''}
                  onChange={(e) => {
                    try {
                      const value = e.target.value ? JSON.parse(e.target.value) : null;
                      setFormData(prev => ({ ...prev, request_format: value }));
                    } catch (err) {
                      // Allow invalid JSON during typing, but don't update the state
                      console.log('Invalid JSON in request format');
                    }
                  }}
                  rows={5}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 font-mono text-sm"
                  placeholder="Enter JSON schema for request format"
                />
                <p className="text-xs text-gray-500 mt-1">
                  Enter a JSON schema that describes the request format
                </p>
              </div>
              <div className="mb-4">
                <label htmlFor="response_format" className="block text-sm font-medium text-gray-700 mb-1">
                  Response Format (JSON):
                </label>
                <textarea
                  id="response_format"
                  name="response_format"
                  value={formData.response_format ? JSON.stringify(formData.response_format, null, 2) : ''}
                  onChange={(e) => {
                    try {
                      const value = e.target.value ? JSON.parse(e.target.value) : null;
                      setFormData(prev => ({ ...prev, response_format: value }));
                    } catch (err) {
                      // Allow invalid JSON during typing, but don't update the state
                      console.log('Invalid JSON in response format');
                    }
                  }}
                  rows={5}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 font-mono text-sm"
                  placeholder="Enter JSON schema for response format"
                />
                <p className="text-xs text-gray-500 mt-1">
                  Enter a JSON schema that describes the response format
                </p>
              </div>
              <div className="mb-4">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="is_enabled"
                    name="is_enabled"
                    checked={formData.is_enabled}
                    onChange={(e) => setFormData(prev => ({ ...prev, is_enabled: e.target.checked }))}
                    className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                  />
                  <label htmlFor="is_enabled" className="ml-2 block text-sm text-gray-900">
                    Enabled
                  </label>
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  When disabled, this endpoint will not be accessible
                </p>
              </div>
              <div className="flex justify-end space-x-2">
                <button
                  type="button"
                  onClick={handleCancelEdit}
                  className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                  disabled={loading}
                >
                  {loading ? 'Saving...' : 'Save Changes'}
                </button>
              </div>
            </form>
          </div>
        )}

        {/* Endpoints table */}
        {loading && endpoints.length === 0 ? (
          <div className="flex justify-center items-center h-32">
            <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary-500"></div>
          </div>
        ) : endpoints.length === 0 ? (
          <div className="bg-white p-6 rounded-lg shadow-md text-center">
            <p className="text-gray-500">No endpoints found for the selected provider.</p>
          </div>
        ) : (
          <div className="bg-white shadow-md rounded-lg overflow-hidden">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Name
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Provider
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Path
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Method
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Cache Duration
                  </th>
                  <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {endpoints.map(endpoint => (
                  <tr key={endpoint.id}>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">{endpoint.name}</div>
                      {endpoint.description && (
                        <div className="text-sm text-gray-500">{endpoint.description}</div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        {endpoint.ApiProvider?.name || getProviderName(endpoint.provider_id)}
                      </div>
                      <div className="text-xs text-gray-500">
                        {endpoint.ApiProvider?.category || 'Unknown Category'}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">{endpoint.path}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                        endpoint.method === 'GET' ? 'bg-green-100 text-green-800' :
                        endpoint.method === 'POST' ? 'bg-blue-100 text-blue-800' :
                        endpoint.method === 'PUT' ? 'bg-yellow-100 text-yellow-800' :
                        endpoint.method === 'DELETE' ? 'bg-red-100 text-red-800' :
                        'bg-gray-100 text-gray-800'
                      }`}>
                        {endpoint.method}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        {formatCacheDuration(endpoint.cache_duration_seconds)}
                      </div>
                      <div className="text-xs text-gray-500">
                        {endpoint.cache_duration_seconds} seconds
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex justify-end space-x-2">
                        <button
                          onClick={() => handleToggleStatus(endpoint)}
                          className={`px-2 py-1 rounded-md ${
                            endpoint.is_enabled 
                              ? 'bg-green-100 text-green-800 hover:bg-green-200' 
                              : 'bg-red-100 text-red-800 hover:bg-red-200'
                          }`}
                        >
                          {endpoint.is_enabled ? 'Enabled' : 'Disabled'}
                        </button>
                        <button
                          onClick={() => handleEditClick(endpoint)}
                          className="text-primary-600 hover:text-primary-900"
                        >
                          Edit
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
};

export default ApiEndpointManagement;
