import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { API_URL } from '../../config';

interface Role {
  id: string;
  name: string;
  description: string;
  farm_id: string | null;
  is_system_role: boolean;
  created_at: string;
  updated_at: string;
}

interface Permission {
  id: string;
  role_id: string;
  feature: string;
  can_view: boolean;
  can_create: boolean;
  can_edit: boolean;
  can_delete: boolean;
  can_approve: boolean;
  can_reject: boolean;
  can_assign: boolean;
  can_export: boolean;
  can_import: boolean;
  can_manage_settings: boolean;
  can_generate_reports: boolean;
  can_view_sensitive: boolean;
}

interface Feature {
  name: string;
  displayName: string;
}

const RoleManagement: React.FC = () => {
  const [roles, setRoles] = useState<Role[]>([]);
  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [features, setFeatures] = useState<Feature[]>([
    { name: 'dashboard', displayName: 'Dashboard' },
    { name: 'farms', displayName: 'Farms' },
    { name: 'users', displayName: 'Users' },
    { name: 'fields', displayName: 'Fields' },
    { name: 'crops', displayName: 'Crops' },
    { name: 'equipment', displayName: 'Equipment' },
    { name: 'inventory', displayName: 'Inventory' },
    { name: 'finances', displayName: 'Finances' },
    { name: 'reports', displayName: 'Reports' },
    { name: 'settings', displayName: 'Settings' },
    { name: 'transport', displayName: 'Transport' },
    { name: 'receipts', displayName: 'Receipts' },
  ]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [showAddModal, setShowAddModal] = useState<boolean>(false);
  const [showEditModal, setShowEditModal] = useState<boolean>(false);
  const [showDeleteModal, setShowDeleteModal] = useState<boolean>(false);
  const [showPermissionsModal, setShowPermissionsModal] = useState<boolean>(false);
  const [currentRole, setCurrentRole] = useState<Role | null>(null);
  const [currentPermissions, setCurrentPermissions] = useState<{ 
    [key: string]: { 
      view: boolean, 
      create: boolean, 
      edit: boolean, 
      delete: boolean,
      approve: boolean,
      reject: boolean,
      assign: boolean,
      export: boolean,
      import: boolean,
      manage_settings: boolean,
      generate_reports: boolean,
      view_sensitive: boolean
    } 
  }>({});
  const [formData, setFormData] = useState({
    name: '',
    description: '',
  });

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Fetch global roles
        const rolesResponse = await axios.get(`${API_URL}/roles/global`);
        setRoles(Array.isArray(rolesResponse.data) ? rolesResponse.data : []);

        setLoading(false);
      } catch (err: any) {
        console.error('Error fetching role data:', err);
        setError(err.response?.data?.error || 'Failed to load role data');
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const handleAddRole = () => {
    setFormData({
      name: '',
      description: '',
    });
    setShowAddModal(true);
  };

  const handleEditRole = (role: Role) => {
    setCurrentRole(role);
    setFormData({
      name: role.name,
      description: role.description || '',
    });
    setShowEditModal(true);
  };

  const handleDeleteRole = (role: Role) => {
    setCurrentRole(role);
    setShowDeleteModal(true);
  };

  const handleManagePermissions = async (role: Role) => {
    try {
      setCurrentRole(role);
      setLoading(true);
      setError(null);

      // Fetch permissions for this role
      const permissionsResponse = await axios.get(`${API_URL}/permissions/defaults/${role.id}`);
      const rolePermissions = Array.isArray(permissionsResponse.data) ? permissionsResponse.data : [];

      // Initialize permissions object
      const permissionsObj: { 
        [key: string]: { 
          view: boolean, 
          create: boolean, 
          edit: boolean, 
          delete: boolean,
          approve: boolean,
          reject: boolean,
          assign: boolean,
          export: boolean,
          import: boolean,
          manage_settings: boolean,
          generate_reports: boolean,
          view_sensitive: boolean
        } 
      } = {};

      // Set default permissions (all false)
      features.forEach(feature => {
        permissionsObj[feature.name] = {
          view: false,
          create: false,
          edit: false,
          delete: false,
          approve: false,
          reject: false,
          assign: false,
          export: false,
          import: false,
          manage_settings: false,
          generate_reports: false,
          view_sensitive: false
        };
      });

      // Update with actual permissions
      rolePermissions.forEach((permission: Permission) => {
        if (permissionsObj[permission.feature]) {
          permissionsObj[permission.feature] = {
            view: permission.can_view,
            create: permission.can_create,
            edit: permission.can_edit,
            delete: permission.can_delete,
            approve: permission.can_approve || false,
            reject: permission.can_reject || false,
            assign: permission.can_assign || false,
            export: permission.can_export || false,
            import: permission.can_import || false,
            manage_settings: permission.can_manage_settings || false,
            generate_reports: permission.can_generate_reports || false,
            view_sensitive: permission.can_view_sensitive || false
          };
        }
      });

      setCurrentPermissions(permissionsObj);
      setLoading(false);
      setShowPermissionsModal(true);
    } catch (err: any) {
      console.error('Error fetching permissions:', err);
      setError(err.response?.data?.error || 'Failed to load permissions');
      setLoading(false);
    }
  };

  const confirmDeleteRole = async () => {
    if (!currentRole) return;

    try {
      setError(null);

      // Delete the role
      await axios.delete(`${API_URL}/roles/${currentRole.id}`);

      // Update the roles list by removing the deleted role
      setRoles(prev => 
        (Array.isArray(prev) ? prev : []).filter(role => 
          role.id !== currentRole.id
        )
      );

      setShowDeleteModal(false);
    } catch (err: any) {
      console.error('Error deleting role:', err);
      setError(err.response?.data?.error || 'Failed to delete role');
    }
  };

  const handleFormChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handlePermissionChange = (
    feature: string, 
    permission: 'view' | 'create' | 'edit' | 'delete' | 'approve' | 'reject' | 'assign' | 'export' | 'import' | 'manage_settings' | 'generate_reports' | 'view_sensitive', 
    value: boolean
  ) => {
    setCurrentPermissions(prev => ({
      ...prev,
      [feature]: {
        ...prev[feature],
        [permission]: value
      }
    }));
  };

  const handleSubmitAdd = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      setError(null);

      // Create the role
      const response = await axios.post(`${API_URL}/roles/global`, {
        name: formData.name,
        description: formData.description
      });

      setRoles(prev => [...(Array.isArray(prev) ? prev : []), response.data]);
      setShowAddModal(false);
    } catch (err: any) {
      console.error('Error adding role:', err);
      setError(err.response?.data?.error || 'Failed to add role');
    }
  };

  const handleSubmitEdit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!currentRole) return;

    try {
      setError(null);

      // Update role data
      const response = await axios.put(`${API_URL}/roles/${currentRole.id}`, {
        name: formData.name,
        description: formData.description
      });

      setRoles(prev => 
        (Array.isArray(prev) ? prev : []).map(role => 
          role.id === currentRole.id ? response.data : role
        )
      );

      setShowEditModal(false);
    } catch (err: any) {
      console.error('Error updating role:', err);
      setError(err.response?.data?.error || 'Failed to update role');
    }
  };

  const handleSubmitPermissions = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!currentRole) return;

    try {
      setError(null);
      setLoading(true);

      // Update permissions for each feature
      for (const feature of features) {
        const featureName = feature.name;
        const permissions = currentPermissions[featureName];

        await axios.post(`${API_URL}/permissions/farm/null/role/${currentRole.id}`, {
          feature: featureName,
          view: permissions.view,
          create: permissions.create,
          edit: permissions.edit,
          delete: permissions.delete,
          approve: permissions.approve,
          reject: permissions.reject,
          assign: permissions.assign,
          export: permissions.export,
          import: permissions.import,
          manage_settings: permissions.manage_settings,
          generate_reports: permissions.generate_reports,
          view_sensitive: permissions.view_sensitive
        });
      }

      setLoading(false);
      setShowPermissionsModal(false);
    } catch (err: any) {
      console.error('Error updating permissions:', err);
      setError(err.response?.data?.error || 'Failed to update permissions');
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
        <strong className="font-bold">Error!</strong>
        <span className="block sm:inline"> {error}</span>
      </div>
    );
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold">Manage Global Roles</h2>
        <button 
          onClick={handleAddRole}
          className="bg-primary-600 hover:bg-primary-700 text-white font-bold py-2 px-4 rounded"
        >
          Add Role
        </button>
      </div>

      <div className="bg-white shadow-md rounded-lg table-container">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Role Name
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Description
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Type
              </th>
              <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {(Array.isArray(roles) ? roles : []).map(role => (
              <tr key={role.id}>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm font-medium text-gray-900">{role.name}</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-500">{role.description || 'No description'}</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                    role.is_system_role ? 'bg-purple-100 text-purple-800' : 'bg-blue-100 text-blue-800'
                  }`}>
                    {role.is_system_role ? 'System Role' : 'Custom Role'}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <button 
                    onClick={() => handleManagePermissions(role)}
                    className="text-green-600 hover:text-green-900 mr-4"
                  >
                    Permissions
                  </button>
                  {!role.is_system_role && (
                    <>
                      <button 
                        onClick={() => handleEditRole(role)}
                        className="text-primary-600 hover:text-primary-900 mr-4"
                      >
                        Edit
                      </button>
                      <button 
                        onClick={() => handleDeleteRole(role)}
                        className="text-red-600 hover:text-red-900"
                      >
                        Delete
                      </button>
                    </>
                  )}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Add Role Modal */}
      {showAddModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full flex items-center justify-center z-50">
          <div className="relative bg-white rounded-lg shadow-xl max-w-md w-full">
            <div className="flex justify-between items-center p-5 border-b">
              <h3 className="text-lg font-semibold">Add New Role</h3>
              <button 
                onClick={() => setShowAddModal(false)}
                className="text-gray-400 hover:text-gray-500"
              >
                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <form onSubmit={handleSubmitAdd} className="p-5">
              <div className="mb-4">
                <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="name">
                  Role Name
                </label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleFormChange}
                  className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                  required
                />
              </div>
              <div className="mb-4">
                <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="description">
                  Description
                </label>
                <textarea
                  id="description"
                  name="description"
                  value={formData.description}
                  onChange={handleFormChange}
                  className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                  rows={3}
                />
              </div>
              <div className="flex justify-end">
                <button
                  type="button"
                  onClick={() => setShowAddModal(false)}
                  className="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded mr-2"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="bg-primary-600 hover:bg-primary-700 text-white font-bold py-2 px-4 rounded"
                >
                  Add Role
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Edit Role Modal */}
      {showEditModal && currentRole && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full flex items-center justify-center z-50">
          <div className="relative bg-white rounded-lg shadow-xl max-w-md w-full">
            <div className="flex justify-between items-center p-5 border-b">
              <h3 className="text-lg font-semibold">Edit Role</h3>
              <button 
                onClick={() => setShowEditModal(false)}
                className="text-gray-400 hover:text-gray-500"
              >
                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <form onSubmit={handleSubmitEdit} className="p-5">
              <div className="mb-4">
                <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="edit-name">
                  Role Name
                </label>
                <input
                  type="text"
                  id="edit-name"
                  name="name"
                  value={formData.name}
                  onChange={handleFormChange}
                  className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                  required
                />
              </div>
              <div className="mb-4">
                <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="edit-description">
                  Description
                </label>
                <textarea
                  id="edit-description"
                  name="description"
                  value={formData.description}
                  onChange={handleFormChange}
                  className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                  rows={3}
                />
              </div>
              <div className="flex justify-end">
                <button
                  type="button"
                  onClick={() => setShowEditModal(false)}
                  className="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded mr-2"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="bg-primary-600 hover:bg-primary-700 text-white font-bold py-2 px-4 rounded"
                >
                  Update Role
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Delete Role Confirmation Modal */}
      {showDeleteModal && currentRole && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full flex items-center justify-center z-50">
          <div className="relative bg-white rounded-lg shadow-xl max-w-md w-full">
            <div className="flex justify-between items-center p-5 border-b">
              <h3 className="text-lg font-semibold">Confirm Delete</h3>
              <button 
                onClick={() => setShowDeleteModal(false)}
                className="text-gray-400 hover:text-gray-500"
              >
                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <div className="p-5">
              <p className="text-gray-700 mb-4">
                Are you sure you want to delete the role <span className="font-semibold">{currentRole.name}</span>?
              </p>
              <p className="text-red-600 mb-4">
                This action cannot be undone. All users with this role will lose their permissions.
              </p>
              <div className="flex justify-end">
                <button
                  type="button"
                  onClick={() => setShowDeleteModal(false)}
                  className="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded mr-2"
                >
                  Cancel
                </button>
                <button
                  type="button"
                  onClick={confirmDeleteRole}
                  className="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded"
                >
                  Delete Role
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Manage Permissions Modal */}
      {showPermissionsModal && currentRole && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full flex items-center justify-center z-50">
          <div className="relative bg-white rounded-lg shadow-xl max-w-6xl w-full mx-auto px-4">
            <div className="flex justify-between items-center p-5 border-b">
              <h3 className="text-lg font-semibold">Manage Permissions for {currentRole.name}</h3>
              <button 
                onClick={() => setShowPermissionsModal(false)}
                className="text-gray-400 hover:text-gray-500"
              >
                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <form onSubmit={handleSubmitPermissions} className="p-5">
              <div className="mb-4 table-container">
                <table className="divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Feature
                      </th>
                      <th scope="col" className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                        View
                      </th>
                      <th scope="col" className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Create
                      </th>
                      <th scope="col" className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Edit
                      </th>
                      <th scope="col" className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Delete
                      </th>
                      <th scope="col" className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Approve
                      </th>
                      <th scope="col" className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Reject
                      </th>
                      <th scope="col" className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Assign
                      </th>
                      <th scope="col" className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Export
                      </th>
                      <th scope="col" className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Import
                      </th>
                      <th scope="col" className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Settings
                      </th>
                      <th scope="col" className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Reports
                      </th>
                      <th scope="col" className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Sensitive
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {features.map(feature => (
                      <tr key={feature.name}>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900">{feature.displayName}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-center">
                          <input
                            type="checkbox"
                            checked={currentPermissions[feature.name]?.view || false}
                            onChange={(e) => handlePermissionChange(feature.name, 'view', e.target.checked)}
                            className="form-checkbox h-5 w-5 text-primary-600"
                          />
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-center">
                          <input
                            type="checkbox"
                            checked={currentPermissions[feature.name]?.create || false}
                            onChange={(e) => handlePermissionChange(feature.name, 'create', e.target.checked)}
                            className="form-checkbox h-5 w-5 text-primary-600"
                          />
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-center">
                          <input
                            type="checkbox"
                            checked={currentPermissions[feature.name]?.edit || false}
                            onChange={(e) => handlePermissionChange(feature.name, 'edit', e.target.checked)}
                            className="form-checkbox h-5 w-5 text-primary-600"
                          />
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-center">
                          <input
                            type="checkbox"
                            checked={currentPermissions[feature.name]?.delete || false}
                            onChange={(e) => handlePermissionChange(feature.name, 'delete', e.target.checked)}
                            className="form-checkbox h-5 w-5 text-primary-600"
                          />
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-center">
                          <input
                            type="checkbox"
                            checked={currentPermissions[feature.name]?.approve || false}
                            onChange={(e) => handlePermissionChange(feature.name, 'approve', e.target.checked)}
                            className="form-checkbox h-5 w-5 text-primary-600"
                          />
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-center">
                          <input
                            type="checkbox"
                            checked={currentPermissions[feature.name]?.reject || false}
                            onChange={(e) => handlePermissionChange(feature.name, 'reject', e.target.checked)}
                            className="form-checkbox h-5 w-5 text-primary-600"
                          />
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-center">
                          <input
                            type="checkbox"
                            checked={currentPermissions[feature.name]?.assign || false}
                            onChange={(e) => handlePermissionChange(feature.name, 'assign', e.target.checked)}
                            className="form-checkbox h-5 w-5 text-primary-600"
                          />
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-center">
                          <input
                            type="checkbox"
                            checked={currentPermissions[feature.name]?.export || false}
                            onChange={(e) => handlePermissionChange(feature.name, 'export', e.target.checked)}
                            className="form-checkbox h-5 w-5 text-primary-600"
                          />
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-center">
                          <input
                            type="checkbox"
                            checked={currentPermissions[feature.name]?.import || false}
                            onChange={(e) => handlePermissionChange(feature.name, 'import', e.target.checked)}
                            className="form-checkbox h-5 w-5 text-primary-600"
                          />
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-center">
                          <input
                            type="checkbox"
                            checked={currentPermissions[feature.name]?.manage_settings || false}
                            onChange={(e) => handlePermissionChange(feature.name, 'manage_settings', e.target.checked)}
                            className="form-checkbox h-5 w-5 text-primary-600"
                          />
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-center">
                          <input
                            type="checkbox"
                            checked={currentPermissions[feature.name]?.generate_reports || false}
                            onChange={(e) => handlePermissionChange(feature.name, 'generate_reports', e.target.checked)}
                            className="form-checkbox h-5 w-5 text-primary-600"
                          />
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-center">
                          <input
                            type="checkbox"
                            checked={currentPermissions[feature.name]?.view_sensitive || false}
                            onChange={(e) => handlePermissionChange(feature.name, 'view_sensitive', e.target.checked)}
                            className="form-checkbox h-5 w-5 text-primary-600"
                          />
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
              <div className="flex justify-end">
                <button
                  type="button"
                  onClick={() => setShowPermissionsModal(false)}
                  className="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded mr-2"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="bg-primary-600 hover:bg-primary-700 text-white font-bold py-2 px-4 rounded"
                >
                  Save Permissions
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default RoleManagement;
