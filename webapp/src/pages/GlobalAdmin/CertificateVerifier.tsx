import React, { useState, useEffect, useRef } from 'react';
import axios from 'axios';
import { API_URL } from '../../config';
import { useDropzone } from 'react-dropzone';

interface Certificate {
  id: string;
  certificate_name: string;
  issuer: string;
  subject: string;
  valid_from: string;
  valid_to: string;
  is_active: boolean;
  farm: {
    id: string;
    name: string;
  };
  user: {
    id: string;
    first_name: string;
    last_name: string;
    email: string;
  };
}

interface Document {
  id: string;
  title: string;
  document_type: string;
  status: string;
  file_path: string;
  has_digital_certificate_signature: boolean;
  has_blockchain_verification: boolean;
  farm_id: string;
  created_at: string;
}

interface VerificationResult {
  verified: boolean;
  documentHash?: string;
  message: string;
  error?: string;
  blockchainVerified?: boolean;
  blockchainMessage?: string;
}

const CertificateVerifier: React.FC = () => {
  const [certificates, setCertificates] = useState<Certificate[]>([]);
  const [documents, setDocuments] = useState<Document[]>([]);
  const [selectedDocument, setSelectedDocument] = useState<Document | null>(null);
  const [verificationResult, setVerificationResult] = useState<VerificationResult | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [verifying, setVerifying] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [farmFilter, setFarmFilter] = useState<string>('');
  const [farms, setFarms] = useState<{id: string, name: string}[]>([]);

  // Certificate creation modal state
  const [showCreateModal, setShowCreateModal] = useState<boolean>(false);
  const [showUploadModal, setShowUploadModal] = useState<boolean>(false);
  const [creatingCertificate, setCreatingCertificate] = useState<boolean>(false);
  const [uploadingCertificate, setUploadingCertificate] = useState<boolean>(false);
  const [deletingCertificate, setDeletingCertificate] = useState<boolean>(false);

  // Certificate creation form state
  const [certificateForm, setCertificateForm] = useState({
    farmId: '',
    certificateName: '',
    commonName: '',
    organization: '',
    organizationalUnit: '',
    locality: '',
    state: '',
    country: '',
    validityDays: '365',
    passphrase: ''
  });

  // Certificate upload state
  const [uploadForm, setUploadForm] = useState({
    farmId: '',
    certificateName: '',
    passphrase: ''
  });
  const [certificateFile, setCertificateFile] = useState<File | null>(null);
  const [privateKeyFile, setPrivateKeyFile] = useState<File | null>(null);

  const [stats, setStats] = useState<{
    totalCertificates: number;
    activeCertificates: number;
    expiredCertificates: number;
    documentsWithSignatures: number;
    documentsWithBlockchain: number;
  }>({
    totalCertificates: 0,
    activeCertificates: 0,
    expiredCertificates: 0,
    documentsWithSignatures: 0,
    documentsWithBlockchain: 0
  });

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch all farms for filtering
      const farmsResponse = await axios.get(`${API_URL}/admin/farms`);
      setFarms(farmsResponse.data.farms || []);

      // Fetch certificate stats
      const statsResponse = await axios.get(`${API_URL}/admin/certificate-stats`);
      setStats(statsResponse.data.stats);

      // Fetch all certificates
      const certificatesResponse = await axios.get(`${API_URL}/admin/digital-certificates`);
      setCertificates(certificatesResponse.data.certificates || []);

      // Fetch all documents with digital signatures
      const documentsResponse = await axios.get(`${API_URL}/admin/signable-documents/with-digital-signatures`);
      setDocuments(documentsResponse.data.documents || []);

      setLoading(false);
    } catch (err: any) {
      console.error('Error fetching data:', err);
      setError(err.response?.data?.error || 'Failed to load data');
      setLoading(false);
    }
  };

  const handleVerifyDocument = async (documentId: string) => {
    try {
      setVerifying(true);
      setError(null);
      setSuccessMessage(null);
      setVerificationResult(null);

      // Find the document
      const document = documents.find(doc => doc.id === documentId);
      if (!document) {
        throw new Error('Document not found');
      }

      setSelectedDocument(document);

      // Verify document signature using admin endpoint
      const verificationResponse = await axios.get(`${API_URL}/admin/signable-documents/${documentId}/verify-signature`);

      const signatureVerification = verificationResponse.data.signatureVerification;
      const blockchainVerification = verificationResponse.data.blockchainVerification;

      // Set verification result
      setVerificationResult({
        verified: signatureVerification.verified,
        documentHash: signatureVerification.documentHash,
        message: signatureVerification.message,
        blockchainVerified: blockchainVerification?.verified,
        blockchainMessage: blockchainVerification?.message
      });

      if (signatureVerification.verified) {
        setSuccessMessage(`Document "${document.title}" has been verified successfully.`);
      } else {
        setError(`Document verification failed: ${signatureVerification.message}`);
      }

      setVerifying(false);
    } catch (err: any) {
      console.error('Error verifying document:', err);
      setError(err.response?.data?.error || 'Failed to verify document');
      setVerifying(false);
    }
  };

  // Handle certificate form input changes
  const handleCertificateFormChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setCertificateForm(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle upload form input changes
  const handleUploadFormChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setUploadForm(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle certificate file drop
  const onCertificateFileDrop = (acceptedFiles: File[]) => {
    if (acceptedFiles.length > 0) {
      setCertificateFile(acceptedFiles[0]);
    }
  };

  // Handle private key file drop
  const onPrivateKeyFileDrop = (acceptedFiles: File[]) => {
    if (acceptedFiles.length > 0) {
      setPrivateKeyFile(acceptedFiles[0]);
    }
  };

  // Certificate file dropzone
  const {
    getRootProps: getCertificateRootProps,
    getInputProps: getCertificateInputProps,
    isDragActive: isCertificateDragActive
  } = useDropzone({
    onDrop: onCertificateFileDrop,
    accept: {
      'application/x-x509-ca-cert': ['.crt', '.pem', '.cer'],
      'application/pkix-cert': ['.crt', '.pem', '.cer'],
      'text/plain': ['.crt', '.pem', '.cer']
    },
    maxFiles: 1
  });

  // Private key file dropzone
  const {
    getRootProps: getPrivateKeyRootProps,
    getInputProps: getPrivateKeyInputProps,
    isDragActive: isPrivateKeyDragActive
  } = useDropzone({
    onDrop: onPrivateKeyFileDrop,
    accept: {
      'application/x-pem-file': ['.pem', '.key'],
      'text/plain': ['.pem', '.key']
    },
    maxFiles: 1
  });

  // Create a new certificate
  const handleCreateCertificate = async () => {
    try {
      setCreatingCertificate(true);
      setError(null);
      setSuccessMessage(null);

      // Validate form
      if (!certificateForm.farmId) {
        throw new Error('Please select a farm');
      }
      if (!certificateForm.certificateName) {
        throw new Error('Certificate name is required');
      }
      if (!certificateForm.commonName) {
        throw new Error('Common name is required');
      }
      if (!certificateForm.organization) {
        throw new Error('Organization is required');
      }

      // Create certificate
      const response = await axios.post(`${API_URL}/admin/digital-certificates`, certificateForm);

      // Update certificates list
      await fetchData();

      // Show success message
      setSuccessMessage(`Certificate "${response.data.certificate.name}" created successfully`);

      // Close modal
      setShowCreateModal(false);

      // Reset form
      setCertificateForm({
        farmId: '',
        certificateName: '',
        commonName: '',
        organization: '',
        organizationalUnit: '',
        locality: '',
        state: '',
        country: '',
        validityDays: '365',
        passphrase: ''
      });

      setCreatingCertificate(false);
    } catch (err: any) {
      console.error('Error creating certificate:', err);
      setError(err.response?.data?.error || err.message || 'Failed to create certificate');
      setCreatingCertificate(false);
    }
  };

  // Upload an existing certificate
  const handleUploadCertificate = async () => {
    try {
      setUploadingCertificate(true);
      setError(null);
      setSuccessMessage(null);

      // Validate form
      if (!uploadForm.farmId) {
        throw new Error('Please select a farm');
      }
      if (!uploadForm.certificateName) {
        throw new Error('Certificate name is required');
      }
      if (!certificateFile) {
        throw new Error('Certificate file is required');
      }
      if (!privateKeyFile) {
        throw new Error('Private key file is required');
      }

      // Create form data
      const formData = new FormData();
      formData.append('farmId', uploadForm.farmId);
      formData.append('certificateName', uploadForm.certificateName);
      if (uploadForm.passphrase) {
        formData.append('passphrase', uploadForm.passphrase);
      }
      formData.append('certificate', certificateFile);
      formData.append('privateKey', privateKeyFile);

      // Upload certificate
      const response = await axios.post(`${API_URL}/admin/digital-certificates/upload`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });

      // Update certificates list
      await fetchData();

      // Show success message
      setSuccessMessage(`Certificate "${response.data.certificate.name}" uploaded successfully`);

      // Close modal
      setShowUploadModal(false);

      // Reset form
      setUploadForm({
        farmId: '',
        certificateName: '',
        passphrase: ''
      });
      setCertificateFile(null);
      setPrivateKeyFile(null);

      setUploadingCertificate(false);
    } catch (err: any) {
      console.error('Error uploading certificate:', err);
      setError(err.response?.data?.error || err.message || 'Failed to upload certificate');
      setUploadingCertificate(false);
    }
  };

  // Delete a certificate
  const handleDeleteCertificate = async (certificateId: string) => {
    if (!window.confirm('Are you sure you want to delete this certificate? This action cannot be undone.')) {
      return;
    }

    try {
      setDeletingCertificate(true);
      setError(null);
      setSuccessMessage(null);

      // Delete certificate
      await axios.delete(`${API_URL}/admin/digital-certificates/${certificateId}`);

      // Update certificates list
      await fetchData();

      // Show success message
      setSuccessMessage('Certificate deleted successfully');

      setDeletingCertificate(false);
    } catch (err: any) {
      console.error('Error deleting certificate:', err);
      setError(err.response?.data?.error || 'Failed to delete certificate');
      setDeletingCertificate(false);
    }
  };

  // Filter documents based on search term and farm filter
  const filteredDocuments = documents.filter(doc => {
    const matchesSearch = searchTerm === '' || 
      doc.title.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesFarm = farmFilter === '' || doc.farm_id === farmFilter;
    return matchesSearch && matchesFarm;
  });

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold mb-6">Certificate and Document Verification</h1>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4 mb-8">
        <div className="bg-white p-4 rounded-lg shadow">
          <h3 className="text-lg font-semibold text-gray-700">Total Certificates</h3>
          <p className="text-3xl font-bold text-blue-600">{stats.totalCertificates}</p>
        </div>
        <div className="bg-white p-4 rounded-lg shadow">
          <h3 className="text-lg font-semibold text-gray-700">Active Certificates</h3>
          <p className="text-3xl font-bold text-green-600">{stats.activeCertificates}</p>
        </div>
        <div className="bg-white p-4 rounded-lg shadow">
          <h3 className="text-lg font-semibold text-gray-700">Expired Certificates</h3>
          <p className="text-3xl font-bold text-red-600">{stats.expiredCertificates}</p>
        </div>
        <div className="bg-white p-4 rounded-lg shadow">
          <h3 className="text-lg font-semibold text-gray-700">Signed Documents</h3>
          <p className="text-3xl font-bold text-purple-600">{stats.documentsWithSignatures}</p>
        </div>
        <div className="bg-white p-4 rounded-lg shadow">
          <h3 className="text-lg font-semibold text-gray-700">Blockchain Verified</h3>
          <p className="text-3xl font-bold text-amber-600">{stats.documentsWithBlockchain}</p>
        </div>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          <p>{error}</p>
        </div>
      )}

      {successMessage && (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
          <p>{successMessage}</p>
        </div>
      )}

      <div className="mb-8">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold">Digital Certificates</h2>
          <div className="flex space-x-2">
            <button
              onClick={() => setShowCreateModal(true)}
              className="bg-green-500 hover:bg-green-600 text-white py-2 px-4 rounded"
            >
              Create Certificate
            </button>
            <button
              onClick={() => setShowUploadModal(true)}
              className="bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded"
            >
              Upload Certificate
            </button>
          </div>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full bg-white border border-gray-200">
            <thead>
              <tr className="bg-gray-100">
                <th className="py-2 px-4 border-b text-left">Name</th>
                <th className="py-2 px-4 border-b text-left">Issuer</th>
                <th className="py-2 px-4 border-b text-left">Subject</th>
                <th className="py-2 px-4 border-b text-left">Valid From</th>
                <th className="py-2 px-4 border-b text-left">Valid To</th>
                <th className="py-2 px-4 border-b text-left">Farm</th>
                <th className="py-2 px-4 border-b text-left">Status</th>
                <th className="py-2 px-4 border-b text-left">Actions</th>
              </tr>
            </thead>
            <tbody>
              {certificates.length === 0 ? (
                <tr>
                  <td colSpan={8} className="py-4 px-4 text-center text-gray-500">
                    No certificates found
                  </td>
                </tr>
              ) : (
                certificates.map(cert => (
                  <tr key={cert.id} className="hover:bg-gray-50">
                    <td className="py-2 px-4 border-b">{cert.certificate_name}</td>
                    <td className="py-2 px-4 border-b">{cert.issuer}</td>
                    <td className="py-2 px-4 border-b">{cert.subject}</td>
                    <td className="py-2 px-4 border-b">{new Date(cert.valid_from).toLocaleDateString()}</td>
                    <td className="py-2 px-4 border-b">{new Date(cert.valid_to).toLocaleDateString()}</td>
                    <td className="py-2 px-4 border-b">{cert.farm?.name || 'Unknown'}</td>
                    <td className="py-2 px-4 border-b">
                      <span className={`px-2 py-1 rounded text-xs ${cert.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                        {cert.is_active ? 'Active' : 'Inactive'}
                      </span>
                    </td>
                    <td className="py-2 px-4 border-b">
                      <button
                        onClick={() => handleDeleteCertificate(cert.id)}
                        disabled={deletingCertificate}
                        className="bg-red-500 hover:bg-red-600 text-white py-1 px-3 rounded text-sm"
                      >
                        Delete
                      </button>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Create Certificate Modal */}
      {showCreateModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-8 max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <h2 className="text-xl font-semibold mb-4">Create New Certificate</h2>

            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">Farm</label>
              <select
                name="farmId"
                value={certificateForm.farmId}
                onChange={handleCertificateFormChange}
                className="w-full p-2 border border-gray-300 rounded"
                disabled={creatingCertificate}
              >
                <option value="">Select a farm</option>
                {farms.map(farm => (
                  <option key={farm.id} value={farm.id}>{farm.name}</option>
                ))}
              </select>
            </div>

            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">Certificate Name</label>
              <input
                type="text"
                name="certificateName"
                value={certificateForm.certificateName}
                onChange={handleCertificateFormChange}
                className="w-full p-2 border border-gray-300 rounded"
                placeholder="Enter a name for this certificate"
                disabled={creatingCertificate}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Common Name (CN)</label>
                <input
                  type="text"
                  name="commonName"
                  value={certificateForm.commonName}
                  onChange={handleCertificateFormChange}
                  className="w-full p-2 border border-gray-300 rounded"
                  placeholder="e.g., *.example.com"
                  disabled={creatingCertificate}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Organization (O)</label>
                <input
                  type="text"
                  name="organization"
                  value={certificateForm.organization}
                  onChange={handleCertificateFormChange}
                  className="w-full p-2 border border-gray-300 rounded"
                  placeholder="e.g., Example Inc."
                  disabled={creatingCertificate}
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Organizational Unit (OU)</label>
                <input
                  type="text"
                  name="organizationalUnit"
                  value={certificateForm.organizationalUnit}
                  onChange={handleCertificateFormChange}
                  className="w-full p-2 border border-gray-300 rounded"
                  placeholder="e.g., IT Department"
                  disabled={creatingCertificate}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Locality (L)</label>
                <input
                  type="text"
                  name="locality"
                  value={certificateForm.locality}
                  onChange={handleCertificateFormChange}
                  className="w-full p-2 border border-gray-300 rounded"
                  placeholder="e.g., San Francisco"
                  disabled={creatingCertificate}
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">State/Province (ST)</label>
                <input
                  type="text"
                  name="state"
                  value={certificateForm.state}
                  onChange={handleCertificateFormChange}
                  className="w-full p-2 border border-gray-300 rounded"
                  placeholder="e.g., California"
                  disabled={creatingCertificate}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Country (C)</label>
                <input
                  type="text"
                  name="country"
                  value={certificateForm.country}
                  onChange={handleCertificateFormChange}
                  className="w-full p-2 border border-gray-300 rounded"
                  placeholder="e.g., US"
                  disabled={creatingCertificate}
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Validity (days)</label>
                <input
                  type="number"
                  name="validityDays"
                  value={certificateForm.validityDays}
                  onChange={handleCertificateFormChange}
                  className="w-full p-2 border border-gray-300 rounded"
                  min="1"
                  max="3650"
                  disabled={creatingCertificate}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Passphrase (optional)</label>
                <input
                  type="password"
                  name="passphrase"
                  value={certificateForm.passphrase}
                  onChange={handleCertificateFormChange}
                  className="w-full p-2 border border-gray-300 rounded"
                  placeholder="Enter a passphrase to protect the private key"
                  disabled={creatingCertificate}
                />
              </div>
            </div>

            <div className="flex justify-end space-x-2 mt-6">
              <button
                onClick={() => setShowCreateModal(false)}
                className="bg-gray-300 hover:bg-gray-400 text-gray-800 py-2 px-4 rounded"
                disabled={creatingCertificate}
              >
                Cancel
              </button>
              <button
                onClick={handleCreateCertificate}
                disabled={creatingCertificate}
                className="bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded"
              >
                {creatingCertificate ? 'Creating...' : 'Create Certificate'}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Upload Certificate Modal */}
      {showUploadModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-8 max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <h2 className="text-xl font-semibold mb-4">Upload Existing Certificate</h2>

            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">Farm</label>
              <select
                name="farmId"
                value={uploadForm.farmId}
                onChange={handleUploadFormChange}
                className="w-full p-2 border border-gray-300 rounded"
                disabled={uploadingCertificate}
              >
                <option value="">Select a farm</option>
                {farms.map(farm => (
                  <option key={farm.id} value={farm.id}>{farm.name}</option>
                ))}
              </select>
            </div>

            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">Certificate Name</label>
              <input
                type="text"
                name="certificateName"
                value={uploadForm.certificateName}
                onChange={handleUploadFormChange}
                className="w-full p-2 border border-gray-300 rounded"
                placeholder="Enter a name for this certificate"
                disabled={uploadingCertificate}
              />
            </div>

            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">Certificate File (.crt, .pem, .cer)</label>
              <div
                {...getCertificateRootProps()}
                className={`border-2 border-dashed p-4 rounded-lg cursor-pointer ${
                  isCertificateDragActive ? 'border-blue-500 bg-blue-50' : 'border-gray-300'
                }`}
              >
                <input {...getCertificateInputProps()} disabled={uploadingCertificate} />
                {certificateFile ? (
                  <p className="text-sm text-center text-gray-700">
                    Selected file: <span className="font-semibold">{certificateFile.name}</span>
                  </p>
                ) : (
                  <p className="text-sm text-center text-gray-500">
                    Drag and drop a certificate file here, or click to select a file
                  </p>
                )}
              </div>
            </div>

            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">Private Key File (.key, .pem)</label>
              <div
                {...getPrivateKeyRootProps()}
                className={`border-2 border-dashed p-4 rounded-lg cursor-pointer ${
                  isPrivateKeyDragActive ? 'border-blue-500 bg-blue-50' : 'border-gray-300'
                }`}
              >
                <input {...getPrivateKeyInputProps()} disabled={uploadingCertificate} />
                {privateKeyFile ? (
                  <p className="text-sm text-center text-gray-700">
                    Selected file: <span className="font-semibold">{privateKeyFile.name}</span>
                  </p>
                ) : (
                  <p className="text-sm text-center text-gray-500">
                    Drag and drop a private key file here, or click to select a file
                  </p>
                )}
              </div>
            </div>

            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">Passphrase (if private key is encrypted)</label>
              <input
                type="password"
                name="passphrase"
                value={uploadForm.passphrase}
                onChange={handleUploadFormChange}
                className="w-full p-2 border border-gray-300 rounded"
                placeholder="Enter the passphrase for the private key"
                disabled={uploadingCertificate}
              />
            </div>

            <div className="flex justify-end space-x-2 mt-6">
              <button
                onClick={() => setShowUploadModal(false)}
                className="bg-gray-300 hover:bg-gray-400 text-gray-800 py-2 px-4 rounded"
                disabled={uploadingCertificate}
              >
                Cancel
              </button>
              <button
                onClick={handleUploadCertificate}
                disabled={uploadingCertificate}
                className="bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded"
              >
                {uploadingCertificate ? 'Uploading...' : 'Upload Certificate'}
              </button>
            </div>
          </div>
        </div>
      )}

      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-4">Documents with Digital Signatures</h2>

        <div className="flex flex-wrap gap-4 mb-4">
          <div className="w-full md:w-1/3">
            <label className="block text-sm font-medium text-gray-700 mb-1">Search Documents</label>
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="Search by title..."
              className="w-full p-2 border border-gray-300 rounded"
            />
          </div>

          <div className="w-full md:w-1/3">
            <label className="block text-sm font-medium text-gray-700 mb-1">Filter by Farm</label>
            <select
              value={farmFilter}
              onChange={(e) => setFarmFilter(e.target.value)}
              className="w-full p-2 border border-gray-300 rounded"
            >
              <option value="">All Farms</option>
              {farms.map(farm => (
                <option key={farm.id} value={farm.id}>{farm.name}</option>
              ))}
            </select>
          </div>
        </div>

        <div className="overflow-x-auto">
          <table className="min-w-full bg-white border border-gray-200">
            <thead>
              <tr className="bg-gray-100">
                <th className="py-2 px-4 border-b text-left">Title</th>
                <th className="py-2 px-4 border-b text-left">Type</th>
                <th className="py-2 px-4 border-b text-left">Status</th>
                <th className="py-2 px-4 border-b text-left">Created</th>
                <th className="py-2 px-4 border-b text-left">Digital Signature</th>
                <th className="py-2 px-4 border-b text-left">Blockchain</th>
                <th className="py-2 px-4 border-b text-left">Actions</th>
              </tr>
            </thead>
            <tbody>
              {filteredDocuments.length === 0 ? (
                <tr>
                  <td colSpan={7} className="py-4 px-4 text-center text-gray-500">
                    No documents found
                  </td>
                </tr>
              ) : (
                filteredDocuments.map(doc => (
                  <tr key={doc.id} className="hover:bg-gray-50">
                    <td className="py-2 px-4 border-b">{doc.title}</td>
                    <td className="py-2 px-4 border-b">{doc.document_type}</td>
                    <td className="py-2 px-4 border-b">
                      <span className={`px-2 py-1 rounded text-xs ${
                        doc.status === 'completed' ? 'bg-green-100 text-green-800' : 
                        doc.status === 'draft' ? 'bg-gray-100 text-gray-800' :
                        'bg-blue-100 text-blue-800'
                      }`}>
                        {doc.status}
                      </span>
                    </td>
                    <td className="py-2 px-4 border-b">{new Date(doc.created_at).toLocaleDateString()}</td>
                    <td className="py-2 px-4 border-b">
                      {doc.has_digital_certificate_signature ? (
                        <span className="px-2 py-1 rounded text-xs bg-green-100 text-green-800">Yes</span>
                      ) : (
                        <span className="px-2 py-1 rounded text-xs bg-red-100 text-red-800">No</span>
                      )}
                    </td>
                    <td className="py-2 px-4 border-b">
                      {doc.has_blockchain_verification ? (
                        <span className="px-2 py-1 rounded text-xs bg-green-100 text-green-800">Yes</span>
                      ) : (
                        <span className="px-2 py-1 rounded text-xs bg-red-100 text-red-800">No</span>
                      )}
                    </td>
                    <td className="py-2 px-4 border-b">
                      <button
                        onClick={() => handleVerifyDocument(doc.id)}
                        disabled={verifying}
                        className="bg-blue-500 hover:bg-blue-600 text-white py-1 px-3 rounded text-sm"
                      >
                        {verifying && selectedDocument?.id === doc.id ? 'Verifying...' : 'Verify'}
                      </button>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>

      {verificationResult && selectedDocument && (
        <div className="bg-white border border-gray-200 rounded-lg p-6 mb-8">
          <h3 className="text-lg font-semibold mb-4">Verification Results for "{selectedDocument.title}"</h3>

          <div className="mb-4">
            <h4 className="font-medium mb-2">Digital Signature Verification</h4>
            <div className={`p-4 rounded ${verificationResult.verified ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'}`}>
              <p className={`font-semibold ${verificationResult.verified ? 'text-green-700' : 'text-red-700'}`}>
                {verificationResult.verified ? 'Signature Valid' : 'Signature Invalid'}
              </p>
              <p className="text-sm mt-1">{verificationResult.message}</p>
              {verificationResult.documentHash && (
                <div className="mt-2">
                  <p className="text-xs text-gray-600">Document Hash:</p>
                  <p className="text-xs font-mono bg-gray-100 p-2 rounded mt-1 break-all">{verificationResult.documentHash}</p>
                </div>
              )}
            </div>
          </div>

          {verificationResult.blockchainVerified !== undefined && (
            <div>
              <h4 className="font-medium mb-2">Blockchain Verification</h4>
              <div className={`p-4 rounded ${verificationResult.blockchainVerified ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'}`}>
                <p className={`font-semibold ${verificationResult.blockchainVerified ? 'text-green-700' : 'text-red-700'}`}>
                  {verificationResult.blockchainVerified ? 'Blockchain Verification Valid' : 'Blockchain Verification Invalid'}
                </p>
                <p className="text-sm mt-1">{verificationResult.blockchainMessage}</p>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default CertificateVerifier;
