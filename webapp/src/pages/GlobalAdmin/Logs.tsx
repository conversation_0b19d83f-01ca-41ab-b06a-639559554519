import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { API_URL } from '../../config';

interface SentryIssue {
  id: string;
  title: string;
  culprit: string;
  level: string;
  status: string;
  firstSeen: string;
  lastSeen: string;
  count: number;
  userCount: number;
  project: string;
  type: string;
}

interface SentryEvent {
  id: string;
  message: string;
  dateCreated: string;
  user: {
    id: string;
    email: string;
    username: string;
    ip_address: string;
  } | null;
  tags: {
    key: string;
    value: string;
  }[];
  entries: any[];
}

interface SentryStats {
  [date: string]: [number, number]; // [timestamp, count]
}

const Logs: React.FC = () => {
  const [issues, setIssues] = useState<SentryIssue[]>([]);
  const [selectedIssue, setSelectedIssue] = useState<SentryIssue | null>(null);
  const [issueEvents, setIssueEvents] = useState<SentryEvent[]>([]);
  const [stats, setStats] = useState<SentryStats | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [showEventModal, setShowEventModal] = useState<boolean>(false);
  const [selectedEvent, setSelectedEvent] = useState<SentryEvent | null>(null);
  const [activeFilter, setActiveFilter] = useState<'all' | 'unresolved' | 'resolved'>('all');

  useEffect(() => {
    fetchIssues();
    fetchStats();
  }, [activeFilter]);

  const fetchIssues = async () => {
    try {
      setLoading(true);
      setError(null);

      let queryParams = new URLSearchParams();
      if (searchQuery) {
        queryParams.append('query', searchQuery);
      }
      if (activeFilter === 'unresolved') {
        queryParams.append('status', 'unresolved');
      } else if (activeFilter === 'resolved') {
        queryParams.append('status', 'resolved');
      }

      const response = await axios.get(`${API_URL}/sentry?${queryParams.toString()}`);
      setIssues(response.data.issues || []);
      setLoading(false);
    } catch (err: any) {
      console.error('Error fetching Sentry issues:', err);
      setError(err.response?.data?.error || 'Failed to load Sentry issues');
      setLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      const response = await axios.get(`${API_URL}/sentry/stats/overview`);
      setStats(response.data.stats || {});
    } catch (err: any) {
      console.error('Error fetching Sentry stats:', err);
    }
  };

  const fetchIssueEvents = async (issueId: string) => {
    try {
      setLoading(true);
      setError(null);

      const response = await axios.get(`${API_URL}/sentry/${issueId}/events`);
      setIssueEvents(response.data.events || []);
      setLoading(false);
    } catch (err: any) {
      console.error('Error fetching Sentry issue events:', err);
      setError(err.response?.data?.error || 'Failed to load Sentry issue events');
      setLoading(false);
    }
  };

  const handleIssueClick = async (issue: SentryIssue) => {
    setSelectedIssue(issue);
    await fetchIssueEvents(issue.id);
  };

  const handleEventClick = (event: SentryEvent) => {
    setSelectedEvent(event);
    setShowEventModal(true);
  };

  const handleResolveIssue = async (issueId: string) => {
    try {
      setLoading(true);
      setError(null);

      await axios.put(`${API_URL}/sentry/${issueId}/resolve`);

      // Refresh issues
      await fetchIssues();
      setLoading(false);
    } catch (err: any) {
      console.error('Error resolving Sentry issue:', err);
      setError(err.response?.data?.error || 'Failed to resolve Sentry issue');
      setLoading(false);
    }
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    fetchIssues();
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const getLevelBadgeClass = (level: string) => {
    switch (level.toLowerCase()) {
      case 'error':
        return 'bg-red-100 text-red-800';
      case 'warning':
        return 'bg-yellow-100 text-yellow-800';
      case 'info':
        return 'bg-blue-100 text-blue-800';
      case 'debug':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusBadgeClass = (status: string) => {
    switch (status.toLowerCase()) {
      case 'resolved':
        return 'bg-green-100 text-green-800';
      case 'unresolved':
        return 'bg-red-100 text-red-800';
      case 'ignored':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading && !selectedIssue) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
      </div>
    );
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <div>
          <h2 className="text-xl font-semibold">Error Logs</h2>
          <p className="text-sm text-gray-600 mt-1">
            View and manage application errors captured by Sentry
          </p>
        </div>
        <form onSubmit={handleSearch} className="flex">
          <input
            type="text"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            placeholder="Search issues..."
            className="border border-gray-300 rounded-l px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500"
          />
          <button
            type="submit"
            className="bg-primary-600 hover:bg-primary-700 text-white font-bold py-2 px-4 rounded-r"
          >
            Search
          </button>
        </form>
      </div>

      <div className="flex space-x-4 mb-4">
        <div className="flex items-center">
          <input
            type="radio"
            id="filter-all"
            checked={activeFilter === 'all'}
            onChange={() => setActiveFilter('all')}
            className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300"
          />
          <label htmlFor="filter-all" className="ml-2 block text-sm text-gray-700">
            Show All
          </label>
        </div>
        <div className="flex items-center">
          <input
            type="radio"
            id="filter-unresolved"
            checked={activeFilter === 'unresolved'}
            onChange={() => setActiveFilter('unresolved')}
            className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300"
          />
          <label htmlFor="filter-unresolved" className="ml-2 block text-sm text-gray-700">
            Unresolved Issues
          </label>
        </div>
        <div className="flex items-center">
          <input
            type="radio"
            id="filter-resolved"
            checked={activeFilter === 'resolved'}
            onChange={() => setActiveFilter('resolved')}
            className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300"
          />
          <label htmlFor="filter-resolved" className="ml-2 block text-sm text-gray-700">
            Resolved Issues
          </label>
        </div>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
          <strong className="font-bold">Error!</strong>
          <span className="block sm:inline"> {error}</span>
        </div>
      )}

      {selectedIssue ? (
        <div>
          <button
            onClick={() => setSelectedIssue(null)}
            className="mb-4 flex items-center text-primary-600 hover:text-primary-900"
          >
            <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
            </svg>
            Back to Issues
          </button>

          <div className="bg-white shadow-md rounded-lg overflow-hidden mb-6">
            <div className="px-6 py-4 border-b border-gray-200">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-semibold">{selectedIssue.title}</h3>
                <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusBadgeClass(selectedIssue.status)}`}>
                  {selectedIssue.status}
                </span>
              </div>
              <p className="text-sm text-gray-600 mt-1">{selectedIssue.culprit}</p>
              <div className="mt-2">
                <span className="text-sm text-gray-500">First seen: {formatDate(selectedIssue.firstSeen)}</span>
                <span className="text-sm text-gray-500 ml-4">Last seen: {formatDate(selectedIssue.lastSeen)}</span>
                <span className="text-sm text-gray-500 ml-4">Events: {selectedIssue.count}</span>
                <span className="text-sm text-gray-500 ml-4">Users affected: {selectedIssue.userCount}</span>
              </div>
            </div>

            <div className="px-6 py-4">
              <h4 className="text-md font-semibold mb-2">Recent Events</h4>

              {loading ? (
                <div className="flex justify-center items-center h-32">
                  <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary-500"></div>
                </div>
              ) : (
                <div className="table-container">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Event ID
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Message
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          User
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Date
                        </th>
                        <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {issueEvents.length === 0 ? (
                        <tr>
                          <td colSpan={5} className="px-6 py-4 text-center text-sm text-gray-500">
                            No events found for this issue.
                          </td>
                        </tr>
                      ) : (
                        issueEvents.map(event => (
                          <tr key={event.id} className="hover:bg-gray-50 cursor-pointer" onClick={() => handleEventClick(event)}>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {event.id.substring(0, 8)}...
                            </td>
                            <td className="px-6 py-4">
                              <div className="text-sm text-gray-900">{event.message}</div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {event.user ? (event.user.email || event.user.username || event.user.ip_address) : '-'}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {formatDate(event.dateCreated)}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                              <button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleEventClick(event);
                                }}
                                className="text-primary-600 hover:text-primary-900"
                              >
                                View Details
                              </button>
                            </td>
                          </tr>
                        ))
                      )}
                    </tbody>
                  </table>
                </div>
              )}
            </div>

            {selectedIssue.status !== 'resolved' && (
              <div className="px-6 py-4 bg-gray-50 border-t border-gray-200">
                <button
                  onClick={() => handleResolveIssue(selectedIssue.id)}
                  className="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded"
                >
                  Resolve Issue
                </button>
              </div>
            )}
          </div>
        </div>
      ) : (
        <div className="bg-white shadow-md rounded-lg table-container">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Issue
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Level
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Events
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Users
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Last Seen
                </th>
                <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {issues.length === 0 ? (
                <tr>
                  <td colSpan={7} className="px-6 py-4 text-center text-sm text-gray-500">
                    No issues found. {searchQuery ? 'Try a different search query.' : ''}
                  </td>
                </tr>
              ) : (
                issues.map(issue => (
                  <tr key={issue.id} className="hover:bg-gray-50 cursor-pointer" onClick={() => handleIssueClick(issue)}>
                    <td className="px-6 py-4">
                      <div className="text-sm font-medium text-gray-900">{issue.title}</div>
                      <div className="text-sm text-gray-500">{issue.culprit}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getLevelBadgeClass(issue.level)}`}>
                        {issue.level}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusBadgeClass(issue.status)}`}>
                        {issue.status}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {issue.count}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {issue.userCount}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {formatDate(issue.lastSeen)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleIssueClick(issue);
                        }}
                        className="text-primary-600 hover:text-primary-900 mr-4"
                      >
                        View Details
                      </button>
                      {issue.status !== 'resolved' && (
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleResolveIssue(issue.id);
                          }}
                          className="text-green-600 hover:text-green-900"
                        >
                          Resolve
                        </button>
                      )}
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      )}

      {/* Event Detail Modal */}
      {showEventModal && selectedEvent && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full flex items-center justify-center">
          <div className="relative bg-white rounded-lg shadow-xl max-w-4xl w-full">
            <div className="flex justify-between items-center p-5 border-b">
              <h3 className="text-lg font-semibold">Event Details</h3>
              <button 
                onClick={() => setShowEventModal(false)}
                className="text-gray-400 hover:text-gray-500"
              >
                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <div className="p-5 overflow-y-auto max-h-[70vh]">
              <div className="mb-4">
                <h4 className="text-md font-semibold mb-2">Message</h4>
                <p className="text-sm text-gray-800">{selectedEvent.message}</p>
              </div>

              <div className="mb-4">
                <h4 className="text-md font-semibold mb-2">Date</h4>
                <p className="text-sm text-gray-800">{formatDate(selectedEvent.dateCreated)}</p>
              </div>

              {selectedEvent.user && (
                <div className="mb-4">
                  <h4 className="text-md font-semibold mb-2">User</h4>
                  <p className="text-sm text-gray-800">
                    {selectedEvent.user.email && <span className="block">Email: {selectedEvent.user.email}</span>}
                    {selectedEvent.user.username && <span className="block">Username: {selectedEvent.user.username}</span>}
                    {selectedEvent.user.ip_address && <span className="block">IP: {selectedEvent.user.ip_address}</span>}
                  </p>
                </div>
              )}

              {selectedEvent.tags && selectedEvent.tags.length > 0 && (
                <div className="mb-4">
                  <h4 className="text-md font-semibold mb-2">Tags</h4>
                  <div className="flex flex-wrap gap-2">
                    {selectedEvent.tags.map((tag, index) => (
                      <span key={index} className="px-2 py-1 bg-gray-100 text-gray-800 text-xs rounded">
                        {tag.key}: {tag.value}
                      </span>
                    ))}
                  </div>
                </div>
              )}

              {selectedEvent.entries && selectedEvent.entries.length > 0 && (
                <div className="mb-4">
                  <h4 className="text-md font-semibold mb-2">Stack Trace</h4>
                  <pre className="bg-gray-100 p-4 rounded overflow-x-auto text-xs">
                    {JSON.stringify(selectedEvent.entries, null, 2)}
                  </pre>
                </div>
              )}
            </div>
            <div className="flex justify-end p-5 border-t">
              <button
                type="button"
                onClick={() => setShowEventModal(false)}
                className="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Logs;
