                />
              </div>
              <div className="mb-4">
                <label className="block text-gray-700 text-sm font-bold mb-2">
                  Features
                </label>
                <p className="text-sm text-gray-500 mb-4">
                  Select which features will be available in this subscription plan. Users will only see menu items for features included in their plan.
                </p>
                <div className="max-h-60 overflow-y-auto border rounded-md p-4">
                  {/* Group features by category */}
                  {Array.from(new Set(availableFeatures.map(f => f.category))).map(category => (
                    <div key={category} className="mb-4">
                      <h4 className="font-medium text-gray-700 mb-2">{category}</h4>
                      <div className="space-y-2 ml-2">
                        {availableFeatures
                          .filter(feature => feature.category === category)
                          .map(feature => (
                            <div key={feature.id} className="flex items-center">
                              <input
                                type="checkbox"
                                id={`feature-${feature.id}`}
                                checked={!!selectedFeatures[feature.id]}
                                onChange={() => handleFeatureToggle(feature.id)}
                                className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                              />
                              <label htmlFor={`feature-${feature.id}`} className="ml-2 block text-sm text-gray-900">
                                {feature.title}
                              </label>
                            </div>
                          ))}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
              <div className="flex justify-end">
                <button
                  type="button"
                  onClick={() => setShowAddModal(false)}
                  className="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded mr-2"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="bg-primary-600 hover:bg-primary-700 text-white font-bold py-2 px-4 rounded"
                >
                  Add Plan
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Edit Plan Modal */}
      {showEditModal && currentPlan && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full flex items-center justify-center">
          <div className="relative bg-white rounded-lg shadow-xl max-w-md w-full">
            <div className="flex justify-between items-center p-5 border-b">
              <h3 className="text-lg font-semibold">Edit Subscription Plan</h3>
              <button 
                onClick={() => setShowEditModal(false)}
                className="text-gray-400 hover:text-gray-500"
              >
                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <form onSubmit={handleSubmitEdit} className="p-5 max-h-[80vh] overflow-y-auto">
              <div className="mb-4">
                <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="edit-name">
                  Plan Name
                </label>
                <input
                  type="text"
                  id="edit-name"
                  name="name"
                  value={formData.name}
                  onChange={handleFormChange}
                  className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                  required
                />
              </div>
              <div className="mb-4">
                <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="edit-description">
                  Description
                </label>
                <textarea
                  id="edit-description"
                  name="description"
                  value={formData.description}
                  onChange={handleFormChange}
                  className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                  rows={3}
                />
              </div>
              <div className="mb-4 grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="edit-price_monthly">
                    Monthly Price ($)
                  </label>
                  <input
                    type="number"
                    id="edit-price_monthly"
                    name="price_monthly"
                    value={formData.price_monthly}
                    onChange={handleFormChange}
                    className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                    min="0"
                    step="0.01"
                    required
                  />
                </div>
                <div>
                  <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="edit-price_yearly">
                    Yearly Price ($)
                  </label>
                  <input
                    type="number"
                    id="edit-price_yearly"
                    name="price_yearly"
                    value={formData.price_yearly}
                    onChange={handleFormChange}
                    className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                    min="0"
                    step="0.01"
                    required
                  />
                </div>
              </div>
              <div className="mb-4 grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="edit-max_farms">
                    Max Farms
                  </label>
                  <input
                    type="number"
                    id="edit-max_farms"
                    name="max_farms"
                    value={formData.max_farms}
                    onChange={handleFormChange}
                    className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                    min="0"
                    required
                  />
                </div>
                <div>
                  <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="edit-max_users">
                    Max Users
                  </label>
                  <input
                    type="number"
                    id="edit-max_users"
                    name="max_users"
                    value={formData.max_users}
                    onChange={handleFormChange}
                    className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                    min="0"
                    required
                  />
                </div>
              </div>
              <div className="mb-4">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    name="is_active"
                    checked={formData.is_active}
                    onChange={handleFormChange}
                    className="form-checkbox h-5 w-5 text-primary-600"
                  />
                  <span className="ml-2 text-gray-700">Active</span>
                </label>
              </div>
              <div className="mb-4">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    name="is_trial"
                    checked={formData.is_trial}
                    onChange={handleFormChange}
                    className="form-checkbox h-5 w-5 text-primary-600"
                  />
                  <span className="ml-2 text-gray-700">Trial Plan</span>
                </label>
              </div>
              <div className="mb-4">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    name="is_default"
                    checked={formData.is_default}
                    onChange={handleFormChange}
                    className="form-checkbox h-5 w-5 text-primary-600"
                  />
                  <span className="ml-2 text-gray-700">Default Trial Plan</span>
                </label>
                <p className="text-xs text-gray-500 mt-1 ml-7">
                  Only one plan can be set as the default trial plan. This plan will be used for all new trial subscriptions.
                </p>
              </div>
              <div className="mb-4">
                <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="edit-trial_duration_days">
                  Trial Duration (days)
                </label>
                <input
                  type="number"
                  id="edit-trial_duration_days"
                  name="trial_duration_days"
                  value={formData.trial_duration_days}
                  onChange={handleFormChange}
                  className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                  min="1"
                />
              </div>
              <div className="mb-4">
                <label className="block text-gray-700 text-sm font-bold mb-2">
                  Features
                </label>
                <p className="text-sm text-gray-500 mb-4">
                  Select which features will be available in this subscription plan. Users will only see menu items for features included in their plan.
                </p>
                <div className="max-h-60 overflow-y-auto border rounded-md p-4">
                  {/* Group features by category */}
                  {Array.from(new Set(availableFeatures.map(f => f.category))).map(category => (
                    <div key={category} className="mb-4">
                      <h4 className="font-medium text-gray-700 mb-2">{category}</h4>
                      <div className="space-y-2 ml-2">
                        {availableFeatures
                          .filter(feature => feature.category === category)
                          .map(feature => (
                            <div key={feature.id} className="flex items-center">
                              <input
                                type="checkbox"
                                id={`edit-feature-${feature.id}`}
                                checked={!!selectedFeatures[feature.id]}
                                onChange={() => handleFeatureToggle(feature.id)}
                                className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                              />
                              <label htmlFor={`edit-feature-${feature.id}`} className="ml-2 block text-sm text-gray-900">
                                {feature.title}
                              </label>
                            </div>
                          ))}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
              <div className="flex justify-end">
                <button
                  type="button"
                  onClick={() => setShowEditModal(false)}
                  className="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded mr-2"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="bg-primary-600 hover:bg-primary-700 text-white font-bold py-2 px-4 rounded"
                >
                  Update Plan
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default SubscriptionManagement;