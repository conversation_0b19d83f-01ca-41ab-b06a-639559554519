import { useState, useEffect, useContext } from 'react';
import { Link } from 'react-router-dom';
import axios from 'axios';
import { AuthContext } from '../../context/AuthContext';
import { useFarm } from '../../context/FarmContext';
import Layout from '../../components/Layout';
import { API_URL } from '../../config';

interface Field {
  id: string;
  farm_id: string;
  farm_name: string;
  name: string;
  size: number | null;
  size_unit: string;
  field_type: string;
  crop_type: string;
  status: string;
  created_at: string;
  updated_at: string;
}

const FieldList = () => {
  const [fields, setFields] = useState<Field[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const { user } = useContext(AuthContext);
  const { selectedFarm } = useFarm();

  // Fetch fields
  useEffect(() => {
    const fetchFields = async () => {
      setLoading(true);
      setError(null);

      // Only fetch fields if a farm is selected
      if (!selectedFarm) {
        setFields([]);
        setLoading(false);
        return;
      }

      try {
        const url = `${API_URL}/fields/farm/${selectedFarm.id}`;
        const response = await axios.get(url);
        setFields(response.data);
      } catch (err: any) {
        console.error('Error fetching fields:', err);
        setError('Failed to load fields. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchFields();
  }, [selectedFarm]);

  // Format size with unit
  const formatSize = (size: number | null, unit: string) => {
    if (size === null) return 'N/A';
    return `${size} ${unit}`;
  };

  // Format field type for display
  const formatFieldType = (type: string) => {
    if (!type) return 'N/A';
    return type.charAt(0).toUpperCase() + type.slice(1).replace('_', ' ');
  };

  // Format status for display
  const getStatusBadgeClass = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'fallow':
        return 'bg-yellow-100 text-yellow-800';
      case 'planted':
        return 'bg-blue-100 text-blue-800';
      case 'harvested':
        return 'bg-purple-100 text-purple-800';
      case 'inactive':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <Layout>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Fields</h1>
        <Link
          to="/fields/new"
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
        >
          Add New Field
        </Link>
      </div>

      {/* Display selected farm */}
      <div className="mb-6">
        <p className="text-sm text-gray-500">
          Showing fields for farm: <span className="font-medium text-gray-700">{selectedFarm ? selectedFarm.name : 'All Farms'}</span>
        </p>
        {!selectedFarm && (
          <p className="text-sm text-gray-500 mt-1">
            Select a farm from the header dropdown to view fields for a specific farm.
          </p>
        )}
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">{error}</span>
        </div>
      )}

      {loading ? (
        <div className="flex justify-center items-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500"></div>
        </div>
      ) : fields.length === 0 ? (
        <div className="bg-white shadow rounded-lg p-6 text-center">
          <p className="text-gray-500">No fields found. Add your first field to get started.</p>
          <Link
            to="/fields/new"
            className="inline-flex items-center px-4 py-2 mt-4 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Add New Field
          </Link>
        </div>
      ) : (
        <div className="bg-white shadow overflow-hidden sm:rounded-md">
          <ul className="divide-y divide-gray-200">
            {fields.map(field => (
              <li key={field.id}>
                <Link to={`/fields/${field.id}`} className="block hover:bg-gray-50">
                  <div className="px-4 py-4 sm:px-6">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <p className="text-sm font-medium text-primary-600 truncate">{field.name}</p>
                        <span className={`ml-2 px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusBadgeClass(field.status)}`}>
                          {field.status.charAt(0).toUpperCase() + field.status.slice(1)}
                        </span>
                      </div>
                      <div className="ml-2 flex-shrink-0 flex">
                        <Link
                          to={`/fields/${field.id}/edit`}
                          className="ml-2 inline-flex items-center px-2.5 py-1.5 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                          onClick={(e) => e.stopPropagation()}
                        >
                          Edit
                        </Link>
                      </div>
                    </div>
                    <div className="mt-2 sm:flex sm:justify-between">
                      <div className="sm:flex">
                        <p className="flex items-center text-sm text-gray-500">
                          Farm: {field.farm_name}
                        </p>
                        <p className="mt-2 flex items-center text-sm text-gray-500 sm:mt-0 sm:ml-6">
                          Type: {formatFieldType(field.field_type)}
                        </p>
                        <p className="mt-2 flex items-center text-sm text-gray-500 sm:mt-0 sm:ml-6">
                          Crop: {formatFieldType(field.crop_type)}
                        </p>
                      </div>
                      <div className="mt-2 flex items-center text-sm text-gray-500 sm:mt-0">
                        <p>
                          Size: {formatSize(field.size, field.size_unit)}
                        </p>
                      </div>
                    </div>
                  </div>
                </Link>
              </li>
            ))}
          </ul>
        </div>
      )}
    </Layout>
  );
};

export default FieldList;
