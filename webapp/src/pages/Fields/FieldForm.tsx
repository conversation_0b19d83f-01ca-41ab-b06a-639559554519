import { useState, useEffect, useContext, useCallback } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import axios from 'axios';
import { AuthContext } from '../../context/AuthContext';
import { useFarm } from '../../context/FarmContext';
import Layout from '../../components/Layout';
import LocationAutocomplete from '../../components/LocationAutocomplete';
import FieldBoundaryMapComponent from '../../components/FieldBoundaryMapComponent';
import HarvestDirectionMapComponent from '../../components/HarvestDirectionMapComponent';
import { API_URL } from '../../config/index';
import { loadGoogleMapsApi } from '../../utils/googleMapsLoader';
import { getFarmCropTypes, CropType } from '../../services/cropTypeService';

interface Field {
  id?: string;
  farm_id: string;
  name: string;
  size: number | null;
  size_unit: string;
  field_type: string;
  crop_type: string;
  status: string;
  location: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
  boundaries: string;
  soil_type: string;
  last_soil_test_date: string;
  notes: string;
  lat?: number;
  lng?: number;
}

const FieldForm = () => {
  const { fieldId } = useParams<{ fieldId: string }>();
  const navigate = useNavigate();
  const isEditMode = !!fieldId;

  const [field, setField] = useState<Field>({
    farm_id: '',
    name: '',
    size: null,
    size_unit: 'acres',
    field_type: '',
    crop_type: '',
    status: 'active',
    location: '',
    city: '',
    state: '',
    zipCode: '',
    country: 'USA',
    boundaries: '',
    soil_type: '',
    last_soil_test_date: '',
    notes: ''
  });

  // State for location coordinates
  const [coordinates, setCoordinates] = useState<{ lat: number; lng: number } | null>(null);

  // State for crop types
  const [cropTypes, setCropTypes] = useState<CropType[]>([]);
  const [loadingCropTypes, setLoadingCropTypes] = useState<boolean>(false);

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isLoadingMapsApi, setIsLoadingMapsApi] = useState(true);

  const { user } = useContext(AuthContext);
  const { selectedFarm } = useFarm();

  // Console log for debugging
  useEffect(() => {
    console.log("FieldForm rendered", { user, selectedFarm, field });
  }, [user, selectedFarm, field]);

  // Geocode address to get coordinates
  const geocodeAddress = useCallback(async () => {
    // Skip if we don't have enough address information
    if (!field.location) return;

    try {
      // Only load Google Maps API if it's not already loaded
      if (!window.google || !window.google.maps || !window.google.maps.Geocoder) {
        await loadGoogleMapsApi(['places']);
      }

      // Create geocoder instance
      const geocoder = new google.maps.Geocoder();

      // Build full address string
      let addressString = field.location;
      if (field.city) addressString += `, ${field.city}`;
      if (field.state) addressString += `, ${field.state}`;
      if (field.zipCode) addressString += ` ${field.zipCode}`;
      if (field.country) addressString += `, ${field.country}`;

      console.log('Geocoding address:', addressString);

      try {
        // Use the Promise-based API for better error handling
        const response = await new Promise<google.maps.GeocoderResult[]>((resolve, reject) => {
          geocoder.geocode({ address: addressString }, (results, status) => {
            if (status === google.maps.GeocoderStatus.OK) {
              resolve(results);
            } else {
              reject(status);
            }
          });
        });

        if (response && response.length > 0) {
          const location = response[0].geometry.location;
          const newCoords = {
            lat: location.lat(),
            lng: location.lng()
          };

          // Update coordinates state (for map centering)
          setCoordinates(newCoords);

          // Update field state with coordinates
          setField(prev => ({
            ...prev,
            lat: newCoords.lat,
            lng: newCoords.lng
          }));

          console.log('Geocoded coordinates:', newCoords);
        }
      } catch (geocodeError) {
        console.warn('Geocoding failed:', geocodeError);
      }
    } catch (error) {
      console.error('Error geocoding address:', error);
    }
  }, [field.location, field.city, field.state, field.zipCode, field.country]);

  // Load Google Maps API with Places library
  useEffect(() => {
    const loadMapsApi = async () => {
      try {
        await loadGoogleMapsApi(['places']);
        setIsLoadingMapsApi(false);
      } catch (error) {
        console.error('Failed to load Google Maps API:', error);
        setIsLoadingMapsApi(false);
      }
    };

    loadMapsApi();
  }, []);

  // Set the selected farm when component mounts or selectedFarm changes
  useEffect(() => {
    if (selectedFarm && !isEditMode) {
      setField(prev => ({ ...prev, farm_id: selectedFarm.id }));
    }

    // Fetch crop types for the selected farm
    const fetchCropTypes = async () => {
      if (!selectedFarm) return;

      setLoadingCropTypes(true);
      try {
        const data = await getFarmCropTypes(selectedFarm.id);
        setCropTypes(data);
      } catch (err) {
        console.error('Error fetching crop types:', err);
      } finally {
        setLoadingCropTypes(false);
      }
    };

    fetchCropTypes();
  }, [selectedFarm, isEditMode]);

  // Geocode address when address fields change
  useEffect(() => {
    // Don't geocode if coordinates were already set by the LocationAutocomplete
    // This prevents unnecessary geocoding when selecting from the autocomplete dropdown
    if (field.lat && field.lng) return;

    // Don't geocode if we don't have a location
    if (!field.location) return;

    // Add a small delay to avoid geocoding on every keystroke
    const timer = setTimeout(() => {
      geocodeAddress();
    }, 1000);

    return () => clearTimeout(timer);
  }, [field.location, field.city, field.state, field.zipCode, field.country, geocodeAddress]);

  // Fetch field data if in edit mode
  useEffect(() => {
    const fetchField = async () => {
      if (!fieldId) return;

      setLoading(true);
      setError(null);

      try {
        const response = await axios.get(`${API_URL}/fields/${fieldId}`);

        // Extract location data from location_data field if it exists
        const locationData = response.data.location_data || {};

        // Format the date for the input field
        const formattedData = {
          ...response.data,
          location: locationData.address || '',
          city: locationData.city || '',
          state: locationData.state || '',
          zipCode: locationData.zipCode || '',
          country: locationData.country || 'USA',
          boundaries: locationData.boundaries || '',
          last_soil_test_date: response.data.last_soil_test_date ?
            new Date(response.data.last_soil_test_date).toISOString().split('T')[0] :
            ''
        };

        setField(formattedData);

        // If we have coordinates in location_data, set them for the map
        if (locationData.lat && locationData.lng) {
          setCoordinates({ lat: locationData.lat, lng: locationData.lng });
        }
      } catch (err: any) {
        console.error('Error fetching field:', err);
        setError('Failed to load field data. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    if (isEditMode) {
      fetchField();
    }
  }, [fieldId, isEditMode]);

  // Handle form input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;

    // Check if this is an address field
    const addressFields = ['city', 'state', 'zipCode', 'country'];

    if (addressFields.includes(name)) {
      // If an address field is changed, clear coordinates to trigger geocoding
      setField(prev => ({
        ...prev,
        [name]: value,
        lat: undefined,
        lng: undefined
      }));

      // Only clear coordinates state if we have a location
      // This prevents the map from resetting when editing other fields
      if (field.location) {
        setCoordinates(null);
      }
    } else {
      // For non-address fields, just update the value
      setField(prev => ({ ...prev, [name]: value }));
    }
  };

  // Handle location change from LocationAutocomplete
  const handleLocationChange = (value: string, coords?: { lat: number; lng: number }, addressComponents?: any) => {
    // If we have address components directly from the autocomplete
    if (addressComponents) {
      const { streetAddress, city, state, zipCode, country } = addressComponents;

      // Use streetAddress if available, otherwise use the full address
      const addressToUse = streetAddress || value;

      // Update form data with extracted components
      setField(prev => ({
        ...prev,
        location: addressToUse,
        city: city || prev.city,
        state: state || prev.state,
        zipCode: zipCode || prev.zipCode,
        country: country || prev.country
      }));
    } else {
      // If no address components, just update the location field
      // Also clear coordinates to trigger geocoding
      setField(prev => ({
        ...prev,
        location: value,
        lat: undefined,
        lng: undefined
      }));

      // Clear coordinates state to reset map position
      // This will be updated by the geocodeAddress function
      setCoordinates(null);
    }

    // Set coordinates for map centering if available
    if (coords) {
      setCoordinates(coords);

      // Store coordinates in the location_data that will be sent to the backend
      // This will be used when creating the location_data object in handleSubmit
      setField(prev => ({
        ...prev,
        lat: coords.lat,
        lng: coords.lng
      }));
    }

    // Log address components for debugging
    if (addressComponents) {
      console.log('Field location address components:', addressComponents);
    }
  };

  // Handle boundaries change from MapDrawingComponent
  const handleBoundariesChange = (value: string) => {
    setField(prev => ({ ...prev, boundaries: value }));
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    setLoading(true);
    setError(null);

    try {
      // Validate required fields
      if (!selectedFarm) {
        setError('Please select a farm from the header dropdown.');
        setLoading(false);
        return;
      }

      if (!field.name) {
        setError('Field name is required.');
        setLoading(false);
        return;
      }

      // Create location_data object from location fields
      const location_data = {
        address: field.location,
        city: field.city,
        state: field.state,
        zipCode: field.zipCode,
        country: field.country,
        boundaries: field.boundaries,
        lat: field.lat,
        lng: field.lng
      };

      // Ensure farm_id is set from selectedFarm
      const fieldData = {
        ...field,
        farm_id: selectedFarm.id,
        location_data: location_data
      };

      if (isEditMode) {
        // Update existing field
        await axios.put(`${API_URL}/fields/${fieldId}`, fieldData);
      } else {
        // Create new field
        await axios.post(`${API_URL}/fields`, fieldData);
      }

      // Redirect to field list
      navigate('/fields');
    } catch (err: any) {
      console.error('Error saving field:', err);
      setError('Failed to save field. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Layout>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">
          {isEditMode ? 'Edit Field' : 'Add New Field'}
        </h1>
        <Link
          to="/fields"
          className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
        >
          Back to Fields
        </Link>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">{error}</span>
        </div>
      )}

      <div className="bg-white shadow rounded-lg overflow-hidden">
        <form onSubmit={handleSubmit} className="p-6">
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
            {/* Farm Selection */}
            <div>
              <label htmlFor="farm_id" className="block text-sm font-medium text-gray-700 mb-1">
                Farm <span className="text-red-500">*</span>
              </label>
              <div className="p-2 border rounded bg-gray-50">
                {selectedFarm ? (
                  <span className="text-gray-700">{selectedFarm.name}</span>
                ) : (
                  <span className="text-gray-500 italic">Please select a farm from the header dropdown</span>
                )}
              </div>
              <p className="mt-2 text-sm text-gray-500">
                To change the farm, use the farm selector in the header.
              </p>
              <input
                type="hidden"
                id="farm_id"
                name="farm_id"
                value={selectedFarm?.id || ''}
              />
            </div>

            {/* Field Name */}
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                Field Name <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                id="name"
                name="name"
                value={field.name}
                onChange={handleChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                required
              />
            </div>

            {/* Field Size */}
            <div>
              <label htmlFor="size" className="block text-sm font-medium text-gray-700 mb-1">
                Size
              </label>
              <input
                type="number"
                id="size"
                name="size"
                value={field.size === null ? '' : field.size}
                onChange={handleChange}
                step="0.01"
                min="0"
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              />
            </div>

            {/* Size Unit */}
            <div>
              <label htmlFor="size_unit" className="block text-sm font-medium text-gray-700 mb-1">
                Size Unit
              </label>
              <select
                id="size_unit"
                name="size_unit"
                value={field.size_unit}
                onChange={handleChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              >
                <option value="acres">Acres</option>
                <option value="hectares">Hectares</option>
                <option value="square_meters">Square Meters</option>
                <option value="square_feet">Square Feet</option>
              </select>
            </div>

            {/* Field Type */}
            <div>
              <label htmlFor="field_type" className="block text-sm font-medium text-gray-700 mb-1">
                Field Type
              </label>
              <select
                id="field_type"
                name="field_type"
                value={field.field_type}
                onChange={handleChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              >
                <option value="">Select a type</option>
                <option value="crop">Crop Field</option>
                <option value="pasture">Pasture</option>
                <option value="orchard">Orchard</option>
                <option value="vineyard">Vineyard</option>
                <option value="greenhouse">Greenhouse</option>
                <option value="other">Other</option>
              </select>
            </div>

            {/* Crop Type */}
            <div>
              <div className="flex justify-between items-center">
                <label htmlFor="crop_type" className="block text-sm font-medium text-gray-700 mb-1">
                  Crop Type
                </label>
                <Link
                  to="/crop-management/crop-types"
                  className="text-xs text-primary-600 hover:text-primary-900"
                >
                  Manage Crop Types
                </Link>
              </div>
              <select
                id="crop_type"
                name="crop_type"
                value={field.crop_type}
                onChange={handleChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              >
                <option value="">Select a crop type</option>
                {loadingCropTypes ? (
                  <option value="" disabled>Loading crop types...</option>
                ) : cropTypes.length > 0 ? (
                  cropTypes.map(cropType => (
                    <option key={cropType.id} value={cropType.name.toLowerCase()}>
                      {cropType.name}
                    </option>
                  ))
                ) : (
                  <>
                    <option value="corn">Corn</option>
                    <option value="wheat">Wheat</option>
                    <option value="soybeans">Soybeans</option>
                    <option value="barley">Barley</option>
                    <option value="oats">Oats</option>
                    <option value="rice">Rice</option>
                    <option value="cotton">Cotton</option>
                    <option value="alfalfa">Alfalfa</option>
                    <option value="hay">Hay</option>
                    <option value="potatoes">Potatoes</option>
                    <option value="tomatoes">Tomatoes</option>
                    <option value="apples">Apples</option>
                    <option value="grapes">Grapes</option>
                    <option value="other">Other</option>
                  </>
                )}
              </select>
              <p className="mt-1 text-xs text-gray-500">
                {cropTypes.length === 0 && !loadingCropTypes ?
                  "No custom crop types found. You can add them in Crop Type Management." :
                  "Select a crop type or add custom types in Crop Type Management."}
              </p>
            </div>

            {/* Status */}
            <div>
              <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">
                Status
              </label>
              <select
                id="status"
                name="status"
                value={field.status}
                onChange={handleChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              >
                <option value="active">Active</option>
                <option value="fallow">Fallow</option>
                <option value="planted">Planted</option>
                <option value="harvested">Harvested</option>
                <option value="inactive">Inactive</option>
              </select>
            </div>

            {/* Location */}
            <div>
              <label htmlFor="location" className="block text-sm font-medium text-gray-700 mb-1">
                Street Address
              </label>
              {!isLoadingMapsApi ? (
                <LocationAutocomplete
                  value={field.location}
                  onChange={handleLocationChange}
                  placeholder="Enter an address or location"
                />
              ) : (
                <input
                  type="text"
                  name="location"
                  id="location"
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                  value={field.location}
                  onChange={(e) => handleLocationChange(e.target.value)}
                  placeholder="Loading address autocomplete..."
                />
              )}
              <p className="mt-1 text-sm text-gray-500">
                Start typing to see suggestions. Select a location to automatically zoom the map.
              </p>
            </div>

            {/* City */}
            <div>
              <label htmlFor="city" className="block text-sm font-medium text-gray-700 mb-1">
                City
              </label>
              <input
                type="text"
                id="city"
                name="city"
                value={field.city}
                onChange={handleChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              />
            </div>

            {/* State */}
            <div>
              <label htmlFor="state" className="block text-sm font-medium text-gray-700 mb-1">
                State
              </label>
              <select
                id="state"
                name="state"
                value={field.state}
                onChange={handleChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              >
                <option value="">Select a state</option>
                <option value="AL">Alabama</option>
                <option value="AK">Alaska</option>
                <option value="AZ">Arizona</option>
                <option value="AR">Arkansas</option>
                <option value="CA">California</option>
                <option value="CO">Colorado</option>
                <option value="CT">Connecticut</option>
                <option value="DE">Delaware</option>
                <option value="DC">District Of Columbia</option>
                <option value="FL">Florida</option>
                <option value="GA">Georgia</option>
                <option value="HI">Hawaii</option>
                <option value="ID">Idaho</option>
                <option value="IL">Illinois</option>
                <option value="IN">Indiana</option>
                <option value="IA">Iowa</option>
                <option value="KS">Kansas</option>
                <option value="KY">Kentucky</option>
                <option value="LA">Louisiana</option>
                <option value="ME">Maine</option>
                <option value="MD">Maryland</option>
                <option value="MA">Massachusetts</option>
                <option value="MI">Michigan</option>
                <option value="MN">Minnesota</option>
                <option value="MS">Mississippi</option>
                <option value="MO">Missouri</option>
                <option value="MT">Montana</option>
                <option value="NE">Nebraska</option>
                <option value="NV">Nevada</option>
                <option value="NH">New Hampshire</option>
                <option value="NJ">New Jersey</option>
                <option value="NM">New Mexico</option>
                <option value="NY">New York</option>
                <option value="NC">North Carolina</option>
                <option value="ND">North Dakota</option>
                <option value="OH">Ohio</option>
                <option value="OK">Oklahoma</option>
                <option value="OR">Oregon</option>
                <option value="PA">Pennsylvania</option>
                <option value="RI">Rhode Island</option>
                <option value="SC">South Carolina</option>
                <option value="SD">South Dakota</option>
                <option value="TN">Tennessee</option>
                <option value="TX">Texas</option>
                <option value="UT">Utah</option>
                <option value="VT">Vermont</option>
                <option value="VA">Virginia</option>
                <option value="WA">Washington</option>
                <option value="WV">West Virginia</option>
                <option value="WI">Wisconsin</option>
                <option value="WY">Wyoming</option>
              </select>
            </div>

            {/* Zip Code */}
            <div>
              <label htmlFor="zipCode" className="block text-sm font-medium text-gray-700 mb-1">
                Zip Code
              </label>
              <input
                type="text"
                id="zipCode"
                name="zipCode"
                value={field.zipCode}
                onChange={handleChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              />
            </div>

            {/* Country */}
            <div>
              <label htmlFor="country" className="block text-sm font-medium text-gray-700 mb-1">
                Country
              </label>
              <select
                id="country"
                name="country"
                value={field.country}
                onChange={handleChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              >
                <option value="">Select a country</option>
                <option value="US">United States</option>
                <option value="CAN">Canada</option>
                <option value="MEX">Mexico</option>
                <option value="GBR">United Kingdom</option>
                <option value="FRA">France</option>
                <option value="DEU">Germany</option>
                <option value="ITA">Italy</option>
                <option value="ESP">Spain</option>
                <option value="AUS">Australia</option>
                <option value="NZL">New Zealand</option>
                <option value="JPN">Japan</option>
                <option value="CHN">China</option>
                <option value="BRA">Brazil</option>
                <option value="ARG">Argentina</option>
                <option value="ZAF">South Africa</option>
                <option value="IND">India</option>
                <option value="RUS">Russia</option>
                <option value="NLD">Netherlands</option>
                <option value="BEL">Belgium</option>
                <option value="SWE">Sweden</option>
                <option value="NOR">Norway</option>
                <option value="DNK">Denmark</option>
                <option value="FIN">Finland</option>
                <option value="IRL">Ireland</option>
                <option value="CHE">Switzerland</option>
                <option value="AUT">Austria</option>
                <option value="PRT">Portugal</option>
                <option value="GRC">Greece</option>
              </select>
            </div>

            {/* Soil Type */}
            <div>
              <label htmlFor="soil_type" className="block text-sm font-medium text-gray-700 mb-1">
                Soil Type
              </label>
              <select
                id="soil_type"
                name="soil_type"
                value={field.soil_type}
                onChange={handleChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              >
                <option value="">Select soil type</option>
                <option value="clay">Clay</option>
                <option value="sandy">Sandy</option>
                <option value="loamy">Loamy</option>
                <option value="silty">Silty</option>
                <option value="peaty">Peaty</option>
                <option value="chalky">Chalky</option>
                <option value="other">Other</option>
              </select>
            </div>

            {/* Last Soil Test Date */}
            <div>
              <label htmlFor="last_soil_test_date" className="block text-sm font-medium text-gray-700 mb-1">
                Last Soil Test Date
              </label>
              <input
                type="date"
                id="last_soil_test_date"
                name="last_soil_test_date"
                value={field.last_soil_test_date}
                onChange={handleChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              />
            </div>
          </div>

          {/* Field Boundaries */}
          <div className="mt-6">
            <label htmlFor="boundaries" className="block text-sm font-medium text-gray-700 mb-1">
              Field Boundaries
            </label>
            <FieldBoundaryMapComponent
              value={field.boundaries}
              onChange={handleBoundariesChange}
              center={coordinates || undefined}
              height="400px"
            />
          </div>

          {/* Harvest Direction Maps - Only show if we have field boundaries and in edit mode */}
          {isEditMode && field.boundaries && field.boundaries.trim() !== '' && (
            <div className="mt-6">
              <HarvestDirectionMapComponent
                fieldId={fieldId || ''}
                fieldBoundaries={field.boundaries ? JSON.parse(field.boundaries).features.map(
                  (feature: any) => feature.geometry.coordinates[0].map(
                    (coord: number[]) => ({ lat: coord[1], lng: coord[0] })
                  )
                ) : []}
                center={coordinates || undefined}
                height="400px"
              />
            </div>
          )}

          {/* Notes */}
          <div className="mt-6">
            <label htmlFor="notes" className="block text-sm font-medium text-gray-700 mb-1">
              Notes
            </label>
            <textarea
              id="notes"
              name="notes"
              value={field.notes}
              onChange={handleChange}
              rows={4}
              className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
            ></textarea>
          </div>

          {/* Form Actions */}
          <div className="mt-6 flex justify-end space-x-3">
            <Link
              to="/fields"
              className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              Cancel
            </Link>
            <button
              type="submit"
              disabled={loading}
              className={`inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 ${loading ? 'opacity-70 cursor-not-allowed' : ''}`}
            >
              {loading ? 'Saving...' : 'Save Field'}
            </button>
          </div>
        </form>
      </div>
    </Layout>
  );
};

export default FieldForm;
