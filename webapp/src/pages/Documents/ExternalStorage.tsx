import { useState, useEffect, useContext } from 'react';
import { Link } from 'react-router-dom';
import axios from 'axios';
import { AuthContext } from '../../context/AuthContext';
import Layout from '../../components/Layout';
import { API_URL } from '../../config';
import { getAuthToken } from '../../utils/storageUtils';

interface ExternalFile {
  id: string;
  name: string;
  mimeType: string;
  size: number;
  modifiedTime: string;
  webViewLink?: string;
  iconLink?: string;
  thumbnailLink?: string;
  isFolder?: boolean;
  path?: string;
}

interface ExternalStorageConnection {
  id: string;
  provider: string;
  provider_email: string;
  provider_display_name: string;
  status: string;
  last_sync_at: string | null;
  created_at: string;
  updated_at: string;
}

const ExternalStorage = () => {
  const [activeTab, setActiveTab] = useState<'google' | 'dropbox'>('google');
  const [files, setFiles] = useState<ExternalFile[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentFolder, setCurrentFolder] = useState<string>('root');
  const [folderPath, setFolderPath] = useState<{ id: string; name: string }[]>([
    { id: 'root', name: 'Root' }
  ]);
  const [connections, setConnections] = useState<ExternalStorageConnection[]>([]);
  const [hasGoogleDrive, setHasGoogleDrive] = useState(false);
  const [hasDropbox, setHasDropbox] = useState(false);
  const [selectedFiles, setSelectedFiles] = useState<string[]>([]);

  const { user } = useContext(AuthContext);

  // Fetch available connections
  useEffect(() => {
    const fetchConnections = async () => {
      try {
        const response = await axios.get(
          `${API_URL}/external-storage-auth/user/${user?.id}/farm/${user?.farm_id}/connections`,
          {
            headers: {
              Authorization: `Bearer ${getAuthToken()}`
            }
          }
        );

        setConnections(response.data || []);

        // Check if user has Google Drive and Dropbox connections
        const hasGoogle = response.data.some((connection: ExternalStorageConnection) => connection.provider === 'google_drive');
        const hasDropbox = response.data.some((connection: ExternalStorageConnection) => connection.provider === 'dropbox');

        setHasGoogleDrive(hasGoogle);
        setHasDropbox(hasDropbox);

        // Set active tab based on available connections
        if (hasGoogle) {
          setActiveTab('google');
        } else if (hasDropbox) {
          setActiveTab('dropbox');
        }
      } catch (err: any) {
        console.error('Error fetching connections:', err);
        setError('Failed to load external storage connections. Please try again later.');
      }
    };

    if (user?.id && user?.farm_id) {
      fetchConnections();
    }
  }, [user?.id, user?.farm_id]);

  // Fetch files from external storage
  useEffect(() => {
    const fetchFiles = async () => {
      setLoading(true);
      setError(null);

      try {
        let url;
        if (activeTab === 'google') {
          url = `${API_URL}/documents/farm/${user?.farm_id}/google-drive/files?folderId=${currentFolder}`;
        } else {
          // For Dropbox, we use path instead of folderId
          const path = currentFolder === 'root' ? '' : currentFolder;
          url = `${API_URL}/documents/farm/${user?.farm_id}/dropbox/files?path=${path}`;
        }

        const response = await axios.get(url, {
          headers: {
            Authorization: `Bearer ${getAuthToken()}`
          }
        });

        setFiles(response.data.files || []);
      } catch (err: any) {
        console.error('Error fetching external files:', err);
        setError('Failed to load files. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    if (user?.farm_id && (hasGoogleDrive || hasDropbox)) {
      fetchFiles();
    } else {
      // Set loading to false if no external storage is connected
      setLoading(false);
    }
  }, [user?.farm_id, activeTab, currentFolder, hasGoogleDrive, hasDropbox]);

  // Format file size for display
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
  };

  // Connect to Google Drive
  const connectToGoogleDrive = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await axios.get(
        `${API_URL}/external-storage-auth/google-drive/auth-url?userId=${user?.id}&farmId=${user?.farm_id}`,
        {
          headers: {
            Authorization: `Bearer ${getAuthToken()}`
          }
        }
      );

      // Redirect to Google OAuth page
      window.location.href = response.data.authUrl;
    } catch (err: any) {
      console.error('Error connecting to Google Drive:', err);
      setError('Failed to connect to Google Drive. Please try again later.');
      setLoading(false);
    }
  };

  // Connect to Dropbox
  const connectToDropbox = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await axios.get(
        `${API_URL}/external-storage-auth/dropbox/auth-url?userId=${user?.id}&farmId=${user?.farm_id}`,
        {
          headers: {
            Authorization: `Bearer ${getAuthToken()}`
          }
        }
      );

      // Redirect to Dropbox OAuth page
      window.location.href = response.data.authUrl;
    } catch (err: any) {
      console.error('Error connecting to Dropbox:', err);
      setError('Failed to connect to Dropbox. Please try again later.');
      setLoading(false);
    }
  };

  // Disconnect external storage
  const disconnectExternalStorage = async (connectionId: string, provider: string) => {
    try {
      setLoading(true);
      setError(null);

      await axios.delete(
        `${API_URL}/external-storage-auth/connections/${connectionId}`,
        {
          headers: {
            Authorization: `Bearer ${getAuthToken()}`
          }
        }
      );

      // Refresh connections
      const response = await axios.get(
        `${API_URL}/external-storage-auth/user/${user?.id}/farm/${user?.farm_id}/connections`,
        {
          headers: {
            Authorization: `Bearer ${getAuthToken()}`
          }
        }
      );

      setConnections(response.data || []);

      // Update state based on available connections
      const hasGoogle = response.data.some((connection: ExternalStorageConnection) => connection.provider === 'google_drive');
      const hasDropbox = response.data.some((connection: ExternalStorageConnection) => connection.provider === 'dropbox');

      setHasGoogleDrive(hasGoogle);
      setHasDropbox(hasDropbox);

      // Update active tab if needed
      if (provider === 'google_drive' && !hasGoogle && hasDropbox) {
        setActiveTab('dropbox');
      } else if (provider === 'dropbox' && !hasDropbox && hasGoogle) {
        setActiveTab('google');
      }

      setLoading(false);
    } catch (err: any) {
      console.error('Error disconnecting external storage:', err);
      setError('Failed to disconnect external storage. Please try again later.');
      setLoading(false);
    }
  };

  // Handle folder navigation
  const navigateToFolder = (folderId: string, folderName: string) => {
    setCurrentFolder(folderId);

    if (folderId === 'root') {
      // Reset to root
      setFolderPath([{ id: 'root', name: 'Root' }]);
    } else {
      // Add to path
      setFolderPath(prev => {
        // Check if we're navigating to a folder that's already in the path
        const existingIndex = prev.findIndex(item => item.id === folderId);
        if (existingIndex >= 0) {
          // If so, truncate the path to that point
          return prev.slice(0, existingIndex + 1);
        }
        // Otherwise, add the new folder to the path
        return [...prev, { id: folderId, name: folderName }];
      });
    }
  };

  // Handle file selection
  const toggleFileSelection = (fileId: string) => {
    setSelectedFiles(prev => {
      if (prev.includes(fileId)) {
        return prev.filter(id => id !== fileId);
      } else {
        return [...prev, fileId];
      }
    });
  };

  // Link selected files to document system
  const linkSelectedFiles = async () => {
    setLoading(true);
    setError(null);

    try {
      // Group files by provider
      const googleFiles = [];
      const dropboxFiles = [];

      for (const fileId of selectedFiles) {
        const file = files.find(f => f.id === fileId);
        if (!file) continue;

        if (activeTab === 'google') {
          googleFiles.push(fileId);
        } else {
          dropboxFiles.push(file.path);
        }
      }

      // Process Google Drive files
      if (googleFiles.length > 0) {
        const url = `${API_URL}/documents/farm/${user?.farm_id}/google-drive/link`;
        await axios.post(url, {
          fileIds: googleFiles,
          description: ''
        }, {
          headers: {
            Authorization: `Bearer ${getAuthToken()}`
          }
        });
      }

      // Process Dropbox files
      if (dropboxFiles.length > 0) {
        const url = `${API_URL}/documents/farm/${user?.farm_id}/dropbox/link`;
        await axios.post(url, {
          paths: dropboxFiles,
          description: ''
        }, {
          headers: {
            Authorization: `Bearer ${getAuthToken()}`
          }
        });
      }

      // Clear selection after linking
      setSelectedFiles([]);

      // Show success message
      const successMessage = document.createElement('div');
      successMessage.className = 'fixed bottom-4 right-4 bg-green-500 text-white px-4 py-2 rounded shadow-lg z-50 flex items-center';
      successMessage.innerHTML = `
        <svg class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
        </svg>
        <span>${selectedFiles.length} files linked successfully!</span>
      `;
      document.body.appendChild(successMessage);

      // Remove success message after 3 seconds
      setTimeout(() => {
        document.body.removeChild(successMessage);
      }, 3000);
    } catch (err: any) {
      console.error('Error linking files:', err);
      setError(err.response?.data?.error || 'Failed to link files. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  // Import selected files to local storage
  const importSelectedFiles = async () => {
    if (selectedFiles.length === 0) return;

    setLoading(true);
    setError(null);

    try {
      // First link the files if they're not already linked
      await linkSelectedFiles();

      // Then get the document IDs for the linked files
      const documentsResponse = await axios.get(
        `${API_URL}/documents/farm/${user?.farm_id}/documents?external=true`,
        {
          headers: {
            Authorization: `Bearer ${getAuthToken()}`
          }
        }
      );

      const externalDocs = documentsResponse.data.documents || [];
      const externalFileIds = selectedFiles.map(fileId => {
        const file = files.find(f => f.id === fileId);
        return file ? file.id : null;
      }).filter(Boolean);

      // Find matching document IDs
      const documentIds = externalDocs
        .filter(doc => 
          (doc.external_source === 'google_drive' && activeTab === 'google' && externalFileIds.includes(doc.external_id)) ||
          (doc.external_source === 'dropbox' && activeTab === 'dropbox' && externalFileIds.some(id => doc.file_path.includes(id)))
        )
        .map(doc => doc.id);

      if (documentIds.length === 0) {
        setError('No matching documents found to import');
        setLoading(false);
        return;
      }

      // Bulk import the documents
      await axios.post(
        `${API_URL}/documents/farm/${user?.farm_id}/documents/bulk-import`,
        { documentIds },
        {
          headers: {
            Authorization: `Bearer ${getAuthToken()}`
          }
        }
      );

      // Clear selection after importing
      setSelectedFiles([]);

      // Show success message
      const successMessage = document.createElement('div');
      successMessage.className = 'fixed bottom-4 right-4 bg-green-500 text-white px-4 py-2 rounded shadow-lg z-50 flex items-center';
      successMessage.innerHTML = `
        <svg class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
        </svg>
        <span>${documentIds.length} files imported successfully!</span>
      `;
      document.body.appendChild(successMessage);

      // Remove success message after 3 seconds
      setTimeout(() => {
        document.body.removeChild(successMessage);
      }, 3000);
    } catch (err: any) {
      console.error('Error importing files:', err);
      setError(err.response?.data?.error || 'Failed to import files. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Layout>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">External Storage</h1>
        <div className="flex space-x-2">
          {selectedFiles.length > 0 && (
            <>
              <button
                onClick={linkSelectedFiles}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                title="Create virtual links to these files"
              >
                <svg className="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                </svg>
                Link Files ({selectedFiles.length})
              </button>
              <button
                onClick={importSelectedFiles}
                className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                title="Download and store these files locally"
              >
                <svg className="w-5 h-5 mr-2 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                </svg>
                Import Files
              </button>
            </>
          )}
          {!hasGoogleDrive && (
            <button
              onClick={connectToGoogleDrive}
              className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              <svg className="w-5 h-5 mr-2 text-red-500" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 0C5.372 0 0 5.372 0 12s5.372 12 12 12 12-5.372 12-12S18.628 0 12 0zm0 22.5c-5.799 0-10.5-4.701-10.5-10.5S6.201 1.5 12 1.5 22.5 6.201 22.5 12 17.799 22.5 12 22.5z" />
                <path d="M8.516 7.5h6.968L19.5 12l-4.016 4.5H8.516L4.5 12l4.016-4.5z" />
              </svg>
              Connect Google Drive
            </button>
          )}
          {!hasDropbox && (
            <button
              onClick={connectToDropbox}
              className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              <svg className="w-5 h-5 mr-2 text-blue-600" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 0L6 6l6 6-6 6 6 6 6-6-6-6 6-6-6-6z" />
              </svg>
              Connect Dropbox
            </button>
          )}
        </div>
      </div>

      {/* Help text */}
      {(hasGoogleDrive || hasDropbox) && (
        <div className="mb-4 p-4 bg-blue-50 rounded-md text-sm text-blue-700 flex items-center">
          <svg className="h-5 w-5 mr-2 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <div>
            <p><strong>Link Files</strong>: Creates virtual links to external files (they remain in external storage)</p>
            <p><strong>Import Files</strong>: Downloads and stores files locally in your document system</p>
          </div>
        </div>
      )}

      {/* Connected storage services */}
      {connections.length > 0 && (
        <div className="mb-6">
          <h2 className="text-lg font-medium text-gray-900 mb-3">Connected Storage Services</h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            {connections.map((connection) => (
              <div key={connection.id} className="bg-white rounded-lg border border-gray-200 shadow-sm p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    {connection.provider === 'google_drive' ? (
                      <svg className="w-8 h-8 mr-3 text-red-500" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12 0C5.372 0 0 5.372 0 12s5.372 12 12 12 12-5.372 12-12S18.628 0 12 0zm0 22.5c-5.799 0-10.5-4.701-10.5-10.5S6.201 1.5 12 1.5 22.5 6.201 22.5 12 17.799 22.5 12 22.5z" />
                        <path d="M8.516 7.5h6.968L19.5 12l-4.016 4.5H8.516L4.5 12l4.016-4.5z" />
                      </svg>
                    ) : (
                      <svg className="w-8 h-8 mr-3 text-blue-600" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12 0L6 6l6 6-6 6 6 6 6-6-6-6 6-6-6-6z" />
                      </svg>
                    )}
                    <div>
                      <h3 className="text-md font-medium text-gray-900">
                        {connection.provider === 'google_drive' ? 'Google Drive' : 'Dropbox'}
                      </h3>
                      <p className="text-sm text-gray-500">{connection.provider_email}</p>
                    </div>
                  </div>
                  <button
                    onClick={() => disconnectExternalStorage(connection.id, connection.provider)}
                    className="text-gray-400 hover:text-red-500"
                    title="Disconnect"
                  >
                    <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                  </button>
                </div>
                <div className="mt-2 text-xs text-gray-500">
                  {connection.last_sync_at ? (
                    <span>Last synced: {formatDate(connection.last_sync_at)}</span>
                  ) : (
                    <span>Never synced</span>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Integration tabs */}
      <div className="border-b border-gray-200 mb-4">
        <nav className="-mb-px flex space-x-8">
          <button
            onClick={() => setActiveTab('google')}
            className={`${
              activeTab === 'google'
                ? 'border-primary-500 text-primary-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm ${!hasGoogleDrive && 'opacity-50 cursor-not-allowed'}`}
            disabled={!hasGoogleDrive}
          >
            Google Drive
          </button>
          <button
            onClick={() => setActiveTab('dropbox')}
            className={`${
              activeTab === 'dropbox'
                ? 'border-primary-500 text-primary-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm ${!hasDropbox && 'opacity-50 cursor-not-allowed'}`}
            disabled={!hasDropbox}
          >
            Dropbox
          </button>
        </nav>
      </div>

      {/* No integrations message */}
      {!hasGoogleDrive && !hasDropbox && (
        <div className="flex flex-col items-center justify-center h-64 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
          <svg
            className="h-12 w-12 text-gray-400 mb-2"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"
            />
          </svg>
          <p className="text-gray-500 mb-4">No external storage connections found</p>
          <div className="flex space-x-4">
            <button
              onClick={connectToGoogleDrive}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              <svg className="w-5 h-5 mr-2" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 0C5.372 0 0 5.372 0 12s5.372 12 12 12 12-5.372 12-12S18.628 0 12 0zm0 22.5c-5.799 0-10.5-4.701-10.5-10.5S6.201 1.5 12 1.5 22.5 6.201 22.5 12 17.799 22.5 12 22.5z" />
                <path d="M8.516 7.5h6.968L19.5 12l-4.016 4.5H8.516L4.5 12l4.016-4.5z" />
              </svg>
              Connect Google Drive
            </button>
            <button
              onClick={connectToDropbox}
              className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              <svg className="w-5 h-5 mr-2 text-blue-600" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 0L6 6l6 6-6 6 6 6 6-6-6-6 6-6-6-6z" />
              </svg>
              Connect Dropbox
            </button>
          </div>
        </div>
      )}

      {/* Breadcrumb navigation */}
      {(hasGoogleDrive || hasDropbox) && (
        <div className="flex items-center space-x-2 mb-4">
          {folderPath.map((folder, index) => (
            <div key={folder.id} className="flex items-center">
              {index > 0 && <span className="text-gray-500 mx-2">/</span>}
              <button
                onClick={() => navigateToFolder(folder.id, folder.name)}
                className={`px-2 py-1 text-sm rounded ${
                  currentFolder === folder.id ? 'bg-primary-100 text-primary-700' : 'text-gray-600 hover:bg-gray-100'
                }`}
              >
                {folder.name}
              </button>
            </div>
          ))}
        </div>
      )}

      {/* Error message */}
      {error && (
        <div className="mb-4 p-4 text-sm text-red-700 bg-red-100 rounded-md flex items-center">
          <svg className="h-5 w-5 mr-2 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
          </svg>
          <span>{error}</span>
          <button 
            onClick={() => setError(null)} 
            className="ml-auto text-red-700 hover:text-red-900"
            aria-label="Dismiss error"
          >
            <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      )}

      {/* Loading state */}
      {loading ? (
        <div className="flex flex-col justify-center items-center h-64 bg-gray-50 rounded-lg border border-gray-200">
          <svg
            className="animate-spin h-10 w-10 text-primary-500 mb-4"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              className="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="4"
            ></circle>
            <path
              className="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            ></path>
          </svg>
          <p className="text-gray-600">Loading external storage data...</p>
        </div>
      ) : (
        <>
          {/* Empty state */}
          {files.length === 0 && !loading && (hasGoogleDrive || hasDropbox) && (
            <div className="flex flex-col items-center justify-center h-64 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
              <svg
                className="h-12 w-12 text-gray-400 mb-2"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 13h6m-3-3v6m5 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                />
              </svg>
              <p className="text-gray-500 mb-2">No files found in this folder</p>
            </div>
          )}

          {/* Files list */}
          {files.length > 0 && (
            <div className="bg-white shadow overflow-hidden sm:rounded-md">
              <ul className="divide-y divide-gray-200">
                {files.map((file) => (
                  <li key={file.id}>
                    <div className="px-4 py-4 sm:px-6 hover:bg-gray-50 flex items-center">
                      <div className="mr-4">
                        <input
                          type="checkbox"
                          checked={selectedFiles.includes(file.id)}
                          onChange={() => toggleFileSelection(file.id)}
                          className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                          disabled={file.isFolder}
                        />
                      </div>
                      <div className="flex-1 flex items-center justify-between">
                        <div className="flex items-center">
                          {/* File type icon */}
                          <div className="flex-shrink-0 h-10 w-10 flex items-center justify-center rounded-md bg-gray-100 text-gray-500">
                            {file.isFolder ? (
                              <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth={2}
                                  d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z"
                                />
                              </svg>
                            ) : (
                              <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth={2}
                                  d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                                />
                              </svg>
                            )}
                          </div>
                          <div className="ml-4">
                            {file.isFolder ? (
                              <div 
                                className="text-sm font-medium text-primary-600 hover:text-primary-900 cursor-pointer"
                                onClick={() => navigateToFolder(file.id, file.name)}
                              >
                                {file.name}
                              </div>
                            ) : (
                              <div className="text-sm font-medium text-gray-900">
                                {file.name}
                              </div>
                            )}
                            <div className="text-sm text-gray-500">
                              {file.mimeType}
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center space-x-4">
                          {!file.isFolder && (
                            <>
                              <div className="text-sm text-gray-500">
                                {formatFileSize(file.size || 0)}
                              </div>
                              <div className="text-sm text-gray-500">
                                {formatDate(file.modifiedTime)}
                              </div>
                              {file.webViewLink && (
                                <a
                                  href={file.webViewLink}
                                  className="text-primary-600 hover:text-primary-900"
                                  target="_blank"
                                  rel="noopener noreferrer"
                                >
                                  <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                      strokeWidth={2}
                                      d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"
                                    />
                                  </svg>
                                </a>
                              )}
                            </>
                          )}
                        </div>
                      </div>
                    </div>
                  </li>
                ))}
              </ul>
            </div>
          )}
        </>
      )}
    </Layout>
  );
};

export default ExternalStorage;
