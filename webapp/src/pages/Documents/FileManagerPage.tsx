import React, { useContext, useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { AuthContext } from '../../context/AuthContext';
import { useFarm } from '../../context/FarmContext';
import Layout from '../../components/Layout';
import ErrorBoundary from '../../components/ui/ErrorBoundary';
import FileManager from '../../components/filemanager/FileManager';
import { logReactError } from '../../utils/reactErrorDecoder';
import { FileItem } from '../../components/filemanager/FileManagerTypes';

const FileManagerPage: React.FC = () => {
  const { user } = useContext(AuthContext);
  const { currentFarm } = useFarm();
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();
  const [initialFolderId, setInitialFolderId] = useState<string | null>(null);
  const [farmId, setFarmId] = useState<string>('');

  // Determine farm ID with better error handling
  useEffect(() => {
    const resolvedFarmId = currentFarm?.id || user?.farm_id;
    console.log('FileManagerPage: Resolving farm ID', {
      currentFarmId: currentFarm?.id,
      userFarmId: user?.farm_id,
      resolvedFarmId
    });

    if (resolvedFarmId) {
      setFarmId(resolvedFarmId);
    } else {
      console.warn('FileManagerPage: No farm ID available', { currentFarm, user });
    }
  }, [currentFarm?.id, user?.farm_id]);

  // Get the folder ID from the URL query parameters on initial load
  useEffect(() => {
    const folderId = searchParams.get('folder');
    setInitialFolderId(folderId);
  }, [searchParams]);

  // Handle file open
  const handleFileOpen = (file: FileItem) => {
    navigate(`/documents/view/${file.id}`);
  };

  // Handle folder open - update URL with folder ID
  const handleFolderOpen = (folder: FileItem) => {
    setSearchParams({ folder: folder.id });
  };

  // Handle navigation to root or parent folder
  const handleFolderNavigation = (folderId: string | null, folderName?: string) => {
    if (folderId === null) {
      // Remove folder parameter when navigating to root
      searchParams.delete('folder');
      setSearchParams(searchParams);
    } else {
      setSearchParams({ folder: folderId });
    }
  };

  // Error handler for React errors
  const handleReactError = (error: Error, errorInfo: any) => {
    logReactError(error, errorInfo);
    console.error('FileManager React Error:', error, errorInfo);
  };

  // Don't render FileManager if we don't have a farm ID
  if (!farmId) {
    return (
      <Layout>
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold text-gray-900">File Manager</h1>
        </div>
        <div className="bg-white rounded-lg shadow p-6">
          <div className="text-center">
            <p className="text-gray-500">Loading farm information...</p>
            {!currentFarm && !user?.farm_id && (
              <p className="text-red-500 mt-2">No farm selected. Please select a farm to view documents.</p>
            )}
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <ErrorBoundary onError={handleReactError}>
      <Layout>
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold text-gray-900">File Manager</h1>
        </div>

        <div className="bg-white rounded-lg shadow">
          <FileManager
            farmId={farmId}
            onFileOpen={handleFileOpen}
            onFolderOpen={handleFolderOpen}
            initialFolderId={initialFolderId}
            onFolderNavigation={handleFolderNavigation}
          />
        </div>
      </Layout>
    </ErrorBoundary>
  );
};

export default FileManagerPage;
