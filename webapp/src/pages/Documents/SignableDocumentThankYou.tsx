import { useEffect, useState } from 'react';
import { useSearchParams, Link } from 'react-router-dom';

const SignableDocumentThankYou = () => {
  const [searchParams] = useSearchParams();
  const status = searchParams.get('status') || 'completed';
  const [countdown, setCountdown] = useState(10);

  // Countdown timer for auto-closing the window
  useEffect(() => {
    const timer = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          clearInterval(timer);
          // Close the window if it was opened in a new tab/window
          if (window.opener) {
            window.close();
          }
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  return (
    <div className="min-h-screen bg-gray-100 py-6 flex flex-col justify-center sm:py-12">
      <div className="relative py-3 sm:max-w-xl sm:mx-auto">
        <div className="absolute inset-0 bg-gradient-to-r from-primary-400 to-primary-600 shadow-lg transform -skew-y-6 sm:skew-y-0 sm:-rotate-6 sm:rounded-3xl"></div>
        <div className="relative px-4 py-10 bg-white shadow-lg sm:rounded-3xl sm:p-20">
          <div className="max-w-md mx-auto">
            <div className="text-center">
              {status === 'signed' ? (
                <>
                  <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100">
                    <svg className="h-10 w-10 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                  </div>
                  <h2 className="mt-6 text-3xl font-extrabold text-gray-900">Thank You!</h2>
                  <p className="mt-2 text-lg text-gray-600">
                    You have successfully signed the document.
                  </p>
                  <p className="mt-1 text-sm text-gray-500">
                    A confirmation email will be sent to you shortly with a copy of the signed document.
                  </p>
                </>
              ) : status === 'declined' ? (
                <>
                  <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-red-100">
                    <svg className="h-10 w-10 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </div>
                  <h2 className="mt-6 text-3xl font-extrabold text-gray-900">Document Declined</h2>
                  <p className="mt-2 text-lg text-gray-600">
                    You have declined to sign this document.
                  </p>
                  <p className="mt-1 text-sm text-gray-500">
                    The document owner has been notified of your decision.
                  </p>
                </>
              ) : (
                <>
                  <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-blue-100">
                    <svg className="h-10 w-10 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <h2 className="mt-6 text-3xl font-extrabold text-gray-900">Process Complete</h2>
                  <p className="mt-2 text-lg text-gray-600">
                    Your action has been recorded.
                  </p>
                </>
              )}

              <div className="mt-8 text-sm text-gray-500">
                <p>This window will close automatically in {countdown} seconds.</p>
                <p className="mt-2">
                  If it doesn't close, you can{' '}
                  <button
                    onClick={() => window.close()}
                    className="text-primary-600 hover:text-primary-500"
                  >
                    click here
                  </button>{' '}
                  to close it manually.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SignableDocumentThankYou;