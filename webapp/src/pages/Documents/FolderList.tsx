import { useState, useEffect, useContext } from 'react';
import { Link } from 'react-router-dom';
import axios from 'axios';
import { AuthContext } from '../../context/AuthContext';
import { useFarm } from '../../context/FarmContext';
import Layout from '../../components/Layout';
import { API_URL } from '../../config';
import FileManagerPermissionModal from '../../components/filemanager/FileManagerPermissionModal';
import { getAuthToken } from '../../utils/storageUtils';

interface Folder {
  id: string;
  name: string;
  description: string;
  parent_folder_id: string | null;
  farm_id: string;
  created_by: string;
  created_at: string;
  updated_at: string;
  creator: {
    id: string;
    first_name: string;
    last_name: string;
    email: string;
  };
  parentFolder?: {
    id: string;
    name: string;
  };
}

interface FolderHierarchyItem {
  id: string;
  name: string;
  children: FolderHierarchyItem[];
  is_secure: boolean;
}

const FolderList = () => {
  const [folders, setFolders] = useState<Folder[]>([]);
  const [folderHierarchy, setFolderHierarchy] = useState<FolderHierarchyItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentParentFolder, setCurrentParentFolder] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [folderPath, setFolderPath] = useState<{ id: string | null; name: string }[]>([
    { id: null, name: 'Root' }
  ]);
  const [permissionModalOpen, setPermissionModalOpen] = useState(false);
  const [selectedFolder, setSelectedFolder] = useState<Folder | null>(null);
  const [displayedFolders, setDisplayedFolders] = useState<Folder[]>([]);

  const { user } = useContext(AuthContext);
  const { currentFarm } = useFarm();

  // Fetch folder hierarchy
  useEffect(() => {
    const fetchFolderHierarchy = async () => {
      setLoading(true);
      setError(null);

      try {
        // Fetch the complete folder hierarchy
        const hierarchyUrl = `${API_URL}/farm/${currentFarm?.id || user?.farm_id}/folder-hierarchy`;
        const hierarchyResponse = await axios.get(hierarchyUrl, {
          headers: {
            Authorization: `Bearer ${getAuthToken()}`
          }
        });

        setFolderHierarchy(hierarchyResponse.data || []);

        // If we're searching, we still need to fetch folders with the search query
        if (searchQuery) {
          let foldersUrl = `${API_URL}/documents/farm/${currentFarm?.id || user?.farm_id}/folders`;
          const params: Record<string, string> = { search: searchQuery };
          const queryString = new URLSearchParams(params).toString();
          foldersUrl = `${foldersUrl}?${queryString}`;

          const foldersResponse = await axios.get(foldersUrl, {
            headers: {
              Authorization: `Bearer ${getAuthToken()}`
            }
          });

          setFolders(foldersResponse.data || []);
          setDisplayedFolders(foldersResponse.data || []);
        } else {
          // If not searching, fetch the current level folders for detailed info
          let foldersUrl = `${API_URL}/documents/farm/${currentFarm?.id || user?.farm_id}/folders`;
          const params: Record<string, string> = {};

          if (currentParentFolder) {
            params.parentFolderId = currentParentFolder;
          } else if (currentParentFolder === null) {
            params.parentFolderId = 'null'; // Explicitly request root folders
          }

          const queryString = new URLSearchParams(params).toString();
          foldersUrl = `${foldersUrl}?${queryString}`;

          const foldersResponse = await axios.get(foldersUrl, {
            headers: {
              Authorization: `Bearer ${getAuthToken()}`
            }
          });

          setFolders(foldersResponse.data || []);
          setDisplayedFolders(foldersResponse.data || []);
        }
      } catch (err: any) {
        console.error('Error fetching folders:', err);
        setError('Failed to load folders. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    if (currentFarm?.id || user?.farm_id) {
      fetchFolderHierarchy();
    } else {
      setError('No farm selected. Please select a farm to view folders.');
      setLoading(false);
    }
  }, [currentFarm?.id, user?.farm_id, currentParentFolder, searchQuery]);

  // Find children folders in hierarchy
  const findChildrenInHierarchy = (parentId: string | null): FolderHierarchyItem[] => {
    if (parentId === null) {
      // Return root level folders
      return folderHierarchy;
    }

    // Find the parent folder in the hierarchy
    const findFolder = (items: FolderHierarchyItem[], id: string): FolderHierarchyItem | null => {
      for (const item of items) {
        if (item.id === id) {
          return item;
        }
        if (item.children.length > 0) {
          const found = findFolder(item.children, id);
          if (found) return found;
        }
      }
      return null;
    };

    const parentFolder = findFolder(folderHierarchy, parentId);
    return parentFolder ? parentFolder.children : [];
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
  };

  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };

  // Handle opening the permission modal
  const handleOpenPermissionModal = (folder: Folder) => {
    setSelectedFolder(folder);
    setPermissionModalOpen(true);
  };

  // Handle folder navigation
  const navigateToFolder = (folderId: string | null, folderName: string) => {
    setCurrentParentFolder(folderId);

    if (folderId === null) {
      // Reset to root
      setFolderPath([{ id: null, name: 'Root' }]);
    } else {
      // Add to path
      setFolderPath(prev => {
        // Check if we're navigating to a folder that's already in the path
        const existingIndex = prev.findIndex(item => item.id === folderId);
        if (existingIndex >= 0) {
          // If so, truncate the path to that point
          return prev.slice(0, existingIndex + 1);
        }
        // Otherwise, add the new folder to the path
        return [...prev, { id: folderId, name: folderName }];
      });
    }

    // If we're not searching, update displayed folders based on hierarchy
    if (!searchQuery) {
      // Find the children of the current folder in the hierarchy
      const childrenInHierarchy = findChildrenInHierarchy(folderId);

      // If we have detailed folder info, use it to update displayed folders
      if (folders.length > 0) {
        // Filter folders to only show those that are children of the current parent
        const childFolders = folders.filter(folder => 
          (folderId === null && (folder.parent_folder_id === null || folder.parent_folder_id === '00000000-0000-0000-0000-000000000000')) || 
          folder.parent_folder_id === folderId
        );

        // If we have detailed info for all children, use it
        if (childFolders.length === childrenInHierarchy.length) {
          setDisplayedFolders(childFolders);
        } else {
          // Otherwise, we need to fetch the detailed info for the children
          // This will trigger the useEffect to fetch the folders
        }
      }
    }
  };

  return (
    <Layout>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Folders</h1>
        <Link
          to="/documents/folders/new"
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
        >
          Create Folder
        </Link>
      </div>

      {/* Breadcrumb and Search */}
      <div className="flex justify-between items-center mb-4">
        <div className="flex items-center space-x-2">
          {folderPath.map((folder, index) => (
            <div key={folder.id || 'root'} className="flex items-center">
              {index > 0 && <span className="text-gray-500 mx-2">/</span>}
              <button
                onClick={() => navigateToFolder(folder.id, folder.name)}
                className={`px-2 py-1 text-sm rounded ${
                  currentParentFolder === folder.id ? 'bg-primary-100 text-primary-700' : 'text-gray-600 hover:bg-gray-100'
                }`}
              >
                {folder.name}
              </button>
            </div>
          ))}
        </div>
        <div className="relative">
          <input
            type="text"
            placeholder="Search folders..."
            value={searchQuery}
            onChange={handleSearchChange}
            className="w-64 px-4 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
          />
          <svg
            className="absolute right-3 top-2.5 h-5 w-5 text-gray-400"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
            />
          </svg>
        </div>
      </div>

      {/* Error message */}
      {error && (
        <div className="mb-4 p-4 text-sm text-red-700 bg-red-100 rounded-md">
          {error}
        </div>
      )}

      {/* Loading state */}
      {loading ? (
        <div className="flex justify-center items-center h-64">
          <svg
            className="animate-spin h-8 w-8 text-primary-500"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              className="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="4"
            ></circle>
            <path
              className="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            ></path>
          </svg>
        </div>
      ) : (
        <>
          {/* Empty state */}
          {displayedFolders.length === 0 && !loading && (
            <div className="flex flex-col items-center justify-center h-64 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
              <svg
                className="h-12 w-12 text-gray-400 mb-2"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z"
                />
              </svg>
              <p className="text-gray-500 mb-2">No folders found</p>
              <Link
                to="/documents/folders/new"
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                Create your first folder
              </Link>
            </div>
          )}

          {/* Folders grid */}
          {displayedFolders.length > 0 && (
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
              {displayedFolders.map((folder) => {
                // Find if this folder has children in the hierarchy
                const hierarchyItem = findChildrenInHierarchy(null).find(item => item.id === folder.id) || 
                                     (currentParentFolder ? findChildrenInHierarchy(currentParentFolder).find(item => item.id === folder.id) : null);
                const hasChildren = hierarchyItem && hierarchyItem.children.length > 0;

                return (
                  <div
                    key={folder.id}
                    className="bg-white rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow"
                  >
                    <div className="p-4">
                      <div className="flex items-center justify-between mb-2">
                        <div 
                          className="text-primary-600 hover:text-primary-900 font-medium cursor-pointer"
                          onClick={() => navigateToFolder(folder.id, folder.name)}
                        >
                          <div className="flex items-center">
                            <svg
                              className="h-6 w-6 text-gray-500 mr-2"
                              fill="none"
                              viewBox="0 0 24 24"
                              stroke="currentColor"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z"
                              />
                            </svg>
                            <span className="flex items-center">
                              {folder.name}
                              {hasChildren && (
                                <span className="ml-2 text-xs bg-gray-200 text-gray-700 px-2 py-0.5 rounded-full">
                                  {hierarchyItem?.children.length}
                                </span>
                              )}
                            </span>
                          </div>
                        </div>
                        <div className="flex space-x-2">
                          <Link
                            to={`/documents/folders/edit/${folder.id}`}
                            className="text-gray-600 hover:text-gray-900"
                            title="Edit folder"
                          >
                            <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                              />
                            </svg>
                          </Link>
                          <button
                            onClick={() => handleOpenPermissionModal(folder)}
                            className="text-gray-600 hover:text-gray-900"
                            title="Share folder"
                          >
                            <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.368 2.684 3 3 0 00-5.368-2.684z"
                              />
                            </svg>
                          </button>
                        </div>
                      </div>
                      <p className="text-sm text-gray-500 mb-2 line-clamp-2">
                        {folder.description || 'No description'}
                      </p>
                      <div className="flex justify-between text-xs text-gray-500">
                        <span>Created: {formatDate(folder.created_at)}</span>
                        <span>By: {folder.creator.first_name} {folder.creator.last_name}</span>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </>
      )}
      {/* Permission Modal */}
      {selectedFolder && (
        <FileManagerPermissionModal
          isOpen={permissionModalOpen}
          onClose={() => setPermissionModalOpen(false)}
          item={{
            id: selectedFolder.id,
            name: selectedFolder.name,
            description: selectedFolder.description || '',
            type: 'folder',
            farm_id: selectedFolder.farm_id,
            created_at: selectedFolder.created_at,
            updated_at: selectedFolder.updated_at,
            parent_folder_id: selectedFolder.parent_folder_id
          }}
        />
      )}
    </Layout>
  );
};

export default FolderList;
