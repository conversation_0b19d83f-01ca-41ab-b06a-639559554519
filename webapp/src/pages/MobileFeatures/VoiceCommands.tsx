import React, { useState } from 'react';
import { useAuth } from '../../context/AuthContext';
import { useFarm } from '../../context/FarmContext';
import Layout from '../../components/Layout';
import { Link } from 'react-router-dom';

const VoiceCommands = () => {
  const { user } = useAuth();
  const { currentFarm } = useFarm();
  const [isListening, setIsListening] = useState<boolean>(false);
  const [transcript, setTranscript] = useState<string>('');
  const [commandHistory, setCommandHistory] = useState<{command: string, action: string, timestamp: string}[]>([
    { command: "Record soil sample", action: "Added soil sample record for North Field", timestamp: "2023-10-15 14:32" },
    { command: "Start irrigation in Field 3", action: "Started irrigation system in East Field", timestamp: "2023-10-14 09:15" },
    { command: "Log equipment maintenance", action: "Created maintenance record for Tractor #2", timestamp: "2023-10-12 16:45" }
  ]);

  const handleStartListening = () => {
    setIsListening(true);
    setTranscript('Listening...');
    
    // Simulate voice recognition after 3 seconds
    setTimeout(() => {
      const mockCommands = [
        "Check weather forecast",
        "Record crop observation",
        "Start field mapping",
        "Log equipment usage",
        "Create task reminder"
      ];
      
      const randomCommand = mockCommands[Math.floor(Math.random() * mockCommands.length)];
      setTranscript(randomCommand);
      
      // Simulate processing after 1 more second
      setTimeout(() => {
        setIsListening(false);
        
        // Add to command history
        const newAction = getActionForCommand(randomCommand);
        setCommandHistory(prev => [
          {
            command: randomCommand,
            action: newAction,
            timestamp: new Date().toLocaleString()
          },
          ...prev
        ]);
      }, 1000);
    }, 3000);
  };

  const getActionForCommand = (command: string): string => {
    switch (command) {
      case "Check weather forecast":
        return "Displayed 5-day weather forecast for farm location";
      case "Record crop observation":
        return "Created new crop observation entry for current field";
      case "Start field mapping":
        return "Initiated field boundary mapping for current location";
      case "Log equipment usage":
        return "Recorded 2.5 hours of usage for Tractor #1";
      case "Create task reminder":
        return "Added task reminder for tomorrow at 9:00 AM";
      default:
        return "Command processed successfully";
    }
  };

  return (
    <Layout>
      <div className="px-4 sm:px-6 lg:px-8 py-8">
        <div className="sm:flex sm:items-center">
          <div className="sm:flex-auto">
            <h1 className="text-2xl font-semibold text-gray-900">Voice Commands</h1>
            <p className="mt-2 text-sm text-gray-700">
              Use voice commands for hands-free operation in the field. Simply speak commands to perform actions in the mobile app.
            </p>
          </div>
          <div className="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
            <Link
              to="/mobile-features"
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              All Mobile Features
            </Link>
          </div>
        </div>

        {/* Mobile App Download Section */}
        <div className="mt-8 bg-white shadow sm:rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900">Mobile App Required</h3>
            <div className="mt-2 max-w-xl text-sm text-gray-500">
              <p>
                To use voice commands, you need to download the NxtAcre mobile app. The app is available for iOS and Android devices.
              </p>
            </div>
            <div className="mt-5 flex space-x-4">
              <a
                href="#"
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                <svg className="h-5 w-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M10 0C4.477 0 0 4.477 0 10c0 5.523 4.477 10 10 10s10-4.477 10-10C20 4.477 15.523 0 10 0zm5.5 15.5l-1.414 1.414L10 12.828l-4.086 4.086L4.5 15.5l4.086-4.086L4.5 7.328l1.414-1.414L10 9.172l4.086-4.086L15.5 6.5l-4.086 4.086L15.5 14.672z"/>
                </svg>
                Download for iOS
              </a>
              <a
                href="#"
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                <svg className="h-5 w-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M10 0C4.477 0 0 4.477 0 10c0 5.523 4.477 10 10 10s10-4.477 10-10C20 4.477 15.523 0 10 0zm5.5 15.5l-1.414 1.414L10 12.828l-4.086 4.086L4.5 15.5l4.086-4.086L4.5 7.328l1.414-1.414L10 9.172l4.086-4.086L15.5 6.5l-4.086 4.086L15.5 14.672z"/>
                </svg>
                Download for Android
              </a>
            </div>
          </div>
        </div>

        {/* Voice Command Demo */}
        <div className="mt-8 bg-white shadow sm:rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900">Voice Command Demo</h3>
            <div className="mt-2 max-w-xl text-sm text-gray-500">
              <p>
                Try out voice commands with this demo. In the mobile app, you can use these commands in the field for hands-free operation.
              </p>
            </div>
            <div className="mt-5">
              <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
                <div className="flex flex-col items-center">
                  <button
                    type="button"
                    onClick={handleStartListening}
                    disabled={isListening}
                    className={`inline-flex items-center justify-center p-4 rounded-full ${
                      isListening 
                        ? 'bg-red-100 text-red-700 animate-pulse' 
                        : 'bg-primary-100 text-primary-700 hover:bg-primary-200'
                    } focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500`}
                  >
                    <svg className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                    </svg>
                  </button>
                  <p className="mt-2 text-sm font-medium text-gray-900">
                    {isListening ? 'Listening...' : 'Tap to speak'}
                  </p>
                  {transcript && (
                    <div className="mt-4 p-3 bg-white rounded-lg border border-gray-200 w-full max-w-md">
                      <p className="text-sm text-gray-700">{transcript}</p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Available Commands */}
        <div className="mt-8 bg-white shadow overflow-hidden sm:rounded-lg">
          <div className="px-4 py-5 sm:px-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900">Available Voice Commands</h3>
            <p className="mt-1 max-w-2xl text-sm text-gray-500">
              These commands are available in the mobile app for hands-free operation in the field.
            </p>
          </div>
          <div className="border-t border-gray-200">
            <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Field Operations</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                <ul className="divide-y divide-gray-200">
                  <li className="py-2">
                    <div className="flex items-start">
                      <span className="font-medium">"Start field mapping"</span>
                      <span className="ml-2 text-gray-500">- Begin mapping field boundaries at current location</span>
                    </div>
                  </li>
                  <li className="py-2">
                    <div className="flex items-start">
                      <span className="font-medium">"Record soil sample"</span>
                      <span className="ml-2 text-gray-500">- Create a new soil sample record at current location</span>
                    </div>
                  </li>
                  <li className="py-2">
                    <div className="flex items-start">
                      <span className="font-medium">"Log crop observation"</span>
                      <span className="ml-2 text-gray-500">- Record observations about crops at current location</span>
                    </div>
                  </li>
                </ul>
              </dd>
            </div>
            <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Equipment Management</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                <ul className="divide-y divide-gray-200">
                  <li className="py-2">
                    <div className="flex items-start">
                      <span className="font-medium">"Start equipment diagnostics"</span>
                      <span className="ml-2 text-gray-500">- Begin diagnostic check on connected equipment</span>
                    </div>
                  </li>
                  <li className="py-2">
                    <div className="flex items-start">
                      <span className="font-medium">"Log equipment usage"</span>
                      <span className="ml-2 text-gray-500">- Record hours or mileage for equipment</span>
                    </div>
                  </li>
                  <li className="py-2">
                    <div className="flex items-start">
                      <span className="font-medium">"Schedule maintenance"</span>
                      <span className="ml-2 text-gray-500">- Create maintenance schedule for equipment</span>
                    </div>
                  </li>
                </ul>
              </dd>
            </div>
            <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Task Management</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                <ul className="divide-y divide-gray-200">
                  <li className="py-2">
                    <div className="flex items-start">
                      <span className="font-medium">"Create task"</span>
                      <span className="ml-2 text-gray-500">- Add a new task to your task list</span>
                    </div>
                  </li>
                  <li className="py-2">
                    <div className="flex items-start">
                      <span className="font-medium">"Complete task"</span>
                      <span className="ml-2 text-gray-500">- Mark the current or specified task as complete</span>
                    </div>
                  </li>
                  <li className="py-2">
                    <div className="flex items-start">
                      <span className="font-medium">"Assign task to [name]"</span>
                      <span className="ml-2 text-gray-500">- Assign a task to a team member</span>
                    </div>
                  </li>
                </ul>
              </dd>
            </div>
          </div>
        </div>

        {/* Command History */}
        <div className="mt-8 bg-white shadow overflow-hidden sm:rounded-lg">
          <div className="px-4 py-5 sm:px-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900">Command History</h3>
            <p className="mt-1 max-w-2xl text-sm text-gray-500">
              Recent voice commands and actions taken.
            </p>
          </div>
          <div className="border-t border-gray-200">
            <ul className="divide-y divide-gray-200">
              {commandHistory.map((item, index) => (
                <li key={index} className="px-4 py-4 sm:px-6">
                  <div className="flex flex-col sm:flex-row sm:justify-between">
                    <div className="mb-2 sm:mb-0">
                      <p className="text-sm font-medium text-gray-900">"{item.command}"</p>
                      <p className="text-sm text-gray-500">{item.action}</p>
                    </div>
                    <div className="text-sm text-gray-500">
                      {item.timestamp}
                    </div>
                  </div>
                </li>
              ))}
              {commandHistory.length === 0 && (
                <li className="px-4 py-5 sm:px-6 text-center text-sm text-gray-500">
                  No command history available. Try using voice commands above.
                </li>
              )}
            </ul>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default VoiceCommands;