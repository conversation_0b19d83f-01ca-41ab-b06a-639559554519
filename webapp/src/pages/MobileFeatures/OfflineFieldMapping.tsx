import React, { useState } from 'react';
import { useAuth } from '../../context/AuthContext';
import { useFarm } from '../../context/FarmContext';
import Layout from '../../components/Layout';
import { Link } from 'react-router-dom';

const OfflineFieldMapping = () => {
  const { user } = useAuth();
  const { currentFarm } = useFarm();
  const [isDownloading, setIsDownloading] = useState<boolean>(false);
  const [downloadProgress, setDownloadProgress] = useState<number>(0);
  const [offlineMaps, setOfflineMaps] = useState<{name: string, size: string, date: string}[]>([
    { name: 'North Field', size: '12.5 MB', date: '2023-10-15' },
    { name: 'South Field', size: '8.2 MB', date: '2023-10-10' },
    { name: 'East Field', size: '15.7 MB', date: '2023-09-28' }
  ]);

  const handleDownload = (fieldName: string) => {
    setIsDownloading(true);
    setDownloadProgress(0);

    // Simulate download progress
    const interval = setInterval(() => {
      setDownloadProgress(prev => {
        if (prev >= 100) {
          clearInterval(interval);
          setIsDownloading(false);
          // Add the new map to the list
          setOfflineMaps(prev => [
            ...prev, 
            { 
              name: fieldName, 
              size: `${(Math.random() * 10 + 5).toFixed(1)} MB`, 
              date: new Date().toISOString().split('T')[0] 
            }
          ]);
          return 0;
        }
        return prev + 10;
      });
    }, 500);
  };

  const handleDelete = (fieldName: string) => {
    setOfflineMaps(prev => prev.filter(map => map.name !== fieldName));
  };

  return (
    <Layout>
      <div className="px-4 sm:px-6 lg:px-8 py-8">
        <div className="sm:flex sm:items-center">
          <div className="sm:flex-auto">
            <h1 className="text-2xl font-semibold text-gray-900">Offline Field Mapping</h1>
            <p className="mt-2 text-sm text-gray-700">
              Download field maps for offline use in the mobile app. Create and edit field boundaries even without an internet connection.
            </p>
          </div>
          <div className="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
            <Link
              to="/fields"
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              View All Fields
            </Link>
          </div>
        </div>

        {/* Mobile App Download Section */}
        <div className="mt-8 bg-white shadow sm:rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900">Mobile App Required</h3>
            <div className="mt-2 max-w-xl text-sm text-gray-500">
              <p>
                To use offline field mapping, you need to download the NxtAcre mobile app. The app is available for iOS and Android devices.
              </p>
            </div>
            <div className="mt-5 flex space-x-4">
              <a
                href="#"
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                <svg className="h-5 w-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M10 0C4.477 0 0 4.477 0 10c0 5.523 4.477 10 10 10s10-4.477 10-10C20 4.477 15.523 0 10 0zm5.5 15.5l-1.414 1.414L10 12.828l-4.086 4.086L4.5 15.5l4.086-4.086L4.5 7.328l1.414-1.414L10 9.172l4.086-4.086L15.5 6.5l-4.086 4.086L15.5 14.672z"/>
                </svg>
                Download for iOS
              </a>
              <a
                href="#"
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                <svg className="h-5 w-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M10 0C4.477 0 0 4.477 0 10c0 5.523 4.477 10 10 10s10-4.477 10-10C20 4.477 15.523 0 10 0zm5.5 15.5l-1.414 1.414L10 12.828l-4.086 4.086L4.5 15.5l4.086-4.086L4.5 7.328l1.414-1.414L10 9.172l4.086-4.086L15.5 6.5l-4.086 4.086L15.5 14.672z"/>
                </svg>
                Download for Android
              </a>
            </div>
          </div>
        </div>

        {/* Download Maps Section */}
        <div className="mt-8 bg-white shadow sm:rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900">Download Field Maps</h3>
            <div className="mt-2 max-w-xl text-sm text-gray-500">
              <p>
                Select fields to download for offline use. Downloaded maps will be available in the mobile app even without an internet connection.
              </p>
            </div>
            <div className="mt-5">
              <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
                <div className="relative rounded-lg border border-gray-300 bg-white px-6 py-5 shadow-sm flex items-center space-x-3 hover:border-gray-400 focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-primary-500">
                  <div className="flex-1 min-w-0">
                    <a href="#" className="focus:outline-none">
                      <span className="absolute inset-0" aria-hidden="true"></span>
                      <p className="text-sm font-medium text-gray-900">West Field</p>
                      <p className="text-sm text-gray-500 truncate">15.3 acres</p>
                    </a>
                  </div>
                  <button
                    type="button"
                    onClick={() => handleDownload('West Field')}
                    disabled={isDownloading}
                    className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"
                  >
                    {isDownloading ? 'Downloading...' : 'Download'}
                  </button>
                </div>

                <div className="relative rounded-lg border border-gray-300 bg-white px-6 py-5 shadow-sm flex items-center space-x-3 hover:border-gray-400 focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-primary-500">
                  <div className="flex-1 min-w-0">
                    <a href="#" className="focus:outline-none">
                      <span className="absolute inset-0" aria-hidden="true"></span>
                      <p className="text-sm font-medium text-gray-900">Central Field</p>
                      <p className="text-sm text-gray-500 truncate">22.7 acres</p>
                    </a>
                  </div>
                  <button
                    type="button"
                    onClick={() => handleDownload('Central Field')}
                    disabled={isDownloading}
                    className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"
                  >
                    {isDownloading ? 'Downloading...' : 'Download'}
                  </button>
                </div>

                <div className="relative rounded-lg border border-gray-300 bg-white px-6 py-5 shadow-sm flex items-center space-x-3 hover:border-gray-400 focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-primary-500">
                  <div className="flex-1 min-w-0">
                    <a href="#" className="focus:outline-none">
                      <span className="absolute inset-0" aria-hidden="true"></span>
                      <p className="text-sm font-medium text-gray-900">River Field</p>
                      <p className="text-sm text-gray-500 truncate">18.9 acres</p>
                    </a>
                  </div>
                  <button
                    type="button"
                    onClick={() => handleDownload('River Field')}
                    disabled={isDownloading}
                    className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"
                  >
                    {isDownloading ? 'Downloading...' : 'Download'}
                  </button>
                </div>
              </div>
            </div>

            {isDownloading && (
              <div className="mt-6">
                <h4 className="text-sm font-medium text-gray-900">Downloading map...</h4>
                <div className="mt-2 w-full bg-gray-200 rounded-full h-2.5">
                  <div className="bg-primary-600 h-2.5 rounded-full" style={{ width: `${downloadProgress}%` }}></div>
                </div>
                <p className="mt-2 text-xs text-gray-500">{downloadProgress}% complete</p>
              </div>
            )}
          </div>
        </div>

        {/* Downloaded Maps Section */}
        <div className="mt-8 bg-white shadow overflow-hidden sm:rounded-lg">
          <div className="px-4 py-5 sm:px-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900">Downloaded Maps</h3>
            <p className="mt-1 max-w-2xl text-sm text-gray-500">
              Maps that are available for offline use in the mobile app.
            </p>
          </div>
          <div className="border-t border-gray-200">
            <ul className="divide-y divide-gray-200">
              {offlineMaps.map((map) => (
                <li key={map.name} className="px-4 py-4 sm:px-6">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className="flex-shrink-0">
                        <svg className="h-8 w-8 text-primary-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7" />
                        </svg>
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900">{map.name}</div>
                        <div className="text-sm text-gray-500">
                          Size: {map.size} • Downloaded: {map.date}
                        </div>
                      </div>
                    </div>
                    <div>
                      <button
                        type="button"
                        onClick={() => handleDelete(map.name)}
                        className="inline-flex items-center px-2.5 py-1.5 border border-transparent text-xs font-medium rounded text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                      >
                        Delete
                      </button>
                    </div>
                  </div>
                </li>
              ))}
              {offlineMaps.length === 0 && (
                <li className="px-4 py-5 sm:px-6 text-center text-sm text-gray-500">
                  No maps have been downloaded yet. Download maps above to use them offline.
                </li>
              )}
            </ul>
          </div>
        </div>

        {/* Offline Features Section */}
        <div className="mt-8 bg-white shadow overflow-hidden sm:rounded-lg">
          <div className="px-4 py-5 sm:px-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900">Offline Features</h3>
            <p className="mt-1 max-w-2xl text-sm text-gray-500">
              Features available when using maps offline in the mobile app.
            </p>
          </div>
          <div className="border-t border-gray-200 px-4 py-5 sm:p-6">
            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
              <div className="bg-white overflow-hidden shadow rounded-lg">
                <div className="px-4 py-5 sm:p-6">
                  <div className="flex items-center">
                    <div className="flex-shrink-0 bg-primary-100 rounded-md p-3">
                      <svg className="h-6 w-6 text-primary-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7" />
                      </svg>
                    </div>
                    <div className="ml-5">
                      <h3 className="text-lg leading-6 font-medium text-gray-900">Field Boundary Creation</h3>
                      <div className="mt-2 text-sm text-gray-500">
                        Create and edit field boundaries while in the field, even without an internet connection.
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-white overflow-hidden shadow rounded-lg">
                <div className="px-4 py-5 sm:p-6">
                  <div className="flex items-center">
                    <div className="flex-shrink-0 bg-primary-100 rounded-md p-3">
                      <svg className="h-6 w-6 text-primary-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                      </svg>
                    </div>
                    <div className="ml-5">
                      <h3 className="text-lg leading-6 font-medium text-gray-900">GPS Tracking</h3>
                      <div className="mt-2 text-sm text-gray-500">
                        Track your location in the field and record GPS coordinates without an internet connection.
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-white overflow-hidden shadow rounded-lg">
                <div className="px-4 py-5 sm:p-6">
                  <div className="flex items-center">
                    <div className="flex-shrink-0 bg-primary-100 rounded-md p-3">
                      <svg className="h-6 w-6 text-primary-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4m0 5c0 2.21-3.582 4-8 4s-8-1.79-8-4" />
                      </svg>
                    </div>
                    <div className="ml-5">
                      <h3 className="text-lg leading-6 font-medium text-gray-900">Data Collection</h3>
                      <div className="mt-2 text-sm text-gray-500">
                        Collect field data such as soil samples, crop observations, and notes while offline.
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default OfflineFieldMapping;