import React, { useState, useEffect } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { Button } from '../../components/ui/Button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/Card';
import { Badge } from '../../components/ui/Badge';
import { Separator } from '../../components/ui/Separator';
import { formatCurrency } from '../../utils/formatters';
import { 
  CheckCircle, 
  ShoppingBag, 
  Truck, 
  Calendar, 
  Clock, 
  MapPin, 
  CreditCard,
  AlertCircle
} from 'lucide-react';

const CheckoutSuccess: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const [orders, setOrders] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // In a real implementation, we would fetch the order details from the API
  // For now, we'll use mock data or data passed through location state
  useEffect(() => {
    const fetchOrderDetails = async () => {
      setLoading(true);
      try {
        // Check if order data was passed through location state
        if (location.state && location.state.orders) {
          setOrders(location.state.orders);
        } else {
          // Mock data for demonstration
          setOrders([
            {
              id: 'ord_' + Math.random().toString(36).substr(2, 9),
              farm: {
                id: 'farm_1',
                name: 'Green Valley Farm',
                logo_url: null
              },
              status: 'pending',
              created_at: new Date().toISOString(),
              fulfillment_method: 'delivery',
              estimated_delivery: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString(),
              items: [
                {
                  id: 'item_1',
                  product: {
                    id: 'prod_1',
                    name: 'Organic Apples',
                    price: 4.99,
                    images: []
                  },
                  quantity: 2,
                  price: 4.99
                },
                {
                  id: 'item_2',
                  product: {
                    id: 'prod_2',
                    name: 'Fresh Eggs (Dozen)',
                    price: 6.99,
                    images: []
                  },
                  quantity: 1,
                  price: 6.99
                }
              ],
              subtotal: 16.97,
              delivery_fee: 5.00,
              total: 21.97,
              payment_method: 'card',
              delivery_address: {
                address: '123 Main St',
                city: 'Farmville',
                state: 'CA',
                zip_code: '12345'
              }
            }
          ]);
        }
      } catch (err) {
        console.error('Error fetching order details:', err);
        setError('Failed to load order details. Please check your order history for the latest information.');
      } finally {
        setLoading(false);
      }
    };

    fetchOrderDetails();
  }, [location.state]);

  const formatDate = (dateString: string) => {
    const options: Intl.DateTimeFormatOptions = { 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  if (loading) {
    return (
      <div className="container mx-auto py-16 px-4 flex justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto py-16 px-4">
        <div className="max-w-2xl mx-auto bg-white rounded-lg shadow-md p-8 text-center">
          <AlertCircle className="h-16 w-16 text-red-500 mx-auto mb-6" />
          <h1 className="text-2xl font-bold text-gray-800 mb-4">
            Something went wrong
          </h1>
          <p className="text-gray-600 mb-6">{error}</p>
          <div className="flex flex-col sm:flex-row justify-center gap-4">
            <Button asChild variant="outline">
              <Link to="/marketplace">
                <ShoppingBag className="mr-2 h-4 w-4" />
                Continue Shopping
              </Link>
            </Button>
            <Button asChild>
              <Link to="/marketplace/orders">
                View My Orders
              </Link>
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-16 px-4">
      <div className="max-w-4xl mx-auto">
        <div className="bg-white rounded-lg shadow-md p-8 text-center mb-8">
          <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-6" />

          <h1 className="text-2xl font-bold text-gray-800 mb-4">
            Order Request{orders.length > 1 ? 's' : ''} Submitted Successfully!
          </h1>

          <p className="text-gray-600 mb-6">
            Thank you for your order {orders.length > 1 ? 'requests' : 'request'}. The farm{orders.length > 1 ? 's' : ''} will review your {orders.length > 1 ? 'requests' : 'request'} and contact you soon
            to arrange payment and {orders[0]?.fulfillment_method === 'delivery' ? 'delivery' : 'pickup'} details.
          </p>

          <div className="bg-gray-50 rounded-lg p-6 mb-8">
            <h2 className="text-lg font-semibold text-gray-700 mb-3">What happens next?</h2>

            <ul className="text-left text-gray-600 space-y-3">
              <li className="flex items-start">
                <span className="text-green-500 mr-2">1.</span>
                <span>The farm{orders.length > 1 ? 's' : ''} will review your order {orders.length > 1 ? 'requests' : 'request'}.</span>
              </li>
              <li className="flex items-start">
                <span className="text-green-500 mr-2">2.</span>
                <span>You'll receive a notification when your {orders.length > 1 ? 'requests are' : 'request is'} approved.</span>
              </li>
              <li className="flex items-start">
                <span className="text-green-500 mr-2">3.</span>
                <span>The farm{orders.length > 1 ? 's' : ''} will contact you to arrange payment and {orders[0]?.fulfillment_method === 'delivery' ? 'delivery' : 'pickup'} details.</span>
              </li>
              <li className="flex items-start">
                <span className="text-green-500 mr-2">4.</span>
                <span>You can view the status of your order{orders.length > 1 ? 's' : ''} in your account dashboard.</span>
              </li>
            </ul>
          </div>
        </div>

        {/* Order Summary Section */}
        <div className="mb-8">
          <h2 className="text-xl font-bold mb-4">Order {orders.length > 1 ? 'Summaries' : 'Summary'}</h2>

          {orders.map((order, index) => (
            <Card key={order.id} className="mb-6">
              <CardHeader className="pb-2">
                <div className="flex justify-between items-center">
                  <div>
                    <CardTitle className="flex items-center">
                      {order.farm.logo_url ? (
                        <img 
                          src={order.farm.logo_url} 
                          alt={order.farm.name} 
                          className="w-6 h-6 rounded-full mr-2"
                        />
                      ) : (
                        <ShoppingBag className="w-5 h-5 text-gray-500 mr-2" />
                      )}
                      {order.farm.name}
                    </CardTitle>
                    <CardDescription>
                      Order ID: {order.id}
                    </CardDescription>
                  </div>
                  <Badge className="bg-yellow-100 text-yellow-800">
                    {order.status.toUpperCase()}
                  </Badge>
                </div>
              </CardHeader>

              <CardContent>
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <h3 className="text-sm font-medium text-gray-500 mb-1">Order Date</h3>
                      <p className="flex items-center">
                        <Calendar className="h-4 w-4 mr-1 text-gray-400" />
                        {formatDate(order.created_at)}
                      </p>
                    </div>

                    <div>
                      <h3 className="text-sm font-medium text-gray-500 mb-1">Fulfillment Method</h3>
                      <p className="flex items-center capitalize">
                        {order.fulfillment_method === 'delivery' ? (
                          <Truck className="h-4 w-4 mr-1 text-gray-400" />
                        ) : (
                          <MapPin className="h-4 w-4 mr-1 text-gray-400" />
                        )}
                        {order.fulfillment_method}
                      </p>
                    </div>
                  </div>

                  {order.fulfillment_method === 'delivery' && order.estimated_delivery && (
                    <div>
                      <h3 className="text-sm font-medium text-gray-500 mb-1">Estimated Delivery</h3>
                      <p className="flex items-center">
                        <Clock className="h-4 w-4 mr-1 text-gray-400" />
                        {formatDate(order.estimated_delivery)}
                      </p>
                    </div>
                  )}

                  {order.fulfillment_method === 'delivery' && order.delivery_address && (
                    <div>
                      <h3 className="text-sm font-medium text-gray-500 mb-1">Delivery Address</h3>
                      <p className="text-sm">
                        {order.delivery_address.address}, {order.delivery_address.city}, {order.delivery_address.state} {order.delivery_address.zip_code}
                      </p>
                    </div>
                  )}

                  {order.fulfillment_method === 'pickup' && order.pickup_date && (
                    <div>
                      <h3 className="text-sm font-medium text-gray-500 mb-1">Pickup Date</h3>
                      <p className="flex items-center">
                        <Clock className="h-4 w-4 mr-1 text-gray-400" />
                        {formatDate(order.pickup_date)}
                      </p>
                    </div>
                  )}

                  <div>
                    <h3 className="text-sm font-medium text-gray-500 mb-1">Payment Method</h3>
                    <p className="flex items-center">
                      <CreditCard className="h-4 w-4 mr-1 text-gray-400" />
                      {order.payment_method === 'card' ? 'Credit/Debit Card' : 
                       order.payment_method === 'cash' ? 'Cash on Delivery/Pickup' : 
                       'Invoice'}
                    </p>
                  </div>

                  <Separator />

                  <div>
                    <h3 className="text-sm font-medium text-gray-500 mb-2">Order Items</h3>
                    <div className="space-y-2">
                      {order.items.map((item: any) => (
                        <div key={item.id} className="flex justify-between items-center">
                          <div className="flex items-center">
                            <div className="w-10 h-10 bg-gray-100 rounded-md overflow-hidden mr-3 flex-shrink-0">
                              {item.product.images && item.product.images.length > 0 ? (
                                <img 
                                  src={item.product.images[0].file_path} 
                                  alt={item.product.name} 
                                  className="w-full h-full object-cover"
                                />
                              ) : (
                                <div className="w-full h-full flex items-center justify-center bg-gray-200">
                                  <span className="text-gray-400 text-xs">No image</span>
                                </div>
                              )}
                            </div>
                            <div>
                              <p className="font-medium">{item.product.name}</p>
                              <p className="text-sm text-gray-500">Qty: {item.quantity}</p>
                            </div>
                          </div>
                          <p className="font-medium">{formatCurrency(item.price * item.quantity)}</p>
                        </div>
                      ))}
                    </div>
                  </div>

                  <Separator />

                  <div className="space-y-1">
                    <div className="flex justify-between text-sm">
                      <span>Subtotal</span>
                      <span>{formatCurrency(order.subtotal)}</span>
                    </div>

                    {order.delivery_fee > 0 && (
                      <div className="flex justify-between text-sm">
                        <span>Delivery Fee</span>
                        <span>{formatCurrency(order.delivery_fee)}</span>
                      </div>
                    )}

                    <div className="flex justify-between font-medium pt-1">
                      <span>Total</span>
                      <span>{formatCurrency(order.total)}</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="flex flex-col sm:flex-row justify-center gap-4">
          <Button asChild variant="outline">
            <Link to="/marketplace">
              <ShoppingBag className="mr-2 h-4 w-4" />
              Continue Shopping
            </Link>
          </Button>

          <Button asChild>
            <Link to="/marketplace/orders">
              View My Orders
            </Link>
          </Button>
        </div>
      </div>
    </div>
  );
};

export default CheckoutSuccess;
