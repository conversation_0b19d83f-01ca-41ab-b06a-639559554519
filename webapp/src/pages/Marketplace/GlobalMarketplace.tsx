import React, { useState, useEffect } from 'react';
import { useSearchParams, Link } from 'react-router-dom';
import Layout from '../../components/Layout';
import { 
  getMarketplaceProducts, 
  getMarketplaceCategories,
  searchMarketplaceProducts,
  MarketplaceProduct
} from '../../services/productMarketplaceService';
import { Button } from '../../components/ui/Button';
import { Input } from '../../components/ui/Input';
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '../../components/ui/Card';
import { Pagination } from '../../components/ui/Pagination';
import { Checkbox } from '../../components/ui/Checkbox';
import { Label } from '../../components/ui/Label';
import { Slider } from '../../components/ui/Slider';
import { CartProvider } from '../../context/CartContext';
import CartButton from '../../components/cart/CartButton';
import AddToCartButton from '../../components/cart/AddToCartButton';

const GlobalMarketplaceContent: React.FC = () => {
  // URL search params
  const [searchParams, setSearchParams] = useSearchParams();

  // State for products and pagination
  const [products, setProducts] = useState<MarketplaceProduct[]>([]);
  const [totalProducts, setTotalProducts] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // State for filters
  const [categories, setCategories] = useState<string[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [searchQuery, setSearchQuery] = useState('');
  const [priceRange, setPriceRange] = useState<[number, number]>([0, 1000]);
  const [selectedFarms, setSelectedFarms] = useState<string[]>([]);
  const [farms, setFarms] = useState<{ id: string; name: string }[]>([]);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(12);

  // Get query parameters from URL
  useEffect(() => {
    const category = searchParams.get('category') || '';
    const search = searchParams.get('search') || '';
    const page = parseInt(searchParams.get('page') || '1', 10);
    const minPrice = parseFloat(searchParams.get('minPrice') || '0');
    const maxPrice = parseFloat(searchParams.get('maxPrice') || '1000');
    const farmIds = searchParams.getAll('farmId');

    setSelectedCategory(category);
    setSearchQuery(search);
    setCurrentPage(page);
    setPriceRange([minPrice, maxPrice]);
    setSelectedFarms(farmIds);
  }, [searchParams]);

  // Fetch categories on component mount
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const fetchedCategories = await getMarketplaceCategories();
        setCategories(fetchedCategories);
      } catch (err) {
        console.error('Error fetching categories:', err);
        setError('Failed to load categories. Please try again later.');
      }
    };

    fetchCategories();
  }, []);

  // Fetch products when filters or pagination changes
  useEffect(() => {
    const fetchProducts = async () => {
      setLoading(true);
      setError(null);

      try {
        let result;

        if (searchQuery) {
          // Use search endpoint if there's a search query
          result = await searchMarketplaceProducts(searchQuery, {
            category: selectedCategory,
            minPrice: priceRange[0],
            maxPrice: priceRange[1],
            farmIds: selectedFarms,
            page: currentPage,
            limit: itemsPerPage
          });
        } else {
          // Use regular products endpoint if no search query
          result = await getMarketplaceProducts({
            category: selectedCategory,
            minPrice: priceRange[0],
            maxPrice: priceRange[1],
            farmIds: selectedFarms,
            page: currentPage,
            limit: itemsPerPage
          });
        }

        setProducts(result.products);
        setTotalProducts(result.total);

        // Extract unique farms from products for the farm filter
        const uniqueFarms: { id: string; name: string }[] = Array.from(
          new Map<string, { id: string; name: string }>(
            result.products
              .map((product: MarketplaceProduct) => ({ id: String(product.farm_id), name: product.farm_name }))
              .filter(farm => farm.id && farm.name)
              .map(farm => [farm.id, farm] as [string, { id: string; name: string }])
          ).values()
        );

        if (uniqueFarms.length > 0) {
          setFarms((prevFarms: { id: string; name: string }[]): { id: string; name: string }[] => {
            const existingFarmIds = new Set(prevFarms.map(f => f.id));
            const newFarms = uniqueFarms.filter(f => !existingFarmIds.has(f.id));
            return [...prevFarms, ...newFarms];
          });
        }
      } catch (err) {
        console.error('Error fetching products:', err);
        setError('Failed to load products. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchProducts();
  }, [selectedCategory, searchQuery, priceRange, selectedFarms, currentPage, itemsPerPage]);

  // Update URL when filters change
  const updateFilters = () => {
    const params = new URLSearchParams();

    if (selectedCategory) params.append('category', selectedCategory);
    if (searchQuery) params.append('search', searchQuery);
    if (currentPage > 1) params.append('page', currentPage.toString());
    if (priceRange[0] > 0) params.append('minPrice', priceRange[0].toString());
    if (priceRange[1] < 1000) params.append('maxPrice', priceRange[1].toString());
    selectedFarms.forEach(farmId => params.append('farmId', farmId));

    setSearchParams(params);
  };

  // Handle search form submission
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setCurrentPage(1); // Reset to first page on new search
    updateFilters();
  };

  // Handle category selection
  const handleCategoryChange = (category: string) => {
    setSelectedCategory(category);
    setCurrentPage(1); // Reset to first page on category change
    updateFilters();
  };

  // Handle price range change
  const handlePriceChange = (values: number[]) => {
    setPriceRange([values[0], values[1]]);
  };

  // Handle price range apply
  const handlePriceApply = () => {
    updateFilters();
  };

  // Handle farm selection
  const handleFarmChange = (farmId: string, checked: boolean) => {
    setSelectedFarms(prev => {
      if (checked) {
        return [...prev, farmId];
      } else {
        return prev.filter(id => id !== farmId);
      }
    });
    setCurrentPage(1); // Reset to first page on farm change
    updateFilters();
  };

  // Handle pagination
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    updateFilters();
  };

  // Calculate total pages
  const totalPages = Math.ceil(totalProducts / itemsPerPage);

  return (
    <Layout>
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-between items-center mb-8">
          <h1 className="text-3xl font-bold">NxtAcre Marketplace</h1>
          <div className="flex space-x-4">
            <form onSubmit={handleSearch} className="flex">
              <Input
                type="text"
                placeholder="Search products..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-64 mr-2"
              />
              <Button type="submit">Search</Button>
            </form>
            <CartButton farmId="global" />
          </div>
        </div>

        <div className="flex flex-col md:flex-row gap-8">
          {/* Filters Sidebar */}
          <div className="w-full md:w-1/4 space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Filters</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Categories Filter */}
                <div>
                  <h3 className="font-medium mb-2">Categories</h3>
                  <div className="space-y-2">
                    <div className="flex items-center">
                      <Button
                        variant={selectedCategory === '' ? "default" : "outline"}
                        size="sm"
                        onClick={() => handleCategoryChange('')}
                        className="w-full justify-start"
                      >
                        All Categories
                      </Button>
                    </div>
                    {categories.map((category) => (
                      <div key={category} className="flex items-center">
                        <Button
                          variant={selectedCategory === category ? "default" : "outline"}
                          size="sm"
                          onClick={() => handleCategoryChange(category)}
                          className="w-full justify-start"
                        >
                          {category}
                        </Button>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Price Range Filter */}
                <div>
                  <h3 className="font-medium mb-2">Price Range</h3>
                  <div className="space-y-4">
                    <Slider
                      defaultValue={[priceRange[0], priceRange[1]]}
                      min={0}
                      max={1000}
                      step={10}
                      onValueChange={handlePriceChange}
                      className="mt-6"
                    />
                    <div className="flex justify-between">
                      <span>${priceRange[0]}</span>
                      <span>${priceRange[1]}</span>
                    </div>
                    <Button onClick={handlePriceApply} size="sm" className="w-full">
                      Apply Price Filter
                    </Button>
                  </div>
                </div>

                {/* Farms Filter */}
                {farms.length > 0 && (
                  <div>
                    <h3 className="font-medium mb-2">Farms</h3>
                    <div className="space-y-2 max-h-60 overflow-y-auto">
                      {farms.map((farm) => (
                        <div key={farm.id} className="flex items-center">
                          <Checkbox
                            id={`farm-${farm.id}`}
                            checked={selectedFarms.includes(farm.id)}
                            onChange={(e) => 
                              handleFarmChange(farm.id, e.target.checked)
                            }
                          />
                          <Label htmlFor={`farm-${farm.id}`} className="ml-2">
                            {farm.name}
                          </Label>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Products Grid */}
          <div className="w-full md:w-3/4">
            {error ? (
              <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
                <span className="block sm:inline">{error}</span>
              </div>
            ) : loading ? (
              <div className="flex justify-center items-center h-64">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
              </div>
            ) : products.length === 0 ? (
              <div className="text-center py-12">
                <h2 className="text-xl font-semibold mb-2">No products found</h2>
                <p className="text-gray-600">
                  Try adjusting your filters or search query to find what you're looking for.
                </p>
              </div>
            ) : (
              <>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                  {products.map((product) => (
                    <Card key={product.id} className="h-full flex flex-col">
                      <CardHeader className="pb-2">
                        <div className="aspect-w-1 aspect-h-1 w-full overflow-hidden rounded-lg bg-gray-200 mb-4">
                          {product.images && product.images.length > 0 ? (
                            <img
                              src={product.images[0].file_path}
                              alt={product.name}
                              className="h-48 w-full object-cover object-center"
                            />
                          ) : (
                            <div className="h-48 w-full flex items-center justify-center bg-gray-100">
                              <span className="text-gray-400">No image</span>
                            </div>
                          )}
                        </div>
                        <CardTitle className="text-lg">{product.name}</CardTitle>
                        <p className="text-sm text-gray-500">{product.farm_name}</p>
                      </CardHeader>
                      <CardContent className="flex-grow">
                        <p className="text-xl font-bold text-primary-600">${product.price.toFixed(2)}</p>
                        <p className="text-sm text-gray-700 mt-2 line-clamp-3">
                          {product.marketplace_description || product.description}
                        </p>
                      </CardContent>
                      <CardFooter className="pt-2 flex flex-col gap-2">
                        <Link to={`/store/product/${product.id}`} className="w-full">
                          <Button 
                            variant="outline"
                            className="w-full"
                          >
                            View Details
                          </Button>
                        </Link>
                        <AddToCartButton 
                          productId={product.id}
                          farmId={String(product.farm_id)}
                          className="w-full"
                        />
                      </CardFooter>
                    </Card>
                  ))}
                </div>

                {/* Pagination */}
                {totalPages > 1 && (
                  <div className="mt-8 flex justify-center">
                    <Pagination
                      currentPage={currentPage}
                      totalPages={totalPages}
                      onPageChange={handlePageChange}
                    />
                  </div>
                )}
              </>
            )}
          </div>
        </div>
      </div>
    </Layout>
  );
};

// Export GlobalMarketplaceContent as a named export
export { GlobalMarketplaceContent };

// Export a default component that wraps GlobalMarketplaceContent with CartProvider
export default function GlobalMarketplace() {
  return (
    <CartProvider>
      <GlobalMarketplaceContent />
    </CartProvider>
  );
}