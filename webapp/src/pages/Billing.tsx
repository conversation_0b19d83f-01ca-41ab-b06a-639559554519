import { useState, useEffect, useContext } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import axios from 'axios';
import { AuthContext } from '../context/AuthContext';
import Layout from '../components/Layout';
import { API_URL } from '../config';
import { checkPermission } from '../services/rolePermissionService';
import { getAuthToken } from '../utils/storageUtils';

interface Farm {
  id: string;
  name: string;
  billing_email: string;
  billing_address: string;
  billing_city: string;
  billing_state: string;
  billing_zip_code: string;
  billing_country: string;
  payment_method_id?: string;
  stripe_customer_id?: string;
  UserFarm?: {
    role: string;
  };
}

const Billing = () => {
  const [farms, setFarms] = useState<Farm[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [billingPermissions, setBillingPermissions] = useState<{[key: string]: boolean}>({});

  const { user } = useContext(AuthContext);
  const navigate = useNavigate();

  useEffect(() => {
    // Check if user is authenticated
    if (!user) {
      navigate('/login');
      return;
    }

    const fetchFarms = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await axios.get(`${API_URL}/farms/user/${user.id}`, {
          headers: {
            Authorization: `Bearer ${getAuthToken()}`
          }
        });
        const farmsData = response.data.farms || [];
        setFarms(farmsData);

        // Check billing permissions for each farm
        const permissions: {[key: string]: boolean} = {};
        for (const farm of farmsData) {
          try {
            const hasBillingPermission = await checkPermission(farm.id, user.id, 'billing', 'view');
            permissions[farm.id] = hasBillingPermission;
          } catch (permErr) {
            console.error(`Error checking billing permissions for farm ${farm.id}:`, permErr);
            permissions[farm.id] = false;
          }
        }
        setBillingPermissions(permissions);
      } catch (err: any) {
        console.error('Error fetching farms:', err);
        setError(err.response?.data?.error || 'Failed to load farms');
      } finally {
        setLoading(false);
      }
    };

    fetchFarms();
  }, [user, navigate]);

  return (
    <Layout>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Billing Dashboard</h1>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">{error}</span>
        </div>
      )}

      {loading ? (
        <div className="p-8 text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
          <p className="mt-2 text-gray-500">Loading billing information...</p>
        </div>
      ) : farms.length === 0 ? (
        <div className="bg-white shadow rounded-lg p-8 text-center">
          <svg className="h-16 w-16 text-gray-400 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
          </svg>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No farms found</h3>
          <p className="text-gray-500 mb-6">You don't have access to any farms with billing information.</p>
          <Link
            to="/farms"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            View Farms
          </Link>
        </div>
      ) : (
        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
          {farms.map((farm) => (
            <div key={farm.id} className="bg-white shadow rounded-lg overflow-hidden hover:shadow-md transition-shadow duration-300">
              <div className="p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-2">{farm.name}</h3>
                <div className="text-sm text-gray-500 mb-4">
                  {farm.billing_email && (
                    <div className="mb-1">
                      <span className="font-medium">Billing Email:</span> {farm.billing_email}
                    </div>
                  )}
                  {farm.billing_address && (
                    <div className="mb-1">
                      <span className="font-medium">Billing Address:</span> {farm.billing_address}, {farm.billing_city}, {farm.billing_state} {farm.billing_zip_code}
                    </div>
                  )}
                  {farm.payment_method_id ? (
                    <div className="mt-2">
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        Payment Method Added
                      </span>
                    </div>
                  ) : (
                    <div className="mt-2">
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                        No Payment Method
                      </span>
                    </div>
                  )}
                </div>
                <div className="mt-4 flex justify-end space-x-2">
                  {billingPermissions[farm.id] && (
                    <Link
                      to={`/farms/${farm.id}/billing`}
                      className="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                    >
                      Manage Billing
                    </Link>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </Layout>
  );
};

export default Billing;