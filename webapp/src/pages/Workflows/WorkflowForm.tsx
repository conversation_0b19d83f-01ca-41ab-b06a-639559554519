import { useState, useEffect, useContext } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { AuthContext } from '../../context/AuthContext';
import { useFarm } from '../../context/FarmContext';
import Layout from '../../components/Layout';
import { 
  getWorkflow, 
  createWorkflow, 
  updateWorkflow, 
  WorkflowAutomation 
} from '../../services/workflowService';

interface FormData {
  farm_id: string;
  name: string;
  description: string;
  trigger_type: string;
  trigger_config: any;
  action_type: string;
  action_config: any;
  conditions: any;
  is_active: boolean;
}

const WorkflowForm = () => {
  const { workflowId } = useParams<{ workflowId: string }>();
  const navigate = useNavigate();
  const isEditMode = !!workflowId;

  const [formData, setFormData] = useState<FormData>({
    farm_id: '',
    name: '',
    description: '',
    trigger_type: 'scheduled',
    trigger_config: {},
    action_type: 'create_task',
    action_config: {},
    conditions: {},
    is_active: true
  });

  const [loading, setLoading] = useState(isEditMode);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const { user } = useContext(AuthContext);
  const { currentFarm } = useFarm();

  // Set farm_id from currentFarm
  useEffect(() => {
    if (currentFarm && !isEditMode) {
      setFormData(prev => ({ ...prev, farm_id: currentFarm.id }));
    }
  }, [currentFarm, isEditMode]);

  // Fetch workflow data if in edit mode
  useEffect(() => {
    const fetchWorkflowData = async () => {
      if (!workflowId) return;

      try {
        const { workflow } = await getWorkflow(workflowId);

        setFormData({
          farm_id: workflow.farm_id,
          name: workflow.name,
          description: workflow.description || '',
          trigger_type: workflow.trigger_type,
          trigger_config: workflow.trigger_config || {},
          action_type: workflow.action_type,
          action_config: workflow.action_config || {},
          conditions: workflow.conditions || {},
          is_active: workflow.is_active
        });
      } catch (err: any) {
        console.error('Error fetching workflow:', err);
        setError('Failed to load workflow data. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    if (isEditMode) {
      fetchWorkflowData();
    }
  }, [workflowId, isEditMode]);

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;

    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setFormData(prev => ({ ...prev, [name]: checked }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }
  };

  // Handle JSON field changes
  const handleJsonChange = (field: string, value: string) => {
    try {
      const parsedValue = value.trim() ? JSON.parse(value) : {};
      setFormData(prev => ({ ...prev, [field]: parsedValue }));
      return true;
    } catch (err) {
      return false;
    }
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSaving(true);
    setError(null);

    try {
      if (isEditMode) {
        await updateWorkflow(workflowId!, {
          name: formData.name,
          description: formData.description,
          trigger_type: formData.trigger_type,
          trigger_config: formData.trigger_config,
          action_type: formData.action_type,
          action_config: formData.action_config,
          conditions: formData.conditions,
          is_active: formData.is_active
        });
      } else {
        await createWorkflow(formData);
      }

      navigate('/workflows');
    } catch (err: any) {
      console.error('Error saving workflow:', err);
      setError('Failed to save workflow. Please try again later.');
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <Layout>
        <div className="flex justify-center items-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500"></div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">
          {isEditMode ? 'Edit Workflow' : 'Create New Workflow'}
        </h1>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">{error}</span>
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-6 bg-white shadow px-4 py-5 sm:rounded-lg sm:p-6">
        <div className="md:grid md:grid-cols-3 md:gap-6">
          <div className="md:col-span-1">
            <h3 className="text-lg font-medium leading-6 text-gray-900">Basic Information</h3>
            <p className="mt-1 text-sm text-gray-500">
              Provide basic information about this workflow automation.
            </p>
          </div>
          <div className="mt-5 md:mt-0 md:col-span-2">
            <div className="grid grid-cols-6 gap-6">
              {/* Farm information - using global farm selector */}
              <div className="col-span-6 sm:col-span-3">
                <label className="block text-sm font-medium text-gray-700">
                  Farm
                </label>
                <div className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-gray-50 text-gray-700 sm:text-sm">
                  {currentFarm ? currentFarm.name : 'Please select a farm from the header dropdown'}
                </div>
                <input type="hidden" name="farm_id" value={formData.farm_id} />
              </div>

              {/* Workflow Name */}
              <div className="col-span-6 sm:col-span-3">
                <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                  Workflow Name
                </label>
                <input
                  type="text"
                  name="name"
                  id="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  className="mt-1 focus:ring-primary-500 focus:border-primary-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
                  required
                />
              </div>

              {/* Description */}
              <div className="col-span-6">
                <label htmlFor="description" className="block text-sm font-medium text-gray-700">
                  Description
                </label>
                <textarea
                  id="description"
                  name="description"
                  rows={3}
                  value={formData.description}
                  onChange={handleInputChange}
                  className="mt-1 focus:ring-primary-500 focus:border-primary-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
                />
              </div>

              {/* Active Status */}
              <div className="col-span-6">
                <div className="flex items-center">
                  <input
                    id="is_active"
                    name="is_active"
                    type="checkbox"
                    checked={formData.is_active}
                    onChange={handleInputChange}
                    className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                  />
                  <label htmlFor="is_active" className="ml-2 block text-sm text-gray-900">
                    Active
                  </label>
                </div>
                <p className="mt-1 text-sm text-gray-500">
                  When active, this workflow will be automatically executed when trigger conditions are met.
                </p>
              </div>
            </div>
          </div>
        </div>

        <div className="md:grid md:grid-cols-3 md:gap-6">
          <div className="md:col-span-1">
            <h3 className="text-lg font-medium leading-6 text-gray-900">Trigger Configuration</h3>
            <p className="mt-1 text-sm text-gray-500">
              Define when this workflow should be triggered.
            </p>
          </div>
          <div className="mt-5 md:mt-0 md:col-span-2">
            <div className="grid grid-cols-6 gap-6">
              {/* Trigger Type */}
              <div className="col-span-6 sm:col-span-3">
                <label htmlFor="trigger_type" className="block text-sm font-medium text-gray-700">
                  Trigger Type
                </label>
                <select
                  id="trigger_type"
                  name="trigger_type"
                  value={formData.trigger_type}
                  onChange={handleInputChange}
                  className="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                  required
                >
                  <option value="scheduled">Scheduled</option>
                  <option value="task_status_change">Task Status Change</option>
                  <option value="iot_event">IoT Event</option>
                </select>
              </div>

              {/* Trigger Configuration */}
              <div className="col-span-6">
                <label htmlFor="trigger_config" className="block text-sm font-medium text-gray-700">
                  Trigger Configuration (JSON)
                </label>
                <textarea
                  id="trigger_config"
                  name="trigger_config"
                  rows={5}
                  value={JSON.stringify(formData.trigger_config, null, 2)}
                  onChange={(e) => {
                    const isValid = handleJsonChange('trigger_config', e.target.value);
                    if (!isValid) {
                      // Don't update if JSON is invalid
                    }
                  }}
                  className="mt-1 focus:ring-primary-500 focus:border-primary-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md font-mono"
                  required
                />
                <p className="mt-1 text-xs text-gray-500">
                  {formData.trigger_type === 'scheduled' && 'Example: { "frequency": "daily", "time": "09:00" }'}
                  {formData.trigger_type === 'task_status_change' && 'Example: { "from_status": "pending", "to_status": "completed" }'}
                  {formData.trigger_type === 'iot_event' && 'Example: { "device_type": "sensor", "threshold": 30 }'}
                </p>
              </div>
            </div>
          </div>
        </div>

        <div className="md:grid md:grid-cols-3 md:gap-6">
          <div className="md:col-span-1">
            <h3 className="text-lg font-medium leading-6 text-gray-900">Action Configuration</h3>
            <p className="mt-1 text-sm text-gray-500">
              Define what this workflow should do when triggered.
            </p>
          </div>
          <div className="mt-5 md:mt-0 md:col-span-2">
            <div className="grid grid-cols-6 gap-6">
              {/* Action Type */}
              <div className="col-span-6 sm:col-span-3">
                <label htmlFor="action_type" className="block text-sm font-medium text-gray-700">
                  Action Type
                </label>
                <select
                  id="action_type"
                  name="action_type"
                  value={formData.action_type}
                  onChange={handleInputChange}
                  className="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                  required
                >
                  <option value="create_task">Create Task</option>
                  <option value="update_task">Update Task</option>
                  <option value="send_notification">Send Notification</option>
                </select>
              </div>

              {/* Action Configuration */}
              <div className="col-span-6">
                <label htmlFor="action_config" className="block text-sm font-medium text-gray-700">
                  Action Configuration (JSON)
                </label>
                <textarea
                  id="action_config"
                  name="action_config"
                  rows={5}
                  value={JSON.stringify(formData.action_config, null, 2)}
                  onChange={(e) => {
                    const isValid = handleJsonChange('action_config', e.target.value);
                    if (!isValid) {
                      // Don't update if JSON is invalid
                    }
                  }}
                  className="mt-1 focus:ring-primary-500 focus:border-primary-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md font-mono"
                  required
                />
                <p className="mt-1 text-xs text-gray-500">
                  {formData.action_type === 'create_task' && 'Example: { "title": "New Task", "description": "Task created by workflow", "priority": "medium" }'}
                  {formData.action_type === 'update_task' && 'Example: { "task_id": "task-uuid", "status": "completed" }'}
                  {formData.action_type === 'send_notification' && 'Example: { "message": "Alert: Sensor threshold exceeded", "recipients": ["user-uuid"] }'}
                </p>
              </div>

              {/* Conditions (Optional) */}
              <div className="col-span-6">
                <label htmlFor="conditions" className="block text-sm font-medium text-gray-700">
                  Conditions (Optional, JSON)
                </label>
                <textarea
                  id="conditions"
                  name="conditions"
                  rows={5}
                  value={JSON.stringify(formData.conditions, null, 2)}
                  onChange={(e) => {
                    const isValid = handleJsonChange('conditions', e.target.value);
                    if (!isValid) {
                      // Don't update if JSON is invalid
                    }
                  }}
                  className="mt-1 focus:ring-primary-500 focus:border-primary-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md font-mono"
                />
                <p className="mt-1 text-xs text-gray-500">
                  Example: {`{ "field_id": "field-uuid", "only_if_temperature_above": 25 }`}
                </p>
              </div>
            </div>
          </div>
        </div>

        <div className="flex justify-end">
          <button
            type="button"
            onClick={() => navigate('/workflows')}
            className="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={saving}
            className="ml-3 inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"
          >
            {saving ? 'Saving...' : isEditMode ? 'Update Workflow' : 'Create Workflow'}
          </button>
        </div>
      </form>
    </Layout>
  );
};

export default WorkflowForm;
