import { useState, useEffect, useContext } from 'react';
import { Link } from 'react-router-dom';
import { AuthContext } from '../../context/AuthContext';
import { useFarm } from '../../context/FarmContext';
import Layout from '../../components/Layout';
import { getFarmWorkflows, WorkflowAutomation, deleteWorkflow, executeWorkflow } from '../../services/workflowService';

const WorkflowList = () => {
  const [workflows, setWorkflows] = useState<WorkflowAutomation[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [executingWorkflow, setExecutingWorkflow] = useState<string | null>(null);
  const [executionResult, setExecutionResult] = useState<any | null>(null);

  const { user } = useContext(AuthContext);
  const { selectedFarm } = useFarm();

  // Fetch workflows when selected farm changes
  useEffect(() => {
    const fetchWorkflows = async () => {
      if (!selectedFarm?.id) return;

      setLoading(true);
      setError(null);

      try {
        const data = await getFarmWorkflows(selectedFarm.id);
        setWorkflows(data);
      } catch (err: any) {
        console.error('Error fetching workflows:', err);
        setError('Failed to load workflows. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchWorkflows();
  }, [selectedFarm]);


  // Handle workflow deletion
  const handleDeleteWorkflow = async (id: string) => {
    if (!window.confirm('Are you sure you want to delete this workflow?')) {
      return;
    }

    try {
      await deleteWorkflow(id);
      setWorkflows(workflows.filter(workflow => workflow.id !== id));
    } catch (err: any) {
      console.error('Error deleting workflow:', err);
      setError('Failed to delete workflow. Please try again later.');
    }
  };

  // Handle workflow execution
  const handleExecuteWorkflow = async (id: string) => {
    setExecutingWorkflow(id);
    setExecutionResult(null);

    try {
      const result = await executeWorkflow(id);
      setExecutionResult(result);

      // Update the workflow in the list with new execution count and last executed date
      setWorkflows(workflows.map(workflow => 
        workflow.id === id 
          ? { 
              ...workflow, 
              execution_count: workflow.execution_count + 1,
              last_executed: new Date().toISOString()
            } 
          : workflow
      ));
    } catch (err: any) {
      console.error('Error executing workflow:', err);
      setError('Failed to execute workflow. Please try again later.');
    } finally {
      setExecutingWorkflow(null);
    }
  };

  // Format date for display
  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'Never';
    return new Date(dateString).toLocaleString();
  };

  // Get trigger type display name
  const getTriggerTypeDisplay = (triggerType: string) => {
    switch (triggerType) {
      case 'task_status_change':
        return 'Task Status Change';
      case 'scheduled':
        return 'Scheduled';
      case 'iot_event':
        return 'IoT Event';
      default:
        return triggerType.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ');
    }
  };

  // Get action type display name
  const getActionTypeDisplay = (actionType: string) => {
    switch (actionType) {
      case 'create_task':
        return 'Create Task';
      case 'update_task':
        return 'Update Task';
      case 'send_notification':
        return 'Send Notification';
      default:
        return actionType.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ');
    }
  };

  return (
    <Layout>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Workflow Automation</h1>
        <Link
          to="/workflows/new"
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
        >
          Create New Workflow
        </Link>
      </div>


      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">{error}</span>
        </div>
      )}

      {executionResult && (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="font-bold">Workflow executed successfully!</span>
          <p className="mt-1">Result: {executionResult.message}</p>
          {executionResult.result && (
            <pre className="mt-2 text-xs bg-green-50 p-2 rounded overflow-auto">
              {JSON.stringify(executionResult.result, null, 2)}
            </pre>
          )}
        </div>
      )}

      {loading ? (
        <div className="flex justify-center items-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500"></div>
        </div>
      ) : workflows.length === 0 ? (
        <div className="bg-white shadow rounded-lg p-6 text-center">
          <p className="text-gray-500">No workflows found. Create your first workflow to get started.</p>
          <Link
            to="/workflows/new"
            className="inline-flex items-center px-4 py-2 mt-4 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Create New Workflow
          </Link>
        </div>
      ) : (
        <div className="bg-white shadow overflow-hidden sm:rounded-md">
          <ul className="divide-y divide-gray-200">
            {workflows.map(workflow => (
              <li key={workflow.id}>
                <div className="px-4 py-4 sm:px-6">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <p className="text-sm font-medium text-primary-600 truncate">{workflow.name}</p>
                      <span className={`ml-2 px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${workflow.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                        {workflow.is_active ? 'Active' : 'Inactive'}
                      </span>
                    </div>
                    <div className="ml-2 flex-shrink-0 flex">
                      <button
                        onClick={() => handleExecuteWorkflow(workflow.id)}
                        disabled={!!executingWorkflow}
                        className="ml-2 inline-flex items-center px-2.5 py-1.5 border border-transparent text-xs font-medium rounded text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"
                      >
                        {executingWorkflow === workflow.id ? 'Executing...' : 'Execute Now'}
                      </button>
                      <Link
                        to={`/workflows/${workflow.id}/edit`}
                        className="ml-2 inline-flex items-center px-2.5 py-1.5 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                      >
                        Edit
                      </Link>
                      <button
                        onClick={() => handleDeleteWorkflow(workflow.id)}
                        className="ml-2 inline-flex items-center px-2.5 py-1.5 border border-transparent text-xs font-medium rounded text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                      >
                        Delete
                      </button>
                    </div>
                  </div>
                  <div className="mt-2 sm:flex sm:justify-between">
                    <div className="sm:flex">
                      <p className="flex items-center text-sm text-gray-500">
                        Trigger: {getTriggerTypeDisplay(workflow.trigger_type)}
                      </p>
                      <p className="mt-2 flex items-center text-sm text-gray-500 sm:mt-0 sm:ml-6">
                        Action: {getActionTypeDisplay(workflow.action_type)}
                      </p>
                    </div>
                    <div className="mt-2 flex items-center text-sm text-gray-500 sm:mt-0">
                      <p>
                        Last executed: {formatDate(workflow.last_executed)}
                      </p>
                      <p className="ml-4">
                        Executions: {workflow.execution_count}
                      </p>
                    </div>
                  </div>
                  {workflow.description && (
                    <div className="mt-2">
                      <p className="text-sm text-gray-500">{workflow.description}</p>
                    </div>
                  )}
                </div>
              </li>
            ))}
          </ul>
        </div>
      )}
    </Layout>
  );
};

export default WorkflowList;
