import { useState } from 'react';
import Layout from '../components/Layout';
import DashboardGrid from '../components/DashboardGrid';
import WidgetRenderer from '../components/WidgetRenderer';
import WidgetSelector from '../components/WidgetSelector';
import FarmSetupModal, { openFarmSetupModal } from '../components/FarmSetupModal';
import { useDashboard, Widget } from '../context/DashboardContext';

const Dashboard = () => {
  const { removeWidget } = useDashboard();
  const [editMode, setEditMode] = useState(false);
  const [showWidgetSelector, setShowWidgetSelector] = useState(false);

  // Function to render a widget
  const renderWidget = (widget: Widget) => {
    return (
      <WidgetRenderer
        widget={widget}
        editMode={editMode}
        onRemove={removeWidget}
      />
    );
  };

  return (
    <Layout>
      <div className="dashboard-page">
        {/* Dashboard actions */}
        <div className="mb-4 flex justify-end">
          {/* Add Widget button (only visible in edit mode) */}
          {editMode && (
            <button
              onClick={() => setShowWidgetSelector(true)}
              className="btn btn-primary"
            >
              Add Widget
            </button>
          )}
        </div>

        {/* Dashboard Grid */}
        <DashboardGrid
          editMode={editMode}
          onEditModeChange={setEditMode}
          renderWidget={renderWidget}
        />

        {/* Widget Selector Modal */}
        {showWidgetSelector && (
          <WidgetSelector onClose={() => setShowWidgetSelector(false)} />
        )}

        {/* Farm Setup Modal is now rendered dynamically when needed */}
      </div>
    </Layout>
  );
};

export default Dashboard;
