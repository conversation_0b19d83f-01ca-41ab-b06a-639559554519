import { useState, useContext, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { AuthContext } from '../../context/AuthContext';
import Layout from '../../components/Layout';
import { getAuthorizationUrl, getConnection } from '../../services/quickbooksService';

const QuickBooksLink = () => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [companyName, setCompanyName] = useState<string | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [connectionId, setConnectionId] = useState<string | null>(null);
  
  const { user } = useContext(AuthContext);
  const navigate = useNavigate();
  const location = useLocation();
  
  // Check if this is a callback from QuickBooks OAuth
  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const connectionIdParam = params.get('connectionId');
    
    if (connectionIdParam) {
      setSuccess(true);
      setConnectionId(connectionIdParam);
      
      // Redirect to QuickBooks dashboard after a short delay
      setTimeout(() => {
        navigate('/quickbooks/dashboard');
      }, 3000);
    }
  }, [location, navigate]);
  
  // Check if user is already connected to QuickBooks
  useEffect(() => {
    // Check if user is authenticated
    if (!user) {
      navigate('/login');
      return;
    }
    
    const checkConnection = async () => {
      try {
        setLoading(true);
        setError(null);
        
        // For now, we'll use a placeholder farm ID
        // In a real application, you would get this from the user's selected farm
        const farmId = 'placeholder-farm-id';
        
        const response = await getConnection(farmId);
        if (response.connection) {
          setIsConnected(true);
          setCompanyName(response.connection.company_name);
          setConnectionId(response.connection.id);
        }
      } catch (err: any) {
        // If the error is a 404, it means the user is not connected to QuickBooks
        if (err.response && err.response.status === 404) {
          setIsConnected(false);
        } else {
          console.error('Error checking QuickBooks connection:', err);
          setError(err.response?.data?.error || 'Failed to check QuickBooks connection');
        }
      } finally {
        setLoading(false);
      }
    };
    
    checkConnection();
  }, [user, navigate]);
  
  // Handle the connect button click
  const handleConnectQuickBooks = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // For now, we'll use a placeholder farm ID
      const farmId = 'placeholder-farm-id';
      
      // Get the authorization URL
      const response = await getAuthorizationUrl(farmId);
      
      // Redirect to QuickBooks authorization page
      window.location.href = response.authUrl;
    } catch (err: any) {
      console.error('Error getting QuickBooks authorization URL:', err);
      setError(err.response?.data?.error || 'Failed to connect to QuickBooks');
      setLoading(false);
    }
  };
  
  if (success) {
    return (
      <Layout>
        <div className="max-w-md mx-auto">
          <div className="bg-white shadow-md rounded-lg overflow-hidden">
            <div className="p-6">
              <div className="flex items-center justify-center mb-4">
                <div className="h-16 w-16 bg-green-100 rounded-full flex items-center justify-center">
                  <svg className="h-8 w-8 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                </div>
              </div>
              <h2 className="text-2xl font-bold text-center text-gray-800 mb-2">QuickBooks Connected</h2>
              <p className="text-center text-gray-600 mb-6">
                Your QuickBooks account has been successfully linked to nxtAcre.
              </p>
              <p className="text-center text-sm text-gray-500">
                Redirecting to QuickBooks dashboard...
              </p>
            </div>
          </div>
        </div>
      </Layout>
    );
  }
  
  if (isConnected) {
    return (
      <Layout>
        <div className="max-w-md mx-auto">
          <div className="bg-white shadow-md rounded-lg overflow-hidden">
            <div className="p-6">
              <h2 className="text-2xl font-bold text-gray-800 mb-4">QuickBooks Connected</h2>
              <p className="text-gray-600 mb-6">
                Your QuickBooks account for <strong>{companyName}</strong> is already connected to nxtAcre.
              </p>
              <button
                onClick={() => navigate('/quickbooks/dashboard')}
                className="w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                Go to QuickBooks Dashboard
              </button>
            </div>
          </div>
        </div>
      </Layout>
    );
  }
  
  return (
    <Layout>
      <div className="max-w-md mx-auto">
        <div className="bg-white shadow-md rounded-lg overflow-hidden">
          <div className="p-6">
            <h2 className="text-2xl font-bold text-gray-800 mb-4">Connect QuickBooks</h2>
            <p className="text-gray-600 mb-6">
              Link your QuickBooks account to automatically sync financial data
            </p>
            
            {error && (
              <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
                <span className="block sm:inline">{error}</span>
              </div>
            )}
            
            <div className="mb-6">
              <h3 className="text-lg font-medium text-gray-800 mb-2">Why connect QuickBooks?</h3>
              <ul className="list-disc pl-5 text-sm text-gray-600 space-y-1">
                <li>Automatically sync financial data</li>
                <li>Import accounts, customers, vendors, and items</li>
                <li>Track invoices and bills</li>
                <li>Get insights into your farm's finances</li>
              </ul>
            </div>
            
            <div className="mb-6">
              <h3 className="text-lg font-medium text-gray-800 mb-2">Is it secure?</h3>
              <p className="text-sm text-gray-600">
                We use QuickBooks' secure OAuth authentication. Your credentials are never stored on our servers, and all data is encrypted.
              </p>
            </div>
            
            <button
              onClick={handleConnectQuickBooks}
              disabled={loading}
              className={`w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 ${loading ? 'opacity-70 cursor-not-allowed' : ''}`}
            >
              {loading ? 'Loading...' : 'Connect QuickBooks'}
            </button>
            
            <p className="text-xs text-center text-gray-500 mt-4">
              By connecting your account, you agree to our Terms of Service and Privacy Policy
            </p>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default QuickBooksLink;