import { useState, useEffect, useContext } from 'react';
import { useNavigate } from 'react-router-dom';
import { AuthContext } from '../../context/AuthContext';
import { useFarm } from '../../context/FarmContext';
import Layout from '../../components/Layout';
import { getConnection, syncAccounts, importData } from '../../services/quickbooksService';
import { QuickBooksAccount, QuickBooksCustomer, QuickBooksVendor, QuickBooksItem, QuickBooksInvoice, QuickBooksBill } from '../../services/quickbooksService';

const QuickBooksDashboard = () => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [connection, setConnection] = useState<any>(null);
  const [syncStatus, setSyncStatus] = useState<Record<string, 'idle' | 'loading' | 'success' | 'error'>>({
    accounts: 'idle',
    customers: 'idle',
    vendors: 'idle',
    items: 'idle',
    invoices: 'idle',
    bills: 'idle',
  });
  const [data, setData] = useState<{
    accounts: QuickBooksAccount[];
    customers: QuickBooksCustomer[];
    vendors: QuickBooksVendor[];
    items: QuickBooksItem[];
    invoices: QuickBooksInvoice[];
    bills: QuickBooksBill[];
  }>({
    accounts: [],
    customers: [],
    vendors: [],
    items: [],
    invoices: [],
    bills: [],
  });

  const { user } = useContext(AuthContext);
  const { currentFarm } = useFarm();
  const navigate = useNavigate();

  // Check if user is authenticated and has a farm selected
  useEffect(() => {
    if (!user) {
      navigate('/login');
      return;
    }

    if (!currentFarm) {
      navigate('/dashboard');
      return;
    }

    // Get QuickBooks connection
    const fetchConnection = async () => {
      try {
        setLoading(true);
        setError(null);
        
        const response = await getConnection(currentFarm.id);
        if (response.connection) {
          setConnection(response.connection);
        } else {
          navigate('/quickbooks/link');
        }
      } catch (err: any) {
        console.error('Error fetching QuickBooks connection:', err);
        setError(err.response?.data?.error || 'Failed to fetch QuickBooks connection');
        
        // If the connection doesn't exist, redirect to the link page
        if (err.response && err.response.status === 404) {
          navigate('/quickbooks/link');
        }
      } finally {
        setLoading(false);
      }
    };

    fetchConnection();
  }, [user, currentFarm, navigate]);

  // Handle sync accounts
  const handleSyncAccounts = async () => {
    if (!connection) return;
    
    try {
      setSyncStatus(prev => ({ ...prev, accounts: 'loading' }));
      
      const response = await syncAccounts(connection.id);
      
      if (response.success) {
        setData(prev => ({ ...prev, accounts: response.accounts }));
        setSyncStatus(prev => ({ ...prev, accounts: 'success' }));
      }
    } catch (err) {
      console.error('Error syncing accounts:', err);
      setSyncStatus(prev => ({ ...prev, accounts: 'error' }));
    }
  };

  // Handle import data
  const handleImportData = async (dataType: 'customers' | 'vendors' | 'items' | 'invoices' | 'bills') => {
    if (!connection) return;
    
    try {
      setSyncStatus(prev => ({ ...prev, [dataType]: 'loading' }));
      
      const response = await importData(connection.id, dataType);
      
      if (response.success) {
        setData(prev => ({ ...prev, [dataType]: response.data }));
        setSyncStatus(prev => ({ ...prev, [dataType]: 'success' }));
      }
    } catch (err) {
      console.error(`Error importing ${dataType}:`, err);
      setSyncStatus(prev => ({ ...prev, [dataType]: 'error' }));
    }
  };

  if (loading) {
    return (
      <Layout>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex justify-center">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
          </div>
        </div>
      </Layout>
    );
  }

  if (error) {
    return (
      <Layout>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
            <strong className="font-bold">Error: </strong>
            <span className="block sm:inline">{error}</span>
          </div>
          <div className="mt-4">
            <button
              onClick={() => navigate('/quickbooks/link')}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              Go to QuickBooks Link Page
            </button>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="md:flex md:items-center md:justify-between mb-6">
          <div className="flex-1 min-w-0">
            <h2 className="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
              QuickBooks Dashboard
            </h2>
          </div>
          <div className="mt-4 flex md:mt-0 md:ml-4">
            <button
              onClick={() => navigate('/quickbooks/link')}
              className="ml-3 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              Manage Connection
            </button>
          </div>
        </div>

        {connection && (
          <div className="bg-white shadow overflow-hidden sm:rounded-lg mb-6">
            <div className="px-4 py-5 sm:px-6">
              <h3 className="text-lg leading-6 font-medium text-gray-900">
                QuickBooks Connection
              </h3>
              <p className="mt-1 max-w-2xl text-sm text-gray-500">
                Details about your QuickBooks connection.
              </p>
            </div>
            <div className="border-t border-gray-200 px-4 py-5 sm:p-0">
              <dl className="sm:divide-y sm:divide-gray-200">
                <div className="py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                  <dt className="text-sm font-medium text-gray-500">Company Name</dt>
                  <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                    {connection.company_name}
                  </dd>
                </div>
                <div className="py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                  <dt className="text-sm font-medium text-gray-500">Status</dt>
                  <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                    <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                      connection.status === 'active' 
                        ? 'bg-green-100 text-green-800' 
                        : connection.status === 'error' 
                          ? 'bg-red-100 text-red-800' 
                          : 'bg-yellow-100 text-yellow-800'
                    }`}>
                      {connection.status.charAt(0).toUpperCase() + connection.status.slice(1)}
                    </span>
                  </dd>
                </div>
                <div className="py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                  <dt className="text-sm font-medium text-gray-500">Last Sync</dt>
                  <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                    {connection.last_sync_at 
                      ? new Date(connection.last_sync_at).toLocaleString() 
                      : 'Never'}
                  </dd>
                </div>
              </dl>
            </div>
          </div>
        )}

        <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
          {/* Accounts */}
          <div className="bg-white shadow overflow-hidden sm:rounded-lg">
            <div className="px-4 py-5 sm:px-6 flex justify-between items-center">
              <div>
                <h3 className="text-lg leading-6 font-medium text-gray-900">
                  Accounts
                </h3>
                <p className="mt-1 max-w-2xl text-sm text-gray-500">
                  {data.accounts.length} accounts synced
                </p>
              </div>
              <button
                onClick={handleSyncAccounts}
                disabled={syncStatus.accounts === 'loading'}
                className={`inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded shadow-sm text-white ${
                  syncStatus.accounts === 'loading' 
                    ? 'bg-gray-400' 
                    : 'bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500'
                }`}
              >
                {syncStatus.accounts === 'loading' ? 'Syncing...' : 'Sync Accounts'}
              </button>
            </div>
            <div className="border-t border-gray-200">
              {data.accounts.length > 0 ? (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Name
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Type
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Balance
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {data.accounts.slice(0, 5).map((account) => (
                        <tr key={account.Id}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            {account.Name}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {account.AccountType}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            ${account.CurrentBalance.toFixed(2)}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div className="px-6 py-4 text-sm text-gray-500">
                  No accounts synced yet. Click "Sync Accounts" to import your QuickBooks accounts.
                </div>
              )}
              {data.accounts.length > 5 && (
                <div className="px-6 py-3 bg-gray-50 text-right text-sm font-medium">
                  <a href="#" className="text-primary-600 hover:text-primary-900">
                    View all {data.accounts.length} accounts
                  </a>
                </div>
              )}
            </div>
          </div>

          {/* Customers */}
          <div className="bg-white shadow overflow-hidden sm:rounded-lg">
            <div className="px-4 py-5 sm:px-6 flex justify-between items-center">
              <div>
                <h3 className="text-lg leading-6 font-medium text-gray-900">
                  Customers
                </h3>
                <p className="mt-1 max-w-2xl text-sm text-gray-500">
                  {data.customers.length} customers synced
                </p>
              </div>
              <button
                onClick={() => handleImportData('customers')}
                disabled={syncStatus.customers === 'loading'}
                className={`inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded shadow-sm text-white ${
                  syncStatus.customers === 'loading' 
                    ? 'bg-gray-400' 
                    : 'bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500'
                }`}
              >
                {syncStatus.customers === 'loading' ? 'Syncing...' : 'Sync Customers'}
              </button>
            </div>
            <div className="border-t border-gray-200">
              {data.customers.length > 0 ? (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Name
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Company
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Balance
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {data.customers.slice(0, 5).map((customer) => (
                        <tr key={customer.Id}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            {customer.DisplayName}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {customer.CompanyName || '-'}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            ${customer.Balance.toFixed(2)}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div className="px-6 py-4 text-sm text-gray-500">
                  No customers synced yet. Click "Sync Customers" to import your QuickBooks customers.
                </div>
              )}
              {data.customers.length > 5 && (
                <div className="px-6 py-3 bg-gray-50 text-right text-sm font-medium">
                  <a href="#" className="text-primary-600 hover:text-primary-900">
                    View all {data.customers.length} customers
                  </a>
                </div>
              )}
            </div>
          </div>

          {/* Vendors */}
          <div className="bg-white shadow overflow-hidden sm:rounded-lg">
            <div className="px-4 py-5 sm:px-6 flex justify-between items-center">
              <div>
                <h3 className="text-lg leading-6 font-medium text-gray-900">
                  Vendors
                </h3>
                <p className="mt-1 max-w-2xl text-sm text-gray-500">
                  {data.vendors.length} vendors synced
                </p>
              </div>
              <button
                onClick={() => handleImportData('vendors')}
                disabled={syncStatus.vendors === 'loading'}
                className={`inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded shadow-sm text-white ${
                  syncStatus.vendors === 'loading' 
                    ? 'bg-gray-400' 
                    : 'bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500'
                }`}
              >
                {syncStatus.vendors === 'loading' ? 'Syncing...' : 'Sync Vendors'}
              </button>
            </div>
            <div className="border-t border-gray-200">
              {data.vendors.length > 0 ? (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Name
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Company
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Balance
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {data.vendors.slice(0, 5).map((vendor) => (
                        <tr key={vendor.Id}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            {vendor.DisplayName}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {vendor.CompanyName || '-'}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            ${vendor.Balance.toFixed(2)}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div className="px-6 py-4 text-sm text-gray-500">
                  No vendors synced yet. Click "Sync Vendors" to import your QuickBooks vendors.
                </div>
              )}
              {data.vendors.length > 5 && (
                <div className="px-6 py-3 bg-gray-50 text-right text-sm font-medium">
                  <a href="#" className="text-primary-600 hover:text-primary-900">
                    View all {data.vendors.length} vendors
                  </a>
                </div>
              )}
            </div>
          </div>

          {/* Invoices */}
          <div className="bg-white shadow overflow-hidden sm:rounded-lg">
            <div className="px-4 py-5 sm:px-6 flex justify-between items-center">
              <div>
                <h3 className="text-lg leading-6 font-medium text-gray-900">
                  Invoices
                </h3>
                <p className="mt-1 max-w-2xl text-sm text-gray-500">
                  {data.invoices.length} invoices synced
                </p>
              </div>
              <button
                onClick={() => handleImportData('invoices')}
                disabled={syncStatus.invoices === 'loading'}
                className={`inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded shadow-sm text-white ${
                  syncStatus.invoices === 'loading' 
                    ? 'bg-gray-400' 
                    : 'bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500'
                }`}
              >
                {syncStatus.invoices === 'loading' ? 'Syncing...' : 'Sync Invoices'}
              </button>
            </div>
            <div className="border-t border-gray-200">
              {data.invoices.length > 0 ? (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Invoice #
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Customer
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Date
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Amount
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {data.invoices.slice(0, 5).map((invoice) => (
                        <tr key={invoice.Id}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            {invoice.DocNumber || '-'}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {invoice.CustomerRef.name}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {new Date(invoice.TxnDate).toLocaleDateString()}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            ${invoice.TotalAmt.toFixed(2)}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div className="px-6 py-4 text-sm text-gray-500">
                  No invoices synced yet. Click "Sync Invoices" to import your QuickBooks invoices.
                </div>
              )}
              {data.invoices.length > 5 && (
                <div className="px-6 py-3 bg-gray-50 text-right text-sm font-medium">
                  <a href="#" className="text-primary-600 hover:text-primary-900">
                    View all {data.invoices.length} invoices
                  </a>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default QuickBooksDashboard;