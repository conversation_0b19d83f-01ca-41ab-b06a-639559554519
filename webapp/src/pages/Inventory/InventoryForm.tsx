import { useState, useEffect, useContext } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import axios from 'axios';
import { AuthContext } from '../../context/AuthContext';
import { useFarm } from '../../context/FarmContext';
import Layout from '../../components/Layout';
import { API_URL } from '../../config';

interface InventoryItem {
  id?: string;
  farm_id: string;
  name: string;
  category: string;
  quantity: number;
  unit: string;
  unit_price: number | null;
  supplier: string;
  purchase_date: string;
  expiration_date: string;
  location: string;
  minimum_quantity: number | null;
  notes: string;
}

const InventoryForm = () => {
  const { inventoryId } = useParams<{ inventoryId: string }>();
  const navigate = useNavigate();
  const isEditMode = !!inventoryId;

  const [inventoryItem, setInventoryItem] = useState<InventoryItem>({
    farm_id: '',
    name: '',
    category: '',
    quantity: 0,
    unit: '',
    unit_price: null,
    supplier: '',
    purchase_date: new Date().toISOString().split('T')[0],
    expiration_date: '',
    location: '',
    minimum_quantity: null,
    notes: ''
  });

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { user } = useContext(AuthContext);
  const { currentFarm } = useFarm();

  // Console log for debugging
  useEffect(() => {
    console.log("InventoryForm rendered", { user, currentFarm, inventoryItem });
  }, [user, currentFarm, inventoryItem]);

  // Set farm_id from currentFarm
  useEffect(() => {
    if (currentFarm && !isEditMode) {
      setInventoryItem(prev => ({ ...prev, farm_id: currentFarm.id }));
    }
  }, [currentFarm, isEditMode]);

  // Fetch inventory item data if in edit mode
  useEffect(() => {
    const fetchInventoryItem = async () => {
      if (!inventoryId) return;

      setLoading(true);
      setError(null);

      try {
        const response = await axios.get(`${API_URL}/api/inventory/${inventoryId}`);

        // Format the dates for the input fields
        const formattedData = {
          ...response.data,
          purchase_date: response.data.purchase_date ? 
            new Date(response.data.purchase_date).toISOString().split('T')[0] : 
            '',
          expiration_date: response.data.expiration_date ? 
            new Date(response.data.expiration_date).toISOString().split('T')[0] : 
            ''
        };

        setInventoryItem(formattedData);
      } catch (err: any) {
        console.error('Error fetching inventory item:', err);
        setError('Failed to load inventory item data. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    if (isEditMode) {
      fetchInventoryItem();
    }
  }, [inventoryId, isEditMode]);

  // Handle form input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setInventoryItem(prev => ({ ...prev, [name]: value }));
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    setLoading(true);
    setError(null);

    try {
      // Validate required fields
      if (!currentFarm) {
        setError('Please select a farm from the header dropdown.');
        setLoading(false);
        return;
      }

      if (!inventoryItem.name || !inventoryItem.category) {
        setError('Name and category are required.');
        setLoading(false);
        return;
      }

      if (isEditMode) {
        // Update existing inventory item
        await axios.put(`${API_URL}/api/inventory/${inventoryId}`, inventoryItem);
      } else {
        // Create new inventory item
        await axios.post(`${API_URL}/api/inventory`, inventoryItem);
      }

      // Redirect to inventory list
      navigate('/inventory');
    } catch (err: any) {
      console.error('Error saving inventory item:', err);
      setError('Failed to save inventory item. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Layout>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">
          {isEditMode ? 'Edit Inventory Item' : 'Add New Inventory Item'}
        </h1>
        <Link
          to="/inventory"
          className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
        >
          Back to Inventory
        </Link>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">{error}</span>
        </div>
      )}

      <div className="bg-white shadow rounded-lg overflow-hidden">
        <form onSubmit={handleSubmit} className="p-6">
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
            {/* Farm information - using global farm selector */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Farm <span className="text-red-500">*</span>
              </label>
              <div className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-gray-50 text-gray-700 sm:text-sm">
                {currentFarm ? currentFarm.name : 'Please select a farm from the header dropdown'}
              </div>
              <input type="hidden" name="farm_id" value={inventoryItem.farm_id} />
            </div>

            {/* Item Name */}
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                Item Name <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                id="name"
                name="name"
                value={inventoryItem.name}
                onChange={handleChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                required
              />
            </div>

            {/* Category */}
            <div>
              <label htmlFor="category" className="block text-sm font-medium text-gray-700 mb-1">
                Category <span className="text-red-500">*</span>
              </label>
              <select
                id="category"
                name="category"
                value={inventoryItem.category}
                onChange={handleChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                required
              >
                <option value="" disabled>Select a category</option>
                <option value="seed">Seeds</option>
                <option value="fertilizer">Fertilizers</option>
                <option value="pesticide">Pesticides</option>
                <option value="herbicide">Herbicides</option>
                <option value="feed">Animal Feed</option>
                <option value="medicine">Animal Medicine</option>
                <option value="fuel">Fuel</option>
                <option value="equipment_parts">Equipment Parts</option>
                <option value="supplies">General Supplies</option>
                <option value="other">Other</option>
              </select>
            </div>

            {/* Quantity */}
            <div>
              <label htmlFor="quantity" className="block text-sm font-medium text-gray-700 mb-1">
                Quantity <span className="text-red-500">*</span>
              </label>
              <input
                type="number"
                id="quantity"
                name="quantity"
                value={inventoryItem.quantity}
                onChange={handleChange}
                min="0"
                step="0.01"
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                required
              />
            </div>

            {/* Unit */}
            <div>
              <label htmlFor="unit" className="block text-sm font-medium text-gray-700 mb-1">
                Unit <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                id="unit"
                name="unit"
                value={inventoryItem.unit}
                onChange={handleChange}
                placeholder="e.g., kg, lbs, bags, gallons"
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                required
              />
            </div>

            {/* Unit Price */}
            <div>
              <label htmlFor="unit_price" className="block text-sm font-medium text-gray-700 mb-1">
                Unit Price
              </label>
              <div className="relative rounded-md shadow-sm">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <span className="text-gray-500 sm:text-sm">$</span>
                </div>
                <input
                  type="number"
                  id="unit_price"
                  name="unit_price"
                  value={inventoryItem.unit_price === null ? '' : inventoryItem.unit_price}
                  onChange={handleChange}
                  step="0.01"
                  min="0"
                  className="block w-full pl-7 pr-12 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                  placeholder="0.00"
                />
                <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                  <span className="text-gray-500 sm:text-sm">USD</span>
                </div>
              </div>
            </div>

            {/* Supplier */}
            <div>
              <label htmlFor="supplier" className="block text-sm font-medium text-gray-700 mb-1">
                Supplier
              </label>
              <input
                type="text"
                id="supplier"
                name="supplier"
                value={inventoryItem.supplier}
                onChange={handleChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              />
            </div>

            {/* Purchase Date */}
            <div>
              <label htmlFor="purchase_date" className="block text-sm font-medium text-gray-700 mb-1">
                Purchase Date
              </label>
              <input
                type="date"
                id="purchase_date"
                name="purchase_date"
                value={inventoryItem.purchase_date}
                onChange={handleChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              />
            </div>

            {/* Expiration Date */}
            <div>
              <label htmlFor="expiration_date" className="block text-sm font-medium text-gray-700 mb-1">
                Expiration Date
              </label>
              <input
                type="date"
                id="expiration_date"
                name="expiration_date"
                value={inventoryItem.expiration_date}
                onChange={handleChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              />
            </div>

            {/* Storage Location */}
            <div>
              <label htmlFor="location" className="block text-sm font-medium text-gray-700 mb-1">
                Storage Location
              </label>
              <input
                type="text"
                id="location"
                name="location"
                value={inventoryItem.location}
                onChange={handleChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                placeholder="e.g., Barn A, Shed 2, Main Storage"
              />
            </div>

            {/* Minimum Quantity */}
            <div>
              <label htmlFor="minimum_quantity" className="block text-sm font-medium text-gray-700 mb-1">
                Minimum Quantity (for alerts)
              </label>
              <input
                type="number"
                id="minimum_quantity"
                name="minimum_quantity"
                value={inventoryItem.minimum_quantity === null ? '' : inventoryItem.minimum_quantity}
                onChange={handleChange}
                min="0"
                step="0.01"
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                placeholder="Minimum quantity before reorder"
              />
            </div>
          </div>

          {/* Notes */}
          <div className="mt-6">
            <label htmlFor="notes" className="block text-sm font-medium text-gray-700 mb-1">
              Notes
            </label>
            <textarea
              id="notes"
              name="notes"
              value={inventoryItem.notes}
              onChange={handleChange}
              rows={4}
              className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
            ></textarea>
          </div>

          {/* Form Actions */}
          <div className="mt-6 flex justify-end space-x-3">
            <Link
              to="/inventory"
              className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              Cancel
            </Link>
            <button
              type="submit"
              disabled={loading}
              className={`inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 ${loading ? 'opacity-70 cursor-not-allowed' : ''}`}
            >
              {loading ? 'Saving...' : 'Save Inventory Item'}
            </button>
          </div>
        </form>
      </div>
    </Layout>
  );
};

export default InventoryForm;
