import { useState, useEffect, useContext } from 'react';
import { Link, useNavigate, useParams } from 'react-router-dom';
import axios from 'axios';
import { format } from 'date-fns';
import { AuthContext } from '../../context/AuthContext';
import { useFarm } from '../../context/FarmContext';
import Layout from '../../components/Layout';
import { API_URL } from '../../config';

interface Invoice {
  id: string;
  invoice_number: string;
  issue_date: string;
  due_date: string;
  status: string;
  total_amount: number;
}

interface Customer {
  id: string;
  name: string;
  contact_name: string;
  email: string;
  phone: string;
  address: string;
  city: string;
  state: string;
  zip_code: string;
  country: string;
  tax_id: string;
  notes: string;
  created_at: string;
  updated_at: string;
  farm_id: string;
  phone_book_subscription: boolean;
  ios_phone_book_sync: boolean;
  android_phone_book_sync: boolean;
  phone_book_last_sync: string | null;
  phone_book_sync_id: string | null;
  external_phone_book_id: string | null;
  Invoices?: Invoice[];
}

const CustomerDetail = () => {
  const { customerId } = useParams<{ customerId: string }>();
  const [customer, setCustomer] = useState<Customer | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [phoneBookLoading, setPhoneBookLoading] = useState(false);
  const [phoneBookError, setPhoneBookError] = useState<string | null>(null);
  const [phoneBookSuccess, setPhoneBookSuccess] = useState<string | null>(null);
  const [showPhoneBookModal, setShowPhoneBookModal] = useState(false);
  const [phoneBookOptions, setPhoneBookOptions] = useState({
    ios: false,
    android: false
  });

  const { user } = useContext(AuthContext);
  const { currentFarm } = useFarm();
  const navigate = useNavigate();

  useEffect(() => {
    // Check if user is authenticated
    if (!user) {
      navigate('/login');
      return;
    }

    // Check if farm is selected
    if (!currentFarm) {
      setError('Please select a farm to view customer details');
      setLoading(false);
      return;
    }

    const fetchCustomer = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await axios.get(`${API_URL}/customers/${customerId}`, {
          params: {
            farmId: currentFarm.id
          }
        });
        setCustomer(response.data.customer);
      } catch (err: any) {
        console.error('Error fetching customer:', err);
        setError(err.response?.data?.error || 'Failed to load customer details');
      } finally {
        setLoading(false);
      }
    };

    fetchCustomer();
  }, [user, navigate, customerId, currentFarm]);

  const handleDelete = async () => {
    try {
      setLoading(true);
      setError(null);

      if (!currentFarm) {
        setError('Please select a farm to delete this customer');
        setLoading(false);
        return;
      }

      await axios.delete(`${API_URL}/customers/${customerId}`, {
        params: {
          farmId: currentFarm.id
        }
      });

      // Redirect to customers list
      navigate('/customers');
    } catch (err: any) {
      console.error('Error deleting customer:', err);
      setError(err.response?.data?.error || 'Failed to delete customer');
      setLoading(false);
    }
  };

  // Phone book integration functions
  const handlePhoneBookSubscribe = async () => {
    try {
      setPhoneBookLoading(true);
      setPhoneBookError(null);
      setPhoneBookSuccess(null);

      // Validate that at least one platform is selected
      if (!phoneBookOptions.ios && !phoneBookOptions.android) {
        setPhoneBookError('Please select at least one platform (iOS or Android)');
        setPhoneBookLoading(false);
        return;
      }

      const response = await axios.post(`${API_URL}/customers/${customerId}/phone-book/subscribe`, {
        ios: phoneBookOptions.ios,
        android: phoneBookOptions.android
      });

      // Update customer data with the response
      setCustomer(response.data.customer);
      setPhoneBookSuccess('Customer subscribed to phone book updates successfully');
      setShowPhoneBookModal(false);
    } catch (err: any) {
      console.error('Error subscribing to phone book:', err);
      setPhoneBookError(err.response?.data?.error || 'Failed to subscribe to phone book');
    } finally {
      setPhoneBookLoading(false);
    }
  };

  const handlePhoneBookUnsubscribe = async () => {
    try {
      setPhoneBookLoading(true);
      setPhoneBookError(null);
      setPhoneBookSuccess(null);

      const response = await axios.post(`${API_URL}/customers/${customerId}/phone-book/unsubscribe`);

      // Update customer data with the response
      setCustomer(response.data.customer);
      setPhoneBookSuccess('Customer unsubscribed from phone book updates successfully');
    } catch (err: any) {
      console.error('Error unsubscribing from phone book:', err);
      setPhoneBookError(err.response?.data?.error || 'Failed to unsubscribe from phone book');
    } finally {
      setPhoneBookLoading(false);
    }
  };

  const handlePhoneBookSync = async () => {
    try {
      setPhoneBookLoading(true);
      setPhoneBookError(null);
      setPhoneBookSuccess(null);

      const response = await axios.post(`${API_URL}/customers/${customerId}/phone-book/sync`);

      // Update customer data with the response
      setCustomer(response.data.customer);
      setPhoneBookSuccess('Customer synced with phone book successfully');
    } catch (err: any) {
      console.error('Error syncing with phone book:', err);
      setPhoneBookError(err.response?.data?.error || 'Failed to sync with phone book');
    } finally {
      setPhoneBookLoading(false);
    }
  };

  const openPhoneBookModal = () => {
    // Initialize options with current settings if customer is already subscribed
    if (customer) {
      setPhoneBookOptions({
        ios: customer.ios_phone_book_sync,
        android: customer.android_phone_book_sync
      });
    }
    setShowPhoneBookModal(true);
  };

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  return (
    <Layout>
      {loading && !customer ? (
        <div className="p-8 text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
          <p className="mt-2 text-gray-500">Loading customer details...</p>
        </div>
      ) : error ? (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">{error}</span>
        </div>
      ) : customer ? (
        <>
          <div className="flex justify-between items-center mb-6">
            <h1 className="text-2xl font-bold text-gray-900">{customer.name}</h1>
            <div className="flex space-x-2">
              <Link
                to="/customers"
                className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                Back to Customers
              </Link>
              <Link
                to={`/customers/${customer.id}/edit`}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                Edit Customer
              </Link>
              <button
                type="button"
                onClick={() => setDeleteConfirmOpen(true)}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
              >
                Delete Customer
              </button>
            </div>
          </div>

          {/* Phone Book Integration Status/Alert */}
          {phoneBookSuccess && (
            <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-6" role="alert">
              <span className="block sm:inline">{phoneBookSuccess}</span>
              <button
                type="button"
                className="absolute top-0 bottom-0 right-0 px-4 py-3"
                onClick={() => setPhoneBookSuccess(null)}
              >
                <span className="sr-only">Close</span>
                <svg className="h-6 w-6 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
          )}

          {phoneBookError && (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
              <span className="block sm:inline">{phoneBookError}</span>
              <button
                type="button"
                className="absolute top-0 bottom-0 right-0 px-4 py-3"
                onClick={() => setPhoneBookError(null)}
              >
                <span className="sr-only">Close</span>
                <svg className="h-6 w-6 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
          )}

          <div className="bg-white shadow rounded-lg overflow-hidden mb-6">
            <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
              <h2 className="text-lg font-medium text-gray-900">Customer Information</h2>
            </div>
            <div className="p-6">
              <dl className="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
                <div>
                  <dt className="text-sm font-medium text-gray-500">Customer Name</dt>
                  <dd className="mt-1 text-sm text-gray-900">{customer.name}</dd>
                </div>

                {customer.contact_name && (
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Contact Person</dt>
                    <dd className="mt-1 text-sm text-gray-900">{customer.contact_name}</dd>
                  </div>
                )}

                {customer.email && (
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Email</dt>
                    <dd className="mt-1 text-sm text-gray-900">
                      <a href={`mailto:${customer.email}`} className="text-primary-600 hover:text-primary-900">
                        {customer.email}
                      </a>
                    </dd>
                  </div>
                )}

                {customer.phone && (
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Phone</dt>
                    <dd className="mt-1 text-sm text-gray-900">
                      <a href={`tel:${customer.phone}`} className="text-primary-600 hover:text-primary-900">
                        {customer.phone}
                      </a>
                    </dd>
                  </div>
                )}

                {(customer.address || customer.city || customer.state || customer.zip_code) && (
                  <div className="sm:col-span-2">
                    <dt className="text-sm font-medium text-gray-500">Address</dt>
                    <dd className="mt-1 text-sm text-gray-900">
                      {customer.address && <div>{customer.address}</div>}
                      {(customer.city || customer.state || customer.zip_code) && (
                        <div>
                          {customer.city && `${customer.city}, `}
                          {customer.state && `${customer.state} `}
                          {customer.zip_code}
                        </div>
                      )}
                      {customer.country && <div>{customer.country}</div>}
                    </dd>
                  </div>
                )}

                {customer.tax_id && (
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Tax ID</dt>
                    <dd className="mt-1 text-sm text-gray-900">{customer.tax_id}</dd>
                  </div>
                )}

                <div>
                  <dt className="text-sm font-medium text-gray-500">Created</dt>
                  <dd className="mt-1 text-sm text-gray-900">
                    {new Date(customer.created_at).toLocaleDateString()}
                  </dd>
                </div>

                <div>
                  <dt className="text-sm font-medium text-gray-500">Last Updated</dt>
                  <dd className="mt-1 text-sm text-gray-900">
                    {new Date(customer.updated_at).toLocaleDateString()}
                  </dd>
                </div>

                {customer.notes && (
                  <div className="sm:col-span-2">
                    <dt className="text-sm font-medium text-gray-500">Notes</dt>
                    <dd className="mt-1 text-sm text-gray-900 whitespace-pre-line">{customer.notes}</dd>
                  </div>
                )}
              </dl>
            </div>
          </div>

          {/* Phone Book Integration Section */}
          <div className="bg-white shadow rounded-lg overflow-hidden mb-6">
            <div className="px-6 py-4 border-b border-gray-200 bg-gray-50 flex justify-between items-center">
              <h2 className="text-lg font-medium text-gray-900">Phone Book Integration</h2>
              {customer.phone_book_subscription ? (
                <div className="flex space-x-2">
                  <button
                    type="button"
                    onClick={handlePhoneBookSync}
                    disabled={phoneBookLoading}
                    className={`inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 ${phoneBookLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
                  >
                    {phoneBookLoading ? 'Syncing...' : 'Sync Now'}
                  </button>
                  <button
                    type="button"
                    onClick={handlePhoneBookUnsubscribe}
                    disabled={phoneBookLoading}
                    className={`inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 ${phoneBookLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
                  >
                    {phoneBookLoading ? 'Processing...' : 'Unsubscribe'}
                  </button>
                </div>
              ) : (
                <button
                  type="button"
                  onClick={openPhoneBookModal}
                  disabled={phoneBookLoading}
                  className={`inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 ${phoneBookLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
                >
                  {phoneBookLoading ? 'Processing...' : 'Subscribe to Phone Book'}
                </button>
              )}
            </div>
            <div className="p-6">
              {customer.phone_book_subscription ? (
                <dl className="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Subscription Status</dt>
                    <dd className="mt-1 text-sm text-gray-900">
                      <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                        Active
                      </span>
                    </dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Platforms</dt>
                    <dd className="mt-1 text-sm text-gray-900">
                      {customer.ios_phone_book_sync && (
                        <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800 mr-2">
                          iOS
                        </span>
                      )}
                      {customer.android_phone_book_sync && (
                        <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                          Android
                        </span>
                      )}
                    </dd>
                  </div>
                  {customer.phone_book_last_sync && (
                    <div>
                      <dt className="text-sm font-medium text-gray-500">Last Synced</dt>
                      <dd className="mt-1 text-sm text-gray-900">
                        {new Date(customer.phone_book_last_sync).toLocaleString()}
                      </dd>
                    </div>
                  )}
                  <div className="sm:col-span-2">
                    <p className="text-sm text-gray-500">
                      This customer's contact information is being synced with phone books. Any changes made to the customer's information will be automatically synced with the selected platforms.
                    </p>
                  </div>
                </dl>
              ) : (
                <p className="text-sm text-gray-500">
                  This customer is not currently subscribed to phone book integration. Subscribe to automatically sync this customer's contact information with iOS and Android phone books.
                </p>
              )}
            </div>
          </div>

          <div className="bg-white shadow rounded-lg overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-200 bg-gray-50 flex justify-between items-center">
              <h2 className="text-lg font-medium text-gray-900">Invoices</h2>
              <Link
                to={`/invoices/new?customerId=${customer.id}`}
                className="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                Create Invoice
              </Link>
            </div>
            <div className="p-6">
              {customer.Invoices && customer.Invoices.length > 0 ? (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Invoice #
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Issue Date
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Due Date
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Status
                        </th>
                        <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Amount
                        </th>
                        <th scope="col" className="relative px-6 py-3">
                          <span className="sr-only">View</span>
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {customer.Invoices.map((invoice) => (
                        <tr key={invoice.id} className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            {invoice.invoice_number}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {new Date(invoice.issue_date).toLocaleDateString()}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {new Date(invoice.due_date).toLocaleDateString()}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                              ${invoice.status === 'paid' ? 'bg-green-100 text-green-800' : 
                                invoice.status === 'overdue' ? 'bg-red-100 text-red-800' : 
                                invoice.status === 'draft' ? 'bg-gray-100 text-gray-800' : 
                                'bg-yellow-100 text-yellow-800'}`}>
                              {invoice.status.charAt(0).toUpperCase() + invoice.status.slice(1)}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-right font-medium">
                            {formatCurrency(invoice.total_amount)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <Link to={`/invoices/${invoice.id}`} className="text-primary-600 hover:text-primary-900">
                              View
                            </Link>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <p className="text-gray-500 text-center py-4">No invoices found for this customer.</p>
              )}
            </div>
          </div>

          {/* Delete Confirmation Modal */}
          {deleteConfirmOpen && (
            <div className="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
              <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
                <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>
                <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-y-auto max-h-[90vh] shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
                  <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                    <div className="sm:flex sm:items-start">
                      <div className="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                        <svg className="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                        </svg>
                      </div>
                      <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                        <h3 className="text-lg leading-6 font-medium text-gray-900" id="modal-title">
                          Delete Customer
                        </h3>
                        <div className="mt-2">
                          <p className="text-sm text-gray-500">
                            Are you sure you want to delete this customer? This action cannot be undone.
                            {customer.Invoices && customer.Invoices.length > 0 && (
                              <span className="block mt-2 text-red-600 font-semibold">
                                Warning: This customer has {customer.Invoices.length} invoice(s). Deleting this customer may affect these invoices.
                              </span>
                            )}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                    <button
                      type="button"
                      onClick={handleDelete}
                      disabled={loading}
                      className={`w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm ${loading ? 'opacity-70 cursor-not-allowed' : ''}`}
                    >
                      {loading ? 'Deleting...' : 'Delete'}
                    </button>
                    <button
                      type="button"
                      onClick={() => setDeleteConfirmOpen(false)}
                      className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                    >
                      Cancel
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Phone Book Subscription Modal */}
          {showPhoneBookModal && (
            <div className="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
              <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
                <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>
                <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-y-auto max-h-[90vh] shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
                  <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                    <div className="sm:flex sm:items-start">
                      <div className="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-primary-100 sm:mx-0 sm:h-10 sm:w-10">
                        <svg className="h-6 w-6 text-primary-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z" />
                        </svg>
                      </div>
                      <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                        <h3 className="text-lg leading-6 font-medium text-gray-900" id="modal-title">
                          Subscribe to Phone Book
                        </h3>
                        <div className="mt-2">
                          <p className="text-sm text-gray-500 mb-4">
                            Select which phone book platforms you want to sync this customer with. The customer's contact information will be automatically synced with the selected platforms.
                          </p>

                          {phoneBookError && (
                            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
                              <span className="block sm:inline">{phoneBookError}</span>
                            </div>
                          )}

                          <div className="space-y-4">
                            <div className="flex items-center">
                              <input
                                id="ios-checkbox"
                                type="checkbox"
                                checked={phoneBookOptions.ios}
                                onChange={() => setPhoneBookOptions({
                                  ...phoneBookOptions,
                                  ios: !phoneBookOptions.ios
                                })}
                                className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                              />
                              <label htmlFor="ios-checkbox" className="ml-2 block text-sm text-gray-900">
                                iOS Phone Book
                              </label>
                            </div>
                            <div className="flex items-center">
                              <input
                                id="android-checkbox"
                                type="checkbox"
                                checked={phoneBookOptions.android}
                                onChange={() => setPhoneBookOptions({
                                  ...phoneBookOptions,
                                  android: !phoneBookOptions.android
                                })}
                                className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                              />
                              <label htmlFor="android-checkbox" className="ml-2 block text-sm text-gray-900">
                                Android Phone Book
                              </label>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                    <button
                      type="button"
                      onClick={handlePhoneBookSubscribe}
                      disabled={phoneBookLoading}
                      className={`w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary-600 text-base font-medium text-white hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:ml-3 sm:w-auto sm:text-sm ${phoneBookLoading ? 'opacity-70 cursor-not-allowed' : ''}`}
                    >
                      {phoneBookLoading ? 'Subscribing...' : 'Subscribe'}
                    </button>
                    <button
                      type="button"
                      onClick={() => setShowPhoneBookModal(false)}
                      className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                    >
                      Cancel
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}
        </>
      ) : (
        <div className="bg-white shadow rounded-lg p-8 text-center">
          <p className="text-gray-500">Customer not found.</p>
          <Link
            to="/customers"
            className="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Back to Customers
          </Link>
        </div>
      )}
    </Layout>
  );
};

export default CustomerDetail;
