import { useState, useEffect, useContext } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import axios from 'axios';
import { AuthContext } from '../../context/AuthContext';
import { useFarm } from '../../context/FarmContext';
import Layout from '../../components/Layout';
import { API_URL } from '../../config';
import ProductImageManager from '../../components/products/ProductImageManager';
import ProductMarketplaceSettings from '../../components/products/ProductMarketplaceSettings';
import ProductFulfillmentOptions from '../../components/products/ProductFulfillmentOptions';

interface Product {
  id?: string;
  farm_id: string;
  name: string;
  description: string;
  sku: string;
  price: number;
  cost: number | null;
  unit: string;
  category: string;
  type: string;
  source_id: string | null;
  is_active: boolean;
  is_tax_exempt: boolean;
  is_marketplace_visible?: boolean;
  marketplace_description?: string;
  marketplace_category?: string;
  override_farm_fulfillment?: boolean;
  offers_delivery?: boolean;
  offers_pickup?: boolean;
}

interface SeedProductData {
  variety?: string;
  brand?: string;
  seed_type?: string;
  germination_rate?: number;
  treatment?: string;
  organic?: boolean;
  maturity_days?: number;
  year?: number;
  supplier?: string;
  notes?: string;
}

interface ChemicalProductData {
  chemical_type?: string;
  active_ingredients?: string;
  concentration?: string;
  application_rate?: string;
  application_method?: string;
  restricted_use?: boolean;
  safety_information?: string;
  preharvest_interval?: number;
  reentry_interval?: number;
  epa_registration?: string;
  manufacturer?: string;
  supplier?: string;
  expiration_date?: string;
  storage_requirements?: string;
  notes?: string;
}

interface SourceOption {
  id: string;
  name: string;
  type: string;
}

const ProductForm = () => {
  const { productId } = useParams<{ productId: string }>();
  const navigate = useNavigate();
  const isEditMode = !!productId;

  const [product, setProduct] = useState<Product>({
    farm_id: '',
    name: '',
    description: '',
    sku: '',
    price: 0,
    cost: null,
    unit: '',
    category: '',
    type: 'other',
    source_id: null,
    is_active: true,
    is_tax_exempt: false,
    is_marketplace_visible: false,
    marketplace_description: '',
    marketplace_category: '',
    override_farm_fulfillment: false,
    offers_delivery: true,
    offers_pickup: true
  });

  const [seedData, setSeedData] = useState<SeedProductData>({
    variety: '',
    brand: '',
    seed_type: '',
    germination_rate: undefined,
    treatment: '',
    organic: false,
    maturity_days: undefined,
    year: new Date().getFullYear(),
    supplier: '',
    notes: ''
  });

  const [chemicalData, setChemicalData] = useState<ChemicalProductData>({
    chemical_type: 'other',
    active_ingredients: '',
    concentration: '',
    application_rate: '',
    application_method: '',
    restricted_use: false,
    safety_information: '',
    preharvest_interval: undefined,
    reentry_interval: undefined,
    epa_registration: '',
    manufacturer: '',
    supplier: '',
    expiration_date: '',
    storage_requirements: '',
    notes: ''
  });

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [sourceOptions, setSourceOptions] = useState<SourceOption[]>([]);

  const { user } = useContext(AuthContext);
  const { selectedFarm } = useFarm();

  // Use selectedFarm from FarmContext
  useEffect(() => {
    if (selectedFarm && !isEditMode) {
      // Set the selected farm for new products
      setProduct(prev => ({ ...prev, farm_id: selectedFarm.id }));
      // Fetch source options for the selected farm
      fetchSourceOptions(selectedFarm.id);
    }
  }, [selectedFarm, isEditMode]);

  // Fetch product data if in edit mode
  useEffect(() => {
    const fetchProduct = async () => {
      if (!productId) return;

      setLoading(true);
      setError(null);

      try {
        const response = await axios.get(`${API_URL}/products/${productId}`);
        setProduct(response.data);

        // Fetch source options for the product's farm
        if (response.data.farm_id) {
          fetchSourceOptions(response.data.farm_id);
        }
      } catch (err: any) {
        console.error('Error fetching product:', err);
        setError('Failed to load product data. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    if (isEditMode) {
      fetchProduct();
    }
  }, [productId, isEditMode]);

  // Fetch source options (crops, livestock, equipment, seeds, chemicals)
  const fetchSourceOptions = async (farmId: string) => {
    try {
      // Fetch crops
      const cropsResponse = await axios.get(`${API_URL}/api/crops/farm/${farmId}`);
      const cropsList = Array.isArray(cropsResponse.data) ? cropsResponse.data : cropsResponse.data.crops || [];
      const cropOptions = cropsList.map(crop => ({ id: crop.id, name: crop.name, type: 'crop' }));

      // Fetch livestock
      const livestockResponse = await axios.get(`${API_URL}/api/livestock/farm/${farmId}`);
      const livestockList = Array.isArray(livestockResponse.data) ? livestockResponse.data : livestockResponse.data.livestock || [];
      const livestockOptions = livestockList.map(livestock => ({ id: livestock.id, name: livestock.type, type: 'livestock' }));

      // Fetch equipment
      const equipmentResponse = await axios.get(`${API_URL}/api/equipment/farm/${farmId}`);
      const equipmentList = Array.isArray(equipmentResponse.data) ? equipmentResponse.data : equipmentResponse.data.equipment || [];
      const equipmentOptions = equipmentList.map(equipment => ({ id: equipment.id, name: equipment.name, type: 'equipment' }));

      // Fetch seeds
      const seedsResponse = await axios.get(`${API_URL}/api/seeds/farm/${farmId}`);
      const seedsList = Array.isArray(seedsResponse.data) ? seedsResponse.data : seedsResponse.data.seeds || [];
      const seedOptions = seedsList.map(seed => ({
        id: seed.id,
        name: seed.Product ? seed.Product.name : `Seed ${seed.id}`,
        type: 'seed'
      }));

      // Fetch chemicals
      const chemicalsResponse = await axios.get(`${API_URL}/api/chemicals/farm/${farmId}`);
      const chemicalsList = Array.isArray(chemicalsResponse.data) ? chemicalsResponse.data : chemicalsResponse.data.chemicals || [];
      const chemicalOptions = chemicalsList.map(chemical => ({
        id: chemical.id,
        name: chemical.Product ? chemical.Product.name : `Chemical ${chemical.id}`,
        type: 'chemical'
      }));

      // Combine all source options
      setSourceOptions([
        ...cropOptions,
        ...livestockOptions,
        ...equipmentOptions,
        ...seedOptions,
        ...chemicalOptions
      ]);
    } catch (err: any) {
      console.error('Error fetching source options:', err);
    }
  };


  // Handle type change
  const handleTypeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const type = e.target.value;
    setProduct(prev => ({ 
      ...prev, 
      type, 
      source_id: null, // Reset source_id when type changes
      category: type === 'seed' ? 'Seeds' : type === 'chemical' ? 'Chemicals' : prev.category
    }));

    // Reset seed data if changing from seed type
    if (type !== 'seed') {
      setSeedData({
        variety: '',
        brand: '',
        seed_type: '',
        germination_rate: undefined,
        treatment: '',
        organic: false,
        maturity_days: undefined,
        year: new Date().getFullYear(),
        supplier: '',
        notes: ''
      });
    }

    // Reset chemical data if changing from chemical type
    if (type !== 'chemical') {
      setChemicalData({
        chemical_type: 'other',
        active_ingredients: '',
        concentration: '',
        application_rate: '',
        application_method: '',
        restricted_use: false,
        safety_information: '',
        preharvest_interval: undefined,
        reentry_interval: undefined,
        epa_registration: '',
        manufacturer: '',
        supplier: '',
        expiration_date: '',
        storage_requirements: '',
        notes: ''
      });
    }
  };

  // Handle form input changes for the base product
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;

    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setProduct(prev => ({ ...prev, [name]: checked }));
    } else if (name === 'source_id') {
      setProduct(prev => ({ 
        ...prev, 
        [name]: value === '' ? null : value 
      }));
    } else if (type === 'number') {
      setProduct(prev => ({ 
        ...prev, 
        [name]: value === '' ? null : Number(value) 
      }));
    } else {
      setProduct(prev => ({ ...prev, [name]: value }));
    }
  };

  // Handle form input changes for seed data
  const handleSeedDataChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;

    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setSeedData(prev => ({ ...prev, [name]: checked }));
    } else if (type === 'number') {
      setSeedData(prev => ({ 
        ...prev, 
        [name]: value === '' ? undefined : Number(value) 
      }));
    } else {
      setSeedData(prev => ({ ...prev, [name]: value }));
    }
  };

  // Handle form input changes for chemical data
  const handleChemicalDataChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;

    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setChemicalData(prev => ({ ...prev, [name]: checked }));
    } else if (type === 'number') {
      setChemicalData(prev => ({ 
        ...prev, 
        [name]: value === '' ? undefined : Number(value) 
      }));
    } else {
      setChemicalData(prev => ({ ...prev, [name]: value }));
    }
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    setLoading(true);
    setError(null);

    try {
      // Validate required fields
      if (!selectedFarm || !product.name || product.price < 0) {
        setError('Farm, name, and a non-negative price are required.');
        setLoading(false);
        return;
      }

      // Ensure farm_id is set from selectedFarm
      const productWithFarm = {
        ...product,
        farm_id: selectedFarm.id
      };

      let createdOrUpdatedProductId;

      if (productWithFarm.type === 'seed') {
        // Handle seed product
        const seedProductData = {
          ...seedData,
          farm_id: selectedFarm.id,
          name: productWithFarm.name,
          description: productWithFarm.description,
          sku: productWithFarm.sku,
          price: productWithFarm.price,
          cost: productWithFarm.cost,
          unit: productWithFarm.unit,
          category: productWithFarm.category || 'Seeds',
          is_active: productWithFarm.is_active
        };

        if (isEditMode) {
          // Update existing seed product
          await axios.put(`${API_URL}/seeds/${productId}`, seedProductData);
          createdOrUpdatedProductId = productId;
        } else {
          // Create new seed product
          const response = await axios.post(`${API_URL}/seeds`, seedProductData);
          createdOrUpdatedProductId = response.data.id;
        }
      } else if (productWithFarm.type === 'chemical') {
        // Handle chemical product
        const chemicalProductData = {
          ...chemicalData,
          farm_id: selectedFarm.id,
          name: productWithFarm.name,
          description: productWithFarm.description,
          sku: productWithFarm.sku,
          price: productWithFarm.price,
          cost: productWithFarm.cost,
          unit: productWithFarm.unit,
          category: productWithFarm.category || 'Chemicals',
          is_active: productWithFarm.is_active
        };

        if (isEditMode) {
          // Update existing chemical product
          await axios.put(`${API_URL}/chemicals/${productId}`, chemicalProductData);
          createdOrUpdatedProductId = productId;
        } else {
          // Create new chemical product
          const response = await axios.post(`${API_URL}/chemicals`, chemicalProductData);
          createdOrUpdatedProductId = response.data.id;
        }
      } else {
        // Handle regular product
        if (isEditMode) {
          // Update existing product
          await axios.put(`${API_URL}/products/${productId}`, productWithFarm);
          createdOrUpdatedProductId = productId;
        } else {
          // Create new product
          const response = await axios.post(`${API_URL}/products`, productWithFarm);
          createdOrUpdatedProductId = response.data.id;
        }
      }

      // Ask user if they want to set up inventory for this product
      if (window.confirm('Product saved successfully! Would you like to set up inventory for this product now?')) {
        // Redirect to inventory management page
        navigate(`/products/${createdOrUpdatedProductId}/inventory`);
      } else {
        // Redirect to product detail page
        navigate(`/products/${createdOrUpdatedProductId}`);
      }
    } catch (err: any) {
      console.error('Error saving product:', err);
      setError('Failed to save product. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Layout>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">
          {isEditMode ? 'Edit Product' : 'Add New Product'}
        </h1>
        <Link
          to="/products"
          className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
        >
          Back to Products
        </Link>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">{error}</span>
        </div>
      )}

      <div className="bg-white shadow rounded-lg overflow-hidden">
        <form onSubmit={handleSubmit} className="p-6">
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
            {/* Farm Selection */}
            <div>
              <label htmlFor="farm_id" className="block text-sm font-medium text-gray-700 mb-1">
                Farm <span className="text-red-500">*</span>
              </label>
              <div className="p-2 border rounded bg-gray-50">
                {selectedFarm ? (
                  <span className="text-gray-700">{selectedFarm.name}</span>
                ) : (
                  <span className="text-gray-500 italic">Please select a farm from the header dropdown</span>
                )}
              </div>
              <p className="mt-2 text-sm text-gray-500">
                To change the farm, use the farm selector in the header.
              </p>
              <input
                type="hidden"
                id="farm_id"
                name="farm_id"
                value={selectedFarm?.id || ''}
              />
            </div>

            {/* Product Name */}
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                Product Name <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                id="name"
                name="name"
                value={product.name}
                onChange={handleChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                required
              />
            </div>

            {/* SKU */}
            <div>
              <label htmlFor="sku" className="block text-sm font-medium text-gray-700 mb-1">
                SKU
              </label>
              <input
                type="text"
                id="sku"
                name="sku"
                value={product.sku}
                onChange={handleChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              />
            </div>

            {/* Product Type */}
            <div>
              <label htmlFor="type" className="block text-sm font-medium text-gray-700 mb-1">
                Type
              </label>
              <select
                id="type"
                name="type"
                value={product.type}
                onChange={handleTypeChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              >
                <option value="other">Other</option>
                <option value="crop">Crop</option>
                <option value="livestock">Livestock</option>
                <option value="equipment">Equipment</option>
                <option value="seed">Seed</option>
                <option value="chemical">Chemical</option>
              </select>
            </div>

            {/* Source */}
            {product.type !== 'other' && (
              <div>
                <label htmlFor="source_id" className="block text-sm font-medium text-gray-700 mb-1">
                  Source
                </label>
                <select
                  id="source_id"
                  name="source_id"
                  value={product.source_id || ''}
                  onChange={handleChange}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                >
                  <option value="">None</option>
                  {sourceOptions
                    .filter(option => option.type === product.type)
                    .map(option => (
                      <option key={option.id} value={option.id}>{option.name}</option>
                    ))
                  }
                </select>
              </div>
            )}

            {/* Category */}
            <div>
              <label htmlFor="category" className="block text-sm font-medium text-gray-700 mb-1">
                Category
              </label>
              <input
                type="text"
                id="category"
                name="category"
                value={product.category}
                onChange={handleChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              />
            </div>

            {/* Price */}
            <div>
              <label htmlFor="price" className="block text-sm font-medium text-gray-700 mb-1">
                Price <span className="text-red-500">*</span>
              </label>
              <div className="relative rounded-md shadow-sm">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <span className="text-gray-500 sm:text-sm">$</span>
                </div>
                <input
                  type="number"
                  id="price"
                  name="price"
                  value={product.price}
                  onChange={handleChange}
                  step="0.01"
                  min="0"
                  className="block w-full pl-7 pr-12 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                  required
                />
                <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                  <span className="text-gray-500 sm:text-sm">USD</span>
                </div>
              </div>
            </div>

            {/* Cost */}
            <div>
              <label htmlFor="cost" className="block text-sm font-medium text-gray-700 mb-1">
                Cost
              </label>
              <div className="relative rounded-md shadow-sm">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <span className="text-gray-500 sm:text-sm">$</span>
                </div>
                <input
                  type="number"
                  id="cost"
                  name="cost"
                  value={product.cost === null ? '' : product.cost}
                  onChange={handleChange}
                  step="0.01"
                  min="0"
                  className="block w-full pl-7 pr-12 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                  placeholder="0.00"
                />
                <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                  <span className="text-gray-500 sm:text-sm">USD</span>
                </div>
              </div>
            </div>

            {/* Unit */}
            <div>
              <label htmlFor="unit" className="block text-sm font-medium text-gray-700 mb-1">
                Unit
              </label>
              <input
                type="text"
                id="unit"
                name="unit"
                value={product.unit}
                onChange={handleChange}
                placeholder="e.g., lb, kg, each"
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              />
            </div>

            {/* Active Status */}
            <div className="flex items-center">
              <input
                id="is_active"
                name="is_active"
                type="checkbox"
                checked={product.is_active}
                onChange={(e) => setProduct(prev => ({ ...prev, is_active: e.target.checked }))}
                className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
              />
              <label htmlFor="is_active" className="ml-2 block text-sm text-gray-900">
                Active
              </label>
            </div>

            {/* Tax Exempt Status */}
            <div className="flex items-center">
              <input
                id="is_tax_exempt"
                name="is_tax_exempt"
                type="checkbox"
                checked={product.is_tax_exempt}
                onChange={(e) => setProduct(prev => ({ ...prev, is_tax_exempt: e.target.checked }))}
                className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
              />
              <label htmlFor="is_tax_exempt" className="ml-2 block text-sm text-gray-900">
                Tax Exempt
              </label>
              <span className="ml-2 text-xs text-gray-500">
                (Check if this product should not have taxes applied)
              </span>
            </div>
          </div>

          {/* Description */}
          <div className="mt-6">
            <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
              Description
            </label>
            <textarea
              id="description"
              name="description"
              value={product.description}
              onChange={handleChange}
              rows={4}
              className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
            ></textarea>
          </div>

          {/* Seed-specific fields */}
          {product.type === 'seed' && (
            <div className="mt-6 border-t border-gray-200 pt-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Seed Details</h3>
              <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                {/* Variety */}
                <div>
                  <label htmlFor="variety" className="block text-sm font-medium text-gray-700 mb-1">
                    Variety
                  </label>
                  <input
                    type="text"
                    id="variety"
                    name="variety"
                    value={seedData.variety}
                    onChange={handleSeedDataChange}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                  />
                </div>

                {/* Brand */}
                <div>
                  <label htmlFor="brand" className="block text-sm font-medium text-gray-700 mb-1">
                    Brand
                  </label>
                  <input
                    type="text"
                    id="brand"
                    name="brand"
                    value={seedData.brand}
                    onChange={handleSeedDataChange}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                  />
                </div>

                {/* Seed Type */}
                <div>
                  <label htmlFor="seed_type" className="block text-sm font-medium text-gray-700 mb-1">
                    Seed Type
                  </label>
                  <input
                    type="text"
                    id="seed_type"
                    name="seed_type"
                    value={seedData.seed_type}
                    onChange={handleSeedDataChange}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                  />
                </div>

                {/* Germination Rate */}
                <div>
                  <label htmlFor="germination_rate" className="block text-sm font-medium text-gray-700 mb-1">
                    Germination Rate (%)
                  </label>
                  <input
                    type="number"
                    id="germination_rate"
                    name="germination_rate"
                    value={seedData.germination_rate === undefined ? '' : seedData.germination_rate}
                    onChange={handleSeedDataChange}
                    min="0"
                    max="100"
                    step="0.1"
                    className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                  />
                </div>

                {/* Treatment */}
                <div>
                  <label htmlFor="treatment" className="block text-sm font-medium text-gray-700 mb-1">
                    Treatment
                  </label>
                  <input
                    type="text"
                    id="treatment"
                    name="treatment"
                    value={seedData.treatment}
                    onChange={handleSeedDataChange}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                  />
                </div>

                {/* Organic */}
                <div className="flex items-center">
                  <input
                    id="organic"
                    name="organic"
                    type="checkbox"
                    checked={seedData.organic}
                    onChange={handleSeedDataChange}
                    className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                  />
                  <label htmlFor="organic" className="ml-2 block text-sm text-gray-900">
                    Organic
                  </label>
                </div>

                {/* Maturity Days */}
                <div>
                  <label htmlFor="maturity_days" className="block text-sm font-medium text-gray-700 mb-1">
                    Days to Maturity
                  </label>
                  <input
                    type="number"
                    id="maturity_days"
                    name="maturity_days"
                    value={seedData.maturity_days === undefined ? '' : seedData.maturity_days}
                    onChange={handleSeedDataChange}
                    min="0"
                    className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                  />
                </div>

                {/* Year */}
                <div>
                  <label htmlFor="year" className="block text-sm font-medium text-gray-700 mb-1">
                    Year
                  </label>
                  <input
                    type="number"
                    id="year"
                    name="year"
                    value={seedData.year === undefined ? '' : seedData.year}
                    onChange={handleSeedDataChange}
                    min="1900"
                    max="2100"
                    className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                  />
                </div>

                {/* Supplier */}
                <div>
                  <label htmlFor="supplier" className="block text-sm font-medium text-gray-700 mb-1">
                    Supplier
                  </label>
                  <input
                    type="text"
                    id="supplier"
                    name="supplier"
                    value={seedData.supplier}
                    onChange={handleSeedDataChange}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                  />
                </div>
              </div>

              {/* Notes */}
              <div className="mt-4">
                <label htmlFor="seed_notes" className="block text-sm font-medium text-gray-700 mb-1">
                  Notes
                </label>
                <textarea
                  id="seed_notes"
                  name="notes"
                  value={seedData.notes}
                  onChange={handleSeedDataChange}
                  rows={3}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                ></textarea>
              </div>
            </div>
          )}

          {/* Chemical-specific fields */}
          {product.type === 'chemical' && (
            <div className="mt-6 border-t border-gray-200 pt-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Chemical Details</h3>
              <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                {/* Chemical Type */}
                <div>
                  <label htmlFor="chemical_type" className="block text-sm font-medium text-gray-700 mb-1">
                    Chemical Type
                  </label>
                  <select
                    id="chemical_type"
                    name="chemical_type"
                    value={chemicalData.chemical_type}
                    onChange={handleChemicalDataChange}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                  >
                    <option value="herbicide">Herbicide</option>
                    <option value="insecticide">Insecticide</option>
                    <option value="fungicide">Fungicide</option>
                    <option value="fertilizer">Fertilizer</option>
                    <option value="other">Other</option>
                  </select>
                </div>

                {/* Active Ingredients */}
                <div>
                  <label htmlFor="active_ingredients" className="block text-sm font-medium text-gray-700 mb-1">
                    Active Ingredients
                  </label>
                  <input
                    type="text"
                    id="active_ingredients"
                    name="active_ingredients"
                    value={chemicalData.active_ingredients}
                    onChange={handleChemicalDataChange}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                  />
                </div>

                {/* Concentration */}
                <div>
                  <label htmlFor="concentration" className="block text-sm font-medium text-gray-700 mb-1">
                    Concentration
                  </label>
                  <input
                    type="text"
                    id="concentration"
                    name="concentration"
                    value={chemicalData.concentration}
                    onChange={handleChemicalDataChange}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                  />
                </div>

                {/* Application Rate */}
                <div>
                  <label htmlFor="application_rate" className="block text-sm font-medium text-gray-700 mb-1">
                    Application Rate
                  </label>
                  <input
                    type="text"
                    id="application_rate"
                    name="application_rate"
                    value={chemicalData.application_rate}
                    onChange={handleChemicalDataChange}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                  />
                </div>

                {/* Application Method */}
                <div>
                  <label htmlFor="application_method" className="block text-sm font-medium text-gray-700 mb-1">
                    Application Method
                  </label>
                  <input
                    type="text"
                    id="application_method"
                    name="application_method"
                    value={chemicalData.application_method}
                    onChange={handleChemicalDataChange}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                  />
                </div>

                {/* Restricted Use */}
                <div className="flex items-center">
                  <input
                    id="restricted_use"
                    name="restricted_use"
                    type="checkbox"
                    checked={chemicalData.restricted_use}
                    onChange={handleChemicalDataChange}
                    className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                  />
                  <label htmlFor="restricted_use" className="ml-2 block text-sm text-gray-900">
                    Restricted Use
                  </label>
                </div>

                {/* Preharvest Interval */}
                <div>
                  <label htmlFor="preharvest_interval" className="block text-sm font-medium text-gray-700 mb-1">
                    Preharvest Interval (days)
                  </label>
                  <input
                    type="number"
                    id="preharvest_interval"
                    name="preharvest_interval"
                    value={chemicalData.preharvest_interval === undefined ? '' : chemicalData.preharvest_interval}
                    onChange={handleChemicalDataChange}
                    min="0"
                    className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                  />
                </div>

                {/* Reentry Interval */}
                <div>
                  <label htmlFor="reentry_interval" className="block text-sm font-medium text-gray-700 mb-1">
                    Reentry Interval (hours)
                  </label>
                  <input
                    type="number"
                    id="reentry_interval"
                    name="reentry_interval"
                    value={chemicalData.reentry_interval === undefined ? '' : chemicalData.reentry_interval}
                    onChange={handleChemicalDataChange}
                    min="0"
                    className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                  />
                </div>

                {/* EPA Registration */}
                <div>
                  <label htmlFor="epa_registration" className="block text-sm font-medium text-gray-700 mb-1">
                    EPA Registration
                  </label>
                  <input
                    type="text"
                    id="epa_registration"
                    name="epa_registration"
                    value={chemicalData.epa_registration}
                    onChange={handleChemicalDataChange}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                  />
                </div>

                {/* Manufacturer */}
                <div>
                  <label htmlFor="manufacturer" className="block text-sm font-medium text-gray-700 mb-1">
                    Manufacturer
                  </label>
                  <input
                    type="text"
                    id="manufacturer"
                    name="manufacturer"
                    value={chemicalData.manufacturer}
                    onChange={handleChemicalDataChange}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                  />
                </div>

                {/* Supplier */}
                <div>
                  <label htmlFor="chemical_supplier" className="block text-sm font-medium text-gray-700 mb-1">
                    Supplier
                  </label>
                  <input
                    type="text"
                    id="chemical_supplier"
                    name="supplier"
                    value={chemicalData.supplier}
                    onChange={handleChemicalDataChange}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                  />
                </div>

                {/* Expiration Date */}
                <div>
                  <label htmlFor="expiration_date" className="block text-sm font-medium text-gray-700 mb-1">
                    Expiration Date
                  </label>
                  <input
                    type="date"
                    id="expiration_date"
                    name="expiration_date"
                    value={chemicalData.expiration_date}
                    onChange={handleChemicalDataChange}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                  />
                </div>
              </div>

              {/* Storage Requirements */}
              <div className="mt-4">
                <label htmlFor="storage_requirements" className="block text-sm font-medium text-gray-700 mb-1">
                  Storage Requirements
                </label>
                <textarea
                  id="storage_requirements"
                  name="storage_requirements"
                  value={chemicalData.storage_requirements}
                  onChange={handleChemicalDataChange}
                  rows={2}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                ></textarea>
              </div>

              {/* Safety Information */}
              <div className="mt-4">
                <label htmlFor="safety_information" className="block text-sm font-medium text-gray-700 mb-1">
                  Safety Information
                </label>
                <textarea
                  id="safety_information"
                  name="safety_information"
                  value={chemicalData.safety_information}
                  onChange={handleChemicalDataChange}
                  rows={2}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                ></textarea>
              </div>

              {/* Notes */}
              <div className="mt-4">
                <label htmlFor="chemical_notes" className="block text-sm font-medium text-gray-700 mb-1">
                  Notes
                </label>
                <textarea
                  id="chemical_notes"
                  name="notes"
                  value={chemicalData.notes}
                  onChange={handleChemicalDataChange}
                  rows={2}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                ></textarea>
              </div>
            </div>
          )}

          {/* Product Images */}
          {isEditMode && product.id && (
            <div className="mt-6 border-t border-gray-200 pt-6">
              <ProductImageManager
                productId={product.id}
                readOnly={loading}
              />
            </div>
          )}

          {/* Marketplace Settings */}
          <div className="mt-6 border-t border-gray-200 pt-6">
            <ProductMarketplaceSettings
              isMarketplaceVisible={product.is_marketplace_visible || false}
              marketplaceDescription={product.marketplace_description || ''}
              marketplaceCategory={product.marketplace_category || ''}
              onVisibilityChange={(isVisible) => setProduct(prev => ({ ...prev, is_marketplace_visible: isVisible }))}
              onDescriptionChange={(description) => setProduct(prev => ({ ...prev, marketplace_description: description }))}
              onCategoryChange={(category) => setProduct(prev => ({ ...prev, marketplace_category: category }))}
              readOnly={loading}
            />
          </div>

          {/* Fulfillment Options */}
          <div className="mt-6 border-t border-gray-200 pt-6">
            <ProductFulfillmentOptions
              productId={product.id || ''}
              farmId={product.farm_id}
              readOnly={loading}
              onFulfillmentOptionsChange={(options) => setProduct(prev => ({
                ...prev,
                override_farm_fulfillment: options.override_farm_fulfillment,
                offers_delivery: options.offers_delivery,
                offers_pickup: options.offers_pickup
              }))}
            />
          </div>

          {/* Form Actions */}
          <div className="mt-6 flex justify-end space-x-3">
            <Link
              to="/products"
              className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              Cancel
            </Link>
            <button
              type="submit"
              disabled={loading}
              className={`inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 ${loading ? 'opacity-70 cursor-not-allowed' : ''}`}
            >
              {loading ? 'Saving...' : 'Save Product'}
            </button>
          </div>
        </form>
      </div>
    </Layout>
  );
};

export default ProductForm;
