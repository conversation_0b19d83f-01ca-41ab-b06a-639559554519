import { useState, useEffect } from 'react';
import { usePara<PERSON>, Link, useNavigate } from 'react-router-dom';
import axios from 'axios';
import Layout from '../../components/Layout';
import { API_URL } from '../../config';
import { getProductInventory, getAvailableProductInventory, ProductInventoryLink } from '../../services/productInventoryService';

interface Product {
  id: string;
  farm_id: string;
  name: string;
  description: string;
  sku: string;
  price: number;
  cost: number;
  unit: string;
  category: string;
  type: string;
  source_id: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

interface Farm {
  id: string;
  name: string;
}

interface Source {
  id: string;
  name: string;
  type: string;
}

const ProductDetail = () => {
  const { productId } = useParams<{ productId: string }>();
  const navigate = useNavigate();

  const [product, setProduct] = useState<Product | null>(null);
  const [farm, setFarm] = useState<Farm | null>(null);
  const [source, setSource] = useState<Source | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [productInventory, setProductInventory] = useState<ProductInventoryLink[]>([]);
  const [availableInventory, setAvailableInventory] = useState<number | null>(null);
  const [inventoryLoading, setInventoryLoading] = useState(false);

  // Fetch product data
  useEffect(() => {
    const fetchProduct = async () => {
      if (!productId) return;

      setLoading(true);
      setError(null);

      try {
        // Fetch product details
        const productResponse = await axios.get(`${API_URL}/products/${productId}`);
        setProduct(productResponse.data);

        // Fetch farm details
        const farmResponse = await axios.get(`${API_URL}/farms/${productResponse.data.farm_id}`);
        setFarm(farmResponse.data);

        // Fetch source details if applicable
        if (productResponse.data.source_id && productResponse.data.type !== 'other') {
          let sourceEndpoint = '';

          switch (productResponse.data.type) {
            case 'crop':
              sourceEndpoint = `${API_URL}/crops/${productResponse.data.source_id}`;
              break;
            case 'livestock':
              sourceEndpoint = `${API_URL}/livestock/${productResponse.data.source_id}`;
              break;
            case 'equipment':
              sourceEndpoint = `${API_URL}/equipment/${productResponse.data.source_id}`;
              break;
          }

          if (sourceEndpoint) {
            const sourceResponse = await axios.get(sourceEndpoint);

            // Format source name based on type
            let sourceName = '';
            if (productResponse.data.type === 'crop') {
              sourceName = sourceResponse.data.name + (sourceResponse.data.variety ? ` - ${sourceResponse.data.variety}` : '');
            } else if (productResponse.data.type === 'livestock') {
              sourceName = sourceResponse.data.type + (sourceResponse.data.breed ? ` - ${sourceResponse.data.breed}` : '');
            } else if (productResponse.data.type === 'equipment') {
              sourceName = sourceResponse.data.name;
            }

            setSource({
              id: sourceResponse.data.id,
              name: sourceName,
              type: productResponse.data.type
            });
          }
        }
      } catch (err: any) {
        console.error('Error fetching product details:', err);
        setError('Failed to load product details. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchProduct();
  }, [productId]);

  // Fetch product inventory data
  useEffect(() => {
    const fetchProductInventory = async () => {
      if (!productId) return;

      setInventoryLoading(true);

      try {
        // Fetch inventory items linked to this product
        const inventoryLinks = await getProductInventory(productId);
        setProductInventory(inventoryLinks);

        // Calculate available inventory
        const available = await getAvailableProductInventory(productId);
        setAvailableInventory(available);
      } catch (err) {
        console.error('Error fetching product inventory:', err);
        // Don't set error state to avoid disrupting the main view
      } finally {
        setInventoryLoading(false);
      }
    };

    if (product) {
      fetchProductInventory();
    }
  }, [productId, product]);

  // Handle product deletion
  const handleDeleteProduct = async () => {
    if (!window.confirm('Are you sure you want to delete this product?')) {
      return;
    }

    try {
      await axios.delete(`${API_URL}/products/${productId}`);
      navigate('/products');
    } catch (err: any) {
      console.error('Error deleting product:', err);
      setError('Failed to delete product. Please try again later.');
    }
  };

  // Format currency
  const formatCurrency = (amount: number | null) => {
    if (amount === null) return 'N/A';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (loading) {
    return (
      <Layout>
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
          <p className="ml-3 text-gray-500">Loading product details...</p>
        </div>
      </Layout>
    );
  }

  if (error) {
    return (
      <Layout>
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">{error}</span>
        </div>
        <div className="flex justify-center mt-4">
          <Link
            to="/products"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Back to Products
          </Link>
        </div>
      </Layout>
    );
  }

  if (!product) {
    return (
      <Layout>
        <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">Product not found.</span>
        </div>
        <div className="flex justify-center mt-4">
          <Link
            to="/products"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Back to Products
          </Link>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">{product.name}</h1>
        <div className="flex space-x-2">
          <Link
            to={`/products/${productId}/edit`}
            className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Edit
          </Link>
          <button
            onClick={handleDeleteProduct}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
          >
            Delete
          </button>
          <Link
            to="/products"
            className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Back to Products
          </Link>
        </div>
      </div>

      <div className="bg-white shadow overflow-hidden sm:rounded-lg mb-6">
        <div className="px-4 py-5 sm:px-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900">Product Details</h3>
          <p className="mt-1 max-w-2xl text-sm text-gray-500">Details and information about the product.</p>
        </div>
        <div className="border-t border-gray-200">
          <dl>
            <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Product Name</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                {product.name}
              </dd>
            </div>
            <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Farm</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                {farm ? farm.name : 'Unknown Farm'}
              </dd>
            </div>
            <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">SKU</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                {product.sku || 'Not specified'}
              </dd>
            </div>
            <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Type</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                {product.type === 'crop' ? 'Crop' : 
                 product.type === 'livestock' ? 'Livestock' : 
                 product.type === 'equipment' ? 'Equipment' : 'Other'}
              </dd>
            </div>
            {source && (
              <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt className="text-sm font-medium text-gray-500">Source</dt>
                <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                  <Link 
                    to={`/${source.type}s/${source.id}`} 
                    className="text-primary-600 hover:text-primary-900"
                  >
                    {source.name}
                  </Link>
                </dd>
              </div>
            )}
            <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Category</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                {product.category || 'Not specified'}
              </dd>
            </div>
            <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Price</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                {formatCurrency(product.price)} {product.unit ? `per ${product.unit}` : ''}
              </dd>
            </div>
            <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Cost</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                {product.cost ? formatCurrency(product.cost) : 'Not specified'}
              </dd>
            </div>
            <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Status</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                  product.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                }`}>
                  {product.is_active ? 'Active' : 'Inactive'}
                </span>
              </dd>
            </div>
            <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Description</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                {product.description || 'No description'}
              </dd>
            </div>
            <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Created</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                {formatDate(product.created_at)}
              </dd>
            </div>
            <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Last Updated</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                {formatDate(product.updated_at)}
              </dd>
            </div>
          </dl>
        </div>
      </div>

      {/* Inventory Section */}
      {product && (
        <div className="bg-white shadow overflow-hidden sm:rounded-lg mb-6">
          <div className="px-4 py-5 sm:px-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900">Inventory Information</h3>
            <p className="mt-1 max-w-2xl text-sm text-gray-500">
              Inventory items linked to this product and current stock levels.
            </p>
          </div>

          <div className="border-t border-gray-200">
            {inventoryLoading ? (
              <div className="px-4 py-5 sm:px-6 flex justify-center">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary-600"></div>
                <p className="ml-3 text-gray-500">Loading inventory data...</p>
              </div>
            ) : productInventory.length > 0 ? (
              <>
                <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                  <dt className="text-sm font-medium text-gray-500">Available Stock</dt>
                  <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                    {availableInventory !== null ? (
                      <span className={`font-semibold ${availableInventory > 10 ? 'text-green-600' : availableInventory > 0 ? 'text-yellow-600' : 'text-red-600'}`}>
                        {availableInventory} {product.unit}
                      </span>
                    ) : (
                      <span className="text-gray-500 italic">Not tracked in inventory</span>
                    )}
                  </dd>
                </div>

                <div className="px-4 py-5 sm:px-6">
                  <h4 className="text-sm font-medium text-gray-500 mb-3">Linked Inventory Items</h4>
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Item Name
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Current Stock
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Used Per Unit
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Units Producible
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {productInventory.map((link) => {
                          const inventoryItem = link.InventoryItem;
                          if (!inventoryItem) return null;

                          const unitsProducible = Math.floor(inventoryItem.quantity / link.quantity_per_unit);

                          return (
                            <tr key={link.id}>
                              <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                <Link to={`/inventory/${inventoryItem.id}`} className="text-primary-600 hover:text-primary-900">
                                  {inventoryItem.name}
                                </Link>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {inventoryItem.quantity} {inventoryItem.unit}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {link.quantity_per_unit} {inventoryItem.unit} per {product.unit}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <span className={unitsProducible > 10 ? 'text-green-600' : unitsProducible > 0 ? 'text-yellow-600' : 'text-red-600'}>
                                  {unitsProducible} {product.unit}
                                </span>
                              </td>
                            </tr>
                          );
                        })}
                      </tbody>
                    </table>
                  </div>
                </div>

                <div className="bg-gray-50 px-4 py-4 sm:px-6 flex justify-end">
                  <Link
                    to={`/products/${productId}/inventory`}
                    className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                  >
                    Manage Inventory Links
                  </Link>
                </div>
              </>
            ) : (
              <div className="px-4 py-5 sm:px-6">
                <div className="text-center py-4">
                  <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-4">
                    <div className="flex">
                      <div className="flex-shrink-0">
                        <svg className="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                          <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <div className="ml-3">
                        <p className="text-sm text-yellow-700">
                          This product is not linked to any inventory items. Without inventory links, you won't be able to track stock levels for this product.
                        </p>
                      </div>
                    </div>
                  </div>
                  <Link
                    to={`/products/${productId}/inventory`}
                    className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                  >
                    Set Up Inventory Links
                  </Link>
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </Layout>
  );
};

export default ProductDetail;
