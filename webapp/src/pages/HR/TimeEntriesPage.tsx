import { useState, useEffect, useContext } from 'react';
import { Link } from 'react-router-dom';
import axios from 'axios';
import { AuthContext } from '../../context/AuthContext';
import { useFarm } from '../../context/FarmContext';
import Layout from '../../components/Layout';
import { API_URL } from '../../config';

interface TimeEntry {
  id: string;
  employee_id: string;
  task_id?: string;
  start_time: string;
  end_time?: string;
  duration?: number;
  break_duration?: number;
  activity_type?: string;
  category?: string;
  description?: string;
  notes?: string;
  approved: boolean;
  approved_by?: string;
  approved_at?: string;
  created_at: string;
  updated_at: string;
  Employee?: {
    id: string;
    first_name: string;
    last_name: string;
  };
  Task?: {
    id: string;
    title: string;
  };
  approver?: {
    id: string;
    first_name: string;
    last_name: string;
  };
}

const TimeEntriesPage = () => {
  const [timeEntries, setTimeEntries] = useState<TimeEntry[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedEmployee, setSelectedEmployee] = useState<string>('all');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedActivityType, setSelectedActivityType] = useState<string>('all');
  const [selectedApprovalStatus, setSelectedApprovalStatus] = useState<string>('all');
  const [startDate, setStartDate] = useState<string>('');
  const [endDate, setEndDate] = useState<string>('');
  const [employees, setEmployees] = useState<{ id: string; name: string }[]>([]);
  const [categories, setCategories] = useState<string[]>([]);
  const [activityTypes, setActivityTypes] = useState<string[]>([]);

  const { user } = useContext(AuthContext);
  const { currentFarm } = useFarm();

  // Fetch employees for the dropdown
  useEffect(() => {
    const fetchEmployees = async () => {
      if (!currentFarm) return;

      try {
        const response = await axios.get(`${API_URL}/employees?farm_id=${currentFarm.id}`);
        const employeeOptions = response.data.map((emp: any) => ({
          id: emp.id,
          name: `${emp.first_name} ${emp.last_name}`
        }));
        setEmployees(employeeOptions);
      } catch (err: any) {
        console.error('Error fetching employees:', err);
      }
    };

    fetchEmployees();
  }, [currentFarm]);

  // Fetch time entries
  useEffect(() => {
    const fetchTimeEntries = async () => {
      setLoading(true);
      setError(null);

      try {
        let url = `${API_URL}/time-entries`;
        const params: Record<string, string> = {};

        // Apply filters
        if (selectedEmployee !== 'all') {
          params.employee_id = selectedEmployee;
        }

        if (selectedCategory !== 'all') {
          params.category = selectedCategory;
        }

        if (selectedActivityType !== 'all') {
          params.activity_type = selectedActivityType;
        }

        if (selectedApprovalStatus !== 'all') {
          params.approved = selectedApprovalStatus;
        }

        if (startDate) {
          params.start_date = startDate;
        }

        if (endDate) {
          params.end_date = endDate;
        }

        // Add query parameters if any filters are applied
        if (Object.keys(params).length > 0) {
          const queryString = new URLSearchParams(params).toString();
          url = `${url}?${queryString}`;
        }

        const response = await axios.get(url);
        setTimeEntries(response.data);

        // Extract unique categories and activity types for filter dropdowns
        const uniqueCategories = Array.from(
          new Set(response.data.map((entry: TimeEntry) => entry.category).filter(Boolean))
        );
        setCategories(uniqueCategories as string[]);

        const uniqueActivityTypes = Array.from(
          new Set(response.data.map((entry: TimeEntry) => entry.activity_type).filter(Boolean))
        );
        setActivityTypes(uniqueActivityTypes as string[]);
      } catch (err: any) {
        console.error('Error fetching time entries:', err);
        setError('Failed to load time entries. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchTimeEntries();
  }, [currentFarm, selectedEmployee, selectedCategory, selectedActivityType, selectedApprovalStatus, startDate, endDate]);

  // Handle filter changes
  const handleEmployeeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedEmployee(e.target.value);
  };

  const handleCategoryChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedCategory(e.target.value);
  };

  const handleActivityTypeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedActivityType(e.target.value);
  };

  const handleApprovalStatusChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedApprovalStatus(e.target.value);
  };

  const handleStartDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setStartDate(e.target.value);
  };

  const handleEndDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setEndDate(e.target.value);
  };

  // Format date for display
  const formatDate = (dateString: string | null | undefined) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString();
  };

  // Format time for display
  const formatTime = (dateString: string | null | undefined) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  // Format duration for display
  const formatDuration = (hours: number | null | undefined) => {
    if (hours === null || hours === undefined) return 'N/A';

    const wholeHours = Math.floor(hours);
    const minutes = Math.round((hours - wholeHours) * 60);

    if (wholeHours === 0) {
      return `${minutes}m`;
    } else if (minutes === 0) {
      return `${wholeHours}h`;
    } else {
      return `${wholeHours}h ${minutes}m`;
    }
  };

  // Handle approve/reject
  const handleApprove = async (timeEntryId: string, approved: boolean) => {
    try {
      await axios.put(`${API_URL}/time-entries/${timeEntryId}/approve`, {
        approved,
        approved_by: user?.id
      });

      // Refresh the list
      const updatedEntries = timeEntries.map(entry => 
        entry.id === timeEntryId 
          ? { ...entry, approved, approved_by: user?.id, approved_at: new Date().toISOString() } 
          : entry
      );
      setTimeEntries(updatedEntries);
    } catch (err: any) {
      console.error('Error approving time entry:', err);
      alert('Failed to update approval status. Please try again.');
    }
  };

  return (
    <Layout>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Time Entries</h1>
        <Link
          to="/hr/time-entries/new"
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
        >
          Add New Time Entry
        </Link>
      </div>

      {/* Filters */}
      <div className="bg-white shadow rounded-lg mb-6">
        <div className="px-4 py-5 sm:p-6">
          <h2 className="text-lg font-medium text-gray-900 mb-4">Filters</h2>
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
            {/* Employee Filter */}
            <div>
              <label htmlFor="employee-filter" className="block text-sm font-medium text-gray-700 mb-1">
                Employee
              </label>
              <select
                id="employee-filter"
                value={selectedEmployee}
                onChange={handleEmployeeChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              >
                <option value="all">All Employees</option>
                {employees.map(emp => (
                  <option key={emp.id} value={emp.id}>{emp.name}</option>
                ))}
              </select>
            </div>

            {/* Category Filter */}
            <div>
              <label htmlFor="category-filter" className="block text-sm font-medium text-gray-700 mb-1">
                Category
              </label>
              <select
                id="category-filter"
                value={selectedCategory}
                onChange={handleCategoryChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              >
                <option value="all">All Categories</option>
                {categories.map(category => (
                  <option key={category} value={category}>{category}</option>
                ))}
              </select>
            </div>

            {/* Activity Type Filter */}
            <div>
              <label htmlFor="activity-type-filter" className="block text-sm font-medium text-gray-700 mb-1">
                Activity Type
              </label>
              <select
                id="activity-type-filter"
                value={selectedActivityType}
                onChange={handleActivityTypeChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              >
                <option value="all">All Activity Types</option>
                {activityTypes.map(type => (
                  <option key={type} value={type}>{type}</option>
                ))}
              </select>
            </div>

            {/* Approval Status Filter */}
            <div>
              <label htmlFor="approval-status-filter" className="block text-sm font-medium text-gray-700 mb-1">
                Approval Status
              </label>
              <select
                id="approval-status-filter"
                value={selectedApprovalStatus}
                onChange={handleApprovalStatusChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              >
                <option value="all">All Statuses</option>
                <option value="true">Approved</option>
                <option value="false">Pending</option>
              </select>
            </div>

            {/* Date Range Filters */}
            <div>
              <label htmlFor="start-date-filter" className="block text-sm font-medium text-gray-700 mb-1">
                Start Date
              </label>
              <input
                type="date"
                id="start-date-filter"
                value={startDate}
                onChange={handleStartDateChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              />
            </div>

            <div>
              <label htmlFor="end-date-filter" className="block text-sm font-medium text-gray-700 mb-1">
                End Date
              </label>
              <input
                type="date"
                id="end-date-filter"
                value={endDate}
                onChange={handleEndDateChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              />
            </div>
          </div>
        </div>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">{error}</span>
        </div>
      )}

      {loading ? (
        <div className="flex justify-center items-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500"></div>
        </div>
      ) : timeEntries.length === 0 ? (
        <div className="bg-white shadow rounded-lg p-6 text-center">
          <p className="text-gray-500">No time entries found. Add your first time entry to get started.</p>
          <Link
            to="/hr/time-entries/new"
            className="inline-flex items-center px-4 py-2 mt-4 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Add New Time Entry
          </Link>
        </div>
      ) : (
        <div className="bg-white shadow overflow-hidden sm:rounded-md">
          <ul className="divide-y divide-gray-200">
            {timeEntries.map(entry => (
              <li key={entry.id}>
                <div className="px-4 py-4 sm:px-6 hover:bg-gray-50">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <p className="text-sm font-medium text-primary-600 truncate">
                        {entry.Employee ? `${entry.Employee.first_name} ${entry.Employee.last_name}` : 'Unknown Employee'}
                      </p>
                      <span className={`ml-2 px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${entry.approved ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}`}>
                        {entry.approved ? 'Approved' : 'Pending'}
                      </span>
                    </div>
                    <div className="ml-2 flex-shrink-0 flex">
                      {!entry.approved && (
                        <>
                          <button
                            onClick={() => handleApprove(entry.id, true)}
                            className="ml-2 inline-flex items-center px-2.5 py-1.5 border border-transparent text-xs font-medium rounded text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                          >
                            Approve
                          </button>
                          <button
                            onClick={() => handleApprove(entry.id, false)}
                            className="ml-2 inline-flex items-center px-2.5 py-1.5 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                          >
                            Reject
                          </button>
                        </>
                      )}
                      <Link
                        to={`/hr/time-entries/${entry.id}/edit`}
                        className="ml-2 inline-flex items-center px-2.5 py-1.5 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                      >
                        Edit
                      </Link>
                    </div>
                  </div>
                  <div className="mt-2 sm:flex sm:justify-between">
                    <div className="sm:flex">
                      <p className="flex items-center text-sm text-gray-500">
                        <span className="truncate">{formatDate(entry.start_time)}</span>
                        <span className="mx-1">•</span>
                        <span>{formatTime(entry.start_time)} - {entry.end_time ? formatTime(entry.end_time) : 'In Progress'}</span>
                      </p>
                      <p className="mt-2 flex items-center text-sm text-gray-500 sm:mt-0 sm:ml-6">
                        Duration: {formatDuration(entry.duration)}
                      </p>
                      {entry.category && (
                        <p className="mt-2 flex items-center text-sm text-gray-500 sm:mt-0 sm:ml-6">
                          Category: {entry.category}
                        </p>
                      )}
                    </div>
                    <div className="mt-2 flex items-center text-sm text-gray-500 sm:mt-0">
                      {entry.Task && (
                        <p>Task: {entry.Task.title}</p>
                      )}
                    </div>
                  </div>
                  {entry.description && (
                    <div className="mt-2">
                      <p className="text-sm text-gray-500">{entry.description}</p>
                    </div>
                  )}
                </div>
              </li>
            ))}
          </ul>
        </div>
      )}
    </Layout>
  );
};

export default TimeEntriesPage;
