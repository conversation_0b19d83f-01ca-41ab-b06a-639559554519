import { useState, useEffect, useContext } from 'react';
import { Link } from 'react-router-dom';
import axios from 'axios';
import { AuthContext } from '../../context/AuthContext';
import { useFarm } from '../../context/FarmContext';
import Layout from '../../components/Layout';
import { API_URL } from '../../config';

interface Expense {
  id: string;
  employee_id: string;
  expense_date: string;
  category: string;
  amount: number;
  description: string;
  receipt_url?: string;
  status: string;
  payment_method?: string;
  payment_reference?: string;
  notes?: string;
  reviewed_by?: string;
  reviewed_at?: string;
  review_notes?: string;
  reimbursed_date?: string;
  reimbursed_amount?: number;
  created_at: string;
  updated_at: string;
  Employee?: {
    id: string;
    first_name: string;
    last_name: string;
  };
  reviewer?: {
    id: string;
    first_name: string;
    last_name: string;
  };
}

const ExpensesPage = () => {
  const [expenses, setExpenses] = useState<Expense[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedEmployee, setSelectedEmployee] = useState<string>('all');
  const [selectedStatus, setSelectedStatus] = useState<string>('all');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [startDate, setStartDate] = useState<string>('');
  const [endDate, setEndDate] = useState<string>('');
  const [employees, setEmployees] = useState<{ id: string; name: string }[]>([]);
  const [categories, setCategories] = useState<string[]>([
    'travel',
    'meals',
    'supplies',
    'equipment',
    'software',
    'other'
  ]);

  const { user } = useContext(AuthContext);
  const { currentFarm } = useFarm();

  // Fetch employees for the dropdown
  useEffect(() => {
    const fetchEmployees = async () => {
      if (!currentFarm) return;

      try {
        const response = await axios.get(`${API_URL}/employees?farm_id=${currentFarm.id}`);
        const employeeOptions = response.data.map((emp: any) => ({
          id: emp.id,
          name: `${emp.first_name} ${emp.last_name}`
        }));
        setEmployees(employeeOptions);

        // If the user is an employee, pre-select them
        if (user) {
          const userEmployee = response.data.find((emp: any) => emp.email === user.email);
          if (userEmployee) {
            setSelectedEmployee(userEmployee.id);
          }
        }
      } catch (err: any) {
        console.error('Error fetching employees:', err);
      }
    };

    fetchEmployees();
  }, [currentFarm, user]);

  // Fetch expenses
  useEffect(() => {
    const fetchExpenses = async () => {
      setLoading(true);
      setError(null);

      try {
        let url = `${API_URL}/expenses`;
        const params: Record<string, string> = {};

        // Apply filters
        if (selectedEmployee !== 'all') {
          params.employee_id = selectedEmployee;
        }

        if (selectedStatus !== 'all') {
          params.status = selectedStatus;
        }

        if (selectedCategory !== 'all') {
          params.category = selectedCategory;
        }

        if (startDate) {
          params.start_date = startDate;
        }

        if (endDate) {
          params.end_date = endDate;
        }

        // Add query parameters if any filters are applied
        if (Object.keys(params).length > 0) {
          const queryString = new URLSearchParams(params).toString();
          url = `${url}?${queryString}`;
        }

        const response = await axios.get(url);
        setExpenses(response.data);

        // Extract unique categories for filter dropdown
        const uniqueCategories = Array.from(
          new Set(response.data.map((expense: Expense) => expense.category).filter(Boolean))
        );
        if (uniqueCategories.length > 0) {
          setCategories(uniqueCategories as string[]);
        }
      } catch (err: any) {
        console.error('Error fetching expenses:', err);
        setError('Failed to load expenses. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchExpenses();
  }, [currentFarm, selectedEmployee, selectedStatus, selectedCategory, startDate, endDate]);

  // Handle filter changes
  const handleEmployeeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedEmployee(e.target.value);
  };

  const handleStatusChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedStatus(e.target.value);
  };

  const handleCategoryChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedCategory(e.target.value);
  };

  const handleStartDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setStartDate(e.target.value);
  };

  const handleEndDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setEndDate(e.target.value);
  };

  // Format date for display
  const formatDate = (dateString: string | null | undefined) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString();
  };

  // Format currency for display
  const formatCurrency = (amount: number | null | undefined) => {
    if (amount === null || amount === undefined) return 'N/A';
    return new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(amount);
  };

  // Get status badge class
  const getStatusBadgeClass = (status: string) => {
    switch (status) {
      case 'approved':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'denied':
        return 'bg-red-100 text-red-800';
      case 'reimbursed':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Handle review expense
  const handleReviewExpense = async (expenseId: string, status: string) => {
    try {
      await axios.put(`${API_URL}/expenses/${expenseId}/review`, {
        status,
        reviewed_by: user?.id,
        review_notes: '',
        reimbursed_date: status === 'reimbursed' ? new Date().toISOString() : undefined
      });

      // Refresh the list
      const updatedExpenses = expenses.map(expense => 
        expense.id === expenseId 
          ? { 
              ...expense, 
              status, 
              reviewed_by: user?.id, 
              reviewed_at: new Date().toISOString(),
              reimbursed_date: status === 'reimbursed' ? new Date().toISOString() : expense.reimbursed_date
            } 
          : expense
      );
      setExpenses(updatedExpenses);
    } catch (err: any) {
      console.error('Error reviewing expense:', err);
      alert('Failed to update expense status. Please try again.');
    }
  };

  return (
    <Layout>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Expenses</h1>
        <Link
          to="/hr/expenses/new"
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
        >
          Submit Expense
        </Link>
      </div>

      {/* Filters */}
      <div className="bg-white shadow rounded-lg mb-6">
        <div className="px-4 py-5 sm:p-6">
          <h2 className="text-lg font-medium text-gray-900 mb-4">Filters</h2>
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5">
            {/* Employee Filter */}
            <div>
              <label htmlFor="employee-filter" className="block text-sm font-medium text-gray-700 mb-1">
                Employee
              </label>
              <select
                id="employee-filter"
                value={selectedEmployee}
                onChange={handleEmployeeChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              >
                <option value="all">All Employees</option>
                {employees.map(emp => (
                  <option key={emp.id} value={emp.id}>{emp.name}</option>
                ))}
              </select>
            </div>

            {/* Status Filter */}
            <div>
              <label htmlFor="status-filter" className="block text-sm font-medium text-gray-700 mb-1">
                Status
              </label>
              <select
                id="status-filter"
                value={selectedStatus}
                onChange={handleStatusChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              >
                <option value="all">All Statuses</option>
                <option value="pending">Pending</option>
                <option value="approved">Approved</option>
                <option value="denied">Denied</option>
                <option value="reimbursed">Reimbursed</option>
              </select>
            </div>

            {/* Category Filter */}
            <div>
              <label htmlFor="category-filter" className="block text-sm font-medium text-gray-700 mb-1">
                Category
              </label>
              <select
                id="category-filter"
                value={selectedCategory}
                onChange={handleCategoryChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              >
                <option value="all">All Categories</option>
                {categories.map(category => (
                  <option key={category} value={category}>{category.charAt(0).toUpperCase() + category.slice(1)}</option>
                ))}
              </select>
            </div>

            {/* Date Range Filters */}
            <div>
              <label htmlFor="start-date-filter" className="block text-sm font-medium text-gray-700 mb-1">
                Start Date
              </label>
              <input
                type="date"
                id="start-date-filter"
                value={startDate}
                onChange={handleStartDateChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              />
            </div>

            <div>
              <label htmlFor="end-date-filter" className="block text-sm font-medium text-gray-700 mb-1">
                End Date
              </label>
              <input
                type="date"
                id="end-date-filter"
                value={endDate}
                onChange={handleEndDateChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              />
            </div>
          </div>
        </div>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">{error}</span>
        </div>
      )}

      {loading ? (
        <div className="flex justify-center items-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500"></div>
        </div>
      ) : expenses.length === 0 ? (
        <div className="bg-white shadow rounded-lg p-6 text-center">
          <p className="text-gray-500">No expenses found. Submit your first expense to get started.</p>
          <Link
            to="/hr/expenses/new"
            className="inline-flex items-center px-4 py-2 mt-4 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Submit Expense
          </Link>
        </div>
      ) : (
        <div className="bg-white shadow overflow-hidden sm:rounded-md">
          <ul className="divide-y divide-gray-200">
            {expenses.map(expense => (
              <li key={expense.id}>
                <div className="px-4 py-4 sm:px-6 hover:bg-gray-50">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <p className="text-sm font-medium text-primary-600 truncate">
                        {expense.Employee ? `${expense.Employee.first_name} ${expense.Employee.last_name}` : 'Unknown Employee'}
                      </p>
                      <span className={`ml-2 px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusBadgeClass(expense.status)}`}>
                        {expense.status.charAt(0).toUpperCase() + expense.status.slice(1)}
                      </span>
                    </div>
                    <div className="ml-2 flex-shrink-0 flex">
                      {expense.status === 'pending' && (
                        <>
                          <button
                            onClick={() => handleReviewExpense(expense.id, 'approved')}
                            className="ml-2 inline-flex items-center px-2.5 py-1.5 border border-transparent text-xs font-medium rounded text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                          >
                            Approve
                          </button>
                          <button
                            onClick={() => handleReviewExpense(expense.id, 'denied')}
                            className="ml-2 inline-flex items-center px-2.5 py-1.5 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                          >
                            Deny
                          </button>
                        </>
                      )}
                      {expense.status === 'approved' && (
                        <button
                          onClick={() => handleReviewExpense(expense.id, 'reimbursed')}
                          className="ml-2 inline-flex items-center px-2.5 py-1.5 border border-transparent text-xs font-medium rounded text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                        >
                          Mark Reimbursed
                        </button>
                      )}
                      <Link
                        to={`/hr/expenses/${expense.id}/edit`}
                        className="ml-2 inline-flex items-center px-2.5 py-1.5 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                      >
                        Edit
                      </Link>
                    </div>
                  </div>
                  <div className="mt-2 sm:flex sm:justify-between">
                    <div className="sm:flex">
                      <p className="flex items-center text-sm text-gray-500">
                        <span className="truncate">{expense.category.charAt(0).toUpperCase() + expense.category.slice(1)}</span>
                      </p>
                      <p className="mt-2 flex items-center text-sm text-gray-500 sm:mt-0 sm:ml-6">
                        Date: {formatDate(expense.expense_date)}
                      </p>
                      {expense.payment_method && (
                        <p className="mt-2 flex items-center text-sm text-gray-500 sm:mt-0 sm:ml-6">
                          Payment Method: {expense.payment_method}
                        </p>
                      )}
                    </div>
                    <div className="mt-2 flex items-center text-sm text-gray-500 sm:mt-0">
                      <p className="font-semibold">
                        Amount: {formatCurrency(expense.amount)}
                      </p>
                    </div>
                  </div>
                  <div className="mt-2">
                    <p className="text-sm text-gray-500">{expense.description}</p>
                  </div>
                  {expense.receipt_url && (
                    <div className="mt-2">
                      <a 
                        href={expense.receipt_url} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="text-sm text-primary-600 hover:text-primary-500"
                      >
                        View Receipt
                      </a>
                    </div>
                  )}
                </div>
              </li>
            ))}
          </ul>
        </div>
      )}
    </Layout>
  );
};

export default ExpensesPage;
