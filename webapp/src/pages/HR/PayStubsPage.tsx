import { useState, useEffect, useContext } from 'react';
import { Link } from 'react-router-dom';
import axios from 'axios';
import { AuthContext } from '../../context/AuthContext';
import { useFarm } from '../../context/FarmContext';
import Layout from '../../components/Layout';
import { API_URL } from '../../config';

interface PayStub {
  id: string;
  employee_id: string;
  pay_period_start: string;
  pay_period_end: string;
  payment_date: string;
  gross_pay: number;
  net_pay: number;
  regular_hours?: number;
  overtime_hours?: number;
  regular_pay?: number;
  overtime_pay?: number;
  deductions?: any;
  taxes?: any;
  benefits?: any;
  year_to_date?: any;
  document_url?: string;
  notes?: string;
  status: string;
  created_at: string;
  updated_at: string;
  Employee?: {
    id: string;
    first_name: string;
    last_name: string;
  };
}

const PayStubsPage = () => {
  const [payStubs, setPayStubs] = useState<PayStub[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedEmployee, setSelectedEmployee] = useState<string>('all');
  const [selectedYear, setSelectedYear] = useState<string>(new Date().getFullYear().toString());
  const [selectedMonth, setSelectedMonth] = useState<string>('all');
  const [employees, setEmployees] = useState<{ id: string; name: string }[]>([]);
  const [years] = useState<string[]>(() => {
    const currentYear = new Date().getFullYear();
    return Array.from({ length: 5 }, (_, i) => (currentYear - i).toString());
  });
  const [months] = useState<{ value: string; label: string }[]>([
    { value: '1', label: 'January' },
    { value: '2', label: 'February' },
    { value: '3', label: 'March' },
    { value: '4', label: 'April' },
    { value: '5', label: 'May' },
    { value: '6', label: 'June' },
    { value: '7', label: 'July' },
    { value: '8', label: 'August' },
    { value: '9', label: 'September' },
    { value: '10', label: 'October' },
    { value: '11', label: 'November' },
    { value: '12', label: 'December' }
  ]);

  const { user } = useContext(AuthContext);
  const { currentFarm } = useFarm();

  // Fetch employees for the dropdown
  useEffect(() => {
    const fetchEmployees = async () => {
      if (!currentFarm) return;

      try {
        const response = await axios.get(`${API_URL}/employees?farm_id=${currentFarm.id}`);
        const employeeOptions = response.data.map((emp: any) => ({
          id: emp.id,
          name: `${emp.first_name} ${emp.last_name}`
        }));
        setEmployees(employeeOptions);

        // If the user is an employee, pre-select them
        if (user) {
          const userEmployee = response.data.find((emp: any) => emp.email === user.email);
          if (userEmployee) {
            setSelectedEmployee(userEmployee.id);
          }
        }
      } catch (err: any) {
        console.error('Error fetching employees:', err);
      }
    };

    fetchEmployees();
  }, [currentFarm, user]);

  // Fetch pay stubs
  useEffect(() => {
    const fetchPayStubs = async () => {
      setLoading(true);
      setError(null);

      try {
        let url = `${API_URL}/pay-stubs`;
        const params: Record<string, string> = {};

        // Apply filters
        if (selectedEmployee !== 'all') {
          params.employee_id = selectedEmployee;
        }

        if (selectedYear !== 'all') {
          params.year = selectedYear;
        }

        if (selectedMonth !== 'all') {
          params.month = selectedMonth;
        }

        // Add query parameters if any filters are applied
        if (Object.keys(params).length > 0) {
          const queryString = new URLSearchParams(params).toString();
          url = `${url}?${queryString}`;
        }

        const response = await axios.get(url);
        setPayStubs(response.data);
      } catch (err: any) {
        console.error('Error fetching pay stubs:', err);
        setError('Failed to load pay stubs. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchPayStubs();
  }, [currentFarm, selectedEmployee, selectedYear, selectedMonth]);

  // Handle filter changes
  const handleEmployeeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedEmployee(e.target.value);
  };

  const handleYearChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedYear(e.target.value);
  };

  const handleMonthChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedMonth(e.target.value);
  };

  // Format date for display
  const formatDate = (dateString: string | null | undefined) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString();
  };

  // Format currency for display
  const formatCurrency = (amount: number | null | undefined) => {
    if (amount === null || amount === undefined) return 'N/A';
    return new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(amount);
  };

  // Get status badge class
  const getStatusBadgeClass = (status: string) => {
    switch (status) {
      case 'viewed':
        return 'bg-green-100 text-green-800';
      case 'issued':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Handle view pay stub
  const handleViewPayStub = async (payStubId: string) => {
    try {
      // Mark pay stub as viewed
      await axios.put(`${API_URL}/pay-stubs/${payStubId}/view`);

      // Update the pay stub status in the local state
      const updatedPayStubs = payStubs.map(payStub => 
        payStub.id === payStubId 
          ? { ...payStub, status: 'viewed' } 
          : payStub
      );
      setPayStubs(updatedPayStubs);

      // If there's a document URL, open it in a new tab
      const payStub = payStubs.find(p => p.id === payStubId);
      if (payStub?.document_url) {
        window.open(payStub.document_url, '_blank');
      } else {
        // Otherwise, navigate to a detailed view (you might want to implement this)
        // navigate(`/hr/pay-stubs/${payStubId}`);
        alert('Pay stub document is not available.');
      }
    } catch (err: any) {
      console.error('Error viewing pay stub:', err);
      alert('Failed to view pay stub. Please try again.');
    }
  };

  return (
    <Layout>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Pay Stubs</h1>
      </div>

      {/* Filters */}
      <div className="bg-white shadow rounded-lg mb-6">
        <div className="px-4 py-5 sm:p-6">
          <h2 className="text-lg font-medium text-gray-900 mb-4">Filters</h2>
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
            {/* Employee Filter */}
            <div>
              <label htmlFor="employee-filter" className="block text-sm font-medium text-gray-700 mb-1">
                Employee
              </label>
              <select
                id="employee-filter"
                value={selectedEmployee}
                onChange={handleEmployeeChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              >
                <option value="all">All Employees</option>
                {employees.map(emp => (
                  <option key={emp.id} value={emp.id}>{emp.name}</option>
                ))}
              </select>
            </div>

            {/* Year Filter */}
            <div>
              <label htmlFor="year-filter" className="block text-sm font-medium text-gray-700 mb-1">
                Year
              </label>
              <select
                id="year-filter"
                value={selectedYear}
                onChange={handleYearChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              >
                <option value="all">All Years</option>
                {years.map(year => (
                  <option key={year} value={year}>{year}</option>
                ))}
              </select>
            </div>

            {/* Month Filter */}
            <div>
              <label htmlFor="month-filter" className="block text-sm font-medium text-gray-700 mb-1">
                Month
              </label>
              <select
                id="month-filter"
                value={selectedMonth}
                onChange={handleMonthChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              >
                <option value="all">All Months</option>
                {months.map(month => (
                  <option key={month.value} value={month.value}>{month.label}</option>
                ))}
              </select>
            </div>
          </div>
        </div>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">{error}</span>
        </div>
      )}

      {loading ? (
        <div className="flex justify-center items-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500"></div>
        </div>
      ) : payStubs.length === 0 ? (
        <div className="bg-white shadow rounded-lg p-6 text-center">
          <p className="text-gray-500">No pay stubs found for the selected filters.</p>
        </div>
      ) : (
        <div className="bg-white shadow overflow-hidden sm:rounded-md">
          <ul className="divide-y divide-gray-200">
            {payStubs.map(payStub => (
              <li key={payStub.id}>
                <div className="px-4 py-4 sm:px-6 hover:bg-gray-50">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <p className="text-sm font-medium text-primary-600 truncate">
                        {payStub.Employee ? `${payStub.Employee.first_name} ${payStub.Employee.last_name}` : 'Unknown Employee'}
                      </p>
                      <span className={`ml-2 px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusBadgeClass(payStub.status)}`}>
                        {payStub.status.charAt(0).toUpperCase() + payStub.status.slice(1)}
                      </span>
                    </div>
                    <div className="ml-2 flex-shrink-0 flex">
                      <button
                        onClick={() => handleViewPayStub(payStub.id)}
                        className="inline-flex items-center px-2.5 py-1.5 border border-transparent text-xs font-medium rounded text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                      >
                        View Pay Stub
                      </button>
                    </div>
                  </div>
                  <div className="mt-2 sm:flex sm:justify-between">
                    <div className="sm:flex">
                      <p className="flex items-center text-sm text-gray-500">
                        <span className="truncate">Pay Period: {formatDate(payStub.pay_period_start)} - {formatDate(payStub.pay_period_end)}</span>
                      </p>
                      <p className="mt-2 flex items-center text-sm text-gray-500 sm:mt-0 sm:ml-6">
                        Payment Date: {formatDate(payStub.payment_date)}
                      </p>
                    </div>
                    <div className="mt-2 flex items-center text-sm text-gray-500 sm:mt-0">
                      <p className="font-semibold">
                        Net Pay: {formatCurrency(payStub.net_pay)}
                      </p>
                    </div>
                  </div>
                  <div className="mt-2 grid grid-cols-1 gap-2 sm:grid-cols-3">
                    <p className="text-sm text-gray-500">
                      Gross Pay: {formatCurrency(payStub.gross_pay)}
                    </p>
                    {payStub.regular_hours !== undefined && (
                      <p className="text-sm text-gray-500">
                        Regular Hours: {payStub.regular_hours}
                      </p>
                    )}
                    {payStub.overtime_hours !== undefined && payStub.overtime_hours > 0 && (
                      <p className="text-sm text-gray-500">
                        Overtime Hours: {payStub.overtime_hours}
                      </p>
                    )}
                  </div>
                </div>
              </li>
            ))}
          </ul>
        </div>
      )}
    </Layout>
  );
};

export default PayStubsPage;
