import React, { useState, useEffect } from 'react';
import { Card, Button, Table, Popconfirm, message, Typography, Tag, Space, Tooltip, Switch } from 'antd';
import type { SortOrder } from 'antd/es/table/interface';
import { DeleteOutlined, LoadingOutlined, LaptopOutlined, MobileOutlined, TabletOutlined, QuestionOutlined, LockOutlined, UnlockOutlined } from '@ant-design/icons';
import moment from 'moment';
import { useAuth } from '../../context/AuthContext';
import { Session, getUserSessions, terminateSession, terminateAllSessions, isCurrentSession, trustDevice, untrustDevice } from '../../services/sessionService';

const { Title, Text } = Typography;

// Extend the Session interface to add is_current property
interface SessionWithCurrent extends Session {
  is_current: boolean;
}

const SessionManagement: React.FC = () => {
  const [sessions, setSessions] = useState<SessionWithCurrent[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [terminatingAll, setTerminatingAll] = useState<boolean>(false);
  const [terminatingIds, setTerminatingIds] = useState<string[]>([]);
  const [trustingIds, setTrustingIds] = useState<string[]>([]);
  const [untrustingIds, setUntrustingIds] = useState<string[]>([]);
  const { token } = useAuth();

  const fetchSessions = async () => {
    try {
      setLoading(true);
      const sessionsData = await getUserSessions();

      // Mark the current session
      const sessionsWithCurrent = sessionsData.map((session) => ({
        ...session,
        is_current: isCurrentSession(session)
      }));

      // Group sessions by device_fingerprint and keep only the newest entry for each device
      const groupedSessions: { [key: string]: SessionWithCurrent } = {};

      sessionsWithCurrent.forEach(session => {
        // Use a combination of device properties as a key if device_fingerprint is not available
        const deviceKey = session.device_fingerprint || 
          `${session.device_type}-${session.browser}-${session.operating_system}-${session.ip_address}`;

        // If this is the first session for this device or if this session is newer than the existing one
        if (!groupedSessions[deviceKey] || 
            new Date(session.last_active_at) > new Date(groupedSessions[deviceKey].last_active_at)) {
          groupedSessions[deviceKey] = session;
        }

        // Always keep the current session
        if (session.is_current) {
          groupedSessions[deviceKey] = session;
        }
      });

      // Convert the grouped sessions object back to an array
      const uniqueSessions = Object.values(groupedSessions);

      setSessions(uniqueSessions);
    } catch (error) {
      console.error('Error fetching sessions:', error);
      message.error('Failed to load sessions');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchSessions();
  }, []);

  const handleTerminateSession = async (sessionId: string) => {
    try {
      setTerminatingIds(prev => [...prev, sessionId]);
      await terminateSession(sessionId);

      message.success('Session terminated successfully');
      // After terminating a session, refresh all sessions to ensure we have the latest data
      // This is better than just filtering out the terminated session because it ensures
      // we have the correct grouping of sessions by device
      fetchSessions();
    } catch (error) {
      console.error('Error terminating session:', error);
      message.error('Failed to terminate session');
    } finally {
      setTerminatingIds(prev => prev.filter(id => id !== sessionId));
    }
  };

  const handleTerminateAllSessions = async () => {
    try {
      setTerminatingAll(true);
      await terminateAllSessions();

      message.success('All other sessions terminated successfully');
      // Refresh sessions to ensure we have the latest data with correct grouping
      fetchSessions();
    } catch (error) {
      console.error('Error terminating all sessions:', error);
      message.error('Failed to terminate sessions');
    } finally {
      setTerminatingAll(false);
    }
  };

  const handleTrustDevice = async (session: SessionWithCurrent) => {
    if (!session.device_fingerprint) {
      message.error('Cannot trust this device: missing device fingerprint');
      return;
    }

    try {
      setTrustingIds(prev => [...prev, session.id]);
      await trustDevice(session.device_fingerprint);

      message.success('Device trusted successfully');
      // Refresh the sessions list to get updated data with correct grouping
      fetchSessions();
    } catch (error) {
      console.error('Error trusting device:', error);
      message.error('Failed to trust device');
    } finally {
      setTrustingIds(prev => prev.filter(id => id !== session.id));
    }
  };

  const handleUntrustDevice = async (session: SessionWithCurrent) => {
    if (!session.device_fingerprint) {
      message.error('Cannot untrust this device: missing device fingerprint');
      return;
    }

    try {
      setUntrustingIds(prev => [...prev, session.id]);
      await untrustDevice(session.device_fingerprint);

      message.success('Device untrusted successfully');
      // Refresh the sessions list to get updated data with correct grouping
      fetchSessions();
    } catch (error) {
      console.error('Error untrusting device:', error);
      message.error('Failed to untrust device');
    } finally {
      setUntrustingIds(prev => prev.filter(id => id !== session.id));
    }
  };

  const getDeviceIcon = (deviceType: string) => {
    switch (deviceType.toLowerCase()) {
      case 'desktop':
        return <LaptopOutlined />;
      case 'mobile':
        return <MobileOutlined />;
      case 'tablet':
        return <TabletOutlined />;
      default:
        return <QuestionOutlined />;
    }
  };

  const columns = [
    {
      title: 'Device',
      dataIndex: 'device_type',
      key: 'device_type',
      render: (text: string, record: SessionWithCurrent) => (
        <Space>
          {getDeviceIcon(text)}
          <span>{text || 'Unknown'}</span>
          {record.is_current && <Tag color="green">Current</Tag>}
          {record.is_trusted && <Tag color="blue" icon={<LockOutlined />}>Trusted</Tag>}
        </Space>
      ),
    },
    {
      title: 'Browser',
      dataIndex: 'browser',
      key: 'browser',
    },
    {
      title: 'Operating System',
      dataIndex: 'operating_system',
      key: 'operating_system',
    },
    {
      title: 'IP Address',
      dataIndex: 'ip_address',
      key: 'ip_address',
    },
    {
      title: 'Last Active',
      dataIndex: 'last_active_at',
      key: 'last_active_at',
      render: (text: string) => (
        <Tooltip title={moment(text).format('YYYY-MM-DD HH:mm:ss')}>
          {moment(text).fromNow()}
        </Tooltip>
      ),
      sorter: (a: SessionWithCurrent, b: SessionWithCurrent) => 
        new Date(a.last_active_at).getTime() - new Date(b.last_active_at).getTime(),
      defaultSortOrder: 'descend' as SortOrder,
    },
    {
      title: 'Login Time',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (text: string) => (
        <Tooltip title={moment(text).format('YYYY-MM-DD HH:mm:ss')}>
          {moment(text).fromNow()}
        </Tooltip>
      ),
    },
    {
      title: 'Trusted Device',
      key: 'trusted',
      render: (_: any, record: SessionWithCurrent) => (
        <Space>
          <Switch
            checked={record.is_trusted}
            onChange={(checked) => checked ? handleTrustDevice(record) : handleUntrustDevice(record)}
            loading={trustingIds.includes(record.id) || untrustingIds.includes(record.id)}
            disabled={!record.device_fingerprint}
            checkedChildren={<LockOutlined />}
            unCheckedChildren={<UnlockOutlined />}
          />
          <Tooltip title={record.is_trusted 
            ? "Trusted devices never expire automatically" 
            : "Untrusted devices expire after 7 days of inactivity"}>
            <QuestionOutlined style={{ cursor: 'pointer' }} />
          </Tooltip>
        </Space>
      ),
    },
    {
      title: 'Action',
      key: 'action',
      render: (_: any, record: SessionWithCurrent) => (
        !record.is_current && (
          <Popconfirm
            title="Are you sure you want to terminate this session?"
            onConfirm={() => handleTerminateSession(record.id)}
            okText="Yes"
            cancelText="No"
          >
            <Button 
              danger 
              icon={terminatingIds.includes(record.id) ? <LoadingOutlined /> : <DeleteOutlined />}
              disabled={terminatingIds.includes(record.id)}
            >
              Terminate
            </Button>
          </Popconfirm>
        )
      ),
    },
  ];

  return (
    <Card title={<Title level={4}>Session Management</Title>} className="shadow-sm">
      <div className="mb-4">
        <Text>
          View and manage your active login sessions across different devices. Terminating a session will immediately log out that device.
        </Text>
        <div className="mt-2">
          <Text>
            <strong>Trusted Devices:</strong> Sessions from trusted devices remain active indefinitely and will always appear on this page. 
            Untrusted devices will only appear until their session expires (7 days after last activity).
          </Text>
        </div>
      </div>

      <div className="mb-4 flex justify-end">
        <Popconfirm
          title="Are you sure you want to terminate all other sessions?"
          onConfirm={handleTerminateAllSessions}
          okText="Yes"
          cancelText="No"
          disabled={sessions.filter(s => !s.is_current).length === 0}
        >
          <Button 
            danger 
            loading={terminatingAll}
            disabled={sessions.filter(s => !s.is_current).length === 0}
          >
            Terminate All Other Sessions
          </Button>
        </Popconfirm>
      </div>

      <div className="overflow-x-auto">
        <Table 
          columns={columns} 
          dataSource={sessions} 
          rowKey="id" 
          loading={loading}
          pagination={false}
          locale={{ emptyText: 'No active sessions found' }}
          scroll={{ x: 'max-content' }}
        />
      </div>
    </Card>
  );
};

export default SessionManagement;
