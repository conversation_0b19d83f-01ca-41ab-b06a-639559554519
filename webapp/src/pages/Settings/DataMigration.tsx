import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import axios from 'axios';
import Layout from '../../components/Layout';
import { useFarm } from '../../context/FarmContext';
import { 
  getSupportedSystems, 
  getMigrationJobs, 
  createMigrationJob, 
  uploadMigrationFile, 
  startMigrationJob,
  exportData,
  getMigrationTemplates,
  MigrationSystem,
  MigrationJob
} from '../../services/migrationService';
import { API_URL } from '../../config';

const DataMigration = () => {
  const [activeTab, setActiveTab] = useState<'import' | 'export' | 'history'>('import');
  const [supportedSystems, setSupportedSystems] = useState<MigrationSystem[]>([]);
  const [migrationJobs, setMigrationJobs] = useState<MigrationJob[]>([]);
  const [selectedSystem, setSelectedSystem] = useState<string>('');
  const [selectedEntities, setSelectedEntities] = useState<string[]>([]);
  const [file, setFile] = useState<File | null>(null);
  const [fileFormat, setFileFormat] = useState<string>('csv');
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const { currentFarm } = useFarm();

  // Fetch supported systems and migration jobs when farm is selected
  useEffect(() => {
    if (!currentFarm) return;

    const fetchData = async () => {
      setLoading(true);
      setError(null);

      try {
        // Fetch supported migration systems
        const systems = await getSupportedSystems();
        setSupportedSystems(systems);

        // Fetch migration jobs for the selected farm
        const jobs = await getMigrationJobs(currentFarm.id);
        setMigrationJobs(jobs);
      } catch (err: any) {
        console.error('Error fetching migration data:', err);
        setError('Failed to load migration data. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [currentFarm]);

  // Farm selection is now handled by the global farm selector in the header

  // Handle system selection
  const handleSystemChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const systemId = e.target.value;
    setSelectedSystem(systemId);

    // Reset selected entities when system changes
    setSelectedEntities([]);

    // Find the selected system
    const system = supportedSystems.find(s => s.id === systemId);
    if (system && system.supported_entities.length > 0) {
      // Select all entities by default
      setSelectedEntities(system.supported_entities);
    }
  };

  // Handle entity selection
  const handleEntityChange = (entity: string) => {
    if (selectedEntities.includes(entity)) {
      setSelectedEntities(selectedEntities.filter(e => e !== entity));
    } else {
      setSelectedEntities([...selectedEntities, entity]);
    }
  };

  // Handle file selection
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setFile(e.target.files[0]);
      setError(null);
      setSuccess(null);
    }
  };

  // Handle file format selection
  const handleFormatChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setFileFormat(e.target.value);
  };

  // Create migration job and upload file
  const handleImport = async () => {
    if (!currentFarm || !selectedSystem || selectedEntities.length === 0 || !file) {
      setError('Please fill in all required fields');
      return;
    }

    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      // Create migration job
      const job = await createMigrationJob({
        farmId: currentFarm.id,
        sourceSystem: selectedSystem,
        entities: selectedEntities
      });

      // Upload file
      await uploadMigrationFile(job.id, file, fileFormat);

      // Start migration job
      await startMigrationJob(job.id);

      // Update migration jobs list
      const updatedJobs = await getMigrationJobs(currentFarm.id);
      setMigrationJobs(updatedJobs);

      setSuccess('Migration job created and started successfully. You can view the progress in the History tab.');

      // Switch to history tab
      setActiveTab('history');
    } catch (err: any) {
      console.error('Error creating migration job:', err);
      setError(err.response?.data?.error || 'Failed to create migration job. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  // Handle export
  const handleExport = async () => {
    if (!currentFarm || !selectedSystem || selectedEntities.length === 0) {
      setError('Please fill in all required fields');
      return;
    }

    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      // Export data
      const blob = await exportData(
        currentFarm.id,
        selectedSystem,
        selectedEntities,
        fileFormat
      );

      // Create download link
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `export_${currentFarm.id}_${selectedSystem}_${new Date().toISOString().split('T')[0]}.${fileFormat}`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      setSuccess('Data exported successfully.');
    } catch (err: any) {
      console.error('Error exporting data:', err);
      setError(err.response?.data?.error || 'Failed to export data. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  // Download migration template
  const handleDownloadTemplate = async () => {
    if (!selectedSystem) {
      setError('Please select a system first');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // Get migration templates
      const blob = await getMigrationTemplates(selectedSystem);

      // Create download link
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `template_${selectedSystem}.zip`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (err: any) {
      console.error('Error downloading template:', err);
      setError(err.response?.data?.error || 'Failed to download template. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  // Format date
  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleString();
  };

  // Get system name by ID
  const getSystemName = (systemId: string) => {
    const system = supportedSystems.find(s => s.id === systemId);
    return system ? system.name : 'Unknown System';
  };

  return (
    <Layout>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Data Migration Tools</h1>
        {/* Note: Farm selector removed - using global farm selector in header */}
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">{error}</span>
        </div>
      )}

      {success && (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">{success}</span>
        </div>
      )}

      {/* Tabs */}
      <div className="border-b border-gray-200 mb-6">
        <nav className="-mb-px flex space-x-8">
          <button
            onClick={() => setActiveTab('import')}
            className={`${
              activeTab === 'import'
                ? 'border-primary-500 text-primary-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
          >
            Import Data
          </button>
          <button
            onClick={() => setActiveTab('export')}
            className={`${
              activeTab === 'export'
                ? 'border-primary-500 text-primary-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
          >
            Export Data
          </button>
          <button
            onClick={() => setActiveTab('history')}
            className={`${
              activeTab === 'history'
                ? 'border-primary-500 text-primary-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
          >
            Migration History
          </button>
        </nav>
      </div>

      {/* Import Tab */}
      {activeTab === 'import' && (
        <div className="bg-white shadow rounded-lg overflow-hidden">
          <div className="px-4 py-5 sm:p-6">
            <h2 className="text-lg font-medium text-gray-900 mb-4">Import Data from Another System</h2>

            <div className="mb-6">
              <label htmlFor="sourceSystem" className="block text-sm font-medium text-gray-700 mb-1">
                Source System
              </label>
              <select
                id="sourceSystem"
                name="sourceSystem"
                value={selectedSystem}
                onChange={handleSystemChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              >
                <option value="">Select Source System</option>
                {supportedSystems.map(system => (
                  <option key={system.id} value={system.id}>{system.name}</option>
                ))}
              </select>
            </div>

            {selectedSystem && (
              <>
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Entities to Import
                  </label>
                  <div className="mt-2 grid grid-cols-1 gap-y-2 sm:grid-cols-2 sm:gap-x-4">
                    {supportedSystems.find(s => s.id === selectedSystem)?.supported_entities.map(entity => (
                      <div key={entity} className="flex items-center">
                        <input
                          id={`entity-${entity}`}
                          name={`entity-${entity}`}
                          type="checkbox"
                          checked={selectedEntities.includes(entity)}
                          onChange={() => handleEntityChange(entity)}
                          className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                        />
                        <label htmlFor={`entity-${entity}`} className="ml-2 block text-sm text-gray-900">
                          {entity.charAt(0).toUpperCase() + entity.slice(1)}
                        </label>
                      </div>
                    ))}
                  </div>
                </div>

                <div className="mb-6">
                  <label htmlFor="fileFormat" className="block text-sm font-medium text-gray-700 mb-1">
                    File Format
                  </label>
                  <select
                    id="fileFormat"
                    name="fileFormat"
                    value={fileFormat}
                    onChange={handleFormatChange}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                  >
                    <option value="csv">CSV</option>
                    <option value="excel">Excel</option>
                    <option value="json">JSON</option>
                  </select>
                </div>

                <div className="mb-6">
                  <label htmlFor="file" className="block text-sm font-medium text-gray-700 mb-1">
                    Data File
                  </label>
                  <input
                    type="file"
                    id="file"
                    name="file"
                    onChange={handleFileChange}
                    accept={fileFormat === 'csv' ? '.csv' : fileFormat === 'excel' ? '.xlsx,.xls' : '.json'}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                  />
                </div>

                <div className="flex space-x-3">
                  <button
                    type="button"
                    onClick={handleDownloadTemplate}
                    className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                  >
                    Download Template
                  </button>

                  <button
                    type="button"
                    onClick={handleImport}
                    disabled={loading || !file || selectedEntities.length === 0}
                    className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                  >
                    {loading ? 'Importing...' : 'Import Data'}
                  </button>
                </div>
              </>
            )}
          </div>
        </div>
      )}

      {/* Export Tab */}
      {activeTab === 'export' && (
        <div className="bg-white shadow rounded-lg overflow-hidden">
          <div className="px-4 py-5 sm:p-6">
            <h2 className="text-lg font-medium text-gray-900 mb-4">Export Data to Another System</h2>

            <div className="mb-6">
              <label htmlFor="targetSystem" className="block text-sm font-medium text-gray-700 mb-1">
                Target System
              </label>
              <select
                id="targetSystem"
                name="targetSystem"
                value={selectedSystem}
                onChange={handleSystemChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              >
                <option value="">Select Target System</option>
                {supportedSystems.map(system => (
                  <option key={system.id} value={system.id}>{system.name}</option>
                ))}
              </select>
            </div>

            {selectedSystem && (
              <>
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Entities to Export
                  </label>
                  <div className="mt-2 grid grid-cols-1 gap-y-2 sm:grid-cols-2 sm:gap-x-4">
                    {supportedSystems.find(s => s.id === selectedSystem)?.supported_entities.map(entity => (
                      <div key={entity} className="flex items-center">
                        <input
                          id={`entity-${entity}`}
                          name={`entity-${entity}`}
                          type="checkbox"
                          checked={selectedEntities.includes(entity)}
                          onChange={() => handleEntityChange(entity)}
                          className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                        />
                        <label htmlFor={`entity-${entity}`} className="ml-2 block text-sm text-gray-900">
                          {entity.charAt(0).toUpperCase() + entity.slice(1)}
                        </label>
                      </div>
                    ))}
                  </div>
                </div>

                <div className="mb-6">
                  <label htmlFor="exportFormat" className="block text-sm font-medium text-gray-700 mb-1">
                    Export Format
                  </label>
                  <select
                    id="exportFormat"
                    name="exportFormat"
                    value={fileFormat}
                    onChange={handleFormatChange}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                  >
                    <option value="csv">CSV</option>
                    <option value="excel">Excel</option>
                    <option value="json">JSON</option>
                  </select>
                </div>

                <button
                  type="button"
                  onClick={handleExport}
                  disabled={loading || selectedEntities.length === 0}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                >
                  {loading ? 'Exporting...' : 'Export Data'}
                </button>
              </>
            )}
          </div>
        </div>
      )}

      {/* History Tab */}
      {activeTab === 'history' && (
        <div className="bg-white shadow rounded-lg overflow-hidden">
          <div className="px-4 py-5 sm:p-6">
            <h2 className="text-lg font-medium text-gray-900 mb-4">Migration History</h2>

            {migrationJobs.length === 0 ? (
              <p className="text-sm text-gray-500">No migration jobs found for this farm.</p>
            ) : (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Date
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        System
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {migrationJobs.map(job => (
                      <tr key={job.id}>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {formatDate(job.created_at)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {getSystemName(job.source_system)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                            job.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                            job.status === 'in_progress' ? 'bg-blue-100 text-blue-800' :
                            job.status === 'completed' ? 'bg-green-100 text-green-800' :
                            'bg-red-100 text-red-800'
                          }`}>
                            {job.status.charAt(0).toUpperCase() + job.status.slice(1)}
                          </span>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        </div>
      )}
    </Layout>
  );
};

export default DataMigration;
