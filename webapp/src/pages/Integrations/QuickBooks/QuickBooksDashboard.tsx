import { useState, useEffect, useContext } from 'react';
import { useNavigate } from 'react-router-dom';
import { AuthContext } from '../../../context/AuthContext';
import { useFarm } from '../../../context/FarmContext';
import Layout from '../../../components/Layout';
import { getConnection, syncAccounts, importData, updateItem } from '../../../services/quickbooksService';
import { QuickBooksAccount, QuickBooksCustomer, QuickBooksVendor, QuickBooksItem, QuickBooksInvoice, QuickBooksBill } from '../../../services/quickbooksService';

const QuickBooksDashboard = () => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [connection, setConnection] = useState<any>(null);
  const [syncStatus, setSyncStatus] = useState<Record<string, 'idle' | 'loading' | 'success' | 'error'>>({
    accounts: 'idle',
    customers: 'idle',
    vendors: 'idle',
    items: 'idle',
    invoices: 'idle',
    bills: 'idle',
    transactions: 'idle',
    payments: 'idle',
    taxdocuments: 'idle',
  });
  const [data, setData] = useState<{
    accounts: QuickBooksAccount[];
    customers: QuickBooksCustomer[];
    vendors: QuickBooksVendor[];
    items: QuickBooksItem[];
    invoices: QuickBooksInvoice[];
    bills: QuickBooksBill[];
    transactions: any[];
    payments: any[];
    taxdocuments: any[];
  }>({
    accounts: [],
    customers: [],
    vendors: [],
    items: [],
    invoices: [],
    bills: [],
    transactions: [],
    payments: [],
    taxdocuments: [],
  });

  const { user } = useContext(AuthContext);
  const { currentFarm } = useFarm();
  const navigate = useNavigate();

  // Check if user is authenticated and has a farm selected
  useEffect(() => {
    if (!user) {
      navigate('/login');
      return;
    }

    if (!currentFarm) {
      navigate('/dashboard');
      return;
    }

    // Get QuickBooks connection
    const fetchConnection = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await getConnection(currentFarm.id);
        if (response.connection) {
          setConnection(response.connection);
        } else {
          navigate('/integrations/quickbooks/link');
        }
      } catch (err: any) {
        console.error('Error fetching QuickBooks connection:', err);
        setError(err.response?.data?.error || 'Failed to fetch QuickBooks connection');

        // If the connection doesn't exist, redirect to the link page
        if (err.response && err.response.status === 404) {
          navigate('/integrations/quickbooks/link');
        }
      } finally {
        setLoading(false);
      }
    };

    fetchConnection();
  }, [user, currentFarm, navigate]);

  // Handle sync accounts
  const handleSyncAccounts = async () => {
    if (!connection) return;

    try {
      setSyncStatus(prev => ({ ...prev, accounts: 'loading' }));

      const response = await syncAccounts(connection.id);

      if (response.success) {
        setData(prev => ({ ...prev, accounts: response.accounts }));
        setSyncStatus(prev => ({ ...prev, accounts: 'success' }));
      }
    } catch (err) {
      console.error('Error syncing accounts:', err);
      setSyncStatus(prev => ({ ...prev, accounts: 'error' }));
    }
  };

  // Handle import data
  const handleImportData = async (
    dataType: 'accounts' | 'customers' | 'vendors' | 'items' | 'invoices' | 'bills' | 'transactions' | 'payments' | 'taxdocuments',
    itemId?: string
  ) => {
    if (!connection) return;

    try {
      setSyncStatus(prev => ({ ...prev, [dataType]: 'loading' }));

      // Get optional date range (could be added as parameters in the future)
      const startDate = undefined;
      const endDate = undefined;

      const response = await importData(connection.id, dataType, startDate, endDate, itemId);

      if (response.success) {
        setData(prev => ({ ...prev, [dataType]: response.data }));
        setSyncStatus(prev => ({ ...prev, [dataType]: 'success' }));
      }
    } catch (err) {
      console.error(`Error importing ${dataType}:`, err);
      setSyncStatus(prev => ({ ...prev, [dataType]: 'error' }));
    }
  };

  // Handle update item
  const handleUpdateItem = async (
    dataType: 'accounts' | 'customers' | 'vendors' | 'items' | 'invoices' | 'bills' | 'payments' | 'taxdocuments',
    itemId: string,
    itemData: any
  ) => {
    if (!connection) return;

    try {
      setSyncStatus(prev => ({ ...prev, [dataType]: 'loading' }));

      const response = await updateItem(connection.id, dataType, itemId, itemData);

      if (response.success) {
        // Refresh the data after update
        handleImportData(dataType);
      }
    } catch (err) {
      console.error(`Error updating ${dataType}:`, err);
      setSyncStatus(prev => ({ ...prev, [dataType]: 'error' }));
    }
  };

  if (loading) {
    return (
      <Layout>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex justify-center">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
          </div>
        </div>
      </Layout>
    );
  }

  if (error) {
    return (
      <Layout>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
            <strong className="font-bold">Error: </strong>
            <span className="block sm:inline">{error}</span>
          </div>
          <div className="mt-4">
            <button
              onClick={() => navigate('/integrations/quickbooks/link')}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              Go to QuickBooks Link Page
            </button>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="md:flex md:items-center md:justify-between mb-6">
          <div className="flex-1 min-w-0">
            <h2 className="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
              QuickBooks Dashboard
            </h2>
          </div>
          <div className="mt-4 flex md:mt-0 md:ml-4">
            <button
              onClick={() => navigate('/integrations/quickbooks/link')}
              className="ml-3 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              Manage Connection
            </button>
          </div>
        </div>

        {connection && (
          <div className="bg-white shadow overflow-hidden sm:rounded-lg mb-6">
            <div className="px-4 py-5 sm:px-6">
              <h3 className="text-lg leading-6 font-medium text-gray-900">
                QuickBooks Connection
              </h3>
              <p className="mt-1 max-w-2xl text-sm text-gray-500">
                Details about your QuickBooks connection.
              </p>
            </div>
            <div className="border-t border-gray-200 px-4 py-5 sm:p-0">
              <dl className="sm:divide-y sm:divide-gray-200">
                <div className="py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                  <dt className="text-sm font-medium text-gray-500">Company Name</dt>
                  <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                    {connection.company_name}
                  </dd>
                </div>
                <div className="py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                  <dt className="text-sm font-medium text-gray-500">Status</dt>
                  <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                    <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                      connection.status === 'active' 
                        ? 'bg-green-100 text-green-800' 
                        : connection.status === 'error' 
                          ? 'bg-red-100 text-red-800' 
                          : 'bg-yellow-100 text-yellow-800'
                    }`}>
                      {connection.status.charAt(0).toUpperCase() + connection.status.slice(1)}
                    </span>
                  </dd>
                </div>
                <div className="py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                  <dt className="text-sm font-medium text-gray-500">Last Sync</dt>
                  <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                    {connection.last_sync_at 
                      ? new Date(connection.last_sync_at).toLocaleString() 
                      : 'Never'}
                  </dd>
                </div>
              </dl>
            </div>
          </div>
        )}

        <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
          {/* Accounts */}
          <div className="bg-white shadow overflow-hidden sm:rounded-lg">
            <div className="px-4 py-5 sm:px-6 flex justify-between items-center">
              <div>
                <h3 className="text-lg leading-6 font-medium text-gray-900">
                  Accounts
                </h3>
                <p className="mt-1 max-w-2xl text-sm text-gray-500">
                  {data.accounts.length} accounts synced
                </p>
              </div>
              <button
                onClick={handleSyncAccounts}
                disabled={syncStatus.accounts === 'loading'}
                className={`inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded shadow-sm text-white ${
                  syncStatus.accounts === 'loading' 
                    ? 'bg-gray-400' 
                    : 'bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500'
                }`}
              >
                {syncStatus.accounts === 'loading' ? 'Syncing...' : 'Sync Accounts'}
              </button>
            </div>
            <div className="border-t border-gray-200">
              {data.accounts.length > 0 ? (
                <div className="table-container">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Name
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Type
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Balance
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {data.accounts.slice(0, 5).map((account) => (
                        <tr key={account.Id}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            {account.Name}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {account.AccountType}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            ${account.CurrentBalance.toFixed(2)}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div className="px-6 py-4 text-sm text-gray-500">
                  No accounts synced yet. Click "Sync Accounts" to import your QuickBooks accounts.
                </div>
              )}
              {data.accounts.length > 5 && (
                <div className="px-6 py-3 bg-gray-50 text-right text-sm font-medium">
                  <a href="#" className="text-primary-600 hover:text-primary-900">
                    View all {data.accounts.length} accounts
                  </a>
                </div>
              )}
            </div>
          </div>

          {/* Customers */}
          <div className="bg-white shadow overflow-hidden sm:rounded-lg">
            <div className="px-4 py-5 sm:px-6 flex justify-between items-center">
              <div>
                <h3 className="text-lg leading-6 font-medium text-gray-900">
                  Customers
                </h3>
                <p className="mt-1 max-w-2xl text-sm text-gray-500">
                  {data.customers.length} customers synced
                </p>
              </div>
              <button
                onClick={() => handleImportData('customers')}
                disabled={syncStatus.customers === 'loading'}
                className={`inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded shadow-sm text-white ${
                  syncStatus.customers === 'loading' 
                    ? 'bg-gray-400' 
                    : 'bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500'
                }`}
              >
                {syncStatus.customers === 'loading' ? 'Syncing...' : 'Sync Customers'}
              </button>
            </div>
            <div className="border-t border-gray-200">
              {data.customers.length > 0 ? (
                <div className="table-container">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Name
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Company
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Balance
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {data.customers.slice(0, 5).map((customer) => (
                        <tr key={customer.Id}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            {customer.DisplayName}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {customer.CompanyName || '-'}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            ${customer.Balance.toFixed(2)}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div className="px-6 py-4 text-sm text-gray-500">
                  No customers synced yet. Click "Sync Customers" to import your QuickBooks customers.
                </div>
              )}
              {data.customers.length > 5 && (
                <div className="px-6 py-3 bg-gray-50 text-right text-sm font-medium">
                  <a href="#" className="text-primary-600 hover:text-primary-900">
                    View all {data.customers.length} customers
                  </a>
                </div>
              )}
            </div>
          </div>

          {/* Vendors */}
          <div className="bg-white shadow overflow-hidden sm:rounded-lg">
            <div className="px-4 py-5 sm:px-6 flex justify-between items-center">
              <div>
                <h3 className="text-lg leading-6 font-medium text-gray-900">
                  Vendors
                </h3>
                <p className="mt-1 max-w-2xl text-sm text-gray-500">
                  {data.vendors.length} vendors synced
                </p>
              </div>
              <button
                onClick={() => handleImportData('vendors')}
                disabled={syncStatus.vendors === 'loading'}
                className={`inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded shadow-sm text-white ${
                  syncStatus.vendors === 'loading' 
                    ? 'bg-gray-400' 
                    : 'bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500'
                }`}
              >
                {syncStatus.vendors === 'loading' ? 'Syncing...' : 'Sync Vendors'}
              </button>
            </div>
            <div className="border-t border-gray-200">
              {data.vendors.length > 0 ? (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Name
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Company
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Balance
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {data.vendors.slice(0, 5).map((vendor) => (
                        <tr key={vendor.Id}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            {vendor.DisplayName}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {vendor.CompanyName || '-'}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            ${vendor.Balance.toFixed(2)}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div className="px-6 py-4 text-sm text-gray-500">
                  No vendors synced yet. Click "Sync Vendors" to import your QuickBooks vendors.
                </div>
              )}
              {data.vendors.length > 5 && (
                <div className="px-6 py-3 bg-gray-50 text-right text-sm font-medium">
                  <a href="#" className="text-primary-600 hover:text-primary-900">
                    View all {data.vendors.length} vendors
                  </a>
                </div>
              )}
            </div>
          </div>

          {/* Invoices */}
          <div className="bg-white shadow overflow-hidden sm:rounded-lg">
            <div className="px-4 py-5 sm:px-6 flex justify-between items-center">
              <div>
                <h3 className="text-lg leading-6 font-medium text-gray-900">
                  Invoices
                </h3>
                <p className="mt-1 max-w-2xl text-sm text-gray-500">
                  {data.invoices.length} invoices synced
                </p>
              </div>
              <button
                onClick={() => handleImportData('invoices')}
                disabled={syncStatus.invoices === 'loading'}
                className={`inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded shadow-sm text-white ${
                  syncStatus.invoices === 'loading' 
                    ? 'bg-gray-400' 
                    : 'bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500'
                }`}
              >
                {syncStatus.invoices === 'loading' ? 'Syncing...' : 'Sync Invoices'}
              </button>
            </div>
            <div className="border-t border-gray-200">
              {data.invoices.length > 0 ? (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Invoice #
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Customer
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Date
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Amount
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {data.invoices.slice(0, 5).map((invoice) => (
                        <tr key={invoice.Id}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            {invoice.DocNumber || '-'}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {invoice.CustomerRef.name}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {new Date(invoice.TxnDate).toLocaleDateString()}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            ${invoice.TotalAmt.toFixed(2)}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div className="px-6 py-4 text-sm text-gray-500">
                  No invoices synced yet. Click "Sync Invoices" to import your QuickBooks invoices.
                </div>
              )}
              {data.invoices.length > 5 && (
                <div className="px-6 py-3 bg-gray-50 text-right text-sm font-medium">
                  <a href="#" className="text-primary-600 hover:text-primary-900">
                    View all {data.invoices.length} invoices
                  </a>
                </div>
              )}
            </div>
          </div>

          {/* Bills */}
          <div className="bg-white shadow overflow-hidden sm:rounded-lg">
            <div className="px-4 py-5 sm:px-6 flex justify-between items-center">
              <div>
                <h3 className="text-lg leading-6 font-medium text-gray-900">
                  Bills
                </h3>
                <p className="mt-1 max-w-2xl text-sm text-gray-500">
                  {data.bills.length} bills synced
                </p>
              </div>
              <button
                onClick={() => handleImportData('bills')}
                disabled={syncStatus.bills === 'loading'}
                className={`inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded shadow-sm text-white ${
                  syncStatus.bills === 'loading' 
                    ? 'bg-gray-400' 
                    : 'bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500'
                }`}
              >
                {syncStatus.bills === 'loading' ? 'Syncing...' : 'Sync Bills'}
              </button>
            </div>
            <div className="border-t border-gray-200">
              {data.bills.length > 0 ? (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Bill #
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Vendor
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Date
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Amount
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {data.bills.slice(0, 5).map((bill) => (
                        <tr key={bill.Id}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            {bill.DocNumber || '-'}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {bill.VendorRef.name}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {new Date(bill.TxnDate).toLocaleDateString()}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            ${bill.TotalAmt.toFixed(2)}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div className="px-6 py-4 text-sm text-gray-500">
                  No bills synced yet. Click "Sync Bills" to import your QuickBooks bills.
                </div>
              )}
              {data.bills.length > 5 && (
                <div className="px-6 py-3 bg-gray-50 text-right text-sm font-medium">
                  <a href="#" className="text-primary-600 hover:text-primary-900">
                    View all {data.bills.length} bills
                  </a>
                </div>
              )}
            </div>
          </div>

          {/* Items */}
          <div className="bg-white shadow overflow-hidden sm:rounded-lg">
            <div className="px-4 py-5 sm:px-6 flex justify-between items-center">
              <div>
                <h3 className="text-lg leading-6 font-medium text-gray-900">
                  Items
                </h3>
                <p className="mt-1 max-w-2xl text-sm text-gray-500">
                  {data.items.length} items synced
                </p>
              </div>
              <button
                onClick={() => handleImportData('items')}
                disabled={syncStatus.items === 'loading'}
                className={`inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded shadow-sm text-white ${
                  syncStatus.items === 'loading' 
                    ? 'bg-gray-400' 
                    : 'bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500'
                }`}
              >
                {syncStatus.items === 'loading' ? 'Syncing...' : 'Sync Items'}
              </button>
            </div>
            <div className="border-t border-gray-200">
              {data.items.length > 0 ? (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Name
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Type
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Price
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Status
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {data.items.slice(0, 5).map((item) => (
                        <tr key={item.Id}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            {item.Name}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {item.Type}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            ${item.UnitPrice?.toFixed(2) || '0.00'}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {item.Active ? 'Active' : 'Inactive'}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div className="px-6 py-4 text-sm text-gray-500">
                  No items synced yet. Click "Sync Items" to import your QuickBooks items.
                </div>
              )}
              {data.items.length > 5 && (
                <div className="px-6 py-3 bg-gray-50 text-right text-sm font-medium">
                  <a href="#" className="text-primary-600 hover:text-primary-900">
                    View all {data.items.length} items
                  </a>
                </div>
              )}
            </div>
          </div>

          {/* Payments */}
          <div className="bg-white shadow overflow-hidden sm:rounded-lg">
            <div className="px-4 py-5 sm:px-6 flex justify-between items-center">
              <div>
                <h3 className="text-lg leading-6 font-medium text-gray-900">
                  Payments
                </h3>
                <p className="mt-1 max-w-2xl text-sm text-gray-500">
                  {data.payments.length} payments synced
                </p>
              </div>
              <button
                onClick={() => handleImportData('payments')}
                disabled={syncStatus.payments === 'loading'}
                className={`inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded shadow-sm text-white ${
                  syncStatus.payments === 'loading' 
                    ? 'bg-gray-400' 
                    : 'bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500'
                }`}
              >
                {syncStatus.payments === 'loading' ? 'Syncing...' : 'Sync Payments'}
              </button>
            </div>
            <div className="border-t border-gray-200">
              {data.payments.length > 0 ? (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Payment #
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Customer
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Date
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Amount
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {data.payments.slice(0, 5).map((payment) => (
                        <tr key={payment.Id}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            {payment.PaymentRefNum || '-'}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {payment.CustomerRef?.name || '-'}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {new Date(payment.TxnDate).toLocaleDateString()}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            ${payment.TotalAmt?.toFixed(2) || '0.00'}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div className="px-6 py-4 text-sm text-gray-500">
                  No payments synced yet. Click "Sync Payments" to import your QuickBooks payments.
                </div>
              )}
              {data.payments.length > 5 && (
                <div className="px-6 py-3 bg-gray-50 text-right text-sm font-medium">
                  <a href="#" className="text-primary-600 hover:text-primary-900">
                    View all {data.payments.length} payments
                  </a>
                </div>
              )}
            </div>
          </div>

          {/* Tax Documents */}
          <div className="bg-white shadow overflow-hidden sm:rounded-lg">
            <div className="px-4 py-5 sm:px-6 flex justify-between items-center">
              <div>
                <h3 className="text-lg leading-6 font-medium text-gray-900">
                  Tax Documents
                </h3>
                <p className="mt-1 max-w-2xl text-sm text-gray-500">
                  {data.taxdocuments.length} tax documents synced
                </p>
              </div>
              <button
                onClick={() => handleImportData('taxdocuments')}
                disabled={syncStatus.taxdocuments === 'loading'}
                className={`inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded shadow-sm text-white ${
                  syncStatus.taxdocuments === 'loading' 
                    ? 'bg-gray-400' 
                    : 'bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500'
                }`}
              >
                {syncStatus.taxdocuments === 'loading' ? 'Syncing...' : 'Sync Tax Documents'}
              </button>
            </div>
            <div className="border-t border-gray-200">
              {data.taxdocuments.length > 0 ? (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Document #
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Type
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Date
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Amount
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {data.taxdocuments.slice(0, 5).map((doc) => (
                        <tr key={doc.Id}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            {doc.DocNumber || '-'}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {doc.TaxType || '-'}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {new Date(doc.TxnDate).toLocaleDateString()}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            ${doc.TotalAmt?.toFixed(2) || '0.00'}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div className="px-6 py-4 text-sm text-gray-500">
                  No tax documents synced yet. Click "Sync Tax Documents" to import your QuickBooks tax documents.
                </div>
              )}
              {data.taxdocuments.length > 5 && (
                <div className="px-6 py-3 bg-gray-50 text-right text-sm font-medium">
                  <a href="#" className="text-primary-600 hover:text-primary-900">
                    View all {data.taxdocuments.length} tax documents
                  </a>
                </div>
              )}
            </div>
          </div>

          {/* Transactions */}
          <div className="bg-white shadow overflow-hidden sm:rounded-lg">
            <div className="px-4 py-5 sm:px-6 flex justify-between items-center">
              <div>
                <h3 className="text-lg leading-6 font-medium text-gray-900">
                  Transactions
                </h3>
                <p className="mt-1 max-w-2xl text-sm text-gray-500">
                  {data.transactions.length} transactions synced
                </p>
              </div>
              <button
                onClick={() => handleImportData('transactions')}
                disabled={syncStatus.transactions === 'loading'}
                className={`inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded shadow-sm text-white ${
                  syncStatus.transactions === 'loading' 
                    ? 'bg-gray-400' 
                    : 'bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500'
                }`}
              >
                {syncStatus.transactions === 'loading' ? 'Syncing...' : 'Sync Transactions'}
              </button>
            </div>
            <div className="border-t border-gray-200">
              {data.transactions.length > 0 ? (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Transaction #
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Type
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Date
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Amount
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {data.transactions.slice(0, 5).map((transaction) => (
                        <tr key={transaction.Id}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            {transaction.DocNumber || '-'}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {transaction.TxnType || '-'}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {new Date(transaction.TxnDate).toLocaleDateString()}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            ${transaction.TotalAmt?.toFixed(2) || '0.00'}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div className="px-6 py-4 text-sm text-gray-500">
                  No transactions synced yet. Click "Sync Transactions" to import your QuickBooks transactions.
                </div>
              )}
              {data.transactions.length > 5 && (
                <div className="px-6 py-3 bg-gray-50 text-right text-sm font-medium">
                  <a href="#" className="text-primary-600 hover:text-primary-900">
                    View all {data.transactions.length} transactions
                  </a>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default QuickBooksDashboard;
