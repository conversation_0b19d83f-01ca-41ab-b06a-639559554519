import React, { useState } from 'react';
import { syncFarmData, syncDataFromAmbrook } from '../../../services/ambrookService';
import { useFarm } from '../../../context/FarmContext';
import Layout from '../../../components/Layout';

const AmbrookDashboard: React.FC = () => {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [syncSuccess, setSyncSuccess] = useState<string | null>(null);
  const { selectedFarm } = useFarm();

  const handleSyncData = async () => {
    if (!selectedFarm) {
      setError('Please select a farm to sync data.');
      return;
    }

    try {
      setIsLoading(true);
      setError(null);
      setSyncSuccess(null);

      const response = await syncFarmData(selectedFarm.id);
      setSyncSuccess(response.message);
    } catch (err) {
      console.error('Error syncing farm data with <PERSON><PERSON>:', err);
      setError('Failed to sync farm data with <PERSON><PERSON>. Please try again later.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSyncFromAmbrook = async () => {
    if (!selectedFarm) {
      setError('Please select a farm to sync data.');
      return;
    }

    try {
      setIsLoading(true);
      setError(null);
      setSyncSuccess(null);

      const response = await syncDataFromAmbrook(selectedFarm.id);
      setSyncSuccess(response.message);
    } catch (err) {
      console.error('Error syncing data from Ambrook:', err);
      setError('Failed to sync data from Ambrook. Please try again later.');
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <Layout>
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-green-500"></div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold text-gray-800">Ambrook Financial Tools</h1>
          <div className="flex space-x-2">
            <button
              onClick={handleSyncData}
              disabled={isLoading || !selectedFarm}
              className="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded disabled:opacity-50"
            >
              Sync Farm Data to Ambrook
            </button>
            <button
              onClick={handleSyncFromAmbrook}
              disabled={isLoading || !selectedFarm}
              className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded disabled:opacity-50"
            >
              Sync Data from Ambrook
            </button>
          </div>
        </div>

        {!selectedFarm && (
          <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded mb-4">
            Please select a farm from the header dropdown to enable syncing with Ambrook.
          </div>
        )}

        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            {error}
          </div>
        )}

        {syncSuccess && (
          <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
            {syncSuccess}
          </div>
        )}

        <div className="bg-white rounded-lg shadow-md p-6 mt-6">
          <p className="text-gray-600">
            Use the buttons above to sync your farm data with Ambrook.
          </p>
        </div>
      </div>
    </Layout>
  );
};

export default AmbrookDashboard;
