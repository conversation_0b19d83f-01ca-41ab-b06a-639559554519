import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import Layout from '../../components/Layout';
import { 
  getIntegration, 
  updateIntegrationSettings,
  Integration
} from '../../services/integrationService';

interface FormField {
  key: string;
  label: string;
  type: string;
  description?: string;
  options?: { value: string; label: string }[];
  properties?: Record<string, FormField>;
  required?: boolean;
}

const IntegrationSettings = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();

  const [integration, setIntegration] = useState<Integration | null>(null);
  const [formFields, setFormFields] = useState<FormField[]>([]);
  const [formValues, setFormValues] = useState<Record<string, any>>({});
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Fetch integration data
  useEffect(() => {
    const fetchIntegration = async () => {
      if (!id) return;

      setLoading(true);
      setError(null);

      try {
        const data = await getIntegration(id);
        setIntegration(data);

        // Initialize form values from integration settings
        setFormValues(data.settings || {});

        // Generate form fields from settings schema
        // Use the schema from the API if available, otherwise fall back to a mock schema
        let schema = data.settings_schema;

        // If no schema is available from the API, use a mock schema
        if (!schema) {
          console.warn('No schema found for integration, using mock schema');

          // Mock schema based on our weather integration
          schema = {
            api_key: {
              type: 'string',
              label: 'API Key',
              description: 'Your weather service API key',
              required: true
            },
            update_frequency: {
              type: 'select',
              label: 'Update Frequency',
              description: 'How often to update weather data',
              options: [
                { value: 'hourly', label: 'Hourly' },
                { value: 'daily', label: 'Daily' },
                { value: 'weekly', label: 'Weekly' }
              ]
            },
            temperature_unit: {
              type: 'select',
              label: 'Temperature Unit',
              description: 'Unit for temperature display',
              options: [
                { value: 'celsius', label: 'Celsius' },
                { value: 'fahrenheit', label: 'Fahrenheit' }
              ]
            },
            notifications: {
              type: 'object',
              label: 'Notifications',
              properties: {
                enabled: {
                  type: 'boolean',
                  label: 'Enable Notifications'
                },
                extreme_weather: {
                  type: 'boolean',
                  label: 'Extreme Weather Alerts'
                },
                daily_forecast: {
                  type: 'boolean',
                  label: 'Daily Forecast'
                }
              }
            }
          };
        }

        // Convert schema to form fields
        const fields: FormField[] = Object.entries(schema).map(([key, schema]) => ({
          key,
          ...schema as any
        }));

        setFormFields(fields);
      } catch (err: any) {
        console.error(`Error fetching integration ${id}:`, err);
        setError('Failed to load integration. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchIntegration();
  }, [id]);

  // Handle form input changes
  const handleInputChange = (key: string, value: any) => {
    setFormValues(prev => ({
      ...prev,
      [key]: value
    }));
  };

  // Handle nested object changes (for object type fields)
  const handleNestedChange = (parentKey: string, key: string, value: any) => {
    setFormValues(prev => ({
      ...prev,
      [parentKey]: {
        ...prev[parentKey],
        [key]: value
      }
    }));
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!id || !integration) return;

    setSaving(true);
    setError(null);
    setSuccess(null);

    try {
      const updated = await updateIntegrationSettings(id, formValues);
      setIntegration(updated);
      setSuccess('Settings saved successfully.');

      // Clear success message after 3 seconds
      setTimeout(() => setSuccess(null), 3000);
    } catch (err: any) {
      console.error('Error saving settings:', err);
      setError('Failed to save settings. Please try again later.');
    } finally {
      setSaving(false);
    }
  };

  // Render form field based on type
  const renderField = (field: FormField) => {
    const { key, label, type, description, options, properties, required } = field;

    switch (type) {
      case 'string':
        return (
          <div className="mb-4" key={key}>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              {label}
              {required && <span className="text-red-500 ml-1">*</span>}
            </label>
            {description && <p className="text-sm text-gray-500 mb-1">{description}</p>}
            <input
              type="text"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              value={formValues[key] || ''}
              onChange={(e) => handleInputChange(key, e.target.value)}
              required={required}
            />
          </div>
        );

      case 'select':
        return (
          <div className="mb-4" key={key}>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              {label}
              {required && <span className="text-red-500 ml-1">*</span>}
            </label>
            {description && <p className="text-sm text-gray-500 mb-1">{description}</p>}
            <select
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              value={formValues[key] || ''}
              onChange={(e) => handleInputChange(key, e.target.value)}
              required={required}
            >
              {options?.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>
        );

      case 'boolean':
        return (
          <div className="mb-4" key={key}>
            <div className="flex items-center">
              <input
                type="checkbox"
                id={key}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                checked={formValues[key] || false}
                onChange={(e) => handleInputChange(key, e.target.checked)}
              />
              <label htmlFor={key} className="ml-2 block text-sm text-gray-700">
                {label}
              </label>
            </div>
            {description && <p className="text-sm text-gray-500 mt-1">{description}</p>}
          </div>
        );

      case 'object':
        return (
          <div className="mb-6 border-t border-b border-gray-200 py-4" key={key}>
            <h3 className="text-lg font-medium text-gray-900 mb-2">{label}</h3>
            {description && <p className="text-sm text-gray-500 mb-3">{description}</p>}
            <div className="pl-4 border-l-2 border-gray-200">
              {properties && Object.entries(properties).map(([propKey, propField]) => {
                const fullKey = `${key}.${propKey}`;
                const value = formValues[key]?.[propKey];

                switch (propField.type) {
                  case 'boolean':
                    return (
                      <div className="mb-3" key={fullKey}>
                        <div className="flex items-center">
                          <input
                            type="checkbox"
                            id={fullKey}
                            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                            checked={value || false}
                            onChange={(e) => handleNestedChange(key, propKey, e.target.checked)}
                          />
                          <label htmlFor={fullKey} className="ml-2 block text-sm text-gray-700">
                            {propField.label}
                          </label>
                        </div>
                        {propField.description && (
                          <p className="text-sm text-gray-500 mt-1 ml-6">{propField.description}</p>
                        )}
                      </div>
                    );

                  default:
                    return null;
                }
              })}
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <Layout>
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold">Integration Settings</h1>
          <button
            className="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300"
            onClick={() => navigate('/integrations')}
          >
            Back to Integrations
          </button>
        </div>

        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            {error}
          </div>
        )}

        {success && (
          <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
            {success}
          </div>
        )}

        {loading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
          </div>
        ) : integration ? (
          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="mb-6">
              <div className="flex items-center mb-4">
                {integration.icon ? (
                  <img src={integration.icon} alt={integration.name} className="w-12 h-12 mr-4" />
                ) : (
                  <div className="w-12 h-12 bg-gray-200 rounded-full mr-4 flex items-center justify-center">
                    <span className="text-gray-500 text-2xl">{integration.name.charAt(0)}</span>
                  </div>
                )}
                <div>
                  <h2 className="text-xl font-semibold">{integration.name}</h2>
                  <p className="text-sm text-gray-500">v{integration.version} by {integration.author}</p>
                </div>
                <span className={`ml-auto px-3 py-1 text-sm font-semibold rounded-full ${
                  integration.enabled 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-gray-100 text-gray-800'
                }`}>
                  {integration.enabled ? 'Enabled' : 'Disabled'}
                </span>
              </div>
              {integration.description && (
                <p className="text-gray-600 mb-4">{integration.description}</p>
              )}
              <div className="border-t border-gray-200 pt-4">
                <h3 className="text-lg font-medium mb-4">Configuration</h3>
                <form onSubmit={handleSubmit}>
                  {formFields.map(field => renderField(field))}

                  <div className="mt-6 flex justify-end">
                    <button
                      type="button"
                      className="mr-3 px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50"
                      onClick={() => navigate('/integrations')}
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                      disabled={saving}
                    >
                      {saving ? 'Saving...' : 'Save Settings'}
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        ) : (
          <div className="bg-white rounded-lg shadow-md p-6 text-center">
            <p className="text-gray-500">Integration not found.</p>
            <button
              className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
              onClick={() => navigate('/integrations')}
            >
              Back to Integrations
            </button>
          </div>
        )}
      </div>
    </Layout>
  );
};

export default IntegrationSettings;
