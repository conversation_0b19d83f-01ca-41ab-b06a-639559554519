import { useState, useEffect, useContext } from 'react';
import { Link } from 'react-router-dom';
import { AuthContext } from '../../context/AuthContext';
import { useFarm } from '../../context/FarmContext';
import Layout from '../../components/Layout';
import { 
  getIntegrations, 
  getAvailablePlugins, 
  installPlugin, 
  toggleIntegration, 
  deleteIntegration,
  Integration,
  AvailablePlugin
} from '../../services/integrationService';

const IntegrationsList = () => {
  const [integrations, setIntegrations] = useState<Integration[]>([]);
  const [availablePlugins, setAvailablePlugins] = useState<AvailablePlugin[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showAvailable, setShowAvailable] = useState(false);
  const [installing, setInstalling] = useState(false);

  const { user } = useContext(AuthContext);
  const { selectedFarm } = useFarm();

  // Fetch integrations
  useEffect(() => {
    const fetchIntegrations = async () => {
      setLoading(true);
      setError(null);

      try {
        const data = await getIntegrations(selectedFarm?.id);
        setIntegrations(data);
      } catch (err: any) {
        console.error('Error fetching integrations:', err);
        setError('Failed to load integrations. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    if (user) {
      fetchIntegrations();
    }
  }, [user, selectedFarm]);

  // Fetch available plugins when tab is switched
  useEffect(() => {
    const fetchAvailablePlugins = async () => {
      if (!showAvailable) return;

      setLoading(true);
      setError(null);

      try {
        const data = await getAvailablePlugins();
        setAvailablePlugins(data);
      } catch (err: any) {
        console.error('Error fetching available plugins:', err);
        setError('Failed to load available plugins. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchAvailablePlugins();
  }, [showAvailable]);

  // Handle installing a plugin
  const handleInstallPlugin = async (folder: string) => {
    setInstalling(true);
    setError(null);

    try {
      const installed = await installPlugin(folder);
      setIntegrations([...integrations, installed]);
      setShowAvailable(false);
    } catch (err: any) {
      console.error('Error installing plugin:', err);
      setError('Failed to install plugin. Please try again later.');
    } finally {
      setInstalling(false);
    }
  };

  // Handle enabling/disabling an integration
  const handleToggleIntegration = async (id: string, enabled: boolean) => {
    setError(null);

    try {
      const updated = await toggleIntegration(id, enabled);
      setIntegrations(integrations.map(integration => 
        integration.id === id ? updated : integration
      ));
    } catch (err: any) {
      console.error('Error toggling integration:', err);
      setError(`Failed to ${enabled ? 'enable' : 'disable'} integration. Please try again later.`);
    }
  };

  // Handle deleting an integration
  const handleDeleteIntegration = async (id: string) => {
    if (!window.confirm('Are you sure you want to delete this integration? This action cannot be undone.')) {
      return;
    }

    setError(null);

    try {
      await deleteIntegration(id);
      setIntegrations(integrations.filter(integration => integration.id !== id));
    } catch (err: any) {
      console.error('Error deleting integration:', err);
      setError('Failed to delete integration. Please try again later.');
    }
  };

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <Layout>
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold">Integrations</h1>
          <div className="flex space-x-2">
            <button
              className={`px-4 py-2 rounded-md ${!showAvailable ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-800'}`}
              onClick={() => setShowAvailable(false)}
            >
              Installed
            </button>
            <button
              className={`px-4 py-2 rounded-md ${showAvailable ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-800'}`}
              onClick={() => setShowAvailable(true)}
            >
              Available
            </button>
          </div>
        </div>

        {!selectedFarm && (
          <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded mb-4">
            Please select a farm from the header dropdown to view farm-specific integrations.
          </div>
        )}

        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            {error}
          </div>
        )}

        {loading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
          </div>
        ) : showAvailable ? (
          // Available plugins
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {availablePlugins.length === 0 ? (
              <div className="col-span-full text-center py-12 bg-gray-50 rounded-lg">
                <p className="text-gray-500">No available plugins found.</p>
              </div>
            ) : (
              availablePlugins.map(plugin => (
                <div key={plugin.folder} className="bg-white rounded-lg shadow-md overflow-hidden border border-gray-200">
                  <div className="p-6">
                    <div className="flex items-center mb-4">
                      {plugin.icon ? (
                        <img src={plugin.icon} alt={plugin.name} className="w-10 h-10 mr-3" />
                      ) : (
                        <div className="w-10 h-10 bg-gray-200 rounded-full mr-3 flex items-center justify-center">
                          <span className="text-gray-500 text-xl">{plugin.name.charAt(0)}</span>
                        </div>
                      )}
                      <div>
                        <h2 className="text-xl font-semibold">{plugin.name}</h2>
                        <p className="text-sm text-gray-500">v{plugin.version} by {plugin.author}</p>
                      </div>
                    </div>
                    <p className="text-gray-600 mb-4">{plugin.description}</p>
                    <button
                      className="w-full bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 transition-colors"
                      onClick={() => handleInstallPlugin(plugin.folder)}
                      disabled={installing}
                    >
                      {installing ? 'Installing...' : 'Install'}
                    </button>
                  </div>
                </div>
              ))
            )}
          </div>
        ) : (
          // Installed integrations
          <div className="bg-white rounded-lg shadow overflow-hidden">
            {integrations.length === 0 ? (
              <div className="text-center py-12">
                <p className="text-gray-500">No custom integrations installed.</p>
                <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="bg-white rounded-lg shadow-md overflow-hidden border border-gray-200">
                    <div className="p-6">
                      <div className="flex items-center mb-4">
                        <div className="w-10 h-10 bg-blue-100 rounded-full mr-3 flex items-center justify-center">
                          <span className="text-blue-500 text-xl">Q</span>
                        </div>
                        <div>
                          <h2 className="text-xl font-semibold">QuickBooks</h2>
                          <p className="text-sm text-gray-500">Financial Management</p>
                        </div>
                      </div>
                      <p className="text-gray-600 mb-4">Connect your QuickBooks account to automatically sync financial data.</p>
                      <Link
                        to="/integrations/quickbooks/link"
                        className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors inline-block text-center"
                      >
                        Go to QuickBooks
                      </Link>
                    </div>
                  </div>
                  <div className="bg-white rounded-lg shadow-md overflow-hidden border border-gray-200">
                    <div className="p-6">
                      <div className="flex items-center mb-4">
                        <div className="w-10 h-10 bg-green-100 rounded-full mr-3 flex items-center justify-center">
                          <span className="text-green-500 text-xl">A</span>
                        </div>
                        <div>
                          <h2 className="text-xl font-semibold">Ambrook</h2>
                          <p className="text-sm text-gray-500">Grants & Funding</p>
                        </div>
                      </div>
                      <p className="text-gray-600 mb-4">Access agricultural grants and funding opportunities through Ambrook.</p>
                      <Link
                        to="/integrations/ambrook"
                        className="w-full bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 transition-colors inline-block text-center"
                      >
                        Go to Ambrook
                      </Link>
                    </div>
                  </div>
                </div>
                <button
                  className="mt-6 bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors"
                  onClick={() => setShowAvailable(true)}
                >
                  Browse More Available Plugins
                </button>
              </div>
            ) : (
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Integration
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Version
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Author
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Installed
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {integrations.map(integration => (
                    <tr key={integration.id}>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          {integration.icon ? (
                            <img src={integration.icon} alt={integration.name} className="w-8 h-8 mr-3" />
                          ) : (
                            <div className="w-8 h-8 bg-gray-200 rounded-full mr-3 flex items-center justify-center">
                              <span className="text-gray-500">{integration.name.charAt(0)}</span>
                            </div>
                          )}
                          <div>
                            <div className="font-medium text-gray-900">{integration.name}</div>
                            {integration.description && (
                              <div className="text-sm text-gray-500">{integration.description}</div>
                            )}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {integration.version}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {integration.author}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                          integration.enabled 
                            ? 'bg-green-100 text-green-800' 
                            : 'bg-gray-100 text-gray-800'
                        }`}>
                          {integration.enabled ? 'Enabled' : 'Disabled'}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {formatDate(integration.created_at)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex justify-end space-x-2">
                          <button
                            className={`px-3 py-1 rounded ${
                              integration.enabled 
                                ? 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200' 
                                : 'bg-green-100 text-green-800 hover:bg-green-200'
                            }`}
                            onClick={() => handleToggleIntegration(integration.id, !integration.enabled)}
                          >
                            {integration.enabled ? 'Disable' : 'Enable'}
                          </button>
                          <Link
                            to={`/integrations/${integration.id}/settings`}
                            className="px-3 py-1 bg-blue-100 text-blue-800 rounded hover:bg-blue-200"
                          >
                            Settings
                          </Link>
                          <button
                            className="px-3 py-1 bg-red-100 text-red-800 rounded hover:bg-red-200"
                            onClick={() => handleDeleteIntegration(integration.id)}
                          >
                            Delete
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            )}
          </div>
        )}
      </div>
    </Layout>
  );
};

export default IntegrationsList;
