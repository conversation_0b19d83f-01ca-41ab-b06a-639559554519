import React from 'react';
import Modal from '../../../components/Modal';
import { Button } from '../../../components/ui/Button';
import PasswordGenerator from '../../../components/PasswordGenerator';

interface PasswordGeneratorModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSelectPassword: (password: string) => void;
}

/**
 * Modal component for generating passwords
 */
const PasswordGeneratorModal: React.FC<PasswordGeneratorModalProps> = ({
  isOpen,
  onClose,
  onSelectPassword,
}) => {
  const handleSelectPassword = (password: string) => {
    onSelectPassword(password);
    onClose();
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="Generate Secure Password"
      size="lg"
    >
      <div className="p-2">
        <PasswordGenerator onSelectPassword={handleSelectPassword} />
      </div>

      <div className="flex justify-end mt-4">
        <Button
          variant="secondary"
          onClick={onClose}
        >
          Cancel
        </Button>
      </div>
    </Modal>
  );
};

export default PasswordGeneratorModal;
