import { useState, useEffect, useContext } from 'react';
import { useNavigate, useParams, Link } from 'react-router-dom';
import axios from 'axios';
import { AuthContext } from '../../context/AuthContext';
import Layout from '../../components/Layout';
import { API_URL } from '../../config';
import { toast } from 'react-toastify';
import FarmAssociations from '../../components/farms/FarmAssociations';
import FarmFulfillmentOptions from '../../components/farms/FarmFulfillmentOptions';
import FarmFeatureToggles from '../../components/farms/FarmFeatureToggles';
import FarmFeatureSettings from '../../components/farms/FarmFeatureSettings';

interface User {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
  UserFarm: {
    role: string;
  };
}

interface SubscriptionPlan {
  id: string;
  name: string;
  description: string;
  price_monthly: number;
  price_yearly: number;
  features: any;
  max_farms: number;
  max_users: number;
  is_active: boolean;
}

interface Farm {
  id: string;
  name: string;
  address: string;
  city: string;
  state: string;
  zip_code: string;
  country: string;
  tax_id: string;
  created_at: string;
  updated_at: string;
  subscription_plan_id: string;
  subscription_status: string;
  subscription_start_date: string;
  subscription_end_date: string;
  billing_email: string;
  billing_address: string;
  billing_city: string;
  billing_state: string;
  billing_zip_code: string;
  billing_country: string;
  payment_method_id: string;
  stripe_customer_id: string;
  subdomain: string;
  Users: User[];
  SubscriptionPlan?: SubscriptionPlan;
}

const FarmDetail = () => {
  const { farmId } = useParams<{ farmId: string }>();
  const [farm, setFarm] = useState<Farm | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [leaveConfirmOpen, setLeaveConfirmOpen] = useState(false);
  const [transferOwnershipOpen, setTransferOwnershipOpen] = useState(false);
  const [newOwnerId, setNewOwnerId] = useState<string>('');

  const { user } = useContext(AuthContext);
  const navigate = useNavigate();

  useEffect(() => {
    // Check if user is authenticated
    if (!user) {
      navigate('/login');
      return;
    }

    const fetchFarm = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await axios.get(`${API_URL}/farms/${farmId}`);
        setFarm(response.data.farm);
      } catch (err: any) {
        console.error('Error fetching farm:', err);
        setError(err.response?.data?.error || 'Failed to load farm details');
      } finally {
        setLoading(false);
      }
    };

    fetchFarm();
  }, [user, navigate, farmId]);

  const handleDelete = async () => {
    try {
      setLoading(true);
      setError(null);

      await axios.delete(`${API_URL}/farms/${farmId}`);

      // Redirect to farms list
      navigate('/farms');
    } catch (err: any) {
      console.error('Error deleting farm:', err);
      setError(err.response?.data?.error || 'Failed to delete farm');
      setLoading(false);
    }
  };

  const handleLeaveFarm = async () => {
    try {
      setLoading(true);
      setError(null);

      await axios.delete(`${API_URL}/user-farms/leave/${farmId}`);

      toast.success('You have successfully left the farm');

      // Redirect to farms list
      navigate('/farms');
    } catch (err: any) {
      console.error('Error leaving farm:', err);
      toast.error(err.response?.data?.error || 'Failed to leave farm');
      setLoading(false);
      setLeaveConfirmOpen(false);
    }
  };

  const handleTransferOwnership = async () => {
    try {
      if (!newOwnerId) {
        toast.error('Please select a user to transfer ownership to');
        return;
      }

      setLoading(true);
      setError(null);

      await axios.post(`${API_URL}/user-farms/transfer-ownership/${farmId}`, {
        newOwnerId
      });

      toast.success('Farm ownership transferred successfully');

      // Refresh farm data
      const response = await axios.get(`${API_URL}/farms/${farmId}`);
      setFarm(response.data.farm);

      setTransferOwnershipOpen(false);
    } catch (err: any) {
      console.error('Error transferring farm ownership:', err);
      toast.error(err.response?.data?.error || 'Failed to transfer farm ownership');
      setLoading(false);
      setTransferOwnershipOpen(false);
    }
  };

  // Check if current user is an owner or has admin permissions
  const isOwner = farm?.Users?.some(u =>
      u.id === user?.id && ['owner', 'farm_owner'].includes(u.UserFarm.role)
  );

  const isFarmAdmin = farm?.Users?.some(u =>
      u.id === user?.id && ['owner', 'farm_owner', 'farm_admin'].includes(u.UserFarm.role)
  );

  // Check if current user has permission to manage users (owner, manager, or admin)
  const canManageUsers = farm?.Users?.some(u => 
    u.id === user?.id && ['owner', 'manager', 'farm_owner', 'farm_admin'].includes(u.UserFarm.role)
  );

  return (
    <Layout>
      {loading && !farm ? (
        <div className="p-8 text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
          <p className="mt-2 text-gray-500">Loading farm details...</p>
        </div>
      ) : error ? (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">{error}</span>
        </div>
      ) : farm ? (
        <>
          <div className="flex justify-between items-center mb-6">
            <h1 className="text-2xl font-bold text-gray-900">{farm.name}</h1>
            <div className="flex space-x-2">
              <Link
                to="/farms"
                className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                Back to Farms
              </Link>
              {isOwner ? (
                <>
                  <button
                    type="button"
                    onClick={() => setTransferOwnershipOpen(true)}
                    className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  >
                    Transfer Ownership
                  </button>
                  <button
                    type="button"
                    onClick={() => setDeleteConfirmOpen(true)}
                    className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                  >
                    Delete Farm
                  </button>
                </>
              ) : (
                <button
                  type="button"
                  onClick={() => setLeaveConfirmOpen(true)}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-yellow-600 hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500"
                >
                  Leave Farm
                </button>
              )}
            </div>
          </div>

          <div className="bg-white shadow rounded-lg overflow-hidden mb-6">
            <div className="px-6 py-4 border-b border-gray-200 bg-gray-50 flex justify-between items-center">
              <h2 className="text-lg font-medium text-gray-900">Farm Information</h2>
              {isFarmAdmin && (
                <Link
                  to={`/farms/${farm.id}/edit`}
                  className="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                >
                  Edit Information
                </Link>
              )}
            </div>
            <div className="p-6">
              <dl className="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
                <div>
                  <dt className="text-sm font-medium text-gray-500">Farm Name</dt>
                  <dd className="mt-1 text-sm text-gray-900">{farm.name}</dd>
                </div>

                <div>
                  <dt className="text-sm font-medium text-gray-500">Tax ID</dt>
                  <dd className="mt-1 text-sm text-gray-900">{farm.tax_id || 'Not provided'}</dd>
                </div>

                <div className="sm:col-span-2">
                  <dt className="text-sm font-medium text-gray-500">Address</dt>
                  <dd className="mt-1 text-sm text-gray-900">
                    {farm.address ? (
                      <>
                        {farm.address}<br />
                        {farm.city}, {farm.state} {farm.zip_code}<br />
                        {farm.country}
                      </>
                    ) : (
                      'No address provided'
                    )}
                  </dd>
                </div>

                <div>
                  <dt className="text-sm font-medium text-gray-500">Created</dt>
                  <dd className="mt-1 text-sm text-gray-900">
                    {new Date(farm.created_at).toLocaleDateString()}
                  </dd>
                </div>

                <div>
                  <dt className="text-sm font-medium text-gray-500">Last Updated</dt>
                  <dd className="mt-1 text-sm text-gray-900">
                    {new Date(farm.updated_at).toLocaleDateString()}
                  </dd>
                </div>
              </dl>
            </div>
          </div>

          {/* Subscription and Billing Information */}
          <div className="bg-white shadow rounded-lg overflow-hidden mb-6">
            <div className="px-6 py-4 border-b border-gray-200 bg-gray-50 flex justify-between items-center">
              <h2 className="text-lg font-medium text-gray-900">Subscription & Billing</h2>
              {isFarmAdmin && (
                <div className="flex space-x-2">
                  <Link
                    to={`/farms/${farm.id}/billing`}
                    className="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                  >
                    Manage Subscription
                  </Link>
                  {farm.SubscriptionPlan?.features?.custom_domain_enabled && (
                    <Link
                      to={`/farms/${farm.id}/custom-domain`}
                      className="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                    >
                      Custom Domain
                    </Link>
                  )}
                  {farm.SubscriptionPlan?.features?.customer_portal_enabled && (
                    <Link
                      to={`/farms/${farm.id}/customer-portal`}
                      className="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                    >
                      Customer Portal
                    </Link>
                  )}
                </div>
              )}
            </div>

            {/* Customer Portal Subdomain Links Section */}
            {farm.SubscriptionPlan?.features?.customer_portal_enabled && (
              <div className="px-6 py-4 border-t border-gray-200 bg-gray-50">
                <div className="flex justify-between items-center mb-3">
                  <h3 className="text-sm font-medium text-gray-900">Customer Portal Access</h3>
                </div>
                <div className="mb-4">
                  <p className="text-sm text-gray-600">
                    Your customers can access the portal using the following links:
                  </p>
                </div>
                <div className="space-y-3">
                  {farm.subdomain && (
                    <div className="bg-white p-3 rounded-md border border-gray-200">
                      <div className="flex justify-between items-center">
                        <div>
                          <p className="text-sm font-medium text-gray-900">Subdomain URL</p>
                          <p className="text-sm text-gray-500">
                            <a 
                              href={`https://${farm.subdomain}.nxtacre.com/customer/login`} 
                              target="_blank" 
                              rel="noopener noreferrer"
                              className="text-primary-600 hover:text-primary-500"
                            >
                              {`https://${farm.subdomain}.nxtacre.com/customer/login`}
                            </a>
                          </p>
                        </div>
                        <button
                          onClick={() => {
                            navigator.clipboard.writeText(`https://${farm.subdomain}.nxtacre.com/customer/login`);
                            toast.success('Link copied to clipboard');
                          }}
                          className="inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                        >
                          Copy Link
                        </button>
                      </div>
                    </div>
                  )}
                  <div className="bg-white p-3 rounded-md border border-gray-200">
                    <div className="flex justify-between items-center">
                      <div>
                        <p className="text-sm font-medium text-gray-900">Registration URL</p>
                        <p className="text-sm text-gray-500">
                          <a 
                            href={`https://${farm.subdomain}.nxtacre.com/customer/register`} 
                            target="_blank" 
                            rel="noopener noreferrer"
                            className="text-primary-600 hover:text-primary-500"
                          >
                            {`https://${farm.subdomain}.nxtacre.com/customer/register`}
                          </a>
                        </p>
                      </div>
                      <button
                        onClick={() => {
                          navigator.clipboard.writeText(`https://${farm.subdomain}.nxtacre.com/customer/register`);
                          toast.success('Link copied to clipboard');
                        }}
                        className="inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                      >
                        Copy Link
                      </button>
                    </div>
                  </div>
                </div>
                <div className="mt-4">
                  <p className="text-xs text-gray-500">
                    Share these links with your customers to allow them to login or register for an account.
                  </p>
                </div>
              </div>
            )}

            <div className="p-6">
              <div className="mb-6">
                <div className="flex justify-between items-center mb-3">
                  <h3 className="text-sm font-medium text-gray-900">Subscription Details</h3>
                </div>
                <dl className="grid grid-cols-1 gap-x-4 gap-y-4 sm:grid-cols-2">
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Current Plan</dt>
                    <dd className="mt-1 text-sm text-gray-900">
                      {farm.SubscriptionPlan ? farm.SubscriptionPlan.name : 'No subscription plan'}
                    </dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Status</dt>
                    <dd className="mt-1">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        farm.subscription_status === 'active' 
                          ? 'bg-green-100 text-green-800' 
                          : farm.subscription_status === 'canceled'
                          ? 'bg-red-100 text-red-800'
                          : 'bg-yellow-100 text-yellow-800'
                      }`}>
                        {farm.subscription_status ? farm.subscription_status.charAt(0).toUpperCase() + farm.subscription_status.slice(1) : 'None'}
                      </span>
                    </dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Start Date</dt>
                    <dd className="mt-1 text-sm text-gray-900">
                      {farm.subscription_start_date ? new Date(farm.subscription_start_date).toLocaleDateString() : 'N/A'}
                    </dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Renewal Date</dt>
                    <dd className="mt-1 text-sm text-gray-900">
                      {farm.subscription_end_date ? new Date(farm.subscription_end_date).toLocaleDateString() : 'N/A'}
                    </dd>
                  </div>
                  {farm.SubscriptionPlan && (
                    <>
                      <div>
                        <dt className="text-sm font-medium text-gray-500">Monthly Price</dt>
                        <dd className="mt-1 text-sm text-gray-900">
                          ${Number(farm.SubscriptionPlan.price_monthly).toFixed(2)}
                        </dd>
                      </div>
                      <div>
                        <dt className="text-sm font-medium text-gray-500">Yearly Price</dt>
                        <dd className="mt-1 text-sm text-gray-900">
                          ${Number(farm.SubscriptionPlan.price_yearly).toFixed(2)}
                        </dd>
                      </div>
                    </>
                  )}
                </dl>
              </div>

              <div>
                <div className="flex justify-between items-center mb-3">
                  <h3 className="text-sm font-medium text-gray-900">Billing Information</h3>
                  {isFarmAdmin && (
                    <button
                      onClick={() => navigate(`/farms/${farm.id}/billing`)}
                      className="inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                    >
                      Edit Billing
                    </button>
                  )}
                </div>
                <dl className="grid grid-cols-1 gap-x-4 gap-y-4 sm:grid-cols-2">
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Billing Email</dt>
                    <dd className="mt-1 text-sm text-gray-900">
                      {farm.billing_email || 'Not set'}
                    </dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Payment Method</dt>
                    <dd className="mt-1 text-sm text-gray-900">
                      {farm.payment_method_id ? 'Credit Card (ending in *****)' : 'Not set'}
                    </dd>
                  </div>
                  <div className="sm:col-span-2">
                    <dt className="text-sm font-medium text-gray-500">Billing Address</dt>
                    <dd className="mt-1 text-sm text-gray-900">
                      {farm.billing_address ? (
                        <>
                          {farm.billing_address}<br />
                          {farm.billing_city}, {farm.billing_state} {farm.billing_zip_code}<br />
                          {farm.billing_country}
                        </>
                      ) : (
                        'Not set'
                      )}
                    </dd>
                  </div>
                </dl>
              </div>
            </div>
          </div>

          <div className="bg-white shadow rounded-lg overflow-hidden mb-6">
            <div className="px-6 py-4 border-b border-gray-200 bg-gray-50 flex justify-between items-center">
              <h2 className="text-lg font-medium text-gray-900">Farm Users</h2>
              {canManageUsers && (
                <button
                  type="button"
                  onClick={() => navigate(`/farms/${farm.id}/users/add`)}
                  className="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                >
                  Add User
                </button>
              )}
            </div>
            <div className="p-6">
              {farm.Users && farm.Users.length > 0 ? (
                <ul className="divide-y divide-gray-200">
                  {farm.Users.map(farmUser => (
                    <li key={farmUser.id} className="py-4 flex justify-between items-center">
                      <div>
                        <p className="text-sm font-medium text-gray-900">
                          {farmUser.first_name} {farmUser.last_name}
                        </p>
                        <p className="text-sm text-gray-500">{farmUser.email}</p>
                      </div>
                      <div className="flex items-center">
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                          {farmUser.UserFarm.role.charAt(0).toUpperCase() + farmUser.UserFarm.role.slice(1)}
                        </span>
                        {canManageUsers && farmUser.id !== user?.id && (
                          <button
                            type="button"
                            onClick={() => navigate(`/farms/${farm.id}/users/${farmUser.id}/edit`)}
                            className="ml-2 text-gray-400 hover:text-gray-500"
                          >
                            <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                            </svg>
                          </button>
                        )}
                      </div>
                    </li>
                  ))}
                </ul>
              ) : (
                <p className="text-gray-500 text-center py-4">No users found for this farm.</p>
              )}
            </div>
          </div>

          {/* Farm Associations Section */}
          {isFarmAdmin && (
            <div className="bg-white shadow rounded-lg overflow-hidden mb-6">
              <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
                <h2 className="text-lg font-medium text-gray-900">Farm Associations</h2>
              </div>
              <div className="p-6">
                <FarmAssociations />
              </div>
            </div>
          )}

          {/* Fulfillment Options Section */}
          {isFarmAdmin && (
            <div className="bg-white shadow rounded-lg overflow-hidden mb-6">
              <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
                <h2 className="text-lg font-medium text-gray-900">Fulfillment Options</h2>
              </div>
              <div className="p-6">
                <FarmFulfillmentOptions farmId={farmId || ''} />
              </div>
            </div>
          )}

          {/* Feature Toggles Section */}
          {isFarmAdmin && (
            <div className="bg-white shadow rounded-lg overflow-hidden mb-6">
              <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
                <h2 className="text-lg font-medium text-gray-900">Marketplace Features</h2>
              </div>
              <div className="p-6">
                <FarmFeatureToggles farmId={farmId || ''} />
              </div>
            </div>
          )}

          {/* Feature Settings Section */}
          {isFarmAdmin && (
            <div className="bg-white shadow rounded-lg overflow-hidden mb-6">
              <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
                <h2 className="text-lg font-medium text-gray-900">Feature Settings Configuration</h2>
              </div>
              <div className="p-6">
                <FarmFeatureSettings farmId={farmId || ''} />
              </div>
            </div>
          )}

          {/* Delete Confirmation Modal */}
          {deleteConfirmOpen && (
            <div className="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
              <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
                <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>
                <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-y-auto max-h-[90vh] shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
                  <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                    <div className="sm:flex sm:items-start">
                      <div className="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                        <svg className="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                        </svg>
                      </div>
                      <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                        <h3 className="text-lg leading-6 font-medium text-gray-900" id="modal-title">
                          Delete Farm
                        </h3>
                        <div className="mt-2">
                          <p className="text-sm text-gray-500">
                            Are you sure you want to delete this farm? This action cannot be undone.
                            All data associated with this farm will be permanently removed.
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                    <button
                      type="button"
                      onClick={handleDelete}
                      disabled={loading}
                      className={`w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm ${loading ? 'opacity-70 cursor-not-allowed' : ''}`}
                    >
                      {loading ? 'Deleting...' : 'Delete'}
                    </button>
                    <button
                      type="button"
                      onClick={() => setDeleteConfirmOpen(false)}
                      className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                    >
                      Cancel
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Leave Farm Confirmation Modal */}
          {leaveConfirmOpen && (
            <div className="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
              <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
                <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>
                <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-y-auto max-h-[90vh] shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
                  <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                    <div className="sm:flex sm:items-start">
                      <div className="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-yellow-100 sm:mx-0 sm:h-10 sm:w-10">
                        <svg className="h-6 w-6 text-yellow-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                      </div>
                      <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                        <h3 className="text-lg leading-6 font-medium text-gray-900" id="modal-title">
                          Leave Farm
                        </h3>
                        <div className="mt-2">
                          <p className="text-sm text-gray-500">
                            Are you sure you want to leave this farm? You will lose access to all farm data and will need to be re-invited to join again.
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                    <button
                      type="button"
                      onClick={handleLeaveFarm}
                      disabled={loading}
                      className={`w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-yellow-600 text-base font-medium text-white hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500 sm:ml-3 sm:w-auto sm:text-sm ${loading ? 'opacity-70 cursor-not-allowed' : ''}`}
                    >
                      {loading ? 'Leaving...' : 'Leave Farm'}
                    </button>
                    <button
                      type="button"
                      onClick={() => setLeaveConfirmOpen(false)}
                      className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                    >
                      Cancel
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Transfer Ownership Modal */}
          {transferOwnershipOpen && (
            <div className="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
              <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
                <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>
                <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-y-auto max-h-[90vh] shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
                  <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                    <div className="sm:flex sm:items-start">
                      <div className="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 sm:mx-0 sm:h-10 sm:w-10">
                        <svg className="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4" />
                        </svg>
                      </div>
                      <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                        <h3 className="text-lg leading-6 font-medium text-gray-900" id="modal-title">
                          Transfer Farm Ownership
                        </h3>
                        <div className="mt-2">
                          <p className="text-sm text-gray-500 mb-4">
                            Select a user to transfer farm ownership to. You will be demoted to a farm admin.
                          </p>
                          <div className="mt-4">
                            <label htmlFor="newOwner" className="block text-sm font-medium text-gray-700">
                              New Owner
                            </label>
                            <select
                              id="newOwner"
                              name="newOwner"
                              className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md"
                              value={newOwnerId}
                              onChange={(e) => setNewOwnerId(e.target.value)}
                            >
                              <option value="">Select a user</option>
                              {farm?.Users?.filter(u => u.id !== user?.id).map(u => (
                                <option key={u.id} value={u.id}>
                                  {u.first_name} {u.last_name} ({u.email})
                                </option>
                              ))}
                            </select>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                    <button
                      type="button"
                      onClick={handleTransferOwnership}
                      disabled={loading || !newOwnerId}
                      className={`w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm ${(loading || !newOwnerId) ? 'opacity-70 cursor-not-allowed' : ''}`}
                    >
                      {loading ? 'Transferring...' : 'Transfer Ownership'}
                    </button>
                    <button
                      type="button"
                      onClick={() => setTransferOwnershipOpen(false)}
                      className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                    >
                      Cancel
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}
        </>
      ) : (
        <div className="bg-white shadow rounded-lg p-8 text-center">
          <p className="text-gray-500">Farm not found.</p>
          <Link
            to="/farms"
            className="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Back to Farms
          </Link>
        </div>
      )}
    </Layout>
  );
};

export default FarmDetail;
