import React, { useState, useEffect } from 'react';
import { useAuth } from '../../context/AuthContext';
import { useFarm } from '../../context/FarmContext';
import Layout from '../../components/Layout';
import axios from 'axios';
import { API_URL } from '../../config';
import { Link } from 'react-router-dom';
import LoadingSpinner from '../../components/LoadingSpinner';
import { 
  AreaChart, 
  Area, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer,
  LineChart,
  Line
} from 'recharts';

interface ROIData {
  name: string;
  type: 'crop' | 'field' | 'equipment';
  investment: number;
  revenue: number;
  expenses: number;
  profit: number;
  roi: number;
  paybackPeriod: number;
  year: number;
}

interface ROIHistoryData {
  year: number;
  roi: number;
}

const ROIAnalysis = () => {
  const { user } = useAuth();
  const { currentFarm } = useFarm();
  const [roiData, setRoiData] = useState<ROIData[]>([]);
  const [roiHistory, setRoiHistory] = useState<ROIHistoryData[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'crops' | 'fields' | 'equipment'>('crops');
  const [selectedYear, setSelectedYear] = useState<string>(new Date().getFullYear().toString());

  useEffect(() => {
    if (user && currentFarm) {
      fetchROIData();
    }
  }, [user, currentFarm, activeTab, selectedYear]);

  const fetchROIData = async () => {
    try {
      setLoading(true);
      // In a real implementation, this would fetch actual ROI data from the backend
      // For now, we'll simulate with mock data

      // Mock ROI data for crops
      const mockCropROI: ROIData[] = [
        {
          name: 'Corn',
          type: 'crop',
          investment: 120000,
          revenue: 250000,
          expenses: 150000,
          profit: 100000,
          roi: 83.33,
          paybackPeriod: 1.2,
          year: 2023
        },
        {
          name: 'Soybeans',
          type: 'crop',
          investment: 85000,
          revenue: 175000,
          expenses: 95000,
          profit: 80000,
          roi: 94.12,
          paybackPeriod: 1.06,
          year: 2023
        },
        {
          name: 'Wheat',
          type: 'crop',
          investment: 65000,
          revenue: 110000,
          expenses: 70000,
          profit: 40000,
          roi: 61.54,
          paybackPeriod: 1.63,
          year: 2023
        }
      ];

      // Mock ROI data for fields
      const mockFieldROI: ROIData[] = [
        {
          name: 'North Field',
          type: 'field',
          investment: 250000,
          revenue: 180000,
          expenses: 90000,
          profit: 90000,
          roi: 36.00,
          paybackPeriod: 2.78,
          year: 2023
        },
        {
          name: 'South Field',
          type: 'field',
          investment: 180000,
          revenue: 150000,
          expenses: 75000,
          profit: 75000,
          roi: 41.67,
          paybackPeriod: 2.4,
          year: 2023
        },
        {
          name: 'East Field',
          type: 'field',
          investment: 210000,
          revenue: 190000,
          expenses: 95000,
          profit: 95000,
          roi: 45.24,
          paybackPeriod: 2.21,
          year: 2023
        }
      ];

      // Mock ROI data for equipment
      const mockEquipmentROI: ROIData[] = [
        {
          name: 'Tractor - John Deere 8R',
          type: 'equipment',
          investment: 350000,
          revenue: 0, // Equipment doesn't directly generate revenue
          expenses: 45000, // Maintenance, fuel, etc.
          profit: 120000, // Estimated value added to operations
          roi: 34.29,
          paybackPeriod: 2.92,
          year: 2023
        },
        {
          name: 'Combine Harvester',
          type: 'equipment',
          investment: 425000,
          revenue: 0,
          expenses: 55000,
          profit: 180000,
          roi: 42.35,
          paybackPeriod: 2.36,
          year: 2023
        },
        {
          name: 'Irrigation System',
          type: 'equipment',
          investment: 175000,
          revenue: 0,
          expenses: 25000,
          profit: 95000,
          roi: 54.29,
          paybackPeriod: 1.84,
          year: 2023
        }
      ];

      // Mock ROI history data
      const mockROIHistory: ROIHistoryData[] = [
        { year: 2018, roi: 32.5 },
        { year: 2019, roi: 38.2 },
        { year: 2020, roi: 35.7 },
        { year: 2021, roi: 42.1 },
        { year: 2022, roi: 45.8 },
        { year: 2023, roi: 48.3 }
      ];

      // Set the appropriate data based on the active tab
      switch (activeTab) {
        case 'crops':
          setRoiData(mockCropROI);
          break;
        case 'fields':
          setRoiData(mockFieldROI);
          break;
        case 'equipment':
          setRoiData(mockEquipmentROI);
          break;
        default:
          setRoiData(mockCropROI);
      }

      setRoiHistory(mockROIHistory);
    } catch (err) {
      console.error('Error fetching ROI data:', err);
      setError('Failed to load ROI data. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(value);
  };

  const formatPercentage = (value: number) => {
    return `${value.toFixed(2)}%`;
  };

  const formatYears = (value: number) => {
    return `${value.toFixed(2)} years`;
  };

  const getROIColorClass = (roi: number) => {
    if (roi >= 50) return 'text-green-600';
    if (roi >= 25) return 'text-yellow-600';
    return 'text-red-600';
  };

  if (loading && roiData.length === 0) {
    return (
      <Layout>
        <div className="flex justify-center items-center h-full">
          <LoadingSpinner />
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="px-4 sm:px-6 lg:px-8 py-8">
        <div className="sm:flex sm:items-center">
          <div className="sm:flex-auto">
            <h1 className="text-2xl font-semibold text-gray-900">ROI Analysis</h1>
            <p className="mt-2 text-sm text-gray-700">
              Detailed return on investment analysis for crops, fields, and equipment.
            </p>
          </div>
          <div className="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
            <select
              id="year-select"
              name="year-select"
              className="block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-base focus:border-primary-500 focus:outline-none focus:ring-primary-500 sm:text-sm"
              value={selectedYear}
              onChange={(e) => setSelectedYear(e.target.value)}
            >
              <option value="2023">2023</option>
              <option value="2022">2022</option>
              <option value="2021">2021</option>
              <option value="2020">2020</option>
            </select>
          </div>
        </div>

        {error && (
          <div className="mt-4 bg-red-50 border-l-4 border-red-400 p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm text-red-700">{error}</p>
              </div>
            </div>
          </div>
        )}

        {/* ROI History Chart */}
        <div className="mt-8 bg-white shadow overflow-hidden sm:rounded-lg">
          <div className="px-4 py-5 sm:px-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900">ROI History</h3>
            <p className="mt-1 max-w-2xl text-sm text-gray-500">
              Historical return on investment trends over time.
            </p>
          </div>
          <div className="px-4 py-5 sm:p-6">
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart
                  data={roiHistory}
                  margin={{
                    top: 5,
                    right: 30,
                    left: 20,
                    bottom: 5,
                  }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="year" />
                  <YAxis tickFormatter={(value) => `${value}%`} />
                  <Tooltip formatter={(value) => [`${value}%`, 'ROI']} />
                  <Legend />
                  <Line type="monotone" dataKey="roi" stroke="#3B82F6" activeDot={{ r: 8 }} name="ROI %" />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="mt-8">
          <div className="hidden sm:block">
            <div className="border-b border-gray-200">
              <nav className="-mb-px flex space-x-8" aria-label="Tabs">
                <button
                  onClick={() => setActiveTab('crops')}
                  className={`${
                    activeTab === 'crops'
                      ? 'border-primary-500 text-primary-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
                >
                  Crops
                </button>
                <button
                  onClick={() => setActiveTab('fields')}
                  className={`${
                    activeTab === 'fields'
                      ? 'border-primary-500 text-primary-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
                >
                  Fields
                </button>
                <button
                  onClick={() => setActiveTab('equipment')}
                  className={`${
                    activeTab === 'equipment'
                      ? 'border-primary-500 text-primary-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
                >
                  Equipment
                </button>
              </nav>
            </div>
          </div>
        </div>

        {/* ROI Comparison Chart */}
        <div className="mt-6 bg-white shadow overflow-hidden sm:rounded-lg">
          <div className="px-4 py-5 sm:px-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900">
              {activeTab === 'crops' ? 'Crop' : activeTab === 'fields' ? 'Field' : 'Equipment'} ROI Comparison
            </h3>
            <p className="mt-1 max-w-2xl text-sm text-gray-500">
              Comparison of return on investment across different {activeTab}.
            </p>
          </div>
          <div className="px-4 py-5 sm:p-6">
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <AreaChart
                  data={roiData}
                  margin={{
                    top: 5,
                    right: 30,
                    left: 20,
                    bottom: 5,
                  }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis tickFormatter={(value) => `${value}%`} />
                  <Tooltip formatter={(value, name) => [
                    name === 'roi' ? `${value}%` : formatCurrency(Number(value)),
                    name === 'roi' ? 'ROI' : name.charAt(0).toUpperCase() + name.slice(1)
                  ]} />
                  <Legend />
                  <Area dataKey="roi" fill="#3B82F6" stroke="#3B82F6" name="ROI %" />
                </AreaChart>
              </ResponsiveContainer>
            </div>
          </div>
        </div>

        {/* ROI Data Table */}
        <div className="mt-8">
          <div className="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
            <table className="min-w-full divide-y divide-gray-300">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6">Name</th>
                  <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">Investment</th>
                  <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">Revenue</th>
                  <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">Expenses</th>
                  <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">Profit</th>
                  <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">ROI</th>
                  <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">Payback Period</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200 bg-white">
                {roiData.map((item) => (
                  <tr key={item.name}>
                    <td className="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-6">
                      {item.name}
                    </td>
                    <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">{formatCurrency(item.investment)}</td>
                    <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">{formatCurrency(item.revenue)}</td>
                    <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">{formatCurrency(item.expenses)}</td>
                    <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">{formatCurrency(item.profit)}</td>
                    <td className={`whitespace-nowrap px-3 py-4 text-sm font-medium ${getROIColorClass(item.roi)}`}>
                      {formatPercentage(item.roi)}
                    </td>
                    <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">{formatYears(item.paybackPeriod)}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default ROIAnalysis;
