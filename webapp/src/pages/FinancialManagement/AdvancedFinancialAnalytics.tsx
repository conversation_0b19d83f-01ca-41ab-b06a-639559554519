import React, { useState, useEffect } from 'react';
import { useAuth } from '../../context/AuthContext';
import { useFarm } from '../../context/FarmContext';
import Layout from '../../components/Layout';
import axios from 'axios';
import { API_URL } from '../../config';
import { Link } from 'react-router-dom';
import LoadingSpinner from '../../components/LoadingSpinner';
import { getAuthToken } from '../../utils/storageUtils';
import {
  LineChart,
  Line, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer,
  AreaChart,
  Area
} from 'recharts';

interface FinancialData {
  month: string;
  revenue: number;
  expenses: number;
  profit: number;
  cashFlow: number;
  projectedRevenue?: number;
  projectedExpenses?: number;
  projectedProfit?: number;
  projectedCashFlow?: number;
}

interface FinancialMetric {
  name: string;
  value: number;
  change: number;
  status: 'positive' | 'negative' | 'neutral';
}

const AdvancedFinancialAnalytics = () => {
  const { user } = useAuth();
  const { currentFarm } = useFarm();
  const [financialData, setFinancialData] = useState<FinancialData[]>([]);
  const [metrics, setMetrics] = useState<FinancialMetric[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [timeframe, setTimeframe] = useState<'monthly' | 'quarterly' | 'yearly'>('monthly');
  const [showProjections, setShowProjections] = useState<boolean>(true);

  useEffect(() => {
    if (user && currentFarm) {
      fetchFinancialData();
    }
  }, [user, currentFarm, timeframe]);

  const fetchFinancialData = async () => {
    try {
      setLoading(true);

      // Fetch real financial data from the backend API
      const response = await axios.get(`${API_URL}/financial-analytics/farms/${currentFarm.id}/financial-analytics`, {
        params: {
          timeframe,
          year: new Date().getFullYear(),
          includeProjections: showProjections
        },
        headers: {
          Authorization: `Bearer ${getAuthToken()}`
        }
      });

      if (response.data) {
        setFinancialData(response.data.financialData || []);
        setMetrics(response.data.metrics || []);
      } else {
        throw new Error('Failed to fetch financial data');
      }

      // If API call fails or returns empty data, fall back to mock data
      if (!response.data || !response.data.financialData || response.data.financialData.length === 0) {
        console.warn('Falling back to mock financial data');

        // Generate mock data based on timeframe
        let mockData: FinancialData[] = [];

        if (timeframe === 'monthly') {
          const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
          mockData = months.map((month, index) => {
            const baseRevenue = 50000 + Math.random() * 20000;
            const baseExpenses = 30000 + Math.random() * 15000;
            const profit = baseRevenue - baseExpenses;
            const cashFlow = profit + (Math.random() * 5000 - 2500); // Add some variation

            // For future months, add projections
            const isProjection = index > new Date().getMonth();

            return {
              month,
              revenue: isProjection ? 0 : baseRevenue,
              expenses: isProjection ? 0 : baseExpenses,
              profit: isProjection ? 0 : profit,
              cashFlow: isProjection ? 0 : cashFlow,
              projectedRevenue: isProjection ? baseRevenue * (1 + Math.random() * 0.1) : undefined,
              projectedExpenses: isProjection ? baseExpenses * (1 + Math.random() * 0.05) : undefined,
              projectedProfit: isProjection ? (baseRevenue * (1 + Math.random() * 0.1)) - (baseExpenses * (1 + Math.random() * 0.05)) : undefined,
              projectedCashFlow: isProjection ? (baseRevenue * (1 + Math.random() * 0.1)) - (baseExpenses * (1 + Math.random() * 0.05)) + (Math.random() * 5000 - 2500) : undefined
            };
          });
        } else if (timeframe === 'quarterly') {
          const quarters = ['Q1', 'Q2', 'Q3', 'Q4'];
          mockData = quarters.map((quarter, index) => {
            const baseRevenue = 150000 + Math.random() * 50000;
            const baseExpenses = 90000 + Math.random() * 30000;
            const profit = baseRevenue - baseExpenses;
            const cashFlow = profit + (Math.random() * 10000 - 5000);

            // For future quarters, add projections
            const currentQuarter = Math.floor(new Date().getMonth() / 3);
            const isProjection = index > currentQuarter;

            return {
              month: quarter,
              revenue: isProjection ? 0 : baseRevenue,
              expenses: isProjection ? 0 : baseExpenses,
              profit: isProjection ? 0 : profit,
              cashFlow: isProjection ? 0 : cashFlow,
              projectedRevenue: isProjection ? baseRevenue * (1 + Math.random() * 0.15) : undefined,
              projectedExpenses: isProjection ? baseExpenses * (1 + Math.random() * 0.1) : undefined,
              projectedProfit: isProjection ? (baseRevenue * (1 + Math.random() * 0.15)) - (baseExpenses * (1 + Math.random() * 0.1)) : undefined,
              projectedCashFlow: isProjection ? (baseRevenue * (1 + Math.random() * 0.15)) - (baseExpenses * (1 + Math.random() * 0.1)) + (Math.random() * 10000 - 5000) : undefined
            };
          });
        } else {
          // Yearly data
          const years = ['2020', '2021', '2022', '2023', '2024'];
          mockData = years.map((year, index) => {
            const baseRevenue = 600000 + Math.random() * 200000;
            const baseExpenses = 400000 + Math.random() * 150000;
            const profit = baseRevenue - baseExpenses;
            const cashFlow = profit + (Math.random() * 50000 - 25000);

            // For future years, add projections
            const currentYear = new Date().getFullYear();
            const isProjection = parseInt(year) > currentYear;

            return {
              month: year,
              revenue: isProjection ? 0 : baseRevenue,
              expenses: isProjection ? 0 : baseExpenses,
              profit: isProjection ? 0 : profit,
              cashFlow: isProjection ? 0 : cashFlow,
              projectedRevenue: isProjection ? baseRevenue * (1 + Math.random() * 0.2) : undefined,
              projectedExpenses: isProjection ? baseExpenses * (1 + Math.random() * 0.15) : undefined,
              projectedProfit: isProjection ? (baseRevenue * (1 + Math.random() * 0.2)) - (baseExpenses * (1 + Math.random() * 0.15)) : undefined,
              projectedCashFlow: isProjection ? (baseRevenue * (1 + Math.random() * 0.2)) - (baseExpenses * (1 + Math.random() * 0.15)) + (Math.random() * 50000 - 25000) : undefined
            };
          });
        }

        setFinancialData(mockData);

        // Generate key metrics
        const totalRevenue = mockData.reduce((sum, item) => sum + (item.revenue || 0), 0);
        const totalExpenses = mockData.reduce((sum, item) => sum + (item.expenses || 0), 0);
        const totalProfit = mockData.reduce((sum, item) => sum + (item.profit || 0), 0);
        const profitMargin = totalRevenue > 0 ? (totalProfit / totalRevenue) * 100 : 0;

        const mockMetrics: FinancialMetric[] = [
          {
            name: 'Total Revenue',
            value: totalRevenue,
            change: 8.2,
            status: 'positive'
          },
          {
            name: 'Total Expenses',
            value: totalExpenses,
            change: 3.1,
            status: 'negative'
          },
          {
            name: 'Net Profit',
            value: totalProfit,
            change: 12.5,
            status: 'positive'
          },
          {
            name: 'Profit Margin',
            value: profitMargin,
            change: 4.3,
            status: 'positive'
          }
        ];

        setMetrics(mockMetrics);
      }
    } catch (err) {
      console.error('Error fetching financial data:', err);
      setError('Failed to load financial data. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(value);
  };

  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`;
  };

  if (loading && financialData.length === 0) {
    return (
      <Layout>
        <div className="flex justify-center items-center h-full">
          <LoadingSpinner />
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="px-4 sm:px-6 lg:px-8 py-8">
        <div className="sm:flex sm:items-center">
          <div className="sm:flex-auto">
            <h1 className="text-2xl font-semibold text-gray-900">Advanced Financial Analytics</h1>
            <p className="mt-2 text-sm text-gray-700">
              Predictive cash flow analysis and financial forecasting for your farm operations.
            </p>
          </div>
          <div className="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
            <Link
              to="/transactions"
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              View Transactions
            </Link>
          </div>
        </div>

        {error && (
          <div className="mt-4 bg-red-50 border-l-4 border-red-400 p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm text-red-700">{error}</p>
              </div>
            </div>
          </div>
        )}

        {/* Key Metrics */}
        <div className="mt-8 grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
          {metrics.map((metric) => (
            <div key={metric.name} className="bg-white overflow-hidden shadow rounded-lg">
              <div className="px-4 py-5 sm:p-6">
                <dt className="text-sm font-medium text-gray-500 truncate">{metric.name}</dt>
                <dd className="mt-1 text-3xl font-semibold text-gray-900">
                  {metric.name.includes('Margin') ? formatPercentage(metric.value) : formatCurrency(metric.value)}
                </dd>
                <dd className={`mt-2 flex items-center text-sm ${
                  metric.status === 'positive' ? 'text-green-600' : 
                  metric.status === 'negative' ? 'text-red-600' : 'text-gray-500'
                }`}>
                  {metric.status === 'positive' ? (
                    <svg className="self-center flex-shrink-0 h-5 w-5 text-green-500" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
                      <path fillRule="evenodd" d="M5.293 9.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L11 7.414V15a1 1 0 11-2 0V7.414L6.707 9.707a1 1 0 01-1.414 0z" clipRule="evenodd" />
                    </svg>
                  ) : metric.status === 'negative' ? (
                    <svg className="self-center flex-shrink-0 h-5 w-5 text-red-500" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
                      <path fillRule="evenodd" d="M14.707 10.293a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 111.414-1.414L9 12.586V5a1 1 0 012 0v7.586l2.293-2.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  ) : (
                    <svg className="self-center flex-shrink-0 h-5 w-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
                      <path fillRule="evenodd" d="M5 10a1 1 0 011-1h8a1 1 0 110 2H6a1 1 0 01-1-1z" clipRule="evenodd" />
                    </svg>
                  )}
                  <span className="ml-2">
                    {metric.change}% {metric.status === 'positive' ? 'increase' : metric.status === 'negative' ? 'decrease' : ''} from previous period
                  </span>
                </dd>
              </div>
            </div>
          ))}
        </div>

        {/* Controls */}
        <div className="mt-8 flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div className="flex items-center space-x-4">
            <span className="text-sm font-medium text-gray-700">Timeframe:</span>
            <div className="relative z-0 inline-flex shadow-sm rounded-md">
              <button
                type="button"
                onClick={() => setTimeframe('monthly')}
                className={`relative inline-flex items-center px-4 py-2 rounded-l-md border border-gray-300 text-sm font-medium ${
                  timeframe === 'monthly' ? 'bg-primary-50 text-primary-700 z-10' : 'bg-white text-gray-700'
                }`}
              >
                Monthly
              </button>
              <button
                type="button"
                onClick={() => setTimeframe('quarterly')}
                className={`-ml-px relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium ${
                  timeframe === 'quarterly' ? 'bg-primary-50 text-primary-700 z-10' : 'bg-white text-gray-700'
                }`}
              >
                Quarterly
              </button>
              <button
                type="button"
                onClick={() => setTimeframe('yearly')}
                className={`-ml-px relative inline-flex items-center px-4 py-2 rounded-r-md border border-gray-300 text-sm font-medium ${
                  timeframe === 'yearly' ? 'bg-primary-50 text-primary-700 z-10' : 'bg-white text-gray-700'
                }`}
              >
                Yearly
              </button>
            </div>
          </div>
          <div className="mt-4 sm:mt-0">
            <div className="flex items-center">
              <input
                id="show-projections"
                name="show-projections"
                type="checkbox"
                checked={showProjections}
                onChange={() => setShowProjections(!showProjections)}
                className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
              />
              <label htmlFor="show-projections" className="ml-2 block text-sm text-gray-700">
                Show Projections
              </label>
            </div>
          </div>
        </div>

        {/* Cash Flow Chart */}
        <div className="mt-8 bg-white shadow overflow-hidden sm:rounded-lg">
          <div className="px-4 py-5 sm:px-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900">Cash Flow Analysis</h3>
            <p className="mt-1 max-w-2xl text-sm text-gray-500">
              Historical and projected cash flow over time.
            </p>
          </div>
          <div className="px-4 py-5 sm:p-6">
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart
                  data={financialData}
                  margin={{
                    top: 5,
                    right: 30,
                    left: 20,
                    bottom: 5,
                  }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis tickFormatter={(value) => `$${value / 1000}k`} />
                  <Tooltip formatter={(value) => formatCurrency(Number(value))} />
                  <Legend />
                  <Line type="monotone" dataKey="cashFlow" stroke="#3B82F6" activeDot={{ r: 8 }} name="Cash Flow" />
                  {showProjections && (
                    <Line type="monotone" dataKey="projectedCashFlow" stroke="#3B82F6" strokeDasharray="5 5" name="Projected Cash Flow" />
                  )}
                </LineChart>
              </ResponsiveContainer>
            </div>
          </div>
        </div>

        {/* Revenue vs Expenses Chart */}
        <div className="mt-8 bg-white shadow overflow-hidden sm:rounded-lg">
          <div className="px-4 py-5 sm:px-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900">Revenue vs Expenses</h3>
            <p className="mt-1 max-w-2xl text-sm text-gray-500">
              Comparison of revenue and expenses over time.
            </p>
          </div>
          <div className="px-4 py-5 sm:p-6">
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <AreaChart
                  data={financialData}
                  margin={{
                    top: 5,
                    right: 30,
                    left: 20,
                    bottom: 5,
                  }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis tickFormatter={(value) => `$${value / 1000}k`} />
                  <Tooltip formatter={(value) => formatCurrency(Number(value))} />
                  <Legend />
                  <Area type="monotone" dataKey="revenue" fill="#10B981" stroke="#10B981" name="Revenue" />
                  {showProjections && (
                    <Area type="monotone" dataKey="projectedRevenue" fill="#10B981" stroke="#10B981" fillOpacity={0.5} name="Projected Revenue" />
                  )}
                  <Area type="monotone" dataKey="expenses" fill="#EF4444" stroke="#EF4444" name="Expenses" />
                  {showProjections && (
                    <Area type="monotone" dataKey="projectedExpenses" fill="#EF4444" stroke="#EF4444" fillOpacity={0.5} name="Projected Expenses" />
                  )}
                </AreaChart>
              </ResponsiveContainer>
            </div>
          </div>
        </div>

        {/* Profit Trend Chart */}
        <div className="mt-8 bg-white shadow overflow-hidden sm:rounded-lg">
          <div className="px-4 py-5 sm:px-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900">Profit Trend</h3>
            <p className="mt-1 max-w-2xl text-sm text-gray-500">
              Historical and projected profit over time.
            </p>
          </div>
          <div className="px-4 py-5 sm:p-6">
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart
                  data={financialData}
                  margin={{
                    top: 5,
                    right: 30,
                    left: 20,
                    bottom: 5,
                  }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis tickFormatter={(value) => `$${value / 1000}k`} />
                  <Tooltip formatter={(value) => formatCurrency(Number(value))} />
                  <Legend />
                  <Line type="monotone" dataKey="profit" stroke="#8B5CF6" activeDot={{ r: 8 }} name="Profit" />
                  {showProjections && (
                    <Line type="monotone" dataKey="projectedProfit" stroke="#8B5CF6" strokeDasharray="5 5" name="Projected Profit" />
                  )}
                </LineChart>
              </ResponsiveContainer>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default AdvancedFinancialAnalytics;
