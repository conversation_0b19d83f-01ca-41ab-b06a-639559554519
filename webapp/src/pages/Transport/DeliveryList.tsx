import { useState, useEffect, useContext } from 'react';
import { Link } from 'react-router-dom';
import { AuthContext } from '../../context/AuthContext';
import { useFarm } from '../../context/FarmContext';
import Layout from '../../components/Layout';
import { getDeliveries, deleteDelivery, formatDate, Delivery } from '../../services/deliveryService';

// Using Delivery interface from deliveryService.ts

const DeliveryList = () => {
  const [deliveries, setDeliveries] = useState<Delivery[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const { user } = useContext(AuthContext);
  const { currentFarm } = useFarm();

  // Fetch deliveries for the selected farm
  useEffect(() => {
    const fetchDeliveries = async () => {
      if (!currentFarm) return;

      setLoading(true);
      setError(null);

      try {
        const deliveriesData = await getDeliveries(currentFarm.id);
        setDeliveries(deliveriesData);
      } catch (err: any) {
        console.error('Error fetching deliveries:', err);
        setError('Failed to load deliveries. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchDeliveries();
  }, [currentFarm]);

  // Handle delivery deletion
  const handleDeleteDelivery = async (deliveryId: string) => {
    if (!window.confirm('Are you sure you want to delete this delivery?')) {
      return;
    }

    try {
      await deleteDelivery(deliveryId);
      setDeliveries(deliveries.filter(delivery => delivery.id !== deliveryId));
    } catch (err: any) {
      console.error('Error deleting delivery:', err);
      setError('Failed to delete delivery. Please try again later.');
    }
  };

  // Using formatDate from deliveryService.ts

  return (
    <Layout>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Delivery Management</h1>
        <div>
          <Link
            to="/transport/deliveries/new"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Create Delivery
          </Link>
        </div>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">{error}</span>
        </div>
      )}

      {loading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
          <p className="ml-3 text-gray-500">Loading deliveries...</p>
        </div>
      ) : deliveries.length === 0 ? (
        <div className="bg-white shadow rounded-lg p-8 text-center">
          <p className="text-gray-500 mb-4">No deliveries found for this farm.</p>
          <p className="text-sm text-gray-400 mb-6">
            Create your first delivery to start tracking product shipments.
          </p>
          <Link
            to="/transport/deliveries/new"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Create Delivery
          </Link>
        </div>
      ) : (
        <div className="bg-white shadow overflow-hidden sm:rounded-md">
          <ul className="divide-y divide-gray-200">
            {deliveries.map((delivery) => (
              <li key={delivery.id}>
                <div className="px-4 py-4 sm:px-6">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className="ml-3">
                        <p className="text-sm font-medium text-primary-600 truncate">
                          Delivery to {delivery.destination}
                        </p>
                        <p className="text-sm text-gray-500">
                          {delivery.trackingNumber && <span className="mr-2">Tracking: {delivery.trackingNumber}</span>}
                          {delivery.recipientName && <span className="mr-2">Recipient: {delivery.recipientName}</span>}
                        </p>
                      </div>
                    </div>
                    <div className="flex space-x-2">
                      <Link
                        to={`/transport/deliveries/${delivery.id}`}
                        className="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                      >
                        View
                      </Link>
                      <Link
                        to={`/transport/deliveries/${delivery.id}/edit`}
                        className="inline-flex items-center px-3 py-1 border border-gray-300 text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                      >
                        Edit
                      </Link>
                      <button
                        onClick={() => handleDeleteDelivery(delivery.id)}
                        className="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                      >
                        Delete
                      </button>
                    </div>
                  </div>
                  <div className="mt-2 sm:flex sm:justify-between">
                    <div className="sm:flex">
                      {delivery.deliveryDate && (
                        <p className="flex items-center text-sm text-gray-500 mr-6">
                          <span>Delivery Date: {formatDate(delivery.deliveryDate)}</span>
                        </p>
                      )}
                      {delivery.estimatedArrival && (
                        <p className="mt-2 flex items-center text-sm text-gray-500 sm:mt-0 mr-6">
                          <span>ETA: {formatDate(delivery.estimatedArrival)}</span>
                        </p>
                      )}
                      {delivery.driverName && (
                        <p className="mt-2 flex items-center text-sm text-gray-500 sm:mt-0 mr-6">
                          <span>Driver: {delivery.driverName}</span>
                        </p>
                      )}
                    </div>
                    {delivery.status && (
                      <div className="mt-2 flex items-center text-sm text-gray-500 sm:mt-0">
                        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                          delivery.status === 'scheduled' ? 'bg-blue-100 text-blue-800' : 
                          delivery.status === 'in_transit' ? 'bg-yellow-100 text-yellow-800' : 
                          delivery.status === 'delivered' ? 'bg-green-100 text-green-800' : 
                          delivery.status === 'cancelled' ? 'bg-red-100 text-red-800' : 
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {delivery.status === 'scheduled' ? 'Scheduled' : 
                           delivery.status === 'in_transit' ? 'In Transit' : 
                           delivery.status === 'delivered' ? 'Delivered' : 
                           delivery.status === 'cancelled' ? 'Cancelled' : 
                           delivery.status}
                        </span>
                      </div>
                    )}
                  </div>
                  {delivery.products && delivery.products.length > 0 && (
                    <div className="mt-2 text-sm text-gray-500">
                      <p>Products: {delivery.products.map(p => `${p.name} (${p.quantity} ${p.unit})`).join(', ')}</p>
                    </div>
                  )}
                  {delivery.notes && (
                    <div className="mt-2 text-sm text-gray-500">
                      <p>Notes: {delivery.notes}</p>
                    </div>
                  )}
                </div>
              </li>
            ))}
          </ul>
        </div>
      )}
    </Layout>
  );
};

export default DeliveryList;
