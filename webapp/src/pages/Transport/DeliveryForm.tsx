import { useState, useEffect, useContext } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import axios from 'axios';
import { AuthContext } from '../../context/AuthContext';
import { useFarm } from '../../context/FarmContext';
import Layout from '../../components/Layout';
import { API_URL } from '../../config';

interface Driver {
  id: string;
  firstName: string;
  lastName: string;
}

interface Product {
  id: string;
  name: string;
  quantity: number;
  unit: string;
}

interface DeliveryFormData {
  deliveryDate: string;
  status: string;
  destination: string;
  recipientName: string;
  recipientContact: string;
  driverId: string;
  products: Product[];
  notes: string;
  trackingNumber: string;
  estimatedArrival: string;
}

const DeliveryForm = () => {
  const { deliveryId } = useParams<{ deliveryId: string }>();
  const navigate = useNavigate();
  const isEditMode = !!deliveryId;

  const { user } = useContext(AuthContext);
  const { currentFarm } = useFarm();

  const [formData, setFormData] = useState<DeliveryFormData>({
    deliveryDate: '',
    status: 'scheduled',
    destination: '',
    recipientName: '',
    recipientContact: '',
    driverId: '',
    products: [],
    notes: '',
    trackingNumber: '',
    estimatedArrival: '',
  });

  const [drivers, setDrivers] = useState<Driver[]>([]);
  const [farmProducts, setFarmProducts] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  // Fetch drivers and products for the selected farm
  useEffect(() => {
    const fetchData = async () => {
      if (!currentFarm) return;

      try {
        const [driversResponse, productsResponse] = await Promise.all([
          axios.get(`${API_URL}/drivers?farmId=${currentFarm.id}`),
          axios.get(`${API_URL}/products?farmId=${currentFarm.id}`)
        ]);

        setDrivers(driversResponse.data);
        setFarmProducts(productsResponse.data);
      } catch (err: any) {
        console.error('Error fetching form data:', err);
        setError('Failed to load form data. Please try again later.');
      }
    };

    fetchData();
  }, [currentFarm]);

  // Fetch delivery data if in edit mode
  useEffect(() => {
    if (isEditMode && deliveryId) {
      const fetchDelivery = async () => {
        setLoading(true);
        setError(null);

        try {
          const response = await axios.get(`${API_URL}/deliveries/${deliveryId}`);
          const deliveryData = response.data;
          
          setFormData({
            deliveryDate: deliveryData.deliveryDate || '',
            status: deliveryData.status || 'scheduled',
            destination: deliveryData.destination || '',
            recipientName: deliveryData.recipientName || '',
            recipientContact: deliveryData.recipientContact || '',
            driverId: deliveryData.driverId || '',
            products: deliveryData.products || [],
            notes: deliveryData.notes || '',
            trackingNumber: deliveryData.trackingNumber || '',
            estimatedArrival: deliveryData.estimatedArrival || '',
          });
        } catch (err: any) {
          console.error('Error fetching delivery:', err);
          setError('Failed to load delivery data. Please try again later.');
        } finally {
          setLoading(false);
        }
      };

      fetchDelivery();
    }
  }, [isEditMode, deliveryId]);

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  // Handle product selection
  const handleAddProduct = () => {
    setFormData(prev => ({
      ...prev,
      products: [...prev.products, { id: '', name: '', quantity: 0, unit: '' }]
    }));
  };

  // Handle product removal
  const handleRemoveProduct = (index: number) => {
    setFormData(prev => ({
      ...prev,
      products: prev.products.filter((_, i) => i !== index)
    }));
  };

  // Handle product field changes
  const handleProductChange = (index: number, field: string, value: string | number) => {
    setFormData(prev => {
      const updatedProducts = [...prev.products];
      if (field === 'id' && typeof value === 'string') {
        const selectedProduct = farmProducts.find(p => p.id === value);
        if (selectedProduct) {
          updatedProducts[index] = {
            ...updatedProducts[index],
            id: selectedProduct.id,
            name: selectedProduct.name,
            unit: selectedProduct.unit || 'unit'
          };
        }
      } else {
        updatedProducts[index] = {
          ...updatedProducts[index],
          [field]: value
        };
      }
      return { ...prev, products: updatedProducts };
    });
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!currentFarm) {
      setError('No farm selected. Please select a farm before creating a delivery.');
      return;
    }

    setLoading(true);
    setError(null);
    setSuccessMessage(null);

    try {
      const deliveryData = {
        ...formData,
        farmId: currentFarm.id
      };

      if (isEditMode) {
        await axios.put(`${API_URL}/deliveries/${deliveryId}`, deliveryData);
        setSuccessMessage('Delivery updated successfully!');
      } else {
        await axios.post(`${API_URL}/deliveries`, deliveryData);
        setSuccessMessage('Delivery created successfully!');
      }

      // Redirect after a short delay
      setTimeout(() => {
        navigate('/transport/deliveries');
      }, 1500);
    } catch (err: any) {
      console.error('Error saving delivery:', err);
      setError('Failed to save delivery. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Layout>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">
          {isEditMode ? 'Edit Delivery' : 'Create New Delivery'}
        </h1>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">{error}</span>
        </div>
      )}

      {successMessage && (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">{successMessage}</span>
        </div>
      )}

      <div className="bg-white shadow rounded-lg p-6">
        <form onSubmit={handleSubmit}>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div>
              <label htmlFor="deliveryDate" className="block text-sm font-medium text-gray-700 mb-1">
                Delivery Date
              </label>
              <input
                type="date"
                id="deliveryDate"
                name="deliveryDate"
                value={formData.deliveryDate}
                onChange={handleInputChange}
                className="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 rounded-md"
                required
              />
            </div>

            <div>
              <label htmlFor="estimatedArrival" className="block text-sm font-medium text-gray-700 mb-1">
                Estimated Arrival
              </label>
              <input
                type="date"
                id="estimatedArrival"
                name="estimatedArrival"
                value={formData.estimatedArrival}
                onChange={handleInputChange}
                className="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 rounded-md"
              />
            </div>

            <div>
              <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">
                Status
              </label>
              <select
                id="status"
                name="status"
                value={formData.status}
                onChange={handleInputChange}
                className="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 rounded-md"
                required
              >
                <option value="scheduled">Scheduled</option>
                <option value="in_transit">In Transit</option>
                <option value="delivered">Delivered</option>
                <option value="cancelled">Cancelled</option>
              </select>
            </div>

            <div>
              <label htmlFor="driverId" className="block text-sm font-medium text-gray-700 mb-1">
                Driver
              </label>
              <select
                id="driverId"
                name="driverId"
                value={formData.driverId}
                onChange={handleInputChange}
                className="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 rounded-md"
              >
                <option value="">Select a driver</option>
                {drivers.map(driver => (
                  <option key={driver.id} value={driver.id}>
                    {driver.firstName} {driver.lastName}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label htmlFor="destination" className="block text-sm font-medium text-gray-700 mb-1">
                Destination
              </label>
              <input
                type="text"
                id="destination"
                name="destination"
                value={formData.destination}
                onChange={handleInputChange}
                className="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 rounded-md"
                required
              />
            </div>

            <div>
              <label htmlFor="trackingNumber" className="block text-sm font-medium text-gray-700 mb-1">
                Tracking Number
              </label>
              <input
                type="text"
                id="trackingNumber"
                name="trackingNumber"
                value={formData.trackingNumber}
                onChange={handleInputChange}
                className="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 rounded-md"
              />
            </div>

            <div>
              <label htmlFor="recipientName" className="block text-sm font-medium text-gray-700 mb-1">
                Recipient Name
              </label>
              <input
                type="text"
                id="recipientName"
                name="recipientName"
                value={formData.recipientName}
                onChange={handleInputChange}
                className="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 rounded-md"
                required
              />
            </div>

            <div>
              <label htmlFor="recipientContact" className="block text-sm font-medium text-gray-700 mb-1">
                Recipient Contact
              </label>
              <input
                type="text"
                id="recipientContact"
                name="recipientContact"
                value={formData.recipientContact}
                onChange={handleInputChange}
                className="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 rounded-md"
              />
            </div>
          </div>

          <div className="mb-6">
            <div className="flex justify-between items-center mb-2">
              <label className="block text-sm font-medium text-gray-700">Products</label>
              <button
                type="button"
                onClick={handleAddProduct}
                className="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                Add Product
              </button>
            </div>
            
            {formData.products.length === 0 ? (
              <p className="text-sm text-gray-500">No products added yet.</p>
            ) : (
              <div className="space-y-3">
                {formData.products.map((product, index) => (
                  <div key={index} className="flex items-center space-x-3 p-3 border border-gray-200 rounded-md">
                    <div className="flex-grow grid grid-cols-1 md:grid-cols-3 gap-3">
                      <div>
                        <label htmlFor={`product-${index}`} className="block text-xs font-medium text-gray-700 mb-1">
                          Product
                        </label>
                        <select
                          id={`product-${index}`}
                          value={product.id}
                          onChange={(e) => handleProductChange(index, 'id', e.target.value)}
                          className="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-xs border-gray-300 rounded-md"
                        >
                          <option value="">Select a product</option>
                          {farmProducts.map(p => (
                            <option key={p.id} value={p.id}>{p.name}</option>
                          ))}
                        </select>
                      </div>
                      <div>
                        <label htmlFor={`quantity-${index}`} className="block text-xs font-medium text-gray-700 mb-1">
                          Quantity
                        </label>
                        <input
                          type="number"
                          id={`quantity-${index}`}
                          value={product.quantity}
                          onChange={(e) => handleProductChange(index, 'quantity', parseInt(e.target.value))}
                          className="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-xs border-gray-300 rounded-md"
                          min="1"
                        />
                      </div>
                      <div>
                        <label htmlFor={`unit-${index}`} className="block text-xs font-medium text-gray-700 mb-1">
                          Unit
                        </label>
                        <input
                          type="text"
                          id={`unit-${index}`}
                          value={product.unit}
                          onChange={(e) => handleProductChange(index, 'unit', e.target.value)}
                          className="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-xs border-gray-300 rounded-md"
                        />
                      </div>
                    </div>
                    <button
                      type="button"
                      onClick={() => handleRemoveProduct(index)}
                      className="inline-flex items-center p-1 border border-transparent rounded-full text-red-600 hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM7 9a1 1 0 000 2h6a1 1 0 100-2H7z" clipRule="evenodd" />
                      </svg>
                    </button>
                  </div>
                ))}
              </div>
            )}
          </div>

          <div className="mb-6">
            <label htmlFor="notes" className="block text-sm font-medium text-gray-700 mb-1">
              Notes
            </label>
            <textarea
              id="notes"
              name="notes"
              rows={3}
              value={formData.notes}
              onChange={handleInputChange}
              className="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 rounded-md"
            />
          </div>

          <div className="flex justify-end space-x-3">
            <button
              type="button"
              onClick={() => navigate('/transport/deliveries')}
              className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"
            >
              {loading ? 'Saving...' : isEditMode ? 'Update Delivery' : 'Create Delivery'}
            </button>
          </div>
        </form>
      </div>
    </Layout>
  );
};

export default DeliveryForm;