import { Routes, Route, Navigate, Outlet } from 'react-router-dom';
import { lazy, Suspense } from 'react';

// Lazy load components
const TransportDashboard = lazy(() => import('./TransportDashboard'));
const DriverList = lazy(() => import('./DriverList'));
const DriverForm = lazy(() => import('./DriverForm'));
const DriverDetail = lazy(() => import('./DriverDetail'));
const DeliveryList = lazy(() => import('./DeliveryList'));
const DeliveryForm = lazy(() => import('./DeliveryForm'));
const DeliveryDetail = lazy(() => import('./DeliveryDetail'));
const PickupList = lazy(() => import('./PickupList'));
const PickupForm = lazy(() => import('./PickupForm'));
const PickupDetail = lazy(() => import('./PickupDetail'));
const ScheduleList = lazy(() => import('./ScheduleList'));
const ScheduleForm = lazy(() => import('./ScheduleForm'));
const ScheduleDetail = lazy(() => import('./ScheduleDetail'));
const DriverLocationMap = lazy(() => import('./DriverLocationMap'));

// Loading component
const ComponentLoader = () => (
  <div className="flex items-center justify-center h-64">
    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
    <p className="ml-3 text-gray-500">Loading...</p>
  </div>
);

// Transport Routes component
const TransportRoutes = () => {
  return (
    <Routes>
      <Route path="/" element={
        <Suspense fallback={<ComponentLoader />}>
          <TransportDashboard />
        </Suspense>
      } />

      {/* Driver Routes */}
      <Route path="/drivers" element={
        <Suspense fallback={<ComponentLoader />}>
          <DriverList />
        </Suspense>
      } />
      <Route path="/drivers/new" element={
        <Suspense fallback={<ComponentLoader />}>
          <DriverForm />
        </Suspense>
      } />
      <Route path="/drivers/:driverId" element={
        <Suspense fallback={<ComponentLoader />}>
          <DriverDetail />
        </Suspense>
      } />
      <Route path="/drivers/:driverId/edit" element={
        <Suspense fallback={<ComponentLoader />}>
          <DriverForm />
        </Suspense>
      } />

      {/* Delivery Routes */}
      <Route path="/deliveries" element={
        <Suspense fallback={<ComponentLoader />}>
          <DeliveryList />
        </Suspense>
      } />
      <Route path="/deliveries/new" element={
        <Suspense fallback={<ComponentLoader />}>
          <DeliveryForm />
        </Suspense>
      } />
      <Route path="/deliveries/:deliveryId" element={
        <Suspense fallback={<ComponentLoader />}>
          <DeliveryDetail />
        </Suspense>
      } />
      <Route path="/deliveries/:deliveryId/edit" element={
        <Suspense fallback={<ComponentLoader />}>
          <DeliveryForm />
        </Suspense>
      } />

      {/* Pickup Routes */}
      <Route path="/pickups" element={
        <Suspense fallback={<ComponentLoader />}>
          <PickupList />
        </Suspense>
      } />
      <Route path="/pickups/new" element={
        <Suspense fallback={<ComponentLoader />}>
          <PickupForm />
        </Suspense>
      } />
      <Route path="/pickups/:pickupId" element={
        <Suspense fallback={<ComponentLoader />}>
          <PickupDetail />
        </Suspense>
      } />
      <Route path="/pickups/:pickupId/edit" element={
        <Suspense fallback={<ComponentLoader />}>
          <PickupForm />
        </Suspense>
      } />

      {/* Schedule Routes */}
      <Route path="/schedules" element={
        <Suspense fallback={<ComponentLoader />}>
          <ScheduleList />
        </Suspense>
      } />
      <Route path="/schedules/new" element={
        <Suspense fallback={<ComponentLoader />}>
          <ScheduleForm />
        </Suspense>
      } />
      <Route path="/schedules/:scheduleId" element={
        <Suspense fallback={<ComponentLoader />}>
          <ScheduleDetail />
        </Suspense>
      } />
      <Route path="/schedules/:scheduleId/edit" element={
        <Suspense fallback={<ComponentLoader />}>
          <ScheduleForm />
        </Suspense>
      } />

      {/* Driver Location Map */}
      <Route path="/locations" element={
        <Suspense fallback={<ComponentLoader />}>
          <DriverLocationMap />
        </Suspense>
      } />

      {/* Default redirect */}
      <Route path="*" element={<Navigate to="/" replace />} />
    </Routes>
  );
};

// Default export for the routes
export default TransportRoutes;
