import { useState, useEffect, useContext } from 'react';
import { Link } from 'react-router-dom';
import { AuthContext } from '../../context/AuthContext';
import { useFarm } from '../../context/FarmContext';
import Layout from '../../components/Layout';
import { getFarmIoTDevices, deleteIoTDevice } from '../../services/iotService';
import { IoTDevice } from '../../services/iotService';

const IoTDeviceList = () => {
  const [devices, setDevices] = useState<IoTDevice[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const { user } = useContext(AuthContext);
  const { selectedFarm } = useFarm();

  // Fetch IoT devices for the selected farm
  useEffect(() => {
    const fetchDevices = async () => {
      if (!selectedFarm) return;

      setLoading(true);
      setError(null);

      try {
        const response = await getFarmIoTDevices(selectedFarm.id);
        setDevices(response.devices || []);
      } catch (err: any) {
        console.error('Error fetching IoT devices:', err);
        setError('Failed to load IoT devices. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchDevices();
  }, [selectedFarm]);

  // Handle device deletion
  const handleDeleteDevice = async (deviceId: string) => {
    if (!window.confirm('Are you sure you want to delete this IoT device?')) {
      return;
    }

    try {
      await deleteIoTDevice(deviceId);
      setDevices(devices.filter(device => device.id !== deviceId));
    } catch (err: any) {
      console.error('Error deleting IoT device:', err);
      setError('Failed to delete IoT device. Please try again later.');
    }
  };

  // Format date
  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Get status color class
  const getStatusColorClass = (status: string) => {
    switch (status.toLowerCase()) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'inactive':
        return 'bg-red-100 text-red-800';
      case 'maintenance':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Get battery level color class
  const getBatteryLevelColorClass = (level: number | null) => {
    if (level === null) return 'bg-gray-100 text-gray-800';
    if (level <= 20) return 'bg-red-100 text-red-800';
    if (level <= 50) return 'bg-yellow-100 text-yellow-800';
    return 'bg-green-100 text-green-800';
  };

  return (
    <Layout>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">IoT Device Management</h1>
        <div className="flex space-x-2">
          <Link
            to="/iot/cameras"
            className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Camera Dashboard
          </Link>
          <Link
            to="/iot/new"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Add IoT Device
          </Link>
        </div>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">{error}</span>
        </div>
      )}

      {!selectedFarm ? (
        <div className="bg-white shadow rounded-lg p-8 text-center">
          <p className="text-gray-500 mb-4">Please select a farm to view IoT devices.</p>
          <p className="text-sm text-gray-400 mb-6">
            Use the farm selector in the header to choose a farm.
          </p>
        </div>
      ) : loading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
          <p className="ml-3 text-gray-500">Loading IoT devices...</p>
        </div>
      ) : devices.length === 0 ? (
        <div className="bg-white shadow rounded-lg p-8 text-center">
          <p className="text-gray-500 mb-4">No IoT devices found for this farm.</p>
          <p className="text-sm text-gray-400 mb-6">
            Add your first IoT device to start monitoring your farm with sensors and connected devices.
          </p>
          <Link
            to="/iot/new"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Add IoT Device
          </Link>
        </div>
      ) : (
        <div className="bg-white shadow overflow-hidden sm:rounded-md">
          <ul className="divide-y divide-gray-200">
            {devices.map((device) => (
              <li key={device.id}>
                <div className="px-4 py-4 sm:px-6">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className="ml-3">
                        <p className="text-sm font-medium text-primary-600 truncate">
                          {device.name}
                        </p>
                        <p className="text-sm text-gray-500">
                          <span className="mr-2">Type: {device.device_type}</span>
                          {device.manufacturer && <span className="mr-2">Manufacturer: {device.manufacturer}</span>}
                          {device.model && <span className="mr-2">Model: {device.model}</span>}
                        </p>
                      </div>
                    </div>
                    <div className="flex space-x-2">
                      <Link
                        to={`/iot/${device.id}`}
                        className="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                      >
                        View
                      </Link>
                      <Link
                        to={`/iot/${device.id}/edit`}
                        className="inline-flex items-center px-3 py-1 border border-gray-300 text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                      >
                        Edit
                      </Link>
                      <button
                        onClick={() => handleDeleteDevice(device.id)}
                        className="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                      >
                        Delete
                      </button>
                    </div>
                  </div>
                  <div className="mt-2 sm:flex sm:justify-between">
                    <div className="sm:flex">
                      {device.location_description && (
                        <p className="flex items-center text-sm text-gray-500 mr-6">
                          <span>Location: {device.location_description}</span>
                        </p>
                      )}
                      {device.last_communication && (
                        <p className="mt-2 flex items-center text-sm text-gray-500 sm:mt-0 mr-6">
                          <span>Last Communication: {formatDate(device.last_communication)}</span>
                        </p>
                      )}
                    </div>
                    <div className="mt-2 flex items-center space-x-2 text-sm text-gray-500 sm:mt-0">
                      {device.status && (
                        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusColorClass(device.status)}`}>
                          {device.status.charAt(0).toUpperCase() + device.status.slice(1)}
                        </span>
                      )}
                      {device.battery_level !== null && (
                        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getBatteryLevelColorClass(device.battery_level)}`}>
                          Battery: {device.battery_level}%
                        </span>
                      )}
                      {device.signal_strength !== null && (
                        <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                          Signal: {device.signal_strength}%
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              </li>
            ))}
          </ul>
        </div>
      )}
    </Layout>
  );
};

export default IoTDeviceList;
