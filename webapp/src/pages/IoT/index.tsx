import { Routes, Route } from 'react-router-dom';
import IoTDeviceList from './IoTDeviceList';
import IoTDeviceDetail from './IoTDeviceDetail';
import IoTDeviceForm from './IoTDeviceForm';
import CameraDashboard from './CameraDashboard';

const IoTRoutes = () => {
  return (
    <Routes>
      <Route path="/" element={<IoTDeviceList />} />
      <Route path="/cameras" element={<CameraDashboard />} />
      <Route path="/new" element={<IoTDeviceForm />} />
      <Route path="/:deviceId" element={<IoTDeviceDetail />} />
      <Route path="/:deviceId/edit" element={<IoTDeviceForm />} />
    </Routes>
  );
};

export default IoTRoutes;
