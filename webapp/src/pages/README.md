# Error Pages

This directory contains various error pages that can be used throughout the application to provide a better user experience when errors occur.

## Available Error Pages

- **NotFound (404)**: Displayed when a user navigates to a route that doesn't exist.
- **Unauthorized (401)**: Displayed when a user tries to access a protected route without being authenticated.
- **Forbidden (403)**: Displayed when a user is authenticated but doesn't have permission to access a resource.
- **ServerError (500)**: Displayed when there's a server error.
- **MaintenancePage**: Displayed when the system is under maintenance.

## How to Use

### Direct Navigation

You can navigate directly to these pages using the following routes:

- `/not-found` - 404 Not Found page
- `/unauthorized` - 401 Unauthorized page
- `/forbidden` - 403 Forbidden page
- `/server-error` - 500 Server Error page
- `/maintenance` - Maintenance page

### Programmatic Navigation

You can also navigate to these pages programmatically using the `navigate` function from `react-router-dom`:

```tsx
import { useNavigate } from 'react-router-dom';

const MyComponent = () => {
  const navigate = useNavigate();

  const handleSomeAction = () => {
    // Navigate to the error page
    navigate('/unauthorized');
  };

  return (
    // Component JSX
  );
};
```

### Error Handling Utilities

For API calls, you can use the error handling utilities in `src/utils/errorHandler.ts` to automatically redirect to the appropriate error page based on the error status code:

```tsx
import { useNavigate } from 'react-router-dom';
import { handleApiError, redirectIfMaintenance } from '../utils/errorHandler';

const MyComponent = () => {
  const navigate = useNavigate();

  const fetchData = async () => {
    try {
      // Check if the server is in maintenance mode
      await redirectIfMaintenance(navigate);

      // Make the API call
      const response = await axios.get('/api/some-endpoint');
      return response.data;
    } catch (error) {
      // Handle the error and get the error message
      const errorMessage = handleApiError(error, navigate, 'Failed to fetch data');
      // You can display the error message in your component
      console.error(errorMessage);
      return null;
    }
  };

  return (
    // Component JSX
  );
};
```

See the example component in `src/examples/ErrorHandlingExample.tsx` for a complete example of how to use the error handling utilities.

## Customizing Error Pages

Each error page can be customized by modifying the corresponding component in its directory. The pages are designed to be consistent with the rest of the application, using the same styling and layout.