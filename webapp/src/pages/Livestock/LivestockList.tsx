import { useState, useEffect, useContext } from 'react';
import { Link } from 'react-router-dom';
import axios from 'axios';
import { AuthContext } from '../../context/AuthContext';
import { useFarm } from '../../context/FarmContext';
import Layout from '../../components/Layout';
import { API_URL } from '../../config';

interface Livestock {
  id: string;
  type: string;
  breed: string;
  quantity: number;
  acquisition_date: string;
  acquisition_cost: number;
  status: string;
  notes: string;
}

const LivestockList = () => {
  const [livestock, setLivestock] = useState<Livestock[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const { user } = useContext(AuthContext);
  const { selectedFarm } = useFarm();

  // Fetch livestock for the selected farm
  useEffect(() => {
    const fetchLivestock = async () => {
      if (!selectedFarm) return;

      setLoading(true);
      setError(null);

      try {
        const response = await axios.get(`${API_URL}/livestock/farm/${selectedFarm.id}`);
        setLivestock(Array.isArray(response.data) ? response.data : response.data.livestock || []);
      } catch (err: any) {
        console.error('Error fetching livestock:', err);
        setError('Failed to load livestock. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchLivestock();
  }, [selectedFarm]);

  // Handle livestock deletion
  const handleDeleteLivestock = async (livestockId: string) => {
    if (!window.confirm('Are you sure you want to delete this livestock?')) {
      return;
    }

    try {
      await axios.delete(`${API_URL}/livestock/${livestockId}`);
      setLivestock(livestock.filter(item => item.id !== livestockId));
    } catch (err: any) {
      console.error('Error deleting livestock:', err);
      setError('Failed to delete livestock. Please try again later.');
    }
  };

  // Create a product from livestock
  const handleCreateProduct = async (livestockId: string) => {
    try {
      const response = await axios.post(`${API_URL}/products/from-livestock/${livestockId}`, {
        price: 0, // Default price, can be updated later
        unit: 'head' // Default unit for livestock
      });

      alert('Product created successfully!');
    } catch (err: any) {
      console.error('Error creating product from livestock:', err);
      setError('Failed to create product. Please try again later.');
    }
  };

  // Format currency
  const formatCurrency = (amount: number | null) => {
    if (amount === null) return 'N/A';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  // Format date
  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <Layout>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Livestock Management</h1>
        <div className="flex space-x-3">
          <Link
            to="/vets"
            className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Manage Vets
          </Link>
          <Link
            to="/livestock/new"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Add Livestock
          </Link>
        </div>
      </div>

      {/* Display selected farm */}
      <div className="mb-6">
        <p className="text-sm text-gray-500">
          Showing livestock for farm: <span className="font-medium text-gray-700">{selectedFarm ? selectedFarm.name : 'No farm selected'}</span>
        </p>
        {!selectedFarm && (
          <p className="text-sm text-red-500 mt-1">
            Please select a farm from the header dropdown to view livestock.
          </p>
        )}
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">{error}</span>
        </div>
      )}

      {loading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
          <p className="ml-3 text-gray-500">Loading livestock...</p>
        </div>
      ) : livestock.length === 0 ? (
        <div className="bg-white shadow rounded-lg p-8 text-center">
          <p className="text-gray-500 mb-4">No livestock found for this farm.</p>
          <p className="text-sm text-gray-400 mb-6">
            Add your first livestock to start tracking your animals, health records, and production data.
          </p>
          <Link
            to="/livestock/new"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Add Livestock
          </Link>
        </div>
      ) : (
        <div className="bg-white shadow overflow-hidden sm:rounded-md">
          <ul className="divide-y divide-gray-200">
            {livestock.map((item) => (
              <li key={item.id}>
                <div className="px-4 py-4 sm:px-6">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className="ml-3">
                        <p className="text-sm font-medium text-primary-600 truncate">
                          {item.type} {item.breed && `- ${item.breed}`}
                        </p>
                        <p className="text-sm text-gray-500">
                          <span className="mr-2">Quantity: {item.quantity}</span>
                          {item.acquisition_date && (
                            <span className="mr-2">Acquired: {formatDate(item.acquisition_date)}</span>
                          )}
                          {item.acquisition_cost && (
                            <span>Cost: {formatCurrency(item.acquisition_cost)}</span>
                          )}
                        </p>
                      </div>
                    </div>
                    <div className="flex space-x-2">
                      <button
                        onClick={() => handleCreateProduct(item.id)}
                        className="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                      >
                        Create Product
                      </button>
                      <Link
                        to={`/livestock/${item.id}`}
                        className="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                      >
                        View
                      </Link>
                      <Link
                        to={`/livestock/${item.id}/edit`}
                        className="inline-flex items-center px-3 py-1 border border-gray-300 text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                      >
                        Edit
                      </Link>
                      <button
                        onClick={() => handleDeleteLivestock(item.id)}
                        className="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                      >
                        Delete
                      </button>
                    </div>
                  </div>
                  {item.status && (
                    <div className="mt-2">
                      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                        item.status === 'active' ? 'bg-green-100 text-green-800' : 
                        item.status === 'sold' ? 'bg-blue-100 text-blue-800' : 
                        item.status === 'deceased' ? 'bg-red-100 text-red-800' : 
                        'bg-gray-100 text-gray-800'
                      }`}>
                        {item.status.charAt(0).toUpperCase() + item.status.slice(1)}
                      </span>
                    </div>
                  )}
                  {item.notes && (
                    <div className="mt-2 text-sm text-gray-500">
                      <p>{item.notes}</p>
                    </div>
                  )}
                </div>
              </li>
            ))}
          </ul>
        </div>
      )}
    </Layout>
  );
};

export default LivestockList;
