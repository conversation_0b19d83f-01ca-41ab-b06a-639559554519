import { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, Link, useNavigate } from 'react-router-dom';
import axios from 'axios';
import Layout from '../../components/Layout';
import { API_URL } from '../../config';
import { toast } from 'react-toastify';

interface Livestock {
  id: string;
  farm_id: string;
  type: string;
  breed: string;
  quantity: number;
  acquisition_date: string;
  acquisition_cost: number;
  status: string;
  notes: string;
  created_at: string;
  updated_at: string;
}

interface Farm {
  id: string;
  name: string;
}

interface Product {
  id: string;
  name: string;
  price: number;
  unit: string;
  created_at: string;
}

interface HealthMetrics {
  weight_trend: string;
  feed_consumption: string;
  growth_rate: string;
  overall_condition: string;
}

interface DiseaseRisk {
  current_risk: string;
  preventive_measures: string;
}

interface NutritionAssessment {
  feed_quality: string;
  dietary_balance: string;
  supplement_needs: string;
}

interface IdentifiedIssue {
  issue: string;
  description: string;
  priority: string;
}

interface AIRecommendation {
  action: string;
  details: string;
  expected_impact: string;
}

interface AIHerdHealthAnalysis {
  id: string;
  farm_id: string;
  livestock_group_id: string | null;
  health_metrics: HealthMetrics;
  disease_risk: DiseaseRisk;
  nutrition_assessment: NutritionAssessment;
  issues_identified: IdentifiedIssue[];
  recommendations: AIRecommendation[];
  priority_level: string;
  expected_impact: string;
  confidence_score: number;
  analysis_date: string;
  is_applied: boolean;
}

const LivestockDetail = () => {
  const { livestockId } = useParams<{ livestockId: string }>();
  const navigate = useNavigate();

  const [livestock, setLivestock] = useState<Livestock | null>(null);
  const [farm, setFarm] = useState<Farm | null>(null);
  const [products, setProducts] = useState<Product[]>([]);
  const [aiAnalysis, setAiAnalysis] = useState<AIHerdHealthAnalysis | null>(null);
  const [loadingAI, setLoadingAI] = useState<boolean>(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch livestock data
  useEffect(() => {
    const fetchLivestock = async () => {
      if (!livestockId) return;

      setLoading(true);
      setError(null);

      try {
        // Fetch livestock details
        const livestockResponse = await axios.get(`${API_URL}/livestock/${livestockId}`);
        setLivestock(livestockResponse.data);

        // Fetch farm details
        const farmResponse = await axios.get(`${API_URL}/farms/${livestockResponse.data.farm_id}`);
        setFarm(farmResponse.data);

        // Fetch products associated with this livestock
        const productsResponse = await axios.get(`${API_URL}/products/farm/${livestockResponse.data.farm_id}`);
        // Filter products that are associated with this livestock
        const livestockProducts = productsResponse.data.filter((product: any) => 
          product.type === 'livestock' && product.source_id === livestockId
        );
        setProducts(livestockProducts);

        // Fetch AI herd health analysis
        try {
          const aiAnalysisResponse = await axios.get(`${API_URL}/ai-analysis/herd-health/${livestockResponse.data.farm_id}`, {
            params: { livestockGroupId: livestockId }
          });

          if (aiAnalysisResponse.data.analysis) {
            setAiAnalysis(aiAnalysisResponse.data.analysis);
          } else {
            setAiAnalysis(null);
          }
        } catch (aiErr) {
          console.error('Error fetching AI herd health analysis:', aiErr);
          // Don't set an error for the whole page if just the AI analysis fails
        }
      } catch (err: any) {
        console.error('Error fetching livestock details:', err);
        setError('Failed to load livestock details. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchLivestock();
  }, [livestockId]);

  // Function to generate AI herd health analysis
  const generateAIAnalysis = async () => {
    if (!livestock || !farm) return;

    setLoadingAI(true);

    try {
      const response = await axios.post(`${API_URL}/ai-analysis/herd-health`, {
        farmId: livestock.farm_id,
        livestockGroupId: livestockId,
        herdData: {
          type: livestock.type,
          breed: livestock.breed,
          quantity: livestock.quantity,
          status: livestock.status,
          notes: livestock.notes
        }
      });

      if (response.data.analysis) {
        setAiAnalysis(response.data.analysis);
        toast.success('AI herd health analysis generated successfully');
      }
    } catch (err: any) {
      console.error('Error generating AI herd health analysis:', err);
      toast.error('Failed to generate AI herd health analysis. Please try again later.');
    } finally {
      setLoadingAI(false);
    }
  };

  // Function to apply AI herd health analysis
  const applyAIAnalysis = async () => {
    if (!aiAnalysis) return;

    setLoadingAI(true);

    try {
      const response = await axios.put(`${API_URL}/ai-analysis/herd-health/${aiAnalysis.id}`, {
        isApplied: true
      });

      if (response.data.analysis) {
        setAiAnalysis(response.data.analysis);
        toast.success('AI herd health analysis applied successfully');
      }
    } catch (err: any) {
      console.error('Error applying AI herd health analysis:', err);
      toast.error('Failed to apply AI herd health analysis. Please try again later.');
    } finally {
      setLoadingAI(false);
    }
  };

  // Handle livestock deletion
  const handleDeleteLivestock = async () => {
    if (!window.confirm('Are you sure you want to delete this livestock?')) {
      return;
    }

    try {
      await axios.delete(`${API_URL}/livestock/${livestockId}`);
      navigate('/livestock');
    } catch (err: any) {
      console.error('Error deleting livestock:', err);
      setError('Failed to delete livestock. Please try again later.');
    }
  };

  // Create a product from this livestock
  const handleCreateProduct = async () => {
    try {
      const response = await axios.post(`${API_URL}/products/from-livestock/${livestockId}`, {
        price: 0, // Default price, can be updated later
        unit: 'head' // Default unit for livestock
      });

      // Add the new product to the products list
      setProducts([...products, response.data]);

      alert('Product created successfully!');
    } catch (err: any) {
      console.error('Error creating product from livestock:', err);
      setError('Failed to create product. Please try again later.');
    }
  };

  // Format currency
  const formatCurrency = (amount: number | null) => {
    if (amount === null) return 'N/A';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  // Format date
  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (loading) {
    return (
      <Layout>
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
          <p className="ml-3 text-gray-500">Loading livestock details...</p>
        </div>
      </Layout>
    );
  }

  if (error) {
    return (
      <Layout>
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">{error}</span>
        </div>
        <div className="flex justify-center mt-4">
          <Link
            to="/livestock"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Back to Livestock
          </Link>
        </div>
      </Layout>
    );
  }

  if (!livestock) {
    return (
      <Layout>
        <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">Livestock not found.</span>
        </div>
        <div className="flex justify-center mt-4">
          <Link
            to="/livestock"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Back to Livestock
          </Link>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">
          {livestock.type.charAt(0).toUpperCase() + livestock.type.slice(1)} {livestock.breed && `- ${livestock.breed}`}
        </h1>
        <div className="flex space-x-2">
          <button
            onClick={handleCreateProduct}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
          >
            Create Product
          </button>
          <Link
            to={`/livestock/${livestockId}/edit`}
            className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Edit
          </Link>
          <button
            onClick={handleDeleteLivestock}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
          >
            Delete
          </button>
          <Link
            to="/livestock"
            className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Back to Livestock
          </Link>
        </div>
      </div>

      <div className="bg-white shadow overflow-hidden sm:rounded-lg mb-6">
        <div className="px-4 py-5 sm:px-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900">Livestock Details</h3>
          <p className="mt-1 max-w-2xl text-sm text-gray-500">Details and information about the livestock.</p>
        </div>
        <div className="border-t border-gray-200">
          <dl>
            <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Farm</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                {farm ? farm.name : 'Unknown Farm'}
              </dd>
            </div>
            <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Type</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                {livestock.type.charAt(0).toUpperCase() + livestock.type.slice(1)}
              </dd>
            </div>
            <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Breed</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                {livestock.breed || 'Not specified'}
              </dd>
            </div>
            <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Quantity</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                {livestock.quantity}
              </dd>
            </div>
            <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Acquisition Date</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                {livestock.acquisition_date ? formatDate(livestock.acquisition_date) : 'Not specified'}
              </dd>
            </div>
            <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Acquisition Cost</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                {livestock.acquisition_cost ? formatCurrency(livestock.acquisition_cost) : 'Not specified'}
              </dd>
            </div>
            <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Status</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                  livestock.status === 'active' ? 'bg-green-100 text-green-800' : 
                  livestock.status === 'sold' ? 'bg-blue-100 text-blue-800' : 
                  livestock.status === 'deceased' ? 'bg-red-100 text-red-800' : 
                  'bg-gray-100 text-gray-800'
                }`}>
                  {livestock.status.charAt(0).toUpperCase() + livestock.status.slice(1)}
                </span>
              </dd>
            </div>
            <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Notes</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                {livestock.notes || 'No notes'}
              </dd>
            </div>
            <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Created</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                {formatDate(livestock.created_at)}
              </dd>
            </div>
            <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Last Updated</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                {formatDate(livestock.updated_at)}
              </dd>
            </div>
          </dl>
        </div>
      </div>

      {/* Products Section */}
      <div className="bg-white shadow overflow-hidden sm:rounded-lg mb-6">
        <div className="px-4 py-5 sm:px-6 flex justify-between items-center">
          <div>
            <h3 className="text-lg leading-6 font-medium text-gray-900">Products</h3>
            <p className="mt-1 max-w-2xl text-sm text-gray-500">Products created from this livestock.</p>
          </div>
          <button
            onClick={handleCreateProduct}
            className="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
          >
            Create Product
          </button>
        </div>
        <div className="border-t border-gray-200">
          {products.length === 0 ? (
            <div className="px-4 py-5 sm:px-6 text-center">
              <p className="text-sm text-gray-500">No products have been created from this livestock yet.</p>
            </div>
          ) : (
            <ul className="divide-y divide-gray-200">
              {products.map(product => (
                <li key={product.id} className="px-4 py-4 sm:px-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-primary-600">{product.name}</p>
                      <p className="text-sm text-gray-500">
                        Price: {formatCurrency(product.price)} / {product.unit || 'unit'}
                      </p>
                      <p className="text-xs text-gray-400">
                        Created: {formatDate(product.created_at)}
                      </p>
                    </div>
                    <Link
                      to={`/products/${product.id}`}
                      className="inline-flex items-center px-3 py-1 border border-gray-300 text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                    >
                      View Product
                    </Link>
                  </div>
                </li>
              ))}
            </ul>
          )}
        </div>
      </div>

      {/* AI Herd Health Analysis */}
      <div className="bg-white shadow overflow-hidden sm:rounded-lg mb-6">
        <div className="px-4 py-5 sm:px-6 flex justify-between items-center">
          <div>
            <h3 className="text-lg leading-6 font-medium text-gray-900">
              AI Herd Health Analysis
            </h3>
            <p className="mt-1 max-w-2xl text-sm text-gray-500">
              Advanced analysis of herd health using artificial intelligence.
            </p>
          </div>
          <div className="flex space-x-2">
            <button
              onClick={generateAIAnalysis}
              disabled={loadingAI}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"
            >
              {loadingAI ? (
                <>
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Processing...
                </>
              ) : (
                'Generate Analysis'
              )}
            </button>
            {aiAnalysis && !aiAnalysis.is_applied && (
              <button
                onClick={applyAIAnalysis}
                disabled={loadingAI}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50"
              >
                {loadingAI ? 'Applying...' : 'Apply Analysis'}
              </button>
            )}
          </div>
        </div>

        <div className="border-t border-gray-200">
          {loadingAI ? (
            <div className="flex justify-center items-center h-64">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500"></div>
            </div>
          ) : !aiAnalysis ? (
            <div className="px-4 py-5 sm:px-6">
              <p className="text-sm text-gray-500">
                No AI analysis available for this livestock. Click "Generate Analysis" to create one.
              </p>
            </div>
          ) : (
            <dl>
              <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt className="text-sm font-medium text-gray-500">Analysis Date</dt>
                <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                  {formatDate(aiAnalysis.analysis_date)}
                </dd>
              </div>

              <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt className="text-sm font-medium text-gray-500">Status</dt>
                <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${aiAnalysis.is_applied ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}`}>
                    {aiAnalysis.is_applied ? 'Applied' : 'Not Applied'}
                  </span>
                </dd>
              </div>

              <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt className="text-sm font-medium text-gray-500">Confidence Score</dt>
                <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                  <div className="flex items-center">
                    <span className="mr-2">{aiAnalysis.confidence_score.toFixed(1)}%</span>
                    <div className="relative w-24 h-2 bg-gray-200 rounded">
                      <div 
                        style={{ width: `${aiAnalysis.confidence_score}%` }} 
                        className="absolute top-0 h-2 rounded bg-blue-500">
                      </div>
                    </div>
                  </div>
                </dd>
              </div>

              <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt className="text-sm font-medium text-gray-500">Health Metrics</dt>
                <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                  <div className="flex flex-col space-y-1">
                    <div>
                      <span className="font-medium">Weight Trend:</span> {aiAnalysis.health_metrics.weight_trend}
                    </div>
                    <div>
                      <span className="font-medium">Feed Consumption:</span> {aiAnalysis.health_metrics.feed_consumption}
                    </div>
                    <div>
                      <span className="font-medium">Growth Rate:</span> {aiAnalysis.health_metrics.growth_rate}
                    </div>
                    <div>
                      <span className="font-medium">Overall Condition:</span> {aiAnalysis.health_metrics.overall_condition}
                    </div>
                  </div>
                </dd>
              </div>

              <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt className="text-sm font-medium text-gray-500">Disease Risk</dt>
                <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                  <div className="flex flex-col space-y-1">
                    <div>
                      <span className="font-medium">Current Risk:</span> {aiAnalysis.disease_risk.current_risk}
                    </div>
                    <div>
                      <span className="font-medium">Preventive Measures:</span> {aiAnalysis.disease_risk.preventive_measures}
                    </div>
                  </div>
                </dd>
              </div>

              <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt className="text-sm font-medium text-gray-500">Nutrition Assessment</dt>
                <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                  <div className="flex flex-col space-y-1">
                    <div>
                      <span className="font-medium">Feed Quality:</span> {aiAnalysis.nutrition_assessment.feed_quality}
                    </div>
                    <div>
                      <span className="font-medium">Dietary Balance:</span> {aiAnalysis.nutrition_assessment.dietary_balance}
                    </div>
                    <div>
                      <span className="font-medium">Supplement Needs:</span> {aiAnalysis.nutrition_assessment.supplement_needs}
                    </div>
                  </div>
                </dd>
              </div>

              <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt className="text-sm font-medium text-gray-500">Priority Level</dt>
                <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                    aiAnalysis.priority_level === 'High' ? 'bg-red-100 text-red-800' :
                    aiAnalysis.priority_level === 'Medium' ? 'bg-yellow-100 text-yellow-800' :
                    'bg-green-100 text-green-800'
                  }`}>
                    {aiAnalysis.priority_level}
                  </span>
                </dd>
              </div>

              <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt className="text-sm font-medium text-gray-500">Expected Impact</dt>
                <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                  {aiAnalysis.expected_impact}
                </dd>
              </div>
            </dl>
          )}
        </div>
      </div>

      {/* AI Identified Issues */}
      {aiAnalysis && aiAnalysis.issues_identified.length > 0 && (
        <div className="bg-white shadow overflow-hidden sm:rounded-lg mb-6">
          <div className="px-4 py-5 sm:px-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900">
              AI Identified Issues
            </h3>
            <p className="mt-1 max-w-2xl text-sm text-gray-500">
              Issues identified by AI analysis that may require attention.
            </p>
          </div>
          <div className="border-t border-gray-200 px-4 py-5">
            <div className="space-y-4">
              {aiAnalysis.issues_identified.map((issue, index) => (
                <div key={index} className={`p-4 border rounded-md ${
                  issue.priority === 'High' ? 'bg-red-100 text-red-800 border-red-200' :
                  issue.priority === 'Medium' ? 'bg-yellow-100 text-yellow-800 border-yellow-200' :
                  'bg-green-100 text-green-800 border-green-200'
                }`}>
                  <div className="flex justify-between items-start">
                    <h5 className="text-sm font-medium">{issue.issue}</h5>
                    <span className="text-xs font-medium uppercase">{issue.priority} Priority</span>
                  </div>
                  <p className="mt-2 text-sm">{issue.description}</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* AI Recommendations */}
      {aiAnalysis && aiAnalysis.recommendations.length > 0 && (
        <div className="bg-white shadow overflow-hidden sm:rounded-lg">
          <div className="px-4 py-5 sm:px-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900">
              AI Recommendations
            </h3>
            <p className="mt-1 max-w-2xl text-sm text-gray-500">
              Recommended actions based on AI analysis.
            </p>
          </div>
          <div className="border-t border-gray-200 px-4 py-5">
            <div className="space-y-4">
              {aiAnalysis.recommendations.map((recommendation, index) => (
                <div key={index} className="p-4 border rounded-md bg-blue-50 text-blue-800 border-blue-200">
                  <h5 className="text-sm font-medium">{recommendation.action}</h5>
                  <p className="mt-2 text-sm">{recommendation.details}</p>
                  <div className="mt-2 text-xs text-blue-600">
                    <span className="font-medium">Expected Impact:</span> {recommendation.expected_impact}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </Layout>
  );
};

export default LivestockDetail;
