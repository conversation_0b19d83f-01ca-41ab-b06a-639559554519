import { useState, useEffect, useContext } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { AuthContext } from '../../context/AuthContext';
import { useFarm } from '../../context/FarmContext';
import Layout from '../../components/Layout';
import ErrorDisplay from '../../components/ui/ErrorDisplay';
import { 
  getReceipts, 
  deleteReceipt, 
  downloadReceipt, 
  formatDate,
  Receipt,
  getReceiptSummary,
  ReceiptSummary,
  generateExpenseReport
} from '../../services/receiptService';

const ReceiptList = () => {
  const [receipts, setReceipts] = useState<Receipt[]>([]);
  const [summary, setSummary] = useState<ReceiptSummary | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | any | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [categoryFilter, setCategoryFilter] = useState<string>('');
  const [page, setPage] = useState(1);
  const [limit] = useState(20);
  const [totalPages, setTotalPages] = useState(1);
  const [totalReceipts, setTotalReceipts] = useState(0);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  // States for expense report generation
  const [reportLoading, setReportLoading] = useState(false);
  const [reportData, setReportData] = useState<any>(null);
  const [reportModalOpen, setReportModalOpen] = useState(false);

  const { user } = useContext(AuthContext);
  const { currentFarm } = useFarm();
  const navigate = useNavigate();

  // Fetch receipts
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      setError(null);

      try {
        if (!currentFarm?.id && !user?.farm_id) {
          setError('No farm selected. Please select a farm to view receipts.');
          setLoading(false);
          return;
        }

        const farmId = currentFarm?.id || user?.farm_id;

        // Fetch receipts
        const response = await getReceipts(farmId, {
          search: searchQuery,
          status: statusFilter !== 'all' ? statusFilter : undefined,
          category: categoryFilter || undefined,
          page,
          limit,
          sortBy: 'receipt_date',
          sortOrder: 'desc'
        });

        setReceipts(response.receipts || []);
        setTotalPages(response.pagination?.totalPages || 1);
        setTotalReceipts(response.pagination?.total || 0);

        // Fetch summary
        const summaryData = await getReceiptSummary(farmId);
        setSummary(summaryData);
      } catch (err: any) {
        console.error('Error fetching receipts:', err);

        // Check if the error has structured error information
        if (err.structuredError) {
          setError(err.structuredError);
        } else {
          const errorMessage = err.response?.data?.error || 'Failed to load receipts. Please try again later.';
          setError(errorMessage);
        }
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [currentFarm?.id, user?.farm_id, searchQuery, statusFilter, categoryFilter, page, limit]);

  // Handle receipt actions
  const handleViewReceipt = (receipt: Receipt) => {
    navigate(`/receipts/view/${receipt.id}`);
  };

  const handleDownloadReceipt = (receipt: Receipt) => {
    if (receipt.file_path) {
      downloadReceipt(receipt.id);
    } else {
      setError('This receipt does not have an attached file.');
    }
  };

  const handleEditReceipt = (receipt: Receipt) => {
    navigate(`/receipts/edit/${receipt.id}`);
  };

  const handleDeleteReceipt = async (receipt: Receipt) => {
    try {
      await deleteReceipt(receipt.id);
      setReceipts(prev => prev.filter(r => r.id !== receipt.id));
      setSuccessMessage('Receipt deleted successfully');
      setTimeout(() => setSuccessMessage(null), 3000);
    } catch (err: any) {
      console.error('Error deleting receipt:', err);

      // Check if the error has structured error information
      if (err.structuredError) {
        setError(err.structuredError);
      } else {
        const errorMessage = err.response?.data?.error || 'Failed to delete receipt. Please try again later.';
        setError(errorMessage);
      }
    }
  };

  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
    setPage(1); // Reset to first page when search changes
  };

  // Handle status filter change
  const handleStatusFilterChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setStatusFilter(e.target.value);
    setPage(1); // Reset to first page when filter changes
  };

  // Handle category filter change
  const handleCategoryFilterChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setCategoryFilter(e.target.value);
    setPage(1); // Reset to first page when filter changes
  };

  // Handle pagination
  const handlePreviousPage = () => {
    if (page > 1) setPage(page - 1);
  };

  const handleNextPage = () => {
    if (page < totalPages) setPage(page + 1);
  };

  // Handle expense report generation
  const handleGenerateReport = async () => {
    if (!currentFarm?.id && !user?.farm_id) {
      setError('No farm selected. Please select a farm to generate a report.');
      return;
    }

    setReportLoading(true);
    setError(null);

    try {
      const farmId = currentFarm?.id || user?.farm_id;

      // Get the start and end dates for the report (last 30 days by default)
      const endDate = new Date();
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - 30);

      const result = await generateExpenseReport(farmId, {
        startDate: startDate.toISOString().split('T')[0],
        endDate: endDate.toISOString().split('T')[0],
        groupBy: 'category',
        format: 'json'
      });

      setReportData(result);
      setReportModalOpen(true);
    } catch (err: any) {
      console.error('Error generating expense report:', err);

      // Check if the error has structured error information
      if (err.structuredError) {
        setError(err.structuredError);
      } else {
        const errorMessage = err.response?.data?.error || 'Failed to generate expense report. Please try again later.';
        setError(errorMessage);
      }
    } finally {
      setReportLoading(false);
    }
  };

  // Get status badge color
  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case 'approved': return 'bg-green-100 text-green-800';
      case 'rejected': return 'bg-red-100 text-red-800';
      default: return 'bg-yellow-100 text-yellow-800';
    }
  };

  return (
    <Layout>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Receipts</h1>
        <div className="flex space-x-2">
          <button
            onClick={handleGenerateReport}
            disabled={reportLoading}
            className={`inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md ${
              reportLoading
                ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                : 'text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500'
            }`}
          >
            {reportLoading ? 'Generating...' : 'Generate Expense Report'}
          </button>
          <Link
            to="/receipts/new"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Upload Receipt
          </Link>
        </div>
      </div>

      {/* Summary Cards */}
      {summary && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <div className="bg-white rounded-lg shadow p-4 border-l-4 border-primary-500">
            <h3 className="text-sm font-medium text-gray-500">Total Receipts</h3>
            <div className="mt-1 flex items-baseline justify-between">
              <p className="text-2xl font-semibold text-gray-900">{summary.total_count}</p>
              <p className="text-lg font-medium text-gray-900">${summary.total_amount.toFixed(2)}</p>
            </div>
          </div>
          <div className="bg-white rounded-lg shadow p-4 border-l-4 border-yellow-500">
            <h3 className="text-sm font-medium text-gray-500">Pending Receipts</h3>
            <div className="mt-1 flex items-baseline justify-between">
              <p className="text-2xl font-semibold text-gray-900">{summary.by_status.pending.count}</p>
              <p className="text-lg font-medium text-gray-900">${summary.by_status.pending.amount.toFixed(2)}</p>
            </div>
          </div>
          <div className="bg-white rounded-lg shadow p-4 border-l-4 border-green-500">
            <h3 className="text-sm font-medium text-gray-500">Approved Receipts</h3>
            <div className="mt-1 flex items-baseline justify-between">
              <p className="text-2xl font-semibold text-gray-900">{summary.by_status.approved.count}</p>
              <p className="text-lg font-medium text-gray-900">${summary.by_status.approved.amount.toFixed(2)}</p>
            </div>
          </div>
        </div>
      )}

      {/* Filters */}
      <div className="bg-white shadow rounded-lg p-4 mb-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label htmlFor="search" className="block text-sm font-medium text-gray-700 mb-1">
              Search
            </label>
            <input
              type="text"
              id="search"
              placeholder="Search receipts..."
              value={searchQuery}
              onChange={handleSearchChange}
              className="w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
            />
          </div>
          <div>
            <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">
              Status
            </label>
            <select
              id="status"
              value={statusFilter}
              onChange={handleStatusFilterChange}
              className="w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
            >
              <option value="all">All Statuses</option>
              <option value="pending">Pending</option>
              <option value="approved">Approved</option>
              <option value="rejected">Rejected</option>
            </select>
          </div>
          <div>
            <label htmlFor="category" className="block text-sm font-medium text-gray-700 mb-1">
              Category
            </label>
            <input
              type="text"
              id="category"
              placeholder="Filter by category"
              value={categoryFilter}
              onChange={handleCategoryFilterChange}
              className="w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
            />
          </div>
        </div>
      </div>

      {/* Error message */}
      {error && (
        <ErrorDisplay 
          error={error} 
          className="mb-4" 
          onDismiss={() => setError(null)}
          onRetry={() => {
            setError(null);
            const fetchData = async () => {
              setLoading(true);
              setError(null);
              try {
                if (!currentFarm?.id && !user?.farm_id) {
                  setError('No farm selected. Please select a farm to view receipts.');
                  setLoading(false);
                  return;
                }
                const farmId = currentFarm?.id || user?.farm_id;
                const response = await getReceipts(farmId, {
                  search: searchQuery,
                  status: statusFilter !== 'all' ? statusFilter : undefined,
                  category: categoryFilter || undefined,
                  page,
                  limit,
                  sortBy: 'receipt_date',
                  sortOrder: 'desc'
                });
                setReceipts(response.receipts || []);
                setTotalPages(response.pagination?.totalPages || 1);
                setTotalReceipts(response.pagination?.total || 0);
                const summaryData = await getReceiptSummary(farmId);
                setSummary(summaryData);
              } catch (err: any) {
                console.error('Error fetching receipts:', err);
                if (err.structuredError) {
                  setError(err.structuredError);
                } else {
                  const errorMessage = err.response?.data?.error || 'Failed to load receipts. Please try again later.';
                  setError(errorMessage);
                }
              } finally {
                setLoading(false);
              }
            };
            fetchData();
          }}
        />
      )}

      {/* Success message */}
      {successMessage && (
        <div className="mb-4 p-4 text-sm text-green-700 bg-green-100 rounded-md">
          {successMessage}
          <button 
            onClick={() => setSuccessMessage(null)} 
            className="ml-2 text-green-700 hover:text-green-900"
          >
            Dismiss
          </button>
        </div>
      )}

      {/* Loading state */}
      {loading ? (
        <div className="flex justify-center items-center h-64">
          <p className="text-gray-600">Loading receipts...</p>
        </div>
      ) : (
        <>
          {/* Empty state */}
          {receipts.length === 0 && (
            <div className="flex flex-col items-center justify-center h-64 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
              <p className="text-gray-500 mb-2">No receipts found</p>
              <Link
                to="/receipts/new"
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700"
              >
                Upload receipt
              </Link>
            </div>
          )}

          {/* Receipts list */}
          {receipts.length > 0 && (
            <div className="bg-white shadow overflow-hidden sm:rounded-md">
              <ul className="divide-y divide-gray-200">
                {receipts.map((receipt) => (
                  <li key={receipt.id} className="hover:bg-gray-50">
                    <div className="px-4 py-4 flex items-center justify-between cursor-pointer" onClick={() => handleViewReceipt(receipt)}>
                      <div className="flex items-center">
                        <div className="ml-4">
                          <div className="text-sm font-medium text-primary-600">
                            {receipt.vendor_name || 'Unknown Vendor'}
                            {receipt.receipt_number && ` - ${receipt.receipt_number}`}
                          </div>
                          <div className="text-sm text-gray-500">
                            {receipt.description || 'No description'}
                          </div>
                          {receipt.categories && receipt.categories.length > 0 && (
                            <div className="mt-1 flex flex-wrap gap-1">
                              {receipt.categories.map((category, index) => (
                                <span 
                                  key={index} 
                                  className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800"
                                >
                                  {category}
                                </span>
                              ))}
                            </div>
                          )}
                        </div>
                      </div>
                      <div className="flex items-center space-x-4">
                        <div className="text-sm text-gray-500">
                          {receipt.amount ? `$${receipt.amount.toFixed(2)}` : 'N/A'}
                        </div>
                        <div className="text-sm text-gray-500">
                          {formatDate(receipt.receipt_date)}
                        </div>
                        <div>
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusBadgeColor(receipt.status)}`}>
                            {receipt.status.charAt(0).toUpperCase() + receipt.status.slice(1)}
                          </span>
                        </div>
                        <div className="flex space-x-2">
                          {receipt.file_path && (
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                handleDownloadReceipt(receipt);
                              }}
                              className="text-primary-600 hover:text-primary-900"
                            >
                              Download
                            </button>
                          )}
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              handleEditReceipt(receipt);
                            }}
                            className="text-gray-600 hover:text-gray-900"
                          >
                            Edit
                          </button>
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              handleDeleteReceipt(receipt);
                            }}
                            className="text-red-600 hover:text-red-900"
                          >
                            Delete
                          </button>
                        </div>
                      </div>
                    </div>
                  </li>
                ))}
              </ul>
            </div>
          )}

          {/* Pagination */}
          {receipts.length > 0 && (
            <div className="flex items-center justify-between mt-4">
              <div className="text-sm text-gray-700">
                Showing {(page - 1) * limit + 1} to {Math.min(page * limit, totalReceipts)} of {totalReceipts} receipts
              </div>
              <div className="flex space-x-2">
                <button
                  onClick={handlePreviousPage}
                  disabled={page === 1}
                  className={`px-3 py-1 border rounded-md ${
                    page === 1 ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'bg-white text-gray-700 hover:bg-gray-50'
                  }`}
                >
                  Previous
                </button>
                <button
                  onClick={handleNextPage}
                  disabled={page === totalPages}
                  className={`px-3 py-1 border rounded-md ${
                    page === totalPages ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'bg-white text-gray-700 hover:bg-gray-50'
                  }`}
                >
                  Next
                </button>
              </div>
            </div>
          )}
        </>
      )}

      {/* Expense Report Modal */}
      {reportModalOpen && reportData && (
        <div className="fixed z-10 inset-0 overflow-y-auto">
          <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity" aria-hidden="true">
              <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>

            <span className="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-y-auto max-h-[90vh] shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="sm:flex sm:items-start">
                  <div className="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-primary-100 sm:mx-0 sm:h-10 sm:w-10">
                    <svg className="h-6 w-6 text-primary-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                  </div>
                  <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                    <h3 className="text-lg leading-6 font-medium text-gray-900">
                      Expense Report
                    </h3>
                    <div className="mt-2">
                      <p className="text-sm text-gray-500 mb-4">
                        Report generated for period: {reportData.report_period.start_date ? new Date(reportData.report_period.start_date).toLocaleDateString() : 'All time'} to {reportData.report_period.end_date ? new Date(reportData.report_period.end_date).toLocaleDateString() : 'Present'}
                      </p>

                      <div className="bg-gray-50 p-4 rounded-md mb-4">
                        <h4 className="text-md font-medium text-gray-700 mb-2">Summary</h4>
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <p className="text-sm text-gray-500">Total Receipts:</p>
                            <p className="text-lg font-medium">{reportData.totals.count}</p>
                          </div>
                          <div>
                            <p className="text-sm text-gray-500">Total Amount:</p>
                            <p className="text-lg font-medium">${reportData.totals.amount.toFixed(2)}</p>
                          </div>
                        </div>
                      </div>

                      <div className="table-container border border-gray-200 rounded-md">
                        <table className="min-w-full divide-y divide-gray-200">
                          <thead className="bg-gray-50">
                            <tr>
                              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {reportData.grouped_by === 'category' ? 'Category' : 
                                 reportData.grouped_by === 'vendor' ? 'Vendor' : 
                                 reportData.grouped_by === 'date' ? 'Month' : 'Status'}
                              </th>
                              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Count
                              </th>
                              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Amount
                              </th>
                            </tr>
                          </thead>
                          <tbody className="bg-white divide-y divide-gray-200">
                            {Object.keys(reportData.groups).map((groupKey) => (
                              <tr key={groupKey} className="hover:bg-gray-50">
                                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                  {groupKey}
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                  {reportData.groups[groupKey].count}
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                  ${reportData.groups[groupKey].total_amount.toFixed(2)}
                                </td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>

                      <div className="mt-4 bg-blue-50 border border-blue-100 rounded-md p-3">
                        <p className="text-sm text-blue-800">
                          This report can be used for expense tracking, tax preparation, and financial analysis. You can generate more detailed reports with different grouping options.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button
                  type="button"
                  className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary-600 text-base font-medium text-white hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:ml-3 sm:w-auto sm:text-sm"
                  onClick={() => {
                    // In a real implementation, this would download the report as a PDF or CSV
                    alert('In a production environment, this would download the report as a PDF or CSV file.');
                  }}
                >
                  Download Report
                </button>
                <button
                  type="button"
                  className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                  onClick={() => setReportModalOpen(false)}
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </Layout>
  );
};

export default ReceiptList;
