import { Routes, Route } from 'react-router-dom';
import { lazy, Suspense } from 'react';

// Lazy load components
const ReceiptList = lazy(() => import('./ReceiptList'));
const ReceiptDetail = lazy(() => import('./ReceiptDetail'));
const ReceiptForm = lazy(() => import('./ReceiptForm'));

// Loading component
const ComponentLoader = () => (
  <div className="flex items-center justify-center h-64">
    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
    <p className="ml-3 text-gray-500">Loading...</p>
  </div>
);

const ReceiptsRoutes = () => {
  return (
    <Routes>
      <Route path="/" element={
        <Suspense fallback={<ComponentLoader />}>
          <ReceiptList />
        </Suspense>
      } />
      <Route path="/view/:id" element={
        <Suspense fallback={<ComponentLoader />}>
          <ReceiptDetail />
        </Suspense>
      } />
      <Route path="/new" element={
        <Suspense fallback={<ComponentLoader />}>
          <ReceiptForm />
        </Suspense>
      } />
      <Route path="/edit/:id" element={
        <Suspense fallback={<ComponentLoader />}>
          <ReceiptForm />
        </Suspense>
      } />
    </Routes>
  );
};

export default ReceiptsRoutes;
