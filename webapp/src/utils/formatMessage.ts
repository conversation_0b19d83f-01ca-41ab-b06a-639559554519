/**
 * Utility functions for formatting chat messages
 */

/**
 * Format message content with basic text formatting
 * Supports:
 * - Bold: *text* or **text**
 * - Italic: _text_ or __text__
 * - Strikethrough: ~text~ or ~~text~~
 * - Code: `text`
 * - Links: [text](url)
 * 
 * @param content Message content to format
 * @returns Formatted message content as HTML
 */
export const formatMessage = (content: string): string => {
  if (!content) return '';

  // Escape HTML to prevent XSS
  let formattedContent = content
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#039;');

  // Format bold text
  formattedContent = formattedContent.replace(
    /\*\*(.*?)\*\*|\*(.*?)\*/g,
    (_, g1, g2) => `<strong>${g1 || g2}</strong>`
  );

  // Format italic text
  formattedContent = formattedContent.replace(
    /__(.*?)__|\_(.*?)\_/g,
    (_, g1, g2) => `<em>${g1 || g2}</em>`
  );

  // Format strikethrough text
  formattedContent = formattedContent.replace(
    /~~(.*?)~~|~(.*?)~/g,
    (_, g1, g2) => `<del>${g1 || g2}</del>`
  );

  // Format code
  formattedContent = formattedContent.replace(
    /`(.*?)`/g,
    (_, g1) => `<code>${g1}</code>`
  );

  // Format links
  formattedContent = formattedContent.replace(
    /\[(.*?)\]\((.*?)\)/g,
    (_, text, url) => `<a href="${url}" target="_blank" rel="noopener noreferrer">${text}</a>`
  );

  // Replace newlines with <br>
  formattedContent = formattedContent.replace(/\n/g, '<br>');

  return formattedContent;
};

/**
 * Format message content as plain text (remove formatting)
 * @param content Message content to format
 * @returns Plain text message content
 */
export const stripFormatting = (content: string): string => {
  if (!content) return '';

  // Remove formatting markers
  return content
    .replace(/\*\*(.*?)\*\*|\*(.*?)\*/g, '$1$2')
    .replace(/__(.*?)__|\_(.*?)\_/g, '$1$2')
    .replace(/~~(.*?)~~|~(.*?)~/g, '$1$2')
    .replace(/`(.*?)`/g, '$1')
    .replace(/\[(.*?)\]\((.*?)\)/g, '$1');
};