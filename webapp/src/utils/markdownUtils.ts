import { marked } from 'marked';

/**
 * Converts Markdown text to HTML
 * @param markdown - The markdown text to convert
 * @returns The HTML representation of the markdown
 */
export const markdownToHtml = (markdown: string): string | Promise<string> => {
  if (!markdown) return '';

  // Configure marked options for better formatting
  marked.setOptions({
    gfm: true, // GitHub Flavored Markdown
    breaks: true, // Convert line breaks to <br>
  });

  return marked(markdown);
};

/**
 * Strips markdown formatting and returns plain text
 * @param markdown - The markdown text to strip
 * @param maxLength - Optional maximum length for the returned text
 * @returns Plain text without markdown formatting
 */
export const stripMarkdown = (markdown: string, maxLength?: number): string => {
  if (!markdown) return '';

  // Convert markdown to plain text by:
  // 1. Remove headers (# Header)
  // 2. Remove emphasis (* or _)
  // 3. Remove links ([text](url))
  // 4. Remove images (![alt](url))
  // 5. Remove code blocks (``` or `)
  // 6. Remove blockquotes (>)
  // 7. Remove horizontal rules (---)
  // 8. Remove list markers (-, *, +, 1.)

  let plainText = markdown
    .replace(/#+\s+(.*)/g, '$1') // Headers
    .replace(/[*_]{1,2}([^*_]+)[*_]{1,2}/g, '$1') // Bold and italic
    .replace(/\[([^\]]+)\]\([^)]+\)/g, '$1') // Links
    .replace(/!\[[^\]]*\]\([^)]+\)/g, '') // Images
    .replace(/`{1,3}[^`]*`{1,3}/g, '') // Code blocks
    .replace(/^\s*>\s+/gm, '') // Blockquotes
    .replace(/^\s*[-*+]\s+/gm, '') // Unordered lists
    .replace(/^\s*\d+\.\s+/gm, '') // Ordered lists
    .replace(/^\s*---+\s*$/gm, '') // Horizontal rules
    .replace(/\n{2,}/g, '\n\n') // Multiple newlines
    .trim();

  // Truncate if maxLength is provided
  if (maxLength && plainText.length > maxLength) {
    plainText = plainText.substring(0, maxLength) + '...';
  }

  return plainText;
};
