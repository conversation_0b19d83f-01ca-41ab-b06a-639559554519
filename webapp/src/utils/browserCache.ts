// Simple in-memory cache implementation for browser environment
interface CacheItem {
  value: any;
  expiry: number;
}

export class BrowserCache {
  private cache: Record<string, CacheItem> = {};
  private defaultTTL: number;
  private checkPeriod: number;
  private checkInterval: number | null = null;

  constructor({ stdTTL = 600, checkperiod = 60 }) {
    this.defaultTTL = stdTTL;
    this.checkPeriod = checkperiod;
    
    // Set up periodic cleanup
    if (typeof window !== 'undefined') {
      this.checkInterval = window.setInterval(() => this.checkExpiredItems(), this.checkPeriod * 1000);
    }
  }

  set(key: string, value: any, ttl: number = this.defaultTTL): boolean {
    const expiry = Date.now() + (ttl * 1000);
    this.cache[key] = { value, expiry };
    return true;
  }

  get(key: string): any {
    const item = this.cache[key];
    if (!item) return undefined;
    
    // Check if the item has expired
    if (item.expiry < Date.now()) {
      delete this.cache[key];
      return undefined;
    }
    
    return item.value;
  }

  del(key: string): boolean {
    if (key in this.cache) {
      delete this.cache[key];
      return true;
    }
    return false;
  }

  private checkExpiredItems(): void {
    const now = Date.now();
    Object.keys(this.cache).forEach(key => {
      if (this.cache[key].expiry < now) {
        delete this.cache[key];
      }
    });
  }
}

// Export a default instance with standard settings
export default new BrowserCache({ stdTTL: 600, checkperiod: 60 });