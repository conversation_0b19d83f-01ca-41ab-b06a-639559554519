// Utility function to load Google Maps API script
import { GOOGLE_MAPS_API_KEY } from '../config/index';

let isLoading = false;
let isLoaded = false;

/**
 * Loads the Google Maps API script with the specified libraries
 * @param libraries - Array of Google Maps libraries to load
 * @returns Promise that resolves when the script is loaded
 */
export const loadGoogleMapsApi = (libraries: string[] = ['places', 'drawing']): Promise<void> => {
  // Return existing promise if already loading
  if (isLoading) {
    return new Promise((resolve) => {
      const checkIfLoaded = () => {
        if (isLoaded) {
          resolve();
        } else {
          setTimeout(checkIfLoaded, 100);
        }
      };
      checkIfLoaded();
    });
  }

  // Return resolved promise if already loaded
  if (isLoaded) {
    return Promise.resolve();
  }

  // Check if the script is already in the document
  const existingScript = document.querySelector('script[src*="maps.googleapis.com/maps/api/js"]');
  if (existingScript) {
    isLoaded = true;
    return Promise.resolve();
  }

  // Start loading
  isLoading = true;

  return new Promise((resolve, reject) => {
    try {
      // Create script element
      const script = document.createElement('script');
      script.type = 'text/javascript';
      script.async = true;
      script.defer = true;
      script.src = `https://maps.googleapis.com/maps/api/js?key=${GOOGLE_MAPS_API_KEY}&libraries=${libraries.join(',')}&v=weekly`;

      // Set up load handlers
      script.onload = () => {
        isLoaded = true;
        isLoading = false;
        resolve();
      };

      script.onerror = (error) => {
        isLoading = false;
        reject(new Error(`Failed to load Google Maps API: ${error}`));
      };

      // Add script to document
      document.head.appendChild(script);
    } catch (error) {
      isLoading = false;
      reject(error);
    }
  });
};

/**
 * Checks if the Google Maps API is loaded
 * @returns boolean indicating if the API is loaded
 */
export const isGoogleMapsLoaded = (): boolean => {
  return typeof window !== 'undefined' &&
         window.google !== undefined &&
         window.google.maps !== undefined;
};

/**
 * Checks if the Google Maps Places API is loaded
 * @returns boolean indicating if the Places API is loaded
 */
export const isGoogleMapsPlacesLoaded = (): boolean => {
  return isGoogleMapsLoaded() &&
         window.google.maps.places !== undefined;
};