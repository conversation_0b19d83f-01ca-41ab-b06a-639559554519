# Storage Utilities for Cross-Subdomain Data Sharing

This directory contains utility functions for handling storage across subdomains in the NxtAcre application.

## Problem

By default, localStorage is isolated to a specific domain or subdomain due to the same-origin policy. This means that when users are redirected between different subdomains (e.g., from `app.nxtacre.com` to `farm1.nxtacre.com`), they lose access to data stored in localStorage, including authentication tokens and user information.

## Solution

The `storageUtils.ts` file provides utility functions that store data in both localStorage and cookies with the domain attribute set to the main domain. This allows data to be shared across all subdomains of the main domain.

## Usage

### Storing Data

Instead of using `localStorage.setItem()` directly, use the following functions:

```typescript
import { setStorageItem, setStorageJSON } from '../utils/storageUtils';

// Store a string value
setStorageItem('token', 'your-auth-token');

// Store a JSON object
setStorageJSON('user', { id: '123', name: '<PERSON>' });
```

### Retrieving Data

Instead of using `localStorage.getItem()` directly, use the following functions:

```typescript
import { getStorageItem, getStorageJSON } from '../utils/storageUtils';

// Get a string value
const token = getStorageItem('token');

// Get a JSON object
const user = getStorageJSON('user');
```

### Removing Data

Instead of using `localStorage.removeItem()` directly, use the following function:

```typescript
import { removeStorageItem } from '../utils/storageUtils';

// Remove a value
removeStorageItem('token');
```

### Convenience Functions

The utility also provides convenience functions for common operations:

```typescript
import { getAuthToken, getUser, getFarmSubdomain, getFarmId } from '../utils/storageUtils';

// Get the authentication token
const token = getAuthToken();

// Get the user object
const user = getUser();

// Get the farm subdomain
const farmSubdomain = getFarmSubdomain();

// Get the farm ID
const farmId = getFarmId();
```

## Updating Service Files

Many service files in the application directly use `localStorage.getItem('token')` to get the authentication token for API requests. These should be updated to use the new `getAuthToken()` function.

Example:

```typescript
// Before
const token = localStorage.getItem('token');

// After
import { getAuthToken } from '../utils/storageUtils';
const token = getAuthToken();
```

## Implementation Details

The utility functions store data in both localStorage and cookies with the domain attribute set to the main domain. When retrieving data, it first checks localStorage, then falls back to cookies if the data is not found in localStorage.

The main domain is determined by extracting the last two parts of the hostname (e.g., `nxtacre.com` from `farm1.nxtacre.com`).