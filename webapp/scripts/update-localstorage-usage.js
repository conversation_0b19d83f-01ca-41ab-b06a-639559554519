#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the directory name
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Path to src directory
const srcDir = path.join(__dirname, '..', 'src');

// Files that need to be updated
const filesToUpdate = [
  // Component files
  'components/CustomerPortalManager.tsx',
  'components/filemanager/FileManagerMoveModal.tsx',
  'components/filemanager/FileManagerWithExplorer.tsx',
  'components/filemanager/FileManagerPermissionModal.tsx',
  'components/filemanager/FileManagerEdit.tsx',
  'components/filemanager/FileManager.tsx',
  'components/filemanager/FileManagerFolderExplorer.tsx',
  'components/filemanager/FileManagerPreview.tsx',
  'components/filemanager/FileManagerShareModal.tsx',

  // Page files
  'pages/CustomerPortal/index.tsx',
  'pages/GlobalAdmin/SubscriptionManagement.tsx',
  'pages/GlobalAdmin/ApiCacheManagement.tsx',
  'pages/GlobalAdmin/PromoCodeManagement.tsx',
  'pages/GlobalAdmin/AIConfigurationManagement.tsx',
  'pages/GlobalAdmin/IntegrationManagement.tsx',
  'pages/GlobalAdmin/ApiEndpointManagement.tsx',
  'pages/Invoices/InvoiceForm.tsx',
  'pages/FinancialManagement/AdvancedFinancialAnalytics.tsx',
  'pages/Farms/FarmBilling.tsx',
  'pages/Subscriptions/SubscriptionDetail.tsx',
  'pages/Subscriptions/SubscriptionsList.tsx',
  'pages/Soil/SoilDetail.tsx',
  'pages/FieldCollaborations/FieldCollaborationList.tsx',
  'pages/Billing.tsx',
  'pages/Documents/CreateFromTemplate.tsx',
  'pages/Documents/TemplateForm.tsx',
  'pages/Documents/SignableDocumentList.tsx',
  'pages/Documents/DocumentForm.tsx',
  'pages/Documents/DocumentDetail.tsx',
  'pages/Documents/FolderList.tsx',
  'pages/Documents/CreateFromFormTemplate.tsx',
  'pages/Documents/ExternalStorage.tsx',
  'pages/Documents/FormBuilderPage.tsx',
  'pages/Documents/SignableDocumentForm.tsx',
  'pages/Documents/TemplateList.tsx',
  'pages/Documents/FolderForm.tsx',
  'pages/Documents/AIDocumentGenerator.tsx',
  'pages/Documents/DocumentList.tsx',
  'pages/Documents/SignableDocumentDetail.tsx',
  'pages/Documents/DocumentPermissionPage.tsx',
  'pages/CropManagement/HarvestScheduling.tsx',
  'pages/CropManagement/CropRotationOptimization.tsx',
  'pages/CropManagement/CropDiseasePrediction.tsx',
  'pages/CropManagement/YieldPrediction.tsx',
  'pages/CustomDomain/index.tsx',
  'pages/BusinessAccount.tsx'
];

console.log('Updating localStorage usage in component and page files...');

let updatedCount = 0;
let errorCount = 0;

for (const fileToUpdate of filesToUpdate) {
  const filePath = path.join(srcDir, fileToUpdate);
  
  try {
    console.log(`Processing ${fileToUpdate}...`);
    
    if (!fs.existsSync(filePath)) {
      console.log(`  File not found: ${filePath}`);
      continue;
    }
    
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    // Check if getAuthToken import already exists
    const hasGetAuthTokenImport = content.includes("import { getAuthToken }") || 
                                  content.includes("getAuthToken") && content.includes("from '../utils/storageUtils'");
    
    // Add import if not present
    if (!hasGetAuthTokenImport && content.includes("localStorage.getItem('token')")) {
      // Find the last import statement
      const importLines = content.split('\n');
      let lastImportIndex = -1;
      
      for (let i = 0; i < importLines.length; i++) {
        if (importLines[i].trim().startsWith('import ') && !importLines[i].includes('//')) {
          lastImportIndex = i;
        }
      }
      
      if (lastImportIndex !== -1) {
        // Add the import after the last import
        importLines.splice(lastImportIndex + 1, 0, "import { getAuthToken } from '../utils/storageUtils';");
        content = importLines.join('\n');
        modified = true;
        console.log(`  Added getAuthToken import`);
      }
    }
    
    // Replace localStorage.getItem('token') with getAuthToken()
    const originalContent = content;
    content = content.replace(/localStorage\.getItem\('token'\)/g, 'getAuthToken()');
    
    if (content !== originalContent) {
      modified = true;
      const replacements = (originalContent.match(/localStorage\.getItem\('token'\)/g) || []).length;
      console.log(`  Replaced ${replacements} localStorage.getItem('token') calls`);
    }
    
    // Write the file back if modified
    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      updatedCount++;
      console.log(`  ✓ Updated ${fileToUpdate}`);
    } else {
      console.log(`  - No changes needed for ${fileToUpdate}`);
    }

  } catch (error) {
    console.error(`  ✗ Error processing ${fileToUpdate}:`, error.message);
    errorCount++;
  }
}

console.log(`\nUpdate complete!`);
console.log(`Updated: ${updatedCount} files`);
console.log(`Errors: ${errorCount} files`);

if (updatedCount > 0) {
  console.log('\nUpdated files:');
  console.log('- All localStorage.getItem(\'token\') calls replaced with getAuthToken()');
  console.log('- Added getAuthToken imports where needed');
}
