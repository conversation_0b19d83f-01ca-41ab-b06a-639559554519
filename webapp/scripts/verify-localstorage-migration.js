#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the directory name
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Path to src directory
const srcDir = path.join(__dirname, '..', 'src');

console.log('🔍 Verifying localStorage migration...\n');

// Function to recursively find all TypeScript/JavaScript files
function findFiles(dir, extensions = ['.ts', '.tsx', '.js', '.jsx']) {
  let files = [];
  
  const items = fs.readdirSync(dir);
  
  for (const item of items) {
    const fullPath = path.join(dir, item);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory()) {
      // Skip node_modules and other irrelevant directories
      if (!['node_modules', '.git', 'dist', 'build'].includes(item)) {
        files = files.concat(findFiles(fullPath, extensions));
      }
    } else if (extensions.some(ext => item.endsWith(ext))) {
      files.push(fullPath);
    }
  }
  
  return files;
}

// Find all relevant files
const allFiles = findFiles(srcDir);

console.log(`📁 Scanning ${allFiles.length} files...\n`);

let issuesFound = 0;
let filesWithIssues = [];

// Check each file for localStorage usage patterns
for (const filePath of allFiles) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const relativePath = path.relative(srcDir, filePath);
    
    // Patterns to check for
    const patterns = [
      {
        regex: /localStorage\.getItem\('token'\)/g,
        message: "Direct localStorage.getItem('token') usage",
        severity: 'ERROR'
      },
      {
        regex: /localStorage\.setItem\('token'/g,
        message: "Direct localStorage.setItem('token') usage",
        severity: 'ERROR'
      },
      {
        regex: /localStorage\.removeItem\('token'\)/g,
        message: "Direct localStorage.removeItem('token') usage",
        severity: 'ERROR'
      },
      {
        regex: /localStorage\.getItem\('user'\)/g,
        message: "Direct localStorage.getItem('user') usage",
        severity: 'ERROR'
      },
      {
        regex: /localStorage\.setItem\('user'/g,
        message: "Direct localStorage.setItem('user') usage",
        severity: 'ERROR'
      },
      {
        regex: /localStorage\.removeItem\('user'\)/g,
        message: "Direct localStorage.removeItem('user') usage",
        severity: 'ERROR'
      }
    ];
    
    let fileHasIssues = false;
    
    for (const pattern of patterns) {
      const matches = content.match(pattern.regex);
      
      if (matches) {
        // Skip if this is the storageUtils.ts file (it's allowed to have fallback localStorage usage)
        if (relativePath.includes('storageUtils.ts') && pattern.message.includes("localStorage.getItem('token')")) {
          continue;
        }
        
        if (!fileHasIssues) {
          console.log(`❌ ${relativePath}:`);
          fileHasIssues = true;
          filesWithIssues.push(relativePath);
        }
        
        console.log(`   ${pattern.severity}: ${pattern.message} (${matches.length} occurrence${matches.length > 1 ? 's' : ''})`);
        issuesFound += matches.length;
      }
    }
    
    // Check if file uses localStorage for auth but doesn't import getAuthToken
    const usesLocalStorageForAuth = /localStorage\.(get|set|remove)Item\(['"](?:token|user|auth_token|refresh_token)['"]/.test(content);
    const hasGetAuthTokenImport = /import.*getAuthToken.*from.*storageUtils/.test(content);
    const isStorageUtilsFile = relativePath.includes('storageUtils.ts');
    
    if (usesLocalStorageForAuth && !hasGetAuthTokenImport && !isStorageUtilsFile) {
      if (!fileHasIssues) {
        console.log(`⚠️  ${relativePath}:`);
        fileHasIssues = true;
        filesWithIssues.push(relativePath);
      }
      console.log(`   WARNING: Uses localStorage for auth but doesn't import getAuthToken`);
    }
    
  } catch (error) {
    console.error(`❌ Error reading ${filePath}:`, error.message);
    issuesFound++;
  }
}

console.log('\n📊 Migration Verification Results:');
console.log('=====================================');

if (issuesFound === 0) {
  console.log('✅ SUCCESS: No localStorage migration issues found!');
  console.log('✅ All authentication-related localStorage usage has been properly migrated.');
  console.log('✅ The application is ready for cross-subdomain authentication.');
} else {
  console.log(`❌ ISSUES FOUND: ${issuesFound} issue${issuesFound > 1 ? 's' : ''} in ${filesWithIssues.length} file${filesWithIssues.length > 1 ? 's' : ''}`);
  console.log('\nFiles with issues:');
  filesWithIssues.forEach(file => console.log(`  - ${file}`));
  console.log('\nPlease fix these issues before deploying.');
}

console.log('\n🔧 Expected patterns after migration:');
console.log('- getAuthToken() instead of localStorage.getItem(\'token\')');
console.log('- getStorageJSON(\'user\') instead of localStorage.getItem(\'user\')');
console.log('- setStorageItem() / setStorageJSON() instead of localStorage.setItem()');
console.log('- removeStorageItem() instead of localStorage.removeItem()');

process.exit(issuesFound > 0 ? 1 : 0);
