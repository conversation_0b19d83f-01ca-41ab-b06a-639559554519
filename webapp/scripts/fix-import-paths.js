#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the directory name
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Path to src directory
const srcDir = path.join(__dirname, '..', 'src');

console.log('🔧 Fixing import paths for storageUtils...\n');

// Function to recursively find all TypeScript/JavaScript files
function findFiles(dir, extensions = ['.ts', '.tsx', '.js', '.jsx']) {
  let files = [];
  
  const items = fs.readdirSync(dir);
  
  for (const item of items) {
    const fullPath = path.join(dir, item);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory()) {
      // Skip node_modules and other irrelevant directories
      if (!['node_modules', '.git', 'dist', 'build'].includes(item)) {
        files = files.concat(findFiles(fullPath, extensions));
      }
    } else if (extensions.some(ext => item.endsWith(ext))) {
      files.push(fullPath);
    }
  }
  
  return files;
}

// Find all relevant files
const allFiles = findFiles(srcDir);

console.log(`📁 Scanning ${allFiles.length} files...\n`);

let fixedCount = 0;
let errorCount = 0;

// Check each file for incorrect import paths
for (const filePath of allFiles) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const relativePath = path.relative(srcDir, filePath);
    
    // Check for incorrect import paths
    const incorrectImportPattern = /from\s+['"]\.\.\/utils\/storageUtils['"]/g;
    
    if (incorrectImportPattern.test(content)) {
      // Calculate the correct relative path
      const fileDir = path.dirname(filePath);
      const utilsPath = path.join(srcDir, 'utils', 'storageUtils.ts');
      const correctRelativePath = path.relative(fileDir, utilsPath).replace(/\\/g, '/').replace('.ts', '');
      
      // Make sure it starts with './' or '../'
      const finalPath = correctRelativePath.startsWith('.') ? correctRelativePath : './' + correctRelativePath;
      
      // Fix the import
      const fixedContent = content.replace(
        /from\s+['"]\.\.\/utils\/storageUtils['"]/g,
        `from '${finalPath}'`
      );
      
      if (fixedContent !== content) {
        fs.writeFileSync(filePath, fixedContent, 'utf8');
        console.log(`✅ Fixed import path in ${relativePath}`);
        console.log(`   Changed: from '../utils/storageUtils'`);
        console.log(`   To:      from '${finalPath}'`);
        fixedCount++;
      }
    }
    
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
    errorCount++;
  }
}

console.log('\n📊 Import Path Fix Results:');
console.log('============================');

if (fixedCount === 0 && errorCount === 0) {
  console.log('✅ All import paths are already correct!');
} else {
  console.log(`✅ Fixed: ${fixedCount} file${fixedCount !== 1 ? 's' : ''}`);
  if (errorCount > 0) {
    console.log(`❌ Errors: ${errorCount} file${errorCount !== 1 ? 's' : ''}`);
  }
}

process.exit(errorCount > 0 ? 1 : 0);
