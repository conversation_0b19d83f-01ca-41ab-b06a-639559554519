import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the directory name
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Path to the models directory
const modelsDir = path.join(__dirname, '..', 'server', 'models');

// Get all .js files in the models directory
const modelFiles = fs.readdirSync(modelsDir).filter(file => file.endsWith('.js'));

console.log(`Found ${modelFiles.length} model files to check.`);

let updatedCount = 0;

// Process each model file
modelFiles.forEach(file => {
  const filePath = path.join(modelsDir, file);
  let content = fs.readFileSync(filePath, 'utf8');

  // Check if the file contains the pattern we want to replace
  if (content.includes("process.env.DB_SCHEMA || 'public'")) {
    // Replace the pattern
    const updatedContent = content.replace(
      "process.env.DB_SCHEMA || 'public'",
      "process.env.DB_SCHEMA || 'site'"
    );

    // Write the updated content back to the file
    fs.writeFileSync(filePath, updatedContent, 'utf8');

    console.log(`Updated schema in ${file}`);
    updatedCount++;
  }
});

console.log(`Updated ${updatedCount} model files to use 'site' as the default schema.`);
