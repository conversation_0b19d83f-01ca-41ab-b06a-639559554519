// Shopping Cart Test Script
// This script tests the shopping cart functionality in the marketplace

const { describe, it, expect, beforeEach, afterEach } = require('@jest/globals');
const axios = require('axios');

// Mock axios for testing
jest.mock('axios');

describe('Shopping Cart Functionality', () => {
  const baseUrl = 'http://localhost:3000/api';
  const productId = 'product-123';
  const cartItemId = 'cart-item-123';
  const cartId = 'cart-123';
  
  beforeEach(() => {
    // Reset mocks before each test
    axios.mockReset();
  });

  afterEach(() => {
    // Clean up after tests if needed
  });

  it('should add an item to the cart', async () => {
    // Mock the API response
    axios.post.mockResolvedValue({
      data: {
        cart: {
          id: cartId,
          items: [
            {
              id: cartItemId,
              product_id: productId,
              quantity: 2,
              product: {
                id: productId,
                name: 'Organic Apples',
                price: 3.99,
                images: [
                  {
                    id: 'image-123',
                    file_path: '/uploads/products/apple.jpg'
                  }
                ]
              }
            }
          ],
          itemCount: 2,
          totalPrice: 7.98,
          farm: {
            id: 'farm-123',
            name: 'Green Valley Farm'
          }
        }
      }
    });

    // Make the request
    const response = await axios.post(`${baseUrl}/marketplace/cart/items`, {
      productId,
      quantity: 2
    });

    // Assertions
    expect(axios.post).toHaveBeenCalledWith(
      `${baseUrl}/marketplace/cart/items`,
      {
        productId,
        quantity: 2
      }
    );
    expect(response.data.cart).toHaveProperty('id');
    expect(response.data.cart).toHaveProperty('items');
    expect(response.data.cart.items).toBeInstanceOf(Array);
    expect(response.data.cart.items.length).toBe(1);
    expect(response.data.cart.items[0].product_id).toBe(productId);
    expect(response.data.cart.items[0].quantity).toBe(2);
    expect(response.data.cart).toHaveProperty('itemCount', 2);
    expect(response.data.cart).toHaveProperty('totalPrice', 7.98);
  });

  it('should get the current cart', async () => {
    // Mock the API response
    axios.get.mockResolvedValue({
      data: {
        cart: {
          id: cartId,
          items: [
            {
              id: cartItemId,
              product_id: productId,
              quantity: 2,
              product: {
                id: productId,
                name: 'Organic Apples',
                price: 3.99,
                images: [
                  {
                    id: 'image-123',
                    file_path: '/uploads/products/apple.jpg'
                  }
                ]
              }
            }
          ],
          itemCount: 2,
          totalPrice: 7.98,
          farm: {
            id: 'farm-123',
            name: 'Green Valley Farm'
          }
        }
      }
    });

    // Make the request
    const response = await axios.get(`${baseUrl}/marketplace/cart`);

    // Assertions
    expect(axios.get).toHaveBeenCalledWith(`${baseUrl}/marketplace/cart`);
    expect(response.data.cart).toHaveProperty('id');
    expect(response.data.cart).toHaveProperty('items');
    expect(response.data.cart.items).toBeInstanceOf(Array);
    expect(response.data.cart.items.length).toBe(1);
    expect(response.data.cart).toHaveProperty('itemCount');
    expect(response.data.cart).toHaveProperty('totalPrice');
  });

  it('should update cart item quantity', async () => {
    const newQuantity = 5;
    
    // Mock the API response
    axios.put.mockResolvedValue({
      data: {
        cart: {
          id: cartId,
          items: [
            {
              id: cartItemId,
              product_id: productId,
              quantity: newQuantity,
              product: {
                id: productId,
                name: 'Organic Apples',
                price: 3.99,
                images: [
                  {
                    id: 'image-123',
                    file_path: '/uploads/products/apple.jpg'
                  }
                ]
              }
            }
          ],
          itemCount: newQuantity,
          totalPrice: 19.95,
          farm: {
            id: 'farm-123',
            name: 'Green Valley Farm'
          }
        }
      }
    });

    // Make the request
    const response = await axios.put(`${baseUrl}/marketplace/cart/items/${cartItemId}`, {
      quantity: newQuantity
    });

    // Assertions
    expect(axios.put).toHaveBeenCalledWith(
      `${baseUrl}/marketplace/cart/items/${cartItemId}`,
      {
        quantity: newQuantity
      }
    );
    expect(response.data.cart.items[0].quantity).toBe(newQuantity);
    expect(response.data.cart.itemCount).toBe(newQuantity);
    expect(response.data.cart.totalPrice).toBe(19.95);
  });

  it('should remove an item from the cart', async () => {
    // Mock the API response
    axios.delete.mockResolvedValue({
      data: {
        cart: {
          id: cartId,
          items: [],
          itemCount: 0,
          totalPrice: 0,
          farm: {
            id: 'farm-123',
            name: 'Green Valley Farm'
          }
        }
      }
    });

    // Make the request
    const response = await axios.delete(`${baseUrl}/marketplace/cart/items/${cartItemId}`);

    // Assertions
    expect(axios.delete).toHaveBeenCalledWith(`${baseUrl}/marketplace/cart/items/${cartItemId}`);
    expect(response.data.cart.items).toBeInstanceOf(Array);
    expect(response.data.cart.items.length).toBe(0);
    expect(response.data.cart.itemCount).toBe(0);
    expect(response.data.cart.totalPrice).toBe(0);
  });

  it('should save cart for later', async () => {
    // Mock the API response
    axios.post.mockResolvedValue({
      data: {
        cart: {
          id: cartId,
          items: [
            {
              id: cartItemId,
              product_id: productId,
              quantity: 2,
              product: {
                id: productId,
                name: 'Organic Apples',
                price: 3.99
              }
            }
          ],
          itemCount: 2,
          totalPrice: 7.98,
          is_saved: true,
          farm: {
            id: 'farm-123',
            name: 'Green Valley Farm'
          }
        }
      }
    });

    // Make the request
    const response = await axios.post(`${baseUrl}/marketplace/cart/save`, {
      cartId
    });

    // Assertions
    expect(axios.post).toHaveBeenCalledWith(
      `${baseUrl}/marketplace/cart/save`,
      {
        cartId
      }
    );
    expect(response.data.cart).toHaveProperty('is_saved', true);
  });

  it('should checkout a cart', async () => {
    // Mock the API response
    axios.post.mockResolvedValue({
      data: {
        message: 'Purchase request submitted successfully',
        purchaseRequest: {
          id: 'purchase-123',
          status: 'pending',
          items: [
            {
              product_id: productId,
              quantity: 2,
              price: 3.99
            }
          ],
          total: 7.98,
          farm_id: 'farm-123',
          fulfillment_method: 'delivery',
          delivery_address_id: 'address-123'
        }
      }
    });

    // Make the request
    const response = await axios.post(`${baseUrl}/marketplace/cart/checkout`, {
      cartId,
      fulfillmentMethod: 'delivery',
      deliveryAddressId: 'address-123',
      notes: 'Please leave at the front door'
    });

    // Assertions
    expect(axios.post).toHaveBeenCalledWith(
      `${baseUrl}/marketplace/cart/checkout`,
      {
        cartId,
        fulfillmentMethod: 'delivery',
        deliveryAddressId: 'address-123',
        notes: 'Please leave at the front door'
      }
    );
    expect(response.data).toHaveProperty('message');
    expect(response.data).toHaveProperty('purchaseRequest');
    expect(response.data.purchaseRequest).toHaveProperty('id');
    expect(response.data.purchaseRequest).toHaveProperty('status', 'pending');
    expect(response.data.purchaseRequest).toHaveProperty('items');
    expect(response.data.purchaseRequest).toHaveProperty('total');
    expect(response.data.purchaseRequest).toHaveProperty('fulfillment_method', 'delivery');
  });

  it('should handle multi-farm carts', async () => {
    // Mock the API response
    axios.get.mockResolvedValue({
      data: {
        carts: [
          {
            id: 'cart-123',
            items: [
              {
                id: 'cart-item-123',
                product_id: 'product-123',
                quantity: 2,
                product: {
                  id: 'product-123',
                  name: 'Organic Apples',
                  price: 3.99
                }
              }
            ],
            itemCount: 2,
            totalPrice: 7.98,
            farm: {
              id: 'farm-123',
              name: 'Green Valley Farm'
            }
          },
          {
            id: 'cart-456',
            items: [
              {
                id: 'cart-item-456',
                product_id: 'product-456',
                quantity: 1,
                product: {
                  id: 'product-456',
                  name: 'Free Range Eggs',
                  price: 5.99
                }
              }
            ],
            itemCount: 1,
            totalPrice: 5.99,
            farm: {
              id: 'farm-456',
              name: 'Sunny Side Farm'
            }
          }
        ],
        totalItemCount: 3,
        totalPrice: 13.97
      }
    });

    // Make the request
    const response = await axios.get(`${baseUrl}/marketplace/carts`);

    // Assertions
    expect(axios.get).toHaveBeenCalledWith(`${baseUrl}/marketplace/carts`);
    expect(response.data.carts).toBeInstanceOf(Array);
    expect(response.data.carts.length).toBe(2);
    expect(response.data.carts[0].farm.id).toBe('farm-123');
    expect(response.data.carts[1].farm.id).toBe('farm-456');
    expect(response.data).toHaveProperty('totalItemCount', 3);
    expect(response.data).toHaveProperty('totalPrice', 13.97);
  });

  it('should checkout all carts', async () => {
    // Mock the API response
    axios.post.mockResolvedValue({
      data: {
        message: 'All purchase requests submitted successfully',
        purchaseRequests: [
          {
            id: 'purchase-123',
            status: 'pending',
            farm_id: 'farm-123',
            total: 7.98
          },
          {
            id: 'purchase-456',
            status: 'pending',
            farm_id: 'farm-456',
            total: 5.99
          }
        ]
      }
    });

    // Make the request
    const response = await axios.post(`${baseUrl}/marketplace/cart/checkout-all`, {
      fulfillmentMethod: 'delivery',
      deliveryAddressId: 'address-123',
      notes: 'Please leave at the front door'
    });

    // Assertions
    expect(axios.post).toHaveBeenCalledWith(
      `${baseUrl}/marketplace/cart/checkout-all`,
      {
        fulfillmentMethod: 'delivery',
        deliveryAddressId: 'address-123',
        notes: 'Please leave at the front door'
      }
    );
    expect(response.data).toHaveProperty('message');
    expect(response.data).toHaveProperty('purchaseRequests');
    expect(response.data.purchaseRequests).toBeInstanceOf(Array);
    expect(response.data.purchaseRequests.length).toBe(2);
    expect(response.data.purchaseRequests[0]).toHaveProperty('farm_id', 'farm-123');
    expect(response.data.purchaseRequests[1]).toHaveProperty('farm_id', 'farm-456');
  });

  it('should set fulfillment method for cart', async () => {
    // Mock the API response
    axios.post.mockResolvedValue({
      data: {
        cart: {
          id: cartId,
          items: [
            {
              id: cartItemId,
              product_id: productId,
              quantity: 2
            }
          ],
          fulfillment_method: 'pickup',
          pickup_date: '2023-08-15T14:00:00Z',
          farm: {
            id: 'farm-123',
            name: 'Green Valley Farm'
          }
        }
      }
    });

    // Make the request
    const response = await axios.post(`${baseUrl}/marketplace/cart/fulfillment-method`, {
      cartId,
      fulfillmentMethod: 'pickup',
      pickupDate: '2023-08-15T14:00:00Z'
    });

    // Assertions
    expect(axios.post).toHaveBeenCalledWith(
      `${baseUrl}/marketplace/cart/fulfillment-method`,
      {
        cartId,
        fulfillmentMethod: 'pickup',
        pickupDate: '2023-08-15T14:00:00Z'
      }
    );
    expect(response.data.cart).toHaveProperty('fulfillment_method', 'pickup');
    expect(response.data.cart).toHaveProperty('pickup_date', '2023-08-15T14:00:00Z');
  });
});