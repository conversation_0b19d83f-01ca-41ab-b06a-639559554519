// Marketplace Product Listings Test Script
// This script tests the product listing functionality in the marketplace

const { describe, it, expect, beforeEach, afterEach } = require('@jest/globals');
const axios = require('axios');

// Mock axios for testing
jest.mock('axios');

describe('Marketplace Product Listings', () => {
  const baseUrl = 'http://localhost:3000/api';
  
  beforeEach(() => {
    // Reset mocks before each test
    axios.mockReset();
  });

  afterEach(() => {
    // Clean up after tests if needed
  });

  it('should get all marketplace products', async () => {
    // Mock the API response
    axios.get.mockResolvedValue({
      data: {
        products: [
          {
            id: 'product-123',
            name: 'Organic Apples',
            price: 3.99,
            description: 'Fresh organic apples',
            farm: {
              id: 'farm-123',
              name: 'Green Valley Farm'
            },
            images: [
              {
                id: 'image-123',
                file_path: '/uploads/products/apple.jpg'
              }
            ]
          },
          {
            id: 'product-456',
            name: 'Free Range Eggs',
            price: 5.99,
            description: 'Farm fresh free range eggs',
            farm: {
              id: 'farm-456',
              name: 'Sunny Side Farm'
            },
            images: [
              {
                id: 'image-456',
                file_path: '/uploads/products/eggs.jpg'
              }
            ]
          }
        ],
        pagination: {
          total: 2,
          page: 1,
          limit: 10,
          totalPages: 1
        }
      }
    });

    // Make the request
    const response = await axios.get(`${baseUrl}/marketplace/products`);

    // Assertions
    expect(axios.get).toHaveBeenCalledWith(`${baseUrl}/marketplace/products`);
    expect(response.data.products).toBeInstanceOf(Array);
    expect(response.data.products.length).toBe(2);
    expect(response.data.products[0]).toHaveProperty('id');
    expect(response.data.products[0]).toHaveProperty('name');
    expect(response.data.products[0]).toHaveProperty('price');
    expect(response.data.products[0]).toHaveProperty('farm');
    expect(response.data.products[0]).toHaveProperty('images');
    expect(response.data.pagination).toHaveProperty('total');
    expect(response.data.pagination).toHaveProperty('page');
  });

  it('should get farm-specific marketplace products', async () => {
    const farmId = 'farm-123';
    
    // Mock the API response
    axios.get.mockResolvedValue({
      data: {
        farm: {
          id: 'farm-123',
          name: 'Green Valley Farm',
          description: 'Organic farm in the valley'
        },
        products: [
          {
            id: 'product-123',
            name: 'Organic Apples',
            price: 3.99,
            description: 'Fresh organic apples',
            images: [
              {
                id: 'image-123',
                file_path: '/uploads/products/apple.jpg'
              }
            ]
          },
          {
            id: 'product-789',
            name: 'Organic Pears',
            price: 4.99,
            description: 'Fresh organic pears',
            images: [
              {
                id: 'image-789',
                file_path: '/uploads/products/pear.jpg'
              }
            ]
          }
        ],
        pagination: {
          total: 2,
          page: 1,
          limit: 10,
          totalPages: 1
        }
      }
    });

    // Make the request
    const response = await axios.get(`${baseUrl}/marketplace/farms/${farmId}/products`);

    // Assertions
    expect(axios.get).toHaveBeenCalledWith(`${baseUrl}/marketplace/farms/${farmId}/products`);
    expect(response.data.farm).toHaveProperty('id', farmId);
    expect(response.data.products).toBeInstanceOf(Array);
    expect(response.data.products.length).toBe(2);
    expect(response.data.products[0]).toHaveProperty('id');
    expect(response.data.products[0]).toHaveProperty('name');
    expect(response.data.products[0]).toHaveProperty('price');
  });

  it('should get product categories', async () => {
    // Mock the API response
    axios.get.mockResolvedValue({
      data: {
        categories: [
          'Fruits',
          'Vegetables',
          'Dairy',
          'Meat',
          'Eggs'
        ]
      }
    });

    // Make the request
    const response = await axios.get(`${baseUrl}/marketplace/products/categories`);

    // Assertions
    expect(axios.get).toHaveBeenCalledWith(`${baseUrl}/marketplace/products/categories`);
    expect(response.data.categories).toBeInstanceOf(Array);
    expect(response.data.categories.length).toBeGreaterThan(0);
    expect(typeof response.data.categories[0]).toBe('string');
  });

  it('should search for products', async () => {
    const searchQuery = 'organic';
    
    // Mock the API response
    axios.get.mockResolvedValue({
      data: {
        products: [
          {
            id: 'product-123',
            name: 'Organic Apples',
            price: 3.99,
            description: 'Fresh organic apples',
            farm: {
              id: 'farm-123',
              name: 'Green Valley Farm'
            },
            images: [
              {
                id: 'image-123',
                file_path: '/uploads/products/apple.jpg'
              }
            ]
          },
          {
            id: 'product-789',
            name: 'Organic Pears',
            price: 4.99,
            description: 'Fresh organic pears',
            farm: {
              id: 'farm-123',
              name: 'Green Valley Farm'
            },
            images: [
              {
                id: 'image-789',
                file_path: '/uploads/products/pear.jpg'
              }
            ]
          }
        ],
        pagination: {
          total: 2,
          page: 1,
          limit: 10,
          totalPages: 1
        }
      }
    });

    // Make the request
    const response = await axios.get(`${baseUrl}/marketplace/products/search?q=${searchQuery}`);

    // Assertions
    expect(axios.get).toHaveBeenCalledWith(`${baseUrl}/marketplace/products/search?q=${searchQuery}`);
    expect(response.data.products).toBeInstanceOf(Array);
    expect(response.data.products.length).toBe(2);
    expect(response.data.products[0].name.toLowerCase()).toContain(searchQuery);
    expect(response.data.pagination).toHaveProperty('total');
  });

  it('should get a single product', async () => {
    const productId = 'product-123';
    
    // Mock the API response
    axios.get.mockResolvedValue({
      data: {
        product: {
          id: 'product-123',
          name: 'Organic Apples',
          price: 3.99,
          description: 'Fresh organic apples',
          marketplace_description: 'Delicious organic apples grown without pesticides',
          marketplace_category: 'Fruits',
          farm: {
            id: 'farm-123',
            name: 'Green Valley Farm',
            description: 'Organic farm in the valley'
          },
          images: [
            {
              id: 'image-123',
              file_path: '/uploads/products/apple.jpg',
              is_primary: true
            },
            {
              id: 'image-124',
              file_path: '/uploads/products/apple2.jpg',
              is_primary: false
            }
          ],
          fulfillment_options: {
            offers_delivery: true,
            offers_pickup: true,
            delivery_fee: 5.00,
            min_order_for_free_delivery: 50.00
          }
        }
      }
    });

    // Make the request
    const response = await axios.get(`${baseUrl}/marketplace/products/${productId}`);

    // Assertions
    expect(axios.get).toHaveBeenCalledWith(`${baseUrl}/marketplace/products/${productId}`);
    expect(response.data.product).toHaveProperty('id', productId);
    expect(response.data.product).toHaveProperty('name');
    expect(response.data.product).toHaveProperty('price');
    expect(response.data.product).toHaveProperty('description');
    expect(response.data.product).toHaveProperty('marketplace_description');
    expect(response.data.product).toHaveProperty('marketplace_category');
    expect(response.data.product).toHaveProperty('farm');
    expect(response.data.product).toHaveProperty('images');
    expect(response.data.product).toHaveProperty('fulfillment_options');
    expect(response.data.product.images).toBeInstanceOf(Array);
    expect(response.data.product.images.length).toBeGreaterThan(0);
  });

  it('should filter products by category', async () => {
    const category = 'Fruits';
    
    // Mock the API response
    axios.get.mockResolvedValue({
      data: {
        products: [
          {
            id: 'product-123',
            name: 'Organic Apples',
            price: 3.99,
            description: 'Fresh organic apples',
            marketplace_category: 'Fruits',
            farm: {
              id: 'farm-123',
              name: 'Green Valley Farm'
            }
          },
          {
            id: 'product-789',
            name: 'Organic Pears',
            price: 4.99,
            description: 'Fresh organic pears',
            marketplace_category: 'Fruits',
            farm: {
              id: 'farm-123',
              name: 'Green Valley Farm'
            }
          }
        ],
        pagination: {
          total: 2,
          page: 1,
          limit: 10,
          totalPages: 1
        }
      }
    });

    // Make the request
    const response = await axios.get(`${baseUrl}/marketplace/products?category=${category}`);

    // Assertions
    expect(axios.get).toHaveBeenCalledWith(`${baseUrl}/marketplace/products?category=${category}`);
    expect(response.data.products).toBeInstanceOf(Array);
    expect(response.data.products.length).toBe(2);
    expect(response.data.products[0].marketplace_category).toBe(category);
    expect(response.data.products[1].marketplace_category).toBe(category);
  });

  it('should filter products by price range', async () => {
    const minPrice = 3.00;
    const maxPrice = 5.00;
    
    // Mock the API response
    axios.get.mockResolvedValue({
      data: {
        products: [
          {
            id: 'product-123',
            name: 'Organic Apples',
            price: 3.99,
            description: 'Fresh organic apples'
          },
          {
            id: 'product-456',
            name: 'Organic Carrots',
            price: 4.50,
            description: 'Fresh organic carrots'
          }
        ],
        pagination: {
          total: 2,
          page: 1,
          limit: 10,
          totalPages: 1
        }
      }
    });

    // Make the request
    const response = await axios.get(`${baseUrl}/marketplace/products?minPrice=${minPrice}&maxPrice=${maxPrice}`);

    // Assertions
    expect(axios.get).toHaveBeenCalledWith(`${baseUrl}/marketplace/products?minPrice=${minPrice}&maxPrice=${maxPrice}`);
    expect(response.data.products).toBeInstanceOf(Array);
    expect(response.data.products.length).toBe(2);
    expect(response.data.products[0].price).toBeGreaterThanOrEqual(minPrice);
    expect(response.data.products[0].price).toBeLessThanOrEqual(maxPrice);
    expect(response.data.products[1].price).toBeGreaterThanOrEqual(minPrice);
    expect(response.data.products[1].price).toBeLessThanOrEqual(maxPrice);
  });

  it('should filter products by farm', async () => {
    const farmId = 'farm-123';
    
    // Mock the API response
    axios.get.mockResolvedValue({
      data: {
        products: [
          {
            id: 'product-123',
            name: 'Organic Apples',
            price: 3.99,
            description: 'Fresh organic apples',
            farm: {
              id: 'farm-123',
              name: 'Green Valley Farm'
            }
          },
          {
            id: 'product-789',
            name: 'Organic Pears',
            price: 4.99,
            description: 'Fresh organic pears',
            farm: {
              id: 'farm-123',
              name: 'Green Valley Farm'
            }
          }
        ],
        pagination: {
          total: 2,
          page: 1,
          limit: 10,
          totalPages: 1
        }
      }
    });

    // Make the request
    const response = await axios.get(`${baseUrl}/marketplace/products?farmId=${farmId}`);

    // Assertions
    expect(axios.get).toHaveBeenCalledWith(`${baseUrl}/marketplace/products?farmId=${farmId}`);
    expect(response.data.products).toBeInstanceOf(Array);
    expect(response.data.products.length).toBe(2);
    expect(response.data.products[0].farm.id).toBe(farmId);
    expect(response.data.products[1].farm.id).toBe(farmId);
  });
});