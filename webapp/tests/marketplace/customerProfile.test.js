// Customer Profile and Address Management Test Script
// This script tests the customer profile and address management functionality in the marketplace

const { describe, it, expect, beforeEach, afterEach } = require('@jest/globals');
const axios = require('axios');

// Mock axios for testing
jest.mock('axios');

describe('Customer Profile and Address Management', () => {
  const baseUrl = 'http://localhost:3000/api';
  const testCustomer = {
    name: '<PERSON>',
    email: '<EMAIL>',
    password: 'Password123!',
    phone: '************'
  };
  const farmId = 'farm-123';
  const addressId = 'address-123';
  
  beforeEach(() => {
    // Reset mocks before each test
    axios.mockReset();
  });

  afterEach(() => {
    // Clean up after tests if needed
  });

  it('should register a new customer', async () => {
    // Mock the API response
    axios.post.mockResolvedValue({
      data: {
        message: 'Customer registered successfully',
        customer: {
          id: 'customer-123',
          name: testCustomer.name,
          email: testCustomer.email,
          phone: testCustomer.phone
        },
        token: 'jwt-token-123'
      }
    });

    // Make the request
    const response = await axios.post(`${baseUrl}/marketplace/customers/register`, {
      ...testCustomer,
      farmId
    });

    // Assertions
    expect(axios.post).toHaveBeenCalledWith(
      `${baseUrl}/marketplace/customers/register`,
      {
        ...testCustomer,
        farmId
      }
    );
    expect(response.data).toHaveProperty('message');
    expect(response.data).toHaveProperty('customer');
    expect(response.data).toHaveProperty('token');
    expect(response.data.customer).toHaveProperty('id');
    expect(response.data.customer).toHaveProperty('name', testCustomer.name);
    expect(response.data.customer).toHaveProperty('email', testCustomer.email);
  });

  it('should login a customer', async () => {
    // Mock the API response
    axios.post.mockResolvedValue({
      data: {
        message: 'Login successful',
        customer: {
          id: 'customer-123',
          name: testCustomer.name,
          email: testCustomer.email,
          phone: testCustomer.phone
        },
        token: 'jwt-token-123'
      }
    });

    // Make the request
    const response = await axios.post(`${baseUrl}/marketplace/customers/login`, {
      email: testCustomer.email,
      password: testCustomer.password,
      farmId
    });

    // Assertions
    expect(axios.post).toHaveBeenCalledWith(
      `${baseUrl}/marketplace/customers/login`,
      {
        email: testCustomer.email,
        password: testCustomer.password,
        farmId
      }
    );
    expect(response.data).toHaveProperty('message');
    expect(response.data).toHaveProperty('customer');
    expect(response.data).toHaveProperty('token');
    expect(response.data.customer).toHaveProperty('id');
    expect(response.data.customer).toHaveProperty('name', testCustomer.name);
    expect(response.data.customer).toHaveProperty('email', testCustomer.email);
  });

  it('should get customer profile', async () => {
    // Mock the API response
    axios.get.mockResolvedValue({
      data: {
        customer: {
          id: 'customer-123',
          name: testCustomer.name,
          email: testCustomer.email,
          phone: testCustomer.phone,
          addresses: [
            {
              id: addressId,
              farm_id: farmId,
              farm_alias: 'My Local Farm',
              address: '123 Main St',
              city: 'Farmville',
              state: 'CA',
              zip_code: '12345',
              delivery_instructions: 'Leave at the front door'
            }
          ]
        }
      }
    });

    // Make the request
    const response = await axios.get(`${baseUrl}/marketplace/customers/profile`);

    // Assertions
    expect(axios.get).toHaveBeenCalledWith(`${baseUrl}/marketplace/customers/profile`);
    expect(response.data).toHaveProperty('customer');
    expect(response.data.customer).toHaveProperty('id');
    expect(response.data.customer).toHaveProperty('name', testCustomer.name);
    expect(response.data.customer).toHaveProperty('email', testCustomer.email);
    expect(response.data.customer).toHaveProperty('addresses');
    expect(response.data.customer.addresses).toBeInstanceOf(Array);
  });

  it('should update customer profile', async () => {
    const updatedProfile = {
      name: 'John Updated Doe',
      phone: '************'
    };

    // Mock the API response
    axios.put.mockResolvedValue({
      data: {
        message: 'Profile updated successfully',
        customer: {
          id: 'customer-123',
          name: updatedProfile.name,
          email: testCustomer.email,
          phone: updatedProfile.phone
        }
      }
    });

    // Make the request
    const response = await axios.put(`${baseUrl}/marketplace/customers/profile`, updatedProfile);

    // Assertions
    expect(axios.put).toHaveBeenCalledWith(
      `${baseUrl}/marketplace/customers/profile`,
      updatedProfile
    );
    expect(response.data).toHaveProperty('message');
    expect(response.data).toHaveProperty('customer');
    expect(response.data.customer).toHaveProperty('name', updatedProfile.name);
    expect(response.data.customer).toHaveProperty('phone', updatedProfile.phone);
  });

  it('should add a new address', async () => {
    const newAddress = {
      farm_id: farmId,
      farm_alias: 'My Local Farm',
      address: '123 Main St',
      city: 'Farmville',
      state: 'CA',
      zip_code: '12345',
      country: 'USA',
      delivery_instructions: 'Leave at the front door',
      is_default: true
    };

    // Mock the API response
    axios.post.mockResolvedValue({
      data: {
        message: 'Address added successfully',
        address: {
          id: addressId,
          ...newAddress
        }
      }
    });

    // Make the request
    const response = await axios.post(`${baseUrl}/marketplace/customers/addresses`, newAddress);

    // Assertions
    expect(axios.post).toHaveBeenCalledWith(
      `${baseUrl}/marketplace/customers/addresses`,
      newAddress
    );
    expect(response.data).toHaveProperty('message');
    expect(response.data).toHaveProperty('address');
    expect(response.data.address).toHaveProperty('id');
    expect(response.data.address).toHaveProperty('farm_id', farmId);
    expect(response.data.address).toHaveProperty('address', newAddress.address);
    expect(response.data.address).toHaveProperty('city', newAddress.city);
    expect(response.data.address).toHaveProperty('state', newAddress.state);
    expect(response.data.address).toHaveProperty('zip_code', newAddress.zip_code);
    expect(response.data.address).toHaveProperty('delivery_instructions', newAddress.delivery_instructions);
  });

  it('should update an address', async () => {
    const updatedAddress = {
      farm_alias: 'Updated Farm Name',
      delivery_instructions: 'Updated delivery instructions'
    };

    // Mock the API response
    axios.put.mockResolvedValue({
      data: {
        message: 'Address updated successfully',
        address: {
          id: addressId,
          farm_id: farmId,
          farm_alias: updatedAddress.farm_alias,
          address: '123 Main St',
          city: 'Farmville',
          state: 'CA',
          zip_code: '12345',
          country: 'USA',
          delivery_instructions: updatedAddress.delivery_instructions,
          is_default: true
        }
      }
    });

    // Make the request
    const response = await axios.put(`${baseUrl}/marketplace/customers/addresses/${addressId}`, updatedAddress);

    // Assertions
    expect(axios.put).toHaveBeenCalledWith(
      `${baseUrl}/marketplace/customers/addresses/${addressId}`,
      updatedAddress
    );
    expect(response.data).toHaveProperty('message');
    expect(response.data).toHaveProperty('address');
    expect(response.data.address).toHaveProperty('id', addressId);
    expect(response.data.address).toHaveProperty('farm_alias', updatedAddress.farm_alias);
    expect(response.data.address).toHaveProperty('delivery_instructions', updatedAddress.delivery_instructions);
  });

  it('should delete an address', async () => {
    // Mock the API response
    axios.delete.mockResolvedValue({
      data: {
        message: 'Address deleted successfully'
      }
    });

    // Make the request
    const response = await axios.delete(`${baseUrl}/marketplace/customers/addresses/${addressId}`);

    // Assertions
    expect(axios.delete).toHaveBeenCalledWith(`${baseUrl}/marketplace/customers/addresses/${addressId}`);
    expect(response.data).toHaveProperty('message');
    expect(response.data.message).toContain('deleted successfully');
  });

  it('should get customer order history', async () => {
    // Mock the API response
    axios.get.mockResolvedValue({
      data: {
        orders: [
          {
            id: 'order-123',
            status: 'delivered',
            created_at: '2023-07-15T10:00:00Z',
            items: [
              {
                product_id: 'product-123',
                quantity: 2,
                price: 3.99
              }
            ],
            total: 7.98,
            farm: {
              id: farmId,
              name: 'Green Valley Farm'
            },
            fulfillment_method: 'delivery'
          },
          {
            id: 'order-456',
            status: 'pending',
            created_at: '2023-07-20T14:30:00Z',
            items: [
              {
                product_id: 'product-456',
                quantity: 1,
                price: 5.99
              }
            ],
            total: 5.99,
            farm: {
              id: 'farm-456',
              name: 'Sunny Side Farm'
            },
            fulfillment_method: 'pickup'
          }
        ]
      }
    });

    // Make the request
    const response = await axios.get(`${baseUrl}/marketplace/customers/orders`);

    // Assertions
    expect(axios.get).toHaveBeenCalledWith(`${baseUrl}/marketplace/customers/orders`);
    expect(response.data).toHaveProperty('orders');
    expect(response.data.orders).toBeInstanceOf(Array);
    expect(response.data.orders.length).toBe(2);
    expect(response.data.orders[0]).toHaveProperty('id');
    expect(response.data.orders[0]).toHaveProperty('status');
    expect(response.data.orders[0]).toHaveProperty('created_at');
    expect(response.data.orders[0]).toHaveProperty('items');
    expect(response.data.orders[0]).toHaveProperty('total');
    expect(response.data.orders[0]).toHaveProperty('farm');
    expect(response.data.orders[0]).toHaveProperty('fulfillment_method');
  });

  it('should handle farm aliases for addresses', async () => {
    const addresses = [
      {
        id: 'address-123',
        farm_id: 'farm-123',
        farm_alias: 'My Local Farm',
        address: '123 Main St',
        city: 'Farmville',
        state: 'CA',
        zip_code: '12345'
      },
      {
        id: 'address-456',
        farm_id: 'farm-456',
        farm_alias: 'Organic Veggies',
        address: '456 Oak Ave',
        city: 'Farmville',
        state: 'CA',
        zip_code: '12345'
      }
    ];

    // Mock the API response
    axios.get.mockResolvedValue({
      data: {
        customer: {
          id: 'customer-123',
          name: testCustomer.name,
          email: testCustomer.email,
          addresses
        }
      }
    });

    // Make the request
    const response = await axios.get(`${baseUrl}/marketplace/customers/profile`);

    // Assertions
    expect(axios.get).toHaveBeenCalledWith(`${baseUrl}/marketplace/customers/profile`);
    expect(response.data.customer).toHaveProperty('addresses');
    expect(response.data.customer.addresses).toBeInstanceOf(Array);
    expect(response.data.customer.addresses.length).toBe(2);
    expect(response.data.customer.addresses[0]).toHaveProperty('farm_alias', 'My Local Farm');
    expect(response.data.customer.addresses[1]).toHaveProperty('farm_alias', 'Organic Veggies');
  });

  it('should handle delivery instructions for addresses', async () => {
    const address = {
      farm_id: farmId,
      farm_alias: 'My Local Farm',
      address: '123 Main St',
      city: 'Farmville',
      state: 'CA',
      zip_code: '12345',
      delivery_instructions: 'Gate code: 1234. Leave packages by the side door.',
      access_code: '1234',
      contact_name: 'John Doe',
      contact_phone: '************'
    };

    // Mock the API response
    axios.post.mockResolvedValue({
      data: {
        message: 'Address added successfully',
        address: {
          id: addressId,
          ...address
        }
      }
    });

    // Make the request
    const response = await axios.post(`${baseUrl}/marketplace/customers/addresses`, address);

    // Assertions
    expect(axios.post).toHaveBeenCalledWith(
      `${baseUrl}/marketplace/customers/addresses`,
      address
    );
    expect(response.data.address).toHaveProperty('delivery_instructions', address.delivery_instructions);
    expect(response.data.address).toHaveProperty('access_code', address.access_code);
    expect(response.data.address).toHaveProperty('contact_name', address.contact_name);
    expect(response.data.address).toHaveProperty('contact_phone', address.contact_phone);
  });
});