# Apple Passkey Implementation

This document describes the implementation of Apple Passkey authentication in the NxtAcre Farm Management Platform.

## Overview

Apple Passkeys are a modern authentication method that uses the WebAuthn standard to provide passwordless authentication. Passkeys allow users to authenticate using biometric methods (Face ID, Touch ID) or device PIN instead of traditional passwords, enhancing both security and user experience.

## Implementation Details

### Database Changes

A new migration file `add_apple_passkey_support.sql` was created to add the following columns to the `users` table:

- `passkey_enabled`: Boolean flag indicating if passkey authentication is enabled for the user
- `passkey_credential_id`: The credential ID for the user's passkey
- `passkey_public_key`: The public key for the user's passkey
- `passkey_challenge`: Temporary challenge used during passkey registration
- `passkey_metadata`: Additional metadata about the passkey (device info, etc.)
- `passkey_created_at`: When the passkey was created
- `passkey_last_used_at`: When the passkey was last used for authentication

### Server-Side Changes

1. **User Model Updates**:
   - Added new fields for passkey authentication
   - Added methods for passkey registration and authentication

2. **Passkey Controller**:
   - Created a new controller `passkeyController.js` with methods for:
     - Generating a challenge for passkey registration
     - Registering a passkey
     - Authenticating with a passkey
     - Removing a passkey

3. **Passkey Routes**:
   - Added new routes for passkey operations:
     - `POST /api/passkey/authenticate`: Authenticate with a passkey
     - `GET /api/passkey/challenge/:userId`: Generate a challenge for passkey registration
     - `POST /api/passkey/register/:userId`: Register a passkey
     - `DELETE /api/passkey/:userId`: Remove a passkey

### Client-Side Changes

1. **Authentication Context**:
   - Updated `AuthContext.tsx` to add methods for passkey authentication:
     - `loginWithPasskey`: Authenticate with a passkey
     - `setupPasskey`: Generate a challenge for passkey registration
     - `registerPasskey`: Register a passkey
     - `removePasskey`: Remove a passkey
     - `isPasskeyAvailable`: Check if passkey authentication is available in the browser

2. **Authentication Provider**:
   - Implemented the passkey authentication methods in `AuthProvider.tsx`

3. **Login Page**:
   - Added a button for passkey authentication
   - Added a function to handle passkey authentication
   - Added a check to determine if passkey authentication is supported in the browser

## Usage

### Authenticating with a Passkey

1. On the login page, if passkey authentication is supported, a "Sign in with Passkey" button is displayed.
2. Clicking this button initiates the passkey authentication flow.
3. The browser prompts the user to authenticate using their device's biometric authentication (Face ID, Touch ID) or device PIN.
4. Upon successful authentication, the user is logged in and redirected to the dashboard.

### Setting Up a Passkey

1. Users can set up a passkey in their profile settings.
2. The system generates a challenge for passkey registration.
3. The browser prompts the user to create a passkey using their device's biometric authentication or device PIN.
4. The passkey is registered with the server and associated with the user's account.

### Removing a Passkey

1. Users can remove a passkey from their profile settings.
2. The passkey is removed from the server and no longer associated with the user's account.

## Security Considerations

- Passkeys use public key cryptography, which is more secure than traditional passwords.
- Passkeys are stored securely on the user's device and are not susceptible to phishing attacks.
- Passkeys require user verification (biometric or PIN) for each authentication, providing strong protection against unauthorized access.
- The server stores only the public key, not any private key material, ensuring that even if the server is compromised, attackers cannot use the stored information to authenticate as users.

## Browser Compatibility

Passkey authentication is supported in:
- Safari 16+ on macOS Ventura and iOS 16+
- Chrome 108+ on Android, macOS, Windows, and iOS
- Edge 108+ on Windows
- Firefox 113+ on macOS and Windows

The implementation includes a feature detection mechanism to only show the passkey option on supported browsers.