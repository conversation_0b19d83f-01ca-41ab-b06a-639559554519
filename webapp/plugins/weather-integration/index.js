/**
 * Weather Integration Plugin
 * 
 * This plugin provides weather data for farm fields based on their location.
 */

class WeatherIntegration {
  constructor(settings) {
    this.settings = settings || {};
    this.name = 'Weather Integration';
    this.version = '1.0.0';
    console.log(`${this.name} v${this.version} initialized with settings:`, this.settings);
  }

  /**
   * Initialize the plugin
   * @param {Object} context - The application context
   * @returns {Promise<void>}
   */
  async initialize(context) {
    console.log(`Initializing ${this.name}...`);
    this.context = context;
    
    // Register event handlers
    if (context.events) {
      context.events.on('field.created', this.onFieldCreated.bind(this));
      context.events.on('field.updated', this.onFieldUpdated.bind(this));
      context.events.on('daily.update', this.onDailyUpdate.bind(this));
    }
    
    // Register API endpoints
    if (context.api) {
      context.api.registerEndpoint('GET', '/weather/:fieldId', this.getFieldWeather.bind(this));
      context.api.registerEndpoint('GET', '/weather/forecast/:fieldId', this.getFieldForecast.bind(this));
    }
    
    // Register UI components
    if (context.ui) {
      context.ui.registerDashboardWidget({
        id: 'weather-widget',
        title: 'Weather',
        component: 'WeatherWidget',
        defaultPosition: { x: 0, y: 0, w: 2, h: 2 },
        permissions: ['read:weather']
      });
      
      context.ui.registerFieldTab({
        id: 'weather-tab',
        title: 'Weather',
        component: 'WeatherTab',
        icon: 'CloudIcon',
        permissions: ['read:weather']
      });
    }
    
    console.log(`${this.name} initialized successfully`);
    return true;
  }

  /**
   * Handle field created event
   * @param {Object} field - The created field
   */
  async onFieldCreated(field) {
    console.log(`Field created: ${field.name}`);
    await this.fetchAndStoreWeatherData(field);
  }

  /**
   * Handle field updated event
   * @param {Object} field - The updated field
   */
  async onFieldUpdated(field) {
    console.log(`Field updated: ${field.name}`);
    await this.fetchAndStoreWeatherData(field);
  }

  /**
   * Handle daily update event
   */
  async onDailyUpdate() {
    console.log('Running daily weather update');
    if (this.settings.update_frequency !== 'daily') {
      console.log(`Skipping update, frequency set to ${this.settings.update_frequency}`);
      return;
    }
    
    try {
      const fields = await this.context.db.getFields();
      for (const field of fields) {
        await this.fetchAndStoreWeatherData(field);
      }
      console.log('Daily weather update completed successfully');
    } catch (error) {
      console.error('Error during daily weather update:', error);
    }
  }

  /**
   * Fetch and store weather data for a field
   * @param {Object} field - The field
   */
  async fetchAndStoreWeatherData(field) {
    if (!field.location || !field.location.latitude || !field.location.longitude) {
      console.warn(`Field ${field.name} has no location data, skipping weather update`);
      return;
    }
    
    try {
      console.log(`Fetching weather data for field: ${field.name}`);
      
      // In a real implementation, this would call a weather API
      const weatherData = await this.mockWeatherApiCall(field.location);
      
      // Store the weather data
      await this.context.db.storeWeatherData(field.id, weatherData);
      
      // Send notifications if enabled
      if (this.settings.notifications && this.settings.notifications.enabled) {
        this.checkAndSendAlerts(field, weatherData);
      }
      
      console.log(`Weather data updated for field: ${field.name}`);
    } catch (error) {
      console.error(`Error fetching weather data for field ${field.name}:`, error);
    }
  }

  /**
   * Check weather conditions and send alerts if necessary
   * @param {Object} field - The field
   * @param {Object} weatherData - The weather data
   */
  checkAndSendAlerts(field, weatherData) {
    // Check for extreme weather conditions
    if (this.settings.notifications.extreme_weather) {
      const extremeConditions = this.checkExtremeConditions(weatherData);
      if (extremeConditions.length > 0) {
        this.sendAlert(field, 'Extreme Weather Alert', `Extreme conditions detected: ${extremeConditions.join(', ')}`);
      }
    }
    
    // Send daily forecast if enabled
    if (this.settings.notifications.daily_forecast) {
      const forecast = this.formatForecast(weatherData);
      this.sendAlert(field, 'Daily Weather Forecast', forecast);
    }
  }

  /**
   * Check for extreme weather conditions
   * @param {Object} weatherData - The weather data
   * @returns {Array} - Array of extreme condition descriptions
   */
  checkExtremeConditions(weatherData) {
    const extremeConditions = [];
    
    // These thresholds would be configurable in a real implementation
    if (weatherData.temperature > 35) {
      extremeConditions.push('High temperature');
    }
    if (weatherData.temperature < 0) {
      extremeConditions.push('Freezing temperature');
    }
    if (weatherData.wind_speed > 30) {
      extremeConditions.push('Strong winds');
    }
    if (weatherData.precipitation > 25) {
      extremeConditions.push('Heavy precipitation');
    }
    
    return extremeConditions;
  }

  /**
   * Format a weather forecast message
   * @param {Object} weatherData - The weather data
   * @returns {String} - Formatted forecast message
   */
  formatForecast(weatherData) {
    const tempUnit = this.settings.temperature_unit === 'celsius' ? '°C' : '°F';
    return `
      Today's forecast for ${weatherData.location}:
      Temperature: ${weatherData.temperature}${tempUnit}
      Condition: ${weatherData.condition}
      Wind: ${weatherData.wind_speed} km/h ${weatherData.wind_direction}
      Precipitation: ${weatherData.precipitation}mm
      Humidity: ${weatherData.humidity}%
    `;
  }

  /**
   * Send an alert to the user
   * @param {Object} field - The field
   * @param {String} title - The alert title
   * @param {String} message - The alert message
   */
  sendAlert(field, title, message) {
    if (this.context.notifications) {
      this.context.notifications.send({
        type: 'weather',
        title,
        message,
        field_id: field.id,
        farm_id: field.farm_id,
        severity: 'info'
      });
    }
  }

  /**
   * API endpoint to get weather data for a field
   * @param {Object} req - The request object
   * @param {Object} res - The response object
   */
  async getFieldWeather(req, res) {
    const { fieldId } = req.params;
    
    try {
      const field = await this.context.db.getField(fieldId);
      if (!field) {
        return res.status(404).json({ error: 'Field not found' });
      }
      
      const weatherData = await this.context.db.getWeatherData(fieldId);
      if (!weatherData) {
        return res.status(404).json({ error: 'Weather data not found' });
      }
      
      res.json(weatherData);
    } catch (error) {
      console.error(`Error getting weather data for field ${fieldId}:`, error);
      res.status(500).json({ error: 'Failed to get weather data' });
    }
  }

  /**
   * API endpoint to get weather forecast for a field
   * @param {Object} req - The request object
   * @param {Object} res - The response object
   */
  async getFieldForecast(req, res) {
    const { fieldId } = req.params;
    
    try {
      const field = await this.context.db.getField(fieldId);
      if (!field) {
        return res.status(404).json({ error: 'Field not found' });
      }
      
      // In a real implementation, this would call a weather API
      const forecast = await this.mockForecastApiCall(field.location);
      
      res.json(forecast);
    } catch (error) {
      console.error(`Error getting forecast for field ${fieldId}:`, error);
      res.status(500).json({ error: 'Failed to get forecast' });
    }
  }

  /**
   * Mock weather API call (for demonstration purposes)
   * @param {Object} location - The location object with latitude and longitude
   * @returns {Promise<Object>} - Weather data
   */
  async mockWeatherApiCall(location) {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // Generate random weather data
    return {
      location: `${location.latitude.toFixed(2)}, ${location.longitude.toFixed(2)}`,
      temperature: Math.round(Math.random() * 30),
      condition: ['Sunny', 'Cloudy', 'Partly Cloudy', 'Rainy', 'Stormy'][Math.floor(Math.random() * 5)],
      wind_speed: Math.round(Math.random() * 30),
      wind_direction: ['N', 'NE', 'E', 'SE', 'S', 'SW', 'W', 'NW'][Math.floor(Math.random() * 8)],
      precipitation: Math.round(Math.random() * 30),
      humidity: Math.round(Math.random() * 100),
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Mock forecast API call (for demonstration purposes)
   * @param {Object} location - The location object with latitude and longitude
   * @returns {Promise<Object>} - Forecast data
   */
  async mockForecastApiCall(location) {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // Generate random forecast data for 5 days
    const forecast = {
      location: `${location.latitude.toFixed(2)}, ${location.longitude.toFixed(2)}`,
      days: []
    };
    
    const today = new Date();
    
    for (let i = 0; i < 5; i++) {
      const date = new Date(today);
      date.setDate(date.getDate() + i);
      
      forecast.days.push({
        date: date.toISOString().split('T')[0],
        temperature_high: Math.round(Math.random() * 30),
        temperature_low: Math.round(Math.random() * 15),
        condition: ['Sunny', 'Cloudy', 'Partly Cloudy', 'Rainy', 'Stormy'][Math.floor(Math.random() * 5)],
        wind_speed: Math.round(Math.random() * 30),
        wind_direction: ['N', 'NE', 'E', 'SE', 'S', 'SW', 'W', 'NW'][Math.floor(Math.random() * 8)],
        precipitation_chance: Math.round(Math.random() * 100),
        humidity: Math.round(Math.random() * 100)
      });
    }
    
    return forecast;
  }

  /**
   * Clean up resources when the plugin is disabled
   */
  async cleanup() {
    console.log(`Cleaning up ${this.name}...`);
    
    // Unregister event handlers
    if (this.context && this.context.events) {
      this.context.events.off('field.created', this.onFieldCreated);
      this.context.events.off('field.updated', this.onFieldUpdated);
      this.context.events.off('daily.update', this.onDailyUpdate);
    }
    
    console.log(`${this.name} cleaned up successfully`);
  }
}

// Export the plugin class
export default WeatherIntegration;