/**
 * Seed & Chemical Database Integration Plugin
 * 
 * This plugin provides access to external seed and chemical product databases.
 */

class SeedChemicalDatabaseIntegration {
  constructor(settings) {
    this.settings = settings || {};
    this.name = 'Seed & Chemical Database';
    this.version = '1.0.0';
    console.log(`${this.name} v${this.version} initialized with settings:`, this.settings);
  }

  /**
   * Initialize the plugin
   * @param {Object} context - The application context
   * @returns {Promise<void>}
   */
  async initialize(context) {
    console.log(`Initializing ${this.name}...`);
    this.context = context;
    
    // Register event handlers
    if (context.events) {
      context.events.on('product.created', this.onProductCreated.bind(this));
      context.events.on('product.updated', this.onProductUpdated.bind(this));
      context.events.on('weekly.update', this.onWeeklyUpdate.bind(this));
    }
    
    // Register API endpoints
    if (context.api) {
      context.api.registerEndpoint('GET', '/products/seeds', this.getSeedProducts.bind(this));
      context.api.registerEndpoint('GET', '/products/chemicals', this.getChemicalProducts.bind(this));
      context.api.registerEndpoint('GET', '/products/seeds/:seedId', this.getSeedProductDetails.bind(this));
      context.api.registerEndpoint('GET', '/products/chemicals/:chemicalId', this.getChemicalProductDetails.bind(this));
      context.api.registerEndpoint('GET', '/products/search', this.searchProducts.bind(this));
    }
    
    // Register UI components
    if (context.ui) {
      context.ui.registerDashboardWidget({
        id: 'product-database-widget',
        title: 'Product Database',
        component: 'ProductDatabaseWidget',
        defaultPosition: { x: 2, y: 0, w: 2, h: 2 },
        permissions: ['read:products']
      });
      
      context.ui.registerInventoryTab({
        id: 'product-database-tab',
        title: 'Product Database',
        component: 'ProductDatabaseTab',
        icon: 'DatabaseIcon',
        permissions: ['read:products']
      });
    }
    
    console.log(`${this.name} initialized successfully`);
    return true;
  }

  /**
   * Handle product created event
   * @param {Object} product - The created product
   */
  async onProductCreated(product) {
    console.log(`Product created: ${product.name}`);
    if (product.type === 'seed' || product.type === 'chemical') {
      await this.enrichProductData(product);
    }
  }

  /**
   * Handle product updated event
   * @param {Object} product - The updated product
   */
  async onProductUpdated(product) {
    console.log(`Product updated: ${product.name}`);
    if (product.type === 'seed' || product.type === 'chemical') {
      await this.enrichProductData(product);
    }
  }

  /**
   * Handle weekly update event
   */
  async onWeeklyUpdate() {
    console.log('Running weekly product database update');
    if (this.settings.update_frequency !== 'weekly') {
      console.log(`Skipping update, frequency set to ${this.settings.update_frequency}`);
      return;
    }
    
    try {
      // Update seed products
      const seedProducts = await this.context.db.getProducts({ type: 'seed' });
      for (const product of seedProducts) {
        await this.enrichProductData(product);
      }
      
      // Update chemical products
      const chemicalProducts = await this.context.db.getProducts({ type: 'chemical' });
      for (const product of chemicalProducts) {
        await this.enrichProductData(product);
      }
      
      // Check for new products and regulatory changes
      await this.checkForNewProducts();
      await this.checkForRegulatoryChanges();
      
      console.log('Weekly product database update completed successfully');
    } catch (error) {
      console.error('Error during weekly product database update:', error);
    }
  }

  /**
   * Enrich product data with information from external databases
   * @param {Object} product - The product to enrich
   */
  async enrichProductData(product) {
    try {
      console.log(`Enriching data for product: ${product.name}`);
      
      let externalData;
      if (product.type === 'seed') {
        externalData = await this.mockSeedDatabaseCall(product);
      } else if (product.type === 'chemical') {
        externalData = await this.mockChemicalDatabaseCall(product);
      }
      
      if (externalData) {
        // Update product with external data
        await this.context.db.updateProduct(product.id, {
          external_data: externalData,
          last_synced: new Date().toISOString()
        });
        
        console.log(`Product data enriched for: ${product.name}`);
      }
    } catch (error) {
      console.error(`Error enriching data for product ${product.name}:`, error);
    }
  }

  /**
   * Check for new products in external databases
   */
  async checkForNewProducts() {
    if (!this.settings.notifications.enabled || !this.settings.notifications.new_products) {
      return;
    }
    
    try {
      console.log('Checking for new products in external databases');
      
      // In a real implementation, this would call external APIs
      const newSeedProducts = await this.mockNewProductsCheck('seed');
      const newChemicalProducts = await this.mockNewProductsCheck('chemical');
      
      // Send notifications for new products
      for (const product of [...newSeedProducts, ...newChemicalProducts]) {
        this.sendAlert(
          'New Product Available',
          `A new ${product.type} product is now available: ${product.name} from ${product.manufacturer}`
        );
      }
    } catch (error) {
      console.error('Error checking for new products:', error);
    }
  }

  /**
   * Check for regulatory changes in external databases
   */
  async checkForRegulatoryChanges() {
    if (!this.settings.notifications.enabled || !this.settings.notifications.regulatory_changes) {
      return;
    }
    
    try {
      console.log('Checking for regulatory changes in external databases');
      
      // In a real implementation, this would call external APIs
      const regulatoryChanges = await this.mockRegulatoryChangesCheck();
      
      // Send notifications for regulatory changes
      for (const change of regulatoryChanges) {
        this.sendAlert(
          'Regulatory Change Alert',
          `Regulatory change for ${change.product_type} products: ${change.description}`
        );
      }
    } catch (error) {
      console.error('Error checking for regulatory changes:', error);
    }
  }

  /**
   * Send an alert to the user
   * @param {String} title - The alert title
   * @param {String} message - The alert message
   */
  sendAlert(title, message) {
    if (this.context.notifications) {
      this.context.notifications.send({
        type: 'product_database',
        title,
        message,
        severity: 'info'
      });
    }
  }

  /**
   * API endpoint to get seed products
   * @param {Object} req - The request object
   * @param {Object} res - The response object
   */
  async getSeedProducts(req, res) {
    try {
      const { limit = 20, offset = 0, category, manufacturer } = req.query;
      
      // Build query parameters
      const query = { type: 'seed' };
      if (category) query.category = category;
      if (manufacturer) query.manufacturer = manufacturer;
      
      const products = await this.context.db.getProducts(query, { limit, offset });
      const total = await this.context.db.countProducts(query);
      
      res.json({
        products,
        total,
        limit: parseInt(limit),
        offset: parseInt(offset)
      });
    } catch (error) {
      console.error('Error getting seed products:', error);
      res.status(500).json({ error: 'Failed to get seed products' });
    }
  }

  /**
   * API endpoint to get chemical products
   * @param {Object} req - The request object
   * @param {Object} res - The response object
   */
  async getChemicalProducts(req, res) {
    try {
      const { limit = 20, offset = 0, category, manufacturer } = req.query;
      
      // Build query parameters
      const query = { type: 'chemical' };
      if (category) query.category = category;
      if (manufacturer) query.manufacturer = manufacturer;
      
      const products = await this.context.db.getProducts(query, { limit, offset });
      const total = await this.context.db.countProducts(query);
      
      res.json({
        products,
        total,
        limit: parseInt(limit),
        offset: parseInt(offset)
      });
    } catch (error) {
      console.error('Error getting chemical products:', error);
      res.status(500).json({ error: 'Failed to get chemical products' });
    }
  }

  /**
   * API endpoint to get seed product details
   * @param {Object} req - The request object
   * @param {Object} res - The response object
   */
  async getSeedProductDetails(req, res) {
    try {
      const { seedId } = req.params;
      
      const product = await this.context.db.getProduct(seedId);
      if (!product) {
        return res.status(404).json({ error: 'Seed product not found' });
      }
      
      if (product.type !== 'seed') {
        return res.status(400).json({ error: 'Product is not a seed product' });
      }
      
      // Get additional details from external database if needed
      if (!product.external_data || !product.last_synced) {
        await this.enrichProductData(product);
        // Fetch the updated product
        const updatedProduct = await this.context.db.getProduct(seedId);
        return res.json(updatedProduct);
      }
      
      res.json(product);
    } catch (error) {
      console.error(`Error getting seed product details:`, error);
      res.status(500).json({ error: 'Failed to get seed product details' });
    }
  }

  /**
   * API endpoint to get chemical product details
   * @param {Object} req - The request object
   * @param {Object} res - The response object
   */
  async getChemicalProductDetails(req, res) {
    try {
      const { chemicalId } = req.params;
      
      const product = await this.context.db.getProduct(chemicalId);
      if (!product) {
        return res.status(404).json({ error: 'Chemical product not found' });
      }
      
      if (product.type !== 'chemical') {
        return res.status(400).json({ error: 'Product is not a chemical product' });
      }
      
      // Get additional details from external database if needed
      if (!product.external_data || !product.last_synced) {
        await this.enrichProductData(product);
        // Fetch the updated product
        const updatedProduct = await this.context.db.getProduct(chemicalId);
        return res.json(updatedProduct);
      }
      
      res.json(product);
    } catch (error) {
      console.error(`Error getting chemical product details:`, error);
      res.status(500).json({ error: 'Failed to get chemical product details' });
    }
  }

  /**
   * API endpoint to search products
   * @param {Object} req - The request object
   * @param {Object} res - The response object
   */
  async searchProducts(req, res) {
    try {
      const { query, type, limit = 20, offset = 0 } = req.query;
      
      if (!query) {
        return res.status(400).json({ error: 'Search query is required' });
      }
      
      // Build search parameters
      const searchParams = { query };
      if (type === 'seed' || type === 'chemical') {
        searchParams.type = type;
      }
      
      const results = await this.context.db.searchProducts(searchParams, { limit, offset });
      const total = await this.context.db.countSearchResults(searchParams);
      
      res.json({
        results,
        total,
        limit: parseInt(limit),
        offset: parseInt(offset)
      });
    } catch (error) {
      console.error('Error searching products:', error);
      res.status(500).json({ error: 'Failed to search products' });
    }
  }

  /**
   * Mock seed database API call (for demonstration purposes)
   * @param {Object} product - The seed product
   * @returns {Promise<Object>} - Enriched seed data
   */
  async mockSeedDatabaseCall(product) {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // Generate mock seed data based on the database preference
    const database = this.settings.preferred_databases.seed;
    
    // Common seed data
    const seedData = {
      database_source: database,
      database_id: `${database}-${Math.floor(Math.random() * 10000)}`,
      last_updated: new Date().toISOString(),
      germination_rate: Math.round(85 + Math.random() * 15) + '%',
      growing_zones: ['Zone ' + Math.floor(Math.random() * 10 + 1), 'Zone ' + Math.floor(Math.random() * 10 + 1)],
      days_to_maturity: Math.floor(Math.random() * 30 + 60),
      planting_depth: (0.5 + Math.random() * 1.5).toFixed(1) + ' inches',
      row_spacing: Math.floor(Math.random() * 12 + 24) + ' inches',
      seeding_rate: Math.floor(Math.random() * 5000 + 25000) + ' seeds/acre'
    };
    
    // Add database-specific data
    switch (database) {
      case 'usda':
        seedData.usda_certification = ['Organic', 'Non-GMO', 'Heritage'][Math.floor(Math.random() * 3)];
        seedData.usda_variety_trials = {
          yield: Math.floor(Math.random() * 50 + 150) + ' bu/acre',
          disease_resistance: ['Low', 'Medium', 'High'][Math.floor(Math.random() * 3)],
          drought_tolerance: ['Low', 'Medium', 'High'][Math.floor(Math.random() * 3)]
        };
        break;
      case 'pioneer':
        seedData.pioneer_hybrid_number = 'P' + Math.floor(Math.random() * 2000);
        seedData.pioneer_technology = ['Optimum AcreMax', 'Optimum AQUAmax', 'Optimum Leptra'][Math.floor(Math.random() * 3)];
        seedData.pioneer_yield_potential = Math.floor(Math.random() * 50 + 150) + ' bu/acre';
        break;
      case 'dekalb':
        seedData.dekalb_hybrid_number = 'DKC' + Math.floor(Math.random() * 70 + 30) + '-' + Math.floor(Math.random() * 99);
        seedData.dekalb_technology = ['DroughtGard', 'VT Double PRO', 'SmartStax'][Math.floor(Math.random() * 3)];
        seedData.dekalb_relative_maturity = (Math.random() * 40 + 80).toFixed(1) + ' days';
        break;
      case 'syngenta':
        seedData.syngenta_variety_number = 'NK' + Math.floor(Math.random() * 9000 + 1000);
        seedData.syngenta_traits = ['Agrisure Viptera', 'Agrisure Duracade', 'Agrisure Artesian'][Math.floor(Math.random() * 3)];
        seedData.syngenta_performance_rating = Math.floor(Math.random() * 3 + 3) + ' out of 5';
        break;
    }
    
    return seedData;
  }

  /**
   * Mock chemical database API call (for demonstration purposes)
   * @param {Object} product - The chemical product
   * @returns {Promise<Object>} - Enriched chemical data
   */
  async mockChemicalDatabaseCall(product) {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // Generate mock chemical data based on the database preference
    const database = this.settings.preferred_databases.chemical;
    
    // Common chemical data
    const chemicalData = {
      database_source: database,
      database_id: `${database}-${Math.floor(Math.random() * 10000)}`,
      last_updated: new Date().toISOString(),
      active_ingredients: [
        {
          name: ['Glyphosate', 'Dicamba', 'Atrazine', '2,4-D', 'Mesotrione'][Math.floor(Math.random() * 5)],
          concentration: Math.floor(Math.random() * 50 + 10) + '%'
        },
        {
          name: ['Metolachlor', 'Acetochlor', 'Clopyralid', 'Flumetsulam', 'Imazethapyr'][Math.floor(Math.random() * 5)],
          concentration: Math.floor(Math.random() * 20 + 5) + '%'
        }
      ],
      application_rate: {
        min: (Math.random() * 0.5 + 0.5).toFixed(2),
        max: (Math.random() * 1 + 1).toFixed(2),
        unit: ['oz/acre', 'pt/acre', 'qt/acre', 'gal/acre'][Math.floor(Math.random() * 4)]
      },
      target_pests: ['Broadleaf weeds', 'Grassy weeds', 'Insects', 'Fungi', 'Nematodes'].slice(0, Math.floor(Math.random() * 3) + 1),
      reentry_interval: Math.floor(Math.random() * 48 + 4) + ' hours',
      preharvest_interval: Math.floor(Math.random() * 30 + 7) + ' days',
      safety_precautions: ['Wear gloves', 'Wear eye protection', 'Wear respirator', 'Avoid skin contact'].slice(0, Math.floor(Math.random() * 3) + 1)
    };
    
    // Add database-specific data
    switch (database) {
      case 'epa':
        chemicalData.epa_registration_number = Math.floor(Math.random() * 90000 + 10000) + '-' + Math.floor(Math.random() * 900 + 100);
        chemicalData.epa_signal_word = ['Caution', 'Warning', 'Danger'][Math.floor(Math.random() * 3)];
        chemicalData.epa_restricted_use = Math.random() > 0.7;
        chemicalData.epa_environmental_hazards = ['Toxic to aquatic organisms', 'Toxic to bees', 'Groundwater concern'].slice(0, Math.floor(Math.random() * 2) + 1);
        break;
      case 'bayer':
        chemicalData.bayer_product_line = ['Roundup', 'Liberty', 'Corvus', 'Delaro'][Math.floor(Math.random() * 4)];
        chemicalData.bayer_technology = ['XtendiMax', 'WideStrike', 'TwinLink'][Math.floor(Math.random() * 3)];
        chemicalData.bayer_crop_safety = ['Excellent', 'Good', 'Fair'][Math.floor(Math.random() * 3)];
        break;
      case 'syngenta':
        chemicalData.syngenta_product_line = ['Acuron', 'Callisto', 'Gramoxone', 'Warrior'][Math.floor(Math.random() * 4)];
        chemicalData.syngenta_resistance_management = 'Group ' + Math.floor(Math.random() * 30 + 1);
        chemicalData.syngenta_tank_mix_partners = ['Atrazine', 'Glyphosate', 'Dicamba'].slice(0, Math.floor(Math.random() * 2) + 1);
        break;
      case 'basf':
        chemicalData.basf_product_line = ['Engenia', 'Outlook', 'Prowl', 'Headline'][Math.floor(Math.random() * 4)];
        chemicalData.basf_formulation = ['EC', 'SC', 'WG', 'WP'][Math.floor(Math.random() * 4)];
        chemicalData.basf_rainfast_time = Math.floor(Math.random() * 4 + 1) + ' hours';
        break;
    }
    
    return chemicalData;
  }

  /**
   * Mock check for new products (for demonstration purposes)
   * @param {String} type - The product type ('seed' or 'chemical')
   * @returns {Promise<Array>} - Array of new products
   */
  async mockNewProductsCheck(type) {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // 20% chance of finding new products
    if (Math.random() > 0.2) {
      return [];
    }
    
    const newProducts = [];
    const count = Math.floor(Math.random() * 3) + 1;
    
    for (let i = 0; i < count; i++) {
      if (type === 'seed') {
        newProducts.push({
          type: 'seed',
          name: ['SuperYield Corn', 'MaxGrow Soybean', 'PremiumHarvest Wheat', 'GoldStandard Alfalfa'][Math.floor(Math.random() * 4)],
          manufacturer: ['Pioneer', 'DEKALB', 'Syngenta', 'Corteva'][Math.floor(Math.random() * 4)]
        });
      } else {
        newProducts.push({
          type: 'chemical',
          name: ['UltraGuard Herbicide', 'MaxDefense Fungicide', 'TotalControl Insecticide', 'PowerShield Adjuvant'][Math.floor(Math.random() * 4)],
          manufacturer: ['Bayer', 'BASF', 'Syngenta', 'Corteva'][Math.floor(Math.random() * 4)]
        });
      }
    }
    
    return newProducts;
  }

  /**
   * Mock check for regulatory changes (for demonstration purposes)
   * @returns {Promise<Array>} - Array of regulatory changes
   */
  async mockRegulatoryChangesCheck() {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // 15% chance of finding regulatory changes
    if (Math.random() > 0.15) {
      return [];
    }
    
    const changes = [];
    const count = Math.floor(Math.random() * 2) + 1;
    
    const possibleChanges = [
      {
        product_type: 'chemical',
        description: 'New application restrictions for dicamba products in effect starting next season.'
      },
      {
        product_type: 'chemical',
        description: 'EPA has revised the reentry intervals for glyphosate-based herbicides.'
      },
      {
        product_type: 'seed',
        description: 'New certification requirements for organic seed producers announced.'
      },
      {
        product_type: 'chemical',
        description: 'Buffer zone requirements increased for certain insecticides to protect pollinators.'
      },
      {
        product_type: 'seed',
        description: 'Updated labeling requirements for treated seeds containing neonicotinoids.'
      }
    ];
    
    for (let i = 0; i < count; i++) {
      changes.push(possibleChanges[Math.floor(Math.random() * possibleChanges.length)]);
    }
    
    return changes;
  }

  /**
   * Clean up resources when the plugin is disabled
   */
  async cleanup() {
    console.log(`Cleaning up ${this.name}...`);
    
    // Unregister event handlers
    if (this.context && this.context.events) {
      this.context.events.off('product.created', this.onProductCreated);
      this.context.events.off('product.updated', this.onProductUpdated);
      this.context.events.off('weekly.update', this.onWeeklyUpdate);
    }
    
    console.log(`${this.name} cleaned up successfully`);
  }
}

// Export the plugin class
export default SeedChemicalDatabaseIntegration;